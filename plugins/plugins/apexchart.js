import plugin from 'tailwindcss/plugin';

export default plugin(function ({ addComponents }) {
  addComponents({
    '.apex-charts': {
      minHeight: 'auto !important',

      text: {
        '@apply !font-pretendard': {},
      },

      '.apexcharts-canvas': {
        '@apply my-0 mx-auto': {},
      },
    },
    '.apexcharts-canvas': {
      '::-webkit-scrollbar-thumb': {
        '@apply bg-slate-600 shadow-md': {},
      },

      ':is(.apexcharts-reset-zoom-icon, .apexcharts-selection-icon, .apexcharts-zoom-icon)':
        {
          '&.apexcharts-selected': {
            svg: {
              '@apply fill-gray-15': {},
            },
          },
        },

      'text.apexcharts-title-text, .apexcharts-subtitle-text': {
        '@apply !fill-gray-10 !font-medium': {},
      },
    },

    '.apexcharts-gridline': {
      '@apply stroke-black/10 dark:stroke-gray-8': {},
    },

    '.apexcharts-yaxis, .apexcharts-xaxis': {
      text: {
        '@apply fill-slate-500 font-pretendard': {},
      },
    },

    '.apexcharts-heatmap-series rect, .apexcharts-treemap-series rect': {
      '@apply stroke-white': {},
    },

    '.apexcharts-legend-text': {
      '@apply !text-gray-10 !font-pretendard !text-xs': {},
    },

    '.apexcharts-xaxis-tick': {
      '@apply stroke-black/10 dark:stroke-gray-8': {},
    },
    '.apexcharts-marker': {
      '@apply stroke-white': {},
    },

    '.apexcharts-tooltip': {
      '@apply shadow-lg': {},

      '&.apexcharts-theme-light': {
        '@apply dark:border-gray-15 border-black/10 bg-white dark:bg-gray-8 dark:text-white/80':
          {},

        '.apexcharts-tooltip-title': {
          '@apply !border-b-black/10 dark:border-gray-15 !bg-white !font-pretendard dark:!bg-gray-8':
            {},
        },
      },
    },

    '.apexcharts-tooltip-title': {
      '@apply !font-pretendard': {},
    },

    '.apexcharts-pie-series, .apexcharts-bar-series': {
      path: {
        '@apply stroke-white dark:stroke-gray-8': {},
      },
    },
    '.apexcharts-radialbar': {
      '.apexcharts-datalabels-group text': {
        '@apply fill-black/10 dark:fill-white/50': {},
      },
    },

    '.apexcharts-radialbar-track': {
      path: {
        '@apply stroke-black/10 dark:stroke-gray-15': {},
      },
    },

    '.apexcharts-radar-series': {
      'polygon, line': {
        '@apply fill-white dark:fill-gray-15 stroke-black/10 dark:stroke-gray-15':
          {},
      },
    },

    '.apexcharts-pie': {
      'circle, line': {
        '@apply stroke-black/10 dark:stroke-gray-15': {},

        '&[fill="transparent"]': {
          '@apply stroke-transparent': {},
        },
      },
      text: {
        '@apply fill-white': {},
      },
    },
    '.apexcharts-xaxistooltip': {
      '&.apexcharts-theme-light': {
        '@apply shadow-md bg-white border-black/10 dark:border-gray-15 !font-pretendard':
          {},

        '&::before': {
          '@apply border-b-black/10 dark:border-b-gray-15': {},
        },
      },
    },
    '.apexcharts-grid-borders': {
      line: {
        '@apply stroke-black/10 dark:stroke-gray-15': {},
      },
    },
  });
});
