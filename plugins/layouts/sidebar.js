import plugin from 'tailwindcss/plugin';

export default plugin(function ({ addComponents }) {
  addComponents({
    '.sidebar': {
      '@apply fixed z-[9999] lg:z-[10] flex-none w-[240px] ltr:border-r rtl:border-l dark:bg-gray-15 border-black/10 transition-all duration-300 overflow-hidden transition-all duration-300':
        {},
      //dark sidebar
      '@apply group-data-[sidebar=dark]/item:bg-gray-15 group-data-[sidebar=dark]/item:border-gray-15 group-data-[sidebar=brand]/item:border-sky-900':
        {},

      '&:hover': {
        '.nav-item': {
          '> a': {
            '@apply w-auto': {},
          },
        },
      },

      '.nav-item': {
        '> a': {
          '@apply flex items-center overflow-hidden text-black whitespace-nowrap text-white last:mb-0 group-data-[sidebar=dark]/item:text-white group-data-[sidebar=brand]/item:text-sky-200/50':
            {},
          //active
          '@apply group-data-[sidebar=dark]/item:[&.active]:text-gray-15 group-data-[sidebar=brand]/item:[&.active]:text-sky-500':
            {},
        },
      },

      'ul.sub-menu': {
        li: {
          a: {
            '@apply flex items-center   overflow-hidden text-black whitespace-nowrap text-white last:mb-0 group-data-[sidebar=dark]/item:text-white group-data-[sidebar=brand]/item:text-sky-200/50':
              {},
            //active
            '@apply group-data-[sidebar=dark]/item:[&.active]:text-gray-15 group-data-[sidebar=brand]/item:[&.active]:text-sky-500':
              {},
          },
        },
      },
    },
  });
});
