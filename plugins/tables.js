import plugin from 'tailwindcss/plugin';

export default plugin(function ({ addComponents }) {
  addComponents({
    table: {
      '@apply w-full !border-collapse': {},

      thead: {
        '@apply border-b bg-gray-4/40 border-black/10 dark:bg-white/5 dark:border-gray-15':
          {},
      },
      ':is(thead, tbody)': {
        tr: {
          '@apply border-b border-black/10 whitespace-nowrap dark:border-gray-15':
            {},
        },
      },
      ':is(thead, tbody, tfoot)': {
        tr: {
          ':is(th, td)': {
            '@apply px-4 py-3 text-black whitespace-nowrap dark:text-white': {},
          },
        },
      },
      tbody: {
        tr: {
          '@apply last:border-0': {},
        },
      },
      '&.table-striped': {
        tbody: {
          tr: {
            '@apply even:bg-gray-4/40 even:dark:bg-white/5': {},
          },
        },
      },
      '&.table-hover': {
        tbody: {
          tr: {
            '@apply hover:bg-gray-4/40 dark:hover:bg-white/5': {},
          },
        },
      },
      '&.table-borderless': {
        ':is(thead, tbody, tfoot)': {
          tr: {
            '@apply border-0': {},
          },
        },
        thead: {
          '@apply border-0': {},
        },
      },
    },
  });
});
