{"name": "himate", "version": "1.0.0", "private": true, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/pretendard": "^5.1.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/list": "^6.1.14", "@fullcalendar/multimonth": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/rrule": "^6.1.17", "@fullcalendar/timegrid": "^6.1.14", "@hookform/resolvers": "^3.9.1", "@mui/material": "^6.4.1", "@mui/x-charts": "^7.24.0", "@popperjs/core": "^2.11.8", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-hover-card": "^1.1.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/themes": "^3.1.6", "@tailwindcss/forms": "^0.5.7", "@tanstack/match-sorter-utils": "^8.15.1", "@tanstack/react-query": "^5.64.2", "@tanstack/react-query-devtools": "^5.62.11", "@tanstack/react-table": "^8.17.3", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "@tinymce/tinymce-react": "^5.1.1", "@toss/use-overlay": "^1.4.2", "@types/geojson": "^7946.0.16", "@types/jest": "^29.5.12", "@types/node": "^20.14.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.2.19", "@types/react-flatpickr": "^3.8.11", "@types/uuid": "^10.0.0", "@vis.gl/react-google-maps": "^1.5.0", "apexcharts": "^4.0.0", "axios": "^1.7.3", "axios-auth-refresh": "^3.3.6", "axios-mock-adapter": "^1.22.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "exceljs": "^4.4.0", "flatpickr": "^4.6.13", "formik": "^2.4.6", "framer-motion": "^12.23.6", "html2canvas": "^1.4.1", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "immer": "^10.1.1", "jspdf": "^3.0.0", "lightbox.js-react": "^1.0.9", "papaparse": "^5.5.3", "prettier": "^3.4.2", "react": "^18.2.0", "react-apexcharts": "^1.6.0", "react-big-calendar": "^1.17.1", "react-calendar": "^5.1.0", "react-countdown": "^2.3.5", "react-countup": "^6.5.3", "react-datepicker": "^7.6.0", "react-dom": "^18.2.0", "react-dropdown": "^1.11.0", "react-flatpickr": "^3.10.13", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.0", "react-icons": "^5.4.0", "react-router-dom": "^7.1.5", "react-time-picker": "^7.0.0", "react-window": "^1.8.11", "react-zoom-pan-pinch": "^3.7.0", "recharts": "^2.15.0", "remixicon": "^4.3.0", "reselect": "^5.1.1", "sass": "^1.77.6", "swiper": "^11.1.4", "tailwind-merge": "^2.6.0", "tailwind-scrollbar": "^3.1.0", "tinymce": "^7.8.0", "tippy.js": "^6.3.7", "uuid": "^11.0.5", "web-vitals": "^4.1.1", "yup": "^1.4.0", "zod": "^3.24.1", "zustand": "^5.0.1"}, "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint --fix .", "preview": "vite preview", "generate-api": "rm -rf src/api/generated && openapi-generator-cli generate -i https://fms-dev-api.cartamobility.com/docs/api-docs.yaml -g typescript-axios -o src/api/generated -c openapi-generator-config.json", "docker:build": "docker build --platform linux/amd64 -t harbor.logisteq.com/himate/himate-web --build-arg ENV_FILE=.env.dev .", "prepare": "husky"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/js": "^9.17.0", "@openapitools/openapi-generator-cli": "^2.16.2", "@types/jquery": "^3.5.32", "@types/papaparse": "^5.3.16", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-big-calendar": "^1.16.1", "@types/react-grid-layout": "^1.3.5", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.3", "globals": "^15.14.0", "husky": "^9.1.7", "lint-staged": "^15.3.0", "postcss": "^8.4.49", "react-grid-layout": "^1.5.0", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.19.1", "vite": "^5.4.10"}, "lint-staged": {"**/*": ["prettier --write .", "eslint ."], "!src/api/generated/**": []}}