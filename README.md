# hiMate-react

hiMate-react는 [sliced-react](https://sliced-react.vercel.app/)에서 구입한 후 StartKit (최소 버전)을 커밋한 프로젝트입니다.

## 개발 환경 설정

### 필수 요구사항

- Node.js 20

### 프로젝트 실행

```sh
nvm use 20
npm install
npm run dev
```

## 기술 스택

### Frontend Framework

- React 18

### 상태 관리

- **서버 상태**: Tanstack Query(React Query)
  - 참고 문서: https://www.heropy.dev/p/HZaKIE
- **클라이언트 상태**: Zustand

### API 통합

- OpenAPI Generator CLI
  - `src/api/generated` 폴더의 자동 생성된 코드는 수정 금지

## 프로젝트 구조

```
src/
├── assets/          # 이미지, 아이콘 등 정적 리소스
├── Common/          # 공통 관련 컴포넌트
├── helpers/         # 유틸리티 함수
├── Layout/          # 레이아웃 관련 컴포넌트
├── Routes/          # 라우팅 관련 컴포넌트
├── Pages/          # 페이지 컴포넌트
├── store/          # Zustand 상태 관리
├── types/          # 타입 정의
└── api/            # API 호출 관련
    ├── generated/  # OpenAPI 자동 생성 파일 (수정 금지)
    └── index.ts    # API 생성 부분, Tag 가 수정될때 마다 수정해야 함 .

```

## 라우팅

- 라우팅 설정: `src/Route/allRoutes.tsx`
- 절대 경로 임포트 지원 (`@` 사용)

예시:

```typescript
// 상대 경로
import VehicleMonitoring from '../Pages/VehicleMonitoring';

// 절대 경로 (권장)
import VehicleMonitoring from '@/Pages/VehicleMonitoring';
```

## TypeScript 사용: TS 사용

- 프로젝트 전반에 TypeScript를 사용.
- 타입 안정성을 위해 모든 컴포넌트와 서버 로직에 TypeScript를 적용하세요.

## Tailwind CSS 사용

- 모든 UI 컴포넌트는 **Tailwind CSS**를 사용하여 생성하세요.
- 가능하면 모바일 사이즈도 대응되도록 반응형으로 생성하세요.
- 공통 컴포넌트는 src/Common/ 폴더에 있습니다

## Radix UI 사용

- 공통 UI 컴포넌트는 **Radix UI**를 사용하세요.
- Radix UI 컴포넌트는 Tailwind CSS와 함께 스타일링하여 사용하세요.

## 개발 환경 URL

### API Swagger

https://fms-dev-api.cartamobility.com/docs/swagger-ui/index.html

### Frontend

https://fms-dev-api.cartamobility.com
