# 빌드 스테이지
FROM node:20-alpine as build

WORKDIR /app

# 패키지 파일 복사 및 설치
COPY package*.json ./
RUN npm install

# 소스 파일 복사
COPY . .

# env 파일 복사
COPY .env.dev .env

# 빌드 실행
RUN npm run build

# 실행 스테이지
FROM nginx:alpine

# nginx 설정을 직접 작성
RUN echo 'server { \
    listen 80; \
    server_name localhost; \
    \
    location / { \
    root /usr/share/nginx/html; \
    index index.html; \
    try_files $uri $uri/ /index.html; \
    } \
    \
    # gzip 압축 설정 \
    gzip on; \
    gzip_vary on; \
    gzip_min_length 10240; \
    gzip_proxied expired no-cache no-store private auth; \
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml application/javascript; \
    gzip_disable "MSIE [1-6]\\."; \
    }' > /etc/nginx/conf.d/default.conf

# 빌드 결과물을 nginx의 서비스 디렉토리로 복사
COPY --from=build /app/dist /usr/share/nginx/html

# 80 포트 노출
EXPOSE 80

# nginx 실행
CMD ["nginx", "-g", "daemon off;"]
