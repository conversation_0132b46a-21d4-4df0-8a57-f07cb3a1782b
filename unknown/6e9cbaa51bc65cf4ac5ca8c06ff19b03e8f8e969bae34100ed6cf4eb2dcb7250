import { useTranslation } from 'react-i18next';
import { t } from 'i18next';
import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';

const ChargeAmount = ({
  title = '누적 충전량',
  before = `1,352Ah ${t('Up')}↑`,
  up,
  unit = 'Ah',
  value = '2,324,324',
}: {
  title?: string;
  before?: string;
  value?: string;
  unit?: string;
  up?: boolean;
}) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';

  return (
    <StatisticsContainer className={'flex gap-[30px] items-center'}>
      <div className="w-full text-lg font-semibold space-y-3">
        <div>
          {title}
          {isEnglish ? (
            <>
              <div className="flex items-center">
                <div className="mr-1">
                  <span
                    className={`body3-b ${up ? 'text-point-3' : 'text-point-2'}`}
                  >
                    {before}
                  </span>
                  <span
                    className={`body3-b ${up ? 'text-point-3' : 'text-point-2'}`}
                  >
                    {up ? t('Up') : t('Down')}
                  </span>
                </div>
                <div className="align-middle  text-base font-normal  leading-normal ">
                  {t('FromYesterday')}
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-center">
                <div className="align-middle  text-base font-normal  leading-normal ">
                  {t('FromYesterday')}
                </div>
                <div className="space-x-1">
                  <span
                    className={`body3-b ${up ? 'text-point-3' : 'text-point-2'}`}
                  >
                    {before}
                  </span>
                  <span
                    className={`body3-b ${up ? 'text-point-3' : 'text-point-2'}`}
                  >
                    {up ? t('Up') : t('Down')}
                  </span>
                </div>
              </div>
            </>
          )}
        </div>
        <div className="h-[60px] p-2.5 justify-end items-start gap-2.5 flex">
          <div className="justify-start items-center gap-1 flex">
            <div className="text-right text-5xl font-light">{value}</div>
            <div className="text-right text-[28px] font-light">{unit}</div>
          </div>
        </div>
      </div>
    </StatisticsContainer>
  );
};

export default ChargeAmount;
