import { useTranslation } from 'react-i18next';
import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';

const ExpendableStatistics = () => {
  const { t } = useTranslation();

  return (
    <StatisticsContainer className={'flex gap-7 items-center'}>
      <div className={'flex-1'}>
        <div className="text-lg font-semibold ">{t('TotalR')}</div>
        <div className="justify-end items-center gap-1 flex h-[67px]">
          <div className="text-right text-[56px] font-light">34</div>
          <div className="text-right text-[32px] font-light">{t('Cases')}</div>
        </div>
      </div>
      <div className="w-px h-[62px] opacity-80 bg-[#b3b3b3] rounded-[8px]"></div>
      <div className={'flex-1'}>
        <div className="text-lg font-semibold ">{t('Expired')}</div>
        <div className="justify-end items-center gap-1 flex h-[67px]">
          <div className="text-right text-[56px] font-light">34</div>
          <div className="text-right text-[32px] font-light">{t('Cases')}</div>
        </div>
      </div>
      <div className="w-px h-[62px] opacity-80 bg-[#b3b3b3] rounded-[8px]"></div>
      <div className={'flex-1'}>
        <div className="text-lg font-semibold ">{t('Reached')}</div>
        <div className="justify-end items-center gap-1 flex h-[67px]">
          <div className="text-right text-[56px] font-light">34</div>
          <div className="text-right text-[32px] font-light">{t('Cases')}</div>
        </div>
      </div>
      <div className="w-px h-[62px] opacity-80 bg-[#b3b3b3] rounded-[8px]"></div>
      <div className={'flex-1'}>
        <div className="text-lg font-semibold ">{t('Resolved')}</div>
        <div className="justify-end items-center gap-1 flex h-[67px]">
          <div className="text-right text-[56px] font-light">34</div>
          <div className="text-right text-[32px] font-light">{t('Cases')}</div>
        </div>
      </div>
    </StatisticsContainer>
  );
};

export default ExpendableStatistics;
