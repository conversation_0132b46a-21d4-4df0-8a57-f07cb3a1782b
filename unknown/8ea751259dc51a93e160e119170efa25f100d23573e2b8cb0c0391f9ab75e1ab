import { useOverlay } from '@toss/use-overlay';
import WorkerEqStatisticsPopup from '@/Pages/Statistics/components/popup/WorkerEqStatisticsPopup.tsx';

const useStatisticsPopup = () => {
  const overlay = useOverlay();

  const openWorkerEqStatisticsPopup = () => {
    return overlay.open(({ isOpen, close }) => {
      return (
        <WorkerEqStatisticsPopup
          onClose={close}
          onConfirm={close}
          isOpen={isOpen}
        />
      );
    });
  };
  return { openWorkerEqStatisticsPopup };
};

export default useStatisticsPopup;
