import React, { HTMLAttributes, RefObject } from 'react';

const useAria = <T extends HTMLElement = HTMLButtonElement>() => {
  const useAriaCheckbox = (
    e: React.MouseEvent<T, MouseEvent>,
    ref: RefObject<T>,
    props?: HTMLAttributes<T>,
    onCheckedChange?: (checked: boolean) => void,
  ) => {
    if (ref.current) {
      if (ref.current.ariaChecked !== 'true') {
        ref.current.ariaChecked = 'true';
        onCheckedChange?.(true);
      } else {
        ref.current.ariaChecked = 'false';
        onCheckedChange?.(false);
      }
    }
    if (props?.onClick) props.onClick(e);
  };

  return { useAriaCheckbox };
};

export default useAria;
