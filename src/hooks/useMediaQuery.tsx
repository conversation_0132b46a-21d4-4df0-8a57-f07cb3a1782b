import { useState, useEffect } from 'react';

/**
 * 특정 미디어 쿼리에 맞는지 확인하는 훅
 * 반응형 UI 구현에 유용하게 사용할 수 있습니다.
 *
 * @param query - CSS 미디어 쿼리 문자열 (예: '(max-width: 640px)')
 * @returns 미디어 쿼리에 일치하는지 여부 (boolean)
 */
export const useMediaQuery = (query: string): boolean => {
  // 미디어 쿼리 일치 여부를 저장하는 상태
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    // 브라우저에서 window 객체가 있는지 확인
    if (typeof window === 'undefined') {
      return;
    }

    // 미디어 쿼리 매처 생성
    const mediaQuery = window.matchMedia(query);

    // 현재 일치 여부 설정
    setMatches(mediaQuery.matches);

    // 미디어 쿼리 변경 감지 함수
    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    // 이벤트 리스너 등록
    mediaQuery.addEventListener('change', handleChange);

    // 클린업 함수: 컴포넌트 언마운트 시 이벤트 리스너 제거
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [query]);

  return matches;
};

export default useMediaQuery;
