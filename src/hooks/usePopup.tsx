import { useOverlay } from '@toss/use-overlay';
import ExportPopup from '@/Common/Popup/ExportPopup.tsx';
import OneButtonPopup from '@/Common/Popup/OneButtonPopup.tsx';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';
import NoButtonPopup from '@/Common/Popup/NoButtonPopup.tsx';
import AlarmCodePopup from '@/Pages/MonitoringEq/components/EqList/Alarm/AlarmCodePopup';

// 내 정보 팝업
import PasswordPopup from '@/Pages/ProfileManagement/Component/PasswordPopup.tsx';
import { ExpendableTabProps } from '@/types';
import ElectricAlarmCodePopup from '@/Pages/MonitoringEq/components/EqList/Alarm/ElectricAlarmCodePopup';
import ExpendableAlarmPopup from '@/Pages/MonitoringEq/components/EqList/Expendables/ExpendableAlarmPopup';

interface PopupContentProps {
  openExportPopup: () => void;
  openOneButtonPopup: () => void;
  openTwoButtonPopup: () => void;

  // 마이페이지
  openPasswordPopup: () => void;

  // 장비 상세
  openAlertPopup: () => void;
  openExpendableAlarmSendPopup: (
    equipmentId: string,
    smodel: string,
    hogiNo: string,
    row: ExpendableTabProps,
  ) => void;
  openAlarmCodePopup: () => void;
  openElectricAlarmCodePopup: () => void;
}

const usePopup = (): PopupContentProps => {
  const overlay = useOverlay();
  const openExportPopup = () => {
    return new Promise<boolean>((resolve) => {
      overlay.open((p) => {
        return (
          <ExportPopup
            isOpen={p.isOpen}
            onClose={() => {
              resolve(false);
              p.close();
            }}
            onConfirm={() => {
              resolve(true);
              p.close();
            }}
          />
        );
      });
    });
  };
  const openOneButtonPopup = () => {
    overlay.open((p) => {
      return (
        <OneButtonPopup
          title={'Title'}
          text={`'TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT ''TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT ''TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT '`}
          buttonText={'TEXT'}
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
        />
      );
    });
  };
  const openTwoButtonPopup = () => {
    overlay.open((p) => {
      return (
        <TwoButtonPopup
          title={'Title'}
          text={`'TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT ''TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT '`}
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
          buttonText={'TEXT'}
          secondButtonText={'TEXT'}
        />
      );
    });
  };
  const openAlertPopup = () => {
    overlay.open((p) => {
      return (
        <NoButtonPopup
          title={'Title'}
          text={`'TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT ''TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT \n' +
            'TEXT TEXT TEXT TEXT TEXT TEXT '`}
          buttonText={'TEXT'}
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
          secondButtonText={'TEXT'}
        />
      );
    });
  };

  // 마이페이지
  const openPasswordPopup = () => {
    overlay.open((p) => {
      return (
        <PasswordPopup
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
        />
      );
    });
  };

  // 장비 상세
  // 소모품 유지보수 알람 메시지 전송
  const openExpendableAlarmSendPopup = (
    equipmentId: string,
    modelName: string,
    plateNo: string,
    row: ExpendableTabProps,
  ) => {
    overlay.open((p) => {
      return (
        <ExpendableAlarmPopup
          equipmentId={equipmentId}
          modelName={modelName}
          plateNo={plateNo}
          row={row}
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
        />
      );
    });
  };
  // 알람코드집
  const openAlarmCodePopup = () => {
    overlay.open((p) => {
      return (
        <AlarmCodePopup
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
        />
      );
    });
  };
  const openElectricAlarmCodePopup = () => {
    overlay.open((p) => {
      return (
        <ElectricAlarmCodePopup
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
        />
      );
    });
  };

  return {
    openExportPopup,
    openOneButtonPopup,
    openTwoButtonPopup,

    // 내 정보 팝업
    openPasswordPopup,

    // 장비 상세
    openAlertPopup,
    openExpendableAlarmSendPopup,
    openAlarmCodePopup,
    openElectricAlarmCodePopup,
  };
};

export default usePopup;
