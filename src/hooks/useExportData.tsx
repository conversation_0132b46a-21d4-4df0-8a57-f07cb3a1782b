import ExcelJS from 'exceljs';
import <PERSON> from 'papaparse';

interface CellStyle {
  font?: {
    bold?: boolean;
    size?: number;
    color?: string;
  };
  fill?: {
    type?: string;
    fgColor?: string;
  };
  alignment?: {
    horizontal?: 'left' | 'center' | 'right';
    vertical?: 'top' | 'middle' | 'bottom';
  };
  border?: {
    top?: { style: string; color: string };
    bottom?: { style: string; color: string };
    left?: { style: string; color: string };
    right?: { style: string; color: string };
  };
}

// 셀 데이터 타입 정의
export interface ExportCellData {
  value: string | number | boolean | null;
  style?: CellStyle;
  rowIndex: number;
  colIndex: number;
  merge?: {
    endRow: number;
    endCol: number;
  };
}

const useExportData = () => {
  // CSV 내보내기
  const exportToCSV = (
    data: Record<string, string | number | boolean | null>[],
    fileName: string,
  ) => {
    const csv = Papa.unparse(data); // 데이터를 CSV로 변환
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    link.click();
  };

  // Excel 내보내기
  const exportToExcel = (
    data: ExportCellData[][],
    styles: CellStyle,
    fileName: string,
  ) => {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Sheet1');

    // 병합 정보 수집
    const merges: Array<{
      startRow: number;
      startCol: number;
      endRow: number;
      endCol: number;
    }> = [];

    // 데이터를 워크시트에 추가
    data.forEach((rowData) => {
      rowData.forEach((cellData: any) => {
        const cell = worksheet.getCell(cellData.rowIndex, cellData.colIndex); // Excel은 1-based index 사용
        cell.value = cellData.value;
        if (cellData.style) {
          // 스타일 적용
          cell.font = { ...cellData.style.font };
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: cellData.style.fill?.fgColor || 'FFFFFF' },
          };
          cell.alignment = { ...cellData.style.alignment };
          cell.border = { ...cellData.style.border };
        } else {
          // 기본 스타일 적용
          if (styles) {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: styles.fill?.fgColor || 'FFFFFF' },
            };
            cell.alignment = { ...styles.alignment };
            if (styles.border) {
              cell.border = {
                top: styles.border.top
                  ? {
                      style: styles.border.top.style as ExcelJS.BorderStyle,
                      color: { argb: styles.border.top.color },
                    }
                  : undefined,
                bottom: styles.border.bottom
                  ? {
                      style: styles.border.bottom.style as ExcelJS.BorderStyle,
                      color: { argb: styles.border.bottom.color },
                    }
                  : undefined,
                left: styles.border.left
                  ? {
                      style: styles.border.left.style as ExcelJS.BorderStyle,
                      color: { argb: styles.border.left.color },
                    }
                  : undefined,
                right: styles.border.right
                  ? {
                      style: styles.border.right.style as ExcelJS.BorderStyle,
                      color: { argb: styles.border.right.color },
                    }
                  : undefined,
              };
            }
          }
        }

        // 병합 정보 수집
        if (cellData.merge) {
          merges.push({
            startRow: cellData.rowIndex,
            startCol: cellData.colIndex,
            endRow: cellData.merge.endRow,
            endCol: cellData.merge.endCol
              ? cellData.merge.endCol
              : cellData.colIndex,
          });
        }
      });
    });

    // 병합 셀 처리
    merges.forEach((merge) => {
      worksheet.mergeCells(
        merge.startRow,
        merge.startCol,
        merge.endRow,
        merge.endCol,
      );
    });

    // 엑셀 파일 다운로드
    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      link.click();
    });
  };

  return { exportToCSV, exportToExcel };
};

export default useExportData;
