import { useEffect, useState } from 'react';
import { Dimension } from '@/types';

export default function useDimension(): Dimension {
  // 초기값을 window.innerWidth로 설정하거나 window 객체가 없는 경우 1920을 기본값으로 사용
  const [width, setWidth] = useState(() =>
    typeof window !== 'undefined' ? window.innerWidth : 1920,
  );
  const [height, setHeight] = useState(() =>
    typeof window !== 'undefined' ? window.innerHeight : 1080,
  );

  const checkDimensions = () => {
    if (typeof window !== 'undefined') {
      setWidth(window.innerWidth);
      setHeight(window.innerHeight);
    }
  };

  useEffect(() => {
    // 컴포넌트 마운트 시 한 번 실행
    checkDimensions();

    // 리사이즈 이벤트 리스너 등록
    window.addEventListener('resize', checkDimensions);
    return () => {
      window.removeEventListener('resize', checkDimensions);
    };
  }, []);

  return { width, height };
}
