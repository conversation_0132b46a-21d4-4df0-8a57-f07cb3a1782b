import React from 'react';
import PageRouter from './PageRouter.tsx';
import Sidebar from './Sidebar.tsx';
import Topbar from './Topbar.tsx';
import Footer from './Footer.tsx';
import { Outlet } from 'react-router-dom';
import { getLoggedInUser, logout } from '@/plugins/api_helper.ts';

import { Toaster } from '@/Common/Components/toast/Toaster.tsx';

const Layout = () => {
  if (!getLoggedInUser()?.access_token) {
    logout();
  }

  return (
    <React.Fragment>
      <div className="main-container mx-auto bg-primary-0-1/50">
        <Sidebar />

        <div className="ml-[240px]">
          <Topbar />
          <div className="h-[calc(100vh)] pt-[74px] relative overflow-y-auto detached-content">
            <Outlet />
            <Toaster />
            <Footer />
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};

export default PageRouter(Layout);
