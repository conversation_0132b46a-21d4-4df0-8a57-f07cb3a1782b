import { useTranslation } from 'react-i18next';
import React from 'react';
import FooterLogo from '@/assets/images/logo/mainLogoF.svg';

const Footer = () => {
  const { t } = useTranslation();
  const yearNum = new Date().getFullYear();

  return (
    <React.Fragment>
      <footer className="w-full py-[26px] pr-8 f-c-b bg-primary-0 border-t border-gray-1">
        {/* 푸터 로고 */}
        <img src={FooterLogo} alt="FooterLogo" />

        <div>
          {/* 약관 */}
          <ul className="f-c-s body5 [&_li]:px-[10px] [&_li:first-child]:pl-0 [&_li:last-child]:pr-0 [&_li]:border-r [&_li:last-child]:border-0 [&_li]:border-gray-6 [&_li]:text-gray-8 [&_li]:leading-[14px] [&_li:not(:first-child)]:cursor-pointer">
            <li>{t('Terms')}</li>
            <li className="font-bold">{t('Conditions')}</li>
          </ul>

          {/*  */}
          <div className="mt-1 caption3 text-gray-8">
            COPYRIGHT © {yearNum} CARTA Mobility. Confidential & Proprietary
          </div>
        </div>
      </footer>
    </React.Fragment>
  );
};

export default Footer;
