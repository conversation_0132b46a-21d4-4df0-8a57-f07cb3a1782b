import { ReactNode } from 'react';
import Dashboard from '@/assets/images/svg/sidebar/Dashboard.tsx';
import Monitoring from '@/assets/images/svg/sidebar/Monitoring.tsx';
import Dispatch from '@/assets/images/svg/sidebar/Dispatch.tsx';
import Fleet from '@/assets/images/svg/sidebar/Fleet.tsx';
import Statistics from '@/assets/images/svg/sidebar/Statistics.tsx';
import Management from '@/assets/images/svg/sidebar/Management.tsx';
import Calendar from '@/assets/images/svg/sidebar/Calendar.tsx';
import Service from '@/assets/images/svg/sidebar/Service.tsx';
import Notice from '@/assets/images/svg/sidebar/Notice.tsx';
import FAQ from '@/assets/images/svg/sidebar/FAQ.tsx';
import QA from '@/assets/images/svg/sidebar/QA.tsx';

export interface SidebarItem {
  id: string;
  label: string;
  link: string;
  icon?: ReactNode;
  iconHover?: string;
  subItems?: SidebarItem[]; // 하위 항목이 있을 경우 재귀적으로 정의
  parentId?: string; // 하위 항목에서 상위 항목을 참조할 때 사용
  isDivide?: boolean; // 구분선을 추가할 때 사용
  disabled?: boolean; // 비활성화 상태를 나타내는 속성 추가
}

const sideBar: SidebarItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    link: '/',
    icon: <Dashboard />,
  },
  {
    id: 'eq_detail',
    label: 'Monitoring',
    link: '/map',
    icon: <Monitoring />,
  },
  {
    id: 'dispatch',
    label: 'Dispatch',
    link: '/dispatch',
    icon: <Dispatch />,
    subItems: [
      {
        id: 'dispatch',
        label: 'Dispatch',
        link: '/dispatch',
        parentId: 'dispatch',
      },
      {
        id: 'history',
        label: 'DispatchHistory',
        link: '/dispatch-history',
        parentId: 'dispatch',
      },
    ],
  },
  {
    id: 'fleet_management',
    label: 'Fleet',
    link: '/fleet',
    icon: <Fleet />,
    subItems: [
      {
        id: 'management',
        label: 'FleetManagement',
        link: '/fleet-management',
        parentId: 'fleet_management',
      },
      {
        id: 'driver',
        label: 'DriverManagement',
        link: '/fleet-driver',
        parentId: 'fleet_management',
      },
      {
        id: 'vehicle',
        label: 'VehicleManagement',
        link: '/fleet-vehicle',
        parentId: 'fleet_management',
      },
      {
        id: 'fault',
        label: 'FaultManagement',
        link: '/fleet-fault',
        parentId: 'fleet_management',
      },
      {
        id: 'consumables',
        label: 'ConsumablesManagement',
        link: '/fleet-consumables',
        parentId: 'fleet_management',
      },
      {
        id: 'accident',
        label: 'AccidentManagement',
        link: '/fleet-accident',
        parentId: 'fleet_management',
      },
      {
        id: 'dealer',
        label: 'DealerServiceCenterManagement',
        link: '/fleet-dealer',
        parentId: 'fleet_management',
      },
      {
        id: 'report',
        label: 'Report',
        link: '/fleet-report',
        parentId: 'fleet_management',
      },
    ],
  },
  {
    id: 'statics',
    label: 'Statistics',
    link: '/statics',
    icon: <Statistics />,
    disabled: true,
    subItems: [
      {
        id: 'breakdown',
        label: 'FaultMaintenanceAnalysis',
        link: '/statics/breakdown',
        parentId: 'statics',
      },
      {
        id: '',
        label: 'ProductivityEfficiencyAnalysis',
        link: '/productivity-efficiency',
        parentId: 'statics',
      },
      {
        id: 'equipment_operation_trend',
        label: 'WorkingTransitionAnalysis',
        link: '/equipment-operation-trend',
        parentId: 'statics',
      },
      {
        id: 'equipment_uptime',
        label: 'WorkingHourAnalysis',
        link: '/equipment-uptime',
        parentId: 'statics',
      },
      {
        id: 'fuel',
        label: 'FuelAnalysis',
        link: '/fuel',
        parentId: 'statics',
      },
      {
        id: 'battery',
        label: 'BatteryAnalysis',
        link: '/battery',
        parentId: 'statics',
      },
      {
        id: 'shock',
        label: 'CollisionAnalysis',
        link: '/shock',
        parentId: 'statics',
      },
      {
        id: 'heatmap',
        label: 'Heatmap',
        link: '/heatmap',
        parentId: 'statics',
      },
      {
        id: 'access',
        label: 'ConnectionAnalysis',
        link: '/access',
        parentId: 'statics',
      },
    ],
  },
  {
    id: 'management',
    label: 'Management',
    link: '/management',
    icon: <Management />,
    disabled: true,
    subItems: [
      {
        id: 'user',
        label: 'UserManagement',
        link: '/user',
        parentId: 'management',
      },
      {
        id: 'service',
        label: 'ServiceHistory',
        link: '/service',
        parentId: 'management',
      },
      {
        id: 'dealership',
        label: 'DealershipEquipmentStatus',
        link: '/dealership',
        parentId: 'management',
      },
    ],
  },
  {
    id: 'table',
    label: 'Calendar',
    link: '/calendar',
    icon: <Calendar />,
    disabled: true,
  },
  {
    id: 'service',
    label: 'ServiceInformation',
    link: '/service',
    icon: <Service />,
    disabled: true,
    subItems: [
      {
        id: 'manual',
        label: 'ManualDownload',
        link: '/manual',
      },
      {
        id: 'tsg',
        label: 'TSG',
        link: '/tsg',
      },
    ],
  },
  {
    id: 'notice',
    label: 'Notice',
    link: '/notice',
    icon: <Notice />,
    isDivide: true,
  },
  {
    id: 'FAQ',
    label: 'FAQ',
    link: '/faq',
    icon: <FAQ />,
  },
  {
    id: 'QnA',
    label: 'QNA',
    link: '/qna',
    icon: <QA />,
  },
];

export { sideBar };
