import {
  Location,
  NavigateFunction,
  Params,
  useLocation,
  useNavigate,
  useParams,
} from 'react-router-dom';
import { ReactElement, ReactNode } from 'react';

export type PropsWithChildren = {
  children?: ReactNode;
  router?: {
    location: Location;
    navigate: NavigateFunction;
    params: Readonly<Params>;
  };
  back?: boolean;
  name?: string;
  className?: string;
  onBackClick?: () => void;
};

function PageRouter(Component: (p: PropsWithChildren) => ReactElement) {
  function ComponentPageRouterProp(props: PropsWithChildren) {
    const location = useLocation();
    const navigate = useNavigate();
    const params = useParams();
    return <Component {...props} router={{ location, navigate, params }} />;
  }

  return ComponentPageRouterProp;
}

export default PageRouter;
