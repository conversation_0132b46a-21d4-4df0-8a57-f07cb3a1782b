import { useTranslation } from 'react-i18next';
import React, { useState, useCallback } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { AnimatePresence, motion } from 'framer-motion';
import { cn } from '@/Common/function/utils.ts';
import { sideBar } from './SidebarData';
import { useToast } from '@/Common/useToast.tsx';
import mainLogo from '@/assets/images/logo/mainLogoS.svg';
import IcArrowDown from '@/assets/images/svg/24/IcArrowDown';

const Sidebar: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const location = useLocation();
  const stored = localStorage.getItem('activeMenu');
  const [activeMenu, setActiveMenu] = useState<number | null>(Number(stored));

  const showUnsupported = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      toast({ types: 'warning', description: t('ItsNotYetAvailable') });
    },
    [toast, t],
  );

  const toggle = (idx: number) => {
    const next = activeMenu === idx ? null : idx;
    setActiveMenu(next);
    localStorage.setItem('activeMenu', JSON.stringify(next));
  };

  return (
    <nav className="sidebar h-full bg-primary-10 fixed z-50 !rounded-b-none w-[240px]">
      <Link to="/" className="w-full ">
        <img src={mainLogo} alt="logo" className="mx-auto py-10" />
      </Link>

      <div className="w-full h-[calc(100vh-80px)] pb-12 px-2 overflow-y-auto hide-scrollbar">
        <ul className="mt-[6px] space-y-2 flex flex-col">
          {sideBar.map((item, idx) => {
            const disabled = item.disabled;
            const hasSub = !!item.subItems;
            const expanded = hasSub && activeMenu === idx;
            const leafActive = !hasSub && location.pathname === item.link;

            const wrapperClasses = cn(
              'w-full flex items-center gap-[10px] rounded-[5px] py-[10px] px-[14px]',
              {
                'hover:bg-white': !disabled && !expanded,
                '[&_span]:hover:text-secondary-6 [&_path]:hover:stroke-secondary-6 [&_circle]:hover:fill-secondary-6':
                  !disabled && !expanded,
                'bg-white [&_span]:text-secondary-6 [&_path]:stroke-secondary-6 [&_circle]:fill-secondary-6':
                  leafActive,
                'text-gray-1': !leafActive && !expanded,
              },
            );

            return (
              <li
                key={idx}
                className={cn(
                  expanded ? 'bg-primary-8/60 rounded-md pb-2' : '',
                  item.isDivide && 'border-t border-primary-7 pt-2',
                )}
              >
                <Link
                  to={disabled ? '#' : hasSub ? '#' : item.link}
                  className={cn('w-full flex items-center justify-between', {
                    active: leafActive,
                  })}
                  onClick={(e) => {
                    if (disabled) return showUnsupported(e);
                    if (hasSub) toggle(idx);
                  }}
                >
                  <div className={wrapperClasses}>
                    {item.icon && (
                      <div className="w-6 h-6 flex-shrink-0">{item.icon}</div>
                    )}
                    <span className="w-full subhead2 text-gray-1 whitespace-pre-line">
                      {t(item.label)}
                    </span>
                    {hasSub && !disabled && (
                      <IcArrowDown
                        className={cn(
                          'w-6 h-6 flex-shrink-0 transition-transform duration-200',
                          { 'rotate-180': expanded },
                        )}
                      />
                    )}
                  </div>
                </Link>

                {hasSub && !disabled && (
                  <AnimatePresence initial={false}>
                    {expanded && (
                      <motion.ul
                        className={cn('space-y-2')}
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                        style={{ overflow: 'hidden' }}
                        key="submenu"
                      >
                        {item.subItems!.map((sub, sidx) => {
                          const subActive = location.pathname === sub.link;
                          return (
                            !sub.subItems && (
                              <li key={sidx} className="px-2">
                                <Link
                                  to={sub.disabled ? '#' : sub.link}
                                  className={cn('w-full', {
                                    active: subActive,
                                  })}
                                  onClick={(e) =>
                                    sub.disabled && showUnsupported(e)
                                  }
                                >
                                  <div
                                    className={cn(
                                      'w-full flex items-center gap-[10px] rounded-[5px] py-[6px] pl-12 pr-2',
                                      {
                                        'hover:bg-white': !sub.disabled,
                                        '[&_span]:hover:text-secondary-6':
                                          !sub.disabled,
                                        'bg-white [&_span]:text-secondary-6':
                                          subActive,
                                        'text-gray-1': !subActive,
                                      },
                                    )}
                                  >
                                    <span className="subtitle6 text-gray-1 whitespace-pre-line">
                                      {t(sub.label)}
                                    </span>
                                  </div>
                                </Link>
                              </li>
                            )
                          );
                        })}
                      </motion.ul>
                    )}
                  </AnimatePresence>
                )}
              </li>
            );
          })}
        </ul>
      </div>
    </nav>
  );
};

export default Sidebar;
