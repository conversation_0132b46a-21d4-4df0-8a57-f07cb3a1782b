﻿import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Theme } from '@radix-ui/themes';
import { AppProviders } from '@/context/AppProviders';
import { APIProvider } from '@vis.gl/react-google-maps';
import Routing from './Route/Index';

import 'remixicon/fonts/remixicon.css';
import '@radix-ui/themes/styles.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import 'react-dropdown/style.css';
import './assets/scss/plugins/react-datepicker.css';
import './assets/scss/plugins/calendar.css';
import '@/i18n/i18n.js';

// Fake backend
import { OverlayProvider } from '@toss/use-overlay';
import { UserLocationProvider } from '@/logiMaps/react/common/Location';

const isDevelopment = process.env.NODE_ENV === 'development';
console.log(`현재 실행 모드: ${isDevelopment ? '개발 모드' : '프로덕션 모드'}`);

// Create a client
const queryClient = new QueryClient();

function App() {
  return (
    <div className="App">
      <QueryClientProvider client={queryClient}>
        <AppProviders>
          <APIProvider
            apiKey={'AIzaSyD_AsNxL2hbgbZv3iKZKhxzxvIWUYcWb60'}
            onLoad={() => console.log('Maps API has loaded.')}
          >
            <UserLocationProvider>
              <OverlayProvider>
                <Theme>
                  <Routing />
                </Theme>
              </OverlayProvider>
            </UserLocationProvider>
          </APIProvider>
        </AppProviders>
        {/* {isDevelopment && <ReactQueryDevtools initialIsOpen={false} />} */}
      </QueryClientProvider>
    </div>
  );
}

export default App;
