import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { authenticationApi } from '@/api';
import type { AuthRequestDTO, AuthResponseDTO } from '@/api/generated';
import { AxiosResponse } from 'axios';

const useLoginQuery = () => {
  // 로그인 mutation
  const useMutationLogin = (
    p?: UseMutationOptions<
      AxiosResponse<AuthResponseDTO>,
      Error,
      AuthRequestDTO,
      unknown
    >,
  ) => {
    return useMutation<
      AxiosResponse<AuthResponseDTO>,
      Error,
      AuthRequestDTO,
      unknown
    >({
      mutationKey: ['login'],
      mutationFn: async (authRequestDTO: AuthRequestDTO) => {
        // mutationFn은 항상 Promise를 반환해야 함
        return authenticationApi.login({
          authRequestDTO,
        });
      },
      ...p, // 옵션 병합
    });
  };

  return {
    useMutationLogin,
  };
};

export default useLoginQuery;
