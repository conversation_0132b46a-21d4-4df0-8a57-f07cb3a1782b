// ResetPw.tsx
import { useTranslation } from 'react-i18next';
import { useMemo, useState } from 'react';
import { isPasswordValid } from '@/utils/regex/validators.ts';
import { validatePassword } from '@/utils/regex/regex';
import Input from '@/Common/Components/common/Input';
import PasswordRegex from '@/Common/Components/common/PasswordRegex';
import { Button } from '@/Common/Components/common/Button';
import notice from '@/assets/images/ic/16/pw_reset_notice.svg';

interface ResetPwProps {
  onClose: () => void;
  onConfirm: () => void;
}

const ResetPw = ({ onClose, onConfirm }: ResetPwProps) => {
  const { t } = useTranslation();

  const [password, setPassword] = useState('');
  const [confirm, setConfirm] = useState('');
  const [touchedPw, setTouchedPw] = useState(false);
  const [touchedConfirm, setTouchedConfirm] = useState(false);

  // 상세 규칙 결과 (validatePassword가 atLeast3Ok, tripleSeqOk를 포함)
  const detail = useMemo(() => validatePassword(password), [password]);
  const { lengthOk, atLeast3Ok, tripleSeqOk } = detail;

  // 제출 가능 기준 (validators.ts에서 length/allowed/atLeast3/tripleSeq 모두 체크)
  const valid = isPasswordValid(password);
  const same = password === confirm;

  const pwError = touchedPw && !valid ? t('') : '';

  const confirmError = touchedConfirm && !same ? t('PasswordsDoNotMatch') : '';

  // api 연결 후 직전 비밀번호 에러 메세지 띄워야 함

  return (
    <article>
      <div className="px-9">
        {/* 새 비밀번호 */}
        <Input
          type="password"
          placeholder={t('EnterNewPassword')}
          className="mb-3"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          onBlur={() => setTouchedPw(true)}
          error={pwError}
        />

        {/* 새 비밀번호 확인 */}
        <Input
          type="password"
          placeholder={t('ConfirmNewPassword')}
          className="mb-[30px]"
          value={confirm}
          onChange={(e) => setConfirm(e.target.value)}
          onBlur={() => setTouchedConfirm(true)}
          error={confirmError}
          success={password && confirm && same ? t('PasswordsMatch') : ''}
        />

        {/* 규칙 안내 (충족 시 체크 아이콘 색상 변화) */}
        <PasswordRegex
          status={{
            lengthOk,
            atLeast3Ok,
            tripleSeqOk,
          }}
        />

        {/* 안내 */}
        <div className="mb-10 f-s gap-2">
          <img src={notice} alt="notice" />
          <h3 className="caption4 text-gray-10 whitespace-pre">
            {t(
              'ForSecurityYouWillNeSignedOutOfAllDevicesAndBrowsersWhenYourPasswordIsChanged',
            )}
          </h3>
        </div>

        {/* 버튼 */}
        <div className="w-full f-c gap-[10px]">
          <Button
            variant="bt_secondary"
            label={t('Logout')}
            className="w-full"
            onClick={onClose}
          />
          <Button
            variant="bt_primary"
            label={t('ChangePassword')}
            className="w-full"
            disabled={!valid || !same}
            onClick={() => {
              setTouchedPw(true);
              setTouchedConfirm(true);
              if (!valid || !same) return;
              onConfirm?.();
            }}
          />
        </div>
      </div>
    </article>
  );
};

export default ResetPw;
