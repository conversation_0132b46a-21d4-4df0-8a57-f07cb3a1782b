import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import { Button } from '@/Common/Components/common/Button';
import Input from '@/Common/Components/common/Input';

const SettingPw = ({ isOpen, onClose, onConfirm }: AlertPopupProps) => {
  const { t } = useTranslation();

  return (
    <Layout isOpen={isOpen}>
      <div className="relative bg-white rounded-lg pb-[30px]">
        <div className="w-full left-[20px] mt-[50px] text-center text-[22px] font-semibold leading-7 mb-6">
          {t('ResetPassword')}
        </div>
        <div className={'space-y-4 px-5 mb-6'}>
          <Input
            placeholder={t('NewPassword')}
            type={'password'}
            error={'dsfd'}
          ></Input>
          <Input
            placeholder={t('ConfirmNewPassword')}
            type={'password'}
            success={'asdsad'}
          ></Input>
        </div>
        <div className="w-[555px] h-[150px] bg-[#f6f6f6] rounded-sm mx-5 flex flex-col justify-center items-start gap-2 px-5">
          <div className="w-[514.41px] text-[#3f3f3f] text-sm font-normal">
            {t('EnterBetween8To12Characters')}
          </div>
          <div className="w-[514.41px] text-[#3f3f3f] text-sm font-normal">
            {t('MixTypesLettersNumbersAndSpecialCharacters')}
          </div>
          <div className="w-[514.41px] text-[#3f3f3f] text-sm font-normal">
            {t('NoConsecutive3CharactersOrNumbersInARowOnTheKeyboard')}
          </div>
        </div>
        <div
          className={'px-5 flex justify-center items-center gap-1 mt-[60px]'}
        >
          <Button
            variant={'bt_primary'}
            label={'Cancel'}
            className={'flex-1'}
            onClick={onClose}
          />
          <Button
            variant={'bt_primary'}
            label={'Next'}
            className={'flex-1'}
            onClick={onConfirm}
          />
        </div>
      </div>
    </Layout>
  );
};
export default SettingPw;
