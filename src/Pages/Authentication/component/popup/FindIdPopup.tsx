import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout';
import { Button } from '@/Common/Components/common/Button';
import { AlertPopupProps } from '@/types';
import { Box, Tabs } from '@radix-ui/themes';
import Input from '@/Common/Components/common/Input';
import Dropdown from '@/Common/Components/common/DropDown';

const FindId = ({ isOpen, onClose, onConfirm }: AlertPopupProps) => {
  const { t } = useTranslation();

  return (
    <Layout isOpen={isOpen}>
      <div className="relative bg-white rounded-lg pb-[30px]">
        <div className="w-full left-[20px] mt-[50px] text-center text-[22px] font-semibold leading-7 mb-6">
          {t('FindID')}
        </div>
        <Tabs.Root defaultValue={'email'}>
          <Tabs.List className="tab-design">
            <Tabs.Trigger value="email">
              <span className="w-[292px]">{t('FindByEMailAddress')}</span>
            </Tabs.Trigger>
            <Tabs.Trigger value="number">
              <span className="w-[292px]">{t('FindByPhoneNumber')}</span>
            </Tabs.Trigger>
          </Tabs.List>

          <Box>
            <Tabs.Content value="email">
              <div
                className={
                  'px-5 flex justify-center items-center gap-1 mt-[40px] mb-[60px]'
                }
              >
                <Input
                  placeholder={t('EMailE')}
                  width={'flex-[3]'}
                  className={'w-full'}
                ></Input>
                <div className="w-3.5 text-center text-black text-sm font-normal leading-[14px]">
                  @
                </div>
                <Dropdown
                  className={
                    '!flex-[2] h-[50px]  bg-white rounded border border-[#d9d9d9]'
                  }
                  onChange={() => undefined}
                  options={[]}
                  placeholder={t('SelectDomain')}
                ></Dropdown>
              </div>
            </Tabs.Content>

            <Tabs.Content value="number">
              <div
                className={
                  'px-5 flex justify-center items-center gap-1 mt-[40px] mb-[60px]'
                }
              >
                <Dropdown
                  className={
                    '!flex-[2] h-[50px]  bg-white rounded border border-[#d9d9d9]'
                  }
                  onChange={() => undefined}
                  options={[]}
                  placeholder={'+82(대한민국)'}
                ></Dropdown>

                <Input
                  placeholder={t('EnterPhoneNumberNoDashes')}
                  width={'flex-[3]'}
                  className={'w-full'}
                ></Input>
              </div>
            </Tabs.Content>
          </Box>
        </Tabs.Root>
        <div></div>
        <div className={'px-5 flex justify-center items-center gap-3'}>
          <Button
            variant={'bt_primary'}
            label={'Cancel'}
            className={'flex-1'}
            onClick={onClose}
          />
          <Button
            variant={'bt_primary'}
            label={'Next'}
            className={'flex-1'}
            onClick={onConfirm}
          />
        </div>
      </div>
    </Layout>
  );
};

export default FindId;
