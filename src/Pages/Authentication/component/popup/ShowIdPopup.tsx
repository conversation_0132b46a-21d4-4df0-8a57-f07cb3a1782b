import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout';
import { Button } from '@/Common/Components/common/Button';
import { AlertPopupProps } from '@/types';

const ShowId = ({ isOpen, onClose, onConfirm }: AlertPopupProps) => {
  const { t } = useTranslation();

  return (
    <Layout isOpen={isOpen}>
      <div className="relative bg-white rounded-lg pb-[30px]">
        <div className="w-full left-[20px] mt-[50px] text-center text-[22px] font-semibold leading-7 mb-6">
          {t('FindId')}
        </div>
        <div className="w-[555px] h-[150px] bg-[#f6f6f6] rounded-sm mx-5 flex flex-col justify-center items-center gap-2">
          <div className="w-[514.42px] text-center text-[#7b7b7b] text-base font-normal">
            {t('YourIDHasBeenFound')}
          </div>
          <div className="w-[514.42px] text-center text-base font-bold">
            User_123
          </div>
        </div>
        <div
          className={'px-5 flex justify-center items-center gap-1 mt-[60px]'}
        >
          <Button
            variant={'bt_primary'}
            label={'Cancel'}
            className={'flex-1'}
            onClick={onClose}
          />
          <Button
            variant={'bt_primary'}
            label={'Next'}
            className={'flex-1'}
            onClick={onConfirm}
          />
        </div>
      </div>
    </Layout>
  );
};

export default ShowId;
