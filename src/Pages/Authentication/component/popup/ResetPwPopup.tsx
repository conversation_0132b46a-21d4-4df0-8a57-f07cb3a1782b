import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { AlertPopupProps } from '@/types';
import { useToast } from '@/Common/useToast.tsx';
import Layout from '@/Common/Popup/Layout';
import EnterEmail from '@/Pages/Authentication/component/resetPw/EnterEmail';
import SendEmail from '@/Pages/Authentication/component/resetPw/SendEmail';
import ResetPw from '@/Pages/Authentication/component/resetPw/ResetPw';
import mainLogoO from '@/assets/images/logo/mainLogoF.svg';

const ResetPwPopup = ({ isOpen, onClose }: AlertPopupProps) => {
  const { t } = useTranslation();

  const { toast } = useToast();

  const [step, setStep] = useState<'enter' | 'send' | 'reset'>('enter');

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[594px] popup-wrap">
        <article className="f-c-c flex-col">
          <img src={mainLogoO} alt="logo" />
          <h2 className="mt-[33px]">{t('ResetPassword')}</h2>
          {step === 'enter' && (
            <h3 className="mt-[6px] caption1">
              {t('PleaseEnterTheEmailAddressYouUsedToSignUp')}
            </h3>
          )}
          {step === 'reset' && (
            <h3 className="mt-[6px] caption1">
              {t('PleaseEnterYourNewPassword')}
            </h3>
          )}
        </article>

        {step === 'enter' && (
          <EnterEmail
            onClose={onClose}
            onConfirm={() => {
              setStep('send');
              toast({
                types: 'success',
                description: t('APasswordResetEmailHasBeenResent'),
              });
            }}
          />
        )}

        {step === 'send' && <SendEmail onClose={onClose} />}

        {/* 메일을 보낸 이메일에서 비밀번호 재설정 클릭 시 해당 컴포넌트 사용 */}
        {/* <ResetPw onClose={onClose} onConfirm={() => setStep('reset')} /> */}
      </section>
    </Layout>
  );
};

export default ResetPwPopup;
