import { useTranslation } from 'react-i18next';
import { useOverlay } from '@toss/use-overlay';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';
import OneButtonPopup from '@/Common/Popup/OneButtonPopup.tsx';
import FindIdPopup from '@/Pages/Authentication/component/popup/FindIdPopup';
import ShowIdPopup from '@/Pages/Authentication/component/popup/ShowIdPopup';
import ResetPwPopup from '@/Pages/Authentication/component/popup/ResetPwPopup';
import SettingPwPopup from '@/Pages/Authentication/component/popup/SettingPwPopup';

const useLogin = () => {
  const { t } = useTranslation();

  const overlay = useOverlay();

  const openLoginAttemptsPopup = () => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          close();
        }}
        isOpen={isOpen}
        title={t('TooManyLoginAttempts')}
        text={t(
          'ThisAccountHasBeenLockedDueToTooManyFailedLoginAttemptsPleaseResetYourPasswordToRegainAccess',
        )}
        buttonText={t('Cancel')}
        secondButtonText={t('ResetPassword')}
      />
    ));
  };
  const openLoginFailedPopup = () => {
    overlay.open(({ isOpen, close }) => (
      <OneButtonPopup
        onClose={close}
        onConfirm={() => {
          close();
        }}
        isOpen={isOpen}
        title={t('LoginFailed')}
        text={t('TemporaryErrorSystemAccessFailedPleaseTryAgainLater')}
        buttonText={t('Cancel')}
      />
    ));
  };
  const openFindIdPopup = () => {
    overlay.open((p) => {
      return (
        <FindIdPopup
          buttonText={t('Cancel')}
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
        />
      );
    });
  };
  const openShowIdPopup = () => {
    overlay.open((p) => {
      return (
        <ShowIdPopup
          buttonText={t('Cancel')}
          secondButtonText={t('ResetPassword')}
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
        />
      );
    });
  };
  const openResetPwPopup = () => {
    overlay.open((p) => {
      return (
        <ResetPwPopup
          buttonText={t('Cancel')}
          secondButtonText={t('Next')}
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
        />
      );
    });
  };
  const openSettingPwPopup = () => {
    overlay.open((p) => {
      return (
        <SettingPwPopup
          buttonText={t('Cancel')}
          secondButtonText={t('Next')}
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
        />
      );
    });
  };
  return {
    openLoginAttemptsPopup,
    openLoginFailedPopup,
    openFindIdPopup,
    openShowIdPopup,
    openResetPwPopup,
    openSettingPwPopup,
  };
};

export default useLogin;
