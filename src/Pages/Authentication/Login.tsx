import { useTranslation } from 'react-i18next';
import { t } from 'i18next';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import useLoginQuery from '@/Pages/Authentication/query';
import { setLoggedInUser } from '@/plugins/api_helper.ts';
import useLoginPopup from '@/Pages/Authentication/component/useLoginPopup';
import Dropdown from '@/Common/Components/common/DropDown';
import Input from '@/Common/Components/common/Input';
import Checkbox from '@/Common/Components/common/CheckBox';
import { Button } from '@/Common/Components/common/Button';
import mainLogoL from '@/assets/images/logo/mainLogoL.svg';

type LoginFormValues = z.infer<typeof loginSchema>;

const loginSchema = z.object({
  loginId: z.string().min(6, t('PleaseEnterYourUsername')),
  password: z.string().min(6, t('PasswordMustBeAtLeast6CharactersLong')),
});

const Login = () => {
  const { t, i18n } = useTranslation();

  const isLanguageChange = (selectedValue: string) => {
    const langCode = selectedValue === t('English') ? 'en' : 'ko';
    i18n.changeLanguage(langCode);
  };
  const selectedValue = i18n.language === 'en' ? t('English') : t('Korean');

  const { openFindIdPopup, openResetPwPopup } = useLoginPopup();

  const [serverError, setServerError] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      loginId: '005001',
      password: '',
    },
  });

  const loginIdVal = watch('loginId');
  const passwordVal = watch('password');

  useEffect(() => {
    if (serverError) setServerError(false);
  }, [loginIdVal, passwordVal]);

  const { useMutationLogin } = useLoginQuery();
  const navigate = useNavigate();

  const { mutate } = useMutationLogin({
    onSuccess: (p) => {
      const isOk = setLoggedInUser(p.data);
      if (isOk) navigate('/');
    },
    onError: () => {
      setServerError(true);
    },
  });

  const onSubmit = (data: LoginFormValues) => {
    setServerError(false);
    mutate(data);
  };

  const noFieldErrors = !errors.loginId && !errors.password;

  return (
    <section className="w-full max-w-[1200px] grid grid-cols-2 gap-0 bg-white rounded-[20px]">
      {/* 왼쪽 이미지 */}
      <div className="h-full f-e col-span-1 relative [&>div]:rounded-l-[20px]">
        <div className="bg-primary-10/80 absolute inset-0 z-[5]" />
        <div className="bg-[url('/src/assets/images/etc/login_main.png')] bg-cover absolute inset-0" />
        <p className="max-w-[406px] w-full mb-[50px] ml-10 font-bruno-ace text-2xl text-white z-[50]">
          {t('OPTIMIZINGTRANSPORTATIONFLEETMANAGEMENTFORSMARTEROPERATIONS')}
        </p>
      </div>

      {/* 로그인 폼 영역 */}
      <div className="py-[50px] px-[66px] col-span-1">
        <div className="w-full mb-[110px] flex justify-end">
          <Dropdown
            size="sm"
            placeholder={selectedValue}
            options={[
              { key: t('English'), value: 'English' },
              { key: t('Korean'), value: 'korean' },
            ]}
            onSelKey={isLanguageChange}
          />
        </div>

        <div className="mb-[60px] f-jc">
          <img src={mainLogoL} />
        </div>

        {/* 로그인 폼 */}
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="mb-[120px] space-y-3"
        >
          <Input
            placeholder={t('Username')}
            {...register('loginId')}
            error={
              errors.loginId?.message ||
              (serverError && noFieldErrors ? ' ' : '')
            }
          />
          <Input
            placeholder={t('PasswordE')}
            type="password"
            {...register('password')}
            error={
              errors.password?.message ||
              (serverError && noFieldErrors
                ? t('Incorrect username or password.')
                : '')
            }
          />

          <div className="f-c-b">
            <Checkbox label={t('RememberMe')} />
            <div className="f-c [&>span]:body4 [&>span]:text-gray-10 [&>span]:cursor-pointer">
              <span onClick={openFindIdPopup}>{t('ForgotUsername')}</span>
              <div className="divider-v h-[14px] mx-2" />
              <span onClick={openResetPwPopup}>{t('ResetPassword')}</span>
            </div>
          </div>

          <Button
            type="submit"
            variant="bt_primary"
            label="Login"
            style={{ marginTop: '60px' }}
            className="w-full"
          />
          <div className="space-x-1 body4 text-center">
            <span className="text-gray-8">{t('DontHaveAnAccount')}</span>
            <span className="text-semantic-2 cursor-pointer">
              {t('Register')}
            </span>
          </div>
        </form>

        <div className="f-c-c body5 text-gray-7">
          <Link to={'#'}>{t('PrivacyPolicy')}</Link>
          <div className="divider-v h-[14px] mx-2" />
          <Link to={'#'}>{t('TermsOfUse')}</Link>
        </div>
      </div>
    </section>
  );
};

export default Login;
