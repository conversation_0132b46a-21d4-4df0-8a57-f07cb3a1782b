import { useNavigate } from 'react-router-dom';
import { PropsWithChildren } from '@/Layout/PageRouter.tsx';
import { cn } from '@/Common/function/utils.ts';
import arrowBack from '@/assets/images/ic/24/back.svg';

export const CustomFrame = ({
  children,
  back,
  name,
  className,
  onBackClick,
}: PropsWithChildren) => {
  const navigate = useNavigate();
  return (
    <main
      className={cn(
        'max-w-[1674px] min-w-[1670px] min-h-[calc(100%+100px)] mx-auto py-10 px-8',
        className,
      )}
    >
      <div className={'mb-5 f-c gap-2'}>
        {back && (
          <img
            src={arrowBack}
            alt={'Back'}
            className={'cursor-pointer'}
            onClick={() => {
              if (onBackClick) {
                onBackClick();
              } else {
                navigate(-1);
              }
            }}
          />
        )}
        <div className="subtitle2">{name}</div>
      </div>
      <div>{children}</div>
    </main>
  );
};
