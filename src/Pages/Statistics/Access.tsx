import { useTranslation } from 'react-i18next';
import { Tabs } from '@radix-ui/themes';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { useState } from 'react';
import AccessLog from '@/Pages/Statistics/components/access/AccessLog.tsx';
import PageAccess from '@/Pages/Statistics/components/access/PageAccess.tsx';

const Access = () => {
  const { t } = useTranslation();

  const [value, setValue] = useState('AccessLog');
  return (
    <CustomFrame name={t('ConnectionAnalysis')}>
      <Tabs.Root value={value} onValueChange={setValue}>
        <Tabs.List className={'tab-design'}>
          <Tabs.Trigger value={'AccessLog'}>
            <span>{t('AccessLog')}</span>
          </Tabs.Trigger>
          <Tabs.Trigger
            value={'PageConnectionAnalysis'}
            disabled
            className="opacity-50 cursor-not-allowed"
          >
            <span>{t('PageConnectionAnalysis')}</span>
          </Tabs.Trigger>
        </Tabs.List>
        <AccessLog />
        <PageAccess />
      </Tabs.Root>
    </CustomFrame>
  );
};

export default Access;
