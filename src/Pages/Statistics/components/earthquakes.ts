import { FeatureCollection, Point } from 'geojson';
import { v4 } from 'uuid';

export type EarthquakeProps = {
  id: string;
  mag: number;
  time: number;
  felt: number | null;
  tsunami: 0 | 1;
};

export type EarthquakesGeojson = FeatureCollection<Point, EarthquakeProps>;

export const testData: EarthquakesGeojson = {
  type: 'FeatureCollection',
  features: [
    {
      type: 'Feature',
      properties: {
        id: v4(),
        mag: 0.5,
        time: 1507425650893,
        felt: null,
        tsunami: 0,
      },
      geometry: {
        type: 'Point',
        coordinates: [127.0418205, 37.5425858],
      },
    },
    {
      type: 'Feature',
      properties: {
        id: v4(),
        mag: 1,
        time: 1507425650893,
        felt: null,
        tsunami: 0,
      },
      geometry: {
        type: 'Point',
        coordinates: [127.0438205, 37.5425858],
      },
    },
    {
      type: 'Feature',
      properties: {
        id: v4(),
        mag: 1.5,
        time: 1507425650893,
        felt: null,
        tsunami: 0,
      },
      geometry: {
        type: 'Point',
        coordinates: [127.0458205, 37.5425858],
      },
    },
    {
      type: 'Feature',
      properties: {
        id: v4(),
        mag: 2,
        time: 1507425650893,
        felt: null,
        tsunami: 0,
      },
      geometry: {
        type: 'Point',
        coordinates: [127.0478205, 37.5425858],
      },
    },
  ],
};
