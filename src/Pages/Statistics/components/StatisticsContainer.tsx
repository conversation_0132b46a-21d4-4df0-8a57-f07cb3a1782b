import { HTMLAttributes } from 'react';
import { cn } from '@/Common/function/utils.ts';

const StatisticsContainer = (p: HTMLAttributes<HTMLDivElement>) => {
  return (
    <div
      className={cn(
        'p-[30px] rounded-[8px] bg-white w-full shadow-[0px_4px_12px_0px_rgba(0,0,0,0.08)]',
        p.className,
      )}
    >
      {p.children}
    </div>
  );
};

export default StatisticsContainer;
