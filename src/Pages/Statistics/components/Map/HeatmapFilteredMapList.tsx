import HeatmapFilteredItem from '@/Pages/Statistics/components/Map/HeatmapFilteredItem';
import { useEffect, useRef, useState } from 'react';
import { FixedSizeList as List } from 'react-window';
import Checkbox from '@/Common/Components/common/CheckBox';

import { Button } from '@/Common/Components/common/Button';
import { StatisticsType } from '@/types/StatisticsType';

const HeatmapVehicleViewList = ({
  // eslint-disable-next-line react/prop-types
  items,
  // eslint-disable-next-line react/prop-types
  onRefreshClick,
}: StatisticsType.HeatmapVehicleViewListProps) => {
  const [sortedItems, setSortedItems] = useState<
    StatisticsType.HeatmapFilteredMapItem[]
  >([]);
  const [checked, setChecked] = useState(false);
  const [width, setWidth] = useState(0);
  const [height, setHeight] = useState(0);
  const listContainer = useRef<HTMLDivElement>(null);

  //container div 크기 추적
  useEffect(() => {
    let resizeObserver: ResizeObserver;

    if (listContainer.current) {
      resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (entry.target === listContainer.current) {
            setWidth(entry.contentRect.width);
            setHeight(entry.contentRect.height);
          }
        }
      });

      resizeObserver.observe(listContainer.current);
    }

    return () => {
      if (listContainer.current && resizeObserver) {
        resizeObserver.unobserve(listContainer.current);
      }
    };
  }, [listContainer.current]);

  useEffect(() => {
    setSortedItems(
      [...items].sort((a, b) =>
        compareSort(a.item.modelName, b.item.modelName, true),
      ),
    );
  }, [items]);

  const compareSort = (a: string, b: string, asc: boolean) => {
    if (asc) {
      return a > b ? 1 : -1;
    } else {
      return a < b ? 1 : -1;
    }
  };

  const onChecked = (id: string | null, checked: boolean) => {
    let _sortedItems: StatisticsType.HeatmapFilteredMapItem[];

    if (id === null) {
      _sortedItems = sortedItems.map((item) => ({
        ...item,
        checked: checked,
      }));
    } else {
      _sortedItems = sortedItems.map((item) => ({
        ...item,
        checked: item.item.id === id ? checked : item.checked,
      }));
    }
    setSortedItems(_sortedItems);

    if (checked) {
      if (_sortedItems.filter((item) => item.checked === false).length == 0) {
        setChecked(checked);
      }
    } else {
      if (_sortedItems.filter((item) => item.checked === false).length > 0) {
        setChecked(checked);
      }
    }
  };

  return (
    <div className="absolute left-0 bottom-0 bg-white w-[438px] h-[calc(100%-74px)] px-5 py-4 overflow-hidden">
      <div className={'p-2 pr-4 flex justify-between items-center gap-2'}>
        <Button
          variant={'bt_primary'}
          label={'Refresh'}
          onClick={() => {
            onRefreshClick?.([...sortedItems]);
          }}
        />
        <Checkbox
          checked={checked}
          onCheckedChange={(checked) => {
            if (typeof checked === 'boolean') {
              onChecked(null, checked);
            }
          }}
        />
      </div>

      {sortedItems.length == 0 && <HeatmapFilteredItem key={0} />}

      <div ref={listContainer} className={'p-2 h-full'}>
        <List
          width={width}
          height={height}
          itemCount={sortedItems.length}
          itemSize={160}
        >
          {({
            index,
            style,
          }: {
            index: number;
            style: React.CSSProperties;
          }) => (
            <div style={style} key={sortedItems[index].item.id}>
              <HeatmapFilteredItem
                item={sortedItems[index].item}
                checked={sortedItems[index].checked}
                onChecked={onChecked}
              />
            </div>
          )}
        </List>
      </div>
    </div>
  );
};

export default HeatmapVehicleViewList;
