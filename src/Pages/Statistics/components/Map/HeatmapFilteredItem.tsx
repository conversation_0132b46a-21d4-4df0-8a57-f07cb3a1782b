import { useTranslation } from 'react-i18next';
import { Fragment } from 'react';
import { Link } from 'react-router-dom';
import { getEqStatIcon, getEqTypeIcon } from '@/Common/function/functions.ts';
import Checkbox from '@/Common/Components/common/CheckBox';
import { StatisticsType } from '@/types/StatisticsType';

// 히트 맵 조회 결과 사이드 팝업 컴포넌트
const HeatmapFilteredItem = (props: StatisticsType.HeatmapFilterItemProps) => {
  const { t } = useTranslation();

  return (
    <Fragment>
      {!props.item && (
        <>
          <div className="h-[100px] flex items-center p-4 text-gray-500 text-opacity-50">
            {t('NoDataAvailable')}
          </div>
          <hr className={'w-full'} />
        </>
      )}
      {props.item && (
        <>
          <div className="h-[153px] flex justify-start items-center p-4 w-full gap-6">
            <div className={'space-y-1 text-center w-20'}>
              <img src={getEqTypeIcon(props.item.machineType)} />
              {/* <img src={getEqStatIcon(props.item.eqStat)} /> */}
            </div>
            <div className={'flex-1'}>
              <div className="w-full h-6 justify-between items-center gap-2 inline-flex">
                <div className="w-full h-6 justify-start items-center gap-2 inline-flex">
                  <div className="text-base font-bold leading-snug">
                    {/* 모델명 */}
                    {props.item.modelName}
                  </div>
                  <div className="text-base font-semibold leading-snug">/</div>
                  <Link
                    to={'/eq_list'}
                    className="text-[#4c8af7] text-base font-semibold underline leading-snug"
                  >
                    {/* 호기 */}
                    {props.item.plateNo}
                  </Link>
                </div>
                <Checkbox
                  checked={props.checked}
                  onCheckedChange={(checked) => {
                    if (typeof checked === 'boolean') {
                      if (props.item && props.onChecked) {
                        props.onChecked(props.item.id, checked);
                      }
                    }
                  }}
                />
              </div>
              <div className="w-full h-6 justify-start items-center gap-6 inline-flex">
                <div className="justify-start items-center gap-5 flex">
                  <div className="text-[#7b7b7b] text-sm font-semibold leading-tight">
                    {t('SerialNo')}
                  </div>
                  <div className="text-sm font-semibold leading-tight">
                    {/* 관리번호 */}
                    {props.item.serialNo}
                  </div>
                </div>
              </div>
              <div className="w-full h-6 justify-start items-center gap-6 inline-flex">
                <div className="justify-start items-center gap-5 flex">
                  <div className="text-[#7b7b7b] text-sm font-semibold leading-tight">
                    {t('Location')}
                  </div>
                  <div className="text-sm font-semibold leading-tight">
                    {/* 위치 */}
                    {props.item.location}
                  </div>
                </div>
              </div>
              <div className="w-full h-6 justify-start items-center gap-6 inline-flex">
                <div className="justify-start items-center gap-5 flex">
                  <div className="text-[#7b7b7b] text-sm font-semibold leading-tight">
                    {t('Dealership')}
                  </div>
                  <div className="text-sm font-semibold leading-tight">
                    {/* 대리점 */}
                    {props.item.dealer}
                  </div>
                </div>
              </div>
              <div className="w-full h-5 justify-start items-center gap-5 inline-flex">
                <div className="text-[#7b7b7b] text-sm font-semibold leading-tight">
                  {t('Mileage')}
                </div>
                <div className="text-sm font-semibold leading-tight">
                  {/* 작동 시간 */}
                  {props.item.mileage}
                </div>
              </div>
            </div>
          </div>
          <hr className={'w-full'} />
        </>
      )}
    </Fragment>
  );
};

export default HeatmapFilteredItem;
