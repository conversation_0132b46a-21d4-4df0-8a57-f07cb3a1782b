import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import { Fragment, useRef } from 'react';
import { Popover } from '@radix-ui/themes';
import Dropdown from '@/Common/Components/common/DropDown';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import filter from '@/assets/images/etc/filter.svg';

export interface HeatmapSearchParams {
  model: string;
  custNo: string;
  date: string;
}

interface HeatmapSearchFilterProps {
  onSearch?: (params: HeatmapSearchParams) => void;
}

/**
 * Heatmap - 검색 필터
 */
const SearchFilter: React.FC<HeatmapSearchFilterProps> = ({ onSearch }) => {
  const { t } = useTranslation();

  const filterBtnRef = useRef<HTMLDivElement>(null);
  const filterImgRef = useRef<HTMLImageElement>(null);

  //모델 필터
  const defaultModelKey = 'ALL';
  const [, /*modelSelKey*/ setModelSelKey] = useState(defaultModelKey);
  const modelOptions = [
    { key: 'ALL', value: 'ALL' },
    { key: 'TEST', value: 'TEST' },
  ];
  const onChangeModelFilter = (value: string) => {
    setModelSelKey(value);
  };
  //관리 번호 필터
  const defaultCustNo = '';
  const [custNo, setCustNo] = useState(defaultCustNo);
  const onChangeCustNoFilter = (value: string) => {
    setCustNo(value);
    if (/^[a-zA-Z0-9]+$/.test(value) == false) {
      setCustNo('');
    }
  };

  return (
    <Fragment>
      <Popover.Root>
        <Popover.Trigger>
          <div
            ref={filterBtnRef}
            aria-checked={'false'}
            className="right-6 top-24 absolute z-10 group"
            onClick={() => {
              if (filterBtnRef.current) {
                if (filterBtnRef.current.ariaChecked === 'true') {
                  filterBtnRef.current.ariaChecked = 'false';
                  if (filterImgRef.current) {
                    filterImgRef.current.src = filter;
                  }
                } else {
                  filterBtnRef.current.ariaChecked = 'true';
                  if (filterImgRef.current) {
                    filterImgRef.current.src = filter;
                  }
                }
              }
            }}
          >
            <img src={filter} ref={filterImgRef} alt={'filter'} />
          </div>
        </Popover.Trigger>
        <Popover.Content
          size="1"
          minWidth="213px"
          maxWidth="400px"
          className={
            '!p-0 w-[162px] relative bg-white rounded-lg border border-[#e6e6e6] !pb-6'
          }
        >
          <div className={'space-y-2 px-4 py-6'}>
            <SearchItemContainer
              className={'py-2 justify-start flex-col items-start'}
            >
              <SearchItemContainer className={'justify-start'}>
                <div className="text-xs font-bold leading-none w-[80px]">
                  {t('Model')}
                </div>
                <div className="flex flex-wrap items-center gap-3 flex-1">
                  <Dropdown
                    className={'h-9'}
                    options={modelOptions}
                    placeholder={defaultModelKey}
                    onSelKey={onChangeModelFilter}
                  />
                </div>
              </SearchItemContainer>
              <SearchItemContainer className={'justify-start'}>
                <div className="text-xs font-bold leading-none w-[80px]">
                  {t('SerialNo')}
                </div>
                <div className="flex flex-wrap items-center gap-3 flex-1">
                  <Input
                    className={'h-9'}
                    value={custNo}
                    placeholder={t('SerialNo')}
                    onChange={(e) => {
                      onChangeCustNoFilter(e.target.value);
                    }}
                  />
                </div>
              </SearchItemContainer>
              <SearchItemContainer className={'justify-start'}>
                <div className="text-xs font-bold leading-none w-[80px]">
                  {t('Date')}
                </div>
                <div className="flex flex-wrap items-center gap-3 flex-1">
                  <FromToSelector fontSize="text-sm" />
                </div>
              </SearchItemContainer>
            </SearchItemContainer>
          </div>
          <div className={'flex gap-[6px] px-4 justify-center'}>
            <Button
              variant={'bt_primary'}
              label={'Search'}
              className={'w-[130px]'}
              onClick={() => {
                const searchParams: HeatmapSearchParams = {
                  model: '',
                  custNo: '',
                  date: '',
                };
                onSearch?.(searchParams);
              }}
            />
            <Button
              variant={'bt_primary'}
              label={'Reset'}
              className={'w-[130px]'}
              onClick={() => {
                onChangeModelFilter(defaultModelKey);
                onChangeCustNoFilter(defaultCustNo);
              }}
            />
          </div>
        </Popover.Content>
      </Popover.Root>
    </Fragment>
  );
};
SearchFilter.displayName = 'SearchFilter';
export default SearchFilter;
