import { useTranslation } from 'react-i18next';
import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';
import React, { useEffect, useState } from 'react';

interface BreakdownStatisticsProps {
  fleetValue: string;
  totalAlarm?: (data: {
    totalCnt?: number;
    pendingCnt?: number;
    responseCnt?: number;
    completedCnt?: number;
  }) => void;
}

// 구조와 UI만 유지, API 관련 로직/타입 제거, 더미 데이터 적용
const BreakdownStatistics: React.FC<BreakdownStatisticsProps> = ({
  fleetValue,
  totalAlarm,
}) => {
  const { t } = useTranslation();

  // 더미 데이터만 사용
  const [fleetBreakdownOccurrenceData] = useState<{
    totalCnt?: number;
    pendingCnt?: number;
    responseCnt?: number;
    completedCnt?: number;
  }>({
    totalCnt: 24,
    pendingCnt: 7,
    responseCnt: 9,
    completedCnt: 8,
  });

  useEffect(() => {
    if (totalAlarm) {
      totalAlarm(fleetBreakdownOccurrenceData);
    }
  }, [fleetBreakdownOccurrenceData, totalAlarm]);

  return (
    <StatisticsContainer className={'flex gap-7 items-center'}>
      <div className={'flex-1'}>
        <div className="text-lg font-semibold ">{t('TotalAlarm')}</div>
        <div className="justify-end items-center gap-1 flex h-[67px]">
          <div className="text-right text-[56px] font-light">
            {fleetBreakdownOccurrenceData?.totalCnt}
          </div>
          <div className="text-right text-[32px] font-light">{t('Cases')}</div>
        </div>
      </div>
      <div className="w-px h-[62px] opacity-80 bg-[#b3b3b3] rounded-[8px]"></div>
      <div className={'flex-1'}>
        <div className="text-lg font-semibold ">{t('InProgress')}</div>
        <div className="justify-end items-center gap-1 flex h-[67px]">
          <div className="text-right text-[56px] font-light">
            {fleetBreakdownOccurrenceData?.pendingCnt}
          </div>
          <div className="text-right text-[32px] font-light">{t('Cases')}</div>
        </div>
      </div>
      <div className="w-px h-[62px] opacity-80 bg-[#b3b3b3] rounded-[8px]"></div>
      <div className={'flex-1'}>
        <div className="text-lg font-semibold ">{t('Pending')}</div>
        <div className="justify-end items-center gap-1 flex h-[67px]">
          <div className="text-right text-[56px] font-light">
            {fleetBreakdownOccurrenceData?.responseCnt}
          </div>
          <div className="text-right text-[32px] font-light">{t('Cases')}</div>
        </div>
      </div>
      <div className="w-px h-[62px] opacity-80 bg-[#b3b3b3] rounded-[8px]"></div>
      <div className={'flex-1'}>
        <div className="text-lg font-semibold ">{t('Resolved')}</div>
        <div className="justify-end items-center gap-1 flex h-[67px]">
          <div className="text-right text-[56px] font-light">
            {fleetBreakdownOccurrenceData?.completedCnt}
          </div>
          <div className="text-right text-[32px] font-light">{t('Cases')}</div>
        </div>
      </div>
    </StatisticsContainer>
  );
};

export default BreakdownStatistics;
