import { useTranslation } from 'react-i18next';
import { v4 } from 'uuid';
import React from 'react';

import arrow_drop_down from '@/assets/images/arrow/arrow_drop_down.png';
import arrow_drop_up from '@/assets/images/arrow/arrow_drop_up.png';
import arrow_eq from '@/assets/images/arrow/arrow_eq.svg';
import { FleetType } from '@/types/FleetType';
import { StatisticsType } from '@/types/StatisticsType';

const ModelBreakdownAlarm: React.FC<FleetType.ExpendableStatisticsProps> = ({
  fleetValue,
}) => {
  const { t } = useTranslation();

  // 더미 데이터
  const DUMMY_ROWS = [
    { name: '160D-9', hitNumber: 23, upDown: true, increase: 1, rowNum: 1 },
    { name: '110D-9', hitNumber: 18, upDown: true, increase: 3, rowNum: 2 },
    { name: '80D-9', hitNumber: 15, upDown: true, increase: -1, rowNum: 3 },
    { name: '250D-9', hitNumber: 10, upDown: true, increase: 1, rowNum: 4 },
    { name: '70D-9', hitNumber: 7, upDown: true, increase: 4, rowNum: 5 },
  ];

  // 필요한 경우 fleetValue로 DUMMY_ROWS를 필터링하거나 변형할 수 있음
  // 예시: fleetValue에 따라 정렬 혹은 변경 필요하면 여기에 로직 추가

  return (
    <div className="w-full h-full flex relative bg-white rounded-[8px] shadow-[0px_4px_12px_0px_rgba(0,0,0,0.08)]">
      <div className={'flex-1 p-[30px] space-y-[30px]'}>
        <div className="text-lg font-semibold leading-[27px]">
          {t('FaultAlarmRankingByModel')}
        </div>
        <div className={'space-y-3 overflow-y-auto h-[calc(100%-60px)]'}>
          {DUMMY_ROWS.map((item, index) => (
            <Row
              key={v4()}
              name={item.name}
              hitNumber={item.hitNumber}
              upDown={item.upDown}
              increase={item.increase}
              rowNum={index + 1}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

const Row = ({
  rowNum,
  name,
  hitNumber,
  increase,
}: StatisticsType.RowStatProps) => {
  const { t } = useTranslation();

  return (
    <div>
      <div className="h-3.5 mb-3 flex justify-between items-center px-9">
        <div className="h-6 flex items-center gap-2 overflow-hidden">
          <div className="w-[15px] text-black text-base font-bold">
            {rowNum}.
          </div>
          <div className="text-base font-medium">{name}</div>
          <div className="text-gray-9 body3-n [&_em]:text-gray-9 [&_em]:ml-1">
            {hitNumber}
            <em>{t('Hours')}</em>
          </div>
        </div>
        <div>
          {increase === 1 ? (
            <img src={arrow_drop_up} className="w-6 h-6 relative" />
          ) : increase === 0 ? (
            <img src={arrow_eq} className="w-6 h-6 relative" />
          ) : (
            <img src={arrow_drop_down} className="w-6 h-6 relative" />
          )}
        </div>
      </div>
      <hr />
    </div>
  );
};

export default ModelBreakdownAlarm;
