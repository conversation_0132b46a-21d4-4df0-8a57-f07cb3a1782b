import { useTranslation } from 'react-i18next';
import React, { useEffect, useState } from 'react';
import ECharts from 'echarts-for-react';
import Dropdown from '@/Common/Components/common/DropDown';
import { LineGraphOption as BaseLineGraphOption } from '@/Common/constants/GraphOptions.ts';
import { v4 } from 'uuid';
import dayjs from 'dayjs';
import cloneDeep from 'lodash/cloneDeep'; // lodash 필요 (불변성 유지용)
import i18n from 'i18next';
import { StatisticsType } from '@/types/StatisticsType';

/**
 * 대시보드에서 사용되는 라인 그래프 컴포넌트
 * @param title - 그래프 제목
 * @param data - 그래프 데이터 배열 (time, value)
 */
const BreakdownLineGraph: React.FC<StatisticsType.LineGraphProps> = ({
  title,
  data,
}) => {
  const { t } = useTranslation();
  const [selectDateType, setSelectDateType] = useState<string>('0');
  const [graphOption, setGraphOption] = useState(() =>
    cloneDeep(BaseLineGraphOption),
  );

  useEffect(() => {
    const updatedOption = cloneDeep(BaseLineGraphOption);

    if (selectDateType === '0') {
      // 일별 (시간 단위)
      const dataDay: StatisticsType.DataLineChat[] = [
        { date: '12:00', value: 10 },
        { date: '13:00', value: 30 },
        { date: '14:00', value: 45 },
        { date: '15:00', value: 80 },
        { date: '16:00', value: 65 },
        { date: '17:00', value: 149 },
        { date: '18:00', value: 120 },
        { date: '19:00', value: 100 },
      ];
      updatedOption.xAxis.data = dataDay.map((e) => e.date);
      updatedOption.series[0].data = dataDay.map((e) => e.value);
    } else if (selectDateType === '1') {
      // 월별 (일 단위)
      const lastMonthDay = Number(
        dayjs().subtract(1, 'month').endOf('month').format('DD'),
      );

      const dataMonth: StatisticsType.DataLineChat[] = [];
      for (let i = 1; i <= lastMonthDay; i++) {
        // 0이 아닌 1일부터 시작
        dataMonth.push({
          date: i.toString(), // 접미사(1st, 2nd 등) 없이 숫자만
          value: i <= 4 ? (i + 1) * 10 : 0,
        });
      }
      updatedOption.xAxis.data = dataMonth.map((e) => e.date);
      updatedOption.series[0].data = dataMonth.map((e) => e.value);
    } else {
      // 연도별 (월 단위)
      const monthKeys = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      const dataYear: StatisticsType.DataLineChat[] = monthKeys.map(
        (key, i) => ({
          date: t(key),
          value: i < 4 ? (i + 1) * 10 : 0,
        }),
      );
      updatedOption.xAxis.data = dataYear.map((e) => e.date);
      updatedOption.series[0].data = dataYear.map((e) => e.value);
    }

    setGraphOption(updatedOption);
  }, [selectDateType, i18n.language]);

  return (
    <div className="flex flex-col w-full h-full relative bg-white rounded-[8px] shadow-[0px_4px_12px_0px_rgba(0,0,0,0.08)]">
      {/* 헤더 */}
      <div className="px-[30px] pt-[30px] pb-2 text-lg font-semibold leading-[27px] flex justify-between items-center">
        {title}
        <div className="flex gap-2 items-center">
          <Dropdown
            className="w-20"
            placeholder={t('Daily')}
            options={[
              { key: t('Daily'), value: '0' },
              { key: t('Monthly'), value: '1' },
              { key: t('Year'), value: '2' },
            ]}
            onChange={(v) => setSelectDateType(v as string)}
          />
        </div>
      </div>
      <div className="w-full h-full">
        <ECharts
          key={v4()}
          option={graphOption}
          style={{ height: '100%', maxWidth: 780, margin: 'auto' }}
        />
      </div>
    </div>
  );
};

export default BreakdownLineGraph;
