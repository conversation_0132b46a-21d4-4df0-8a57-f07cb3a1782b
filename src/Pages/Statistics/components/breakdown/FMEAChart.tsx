import { useTranslation } from 'react-i18next';
import React, { useEffect, useRef, useMemo } from 'react';
import ECharts from 'echarts-for-react';
import { FMEAChartOption } from '@/Common/constants/GraphOptions.ts';
import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';
import { cn } from '@/Common/function/utils.ts';

interface FMEAChartProps {
  fleetValue: string;
}

const FMEAChart: React.FC<FMEAChartProps> = ({ fleetValue }) => {
  const { t } = useTranslation();
  const chartRef = useRef<ECharts>(null);

  // Mock 데이터 (API 로직 제거)
  const mockData = [
    { faultLvl: '1', cnt: 10 },
    { faultLvl: '2', cnt: 5 },
    { faultLvl: '3', cnt: 8 },
    { faultLvl: '4', cnt: 12 },
    { faultLvl: '5', cnt: 3 },
  ];

  const totalCount = useMemo(() => {
    return mockData.reduce((sum, item) => sum + (item?.cnt ?? 0), 0);
  }, []);

  const percentageData = (level: string) => {
    const item = mockData.find((item) => item.faultLvl === level);
    if (!item || totalCount === 0) {
      return 0;
    } else {
      return Math.round(((item.cnt ?? 0) / totalCount) * 100);
    }
  };

  // 차트 데이터 생성 - useMemo로 최적화
  const data = useMemo(
    () => [
      { name: t('Warning2'), value: percentageData('1') },
      { name: t('ServiceSoon'), value: percentageData('4') },
      { name: t('ServiceNow'), value: percentageData('3') },
      { name: t('StopSoon'), value: percentageData('2') },
      { name: t('StopNow'), value: percentageData('5') },
    ],
    [t, totalCount],
  );

  // 차트 옵션 생성 - useMemo로 최적화
  const chartOptions = useMemo(() => {
    const options = { ...FMEAChartOption };
    console.log(options);
    options.series[0].data = data;
    options.series[0].radius = ['40%', '70%'];
    options.series[0].top = 0;
    options.tooltip = {
      trigger: 'item',
      formatter: '{b}: {c}%', // 이름: 값% 형식으로 표시
    };
    return options;
  }, [data]);

  // 컴포넌트 마운트 및 데이터 변경 시 차트 리사이즈
  useEffect(() => {
    const chart = chartRef.current;
    if (chart) {
      setTimeout(() => {
        chart.getEchartsInstance().resize();
      }, 0);
    }
  }, []);

  // 데이터 변경 시 차트 인스턴스 업데이트
  useEffect(() => {
    const chart = chartRef.current;
    if (chart) {
      chart.getEchartsInstance().setOption(chartOptions);
    }
  }, [chartOptions]);

  return (
    <StatisticsContainer className={'flex flex-col md:flex-row p-10'}>
      <div className={'w-full md:w-1/2 flex flex-col mb-2 md:mb-0'}>
        <div className="text-lg font-semibold mb-1">
          {t('RankingByFaultSeverity')}
        </div>
        <div className={'flex-1 flex flex-col items-start justify-center'}>
          <div className="text-[#7b7b7b] text-lg font-medium">
            {t('TotalAlarm')}
          </div>
          <div className="justify-start items-center gap-[5px] inline-flex">
            <div className="text-[74px] font-light leading-tight">
              {totalCount}
            </div>
            <div className="text-[44px] font-light">{t('Cases')}</div>
          </div>
        </div>
        <div className={'flex-wrap flex gap-3 max-w-[320px]'}>
          {chartOptions.series[0].color.map((color: string, index: number) => (
            <div
              className={
                'text-[#3f3f3f] text-xs font-medium flex justify-center items-center gap-1'
              }
              key={color}
            >
              <div
                style={{ backgroundColor: color }}
                className={cn('w-2.5 h-2.5 rounded-full')}
              />
              {data[index]?.name}
            </div>
          ))}
        </div>
      </div>
      <div className={'w-full md:w-1/2 flex items-center justify-center'}>
        <div className="w-full" style={{ height: '315px', paddingTop: '20px' }}>
          <ECharts
            ref={chartRef}
            style={{ width: '100%', height: '100%' }}
            option={chartOptions}
            opts={{ renderer: 'svg' as const }}
            notMerge={true}
          />
        </div>
      </div>
    </StatisticsContainer>
  );
};

export default FMEAChart;
