import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import close_popup from '@/assets/images/etc/close_popup.png';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import { ColumnDef } from '@tanstack/react-table';
import CustomColumnHeader from '@/Common/Components/etc/CustomColumnHeader';
import CustomColumnDataCell from '@/Common/Components/etc/CustomColumnDataCell';
import CommonTable from '@/Common/Components/common/CommonTable';
import { StatisticsType } from '@/types/StatisticsType';

const WorkerEqStatisticsPopup = ({ isOpen, onClose }: AlertPopupProps) => {
  const { t } = useTranslation();

  const columns: ColumnDef<StatisticsType.WorkerEqStatisticsTable>[] = [
    {
      accessorKey: 'name',
      header: () => <CustomColumnHeader>{t('Operator')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.name}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'allEqTime',
      header: () => (
        <CustomColumnHeader>{t('TotalOperatingHours')}</CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.allEqTime}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'workTime',
      header: () => <CustomColumnHeader>{t('WorkingH')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.workTime}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'driveTime',
      header: () => <CustomColumnHeader>{t('Traveling')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.driveTime}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'restTime',
      header: () => <CustomColumnHeader>{t('Idling')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.restTime}</CustomColumnDataCell>
      ),
    },
  ];
  return (
    <Layout isOpen={isOpen}>
      <div className="w-[1280px] bg-white rounded flex-col justify-start items-start inline-flex">
        <div className="self-stretch p-10 rounded-lg justify-between items-center flex">
          <div className="self-stretch justify-start items-center gap-2 inline-flex">
            <div className="text-2xl font-bold leading-normal">
              {t('EquipmentOperationAnalysisByOperator')}
            </div>
          </div>
          <img
            src={close_popup}
            className="w-6 h-6 cursor-pointer"
            onClick={onClose}
          />
        </div>
        <div className={'px-10 w-full space-y-8'}>
          <div className={'flex justify-between items-center '}>
            <SearchItemContainer className={'gap-6'}>
              <SearchItemContainer>
                <SearchLabel>{t('Fleet')}</SearchLabel>
                <DropDown
                  onChange={() => {}}
                  options={[]}
                  placeholder={t('All')}
                />
              </SearchItemContainer>
              <SearchItemContainer>
                <SearchLabel>{t('Date')}</SearchLabel>
                <FromToSelector />
              </SearchItemContainer>
            </SearchItemContainer>

            <SearchItemContainer>
              <Button
                variant={'bt_primary'}
                label={'aaa'}
                className={'text-lg font-Search'}
              />
              <Button
                variant={'bt_primary'}
                label={'Download'}
                className={'text-lg font-semibold'}
              />
            </SearchItemContainer>
          </div>
          <div className={'flex justify-end items-center gap-2'}>
            <DropDown
              onChange={() => {}}
              options={[]}
              placeholder={t('Operator')}
            />
            <DropDown
              onChange={() => {}}
              options={[]}
              placeholder={t('DescendingOrder')}
            />
          </div>
        </div>
        <div className={'px-10 pb-10 w-full mt-1 h-[506px]'}>
          <CommonTable data={[]} columns={columns} isPagination={false} />
        </div>
      </div>
    </Layout>
  );
};

export default WorkerEqStatisticsPopup;
