import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';
import {
  CustomTable,
  CustomTableBody,
  CustomTableCell,
  CustomTableHead,
  CustomTableHeader,
  CustomTableRow,
} from '@/Common/Components/common/CustomTable';
import { ColumnDef, flexRender, Table } from '@tanstack/react-table';
import { v4 } from 'uuid';
import { useElectronicTab } from '@/store/detail-tab.ts';

const StatisticsTable = <P,>({
  table,
  columns,
}: {
  table: Table<P>;
  columns: ColumnDef<P>[];
}) => {
  const { t } = useTranslation();

  const { isElectronic } = useElectronicTab((s) => s);
  return useMemo(
    () => (
      <CustomTable className={'border border-[#bgbgbg]'}>
        <CustomTableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <CustomTableRow key={headerGroup.id} className={''}>
              {headerGroup.headers.map((header) => (
                <CustomTableHead
                  key={header.id}
                  style={{
                    whiteSpace: 'nowrap',
                    width: header.column.getSize(),
                  }}
                  className={'h-[62px]  bg-[#487fdf]/5'}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </CustomTableHead>
              ))}
            </CustomTableRow>
          ))}
        </CustomTableHeader>
        <CustomTableBody>
          {table?.getCoreRowModel().rows.length ? (
            table?.getCoreRowModel().rows.map((row) => (
              <CustomTableRow
                onClick={() => {}}
                key={v4()}
                className={'h-[68px]'}
                style={{ cursor: 'pointer' }}
              >
                {row?.getAllCells().map((cell) => (
                  <CustomTableCell
                    style={{ width: cell.column.getSize() }}
                    key={v4()}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </CustomTableCell>
                ))}
              </CustomTableRow>
            ))
          ) : (
            <CustomTableRow className={'h-full'}>
              <CustomTableCell
                colSpan={columns.length}
                className="text-center h-[53px]"
              >
                {t('NoDataAvailable')}
              </CustomTableCell>
            </CustomTableRow>
          )}
        </CustomTableBody>
      </CustomTable>
    ),
    [isElectronic],
  );
};

export default StatisticsTable;
