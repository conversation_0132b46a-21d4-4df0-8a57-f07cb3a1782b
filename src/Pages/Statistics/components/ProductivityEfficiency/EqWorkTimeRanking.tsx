import { useTranslation } from 'react-i18next';
import arrow_drop_down from '@/assets/images/arrow/arrow_drop_down.png';
import arrow_drop_up from '@/assets/images/arrow/arrow_drop_up.png';
import { v4 } from 'uuid';
import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';
import { StatisticsType } from '@/types/StatisticsType';

const EqWorkTimeRanking = ({
  eq,
  title,
}: StatisticsType.StatisticsImpactProps) => {
  const { t } = useTranslation();

  const Title = title || t('Charging Count by Equipment');

  return (
    <StatisticsContainer className="flex flex-col h-full">
      <div className="text-lg font-semibold leading-[27px] mb-[30px]">
        {Title}
      </div>
      <div className={'space-y-2 overflow-y-auto flex-1'}>
        {eq.slice(0, 5).map((e: StatisticsType.RowProps, index) => (
          <Row key={v4()} {...e} rowNum={index + 1} />
        ))}
      </div>
    </StatisticsContainer>
  );
};

const Row = ({
  rowNum,
  name,
  number,
  upDown,
  where,
  time,
}: StatisticsType.RowProps & {
  number?: string;
  where?: string;
  time?: string;
}) => {
  return (
    <div>
      <div className="w-full mb-2 justify-between items-center inline-flex px-9">
        <div className="w-full h-6 justify-start items-center gap-2 flex overflow-hidden">
          <div className="text-black text-base font-bold">{rowNum}</div>

          <div className="text-base font-medium">{name}</div>
          {number && (
            <>
              <div className="text-[#cccccc] text-base font-normal">|</div>
              <div className="text-[#7b7b7b] text-base font-normal">
                {number}
              </div>
            </>
          )}
          {where && (
            <>
              <div className="text-[#cccccc] text-base font-normal">|</div>
              <div className="text-base font-medium">{where}</div>
            </>
          )}
          <div className="text-[#7b7b7b] text-base font-normal">{time}</div>
          <div className={'flex-1 flex justify-end'}>
            {upDown ? (
              <img src={arrow_drop_up} className="w-6 h-6 relative" />
            ) : (
              <img src={arrow_drop_down} className="w-6 h-6 relative" />
            )}
          </div>
        </div>
      </div>
      <hr />
    </div>
  );
};

export default EqWorkTimeRanking;
