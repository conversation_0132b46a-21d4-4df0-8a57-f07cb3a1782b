import { EqOperationRateOption } from '@/Common/constants/GraphOptions.ts';
import ECharts from 'echarts-for-react';
import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';
import { StatisticsType } from '@/types/StatisticsType';

const EqOperationRate = ({ title, data }: StatisticsType.IndexChartProps) => {
  // 객체를 직접 수정하지 않고 복제하여 사용
  const chartOption = {
    ...EqOperationRateOption,
    series: [
      {
        ...EqOperationRateOption.series[0],
        // 그래프 위치 조정 값 수정
        top: 0,
        right: 0,
        left: 0,
        data: data,
      },
    ],
  };

  return (
    <StatisticsContainer className="w-full h-full flex flex-col">
      <div className="text-lg font-semibold">{title}</div>
      <div className="h-full flex items-center justify-center">
        <ECharts
          style={{ width: '370px', height: '100%' }}
          option={chartOption}
        ></ECharts>
      </div>
    </StatisticsContainer>
  );
};

export default EqOperationRate;
