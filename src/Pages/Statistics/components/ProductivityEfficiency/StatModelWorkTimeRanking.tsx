import { useTranslation } from 'react-i18next';
import arrow_drop_down from '@/assets/images/arrow/arrow_drop_down.png';
import arrow_drop_up from '@/assets/images/arrow/arrow_drop_up.png';
import { v4 } from 'uuid';
import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';
import { StatisticsType } from '@/types/StatisticsType';

const StatModelWorkTimeRanking = ({
  eq,
  title,
}: StatisticsType.StatisticsImpactProps) => {
  const { t } = useTranslation();

  const Title = title || t('WorkingRankingByModel');

  const dummyData = [
    { name: 'HXDEMO', hitNumber: 120, upDown: true },
    { name: 'HX380LS', hitNumber: 98, upDown: false },
    { name: 'HL955T3', hitNumber: 87, upDown: true },
    { name: 'R505LVSS', hitNumber: 76, upDown: false },
    { name: 'HXDEMO23', hitNumber: 65, upDown: true },
  ];

  const dataToRender = eq && eq.length > 0 ? eq.slice(0, 5) : dummyData;

  return (
    <StatisticsContainer className="flex flex-col h-full">
      <div className="text-lg font-semibold leading-[27px] mb-[30px]">
        {Title}
      </div>
      <div className={'space-y-2 overflow-y-auto flex-1'}>
        {dataToRender.map((item, index) => (
          <Row
            key={v4()}
            name={item.name}
            hitNumber={item.hitNumber}
            upDown={item.upDown}
            rowNum={index + 1}
          />
        ))}
      </div>
    </StatisticsContainer>
  );
};

const Row = ({ rowNum, name, hitNumber, upDown }: StatisticsType.RowProps) => {
  const { t } = useTranslation();

  return (
    <div>
      <div className="w-full mb-2 justify-between items-center inline-flex px-9">
        <div className="w-full h-6 justify-start items-center gap-2 flex overflow-hidden">
          <div className=" text-black text-base font-bold">{rowNum}.</div>
          <div className="text-base font-medium">{name}</div>
          <div className="text-gray-9 body3-n [&_em]:text-gray-9 [&_em]:ml-1">
            {hitNumber !== undefined ? hitNumber.toLocaleString() : '-'}
            <em>{t('Hours')}</em>
          </div>
          <div className={'flex-1 flex justify-end'}>
            {upDown ? (
              <img src={arrow_drop_up} className="w-6 h-6 relative" />
            ) : (
              <img src={arrow_drop_down} className="w-6 h-6 relative" />
            )}
          </div>
        </div>
      </div>
      <hr />
    </div>
  );
};

export default StatModelWorkTimeRanking;
