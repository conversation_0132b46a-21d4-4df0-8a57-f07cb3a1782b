import { useTranslation } from 'react-i18next';
import { WorkTimeRankingOption } from '@/Common/constants/GraphOptions.ts';
import ECharts from 'echarts-for-react';
import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';
import { Button } from '@/Common/Components/common/Button';
import { useEffect, useMemo, useState } from 'react';
import i18n from 'i18next';
import {
  formatSecondsToHours,
  formatYearMonth,
} from '@/Common/function/date.ts';
import * as echarts from 'echarts';

type JobRestTimeProps = {
  workData: {
    issueDate: string | number;
    workingHour: number | string;
    travelHour: number | string;
    idleHour: number | string;
  }[];
};

const WorkTimeRanking = ({ workData }: JobRestTimeProps) => {
  const { t } = useTranslation();
  const [currentLang, setCurrentLang] = useState(i18n.language);

  // 언어에 따른 월 표시 설정
  const getMonthLabels = useMemo(() => {
    // 한국어일 경우 '월'을 붙이고, 그 외 언어는 숫자만 표시
    if (workData && workData.length > 1) {
      return [
        formatYearMonth(String(workData[1].issueDate), currentLang),
        formatYearMonth(String(workData[0].issueDate), currentLang),
      ];
    } else {
      return [];
    }
  }, [workData]);

  // 총 주행 시간+ 작업 시간 사용하여 계산된 값을 캐싱
  const travelWorkTime = useMemo(() => {
    if (workData && workData.length > 1) {
      return formatSecondsToHours(
        Number(workData[1].workingHour) + Number(workData[1].travelHour),
      );
    }
    return 0;
  }, [workData]);

  // 이전달 총 주행 시간+ 작업 시간 사용하여 계산된 값을 캐싱
  const beforeTravelWorkTime = useMemo(() => {
    if (workData && workData.length > 1) {
      return formatSecondsToHours(
        Number(workData[0].workingHour) + Number(workData[0].travelHour),
      );
    }
    return 0;
  }, [workData]);

  // 총 유효 시간 계산된 값을 캐싱
  const idleTime = useMemo(() => {
    if (workData && workData.length > 1) {
      return formatSecondsToHours(Number(workData[1].idleHour));
    }
    return 0;
  }, [workData]);

  // 이전 달 총 유효 시간 계산된 값을 캐싱
  const beforeIdleTime = useMemo(() => {
    if (workData && workData.length > 1) {
      return formatSecondsToHours(
        Number(workData[0].workingHour) + Number(workData[0].travelHour),
      );
    }
    return 0;
  }, [workData]);

  // 차트 옵션 수정 - 아래 여백 줄이기 및 언어에 따른 월 표시 변경
  const chartOptions = {
    ...WorkTimeRankingOption,
    grid: {
      ...WorkTimeRankingOption.grid,
      bottom: '10%', // 30%에서 10%로 줄임
    },
    yAxis: {
      ...WorkTimeRankingOption.yAxis,
      data: getMonthLabels,
    },
    series: [
      {
        ...WorkTimeRankingOption.series[0],
        data: [
          {
            value: idleTime,
            itemStyle: {
              borderRadius: [0, 0, 0, 0],
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: '#4d4d4d',
                },
                {
                  offset: 1,
                  color: '#b3b3b3',
                },
              ]),
            },
          },
          {
            value: beforeIdleTime,
            itemStyle: {
              borderRadius: [0, 0, 0, 0],
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: '#4d4d4d',
                },
                {
                  offset: 1,
                  color: '#b3b3b3',
                },
              ]),
            },
          },
        ],
      },
      {
        ...WorkTimeRankingOption.series[1],
        data: [
          {
            value: travelWorkTime,
            itemStyle: {
              borderRadius: [0, 10, 10, 0],
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: '#002554',
                },
                {
                  offset: 1,
                  color: '#5de3a4',
                },
              ]),
            },
          },
          {
            value: beforeTravelWorkTime,
            itemStyle: {
              borderRadius: [0, 10, 10, 0],
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: '#002554',
                },
                {
                  offset: 1,
                  color: '#5de3a4',
                },
              ]),
            },
          },
        ],
      },
    ],
  };

  // 언어 변경 감지
  useEffect(() => {
    const handleLanguageChange = () => {
      setCurrentLang(i18n.language);
    };

    i18n.on('languageChanged', handleLanguageChange);

    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, []);

  return (
    <StatisticsContainer className="h-full flex flex-col">
      {' '}
      {/* h-full로 변경하고 flex-col 추가 */}
      <div className={'flex justify-between items-center w-full mb-2'}>
        <div className="w-full text-lg font-semibold">
          {t('MonthlyWorkHours')}
        </div>
        <div className={'flex gap-3 items-center'}>
          <Button
            variant={'bt_primary'}
            label={'WorkingTraveling'}
            className={
              'w-max text-[#3fa68a]/70 bg-[#3fa68a]/10 border-[#3fa68a]/70 text-xs'
            }
          />
          <Button
            variant={'bt_primary'}
            label={'Idling'}
            className={
              'w-20 text-[#646363]/70 bg-[#646363]/10 border-[#646363]/70'
            }
          />
        </div>
      </div>
      <div className="flex-grow" style={{ minHeight: '200px' }}>
        {' '}
        {/* 차트 컨테이너를 flex-grow로 변경 */}
        <ECharts
          option={chartOptions}
          style={{ height: '100%', width: '100%' }}
        ></ECharts>
      </div>
    </StatisticsContainer>
  );
};

export default WorkTimeRanking;
