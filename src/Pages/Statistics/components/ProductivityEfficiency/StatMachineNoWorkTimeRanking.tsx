import { useTranslation } from 'react-i18next';
import arrow_drop_down from '@/assets/images/arrow/arrow_drop_down.png';
import arrow_drop_up from '@/assets/images/arrow/arrow_drop_up.png';
import { v4 } from 'uuid';
import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';
import arrow_eq from '@/assets/images/arrow/arrow_eq.svg';
import { StatisticsType } from '@/types/StatisticsType';

const StatMachineNoWorkTimeRanking = ({
  eq,
  title,
}: StatisticsType.StatisticsImpactProps) => {
  const { t } = useTranslation();

  const Title = title || t('WorkingRankingByMachine');

  const dummyData = [
    { name: 'HXDEMO', hitNumber: 150, hogi: '0919', increase: 1 },
    { name: 'HX380LS', hitNumber: 140, hogi: '0011', increase: 0 },
    { name: 'HL955T3', hitNumber: 130, hogi: '0009', increase: -1 },
    { name: 'R505LVSS', hitNumber: 120, hogi: '0024', increase: 1 },
    { name: 'HXDEMO23', hitNumber: 110, hogi: '0012', increase: -1 },
  ];

  const dataToRender = eq && eq.length > 0 ? eq.slice(0, 5) : dummyData;

  return (
    <StatisticsContainer className="flex flex-col h-full">
      <div className="text-lg font-semibold leading-[27px] mb-[30px]">
        {Title}
      </div>
      <div className={'space-y-2 overflow-y-auto flex-1'}>
        {dataToRender.map((item, index) => (
          <Row
            key={v4()}
            name={item.name ?? ''}
            hitNumber={item.hitNumber ?? 0}
            hogi={item.hogi}
            increase={item.increase}
            rowNum={index + 1}
          />
        ))}
      </div>
    </StatisticsContainer>
  );
};

const Row = ({
  rowNum,
  name,
  hitNumber,
  hogi,
  increase,
}: StatisticsType.RowStatProps) => {
  const { t } = useTranslation();

  return (
    <div>
      <div className="w-full mb-2 justify-between items-center inline-flex px-9">
        <div className="w-full h-6 justify-start items-center gap-2 flex overflow-hidden">
          <div className=" text-black text-base font-bold">{rowNum}.</div>
          <div className="text-base font-medium">{name}</div>
          <div className="text-base font-medium">{hogi}</div>
          <div className="text-gray-9 body3-n [&_em]:text-gray-9 [&_em]:ml-1">
            {hitNumber.toLocaleString()}
            <em>{t('Hours')}</em>
          </div>
          <div className={'flex-1 flex justify-end'}>
            {increase === 1 ? (
              <img src={arrow_drop_up} className="w-6 h-6 relative" />
            ) : increase === 0 ? (
              <img src={arrow_eq} className="w-6 h-6 relative" />
            ) : (
              <img src={arrow_drop_down} className="w-6 h-6 relative" />
            )}
          </div>
        </div>
      </div>
      <hr />
    </div>
  );
};

export default StatMachineNoWorkTimeRanking;
