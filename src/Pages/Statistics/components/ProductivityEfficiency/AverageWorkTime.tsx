import useDimension from '@/hooks/useDimension.tsx';
import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';
import { StatisticsType } from '@/types/StatisticsType';
import { Gauge, gaugeClasses } from '@mui/x-charts';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

const AverageWorkTime = ({
  title /*, data */,
}: StatisticsType.IndexChartProps) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';

  const { width } = useDimension();
  const [isVerticalLayout, setIsVerticalLayout] = useState(false);

  const GradientComponent = () => {
    return (
      <svg width="0" height="0" style={{ position: 'absolute' }}>
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#EB642B" stopOpacity="1" />
            <stop offset="68.5%" stopColor="#FFBB3D" stopOpacity="1" />
          </linearGradient>
        </defs>
      </svg>
    );
  };

  // 화면 크기에 따라 레이아웃 조정
  useEffect(() => {
    const updateLayout = () => {
      if (width < 360) {
        // 매우 작은 모바일
        setIsVerticalLayout(true);
      } else {
        setIsVerticalLayout(false);
      }
    };

    updateLayout();
  }, [width]);

  return (
    <StatisticsContainer className={'h-full flex flex-col items-center gap-2'}>
      <div className="w-full text-lg font-semibold mb-2">{title}</div>
      <div
        className={'w-full relative flex-grow flex justify-center'}
        style={{ minHeight: '280px' }}
      >
        <div className={'w-full relative'}>
          <GradientComponent />
          <Gauge
            sx={() => ({
              width: '100%',
              height: '100%',
              [`& .${gaugeClasses.valueText}`]: {
                fontSize: 0,
                visibility: 'invisible',
              },
              [`& .${gaugeClasses.valueArc}`]: {
                fill: 'url(#gradient)',
                transition: 'd 0.3s',
              },
              [`& .${gaugeClasses.referenceArc}`]: {
                fill: '#ECECEC',
              },
            })}
            value={300}
            valueMax={500}
            valueMin={0}
            startAngle={-180}
            endAngle={180}
            innerRadius={'85%'}
            cornerRadius="50%"
          ></Gauge>

          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full flex flex-col items-center justify-center">
            <div
              className={`flex ${isVerticalLayout ? 'flex-col' : ''} justify-center items-center gap-2`}
            >
              <div className="flex items-center gap-[5px]">
                <div className="font-normal tracking-tight text-[64px]">8</div>
                <div className="font-normal tracking-tight text-[34px]">
                  {t('HoursH')}
                </div>
              </div>
              <div className="flex items-center gap-[5px]">
                <div className="font-normal tracking-tight text-[64px]">25</div>
                <div className="font-normal tracking-tight text-[34px]">
                  {t('MinuteM')}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        className={`w-full flex items-center justify-center mb-1 ${isEnglish ? 'flex-col gap-1' : 'gap-7'}`}
      >
        <div className={'flex gap-[6px]'}>
          <div className="text-[#7b7b7b] font-normal text-[11px] sm:text-[12px] md:text-[13px] lg:text-[15px]">
            {t('WorkStartTime')}
          </div>
          <div className="text-[#7b7b7b] font-bold text-[11px] sm:text-[12px] md:text-[13px] lg:text-[15px]">
            08:25
          </div>
        </div>
        <div className={'flex gap-[6px]'}>
          <div className="text-[#7b7b7b] font-normal text-[11px] sm:text-[12px] md:text-[13px] lg:text-[15px]">
            {t('QuittingTime')}
          </div>
          <div className="text-[#7b7b7b] font-bold text-[11px] sm:text-[12px] md:text-[13px] lg:text-[15px]">
            17:25
          </div>
        </div>
      </div>
    </StatisticsContainer>
  );
};

export default AverageWorkTime;
