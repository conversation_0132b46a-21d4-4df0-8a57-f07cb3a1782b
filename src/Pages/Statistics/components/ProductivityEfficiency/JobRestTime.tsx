import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';
import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';
import { formatSecondsToHours } from '@/Common/function/date.ts';

type JobRestTimeProps = {
  workData?: {
    workingHour: number | string;
    travelHour: number | string;
    idleHour: number | string;
  }[];
};

const JobRestTime = ({ workData = [] }: JobRestTimeProps) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';

  // 총 주행 시간+ 작업 시간 사용하여 계산된 값을 캐싱
  const travelWorkTime = useMemo(() => {
    if (workData && workData.length > 1) {
      return formatSecondsToHours(
        Number(workData[1].workingHour) + Number(workData[1].travelHour),
      );
    }
    return 0;
  }, [workData]);

  // 이전달 총 주행 시간+ 작업 시간 사용하여 계산된 값을 캐싱
  const beforeTravelWorkTime = useMemo(() => {
    if (workData && workData.length > 1) {
      return formatSecondsToHours(
        Number(workData[0].workingHour) + Number(workData[0].travelHour),
      );
    }
    return 0;
  }, [workData]);

  // 총 유효 시간 계산된 값을 캐싱
  const idleTime = useMemo(() => {
    if (workData && workData.length > 1) {
      return formatSecondsToHours(Number(workData[1].idleHour));
    }
    return 0;
  }, [workData]);

  // 이전 달 총 유효 시간 계산된 값을 캐싱
  const beforeIdleTime = useMemo(() => {
    if (workData && workData.length > 1) {
      return formatSecondsToHours(
        Number(workData[0].workingHour) + Number(workData[0].travelHour),
      );
    }
    return 0;
  }, [workData]);

  // 주행 시간 증감 계산된 값을 캐싱
  const previousWorkTime: { increase: number; time: number } = useMemo(() => {
    if (workData && workData.length > 1) {
      if (Number(travelWorkTime) > Number(beforeTravelWorkTime)) {
        return {
          increase: 1,
          time: Number(travelWorkTime) - Number(beforeTravelWorkTime),
        };
      } else {
        return {
          increase: -1,
          time: Number(beforeTravelWorkTime) - Number(travelWorkTime),
        };
      }
    }
    return { increase: 0, time: 0 };
  }, [workData, travelWorkTime, beforeTravelWorkTime]);

  // 유효 시간 증감 계산된 값을 캐싱
  const previousIdleTime: { increase: number; time: number } = useMemo(() => {
    if (workData && workData.length > 1) {
      if (Number(idleTime) > Number(beforeIdleTime)) {
        return {
          increase: 1,
          time: Number(idleTime) - Number(beforeIdleTime),
        };
      } else {
        return {
          increase: -1,
          time: Number(beforeIdleTime) - Number(idleTime),
        };
      }
    }
    return { increase: 0, time: 0 };
  }, [workData, idleTime, beforeIdleTime]);

  return (
    <StatisticsContainer className={'h-full flex flex-col justify-center py-2'}>
      <div className="flex flex-col md:flex-row gap-2 md:gap-[20px] items-center h-full">
        <div className="w-full text-lg font-semibold space-y-1">
          <div className="mb-[12.5px]">{t('TotalWorkingTraveling')}</div>
          <div
            className={`w-full p-1 flex items-end justify-between ${
              String(travelWorkTime).length > 4 ? 'flex-col gap-1 ' : 'gap-0'
            }`}
          >
            <div className="flex items-center gap-1">
              <div className="font-light text-[48px]">{travelWorkTime}</div>
              <div className="font-light text-2xl">{t('HoursH')}</div>
            </div>
            {isEnglish ? (
              <>
                <div className="flex items-center">
                  <div className="mr-1">
                    <span
                      className={`body3-b ${
                        previousWorkTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousWorkTime.time}
                      {t('HoursH')}
                    </span>
                    <span
                      className={`body3-b ${
                        previousWorkTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousWorkTime.increase ? t('Up') : t('Down')}
                    </span>
                    <span
                      className={`body3-b ${
                        previousWorkTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousWorkTime.increase ? '↑' : '↓'}
                    </span>
                  </div>
                  <div className="align-middle body3-n">
                    {t('FromYesterday')}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="flex items-center">
                  <div className="align-middle body3-n">
                    {t('FromYesterday')}
                  </div>
                  <div className="text-right space-x-1">
                    <span
                      className={`body3-b ${
                        previousWorkTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousWorkTime.time}
                      {t('HoursH')}
                    </span>
                    <span
                      className={`body3-b ${
                        previousWorkTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousWorkTime.increase ? t('Up') : t('Down')}
                    </span>
                    <span
                      className={`body3-b ${
                        previousWorkTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousWorkTime.increase ? '↑' : '↓'}
                    </span>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="hidden md:block w-px h-[60px] opacity-80 bg-[#b3b3b3] rounded-[8px]" />

        <div className="w-full text-lg font-semibold space-y-1">
          <div className="mb-[12.5px]">{t('Idling')}</div>
          <div
            className={`w-full p-1 flex items-end justify-between ${
              String(idleTime).length > 4 ? 'flex-col gap-1 ' : 'gap-0'
            }`}
          >
            <div className="flex items-center gap-1">
              <div className="font-light text-[48px]">{idleTime}</div>
              <div className="font-light text-2xl">{t('HoursH')}</div>
            </div>
            {isEnglish ? (
              <>
                <div className="flex items-center">
                  <div className="mr-1 text-right">
                    <span
                      className={`body3-b ${
                        previousIdleTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousIdleTime.time}
                      {t('HoursH')}
                    </span>
                    <span
                      className={`body3-b ${
                        previousIdleTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousIdleTime.increase ? t('Up') : t('Down')}
                    </span>
                    <span
                      className={`body3-b ${
                        previousIdleTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousIdleTime.increase ? '↑' : '↓'}
                    </span>
                  </div>
                  <div className="align-middle body3-n">
                    {t('FromYesterday')}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="flex items-center">
                  <div className="align-middle body3-n">
                    {t('FromYesterday')}
                  </div>
                  <div className="text-right space-x-1">
                    <span
                      className={`body3-b ${
                        previousIdleTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousIdleTime.time}
                      {t('HoursH')}
                    </span>
                    <span
                      className={`body3-b ${
                        previousIdleTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousIdleTime.increase ? t('Up') : t('Down')}
                    </span>
                    <span
                      className={`body3-b ${
                        previousIdleTime.increase
                          ? 'text-point-3'
                          : 'text-point-2'
                      }`}
                    >
                      {previousIdleTime.increase ? '↑' : '↓'}
                    </span>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </StatisticsContainer>
  );
};

export default JobRestTime;
