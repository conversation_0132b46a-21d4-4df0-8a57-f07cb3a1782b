import { useTranslation } from 'react-i18next';
import arrow_drop_down from '@/assets/images/arrow/arrow_drop_down.png';
import arrow_drop_up from '@/assets/images/arrow/arrow_drop_up.png';
import { v4 } from 'uuid';
import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';
import useStatisticsPopup from '@/Pages/Statistics/components/popup/useStatisticsPopup.tsx';
import { StatisticsType } from '@/types/StatisticsType';

const WorkerRanking = ({ eq }: StatisticsType.ImpactProps) => {
  const { t } = useTranslation();

  const { openWorkerEqStatisticsPopup } = useStatisticsPopup();

  eq = [
    {
      name: '<PERSON>',
      hitNumber: 219,
      upDown: true,
    },
    {
      name: '<PERSON>',
      hitNumber: 211,
      upDown: true,
    },
    {
      name: '<PERSON>',
      hitNumber: 200,
      upDown: true,
    },
    {
      name: '<PERSON>',
      hitNumber: 199,
      upDown: true,
    },
    {
      name: '<PERSON>',
      hitNumber: 199,
      upDown: true,
    },
    {
      name: '<PERSON>',
      hitNumber: 190,
      upDown: true,
    },
    {
      name: '<PERSON>',
      hitNumber: 180,
      upDown: false,
    },
    {
      name: 'Emma',
      hitNumber: 120,
      upDown: false,
    },
    {
      name: 'Andrew',
      hitNumber: 105,
      upDown: true,
    },
  ];

  return (
    <StatisticsContainer>
      <div className={'flex justify-between items-center'}>
        <div className="mb-[3px] text-lg font-semibold leading-[27px]">
          {t('WorkingRankingByOperator')}
        </div>
        <div
          onClick={openWorkerEqStatisticsPopup}
          className="text-[#646363] text-base font-normal underline leading-tight cursor-pointer"
        >
          {t('ViewDetail')}
        </div>
      </div>
      <div className={'space-y-3 overflow-y-auto h-[calc(100%-60px)] mt-5'}>
        {eq.slice(0, 5).map((e: StatisticsType.RowProps, index) => (
          <Row key={v4()} {...e} rowNum={index + 1} />
        ))}
      </div>
    </StatisticsContainer>
  );
};

const Row = ({ rowNum, name, hitNumber, upDown }: StatisticsType.RowProps) => {
  const { t } = useTranslation();

  return (
    <div>
      <div className="w-full h-3.5 mb-3 justify-between items-center inline-flex px-9">
        <div className="w-full h-6 justify-start items-center gap-2 flex overflow-hidden">
          <div className=" text-black text-base font-bold">{rowNum}</div>
          <div className="text-base font-medium">{name}</div>
          <div className="text-gray-9 body3-n [&_em]:text-gray-9 [&_em]:ml-1">
            {hitNumber}
            <em>{t('Hours')}</em>
          </div>
          <div className={'flex-1 flex justify-end'}>
            {upDown ? (
              <img src={arrow_drop_up} className="w-6 h-6 relative" />
            ) : (
              <img src={arrow_drop_down} className="w-6 h-6 relative" />
            )}
          </div>
        </div>
      </div>
      <hr />
    </div>
  );
};

export default WorkerRanking;
