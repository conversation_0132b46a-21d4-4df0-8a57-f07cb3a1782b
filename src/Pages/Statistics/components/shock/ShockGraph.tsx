import React from 'react';
import ECharts from 'echarts-for-react';
import { ShockOption } from '@/Common/constants/GraphOptions.ts';
import { StatisticsType } from '@/types/StatisticsType';

const ShockGraph: React.FC<StatisticsType.DrivingEfficiencyProps> = ({
  data,
  title,
}) => {
  // X축 데이터를 명확하게 숫자 형식으로 설정
  ShockOption.xAxis.data = data.map((e) => e.day);
  ShockOption.series[0].data = data.map((e) => e.value);

  // X축 레이블을 영어로 설정
  ShockOption.xAxis.axisLabel = {
    formatter: 'Day {value}',
  };

  return (
    <div className="flex flex-col w-full h-full relative bg-white rounded-[8px] shadow-[0px_4px_12px_0px_rgba(0,0,0,0.08)]">
      {/* 헤더 */}
      <div className="px-[30px] pt-[30px] pb-2 text-lg font-semibold leading-[27px] flex justify-between items-center">
        {title}
      </div>
      <div className={'w-full h-full'}>
        <ECharts option={ShockOption} />
      </div>
    </div>
  );
};

export default ShockGraph;
