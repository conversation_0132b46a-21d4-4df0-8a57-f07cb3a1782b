import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { Button } from '@/Common/Components/common/Button';
import MonthSelector from '@/Common/Components/datePicker/MonthSelector';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import YearSelector from '@/Common/Components/datePicker/YearSelector';
import Radio, { RadioOption } from '@/Common/Components/common/Radio';
import useDimension from '@/hooks/useDimension.tsx';
import NumberOfCollisions from '@/Pages/Dashboard/components/NumberOfCollisions';
import EqWorkTimeRanking from '@/Pages/Statistics/components/ProductivityEfficiency/EqWorkTimeRanking.tsx';
import ShockGraph from '@/Pages/Statistics/components/shock/ShockGraph.tsx';
import { Tabs } from '@radix-ui/themes';
import { Responsive as ResponsiveGridLayout } from 'react-grid-layout';

import DaySelector from '@/Common/Components/datePicker/DaySelector';

const EqShock = () => {
  const { t } = useTranslation();

  const [selected, setSelected] = useState<string>('0');
  const options: RadioOption[] = [
    { value: '0', label: 'DateD' },
    { value: '1', label: 'MonthM' },
    { value: '2', label: 'Year' },
  ];

  const { width } = useDimension();
  // 각 화면 크기별 레이아웃 정의
  // lg: 데스크탑(8열), md: 태블릿(4열), sm: 작은 태블릿(2열), xs: 모바일(1열)
  const layouts = {
    lg: [
      { i: 'a', x: 0, y: 0, w: 3, h: 21, static: false },
      { i: 'b', x: 3, y: 0, w: 3, h: 21, static: false },
      { i: 'c', x: 6, y: 0, w: 2, h: 21, static: false },
      { i: 'd', x: 0, y: 1, w: 8, h: 25, static: false },
    ],
    md: [
      { i: 'a', x: 0, y: 0, w: 2, h: 21, static: false },
      { i: 'b', x: 2, y: 0, w: 2, h: 21, static: false },
      { i: 'c', x: 0, y: 1, w: 2, h: 21, static: false },
      { i: 'd', x: 0, y: 2, w: 4, h: 25, static: false },
    ],
    sm: [
      { i: 'a', x: 0, y: 0, w: 1, h: 21, static: false },
      { i: 'b', x: 1, y: 0, w: 1, h: 21, static: false },
      { i: 'c', x: 0, y: 1, w: 2, h: 21, static: false },
      { i: 'd', x: 0, y: 2, w: 2, h: 25, static: false },
    ],
    xs: [
      { i: 'a', x: 0, y: 0, w: 1, h: 21, static: false },
      { i: 'b', x: 0, y: 1, w: 1, h: 21, static: false },
      { i: 'c', x: 0, y: 2, w: 1, h: 21, static: false },
      { i: 'd', x: 0, y: 3, w: 1, h: 25, static: false },
    ],
  };

  // 브레이크포인트 설정
  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480 };
  const cols = { lg: 8, md: 4, sm: 2, xs: 1 };

  const gridWidth = width > 1024 ? width - 320 : width - 72;

  // 운행 효율 데이터
  const shockData = [
    { day: '12', value: 97 },
    { day: '13', value: 71 },
    { day: '14', value: 83 },
    { day: '15', value: 88 },
    { day: '16', value: 61 },
    { day: '17', value: 64 },
    { day: '18', value: 33 },
  ];

  const mRankingRows = [
    {
      name: 'HX380LS',
      hitNumber: 0,
      time: '83Times',
      upDown: true,
    },
    {
      name: 'R505LVSS',
      hitNumber: 0,
      time: '80Times',
      upDown: true,
    },
    {
      name: 'HL955T3',
      hitNumber: 0,
      time: '72Times',
      upDown: true,
    },
    {
      name: 'HX220AL',
      hitNumber: 0,
      time: '80Times',
      upDown: true,
    },
    {
      name: 'HX220S',
      hitNumber: 0,
      time: '60Times',
      upDown: true,
    },
  ];

  const eRankingRows = [
    {
      name: 'HXDEMO',
      hitNumber: 0,
      number: '0819',
      where: 'US-01',
      time: '23.57%',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      hitNumber: 0,
      number: '0820',
      where: 'GJ-01',
      time: '21.06%',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      hitNumber: 0,
      number: '0828',
      where: 'GJ-02',
      time: '21.05%',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      hitNumber: 0,
      number: '0830',
      where: 'GJ-03',
      time: '20.94%',
      upDown: true,
    },
    {
      name: 'HXDEMO',
      hitNumber: 0,
      number: '0862',
      where: 'US-02',
      time: '19.44%',
      upDown: true,
    },
  ];

  return (
    <Tabs.Content value={'MachineCollisionAnalysis'}>
      <div className={'w-full h-full space-y-5 p-10'}>
        <SearchItemContainer className={'justify-between'}>
          <SearchItemContainer className={'justify-start gap-6'}>
            <SearchItemContainer>
              <SearchLabel>{t('Fleet')}</SearchLabel>
              <DropDown
                options={[]}
                placeholder={t('All')}
                onChange={() => {}}
              />
            </SearchItemContainer>
            <SearchItemContainer className={'justify-start gap-6'}>
              <Radio
                className={'flex justify-start gap-6'}
                options={options}
                value={selected}
                onValueChange={setSelected}
              />
              {selected === '0' && <DaySelector />}
              {selected === '1' && <MonthSelector />}
              {selected === '2' && <YearSelector />}
              <Button
                variant={'bt_primary'}
                label={'aaaSearch'}
                className="ml-[-4px]"
              />
            </SearchItemContainer>
          </SearchItemContainer>
          <SearchItemContainer>
            <Button variant={'bt_primary'} label={'Download'} />
          </SearchItemContainer>
        </SearchItemContainer>
        <ResponsiveGridLayout
          className="layout"
          layouts={layouts}
          breakpoints={breakpoints}
          cols={cols}
          isDroppable={false}
          isDraggable={false}
          rowHeight={1}
          width={gridWidth}
          margin={[16, 16]}
          containerPadding={[0, 16]}
        >
          <div className={'p-2'} key={'a'}>
            <EqWorkTimeRanking
              eq={mRankingRows}
              title={t('CollisionRankingByModel')}
            />
          </div>

          <div className={'p-2'} key={'b'}>
            <EqWorkTimeRanking
              eq={eRankingRows}
              title={t('CollisionRankingByEquipment')}
            />
          </div>

          <div className={'p-2'} key={'c'}>
            <NumberOfCollisions data={undefined} />
          </div>

          <div className={'p-2'} key={'d'}>
            <ShockGraph
              title={t('CollisionOccurrenceAnalysis')}
              data={shockData}
            />
          </div>
        </ResponsiveGridLayout>
      </div>
    </Tabs.Content>
  );
};

export default EqShock;
