import { Tabs } from '@radix-ui/themes';
import { useTranslation } from 'react-i18next';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import { Button } from '@/Common/Components/common/Button';
import Input from '@/Common/Components/common/Input';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import { ColumnDef } from '@tanstack/react-table';
import CustomColumnHeader from '@/Common/Components/etc/CustomColumnHeader';
import CustomColumnDataCell from '@/Common/Components/etc/CustomColumnDataCell';
import CommonTable from '@/Common/Components/common/CommonTable';
import { StatisticsType } from '@/types/StatisticsType';

const PilotShockHistory = () => {
  const { t } = useTranslation();

  const columns: ColumnDef<StatisticsType.PilotShockHistoryProps>[] = [
    {
      accessorKey: 'date',
      header: () => <CustomColumnHeader>{t('DateO')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.date}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'pilot',
      header: () => <CustomColumnHeader>{t('Driver')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.pilot}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'frontRear',
      header: () => <CustomColumnHeader>{t('FrontRear')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.frontRear}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'side',
      header: () => <CustomColumnHeader>{t('Side')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.side}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'upDown',
      header: () => <CustomColumnHeader>{t('Vertical')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.upDown}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'address',
      header: () => <CustomColumnHeader>{t('AddressC')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.address}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'point',
      header: () => <CustomColumnHeader>{t('LocationC')}</CustomColumnHeader>,
      cell: () => <span className="blue-underline">{t('View')}</span>,
    },
  ];

  const data = [
    {
      date: '2014-12-02',
      pilot: 'Jakub',
      frontRear: '2.5G',
      side: '-',
      upDown: '2.5G',
      address: 'Lesser Poland Voivodeship, Poland',
      point: 'View',
    },
    {
      date: '2014-12-03',
      pilot: 'Alexei',
      frontRear: '2.5G',
      side: '-',
      upDown: '2.5G',
      address: 'Khanty-Mansiysk Autonomous Okrug, Russia',
      point: 'View',
    },
    {
      date: '2014-12-04',
      pilot: 'Carlos',
      frontRear: '2.5G',
      side: '-',
      upDown: '-',
      address: 'Lisbon, Portugal',
      point: 'View',
    },
    {
      date: '2014-12-05',
      pilot: 'Somchai',
      frontRear: '-',
      side: '2.5G',
      upDown: '2.5G',
      address: 'Sakon Nakhon, Thailand',
      point: 'View',
    },
    {
      date: '2014-12-05',
      pilot: 'Budi',
      frontRear: '2.5G',
      side: '-',
      upDown: '2.5G',
      address: 'East Kalimantan, Indonesia',
      point: 'View',
    },
  ];

  return (
    <Tabs.Content value={'OperatorCollisionHistory'}>
      <div className={'w-full h-full space-y-10 p-10'}>
        <SearchItemContainer className={'justify-between'}>
          <SearchItemContainer className={'justify-start gap-6'}>
            <SearchItemContainer>
              <SearchLabel>{t('Driver')}</SearchLabel>
              <Input placeholder={t('Driver')} />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Date')}</SearchLabel>
              <FromToSelector />
            </SearchItemContainer>
            <Button
              variant={'bt_primary'}
              label={'Search'}
              className="ml-[-4px]"
            />
          </SearchItemContainer>
          <SearchItemContainer>
            <Button variant={'bt_primary'} label={'Print'} />
          </SearchItemContainer>
        </SearchItemContainer>
        <CommonTable<StatisticsType.PilotShockHistoryProps>
          data={data}
          columns={columns}
          isPagination={true}
        />
      </div>
    </Tabs.Content>
  );
};

export default PilotShockHistory;
