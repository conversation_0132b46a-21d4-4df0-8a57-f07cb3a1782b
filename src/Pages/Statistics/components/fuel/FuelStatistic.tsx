import { useTranslation } from 'react-i18next';
import ECharts from 'echarts-for-react';
import { ColumnDef } from '@tanstack/react-table';
import CustomColumnHeader from '@/Common/Components/etc/CustomColumnHeader';
import CustomColumnDataCell from '@/Common/Components/etc/CustomColumnDataCell';
import { Tabs } from '@radix-ui/themes';
import FuelAndBatterySearchContainer from '@/Pages/Statistics/components/FuelAndBatterySearchContainer.tsx';
import CommonTable from '@/Common/Components/common/CommonTable';
import { useMemo, useState } from 'react';
import { formatDate, getCurrentYear } from '@/Common/function/date.ts';
import { StatisticsType } from '@/types/StatisticsType';

const FuelStatistic = ({ title }: StatisticsType.MenuTabPros) => {
  const { t } = useTranslation();

  const columns: ColumnDef<StatisticsType.FuelTable>[] = [
    {
      accessorKey: 'month',
      header: () => <CustomColumnHeader>{t('Month')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.month}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'mileage',
      header: () => <CustomColumnHeader>{t('Mileage')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.mileage}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'engineActiveTime',
      header: () => <CustomColumnHeader>{t('EGRunHourH')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>
          {row.original.engineActiveTime}
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'workTime',
      header: () => (
        <CustomColumnHeader>{t('WorkingHourH')}</CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.workTime}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'driveTime',
      header: () => (
        <CustomColumnHeader>{t('TravelingHourH')}</CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.driveTime}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'idleTime',
      header: () => <CustomColumnHeader>{t('IdlingHourH')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.idleTime}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'fuel',
      header: () => <CustomColumnHeader>{t('FuelRateLH')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.fuel}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'fuelConsumption',
      header: () => <CustomColumnHeader>{t('FuelUsedL')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>
          {row.original.fuelConsumption}
        </CustomColumnDataCell>
      ),
    },
  ];

  // 검색 필터 상태
  const [filtersRequest, setFilterRequest] = useState({
    fleetSeqNo: '',
    region: 'ALL',
    country: 'ALL',
    sDealer: '',
    model: '',
    hogi: '',
    year: getCurrentYear(),
    sDate: formatDate(new Date()),
    eDate: formatDate(new Date()),
    periodType: '1',
  });

  // 더미 데이터
  const dummyMonthlyData: StatisticsType.FuelTable[] = [
    {
      month: '01',
      mileage: '100',
      engineActiveTime: '90.2',
      workTime: '50.1',
      driveTime: '40.1',
      idleTime: '30.0',
      fuel: '5.20',
      fuelConsumption: '200.5',
      fuelRate: '5.20',
      fuelRate2: '5.00',
    },
    {
      month: '02',
      mileage: '110',
      engineActiveTime: '95.5',
      workTime: '55.5',
      driveTime: '43.0',
      idleTime: '33.0',
      fuel: '5.60',
      fuelConsumption: '210.0',
      fuelRate: '5.60',
      fuelRate2: '5.10',
    },
    {
      month: '03',
      mileage: '120',
      engineActiveTime: '102.3',
      workTime: '60.2',
      driveTime: '47.1',
      idleTime: '36.0',
      fuel: '5.80',
      fuelConsumption: '220.3',
      fuelRate: '5.80',
      fuelRate2: '5.30',
    },
    // ...필요에 따라 더 추가
  ];

  const dummyPeriodicData: StatisticsType.FuelTable[] = [
    {
      month: '2024-07-01',
      mileage: '100',
      engineActiveTime: '90.2',
      workTime: '50.1',
      driveTime: '40.1',
      idleTime: '30.0',
      fuel: '5.20',
      fuelConsumption: '200.5',
      fuelRate: '5.20',
      fuelRate2: '5.00',
    },
    {
      month: '2024-07-02',
      mileage: '101',
      engineActiveTime: '91.0',
      workTime: '50.5',
      driveTime: '40.5',
      idleTime: '30.5',
      fuel: '5.30',
      fuelConsumption: '201.0',
      fuelRate: '5.30',
      fuelRate2: '5.10',
    },
    // ...필요에 따라 더 추가
  ];

  // 검색 변경 시 더미데이터를 periodType에 따라 세팅
  const onFilterChange = (
    filters: StatisticsType.FuelAndBatterySearchFilters,
  ) => {
    setFilterRequest(filters);
  };

  // 테이블 데이터 결정
  const tablData = useMemo(() => {
    if (filtersRequest.periodType === '1') {
      return dummyMonthlyData;
    } else {
      return dummyPeriodicData;
    }
  }, [filtersRequest.periodType]);

  const chartOptions = useMemo(() => {
    if (Array.isArray(tablData)) {
      const dates = tablData.map((item) => item.month);

      const fuelRates = tablData.map((item) => item.fuelRate);
      const fuelRates2 = tablData.map((item) => item.fuelRate2);

      return {
        title: {},
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          right: '3%',
          data: ['Fuel', 'AVG'],
        },
        grid: {
          left: '0%',
          right: '4%',
          bottom: '0%',
          top: '10%',
          containLabel: true,
        },
        toolbox: {
          feature: {},
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisTick: { show: false },
        },
        yAxis: {
          type: 'value',
          splitLine: { lineStyle: { type: 'dashed' } },
        },
        series: [
          {
            name: 'Fuel',
            type: 'line',
            data: fuelRates,
            color: '#FF5900',
          },
          {
            name: 'AVG',
            type: 'line',
            data: fuelRates2,
            color: '#58B4B6',
          },
        ],
      };
    }
  }, [tablData]);

  return (
    <Tabs.Content value={'FuelEfficiency'}>
      <div className={'w-full h-full space-y-10 p-10'}>
        <FuelAndBatterySearchContainer
          key="fuel-statistic-search"
          onValueChange={onFilterChange}
        />
        <div className={'space-y-[10px]'}>
          <div className={'bg-white h-[368px] pt-10'}>
            <ECharts option={chartOptions} style={{ height: '308px' }} />
          </div>
        </div>
        <div className={'max-h-[710px] overflow-y-auto'}>
          <CommonTable<StatisticsType.FuelTable>
            data={tablData}
            columns={columns}
            isPagination={false}
          />
        </div>
      </div>
    </Tabs.Content>
  );
};

export default FuelStatistic;
