import { useTranslation } from 'react-i18next';
import { Button } from '@/Common/Components/common/Button';
import ECharts from 'echarts-for-react';
import { Tabs } from '@radix-ui/themes';
import { ColumnDef } from '@tanstack/react-table';
import CustomColumnHeader from '@/Common/Components/etc/CustomColumnHeader';
import CustomColumnDataCell from '@/Common/Components/etc/CustomColumnDataCell';
import FuelAndBatterySearchContainer from '@/Pages/Statistics/components/FuelAndBatterySearchContainer.tsx';
import { useMemo, useState } from 'react';
import { formatDate, getCurrentYear } from '@/Common/function/date.ts';
import CommonTable from '@/Common/Components/common/CommonTable';
import * as echarts from 'echarts';
import { StatisticsType } from '@/types/StatisticsType';

const FuelConsumption = ({ title }: { title: string }) => {
  const { t } = useTranslation();

  const columns: ColumnDef<StatisticsType.FuelConsumptionTable>[] = [
    {
      accessorKey: 'date',
      header: () => <CustomColumnHeader>{t('Date')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.date}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'totalEngineActiveTime',
      header: () => <CustomColumnHeader>{t('EngineRunTT')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>
          {row.original.totalEngineActiveTime}
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'totalWorkTime',
      header: () => (
        <CustomColumnHeader>{t('ActualWorkingT')}</CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell>
          {row.original.totalWorkTime}
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'totalDriveTime',
      header: () => <CustomColumnHeader>{t('TravelingT')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>
          {row.original.totalDriveTime}
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'totalFuelConsumption',
      header: () => <CustomColumnHeader>{t('FuelLevelT')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>
          {row.original.totalFuelConsumption}
        </CustomColumnDataCell>
      ),
    },
  ];

  // 검색 필터 상태
  const [filtersRequest, setFilterRequest] = useState({
    fleetSeqNo: '',
    region: 'ALL',
    country: 'ALL',
    sDealer: '',
    model: '',
    hogi: '',
    year: getCurrentYear(),
    sDate: formatDate(new Date()),
    eDate: formatDate(new Date()),
    periodType: '1',
  });

  // 더미 데이터
  const dummyMonthlyData: StatisticsType.FuelConsumptionTable[] = [
    {
      date: '01',
      totalEngineActiveTime: '12:30',
      totalWorkTime: '08:20',
      totalDriveTime: '02:45',
      totalFuelConsumption: '153',
      workingHour: '5000',
      travelHour: '2000',
      fuelRate: '10.2',
      fuelRate2: '5.1',
    },
    {
      date: '02',
      totalEngineActiveTime: '10:45',
      totalWorkTime: '07:10',
      totalDriveTime: '03:00',
      totalFuelConsumption: '145',
      workingHour: '4500',
      travelHour: '2200',
      fuelRate: '9.8',
      fuelRate2: '4.9',
    },
    {
      date: '03',
      totalEngineActiveTime: '11:55',
      totalWorkTime: '08:00',
      totalDriveTime: '02:20',
      totalFuelConsumption: '149',
      workingHour: '4700',
      travelHour: '2100',
      fuelRate: '10.0',
      fuelRate2: '5.0',
    },
  ];

  const dummyPeriodicData: StatisticsType.FuelConsumptionTable[] = [
    {
      date: '2024-07-01',
      totalEngineActiveTime: '12:20',
      totalWorkTime: '08:15',
      totalDriveTime: '02:55',
      totalFuelConsumption: '151',
      workingHour: '4950',
      travelHour: '1980',
      fuelRate: '10.1',
      fuelRate2: '5.0',
    },
    {
      date: '2024-07-02',
      totalEngineActiveTime: '10:55',
      totalWorkTime: '07:00',
      totalDriveTime: '03:05',
      totalFuelConsumption: '140',
      workingHour: '4300',
      travelHour: '2300',
      fuelRate: '9.7',
      fuelRate2: '4.8',
    },
  ];

  // 검색 변경 시 더미데이터를 periodType에 따라 세팅
  const onFilterChange = (
    filters: StatisticsType.FuelAndBatterySearchFilters,
  ) => {
    setFilterRequest(filters);
  };

  // 테이블 데이터 결정
  const tablData = useMemo(() => {
    if (filtersRequest.periodType === '1') {
      return dummyMonthlyData;
    } else {
      return dummyPeriodicData;
    }
  }, [filtersRequest.periodType]);

  const chartOptions = useMemo(() => {
    if (Array.isArray(tablData)) {
      const dates = tablData.map((item) => item.date);
      const fuelRates = tablData.map((item) => item.workingHour);
      const fuelRates2 = tablData.map((item) => item.travelHour);

      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: { show: false },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          top: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          axisTick: { show: false },
          splitLine: {
            show: false,
          },
          data: dates,
        },
        yAxis: {
          type: 'value',
          splitLine: { show: true, lineStyle: { type: 'dashed' } },
        },
        series: [
          {
            name: t('WorkingE'),
            type: 'bar',
            stack: 'total',
            label: {
              show: true,
            },
            barWidth: 20,
            emphasis: {
              focus: 'series',
            },
            data: fuelRates,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#4D4D4D',
              },
              {
                offset: 1,
                color: '#B3B3B3',
              },
            ]),
          },
          {
            name: t('TravelingE'),
            type: 'bar',
            stack: 'total',
            barWidth: 20,
            label: {
              show: true,
            },
            emphasis: {
              focus: 'series',
            },
            data: fuelRates2,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: '#010542',
              },
              {
                offset: 1,
                color: '#FF5900',
              },
            ]),
          },
        ],
      };
    }
  }, [tablData, t]);

  return (
    <Tabs.Content value={'FuelConsumption'}>
      <div className={'w-full h-full space-y-10 p-10'}>
        <FuelAndBatterySearchContainer
          key="fuel-consumption-search"
          onValueChange={onFilterChange}
        />
        <div className={'space-y-[10px]'}>
          <div className="h-[368px] bg-white p-6 space-y-6">
            <div className={'flex justify-between items-center w-full'}>
              <div className="text-lg font-semibold leading-[27px]">
                {/* 일별 가동 시간 */}
              </div>
              <div className={'flex gap-3 items-center'}>
                <Button
                  variant={'bt_primary'}
                  label={'FuelConsumption2'}
                  className={'w-auto text-b01/70 bg-b01/10 border-b01/70 '}
                />
                <Button
                  variant={'bt_primary'}
                  label={'EstimatedFuelConsumption'}
                  className={'text-gray-10/70 bg-text-80/10 border-text-80/70'}
                />
              </div>
            </div>
            <ECharts option={chartOptions} />
          </div>
        </div>
        <CommonTable<StatisticsType.FuelConsumptionTable>
          data={tablData}
          columns={columns}
          isPagination={false}
        />
        <div className="text-point-3 text-sm font-bold">
          {t(
            'CAUTIONTheFuelUsedDataReflectedAboveIsTheoreticalAndMayDifferFromTheActualUsedBasedOnTheEnvironmentOrHowTheMachineWasOperated',
          )}
        </div>
      </div>
    </Tabs.Content>
  );
};

export default FuelConsumption;
