import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';

const LithiumStatistics = ({
  title,
  subtitle,
  value = '300',
  // unit = '회',
}: {
  title?: string;
  subtitle?: string;
  value?: string;
  unit?: string;
}) => {
  return (
    <StatisticsContainer className={'space-y-1 pt-6 pb-4'}>
      <div className="text-lg font-semibold ">
        {title}
        <div className={'body2-s text-gray-7'}>{subtitle}</div>
      </div>
      <div className="justify-end items-center gap-3 w-full flex">
        <div className="justify-end items-center gap-1 flex">
          <div className="text-[56px] font-light leading-[67px]">{value}</div>
          <div className="heading1-l">{/* {unit} */}</div>
        </div>
      </div>
    </StatisticsContainer>
  );
};

export default LithiumStatistics;
