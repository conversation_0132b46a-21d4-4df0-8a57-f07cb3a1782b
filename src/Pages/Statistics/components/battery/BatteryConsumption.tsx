import { useTranslation } from 'react-i18next';
import { Tabs } from '@radix-ui/themes';
import ECharts from 'echarts-for-react';
import { BatteryConsumptionOption } from '@/Common/constants/GraphOptions.ts';
import { ColumnDef } from '@tanstack/react-table';
import CustomColumnHeader from '@/Common/Components/etc/CustomColumnHeader';
import CustomColumnDataCell from '@/Common/Components/etc/CustomColumnDataCell';
import { useTable } from '@/Common/Components/hooks/useTable.tsx';
import StatisticsTable from '@/Pages/Statistics/components/StatisticsTable.tsx';
import FuelAndBatterySearchContainer from '@/Pages/Statistics/components/FuelAndBatterySearchContainer.tsx';
import { StatisticsType } from '@/types/StatisticsType';

const BatteryConsumption = () => {
  const { t } = useTranslation();

  const columns: ColumnDef<StatisticsType.BatteryConsumptionTable>[] = [
    {
      accessorKey: 'date',
      header: () => <CustomColumnHeader>{t('Date')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>{row.original.date}</CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'totalEngineActiveTime',
      header: () => <CustomColumnHeader>{t('EngineRunTT')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>
          {row.original.totalEngineActiveTime}
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'totalWorkTime',
      header: () => (
        <CustomColumnHeader>{t('ActualWorkingT')}</CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell>
          {row.original.totalWorkTime}
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'totalDriveTime',
      header: () => <CustomColumnHeader>{t('TravelingT')}</CustomColumnHeader>,
      cell: ({ row }) => (
        <CustomColumnDataCell>
          {row.original.totalDriveTime}
        </CustomColumnDataCell>
      ),
    },
    {
      accessorKey: 'totalBatteryConsumption',
      header: () => (
        <CustomColumnHeader>{t('BatteryUsedB')}</CustomColumnHeader>
      ),
      cell: ({ row }) => (
        <CustomColumnDataCell>
          {row.original.totalBatteryConsumption}
        </CustomColumnDataCell>
      ),
    },
  ];

  const data: StatisticsType.BatteryConsumptionTable[] = [
    {
      date: '2024-01',
      totalEngineActiveTime: '1hr 43min',
      totalWorkTime: '1hr 12min',
      totalDriveTime: '27min',
      totalBatteryConsumption: '433%',
    },
    {
      date: '2024-02',
      totalEngineActiveTime: '2hr 53min',
      totalWorkTime: '1hr 21min',
      totalDriveTime: '29min',
      totalBatteryConsumption: '131%',
    },
    {
      date: '2024-03',
      totalEngineActiveTime: '1hr 34min',
      totalWorkTime: '1hr 22min',
      totalDriveTime: '30min',
      totalBatteryConsumption: '223%',
    },
    {
      date: '2024-04',
      totalEngineActiveTime: '1hr 10min',
      totalWorkTime: '1hr 12min',
      totalDriveTime: '24min',
      totalBatteryConsumption: '213%',
    },
    {
      date: '2024-05',
      totalEngineActiveTime: '1hr 23min',
      totalWorkTime: '1hr 14min',
      totalDriveTime: '37min',
      totalBatteryConsumption: '333%',
    },
  ];
  const { table } = useTable(data, columns);
  return (
    <Tabs.Content value={'BatteryConsumption'}>
      <div className={'w-full h-full space-y-10 p-10'}>
        <FuelAndBatterySearchContainer />
        <div className="h-[368px] bg-white pt-10">
          <ECharts
            option={BatteryConsumptionOption}
            style={{ height: '100%' }}
          />
        </div>
        <StatisticsTable table={table} columns={columns} />
        <div className="text-point-3 text-sm font-bold">
          {t(
            'CAUTIONTheFuelUsedDataReflectedAboveIsTheoreticalAndMayDifferFromTheActualUsedBasedOnTheEnvironmentOrHowTheMachineWasOperated',
          )}
        </div>
      </div>
    </Tabs.Content>
  );
};

export default BatteryConsumption;
