import StatisticsContainer from '@/Pages/Statistics/components/StatisticsContainer.tsx';

const CellValue = ({
  title = '셀 MAX 전압',
  value = '4',
  unit = 'V',
  titleRight = '셀 MAX 전압',
  valueRight = '4',
  unitRight = 'V',
}: {
  title?: string;
  value?: string;
  unit?: string;
  titleRight?: string;
  valueRight?: string;
  unitRight?: string;
}) => {
  return (
    <StatisticsContainer className={'flex gap-[30px] items-center'}>
      <div className="w-full text-lg font-semibold space-y-3">
        <div>{title}</div>
        <div className="h-[60px] p-2.5 justify-end items-start gap-2.5 flex">
          <div className="justify-start items-center gap-1 flex">
            <div className="text-right text-5xl font-light">{value}</div>
            <div className="text-right text-[28px] font-light">{unit}</div>
          </div>
        </div>
      </div>

      <div className="w-px h-[104px] opacity-80 bg-[#b3b3b3] rounded-[8px]" />

      <div className="w-full text-lg font-semibold space-y-3">
        <div>{titleRight}</div>
        <div className="h-[60px] p-2.5 justify-end items-start gap-2.5 flex">
          <div className="justify-start items-center gap-1 flex">
            <div className="text-right text-5xl font-light">{valueRight}</div>
            <div className="text-right text-[28px] font-light">{unitRight}</div>
          </div>
        </div>
      </div>
    </StatisticsContainer>
  );
};

export default CellValue;
