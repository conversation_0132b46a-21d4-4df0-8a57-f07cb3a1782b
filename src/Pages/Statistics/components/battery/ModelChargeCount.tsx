import ECharts from 'echarts-for-react';
import { ModelChargeCountOption } from '@/Common/constants/GraphOptions.ts';
import { Tabs } from '@radix-ui/themes';
import FuelAndBatterySearchContainer from '@/Pages/Statistics/components/FuelAndBatterySearchContainer.tsx';

const ModelChargeCount = () => {
  return (
    <Tabs.Content value={'ChargingCountByModel'}>
      <div className={'w-full h-full space-y-10 p-10'}>
        <FuelAndBatterySearchContainer />
        <div className={'h-[1100px] bg-white'}>
          <ECharts
            option={ModelChargeCountOption}
            style={{ height: '100%' }}
          ></ECharts>
        </div>
      </div>
    </Tabs.Content>
  );
};

export default ModelChargeCount;
