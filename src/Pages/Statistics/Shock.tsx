import { useTranslation } from 'react-i18next';
import { Tabs } from '@radix-ui/themes';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { useState } from 'react';
import EqShock from '@/Pages/Statistics/components/shock/EqShock.tsx';
import PilotShockHistory from '@/Pages/Statistics/components/shock/PilotShockHistory.tsx';

const Shock = () => {
  const { t } = useTranslation();

  const [value, setValue] = useState('MachineCollisionAnalysis');
  return (
    <CustomFrame name={t('CollisionAnalysis')}>
      <Tabs.Root value={value} onValueChange={setValue}>
        <Tabs.List className={'tab-design'}>
          <Tabs.Trigger value={'MachineCollisionAnalysis'}>
            <span>{t('MachineCollisionAnalysis')}</span>
          </Tabs.Trigger>
          <Tabs.Trigger value={'OperatorCollisionHistory'}>
            <span>{t('OperatorCollisionHistory')}</span>
          </Tabs.Trigger>
        </Tabs.List>
        <EqShock />
        <PilotShockHistory />
      </Tabs.Root>
    </CustomFrame>
  );
};

export default Shock;
