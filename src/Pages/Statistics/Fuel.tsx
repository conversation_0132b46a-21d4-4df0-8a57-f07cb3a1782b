import { useTranslation } from 'react-i18next';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Tabs } from '@radix-ui/themes';
import { useState } from 'react';
import FuelStatistic from '@/Pages/Statistics/components/fuel/FuelStatistic.tsx';
import FuelConsumption from '@/Pages/Statistics/components/fuel/FuelConsumption.tsx';

const Fuel = () => {
  const { t } = useTranslation();

  const [value, setValue] = useState('FuelEfficiency');
  return (
    <CustomFrame name={t('FuelConsumption2')}>
      <Tabs.Root value={value} onValueChange={setValue}>
        <Tabs.List className={'tab-design'}>
          <Tabs.Trigger value={'FuelEfficiency'}>
            <span>{t('FuelEfficiency')}</span>
          </Tabs.Trigger>
          <Tabs.Trigger value={'FuelConsumption'}>
            <span>{t('FuelConsumption')}</span>
          </Tabs.Trigger>
        </Tabs.List>
        <FuelStatistic title={value}></FuelStatistic>
        <FuelConsumption title={value}></FuelConsumption>
      </Tabs.Root>
    </CustomFrame>
  );
};

export default Fuel;
