import { useLayoutStore } from '@/store/layout.ts';
import { useEffect, useRef, useState } from 'react';
import HeatmapSearchFilter, {
  HeatmapSearchParams,
} from '@/Pages/Statistics/components/Map/HeatmapSearchFilter';
import VehicleViewListButton from '@/Common/Components/map/MonitoringViewListButton';
import HeatmapFilteredMapList from '@/Pages/Statistics/components/Map/HeatmapFilteredMapList';
import { MapRandomData } from '@/Common/constants/Maps.ts';
import ZoomController from '@/Common/Components/map/ZoomController';
import { GeneralMap, GeneralMapAdapter } from '@/logiMaps/react/general/Map';
import { GeneralHeatmap } from '@/logiMaps/react/general/Heatmap';
import { StatisticsType } from '@/types/StatisticsType';

/**
 * UI.7.1.7.2 통계 / 히트맵
 */
const Heatmap = () => {
  const { move: layoutMove } = useLayoutStore((state) => state);
  const [searchResult, setSearchResult] = useState<
    StatisticsType.HeatmapFilteredMapItem[]
  >([]);
  const [pathList, setPathList] = useState<{ lat: number; lng: number }[][]>(
    [],
  );

  const [mapAdapter, setMapAdapter] = useState<GeneralMapAdapter | null>(null);
  const maxZoomLevel = 18;
  const minZoomLevel = 4;
  const defaultLevel = 15;
  // const [zoomGauge, setZoomGauge] = useState(defaultLevel);

  const simplificationStep = 2;
  const [opacity] = useState(0.5);
  const [radius] = useState(50);
  const heatmapDataArray = useRef<
    { latlng: { lat: number; lng: number }; intensity: number }[]
  >([]);

  /** useEffect */

  useEffect(() => {
    //for demo...
    const heatmapEqData = MapRandomData.getHeatmapDatas(32).map((item) => ({
      ...item,
      checked: true,
    }));
    setSearchResult(heatmapEqData);
    handleRefreshClick(heatmapEqData);
    //...for demo
  }, []);

  // 맵 업데이트
  useEffect(() => {
    if (mapAdapter && pathList) {
      updateData();
    }
  }, [mapAdapter, pathList]);

  /** Event Listener */

  //검색 버튼을 클릭하면 정보 쿼리해서 우측 리스트에 업데이트
  const handleSearch = (params: HeatmapSearchParams) => {
    if (!params) {
      return;
    }
    //지금은 렌덤 정보
    setSearchResult(MapRandomData.getHeatmapDatas(32));
  };

  //우측 리스트에서 장비를 선택하고 갱신 버튼을 클릭하면 지도에 표시
  const handleRefreshClick = (
    items: StatisticsType.HeatmapFilteredMapItem[],
  ) => {
    setPathList(
      items
        .filter((item) => item.checked === true)
        .map((item) => item.item.path),
    );
  };

  // 지도 로드
  const handleMapInit = (generalMapAdapter: GeneralMapAdapter) => {
    setMapAdapter(generalMapAdapter);
  };

  // 지도 스케일 변경
  const handleMapZoomChanged = () => {
    // setZoomGauge(zoom);
  };

  /** Function */

  // 정보 업데이트
  const updateData = () => {
    // baseLevel 기준으로 합칠 수 있는 좌표는 미리 합치기
    const baseLevel = maxZoomLevel - simplificationStep;
    const magTick = 0.01;
    const baseHeatmapData: Map<
      string,
      { latlng: { lat: number; lng: number }; mag: number }
    > = new Map();

    pathList.forEach((path) => {
      path.forEach((latlng) => {
        const planeXY = mapAdapter?.world2plane(latlng, baseLevel);
        if (planeXY) {
          const key = `${Math.round(planeXY.x)}_${Math.round(planeXY.y)}`;
          const value = baseHeatmapData.get(key);
          if (value) {
            value.mag += magTick;
          } else {
            baseHeatmapData.set(key, {
              mag: magTick,
              latlng: { ...latlng },
            });
          }
        }
      });
    });

    const bounds: { lat: number; lng: number }[] = [];

    heatmapDataArray.current = [];
    baseHeatmapData.forEach((value) => {
      // 북미 영역 좌표만 필터
      if (
        value.latlng.lat >= 10.0 &&
        value.latlng.lat <= 82.0 &&
        value.latlng.lng >= -168.0 &&
        value.latlng.lng <= -40.0
      ) {
        bounds.push(value.latlng);
      }
      heatmapDataArray.current?.push({
        latlng: value.latlng,
        intensity: value.mag,
      });
    });

    fitBounds(bounds);
  };

  // 지도 화면 조정
  const fitBounds = (bounds: { lat: number; lng: number }[]) => {
    if (mapAdapter) {
      const { width, height } = mapAdapter.getScreenSize();
      const padding = {
        top: `${height * 0.1}px`,
        right: `${width * 0.1}px`,
        bottom: `${height * 0.1}px`,
        left: `${width * 0.1}px`,
      };
      if (layoutMove == true) {
        const VehicleViewListWidth = 412;
        const visibleWidth = width - VehicleViewListWidth;
        padding.right = `${visibleWidth * 0.1 + VehicleViewListWidth}px`;
        padding.left = `${visibleWidth * 0.1}px`;
      }
      mapAdapter.fitBounds(bounds, padding);
    }
  };

  return (
    <div className={'w-full h-full'}>
      {/* 지도 영역 */}
      <GeneralMap
        mapSource={'logi'}
        className={'rounded-b-3xl w-full h-full overflow-hidden'}
        id={'heatmap'}
        maxZoom={maxZoomLevel}
        minZoom={minZoomLevel}
        defaultZoom={defaultLevel}
        onInitMap={handleMapInit}
        onZoomChanged={handleMapZoomChanged}
      >
        <GeneralHeatmap
          data={heatmapDataArray.current}
          radius={radius}
          opacity={opacity}
        />
      </GeneralMap>
      {/* 줌 컨트롤 */}
      <ZoomController
        right={layoutMove ? 'left-[462px]' : 'left-5'}
        bottom={'bottom-[30px]'}
        plus={function (): void {
          mapAdapter?.zoomIn();
        }}
        minus={function (): void {
          mapAdapter?.zoomOut();
        }}
      />
      {/*  필터 */}
      <HeatmapSearchFilter onSearch={handleSearch} />
      <VehicleViewListButton status={layoutMove} />
      {layoutMove && (
        <HeatmapFilteredMapList
          items={searchResult}
          onRefreshClick={handleRefreshClick}
        />
      )}
    </div>
  );
};

export default Heatmap;
