import { useTranslation } from 'react-i18next';
import UseManagementPopup from '@/Pages/Management/Component/UseManagementPopup.tsx';
import { CustomFrame } from '@/Pages/CustomFrame';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';

interface DealerTableItem {
  dealer: string;
  dealerName: string;
  region: string;
  country: string;
  customerU: number;
  rental: number;
  used: number;
  total: number;
}

interface FleetSelectItem {
  key: string;
  value: string;
}

const Dealership = () => {
  const { t } = useTranslation();
  const { openAgentDetailsPopup } = UseManagementPopup();

  const [pageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(2);

  // DropDown 더미 데이터
  const areaSelectData: FleetSelectItem[] = [
    { key: t('All'), value: 'ALL' },
    { key: '서울', value: 'SEOUL' },
    { key: '부산', value: 'BUSAN' },
  ];
  const countrySelectData: FleetSelectItem[] = [
    { key: t('All'), value: 'ALL' },
    { key: 'KR', value: 'KR' },
    { key: 'JP', value: 'JP' },
  ];
  const fleetDealerData: FleetSelectItem[] = [
    { key: t('All'), value: 'ALL' },
    { key: 'DealerA', value: 'A' },
    { key: 'DealerB', value: 'B' },
  ];
  const typeSelectData: FleetSelectItem[] = [
    { key: t('All'), value: 'ALL' },
    { key: '굴착기', value: 'E' },
    { key: '로더', value: 'L' },
  ];

  const [inputValues, setInputValues] = useState({
    pageNum: 1,
    pageSize: pageSize,
    region: '',
    country: '',
    dealerId: '',
    dealerName: '',
    type: 'ALL',
  });

  // 드롭다운 변경 핸들러
  const handleDropdownChange = (field: string, value: string) => {
    setInputValues((prev) => ({
      ...prev,
      [field]: value,
    }));
    if (field === 'region') {
      setInputValues((prev) => ({
        ...prev,
        country: '',
        dealerName: '',
      }));
    }
    if (field === 'country') {
      setInputValues((prev) => ({
        ...prev,
        dealerName: '',
      }));
    }
  };

  // 페이지 변경 핸들러
  const handlePageChange = (newPage: number) => {
    setInputValues((prev) => ({
      ...prev,
      pageNum: newPage,
    }));
  };

  const handleSearch = () => {
    setInputValues((prev) => ({
      ...prev,
      pageNum: 1,
    }));
    // 검색시 totalCount 등 필요한 추가 상태도 세팅 가능
  };

  // 더미 테이블 데이터
  const equipmentDealerListData: DealerTableItem[] = [
    {
      dealer: 'A001',
      dealerName: '서울딜러',
      region: '서울',
      country: 'KR',
      customerU: 12,
      rental: 5,
      used: 2,
      total: 19,
    },
    {
      dealer: 'B002',
      dealerName: '부산딜러',
      region: '부산',
      country: 'KR',
      customerU: 8,
      rental: 4,
      used: 1,
      total: 13,
    },
  ];

  const columns: ColumnDef<DealerTableItem>[] = [
    {
      header: t('DealerCode2'),
      accessorKey: 'dealer',
      cell: ({ row }) => (
        <span
          onClick={() => openAgentDetailsPopup(row.original)}
          className="blue-underline"
        >
          {row.original.dealer}
        </span>
      ),
    },
    {
      header: t('DealershipName'),
      accessorKey: 'dealerName',
    },
    {
      header: t('Region'),
      accessorKey: 'region',
    },
    {
      header: t('Country'),
      accessorKey: 'country',
    },
    {
      header: () => (
        <div className="text-center">
          <div>{t('Machines')}</div>
          <div className="text-xs text-gray-500">
            {t('CustomerRentalUsedTotal')}
          </div>
        </div>
      ),
      accessorKey: 'regUserCnt',
      cell: ({ row }) => (
        <div className="text-center text-base font-medium leading-tight">
          {row.original.customerU} / {row.original.rental} / {row.original.used}{' '}
          / {row.original.total}
        </div>
      ),
    },
    {
      header: t('NumberOfRegisteredCustomers'),
      accessorKey: 'customerU',
    },
  ];

  return (
    <CustomFrame name={t('MachineManagementStatusbyDealer')} back={false}>
      <section className="pt-10">
        {/* 필터 선택 */}
        <article className="mb-10 flex items-center justify-between">
          <div className=" flex items-center  gap-6">
            <SearchItemContainer>
              <SearchLabel>{t('Region')}</SearchLabel>
              <DropDown
                onChange={(value) =>
                  handleDropdownChange('region', value.toString())
                }
                options={areaSelectData}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Country')}</SearchLabel>
              <DropDown
                onChange={(value) =>
                  handleDropdownChange('country', value.toString())
                }
                options={countrySelectData}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('DealershipName')}</SearchLabel>
              <DropDown
                onChange={() => {}}
                options={fleetDealerData}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Type')}</SearchLabel>
              <DropDown
                onChange={(value) =>
                  handleDropdownChange('type', value.toString())
                }
                options={typeSelectData}
                placeholder={t('All')}
              />
            </SearchItemContainer>
          </div>
          <div className="flex gap-3">
            <Button
              variant={'bt_primary'}
              label={'Search'}
              onClick={handleSearch}
            />
            <Button variant={'bt_primary'} label={'Print'} />
          </div>
        </article>

        {/* 테이블  */}
        <CommonTable
          columns={columns}
          data={equipmentDealerListData}
          isPagination={true}
          customPageSize={pageSize}
          currentPage={inputValues.pageNum}
          totalCount={totalCount}
          onPageChange={handlePageChange}
        />
      </section>
    </CustomFrame>
  );
};

export default Dealership;
