import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { CustomFrame } from '@/Pages/CustomFrame';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import Input from '@/Common/Components/common/Input';
import CommonTable from '@/Common/Components/common/CommonTable';
import { FleetType } from '@/types/FleetType';

const Account = () => {
  const { t } = useTranslation();

  const [selectedCheck, setSelectedCheck] = useState<number[]>([]);

  const handleSelectionChange = (
    selectedRows: FleetType.FleetListStructure[],
  ) => {
    if (Array.isArray(selectedRows)) {
      setSelectedCheck(selectedRows.map((row) => row.seqNo ?? 0));
    }
  };

  const options = [
    { key: t('All'), value: 'all' },
    { key: t('User'), value: 'option1' },
    { key: t('Dealer'), value: 'option2' },
    { key: t('SubUser'), value: 'option3' },
    { key: t('Admin'), value: 'option4' },
  ];

  const columns = [
    {
      header: t('DealerCode2'),
      accessorKey: 'num',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => (
        <span className="blue-underline">{cell.getValue() as string}</span>
      ),
    },
    {
      header: t('DealershipName'),
      accessorKey: 'name',
    },
    {
      header: t('UserID'),
      accessorKey: 'id',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => (
        <span className="blue-underline">{cell.getValue() as string}</span>
      ),
    },
    {
      header: t('UserName'),
      accessorKey: 'user',
    },
    {
      header: t('Permission'),
      accessorKey: 'access',
    },
    {
      header: t('Region'),
      accessorKey: 'area',
    },
    {
      header: t('Country'),
      accessorKey: 'country',
    },
    {
      header: t('Machines'),
      accessorKey: 'eq',
    },
    {
      header: t('RegistrationDate'),
      accessorKey: 'date',
    },
  ];
  const data = [
    {
      num: '0010190066',
      name: 'SHRIVENKATESHA HEAVY EQUIPMENT',
      id: '0000661',
      user: 'Matthew Zehring',
      access: 'User',
      area: 'North America',
      country: 'USA',
      eq: '2',
      date: '2024-11-25',
    },
    {
      num: '**********',
      name: 'INDOTECH HEAVY EQUIPMENT',
      id: '0000662',
      user: 'Daniel',
      access: 'User',
      area: 'North America',
      country: 'USA',
      eq: '2',
      date: '2024-11-25',
    },
    {
      num: '**********',
      name: 'SRI LAKSHMI EQUIPMENTS',
      id: '0000663',
      user: 'Michael',
      access: 'User',
      area: 'North America',
      country: 'USA',
      eq: '2',
      date: '2024-11-25',
    },
  ];

  return (
    <CustomFrame name={t('AccountManagement')} back={false}>
      <section className="pt-10">
        {/* 필터 선택 */}
        <article className="mb-5 flex items-center gap-6">
          <SearchItemContainer>
            <SearchLabel>{t('Region')}</SearchLabel>
            <DropDown onChange={() => {}} options={[]} placeholder={t('All')} />
          </SearchItemContainer>
          <SearchItemContainer>
            <SearchLabel>{t('Country')}</SearchLabel>
            <DropDown onChange={() => {}} options={[]} placeholder={t('All')} />
          </SearchItemContainer>
          <SearchItemContainer>
            <SearchLabel>{t('DealershipName')}</SearchLabel>
            <DropDown onChange={() => {}} options={[]} placeholder={t('All')} />
          </SearchItemContainer>
          <SearchItemContainer>
            <SearchLabel>{t('Permission')}</SearchLabel>
            <DropDown
              onChange={() => {}}
              options={options}
              placeholder={t('All')}
            />
          </SearchItemContainer>
        </article>

        {/* 필터 검색창 */}
        <article>
          <div className="mb-10 flex items-center justify-between gap-3">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-6">
                <span className="body1-b">{t('UserID')}</span>
                <Input placeholder={t('UserID')} />
              </div>
              <div className="flex items-center gap-6">
                <span className="body1-b">{t('User')}</span>
                <Input placeholder={t('User')} />
              </div>
            </div>
            <div className="flex gap-3">
              <Button variant={'bt_primary'} label={'Search'}>
                {t('')}
              </Button>
              <Button variant={'bt_primary'} label={'Print'}>
                {t('')}
              </Button>
            </div>
          </div>
        </article>

        {/* 테이블 버튼 */}
        <article className="mb-3 flex gap-2">
          <Button
            variant={'bt_primary'}
            label={'PasswordReset'}
            disabled={selectedCheck.length === 0}
          />
          <Button
            variant={'bt_primary'}
            label={'AddNew'}
            disabled={selectedCheck.length === 0}
          />
          <Button variant={'bt_primary'} label={'Delete'} />
        </article>

        {/* 테이블  */}
        <CommonTable
          columns={columns}
          data={data}
          isPagination={true}
          isCheckbox={true}
          onSelectionChange={handleSelectionChange}
        />
      </section>
    </CustomFrame>
  );
};

export default Account;
