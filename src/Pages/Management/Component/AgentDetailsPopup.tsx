import { ChangeEvent, useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout.tsx';
import { Tabs } from '@radix-ui/themes';
import CommonTable from '@/Common/Components/common/CommonTable';
import SummaryData from '@/Common/Components/etc/SummaryData';
import { Button } from '@/Common/Components/common/Button';
import Input from '@/Common/Components/common/Input';
import { Cross1Icon } from '@radix-ui/react-icons';
import { ManagementType } from '@/types/ManagementType';

interface AlertPopupProps {
  data?: ManagementType.DealerManagementColumnsProps;
  onClose: () => void;
  isOpen: boolean;
}

interface EquipmentData {
  rowNumber: number;
  smodel: string;
  hogi: string;
  equipmentId: string;
  mileage: string;
  customer: string;
  rentalYn: string;
  sechandYn: string;
}

interface UserData {
  rowNumber: number;
  userId: string;
  customerName: string;
  userType: string;
  country: string;
}

const AgentDetailsPopup = ({ data, onClose, isOpen }: AlertPopupProps) => {
  const { t } = useTranslation();

  const summaryData = [
    { label: 'DealerCode2', value: data?.dealer },
    { label: 'DealershipName', value: data?.dealerName },
    { label: 'Region', value: data?.region },
    { label: 'Country', value: data?.country },
    { label: 'TotalMachines', value: data?.total },
    { label: 'CustomerRegistration', value: data?.customerU },
    { label: 'Rental', value: data?.rental },
    { label: 'Used', value: data?.used },
  ];

  const [tabValue, setTabValue] = useState(t('MachineList2'));

  const [pageSize] = useState(6);
  const [eqTotalCount] = useState(50); // Mock total count

  const [inputEqValues, setInputEqValues] = useState({
    equipmentId: '',
    vmodel: '',
    hogi: '',
  });

  // Mock 장비 데이터
  const mockEquipmentData: EquipmentData[] = useMemo(
    () => [
      {
        rowNumber: 1,
        smodel: 'EX120-5',
        hogi: 'EQ001',
        equipmentId: 'SN123456',
        mileage: '1250',
        customer: 'ABC Construction',
        rentalYn: 'Y',
        sechandYn: 'N',
      },
      {
        rowNumber: 2,
        smodel: 'EX200-6',
        hogi: 'EQ002',
        equipmentId: 'SN789012',
        mileage: '2350',
        customer: 'XYZ Mining',
        rentalYn: 'N',
        sechandYn: 'N',
      },
      {
        rowNumber: 3,
        smodel: 'DX140LC-5',
        hogi: 'EQ003',
        equipmentId: 'SN345678',
        mileage: '890',
        customer: 'DEF Industries',
        rentalYn: 'Y',
        sechandYn: 'Y',
      },
    ],
    [],
  );

  // 장비 리스트
  const columns1 = [
    {
      header: t('No'),
      accessorKey: 'rowNumber',
    },
    {
      header: t('Model'),
      accessorKey: 'smodel',
    },
    {
      header: t('MachineID'),
      accessorKey: 'hogi',
    },
    {
      header: t('SerialNo'),
      accessorKey: 'equipmentId',
    },
    {
      header: t('Mileage'),
      accessorKey: 'mileage',
    },
    {
      header: t('Customer'),
      accessorKey: 'customer',
    },
    {
      header: t('Rental'),
      accessorKey: 'rentalYn',
    },
    {
      header: t('Used'),
      accessorKey: 'sechandYn',
    },
  ];

  const [userTotalCount] = useState(30); // Mock total count

  const [inputUserValues, setInputUserValues] = useState({
    userId: '',
    userName: '',
  });

  // Mock 사용자 데이터
  const mockUserData: UserData[] = useMemo(
    () => [
      {
        rowNumber: 1,
        userId: 'user001',
        customerName: 'John Smith',
        userType: 'Admin',
        country: 'Korea',
      },
      {
        rowNumber: 2,
        userId: 'user002',
        customerName: 'Jane Doe',
        userType: 'User',
        country: 'Japan',
      },
      {
        rowNumber: 3,
        userId: 'user003',
        customerName: 'Mike Johnson',
        userType: 'Operator',
        country: 'China',
      },
    ],
    [],
  );

  // 사용자 리스트
  const columns2 = [
    {
      header: t('No'),
      accessorKey: 'rowNumber',
    },
    {
      header: t('UserID'),
      accessorKey: 'userId',
    },
    {
      header: t('UserName'),
      accessorKey: 'customerName',
    },
    {
      header: t('Type'),
      accessorKey: 'userType',
    },
    {
      header: t('Country'),
      accessorKey: 'country',
    },
  ];

  // 검색 조건 변경 함수
  const updateSearchParams = (
    field: string | number | boolean,
    value: string | number | boolean,
  ) => {
    console.log('updateSearchParams', field, value);
  };

  // 입력 필드 변경 핸들러
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (tabValue === t('MachineList2')) {
      setInputEqValues((prev) => ({
        ...prev,
        [name]: value,
      }));
    } else {
      setInputUserValues((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  // 입력 필드 초기화 핸들러
  const resetInput = (field: string) => {
    if (tabValue === t('MachineList2')) {
      setInputEqValues((prev) => ({
        ...prev,
        [field]: '',
      }));
    } else {
      setInputUserValues((prev) => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  const handleSearch = () => {
    console.log('handleSearch', inputEqValues, inputUserValues);
  };

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[1236px] p-10 bg-white rounded-lg">
        {/*  */}
        <article className="heading2 mb-[34px] flex items-center justify-between">
          {t('DealerDetails')}
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/* 대리점 정보 요역 */}
        <article className="mb-6 flex justify-start">
          <div className="body1-b mr-[26px]">{t('DealerInformation')}</div>
          <SummaryData details={summaryData} fs="lg" />
        </article>

        {/* 탭 */}
        <Tabs.Root value={tabValue} onValueChange={setTabValue}>
          <Tabs.List className="tab-design">
            <Tabs.Trigger value={t('MachineList2')}>
              <span>{t('MachineList2')}</span>
            </Tabs.Trigger>
            <Tabs.Trigger value={t('UserList')}>
              <span>{t('UserList')}</span>
            </Tabs.Trigger>
          </Tabs.List>

          {/* 장비 리스트*/}
          <Tabs.Content value={t('MachineList2')}>
            <div className="mt-[34px] mb-6 flex items-center justify-between gap-3">
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-6">
                  <span className="body1-b">{t('Model')}</span>
                  <Input
                    placeholder={t('Model')}
                    className="w-[166px]"
                    name="vmodel"
                    value={inputEqValues.vmodel}
                    onChange={handleInputChange}
                    reset={() => resetInput('vmodel')}
                  />
                </div>
                <div className="flex items-center gap-6">
                  <span className="body1-b">{t('MachineID')}</span>
                  <Input
                    placeholder={t('MachineID')}
                    className="w-[166px]"
                    name="hogi"
                    value={inputEqValues.hogi}
                    onChange={handleInputChange}
                    reset={() => resetInput('hogi')}
                  />
                </div>
                <div className="flex items-center gap-6">
                  <span className="body1-b">{t('SerialNo')}</span>
                  <Input
                    placeholder={t('SerialNo')}
                    className="w-[166px]"
                    name="equipmentId"
                    value={inputEqValues.equipmentId}
                    onChange={handleInputChange}
                    reset={() => resetInput('equipmentId')}
                  />
                </div>
              </div>
              <div className="flex gap-3">
                <Button
                  variant={'bt_primary'}
                  label={'Search'}
                  onClick={handleSearch}
                />
                <Button variant={'bt_primary'} label={'Print'} />
              </div>
            </div>
            <CommonTable
              columns={columns1}
              data={mockEquipmentData}
              isPagination={true}
              customPageSize={pageSize}
              currentPage={1}
              totalCount={eqTotalCount}
              onPageChange={(page) => updateSearchParams('pageNum', page)}
            />
          </Tabs.Content>

          {/* 사용자 리스트 */}
          <Tabs.Content value={t('UserList')}>
            <div className="mt-[34px] mb-6 flex items-center justify-between gap-3">
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-6">
                  <span className="body1-b">{t('UserId')}</span>
                  <Input
                    placeholder={t('UserId')}
                    className="w-[166px]"
                    name="userId"
                    value={inputUserValues.userId}
                    onChange={handleInputChange}
                    reset={() => resetInput('userId')}
                  />
                </div>
                <div className="flex items-center gap-6">
                  <span className="body1-b">{t('UserName')}</span>
                  <Input
                    placeholder={t('UserName')}
                    className="w-[166px]"
                    name="userName"
                    value={inputUserValues.userName}
                    onChange={handleInputChange}
                    reset={() => resetInput('userName')}
                  />
                </div>
              </div>
              <div className="flex gap-3">
                <Button
                  variant={'bt_primary'}
                  label={'Search'}
                  onClick={handleSearch}
                />
                <Button variant={'bt_primary'} label={'Print'} />
              </div>
            </div>
            <CommonTable
              columns={columns2}
              data={mockUserData}
              isPagination={true}
              customPageSize={pageSize}
              currentPage={1}
              totalCount={userTotalCount}
              onPageChange={(page) => updateSearchParams('pageNum', page)}
            />
          </Tabs.Content>
        </Tabs.Root>

        {/* 버튼 */}
        <article className="mt-2.5 flex justify-end">
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
        </article>
      </section>
    </Layout>
  );
};

export default AgentDetailsPopup;
