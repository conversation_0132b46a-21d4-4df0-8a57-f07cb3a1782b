import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import DaySelector from '@/Common/Components/datePicker/DaySelector';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import { Cross1Icon } from '@radix-ui/react-icons';

const ServiceRegistrationPopup = ({ onClose, isOpen }: AlertPopupProps) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';

  const [operatingTime, setOperatingTime] = useState('');
  const [actionTakenBy, setActionTakenBy] = useState('');
  const [inspectionDetails, setInspectionDetails] = useState('');
  const [isFormValid, setIsFormValid] = useState(false);

  useEffect(() => {
    const isValid =
      operatingTime.trim() !== '' &&
      actionTakenBy.trim() !== '' &&
      inspectionDetails.trim() !== '';
    setIsFormValid(isValid);
  }, [operatingTime, actionTakenBy, inspectionDetails]);

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[720px] p-10 bg-white rounded-lg">
        {/* 타이틀 & 닫기 */}
        <article className="heading2 flex items-center justify-between">
          {t('RegisterServiceHistory')}
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/* 입력 필드 */}
        <article className="mt-[34px] space-y-6 flex items-start flex-col [&_span]:mr-[26px]">
          {/* 고장 발생일 */}
          <SearchItemContainer className="gap-0">
            <SearchLabel className={`w-[95px] ${isEnglish ? 'w-[230px]' : ''}`}>
              {t('FaultOccurrenceDate')}
            </SearchLabel>
            <DaySelector />
          </SearchItemContainer>

          {/* 수리 완료일 */}
          <SearchItemContainer className="gap-0">
            <SearchLabel className={`w-[95px] ${isEnglish ? 'w-[230px]' : ''}`}>
              {t('FaultResolutionDate2')}
            </SearchLabel>
            <DaySelector />
          </SearchItemContainer>

          {/* 가동 시간 */}
          <div className="flex items-center">
            <span
              className={`body1-b w-[95px] ${isEnglish ? 'w-[165px]' : ''}`}
            >
              {t('OperatingTime')} <em className="text-error">*</em>
            </span>
            <Input
              placeholder={t('OperatingTime')}
              value={operatingTime}
              onChange={(e) => setOperatingTime(e.target.value)}
            />
          </div>

          {/* 조치자 */}
          <div className="flex items-center">
            <span
              className={`body1-b w-[95px] ${isEnglish ? 'w-[165px]' : ''}`}
            >
              {t('ActionTakenBy')} <em className="text-error">*</em>
            </span>
            <Input
              placeholder={t('ActionTakenBy')}
              value={actionTakenBy}
              onChange={(e) => setActionTakenBy(e.target.value)}
            />
          </div>

          {/* 점검 내용 */}
          <div className="flex items-start justify-between">
            <span
              className={`body1-b w-[95px] ${isEnglish ? 'w-[165px]' : ''}`}
            >
              {t('InspectionDetails')} <em className="text-error">*</em>
            </span>
            <textarea
              placeholder={t('InspectionDetails')}
              className="body3-m w-[520px] min-h-[200px] py-3 px-4 border border-gray-4 rounded"
              value={inspectionDetails}
              onChange={(e) => setInspectionDetails(e.target.value)}
            />
          </div>
        </article>

        {/* 버튼 */}
        <article className="mt-10 flex justify-end space-x-3">
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
          <Button
            variant={'bt_primary'}
            label={'Add2'}
            disabled={!isFormValid}
          />
        </article>
      </section>
    </Layout>
  );
};

export default ServiceRegistrationPopup;
