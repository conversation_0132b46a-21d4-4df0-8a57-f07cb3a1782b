import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import UseManagementPopup from '@/Pages/Management/Component/UseManagementPopup.tsx';
import Layout from '@/Common/Popup/Layout.tsx';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import { Cross1Icon } from '@radix-ui/react-icons';

const ServiceHistoryPopup = ({ onClose, isOpen }: AlertPopupProps) => {
  const { t } = useTranslation();

  const { openHistotryDeletePopup } = UseManagementPopup();

  return (
    <Layout isOpen={isOpen} zIndex={21}>
      <section className="w-[720px] p-10 bg-white rounded-lg">
        {/*  */}
        <article className="heading2 flex items-center justify-between">
          {t('ServiceHistory')}
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/*  */}
        <article className="mt-[34px] space-y-6 flex items-start flex-col [&_span]:w-[90px] [&_span]:mr-[26px] ">
          {/* 클레임 정보 */}
          <div className="flex items-center">
            <span className="body1-b">{t('ClaimInformation')}</span>
            <div className="space-x-[30px] body1-s [&_em:nth-child(odd)]:text-gray-9">
              <em>{t('ClaimNumber')}</em>
              <em className="pr-3">123456778901</em>
              <em>{t('ClaimType')}</em>
              <em>FOC</em>
            </div>
          </div>
          {/* 고장 발생일 */}
          <SearchItemContainer className="justify-left gap-0 ">
            <SearchLabel className="w-[115px]">
              {t('FaultOccurrenceDate')}
            </SearchLabel>
            <div className="body1-s">2024-12-23</div>
          </SearchItemContainer>
          {/* 수리 완료일 */}
          <SearchItemContainer className="justify-left gap-0">
            <SearchLabel className="w-[115px] display-[block]">
              {t('FaultResolutionDate2')}
            </SearchLabel>
            <div className="body1-s">2024-12-23</div>
          </SearchItemContainer>
          {/* 가동 시간 */}
          <div className="flex items-center">
            <span className="body1-b">
              {t('OperatingTime')}
              {/*<em className="text-error">*</em>*/}
            </span>
            <div className="body1-s">1,253</div>
          </div>
          {/* 조치자 */}
          <div className="flex items-center">
            <span className="body1-b">
              {t('ActionTakenBy')}
              {/*<em className="text-error">*</em>*/}
            </span>
            <div className="body1-s">Michael</div>
          </div>
          {/* 점검 내용 */}
          <div className="flex items-start justify-between">
            <span className="body1-b">
              {t('InspectionDetails')}
              {/*<em className="text-error">*</em>*/}
            </span>
            <div className="body3-m w-[525px] min-h-[200px] pr-10">
              Seat part failure, contacted the person in charge.
              <br />
              <br />
              Due to a malfunction in the slide under the seat, the seat could
              not be adjusted forward and backward, so the bottom part was
              removed and separated. The seat will be handled by the dealer.
            </div>
          </div>
        </article>

        {/* 버튼 */}
        <div className="mt-10 flex justify-end space-x-3">
          <Button
            variant={'bt_primary'}
            label={'Delete'}
            onClick={() => openHistotryDeletePopup(onClose)}
          />
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
          <Button variant={'bt_primary'} label={'Edit'} />
        </div>
      </section>
    </Layout>
  );
};

export default ServiceHistoryPopup;
