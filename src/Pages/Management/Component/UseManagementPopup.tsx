import { useTranslation } from 'react-i18next';
import { useOverlay } from '@toss/use-overlay';
import { useToast } from '@/Common/useToast.tsx';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';
import ServiceHistoryPopup from '@/Pages/Management/Component/ServiceHistoryPopup.tsx';
import ServiceRegistrationPopup from '@/Pages/Management/Component/ServiceRegistrationPopup.tsx';
import AgentDetailsPopup from '@/Pages/Management/Component/AgentDetailsPopup.tsx';
import { ManagementType } from '@/types/ManagementType';

const UseManagementPopup = () => {
  const { t } = useTranslation();

  const overlay = useOverlay();
  const { toast } = useToast();

  const openHistotryDeletePopup = (cb: () => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TwoButtonPopup
          onClose={close}
          onConfirm={() => {
            toast({
              types: 'warning',
              description: t('TheServiceHistoryHasBeenDeleted'),
            });
            cb();
            close();
          }}
          isOpen={isOpen}
          text={t('AreYouSureYouWantToDeleteTheDeviceTheServiceHistroy')}
          buttonText={t('Cancel')}
          secondButtonText={t('Delete')}
        />
      );
    });
  };
  // 서비스 이력
  const openServiceHistoryPopup = () => {
    overlay.open((p) => {
      return (
        <ServiceHistoryPopup
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
        />
      );
    });
  };
  // 서비스 이력 등록
  const openServiceRegistrationPopup = () => {
    overlay.open((p) => {
      return (
        <ServiceRegistrationPopup
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
          onConfirm={() => {
            p.close();
          }}
        />
      );
    });
  };
  // 대리점 상세 정보
  const openAgentDetailsPopup = (
    data: ManagementType.DealerManagementColumnsProps,
  ) => {
    overlay.open((p) => {
      return (
        <AgentDetailsPopup
          data={data}
          isOpen={p.isOpen}
          onClose={() => {
            p.close();
          }}
        />
      );
    });
  };

  return {
    openHistotryDeletePopup,
    openServiceHistoryPopup,
    openServiceRegistrationPopup,
    openAgentDetailsPopup,
  };
};

export default UseManagementPopup;
