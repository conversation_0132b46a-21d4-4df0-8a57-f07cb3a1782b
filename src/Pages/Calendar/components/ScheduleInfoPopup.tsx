import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import close_popup from '@/assets/images/etc/close_popup.png';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import { Button } from '@/Common/Components/common/Button';
import { calendarTextColor } from '@/Common/function/utils.ts';
import SearchHtmlLabel from '@/Common/Components/layout/SearchHtmlLabel';
import UseSchedulePopup from '@/Pages/Calendar/components/useSchedulePopup.tsx';

type CalendarInfo = {
  colorType?: string;
  title?: string;
  repeatType?: string;
  publicStatus?: string;
  startDate?: string;
  startTime?: string;
  endDate?: string;
  endTime?: string;
  context?: string;
};

const ScheduleInfoPopup = ({ isOpen, onClose }: AlertPopupProps) => {
  const { t } = useTranslation();

  const { openScheduleAddPopup } = UseSchedulePopup();

  const calendarInfo: CalendarInfo = {};

  const repeatStr = (type?: string) => {
    switch (type) {
      case 'N':
        return t('N');
      case 'DAILY':
        return t('DAILY');
      case 'WEEKLY':
        return t('WEEKLY');
      case 'MONTHLY':
        return t('MONTHLY');
      case 'YEARLY':
        return t('YEARLY');
      default:
        return t('N');
    }
  };

  return (
    <Layout isOpen={isOpen}>
      <div>
        <div>
          <div>
            <div>
              <div>{t('NotificationDetails')}</div>
            </div>
            <img src={close_popup} onClick={onClose} />
          </div>
        </div>
        <div>
          <div>
            <SearchItemContainer>
              <div
                style={{
                  backgroundColor: calendarTextColor(calendarInfo?.colorType),
                  width: 16,
                  height: 16,
                  borderRadius: 4,
                }}
              />
              <SearchLabel>{calendarInfo?.title}</SearchLabel>
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Repeat')}</SearchLabel>
              <SearchLabel>{repeatStr(calendarInfo?.repeatType)}</SearchLabel>
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('VisibilityStatus')}</SearchLabel>
              <SearchLabel>
                {calendarInfo?.publicStatus === 'PARTIAL'
                  ? t('RestrictedP')
                  : t('AllP')}
              </SearchLabel>
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Start')}</SearchLabel>
              <SearchLabel>
                {calendarInfo?.startDate}{' '}
                {String(calendarInfo?.startTime).slice(0, 5)}
              </SearchLabel>
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('End')}</SearchLabel>
              <SearchLabel>
                {calendarInfo?.endDate}{' '}
                {String(calendarInfo?.endTime).slice(0, 5)}
              </SearchLabel>
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Content')}</SearchLabel>
              <SearchHtmlLabel>{calendarInfo?.context}</SearchHtmlLabel>
            </SearchItemContainer>
          </div>
          <SearchItemContainer>
            <Button variant={'bt_primary'} label={'Delete'} />
            <Button variant={'bt_primary'} label={'Edit'} />
          </SearchItemContainer>
        </div>
      </div>
    </Layout>
  );
};

export default ScheduleInfoPopup;
