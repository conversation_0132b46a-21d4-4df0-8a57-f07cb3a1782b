import { useOverlay } from '@toss/use-overlay';
import ScheduleAddPopup from '@/Pages/Calendar/components/ScheduleAddPopup.tsx';
import ScheduleInfoPopup from '@/Pages/Calendar/components/ScheduleInfoPopup.tsx';

const UseSchedulePopup = () => {
  const overlay = useOverlay();
  const openScheduleAddPopup = (onConfirm: () => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <ScheduleAddPopup
          isOpen={isOpen}
          onClose={close}
          onConfirm={() => {
            onConfirm();
            close();
          }}
        />
      );
    });
  };
  const openScheduleInfoPopup = (calendarId: string, onConfirm: () => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <ScheduleInfoPopup
          isOpen={isOpen}
          onClose={close}
          onConfirm={() => {
            onConfirm();
            close();
          }}
          calendarId={calendarId}
        />
      );
    });
  };
  return { openScheduleAddPopup, openScheduleInfoPopup };
};

export default UseSchedulePopup;
