import { useTranslation } from 'react-i18next';
import { SyntheticEvent, useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useLocation } from 'react-router-dom';
import { Tabs } from '@radix-ui/themes';
import { DemoTest } from '@/types';
import { EquipmentType } from '@/types/EquipmentType';
import { useElectronicTab } from '@/store/detail-tab.ts';
import { UserLocationProvider } from '@/Common/Components/map/UserLocationContext';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import EqStatisticTab from '@/Pages/MonitoringEq/components/EqList/EqStatisticTab';
import FaultsTab from '@/Pages/MonitoringEq/components/EqList/FaultsTab';
import EQ from '@/Pages/MonitoringEq/components/EqList/EqStatistic/eqinfo/EQ';
import EqStatus from '@/Pages/MonitoringEq/components/EqList/EqStatistic/eqinfo/EqStatus';
import EqWorkStatus from '@/Pages/MonitoringEq/components/EqList/EqStatistic/eqinfo/EqWorkStatus';
import ExpendableTab from '@/Pages/MonitoringEq/components/EqList/ExpendableTab';
import ExpendableTabHoverMenu from '@/Pages/MonitoringEq/components/EqList/Expendables/ExpendableTabHoverMenu';
import ExpendableHistoryTab from '@/Pages/MonitoringEq/components/EqList/Expendables/ExpendableHistoryTab';
import PositionTab from '@/Pages/MonitoringEq/components/EqList/PositionTab';
import MoreInfoTab, {
  MoreInfoTabMenu,
  MoreInfoTabMenuE,
} from '@/Pages/MonitoringEq/components/EqList/moreInfo/MoreInfoTab';
import EngineConsumption from '@/Pages/MonitoringEq/components/EqList/moreInfo/engine/EngineConsumption';
import TemperatureDistribution from '@/Pages/MonitoringEq/components/EqList/moreInfo/engine/TemperatureDistribution';
import DealerServiceCenter from '@/Pages/MonitoringEq/components/EqList/moreInfo/engine/DealerServiceCenter';
import AccidentHistory from '@/Pages/MonitoringEq/components/EqList/moreInfo/engine/AccidentHistory';
import BatteryConsumption from '@/Pages/MonitoringEq/components/EqList/moreInfo/battery/BatteryConsumption';
import LithiumBatteryInfo from '@/Pages/MonitoringEq/components/EqList/moreInfo/battery/LithiumBatteryInfo';
import TemperatureDistributionB from '@/Pages/MonitoringEq/components/EqList/moreInfo/battery/TemperatureDistribution';
import DealerServiceCenterB from '@/Pages/MonitoringEq/components/EqList/moreInfo/battery/DealerServiceCenter';
import AccidentHistoryB from '@/Pages/MonitoringEq/components/EqList/moreInfo/battery/AccidentHistory';
import { Button } from '@/Common/Components/common/Button';
import refresh from '@/assets/images/ic/24/refresh_w.svg';

const EQList = () => {
  const { t, i18n } = useTranslation();

  const location = useLocation();
  const [equipmentId, setEquipmentId] = useState('');

  const isKorean = i18n.language === 'ko';
  const MoreInfoMenu = isKorean ? MoreInfoTabMenu : MoreInfoTabMenuE;

  const { isElectronic } = useElectronicTab((s) => s);

  const [activeTab, setActiveTab] = useState({
    tab: t('Statistics'),
    sub: null as string | null,
  });

  //탭 이벤트
  const handleActiveTabChange = (e: string) => {
    setActiveTab({
      tab: e,
      sub: null,
    });
  };
  //소모품 호버 이벤트
  const onExpendableTabHoverMenuClick = (e: SyntheticEvent) => {
    e.preventDefault();
    const target = e.target as HTMLDivElement;
    setActiveTab({
      tab: t('Consumables'),
      sub: target.innerText,
    });
  };
  //상세 호버 이벤트
  const onMoreInfoTabClick = (e: SyntheticEvent) => {
    e.preventDefault();
    const target = e.target as HTMLDivElement;
    setActiveTab({
      tab: t('MoreInfo'),
      sub: target.innerText,
    });
  };

  /** Query */
  const { data: equipmentBasicInfo } = useQuery<EquipmentType.BasicInfo | null>(
    {
      queryKey: ['eqlist/equipmentBasicInfo', equipmentId],
      queryFn: async () => {
        if (DemoTest.isRandomOn()) {
          return {
            powerType: 'E', // 'E' or ''
            modelName: 'Model X',
            plateNo: '1234-AB',
            serialNo: 'SN123456789',
            deliveryDate: '2023-01-01',
            svcStartDate: '2023-01-02',
            svcEndDate: '2024-01-01',
            commAvail: 'Y',
            commAvailCd: 'Y',
          };
        } else {
          try {
            return {
              powerType: 'E', // 'E' or ''
              modelName: '',
              plateNo: '',
              serialNo: '',
              deliveryDate: '',
              svcStartDate: '',
              svcEndDate: '',
              commAvail: '',
              commAvailCd: '',
            };
          } catch (error) {
            console.error('API 호출 에러:', error);
            throw error;
          }
        }
      },
      enabled: equipmentId.length > 0,
    },
  );

  /** useEffect */
  useEffect(() => {
    if (location.state?.equipmentId) {
      localStorage.setItem('location.equipmentId', location.state.equipmentId);
      setEquipmentId(location.state.equipmentId);
    } else {
      setEquipmentId(localStorage.getItem('location.equipmentId') ?? '');
    }
  }, [location.state?.equipmentId]);

  return (
    <CustomFrame name={t('EquipmentDetails')} back={true}>
      {/*  */}
      <div className="relative">
        <div className="f-c gap-3 absolute top-[-60px] right-0">
          <div className="text-right [&_p]:text-gray-10">
            <p className="pt-[2px] body6">{t('LastUpdated')}</p>
            <p className="caption4">{'2025-04-08 13:34:21'}</p>
          </div>
          <Button
            variant="bt_primary"
            label={
              <div className="f-c gap-1 text-white hover:text-primary-3">
                {t('Update')}
                <img src={refresh} alt="refresh" />
              </div>
            }
          />
        </div>
      </div>

      {/*  */}
      <Tabs.Root value={activeTab.tab} onValueChange={handleActiveTabChange}>
        <Tabs.List className={'tab-design'}>
          <Tabs.Trigger value={t('Statistics')}>
            <span>{t('Statistics')}</span>
          </Tabs.Trigger>
          <Tabs.Trigger value={t('Faults')}>
            <span>{t('Faults')}</span>
          </Tabs.Trigger>
          <Tabs.Trigger value={t('Consumables')}>
            <ExpendableTabHoverMenu onClick={onExpendableTabHoverMenuClick} />
          </Tabs.Trigger>
          <Tabs.Trigger value={t('WorkHistory')}>
            <span>{t('WorkHistory')}</span>
          </Tabs.Trigger>
          <Tabs.Trigger value={t('Settings')} disabled={true}>
            <span>{t('Settings')}</span>
          </Tabs.Trigger>
          <Tabs.Trigger value={t('MoreInfo')} disabled={true}>
            <MoreInfoTab onClick={onMoreInfoTabClick} />
          </Tabs.Trigger>
        </Tabs.List>

        <div className={'pt-[30px] flex gap-4'}>
          <div className={'w-[1300px]'}>
            <Tabs.Content value={t('Statistics')}>
              <EqStatisticTab
                equipmentId={equipmentId}
                equipmentBasicInfo={equipmentBasicInfo}
              />
            </Tabs.Content>
            <Tabs.Content value={t('Faults')}>
              <FaultsTab
                isElectric={equipmentBasicInfo?.powerType == 'E'}
                equipmentId={equipmentId}
                serialNo={equipmentBasicInfo?.serialNo ?? ''}
              />
            </Tabs.Content>
            <Tabs.Content value={t('Consumables')}>
              {activeTab.sub === null ||
              activeTab.sub === t('ConsumablesStatus') ? (
                <ExpendableTab
                  equipmentId={equipmentId}
                  modelName={equipmentBasicInfo?.modelName ?? ''}
                  plateNo={equipmentBasicInfo?.plateNo ?? ''}
                />
              ) : (
                <ExpendableHistoryTab equipmentId={equipmentId} />
              )}
            </Tabs.Content>
            <Tabs.Content value={t('WorkHistory')}>
              {activeTab.tab == t('WorkHistory') && (
                <UserLocationProvider>
                  <PositionTab
                    isElectric={equipmentBasicInfo?.powerType == 'E'}
                    equipmentId={equipmentId}
                  />
                </UserLocationProvider>
              )}
            </Tabs.Content>
            <Tabs.Content value={t('Settings')}></Tabs.Content>
            <Tabs.Content value={t('MoreInfo')}>
              {/* 엔진 전용 */}
              {activeTab.sub === t('EngineConsumption') && (
                <EngineConsumption />
              )}

              {/* 배터리 전용 */}
              {activeTab.sub === t('BatteryConsumption') && (
                <BatteryConsumption />
              )}
              {activeTab.sub === t('LithiumBatteryInfo') && (
                <LithiumBatteryInfo />
              )}

              {/* 공통 라벨: 전동/엔진에 따라 분기 */}
              {activeTab.sub === t('TemperatureDistribution') &&
                (isElectronic ? (
                  <TemperatureDistributionB />
                ) : (
                  <TemperatureDistribution />
                ))}

              {activeTab.sub === t('DealerServiceCenter') &&
                (isElectronic ? (
                  <DealerServiceCenterB />
                ) : (
                  <DealerServiceCenter />
                ))}

              {activeTab.sub === t('AccidentHistory') &&
                (isElectronic ? <AccidentHistoryB /> : <AccidentHistory />)}
            </Tabs.Content>
          </div>

          {/* 차량 정보, 운행 정보 */}
          <div className={'w-[292px] space-y-4 flex-shrink-0'}>
            <EQ
              equipmentId={equipmentId}
              equipmentBasicInfo={equipmentBasicInfo}
            />
            <EqStatus
              equipmentId={equipmentId}
              equipmentBasicInfo={equipmentBasicInfo}
            />
            <EqWorkStatus equipmentId={equipmentId} />
          </div>
        </div>
      </Tabs.Root>
    </CustomFrame>
  );
};

export default EQList;
