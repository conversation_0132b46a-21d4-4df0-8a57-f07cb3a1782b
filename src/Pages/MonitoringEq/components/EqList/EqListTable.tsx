import { useTranslation } from 'react-i18next';
import { useMemo } from 'react';
import {
  CustomTable,
  CustomTableBody,
  CustomTableCell,
  CustomTableHead,
  CustomTableHeader,
  CustomTableRow,
} from '@/Common/Components/common/CustomTable';
import { ColumnDef, flexRender, Table } from '@tanstack/react-table';
import { v4 } from 'uuid';
import { useElectronicTab } from '@/store/detail-tab.ts';

const EqListTable = <P,>({
  table,
  columns,
  onClickRow,
  id,
}: {
  table: Table<P>;
  columns: ColumnDef<P>[];
  onClickRow?: (id?: string) => void;
  id?: string;
}) => {
  const { t } = useTranslation();

  const { isElectronic } = useElectronicTab((s) => s);
  return useMemo(
    () => (
      <CustomTable>
        <CustomTableHeader className={''}>
          {table.getHeaderGroups().map((headerGroup) => (
            <CustomTableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <CustomTableHead
                  key={header.id}
                  style={{
                    width: header.column.getSize(),
                  }}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </CustomTableHead>
              ))}
            </CustomTableRow>
          ))}
        </CustomTableHeader>
        <CustomTableBody>
          {table?.getCoreRowModel().rows.length ? (
            table?.getCoreRowModel().rows.map((row) => (
              <CustomTableRow
                onClick={() => {
                  if (id) {
                    if (onClickRow) {
                      const original = row.original as {
                        [key: string]: string;
                      };
                      onClickRow(original[id]);
                    }
                  } else {
                    if (onClickRow) {
                      onClickRow();
                    }
                  }
                }}
                key={v4()}
              >
                {row
                  ?.getAllCells()
                  .map((cell) => (
                    <CustomTableCell key={v4()}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </CustomTableCell>
                  ))}
              </CustomTableRow>
            ))
          ) : (
            <CustomTableRow>
              <CustomTableCell colSpan={columns.length}>
                {t('NoDataAvailable')}
              </CustomTableCell>
            </CustomTableRow>
          )}
        </CustomTableBody>
      </CustomTable>
    ),
    [isElectronic],
  );
};

export default EqListTable;
