import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { FaultsTableColumnProps } from '@/types';
import UseEqPopup from '@/Pages/MonitoringEq/components/EqList/popup/UseEqPopup';
import FaultsCondition, {
  SearchParams,
} from '@/Pages/MonitoringEq/components/EqList/Alarm/FaultsCondition';
import CommonTable from '@/Common/Components/common/CommonTable';

const FaultsTab = ({
  isElectric,
  equipmentId,
}: {
  isElectric: boolean;
  equipmentId: string;
  serialNo: string;
}) => {
  const { t } = useTranslation();

  const { openChangeStatusPopup } = UseEqPopup();

  const FaultsTableColumn: ColumnDef<FaultsTableColumnProps>[] = [
    {
      size: 122,
      header: () => <div>{t('FaultOccurenceTime')}</div>,
      accessorKey: 'date',
      cell: ({ row }) => <div>{row.original.date}</div>,
      enableSorting: false,
    },
    {
      size: 92,
      header: () => <div>{t('Mileage')}</div>,
      accessorKey: 'mileage',
      cell: ({ row }) => <div>{row.original.mileage}</div>,
      enableSorting: false,
    },
    {
      size: 52,
      header: () => <div>{t('FaultSeverity')}</div>,
      accessorKey: 'severity',
      cell: ({ row }) => <div>{row.original.severity}</div>,
      enableSorting: false,
    },
    {
      size: 52,
      header: () => <div>{t('Status')}</div>,
      accessorKey: 'status',
      cell: ({ row }) => (
        <div
          onClick={openChangeStatusPopup}
          className="underline underline-offset-2 cursor-pointer"
        >
          {row.original.status}
        </div>
      ),
      enableSorting: false,
    },
    {
      size: 92,
      header: () => <div>{t('FaultSymptoms')}</div>,
      accessorKey: 'symptoms',
      cell: ({ row }) => <div>{row.original.symptoms}</div>,
      enableSorting: false,
    },
    {
      size: 482,
      header: () => <div>{t('FaultImage')}</div>,
      accessorKey: 'image',
      cell: () => <div className="blue-underline">{t('View')}</div>,
      enableSorting: false,
    },
    {
      size: 112,
      header: () => <div>{t('Details')}</div>,
      accessorKey: 'details',
      cell: () => <div className="blue-underline">{t('View')}</div>,
      enableSorting: false,
    },
  ];

  /** Query */
  const [locationHistoryParams, setLocationHistoryParams] = useState({
    equipmentId: '',
    severity: '',
    alarmType: '',
    faultType: '',
    status: '',
    fromDate: '',
    toDate: '',
    pageNum: 0,
    pageSize: 0,
  });

  // const FaultsTableData = {
  //   pageNum: 0,
  //   pageSize: 0,
  //   totalCnt: 0,
  //   rows: [] as FaultsTableColumnProps[],
  // };

  const FaultsTableData = {
    pageNum: 1,
    pageSize: 10,
    totalCnt: 2,
    rows: [
      {
        date: '2024-07-18',
        mileage: '13,721mi',
        severity: 'High',
        status: 'Received',
        symptoms: 'O.C. AT RELAY REVERSE WARNING ALARM, DTC; 93',
        image: 'image',
        details: 'details',
      },
      {
        date: '2024-07-18',
        mileage: '13,721mi',
        severity: 'High',
        status: 'Received',
        symptoms: 'O.C. AT RELAY REVERSE WARNING ALARM, DTC; 93',
        image: 'image',
        details: 'details',
      },
    ],
  };

  /** useEffect */

  //초기 검색
  useEffect(() => {
    setLocationHistoryParams((prevState) => ({
      ...prevState,
      equipmentId: equipmentId,
      pageNum: 1,
      pageSize: 10,
    }));
  }, [equipmentId]);

  /** Event Listener */

  //검색 버튼 클릭
  const handleSearch = (params: SearchParams) => {
    setLocationHistoryParams({
      equipmentId: equipmentId,
      severity: params.severity,
      alarmType: params.faultType,
      faultType: params.faultType,
      status: params.status,
      fromDate: params.fromDate,
      toDate: params.toDate,
      pageNum: 1,
      pageSize: 10,
    });
  };

  return (
    <div className={'wrap-layout'}>
      <h2 className="mb-[30px] subtitle3">{t('FaultHistory')}</h2>
      <FaultsCondition isElectric={isElectric} onSearch={handleSearch} />
      <CommonTable<FaultsTableColumnProps>
        data={FaultsTableData.rows}
        columns={FaultsTableColumn}
        isPagination={true}
        customPageSize={FaultsTableData.pageSize}
        totalCount={FaultsTableData.totalCnt}
        currentPage={FaultsTableData.pageNum}
        onPageChange={(page: number) => {
          setLocationHistoryParams((prevState) => ({
            ...prevState,
            pageNum: page,
          }));
        }}
      />
    </div>
  );
};

export default FaultsTab;
