import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { ExpendableTabProps } from '@/types';
import UseEqPopup from '@/Pages/MonitoringEq/components/EqList/popup/UseEqPopup';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import Dropdown from '@/Common/Components/common/DropDown';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import useExpendablePopup from '@/Pages/MonitoringEq/components/EqList/Expendables/useExpendablePopup';

const ExpendableTab = ({
  equipmentId,
}: {
  equipmentId: string;
  modelName: string;
  plateNo: string;
}) => {
  const { t } = useTranslation();

  const { openSetIntervalpopup } = UseEqPopup();

  const { openExchangeTermPopup /*교체 주기 변경*/ } = useExpendablePopup();

  const columns: ColumnDef<ExpendableTabProps>[] = [
    {
      accessorKey: 'item',
      header: () => <div>{t('ConsumableItem')}</div>,
      size: 220,
      cell: ({ row }) => <div>{row.original.item}</div>,
    },
    {
      accessorKey: 'term',
      header: () => <div>{t('ReplacementCycleDays')}</div>,
      size: 92,
      cell: ({ row }) => (
        <div
          onClick={() => {
            openExchangeTermPopup(equipmentId, row.original, refreshData);
          }}
        >
          {row.original.term}
        </div>
      ),
    },
    {
      accessorKey: 'totalCnt',
      header: () => <div>{t('Status')}</div>,
      size: 72,
      cell: ({ row }) => <div>{row.original.totalCnt}</div>,
    },
    {
      accessorKey: 'mileage',
      header: () => <div>{t('LastMaintenanceMileageReplacementDate')}</div>,
      size: 152,
      cell: ({ row }) => <div>{row.original.mileage}</div>,
    },
    {
      accessorKey: 'lastDate',
      header: () => <div>{t('RemainingDays')}</div>,
      size: 112,
      cell: ({ row }) => {
        const value = row.original.lastDate;
        let color = '';
        if (/^D-(\d+)$/.test(value)) {
          const days = parseInt(value.replace('D-', ''), 10);
          if (days <= 14 && days >= 0) color = 'text-[#EAB522]';
        } else if (/^D\+(\d+)$/.test(value)) {
          color = 'text-[#EF3823]';
        } else if (value === 'D-Day') {
          color = 'text-[#EAB522]';
        }
        return <div className={color}>{value}</div>;
      },
    },
  ];

  //구분 필터
  const [gubunSelKey, setGubunSelKey] = useState(t('AllStatus'));

  /** Query */
  const gubunOptions: { key: string; value: string }[] = [
    { key: t('AllStatus'), value: 'all' },
    { key: t('Due'), value: 'due' },
    { key: t('Overdue'), value: 'overdue' },
    { key: t('InMaintenance'), value: 'maintenance' },
    { key: t('Completed'), value: 'completed' },
  ];

  const expendableScheduleRows: ExpendableTabProps[] = [];

  /** useEffect */

  useEffect(() => {
    handleSearchClick();
  }, [equipmentId]);

  /** Event */

  //검색 버튼 클릭
  const handleSearchClick = () => {};

  /** Function */
  const refreshData = () => {
    handleSearchClick();
  };

  return (
    <div className="wrap-layout">
      <h2 className="mb-[30px] subtitle3">{t('ConsumablesStatus')}</h2>

      {/*  */}
      <SearchItemContainer className="mb-[18px] gap-4">
        <Dropdown
          options={gubunOptions}
          placeholder={gubunSelKey}
          onSelKey={setGubunSelKey}
        />
        <Button
          variant={'bt_primary'}
          label={'Search'}
          onClick={handleSearchClick}
        />
      </SearchItemContainer>

      {/*  */}
      <div>
        <div className="w-full mb-[10px] f-c-e gap-2">
          <Button
            variant={'bt_primary_sm'}
            label={'SetInterval'}
            onClick={openSetIntervalpopup}
          />
          <Button variant={'bt_tertiary_sm'} label={'Download'} />
        </div>
        <CommonTable
          data={expendableScheduleRows}
          isPagination={false}
          columns={columns}
        />
      </div>
    </div>
  );
};

export default ExpendableTab;
