import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import dayjs from 'dayjs';
import { handleCapture } from '@/Common/function/pdf';
import { Tabs } from '@radix-ui/themes';
import { DemoTest } from '@/types';
import { Button } from '@/Common/Components/common/Button.tsx';
import DaySelector from '@/Common/Components/datePicker/DaySelector.tsx';
import MonthSelector from '@/Common/Components/datePicker/MonthSelector.tsx';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector.tsx';
import Daily from '@/Pages/MonitoringEq/components/EqList/EqStatistic/Daily.tsx';
import Monthly from '@/Pages/MonitoringEq/components/EqList/EqStatistic/Monthly.tsx';
import CustomRange from '@/Pages/MonitoringEq/components/EqList/EqStatistic/CustomRange.tsx';
import Distance, { DistanceInfo } from './statistics/Distance.tsx';
import EqGaugeInfo, {
  EquipmentGaugeInfo,
} from './EqStatistic/eqinfo/EqGaugeInfo.tsx';
import {
  generateEquipmentDistanceData,
  generateEquipmentGaugeInfo,
} from '@/helpers/equipmentDetailDataGenerator.ts';
import { EquipmentType } from '@/types/EquipmentType.ts';

const EqStatisticTab = ({
  equipmentId,
  equipmentBasicInfo,
}: {
  equipmentId: string;
  equipmentBasicInfo: EquipmentType.BasicInfo | null | undefined;
}) => {
  const { t } = useTranslation();
  const captureRef = useRef<HTMLDivElement>(null);
  const [activeTab, setActiveTab] = useState<string>(t('Daily'));

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  const [controllerParams, setControllerParams] = useState<
    Record<string, string | number | undefined>
  >({});

  const [calendarRange, setCalendarRange] = useState<{
    start: string;
    end: string;
  }>({
    start: '',
    end: '',
  });

  /** params */
  const [equipmentGaugeInfoParams, setEquipmentGaugeInfoParams] = useState({
    isEV: false,
    equipmentId: '',
    date: '',
    time: '',
  });

  /** useQuery */
  const { data: distanceData } = useQuery<DistanceInfo | null>({
    queryKey: ['equipment/distanceInfo'],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return generateEquipmentDistanceData();
      } else {
        try {
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    initialData: null,
    enabled: true,
  });

  const { data: equipmentGaugeInfo } = useQuery<EquipmentGaugeInfo | null>({
    queryKey: ['equipmentGaugeInfo/gauge', equipmentGaugeInfoParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return generateEquipmentGaugeInfo();
      } else {
        try {
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    initialData: null,
    enabled: true,
  });

  /** useEffect */
  useEffect(() => {
    if (equipmentBasicInfo) {
      const currentDate = new Date();

      if (DemoTest.isRandomOn()) {
        setEquipmentGaugeInfoParams({
          isEV: equipmentBasicInfo.powerType == 'E',
          equipmentId: equipmentId,
          date: '2025-02-14',
          time: dayjs(currentDate).format('HH:mm:ss'),
        });
      } else {
        setEquipmentGaugeInfoParams({
          isEV: equipmentBasicInfo.powerType == 'E',
          equipmentId: equipmentId,
          date: dayjs(currentDate).format('YYYY-MM-DD'),
          time: dayjs(currentDate).format('HH:mm:ss'),
        });
      }
    }
  }, [equipmentBasicInfo]);

  const handleDaySelectorInit = (day: string) => {
    setControllerParams((prev) => ({
      ...prev,
      date: dayjs(day).format('YYYY-MM-DD'),
    }));
  };

  const handleDaySelectorChange = (day: string) => {
    setControllerParams((prev) => ({
      ...prev,
      date: dayjs(day).format('YYYY-MM-DD'),
    }));
  };

  const onMonthSelectorInit = (value: unknown) => {
    if (value instanceof Date) {
      setControllerParams((prev) => ({
        ...prev,
        month: dayjs(value).format('YYYY-MM'),
      }));
    }
  };

  const handleMonthSelectorChange = (value: unknown) => {
    if (value instanceof Date) {
      setControllerParams((prev) => ({
        ...prev,
        month: dayjs(value).format('YYYY-MM'),
      }));
    }
  };

  const handleCustomRangeSelectorInit = (start: string, end: string) => {
    setControllerParams((prev) => ({ ...prev, from: start, to: end }));
    setCalendarRange({
      start: dayjs(start).startOf('month').format('YYYY-MM-DD'),
      end: dayjs(end).add(1, 'month').startOf('month').format('YYYY-MM-DD'),
    });
  };

  const handleCustomRangeSelectorChange = (start: string, end: string) => {
    if (!start || !end) return;
    setControllerParams((prev) => ({
      ...prev,
      from: dayjs(start).format('YYYY-MM-DD'),
      to: dayjs(end).format('YYYY-MM-DD'),
    }));
    setCalendarRange({
      start: dayjs(start).startOf('month').format('YYYY-MM-DD'),
      end: dayjs(end).add(1, 'month').startOf('month').format('YYYY-MM-DD'),
    });
  };

  const renderDateController = () => {
    if (activeTab === 'Daily') {
      return (
        <div className="w-max f-c gap-[10px]">
          <DaySelector
            initValue={DemoTest.isRandomOn() ? '2025-02-14' : undefined}
            onInit={handleDaySelectorInit}
            onChange={handleDaySelectorChange}
          />
          <Button
            variant={'bt_tertiary'}
            label={'Download'}
            onClick={() => handleCapture(captureRef, 'Report-Daily.pdf')}
          />
        </div>
      );
    }
    if (activeTab === 'Monthly') {
      return (
        <div className="w-max f-c gap-[10px]">
          <MonthSelector
            value={
              DemoTest.isRandomOn()
                ? new Date()
                : controllerParams.month
                  ? new Date(`${controllerParams.month}-01`)
                  : undefined
            }
            onInit={onMonthSelectorInit}
            onValueChange={handleMonthSelectorChange}
          />
          <Button
            variant={'bt_tertiary'}
            label={'Download'}
            onClick={() => handleCapture(captureRef, 'Report-Monthly.pdf')}
          />
        </div>
      );
    }
    if (activeTab === 'CustomRange') {
      return (
        <div className="w-max f-c gap-[10px]">
          <FromToSelector
            initValue={{
              start: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
              end: dayjs().format('YYYY-MM-DD'),
            }}
            onInit={handleCustomRangeSelectorInit}
            onChange={handleCustomRangeSelectorChange}
          />
          <Button
            variant={'bt_tertiary'}
            label={'Download'}
            onClick={() => handleCapture(captureRef, 'Report-CustomRange.pdf')}
          />
        </div>
      );
    }
    return null;
  };

  return (
    <article ref={captureRef}>
      <div className="grid grid-cols-4 gap-6 [&>div]:bg-white [&>div]:border [&>div]:border-gray-6 [&>div]:rounded-md">
        {/* 거리 정보 */}
        <Distance className="col-span-1" distanceInfo={distanceData} />

        {/* 게이지 그래프 */}
        <div className="col-span-3">
          <EqGaugeInfo equipmentGaugeInfo={equipmentGaugeInfo} />
        </div>

        <div className="p-[30px] col-span-4">
          <Tabs.Root value={activeTab} onValueChange={handleTabChange}>
            <div className="f-c-b">
              <Tabs.List
                style={{ boxShadow: 'none' }}
                className="tab-design-bt"
              >
                <Tabs.Trigger value="Daily">{t('Daily')}</Tabs.Trigger>
                <Tabs.Trigger value="Monthly">{t('Monthly')}</Tabs.Trigger>
                <Tabs.Trigger value="CustomRange">
                  {t('CustomRange')}
                </Tabs.Trigger>
              </Tabs.List>
              {renderDateController()}
            </div>

            <div className="mt-5 p-[30px] bg-gray-1 rounded-md">
              <Tabs.Content value="Daily">
                <Daily
                  equipmentId={equipmentId}
                  equipmentBasicInfo={equipmentBasicInfo}
                  captureRef={captureRef}
                  controllerParams={controllerParams}
                />
              </Tabs.Content>
              <Tabs.Content value="Monthly">
                <Monthly
                  equipmentId={equipmentId}
                  equipmentBasicInfo={equipmentBasicInfo}
                  captureRef={captureRef}
                  controllerParams={controllerParams}
                />
              </Tabs.Content>
              <Tabs.Content value="CustomRange">
                <CustomRange
                  equipmentId={equipmentId}
                  equipmentBasicInfo={equipmentBasicInfo}
                  captureRef={captureRef}
                  controllerParams={controllerParams}
                  calendarRange={calendarRange}
                />
              </Tabs.Content>
            </div>
          </Tabs.Root>
        </div>
      </div>
    </article>
  );
};

export default EqStatisticTab;
