import { useTranslation } from 'react-i18next';
import ECharts from 'echarts-for-react';
import * as echarts from 'echarts';
import { useEffect, useState } from 'react';
import { decimalToTime, timeToDecimal } from '@/Common/function/date';
import { EquipmentType } from '@/types/EquipmentType';

export interface DailyDrivingDistanceProps {
  drivingCommon: EquipmentType.DailyDrivingCommonInfo | null | undefined;
}

/** 일별 운행 거리 (그래프) */

const DailyDrivingDistance = ({ drivingCommon }: DailyDrivingDistanceProps) => {
  const { t } = useTranslation();
  const borderRadius = [0, 40, 40, 0];

  const [drivingDistanceOption, setDrivingDistanceOption] = useState({});

  useEffect(() => {
    let todayDis = 0;
    let avgDis = 0;

    if (drivingCommon?.todayDis) {
      todayDis = timeToDecimal(drivingCommon.todayDis);
    }

    if (drivingCommon?.avgDis) {
      avgDis = timeToDecimal(drivingCommon.avgDis);
    }

    setDrivingDistanceOption({
      textStyle: {
        fontFamily: 'Pretendard',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        valueFormatter: (value: number) => {
          return decimalToTime(value, t('HoursH'), t('MinuteM'));
        },
      },
      legend: { show: false },
      grid: {
        left: '0%',
        right: '10%',
        top: '0%',
        bottom: '0%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        min: 0, // 최소 0
        max: 125, // 최대 15 (or 원하는 값)
        interval: 25, // 눈금 간격 5
        name: 'Km',
        nameLocation: 'end',
        nameTextStyle: {
          fontSize: 14,
          fontWeight: 400,
          align: 'right',
          verticalAlign: 'middle',
          padding: [0, -10, 0, 0],
        },
        axisLabel: {
          fontSize: 14,
          fontWeight: 400,
          color: '#6F6F6F',
          padding: [0, 4, 0, 0],
        },
        splitLine: { lineStyle: { type: 'dashed', color: '#DFDFDF' } },
        axisLine: {
          show: true,
          lineStyle: { type: 'solid', color: '#A8A8A8' },
        },
      },
      yAxis: {
        type: 'category',
        data: [t('30DayAverage'), t('Today')],
        axisTick: {
          show: false,
        },
        axisLine: { show: false, lineStyle: { type: 'dashed' } },
      },
      series: [
        {
          type: 'bar',
          barWidth: 27,
          label: {
            show: false,
          },
          emphasis: {
            focus: 'series',
          },
          data: [
            {
              value: avgDis,
              itemStyle: {
                borderRadius,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#010542',
                  },
                  {
                    offset: 1,
                    color: '#C3C5DF',
                  },
                ]),
              },
            },
            {
              value: todayDis,
              itemStyle: {
                borderRadius,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#010542',
                  },
                  {
                    offset: 1,
                    color: '#C3C5DF',
                  },
                ]),
              },
            },
          ],
        },
      ],
    });
  }, [drivingCommon]);

  return (
    <ECharts
      option={drivingDistanceOption}
      style={{ width: '400px', height: '180px' }}
    ></ECharts>
  );
};

export default DailyDrivingDistance;
