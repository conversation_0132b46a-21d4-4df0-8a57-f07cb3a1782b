import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import EqSingleMarker from '@/Common/Components/Marker/EqSingleMarker';
import EqAddressInfoWindow from '@/Common/Components/eqWindow/EqAddressInfoWindow';
import { MapEngine } from '@/types';
import { GeneralMap, GeneralMapAdapter } from '@/logiMaps/react/general/Map';
import ZoomController from '@/Common/Components/map/ZoomController';

export interface DailyEqLocationProps {
  center: {
    lat: number;
    lng: number;
  };
  updateDate: string;
  address: string;
}

/** 장비 위치 */
const DailyEqLocation: React.FC<DailyEqLocationProps> = (props) => {
  const { t } = useTranslation();

  const maxZoomLevel = 18;
  const minZoomLevel = 4;
  const defaultLevel = 16;

  const [mapAdapter, setMapAdapter] = useState<GeneralMapAdapter | null>(null);

  useEffect(() => {
    if (mapAdapter) {
      mapAdapter.setCenter(props.center);
    }
  }, [props.center]);

  //지도 로드
  const handleMapInit = (generalMapAdapter: GeneralMapAdapter) => {
    generalMapAdapter.setCenter(props.center);
    setMapAdapter(generalMapAdapter);
  };

  return (
    <div className="mt-[10px] py-5 px-6 bg-w-br">
      <h2 className="mb-5 subtitle4">{t('VehicleLocation')}</h2>
      <div>
        <GeneralMap
          mapSource={MapEngine.source()}
          id={'statistic'}
          maxZoom={maxZoomLevel}
          minZoom={minZoomLevel}
          defaultZoom={defaultLevel}
          defaultCenter={props.center}
          onInitMap={handleMapInit}
          className={'w-full h-[318px] overflow-hidden'}
        >
          <EqSingleMarker id={'marker_0'} latlng={props.center} />
          {props.address.length > 0 && (
            <EqAddressInfoWindow
              id={'info_0'}
              position={props.center}
              pixelOffset={[0, -14]}
              address={props.address}
            />
          )}
          <ZoomController
            right={'right-[10px]'}
            bottom={'bottom-[10px]'}
            plus={function (): void {
              mapAdapter?.zoomIn();
            }}
            minus={function (): void {
              mapAdapter?.zoomOut();
            }}
          />
        </GeneralMap>
      </div>
    </div>
  );
};

export default DailyEqLocation;
