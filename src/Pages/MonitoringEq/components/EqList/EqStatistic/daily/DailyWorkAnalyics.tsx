import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import InfoTypeRadio from '@/Common/Components/common/InfoTypeRadio';
import Alert from '@/Pages/MonitoringEq/components/EqList/statistics/Alert';
import DrivingPattern from '@/Pages/MonitoringEq/components/EqList/statistics/DrivingPattern';
import DailyDrivingTime from '@/Pages/MonitoringEq/components/EqList/EqStatistic/daily/DailyDrivingTime';
import DailyDrivingDistance from '@/Pages/MonitoringEq/components/EqList/EqStatistic/daily/DailyDrivingDistance';
import DailyDrivingTimeTable from '@/Pages/MonitoringEq/components/EqList/EqStatistic/daily/DailyDrivingTimeTable';
import DailyDrivingDistanceTable from '@/Pages/MonitoringEq/components/EqList/EqStatistic/daily/DailyDrivingDistanceTable';
import { EquipmentType } from '@/types/EquipmentType';

export interface DailyWorkAnalyicsProps {
  eqDailyInfo: EquipmentType.DailyInfo | null;
}

const DailyWorkAnalyics = ({ eqDailyInfo }: DailyWorkAnalyicsProps) => {
  const { t } = useTranslation();
  const [type, setType] = useState('graph');

  return (
    <article>
      {/* 그래프 or 테이블 */}
      <div className="mb-[10px] f-je">
        <InfoTypeRadio setType={setType} />
      </div>

      <div className="bg-w-br-div grid grid-cols-8 row-auto gap-[10px]">
        {/* 고장 알림 */}
        <Alert
          title="FaultAlert"
          value={eqDailyInfo?.faultAlert.count?.toString() ?? '0'}
          upDown={eqDailyInfo?.faultAlert.recent?.toString() ?? '0'}
          className="col-span-2 row-span-1"
        />

        {/* 운행 시간 */}
        <div className="py-5 px-6 col-span-3 row-span-2">
          <h2 className="mb-5 subtitle4">{t('DrivingTime')}</h2>
          <div>
            {type === 'graph' ? (
              <div className="f-c-c">
                <DailyDrivingTime drivingCommon={eqDailyInfo?.drivingCommon} />
              </div>
            ) : (
              <DailyDrivingTimeTable
                drivingCommon={eqDailyInfo?.drivingCommon}
              />
            )}
          </div>
        </div>

        {/* 운행 거리 */}
        <div className="py-5 px-6 col-span-3 row-span-2">
          <h2 className="mb-5 subtitle4">{t('DrivingDistance')}</h2>
          {type === 'graph' ? (
            <div className="f-c-c">
              <DailyDrivingDistance
                drivingCommon={eqDailyInfo?.drivingCommon}
              />
            </div>
          ) : (
            <DailyDrivingDistanceTable
              drivingCommon={eqDailyInfo?.drivingCommon}
            />
          )}
        </div>

        {/* 소모품 알림 */}
        <Alert
          title="ConsumableAlert"
          value={eqDailyInfo?.consumableAlert.count?.toString() ?? '0'}
          upDown={eqDailyInfo?.consumableAlert.recent?.toString() ?? '0'}
          className="col-span-2 row-span-1"
        />

        {/* 운행 패턴 */}
        <DrivingPattern
          className="col-span-8 row-span-1"
          item={eqDailyInfo?.drivingPattern}
        />
      </div>
    </article>
  );
};

export default DailyWorkAnalyics;
