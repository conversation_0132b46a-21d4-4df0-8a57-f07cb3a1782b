import { useTranslation } from 'react-i18next';
import {
  CustomTable,
  CustomTableBody,
  CustomTableCell,
  CustomTableHead,
  CustomTableHeader,
  CustomTableProps,
  CustomTableRow,
} from '@/Common/Components/common/CustomTable';
import { ColumnDef, flexRender } from '@tanstack/react-table';
import { v4 } from 'uuid';
import { useTable } from '@/Common/Components/hooks/useTable.tsx';
import { useEffect, useState } from 'react';
import { EquipmentType } from '@/types/EquipmentType';

export interface DailyDrivingDistanceTableProps {
  drivingCommon: EquipmentType.DailyDrivingCommonInfo | null | undefined;
}

/** 일별 운행 거리 (테이블) */

const DailyDrivingDistanceTable = ({
  drivingCommon,
  onClickRow,
  id,
}: DailyDrivingDistanceTableProps &
  CustomTableProps<EquipmentType.DailyDrivingDistanceTableColumnProps>) => {
  const { t } = useTranslation();
  const [drivingDistanceTableData, setDrivingDistanceTableData] = useState<
    EquipmentType.DailyDrivingDistanceTableColumnProps[]
  >([]);

  const columns: ColumnDef<EquipmentType.DailyDrivingDistanceTableColumnProps>[] =
    [
      {
        header: () => <div>{t('Category')}</div>,
        accessorKey: 'division',
        cell: ({ row }) => <div>{row.original.division}</div>,
      },

      {
        header: () => <div>{t('DrivingDistance')}</div>,
        accessorKey: 'average',
        cell: ({ row }) => <div>{row.original.work}</div>,
      },
    ];

  useEffect(() => {
    if (drivingCommon) {
      setDrivingDistanceTableData([
        {
          division: 'Power',
          work: drivingCommon.todayDis,
        },
        {
          division: 'Standard',
          work: drivingCommon.avgDis,
        },
      ]);
    }
  }, [drivingCommon]);

  const { table } =
    useTable<EquipmentType.DailyDrivingDistanceTableColumnProps>(
      drivingDistanceTableData,
      columns,
    );

  return (
    <div className="table-border">
      <CustomTable>
        <CustomTableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <CustomTableRow key={headerGroup.id} className={''}>
              {headerGroup.headers.map((header) => (
                <CustomTableHead key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </CustomTableHead>
              ))}
            </CustomTableRow>
          ))}
        </CustomTableHeader>
        <CustomTableBody>
          {table?.getCoreRowModel().rows.length ? (
            table?.getCoreRowModel().rows.map((row) => (
              <CustomTableRow
                onClick={() => {
                  if (id) {
                    if (onClickRow) {
                      const original = row.original;
                      onClickRow(original[id]);
                    }
                  }
                }}
                key={v4()}
              >
                {row
                  ?.getAllCells()
                  .map((cell) => (
                    <CustomTableCell key={v4()}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </CustomTableCell>
                  ))}
              </CustomTableRow>
            ))
          ) : (
            <CustomTableRow>
              <CustomTableCell colSpan={columns.length}>
                {t('NoDataAvailable')}
              </CustomTableCell>
            </CustomTableRow>
          )}
        </CustomTableBody>
      </CustomTable>
    </div>
  );
};

export default DailyDrivingDistanceTable;
