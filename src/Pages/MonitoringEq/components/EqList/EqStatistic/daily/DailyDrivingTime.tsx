import ECharts from 'echarts-for-react';
import { useTranslation } from 'react-i18next';
import * as echarts from 'echarts';
import { useEffect, useState } from 'react';
import { decimalToTime, timeToDecimal } from '@/Common/function/date';
import { EquipmentType } from '@/types/EquipmentType';

export interface DailyDrivingTimeProps {
  drivingCommon: EquipmentType.DailyDrivingCommonInfo | null | undefined;
}

/** 일별 운행 시간 (그래프) */

const DailyDrivingTime = ({ drivingCommon }: DailyDrivingTimeProps) => {
  const { t } = useTranslation();
  const borderRadius = [0, 40, 40, 0];

  const [drivingTimeOption, setDrivingTimeOption] = useState({});

  useEffect(() => {
    let drivingTime = 0;
    let idleTime = 0;

    if (drivingCommon?.drivingTime) {
      drivingTime = timeToDecimal(drivingCommon.drivingTime);
    }

    if (drivingCommon?.idleTime) {
      idleTime = timeToDecimal(drivingCommon.idleTime);
    }

    setDrivingTimeOption({
      textStyle: {
        fontFamily: 'Pretendard',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        valueFormatter: (value: number) => {
          return decimalToTime(value, t('HoursH'), t('MinuteM'));
        },
      },
      legend: { show: false },
      grid: {
        left: '0%',
        right: '8%',
        top: '0%',
        bottom: '0%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        min: 0, // 최소 0
        max: 15, // 최대 15 (or 원하는 값)
        interval: 5, // 눈금 간격 5
        name: 'H',
        nameLocation: 'end',
        nameTextStyle: {
          fontSize: 14,
          fontWeight: 400,
          align: 'right',
          verticalAlign: 'middle',
          padding: [0, -4, 0, 0],
        },
        axisLabel: {
          fontSize: 14,
          fontWeight: 400,
          color: '#6F6F6F',
          padding: [0, 4, 0, 0],
        },
        splitLine: { lineStyle: { type: 'dashed', color: '#DFDFDF' } },
        axisLine: {
          show: true,
          lineStyle: { type: 'solid', color: '#A8A8A8' },
        },
      },
      yAxis: {
        type: 'category',
        data: [t('IdleTimeN'), t('DrivingTimeN')],
        axisTick: { show: false },
        axisLine: { show: false, lineStyle: { type: 'dashed' } },
      },
      series: [
        {
          type: 'bar',
          label: {
            show: false,
          },
          emphasis: {
            focus: 'series',
          },
          barWidth: 27,
          data: [
            {
              value: drivingTime,
              itemStyle: {
                borderRadius,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#010542',
                  },
                  {
                    offset: 1,
                    color: '#C3C5DF',
                  },
                ]),
              },
            },
            {
              value: idleTime,
              itemStyle: {
                borderRadius,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#010542',
                  },
                  {
                    offset: 1,
                    color: '#B9B9B9',
                  },
                ]),
              },
            },
          ],
        },
        {
          type: 'bar',
          barWidth: 27,
          label: {
            show: false,
          },
          emphasis: {
            focus: 'series',
          },
          lineStyle: {
            width: 27,
          },
          data: [
            {
              value: drivingTime,
              itemStyle: {
                borderRadius,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#FF5900',
                  },
                  {
                    offset: 1,
                    color: '#FFDECC',
                  },
                ]),
              },
            },
            {
              value: idleTime,
              itemStyle: {
                borderRadius,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#FF5900',
                  },
                  {
                    offset: 1,
                    color: '#FFDECC',
                  },
                ]),
              },
            },
          ],
        },
      ],
    });
  }, [drivingCommon]);

  return (
    <ECharts
      option={drivingTimeOption}
      style={{ width: '400px', height: '180px' }}
    />
  );
};

export default DailyDrivingTime;
