import { useTranslation } from 'react-i18next';
import {
  CustomTable,
  CustomTableBody,
  CustomTableCell,
  CustomTableHead,
  CustomTableHeader,
  CustomTableProps,
  CustomTableRow,
} from '@/Common/Components/common/CustomTable';
import { ColumnDef, flexRender } from '@tanstack/react-table';
import { v4 } from 'uuid';
import { useTable } from '@/Common/Components/hooks/useTable.tsx';
import { useEffect, useState } from 'react';
import { EquipmentType } from '@/types/EquipmentType';

export interface DailyDrivingTimeTableProps {
  drivingCommon: EquipmentType.DailyDrivingCommonInfo | null | undefined;
}

/** 일별 운행 시간 (테이블) */

const DailyDrivingTimeTable = ({
  drivingCommon,
  onClickRow,
  id,
}: DailyDrivingTimeTableProps &
  CustomTableProps<EquipmentType.DailyDrivingTimeTableColumnProps>) => {
  const { t } = useTranslation();
  const [drivingTimeTableData, setDrivingTimeTableData] = useState<
    EquipmentType.DailyDrivingTimeTableColumnProps[]
  >([]);

  const columns: ColumnDef<EquipmentType.DailyDrivingTimeTableColumnProps>[] = [
    {
      header: () => <div>{t('Category')}</div>,
      accessorKey: 'division',
      cell: ({ row }) => <div>{row.original.division}</div>,
    },
    {
      header: () => <div>{t('OperationTime')}</div>,
      accessorKey: 'daily',
      cell: ({ row }) => <div>{row.original.daily}</div>,
    },
    {
      header: () => <div>{t('Average')}</div>,
      accessorKey: 'average',
      cell: ({ row }) => <div>{row.original.average}</div>,
    },
  ];

  useEffect(() => {
    if (drivingCommon) {
      setDrivingTimeTableData([
        {
          division: 'Driving Time',
          daily: drivingCommon.drivingTime,
          average: drivingCommon.avgDrivingTime,
        },
        {
          division: 'Idle Time',
          daily: drivingCommon.idleTime,
          average: drivingCommon.avgIdleTime,
        },
      ]);
    }
  }, [drivingCommon]);

  const { table } = useTable<EquipmentType.DailyDrivingTimeTableColumnProps>(
    drivingTimeTableData,
    columns,
  );

  return (
    <div className="border-t border-x border-gray-6 rounded-md">
      <CustomTable>
        <CustomTableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <CustomTableRow key={headerGroup.id} className={''}>
              {headerGroup.headers.map((header) => (
                <CustomTableHead
                  key={header.id}
                  style={{
                    whiteSpace: 'nowrap',
                    // width: header.column.getSize(),
                  }}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </CustomTableHead>
              ))}
            </CustomTableRow>
          ))}
        </CustomTableHeader>
        <CustomTableBody>
          {table?.getCoreRowModel().rows.length ? (
            table?.getCoreRowModel().rows.map((row) => (
              <CustomTableRow
                onClick={() => {
                  if (id) {
                    if (onClickRow) {
                      const original = row.original;
                      onClickRow(original[id]);
                    }
                  }
                }}
                key={v4()}
              >
                {row
                  ?.getAllCells()
                  .map((cell) => (
                    <CustomTableCell key={v4()}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </CustomTableCell>
                  ))}
              </CustomTableRow>
            ))
          ) : (
            <CustomTableRow>
              <CustomTableCell colSpan={columns.length}>
                {t('NoDataAvailable')}
              </CustomTableCell>
            </CustomTableRow>
          )}
        </CustomTableBody>
      </CustomTable>
    </div>
  );
};

export default DailyDrivingTimeTable;
