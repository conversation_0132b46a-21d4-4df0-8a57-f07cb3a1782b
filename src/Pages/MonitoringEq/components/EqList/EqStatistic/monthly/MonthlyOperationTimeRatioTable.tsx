import { useTranslation } from 'react-i18next';
import {
  CustomTable,
  CustomTableBody,
  CustomTableCell,
  CustomTableHead,
  CustomTableHeader,
  CustomTableProps,
  CustomTableRow,
} from '@/Common/Components/common/CustomTable';
import { ColumnDef, flexRender } from '@tanstack/react-table';
import { v4 } from 'uuid';
import { useTable } from '@/Common/Components/hooks/useTable.tsx';

export type MonthlyOperationTimeRatioColumnsProps = {
  division: string;
  daily: number | string;
};

export const MonthlyOperationTimeRatioColumns: ColumnDef<MonthlyOperationTimeRatioColumnsProps>[] =
  [
    {
      header: () => {
        const { t } = useTranslation();
        return <div>{t('Category')}</div>;
      },
      accessorKey: 'division',
      cell: ({ row }) => <div>{row.original.division}</div>,
    },
    {
      header: () => {
        const { t } = useTranslation();
        return <div>{t('Ratio')}</div>;
      },
      accessorKey: 'daily',
      cell: ({ row }) => <div>{row.original.daily}</div>,
    },
  ];

const MonthlyOperationTimeRatioTable = ({
  data = [],
  onClickRow,
  id,
}: CustomTableProps<MonthlyOperationTimeRatioColumnsProps>) => {
  const { t } = useTranslation();

  const { table } = useTable<MonthlyOperationTimeRatioColumnsProps>(
    data,
    MonthlyOperationTimeRatioColumns,
  );

  return (
    <div className="mt-[35px]">
      <CustomTable>
        <CustomTableHeader>
          {table.getHeaderGroups().map((headerGroup) => (
            <CustomTableRow key={headerGroup.id} className={''}>
              {headerGroup.headers.map((header) => (
                <CustomTableHead
                  key={header.id}
                  style={{
                    whiteSpace: 'nowrap',
                  }}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                </CustomTableHead>
              ))}
            </CustomTableRow>
          ))}
        </CustomTableHeader>
        <CustomTableBody>
          {table?.getCoreRowModel().rows.length ? (
            table?.getCoreRowModel().rows.map((row) => (
              <CustomTableRow
                onClick={() => {
                  if (id) {
                    if (onClickRow) {
                      const original = row.original;
                      onClickRow(original[id]);
                    }
                  }
                }}
                key={v4()}
              >
                {row
                  ?.getAllCells()
                  .map((cell) => (
                    <CustomTableCell key={v4()}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </CustomTableCell>
                  ))}
              </CustomTableRow>
            ))
          ) : (
            <CustomTableRow>
              <CustomTableCell
                colSpan={MonthlyOperationTimeRatioColumns.length}
              >
                {t('NoDataAvailable')}
              </CustomTableCell>
            </CustomTableRow>
          )}
        </CustomTableBody>
      </CustomTable>
    </div>
  );
};

export default MonthlyOperationTimeRatioTable;
