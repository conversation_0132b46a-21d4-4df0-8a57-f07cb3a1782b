import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import ECharts from 'echarts-for-react';
import * as echarts from 'echarts';

export interface MonthlyOperationTimeRatioGraphProps {
  drivingPercent?: number; // 0~100
  idlePercent?: number; // 0~100
  className?: string;
}

const MonthlyOperationTimeRatioGraph = ({
  drivingPercent = 100,
  idlePercent = 15,
  className = '',
}: MonthlyOperationTimeRatioGraphProps) => {
  const { t } = useTranslation();

  const [option, setOption] = useState({});

  useEffect(() => {
    setOption({
      textStyle: {
        fontFamily: 'Pretendard',
      },
      tooltip: {
        show: false,
      },
      grid: {
        left: '0%',
        right: '8%',
        top: '5%',
        bottom: '0%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        min: 0,
        max: 100,
        splitNumber: 5,
        name: '%',
        nameTextStyle: {
          fontSize: 13,
          fontWeight: 400,
          color: '#6F6F6F',
          padding: [0, 0, 0, 8],
        },
        axisLine: { show: false },
        axisLabel: { fontSize: 13, color: '#6F6F6F', fontWeight: 300 },
        splitLine: {
          show: true,
          lineStyle: { type: 'dotted', color: '#E6E6E6' },
        },
      },
      yAxis: {
        type: 'category',
        data: [
          t('IdleTimeN') || 'Idle Time',
          t('DrivingTimeN') || 'Driving Time',
        ],
        axisTick: { show: false },
        axisLine: { show: false },
        axisLabel: {
          color: '#6F6F6F',
          fontSize: 13,
          fontWeight: 400,
          align: 'right',
          margin: 8,
        },
      },
      series: [
        {
          type: 'bar',
          barWidth: 18,
          data: [
            {
              value: drivingPercent,
              itemStyle: {
                borderRadius: [0, 12, 12, 0],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: '#1C76E0' },
                  { offset: 1, color: '#E8F1FC' },
                ]),
              },
            },
            {
              value: idlePercent,
              itemStyle: {
                borderRadius: [0, 12, 12, 0],
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: '#1C76E0' },
                  { offset: 1, color: '#E8F1FC' },
                ]),
              },
            },
          ],
          label: { show: false },
        },
      ],
      animation: false,
    });
  }, [drivingPercent, idlePercent, t]);

  return (
    <div className={className}>
      <ECharts option={option} style={{ height: 180, width: '100%' }} />
    </div>
  );
};

export default MonthlyOperationTimeRatioGraph;
