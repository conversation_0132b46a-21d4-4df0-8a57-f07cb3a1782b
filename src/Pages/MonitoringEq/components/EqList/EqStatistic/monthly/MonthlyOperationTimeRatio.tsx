import { useTranslation } from 'react-i18next';
import MonthlyOperationTimeRatioGraph from '@/Pages/MonitoringEq/components/EqList/EqStatistic/monthly/MonthlyOperationTimeRatioGraph';
import { useEffect, useState } from 'react';
import MonthlyOperationTimeRatioTable from '@/Pages/MonitoringEq/components/EqList/EqStatistic/monthly/MonthlyOperationTimeRatioTable';

// Define the interface for table data
interface MonthlyOperationTimeRatioColumnProps {
  division: string;
  daily: string;
}

interface MonthlyOperationTimeRatioProps {
  className?: string;
  isMonthly?: boolean;
  yearMonth?: string;
  workAnalyicsInfo: {
    engRunHour: string;
    drivingHour: string;
    travelHour: string;
    idleHour: string;
    stdModeHour: string;
    pwrModeHour: string;
  } | null;
  type: string;
  setType: (value: string) => void;
}

const MonthlyOperationTimeRatio = ({
  className,
  workAnalyicsInfo,
  type,
}: MonthlyOperationTimeRatioProps) => {
  const { t } = useTranslation();

  const [engineActiveTableData, setEngineActiveTableData] = useState<
    MonthlyOperationTimeRatioColumnProps[]
  >([]);

  useEffect(() => {
    if (workAnalyicsInfo) {
      setEngineActiveTableData([
        {
          division: t('DrivingTime'),
          daily: workAnalyicsInfo.travelHour,
        },
        {
          division: t('IdleTime'),
          daily: workAnalyicsInfo.idleHour,
        },
      ]);
    }
  }, [workAnalyicsInfo]);

  return (
    <div className={className}>
      <h2 className="subtitle4">{t('OperationTimeRatio')}</h2>
      {type === 'graph' ? (
        <MonthlyOperationTimeRatioGraph
          drivingPercent={Number(workAnalyicsInfo?.drivingHour) || 0}
          idlePercent={Number(workAnalyicsInfo?.idleHour) || 0}
        />
      ) : (
        <MonthlyOperationTimeRatioTable data={engineActiveTableData} />
      )}
    </div>
  );
};

export default MonthlyOperationTimeRatio;
