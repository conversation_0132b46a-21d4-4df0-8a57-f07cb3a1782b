import { useTranslation } from 'react-i18next';
import ECharts from 'echarts-for-react';
import { useEffect, useState } from 'react';

interface TemperatureItem {
  itemCode: string;
  temp30: number;
  temp30_60: number;
  temp60_70: number;
  temp70_80: number;
  temp80_90: number;
  temp90_100: number;
  temp100: number;
}

interface MonthlyTemperatureDistributionProps {
  temperature: TemperatureItem[];
  className?: string;
}

const MonthlyTemperatureDistribution = ({
  temperature,
  className = '',
}: MonthlyTemperatureDistributionProps) => {
  const { t } = useTranslation();

  const [temperatureOption, setTemperatureOption] = useState({});

  useEffect(() => {
    // 항목별 데이터 준비
    const lTemp = temperature.find((item) => item.itemCode === 'L');
    const cTemp = temperature.find((item) => item.itemCode === 'C');
    const tTemp = temperature.find((item) => item.itemCode === 'T');

    // 범례와 색상을 정의
    const legends = [
      { name: t('HydraulicOil'), color: '#1C76E0' },
      { name: t('Coolant'), color: '#9093B5' },
      { name: t('TransmissionOil'), color: '#EAB522' },
    ];

    const xAxisData = [0, 20, 40, 60, 80, 100];

    setTemperatureOption({
      textStyle: {
        fontFamily: 'Pretendard',
      },
      tooltip: {
        trigger: 'axis',
        valueFormatter: (value: number) => `${value}%`,
      },
      legend: {
        show: true,
        right: 40,
        top: 10,
        icon: 'circle',
        itemWidth: 8,
        itemHeight: 8,
        data: legends.map((l) => l.name),
        textStyle: {
          fontSize: 13,
        },
      },
      grid: {
        left: '0%',
        right: '4%',
        bottom: '0%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        name: '℃',
        nameTextStyle: {
          fontSize: 14,
          fontWeight: 400,
          color: '#888',
          padding: [15, 0, 0, 0],
        },
        axisLabel: { fontSize: 13, color: '#6F6F6F' },
        axisLine: { show: true, lineStyle: { color: '#ccc' } },
        axisTick: { show: false },
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        interval: 50,
        name: '%',
        nameTextStyle: {
          fontSize: 13,
          color: '#888',
          padding: [0, 15, 0, 0],
        },
        axisLabel: { fontSize: 13, color: '#6F6F6F' },
        splitLine: {
          show: true,
          lineStyle: { type: 'dotted', color: '#E0E0E0' },
        },
        axisLine: { show: false },
      },
      series: [
        {
          name: t('HydraulicOil'),
          type: 'bar',
          barWidth: 12,
          data: lTemp
            ? [
                lTemp.temp30,
                lTemp.temp30_60,
                lTemp.temp60_70,
                lTemp.temp70_80,
                lTemp.temp80_90,
                lTemp.temp100,
              ]
            : [0, 0, 0, 0, 0, 0],
          itemStyle: { color: '#1C76E0' },
        },
        {
          name: t('Coolant'),
          type: 'bar',
          barWidth: 12,
          data: cTemp
            ? [
                cTemp.temp30,
                cTemp.temp30_60,
                cTemp.temp60_70,
                cTemp.temp70_80,
                cTemp.temp80_90,
                cTemp.temp100,
              ]
            : [0, 0, 0, 0, 0, 0],
          itemStyle: { color: '#9093B5' },
        },
        {
          name: t('TransmissionOil'),
          type: 'bar',
          barWidth: 12,
          data: tTemp
            ? [
                tTemp.temp30,
                tTemp.temp30_60,
                tTemp.temp60_70,
                tTemp.temp70_80,
                tTemp.temp80_90,
                tTemp.temp100,
              ]
            : [0, 0, 0, 0, 0, 0],
          itemStyle: { color: '#EAB522' },
        },
      ],
    });
  }, [temperature, t]);

  return (
    <div className={className}>
      <h2 className="mb-4 subtitle4">{t('TemperatureDistribution')}</h2>
      <ECharts
        option={temperatureOption}
        style={{ width: '100%', height: 220 }}
      />
    </div>
  );
};

export default MonthlyTemperatureDistribution;
