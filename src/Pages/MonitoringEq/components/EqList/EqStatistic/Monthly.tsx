import { useEffect, useState } from 'react';
import { Tabs } from '@radix-ui/themes';
import BasicCard from '@/Common/Components/common/BasicCard';
import { MonthlyDrivingDistance } from '@/Pages/MonitoringEq/components/EqList/EqStatistic/monthly/MonthlyDrivingDistance';
import MonthlyFuelConsumption from '@/Pages/MonitoringEq/components/EqList/EqStatistic/monthly/MonthlyFuelConsumption';
import MonthlyOperationTimeRatio from '@/Pages/MonitoringEq/components/EqList/EqStatistic/monthly/MonthlyOperationTimeRatio';
import MonthlyDrivingPattern from '@/Pages/MonitoringEq/components/EqList/EqStatistic/monthly/MonthlyDrivingPattern';
import MonthlyTemperatureDistribution from '@/Pages/MonitoringEq/components/EqList/EqStatistic/monthly/MonthlyTemperatureDistribution';
import InfoTypeRadio from '@/Common/Components/common/InfoTypeRadio';
import { MonthlyCalendar } from '@/Common/Calendar.tsx';
import { DemoTest } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { generateEquipmentMonthlyInfo } from '@/helpers/equipmentDetailDataGenerator';
import { EquipmentType } from '@/types/EquipmentType';

const Monthly = ({
  equipmentId,
  equipmentBasicInfo,
  controllerParams,
  captureRef,
}: {
  equipmentId: string;
  equipmentBasicInfo: EquipmentType.BasicInfo | null | undefined;
  captureRef: React.RefObject<HTMLDivElement>;
  controllerParams: Record<string, string | number | undefined>;
}) => {
  const [type, setType] = useState('graph');

  const [eqMonthlyInfoParams, setEqMonthlyInfoParams] = useState({
    equipmentId: '',
    date: '',
  });

  /** useQuery */

  // 월별 통계 조회
  const { data: eqMonthlyInfo } = useQuery<EquipmentType.MonthlyInfo | null>({
    queryKey: ['equipment/monthly/info', eqMonthlyInfoParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        const isElectric = equipmentBasicInfo?.powerType === 'E';
        return generateEquipmentMonthlyInfo(isElectric);
      } else {
        try {
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    initialData: null,
    enabled:
      eqMonthlyInfoParams.equipmentId.length > 0 &&
      eqMonthlyInfoParams.date.length > 0,
  });

  //** useEffect */

  // equipmentId 변경되면 검색
  useEffect(() => {
    if (equipmentId && controllerParams.month) {
      setEqMonthlyInfoParams({
        equipmentId: equipmentId,
        date: controllerParams.month as string,
      });
    }
  }, [equipmentId, controllerParams.month]);

  return (
    <Tabs.Content value="Monthly">
      <div className="mb-[10px] f-je">
        <InfoTypeRadio setType={setType} />
      </div>

      {/* 운행일, 총 운행 시간, 월 평균 운행 시간 */}
      <div className="f-c gap-[10px]">
        <BasicCard
          title="OperationDate"
          value={eqMonthlyInfo?.common.workingDays ?? 0}
          unit="Days"
        />
        <BasicCard
          title="TotalOperationTime"
          value={eqMonthlyInfo?.common.totalOperatingTime.hours ?? 0}
          unit="hr"
          subValue={eqMonthlyInfo?.common.totalOperatingTime.mins ?? 0}
          subUnit="min"
        />
        <BasicCard
          title="AvgDailyTime"
          value={eqMonthlyInfo?.common.avgDailyOperatingTime.hours ?? 0}
          unit="hr"
          subValue={eqMonthlyInfo?.common.avgDailyOperatingTime.mins ?? 0}
          subUnit="min"
        />
      </div>

      {/* 운행 시간 통계 */}
      <div className="mt-[10px] p-p-div bg-w-br-div grid grid-cols-5 grid-rows-subgrid gap-[10px]">
        {type === 'graph' ? (
          <MonthlyDrivingDistance
            engine={
              eqMonthlyInfo?.engine.map((item) => ({
                day: item.day,
                drivingTime: { hours: item.travelHour.hours },
                idlingTime: { hours: item.idleHour.hours },
              })) ?? []
            }
            className="col-span-5 row-span-1"
          />
        ) : (
          <MonthlyCalendar
            yearMonth={eqMonthlyInfo?.yearMonth ?? '0'}
            engine={eqMonthlyInfo?.engine ?? []}
            className="col-span-5 row-span-1"
          />
        )}

        {/* 운행 시간 비율 */}
        <MonthlyOperationTimeRatio
          isMonthly={true}
          yearMonth={eqMonthlyInfo?.yearMonth}
          workAnalyicsInfo={eqMonthlyInfo?.common ?? null}
          type={type}
          setType={setType}
          className="col-span-2 row-span-1"
        />

        {/* 연료 사용량 */}
        <MonthlyFuelConsumption
          isElectric={eqMonthlyInfo?.isElectric ?? false}
          fuel={eqMonthlyInfo?.fuel ?? null}
          battery={eqMonthlyInfo?.battery ?? null}
          className="col-span-3 row-span-2"
          type={type}
        />

        {/* 운행 패턴 */}
        <MonthlyDrivingPattern
          className="col-span-2 row-span-1"
          values={eqMonthlyInfo?.drivingPattern ?? null}
        />

        {/* 온도 분포 */}
        <MonthlyTemperatureDistribution
          temperature={eqMonthlyInfo?.temperature ?? []}
          className="col-span-5 row-span-1"
        />
      </div>
    </Tabs.Content>
  );
};

export default Monthly;
