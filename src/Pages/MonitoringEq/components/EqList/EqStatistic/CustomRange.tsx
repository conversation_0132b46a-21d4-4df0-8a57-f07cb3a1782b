import { useEffect, useState } from 'react';
import { Tabs } from '@radix-ui/themes';
import BasicCard from '@/Common/Components/common/BasicCard';
import { RangeDrivingDistance } from '@/Pages/MonitoringEq/components/EqList/EqStatistic/range/RangeDrivingDistance';
import RangeFuelConsumption from '@/Pages/MonitoringEq/components/EqList/EqStatistic/range/RangeFuelConsumption';
import RangeOperationTimeRatio from '@/Pages/MonitoringEq/components/EqList/EqStatistic/range/RangeOperationTimeRatio';
import RangeDrivingPattern from '@/Pages/MonitoringEq/components/EqList/EqStatistic/range/RangeDrivingPattern';
import RangeTemperatureDistribution from '@/Pages/MonitoringEq/components/EqList/EqStatistic/range/RangeTemperatureDistribution';
import InfoTypeRadio from '@/Common/Components/common/InfoTypeRadio';
import { RangeCalendar } from '@/Common/Calendar.tsx';
import { DemoTest } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { generateEquipmentCustomRange } from '@/helpers/equipmentDetailDataGenerator';
import { EquipmentType } from '@/types/EquipmentType';

const CustomRange = ({
  equipmentId,
  equipmentBasicInfo,
  controllerParams,
  calendarRange, // ★ 추가
}: {
  equipmentId: string;
  equipmentBasicInfo: EquipmentType.BasicInfo | null | undefined;
  captureRef: React.RefObject<HTMLDivElement>;
  controllerParams: Record<string, string | number | undefined>;
  calendarRange?: { start: string; end: string }; // ★ 추가
}) => {
  const [type, setType] = useState('graph');

  const [eqPeriodicInfoParams, setEqPeriodicInfoParams] = useState({
    equipmentId: '',
    from: '',
    to: '',
  });

  /** useQuery */
  const { data: eqPeriodicInfo } = useQuery<EquipmentType.PeriodicInfo | null>({
    queryKey: ['equipment/monthly/info', eqPeriodicInfoParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        const isElectric = equipmentBasicInfo?.powerType === 'E';
        return generateEquipmentCustomRange(isElectric);
      } else {
        try {
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    initialData: null,
    enabled:
      eqPeriodicInfoParams.equipmentId.length > 0 &&
      eqPeriodicInfoParams.from.length > 0 &&
      eqPeriodicInfoParams.to.length > 0,
  });

  // equipmentId 변경되면 검색
  useEffect(() => {
    if (equipmentId && controllerParams.from && controllerParams.to) {
      setEqPeriodicInfoParams({
        equipmentId: equipmentId,
        from: controllerParams.from as string,
        to: controllerParams.to as string,
      });
    }
  }, [equipmentId, controllerParams.from, controllerParams.to]);

  return (
    <Tabs.Content value="CustomRange">
      <div className="mb-[10px] f-je">
        <InfoTypeRadio setType={setType} />
      </div>

      {/* 운행일, 총 운행시간, 일 평균 운행시간 */}
      <div className="f-c gap-[10px]">
        <BasicCard
          title="OperationDate"
          value={eqPeriodicInfo?.common.workingDays ?? 0}
          unit="Days"
        />
        <BasicCard
          title="TotalOperationTime"
          value={eqPeriodicInfo?.common.totalOperatingTime.hours ?? 0}
          unit="hr"
          subValue={eqPeriodicInfo?.common.totalOperatingTime.mins ?? 0}
          subUnit="min"
        />
        <BasicCard
          title="AvgDailyTime"
          value={eqPeriodicInfo?.common.avgDailyOperatingTime.hours ?? 0}
          unit="hr"
          subValue={eqPeriodicInfo?.common.avgDailyOperatingTime.mins ?? 0}
          subUnit="min"
        />
      </div>

      {/* 운행 통계 */}
      <div className="mt-[10px] p-p-div bg-w-br-div grid grid-cols-5 grid-rows-subgrid gap-[10px]">
        {type === 'graph' ? (
          <RangeDrivingDistance
            engine={
              eqPeriodicInfo?.engine.map((item) => ({
                day: item.day,
                drivingTime: { hours: item.travelHour.hours },
                idlingTime: { hours: item.idleHour.hours },
              })) ?? []
            }
            className="col-span-5 row-span-1"
          />
        ) : (
          <RangeCalendar
            yearMonth={eqPeriodicInfo?.fromDate?.slice(0, 7) ?? '0'}
            engine={eqPeriodicInfo?.engine ?? []}
            className="col-span-5 row-span-1"
            visibleRange={calendarRange}
          />
        )}

        {/* 운행 시간 비율 */}
        <RangeOperationTimeRatio
          isRange={true}
          yearMonth={eqPeriodicInfo?.fromDate}
          workAnalyicsInfo={eqPeriodicInfo?.common ?? null}
          type={type}
          setType={setType}
          className="col-span-2 row-span-1"
        />

        {/* 연료 사용량 */}
        <RangeFuelConsumption
          isElectric={eqPeriodicInfo?.isElectric ?? false}
          fuel={eqPeriodicInfo?.fuel ?? null}
          battery={eqPeriodicInfo?.battery ?? null}
          className="col-span-3 row-span-2"
          type={type}
        />

        {/* 운행 패턴 */}
        <RangeDrivingPattern
          className="col-span-2 row-span-1"
          values={eqPeriodicInfo?.drivingPattern ?? null}
        />

        {/* 온도 분포 */}
        <RangeTemperatureDistribution
          temperature={eqPeriodicInfo?.temperature ?? []}
          className="col-span-5 row-span-1"
        />
      </div>
    </Tabs.Content>
  );
};

export default CustomRange;
