import { useTranslation } from 'react-i18next';
import ECharts from 'echarts-for-react';
import { useEffect, useState } from 'react';
import * as echarts from 'echarts';

interface RangeDrivingDistanceProps {
  engine: {
    day: number;
    drivingTime: { hours: number };
    idlingTime: { hours: number };
  }[];
  className?: string;
}

export const RangeDrivingDistance = ({
  engine,
  className,
}: RangeDrivingDistanceProps) => {
  const { t } = useTranslation();
  const [RangeOption, setRangeOption] = useState({});

  useEffect(() => {
    const days = Array.from({ length: 31 }, (_, i) => i + 1);
    const drivingArr = Array(31).fill(0);
    const idlingArr = Array(31).fill(0);

    engine.forEach((item) => {
      const idx = item.day - 1;
      if (idx >= 0 && idx < 31) {
        drivingArr[idx] = item.drivingTime.hours;
        idlingArr[idx] = item.idlingTime.hours;
      }
    });

    setRangeOption({
      textStyle: {
        fontFamily: 'Pretendard',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        valueFormatter: (value: number) => `${value} ${t('HoursS')}`,
      },
      legend: {
        show: true,
        data: [t('DrivingTime'), t('IdlingTime')],
        right: 40,
        top: 10,
        icon: 'circle',
        itemWidth: 8,
        itemHeight: 8,
        borderRdius: 4,
      },
      grid: {
        left: '0%',
        right: '8%',
        bottom: '0%',
        top: '30%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: days,
        axisLabel: { fontSize: 12 },
        name: t('Day'),
        nameLocation: 'end',
        nameTextStyle: {
          fontWeight: 'normal',
          fontSize: 13,
          padding: [4, 0, 0, 0],
        },
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 8,
        name: t('Time'),
        nameTextStyle: {
          fontWeight: 'normal',
          fontSize: 13,
          padding: [0, 0, 4, 0],
        },
        axisLabel: { fontSize: 13 },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            width: 1,
          },
        },
      },
      series: [
        {
          name: t('DrivingTime'),
          type: 'bar',
          stack: 'total',
          data: drivingArr,
          barWidth: 8,
          barBorderRadius: [4, 4, 0, 0],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              { offset: 0, color: '#010542' },
              { offset: 1, color: '#B6B6DC' },
            ]),
          },
        },
        {
          name: t('IdlingTime'),
          type: 'bar',
          stack: 'total',
          data: idlingArr,
          barWidth: 8,
          barBorderRadius: [4, 4, 0, 0],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              { offset: 0, color: '#FF5900' },
              { offset: 1, color: '#FFD5B5' },
            ]),
          },
        },
      ],
    });
  }, [engine, t]);

  return (
    <div className={`${className} py-5 px-6`}>
      <h2 className="subtitle4">{t('DrivingDistance')}</h2>
      <ECharts option={RangeOption} style={{ height: 240, width: '100%' }} />
    </div>
  );
};
