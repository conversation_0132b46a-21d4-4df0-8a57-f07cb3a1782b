import { useTranslation } from 'react-i18next';
import RangeOperationTimeRatioGraph from '@/Pages/MonitoringEq/components/EqList/EqStatistic/range/RangeOperationTimeRatioGraph';
import { useEffect, useState } from 'react';
import RangeOperationTimeRatioTable from '@/Pages/MonitoringEq/components/EqList/EqStatistic/range/RangeOperationTimeRatioTable';

interface RangeOperationTimeRatioProps {
  className?: string;
  isRange?: boolean;
  yearMonth?: string;
  workAnalyicsInfo: {
    engRunHour: string;
    drivingHour: string;
    travelHour: string;
    idleHour: string;
    stdModeHour: string;
    pwrModeHour: string;
  } | null;
  type: string;
  setType: (value: string) => void;
}

interface RangeOperationTimeRatioData {
  division: string;
  daily: string;
}

const RangeOperationTimeRatio = ({
  className,
  workAnalyicsInfo,
  type,
}: RangeOperationTimeRatioProps) => {
  const { t } = useTranslation();

  const [RangeOperationTimeRatioData, setRangeOperationTimeRatioData] =
    useState<RangeOperationTimeRatioData[]>([]);

  useEffect(() => {
    if (workAnalyicsInfo) {
      setRangeOperationTimeRatioData([
        {
          division: t('DrivingTime'),
          daily: workAnalyicsInfo.travelHour,
        },
        {
          division: t('IdleTime'),
          daily: workAnalyicsInfo.idleHour,
        },
      ]);
    }
  }, [workAnalyicsInfo]);

  return (
    <div className={className}>
      <h2 className="subtitle4">{t('OperationTimeRatio')}</h2>
      {type === 'graph' ? (
        <RangeOperationTimeRatioGraph
          drivingPercent={Number(workAnalyicsInfo?.drivingHour) || 0}
          idlePercent={Number(workAnalyicsInfo?.idleHour) || 0}
        />
      ) : (
        <RangeOperationTimeRatioTable data={RangeOperationTimeRatioData} />
      )}
    </div>
  );
};

export default RangeOperationTimeRatio;
