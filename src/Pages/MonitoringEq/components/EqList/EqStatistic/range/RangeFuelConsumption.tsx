import { useTranslation } from 'react-i18next';
import ECharts from 'echarts-for-react';
import { useEffect, useState } from 'react';
import * as echarts from 'echarts/core';
import dayjs from 'dayjs';

interface RangeFuelConsumptionProps {
  className?: string;
  yearMonth?: string;
  isElectric: boolean;
  fuel?: {
    fuelUsed: number;
    fuelUsedAvg: number;
    fuelRate: number;
    logs: { date: number; usage: number }[];
  } | null;
  battery?: {
    batteryUsed: number;
    batteryUsedAvg: number;
    batteryRate: number;
    logs: { date: number; usage: number }[];
  } | null;
  type: string; // 'graph' or 'table'
}

interface RangeFuelConsumptionTableProps {
  yearMonth?: string;
  isElectric: boolean;
  values?: {
    date: number;
    usage: number;
  }[];
}

function RangeFuelConsumptionTable({
  yearMonth,
  isElectric,
  values,
}: RangeFuelConsumptionTableProps) {
  const days =
    yearMonth && dayjs(yearMonth).isValid()
      ? Array.from({ length: dayjs(yearMonth).daysInMonth() }, (_, i) => i + 1)
      : Array.from({ length: 31 }, (_, i) => i + 1);

  const month =
    yearMonth && dayjs(yearMonth).isValid()
      ? dayjs(yearMonth).month() + 1
      : dayjs().month() + 1;

  const itemsPerRow = 9; // 1줄에 8개씩
  const rows = Math.ceil(days.length / itemsPerRow);
  const lastRowStartIdx = (rows - 1) * itemsPerRow;

  return (
    <div className="flex flex-wrap">
      {days.map((item, idx) => {
        const isLastRow = idx >= lastRowStartIdx;
        return (
          <div
            key={item}
            className={
              `w-[72.5px] py-3 f-c-c flex-col gap-1 ` +
              (isLastRow ? '' : ' border-b border-gray-6')
            }
          >
            <div className="caption3">
              {month}.{item}
            </div>
            <div className="body3 text-gray-12">
              {`${values?.find((value) => value.date === item)?.usage?.toFixed(0) ?? 0}`}
              {isElectric ? '%' : 'L'}
            </div>
          </div>
        );
      })}
    </div>
  );
}

const RangeFuelConsumption = ({
  className = '',
  yearMonth,
  isElectric = false,
  fuel,
  battery,
  type = 'graph', // <- 기본값은 'graph'
}: RangeFuelConsumptionProps) => {
  const { t } = useTranslation();
  const [RangeOption, setRangeOption] = useState({});

  useEffect(() => {
    const days = Array.from({ length: 31 }, (_, i) => i + 1);
    const bars = Array(31).fill(0);
    for (let i = 0; i < bars.length; ++i) {
      bars[i] = isElectric
        ? (battery?.logs[i]?.usage ?? 0)
        : (fuel?.logs[i]?.usage ?? 0);
    }

    setRangeOption({
      textStyle: { fontFamily: 'Pretendard' },
      tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
      grid: {
        left: '0%',
        right: '8%',
        bottom: '0%',
        top: '13%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: days,
        name: t('Day') || 'Day',
        nameLocation: 'end',
        nameTextStyle: {
          fontWeight: 'normal',
          fontSize: 13,
          padding: [0, 0, 0, 0],
        },
        axisLabel: { fontSize: 12 },
        boundaryGap: true,
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 30,
        name: 'L',
        nameTextStyle: {
          fontWeight: 'normal',
          fontSize: 13,
          padding: [0, 0, 0, 0],
        },
        axisLabel: { fontSize: 13 },
        splitLine: { show: true, lineStyle: { type: 'dashed', width: 1 } },
      },
      series: [
        {
          type: 'bar',
          data: bars,
          barWidth: 8,
          barBorderRadius: [4, 4, 0, 0],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              { offset: 0, color: '#6F6F6F' },
              { offset: 1, color: '#DFDFDF' },
            ]),
          },
        },
      ],
    });
  }, [isElectric, fuel, battery, t]);

  return (
    <div className={`${className} py-5 px-6 f-c-b flex-col`}>
      <h2 className="w-full mb-4 subtitle4 text-left">
        {t('FuelConsumption')}
      </h2>
      {type === 'graph' ? (
        <div className="w-full">
          <ECharts
            option={RangeOption}
            style={{ height: 240, width: '100%' }}
          />
        </div>
      ) : (
        <RangeFuelConsumptionTable
          yearMonth={yearMonth}
          isElectric={isElectric}
          values={isElectric ? battery?.logs : fuel?.logs}
        />
      )}

      <div className="w-full mt-10 p-5 text-divider2 bg-gray-1 rounded-md [&_h3]:w-[140px] [&_h3]:f-js [&_h3]:body3 [&_p]:subtitle2 [&_p]:f-je">
        <div>
          <h3>{t('TotalFuelConsumption')}</h3>
          <p>
            {isElectric
              ? (battery?.batteryUsed ?? '-')
              : (fuel?.fuelUsed ?? '-')}
            {t('L')}
          </p>
        </div>
        <div>
          <h3>{t('AverageDailyFuelConsumption')}</h3>
          <p>
            {isElectric
              ? (battery?.batteryUsedAvg ?? '-')
              : (fuel?.fuelUsedAvg ?? '-')}
            {t('L')}
          </p>
        </div>
        <div>
          <h3>{t('AverageFuelEfficiency')}</h3>
          <p>
            {isElectric
              ? (battery?.batteryRate ?? '-')
              : (fuel?.fuelRate ?? '-')}
            {t('Lh')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default RangeFuelConsumption;
