import { useTranslation } from 'react-i18next';

interface RangeDrivingPatternProps {
  className?: string;
  title?: string;
  values: {
    overspeedCount: number; //과속 횟수
    averageSpeed: number; //평균 속도
    harshBrakingCount: number; //급제동 횟수
    harshAccelerationCount: number; //급가속 횟수
  } | null;
}

export default function RangeDrivingPattern({
  className,
  title,
  values,
}: RangeDrivingPatternProps) {
  const { t } = useTranslation();

  return (
    <div className={`${className} w-full`}>
      <div className="mb-5 subtitle4">
        {title ? t(title) : t('DrivingPattern')}
      </div>
      <div
        className="
          flex flex-wrap
          [&>div]:border-r
          [&>div:nth-child(2)]:border-r-0
          [&>div:nth-child(4)]:border-r-0
          [&>div]:border-b
          [&>div:nth-child(3)]:border-b-0
          [&>div:nth-child(4)]:border-b-0
          [&>div]:border-gray-6
          [&>div]:w-1/2
          [&>div]:py-2
          [&>div]:px-4
          [&>div]:f-c-b
          [&_h3]:body3
          [&_p]:subtitle2
        "
      >
        <div>
          <h3>{t('OverspeedCount')}</h3>
          <div className="f-c gap-[2px]">
            <p>{values?.overspeedCount ?? '-'}</p>
          </div>
        </div>
        <div>
          <h3>{t('AverageSpeed')}</h3>
          <div className="f-c gap-[2px]">
            <p>{values?.averageSpeed ?? '-'}</p>
            <span className="body3">{' km/h'}</span>
          </div>
        </div>
        <div>
          <h3>{t('HarshBrakingCount')}</h3>
          <div className="f-c gap-[2px]">
            <p>{values?.harshBrakingCount ?? '-'}</p>
          </div>
        </div>
        <div>
          <h3>{t('HarshAccelerationCount')}</h3>
          <div className="f-c gap-[2px]">
            <p>{values?.harshAccelerationCount ?? '-'}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
