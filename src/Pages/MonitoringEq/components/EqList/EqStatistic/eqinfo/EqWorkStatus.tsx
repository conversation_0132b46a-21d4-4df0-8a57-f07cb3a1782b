import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import {
  getRandomTime,
  getRandomWorkStatus,
  getRandomWorkingHours,
  getRandomWorkingHoursChange,
} from '@/helpers/randomDataGenerator';

import { DemoTest } from '@/types';

const EqWorkStatus = ({ equipmentId }: { equipmentId: string }) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';
  const [currentDate, setCurrentDate] = useState('');
  const [, setWorkStatus] = useState({
    status: '',
    isWorking: false,
  });
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [workingHours, setWorkingHours] = useState('');
  const [workingHoursChange, setWorkingHoursChange] = useState({
    text1: '',
    text2: '',
    isIncrease: false as boolean | undefined,
  });

  /** Query */

  const equipmentRecentDateTime = {
    date: '',
    startTime: '',
    endTime: '',
  };

  useEffect(() => {
    if (equipmentRecentDateTime) {
      if (i18n.language === 'en') {
        setCurrentDate(`Based on ${equipmentRecentDateTime.date}`);
      } else {
        setCurrentDate(`${equipmentRecentDateTime.date}일 기준`);
      }

      setWorkStatus({
        status: '',
        isWorking: false,
      });
      setStartTime(equipmentRecentDateTime.startTime);
      setEndTime(equipmentRecentDateTime.endTime);
      setWorkingHours('-');
      setWorkingHoursChange({
        text1: '',
        text2: '',
        isIncrease: undefined,
      });
    }

    if (DemoTest.isRandomOn()) {
      // 현재 날짜 설정
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');

      if (i18n.language === 'en') {
        setCurrentDate(`${year}.${month}.${day}`);
      } else {
        setCurrentDate(`${year}.${month}.${day}일 기준`);
      }

      // 랜덤 데이터 생성
      setWorkStatus(getRandomWorkStatus());
      setStartTime(getRandomTime());
      setEndTime(getRandomTime());
      setWorkingHours(getRandomWorkingHours());
      setWorkingHoursChange(getRandomWorkingHoursChange());
    }
  }, [i18n.language]);

  return (
    <div className="p-5 f-c-c flex-col w-b-b-r text-center [&>div]:mb-4 [&>div:last-child]:mb-0 [&_h3]:body3 [&_h3]:text-gray-12 [&_p]:subtitle1 [&_p]:text-gray-12">
      <div>{currentDate}</div>

      {/* startTime */}
      <div>
        <h3>{t('OperationStart')}</h3>
        <p>{startTime}</p>
      </div>

      {/* endTime */}
      <div>
        <h3>{t('OperationEnded')}</h3>
        <p>{endTime}</p>
      </div>

      {/* workingHours */}
      <div className="w-full p-[14px] f-c-c flex-col bg-gray-2 rounded-md">
        <h3>{t('TotalOperationTime')}</h3>
        <p>{workingHours}</p>
        <div className="mt-[6px]">
          {isEnglish ? (
            <div className="pb-2 f-c gap-[6px]">
              <div
                className={`w-fit px-[7px] rounded ${
                  workingHoursChange.isIncrease
                    ? 'bg-semantic-1-1 [&>span]:text-semantic-1'
                    : 'bg-semantic-4-1 [&>span]:text-semantic-4'
                }`}
              >
                <span>{workingHoursChange.isIncrease ? '+' : '-'}</span>
                <span>{workingHoursChange.text2}</span>
                <span>
                  {workingHoursChange.isIncrease ? t('Up') : t('Down')}
                </span>
              </div>
              <div className="caption4 text-gray-10">
                {workingHoursChange.text1}
              </div>
            </div>
          ) : (
            <div className="pb-2 f-c gap-[6px]">
              <div className="caption4 text-gray-10">
                {workingHoursChange.text1}
              </div>
              <div
                className={`w-fit px-[7px] rounded ${
                  workingHoursChange.isIncrease
                    ? 'bg-semantic-1-1 [&>span]:text-semantic-1'
                    : 'bg-semantic-4-1 [&>span]:text-semantic-4'
                }`}
              >
                <span>{workingHoursChange.isIncrease ? '+' : '-'}</span>
                <span>{workingHoursChange.text2}</span>
                <span>
                  {workingHoursChange.isIncrease ? t('Up') : t('Down')}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EqWorkStatus;
