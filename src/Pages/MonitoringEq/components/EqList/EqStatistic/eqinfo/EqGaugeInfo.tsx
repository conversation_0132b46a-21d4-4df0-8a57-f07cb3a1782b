import GaugeInfo from './GaugeInfo';

type GaugeValue = {
  value: number;
  gauge: number;
};

export type EquipmentGaugeInfo = {
  isEV: boolean;
  fuelD?: {
    fuel: GaugeValue;
    tmoTemp: GaugeValue;
    coolTemp: GaugeValue;
    defLevel: GaugeValue;
  };
  fuelG?: {
    fuel: GaugeValue;
    tmoTemp: GaugeValue;
    coolTemp: GaugeValue;
  };
  ev?: {
    battery: GaugeValue;
    rightMotor: GaugeValue;
    leftMotor: GaugeValue;
    pumpMotor: GaugeValue;
    epsMotor: GaugeValue;
  };
};

export interface EqGaugeInfoProps {
  equipmentGaugeInfo: EquipmentGaugeInfo | null;
}

const EqGaugeInfo = ({ equipmentGaugeInfo }: EqGaugeInfoProps) => {
  if (equipmentGaugeInfo?.isEV === true) {
    return (
      equipmentGaugeInfo.ev && (
        <div className="f-c-c gap-6 [&>div]:w-[180px] [&>div]:h-[180px]">
          <GaugeInfo
            {...equipmentGaugeInfo.ev.battery}
            textColor="#1C76E0"
            unit="%"
            minLabel="E"
            maxLabel="F"
            label="Battery"
            gaugeColor="#1C76E0"
          />
          <GaugeInfo
            {...equipmentGaugeInfo.ev.rightMotor}
            textColor="#787BA2"
            unit="˚C"
            minLabel="C"
            maxLabel="H"
            label="MoterR"
            gaugeColor="#787BA2"
          />
          <GaugeInfo
            {...equipmentGaugeInfo.ev.leftMotor}
            textColor="#FF8341"
            unit="˚C"
            minLabel="C"
            maxLabel="H"
            label="MoterL"
            gaugeColor="#FF8341"
          />
          <GaugeInfo
            {...equipmentGaugeInfo.ev.pumpMotor}
            textColor="#EAB522"
            unit="˚C"
            minLabel="C"
            maxLabel="H"
            label="Pump"
            gaugeColor="#EAB522"
          />
          <GaugeInfo
            {...equipmentGaugeInfo.ev.epsMotor}
            textColor="#38AA51"
            unit="%"
            minLabel="E"
            maxLabel="F"
            label="EPS"
            gaugeColor="#38AA51"
          />
        </div>
      )
    );
  } else if (equipmentGaugeInfo?.fuelD) {
    return (
      <div className="f-c-c gap-10 [&>div]:w-[180px] [&>div]:h-[180px]">
        <GaugeInfo
          {...equipmentGaugeInfo.fuelD.fuel}
          textColor="9093B5"
          unit="L"
          minLabel="E"
          maxLabel="F"
          label="Fuel"
          gaugeColor="#1C76E0"
        />
        <GaugeInfo
          {...equipmentGaugeInfo.fuelD.coolTemp}
          textColor="9093B5"
          unit="˚C"
          minLabel="C"
          maxLabel="H"
          label="EngineCoolant"
          gaugeColor="#9093B5"
        />
        <GaugeInfo
          {...equipmentGaugeInfo.fuelD.tmoTemp}
          textColor="EAB522"
          unit="˚C"
          minLabel="C"
          maxLabel="H"
          label="TransmissionFluid"
          gaugeColor="#EAB522"
        />
        <GaugeInfo
          {...equipmentGaugeInfo.fuelD.defLevel}
          textColor="38AA51"
          unit="%"
          minLabel="E"
          maxLabel="F"
          label="DEF"
          gaugeColor="#38AA51"
        />
      </div>
    );
  } else {
    return (
      <div className="f-c-c gap-20 [&>div]:w-[180px] [&>div]:h-[180px]">
        <GaugeInfo
          {...(equipmentGaugeInfo?.fuelG?.fuel ?? { value: 0, gauge: 0 })}
          textColor="9093B5"
          unit="L"
          minLabel="E"
          maxLabel="F"
          label="Fuel"
          gaugeColor="#1C76E0"
        />
        <GaugeInfo
          {...(equipmentGaugeInfo?.fuelG?.coolTemp ?? { value: 0, gauge: 0 })}
          textColor="9093B5"
          unit="˚C"
          minLabel="C"
          maxLabel="H"
          label="EngineCoolant"
          gaugeColor="#9093B5"
        />
        <GaugeInfo
          {...(equipmentGaugeInfo?.fuelG?.tmoTemp ?? { value: 0, gauge: 0 })}
          textColor="EAB522"
          unit="˚C"
          minLabel="C"
          maxLabel="H"
          label="TransmissionFluid"
          gaugeColor="#EAB522"
        />
      </div>
    );
  }
  return null;
};

export default EqGaugeInfo;
