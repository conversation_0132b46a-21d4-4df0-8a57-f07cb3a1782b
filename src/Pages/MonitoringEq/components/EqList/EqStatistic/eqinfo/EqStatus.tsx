import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { EquipmentType } from '@/types/EquipmentType';

const EqStatus = ({
  equipmentBasicInfo,
}: {
  equipmentId: string;
  equipmentBasicInfo: EquipmentType.BasicInfo | null | undefined;
}) => {
  const { t } = useTranslation();
  const [Country, setCountry] = useState('');
  const [LocalDateTime, setLocalDateTime] = useState('');

  // 페이지 로드시 랜덤 데이터 생성
  useEffect(() => {
    if (equipmentBasicInfo) {
      setCountry(equipmentBasicInfo.modelName);
      setLocalDateTime(equipmentBasicInfo.deliveryDate);
    }
  }, [equipmentBasicInfo]);

  return (
    <div className="p-5 w-b-b-r [&>div]:py-3 [&>div:first-child]:pt-0 [&>div:last-child]:pb-0 [&>div]:border-b [&>div:last-child]:border-0 [&>div]:border-gray-6 [&_h3]:body2 [&_h3]:text-gray-10 [&_p]:caption1">
      <div>
        <h3>{t('Country')}</h3>
        <p>{Country}</p>
      </div>
      <div>
        <h3>{t('LocalDateTime')}</h3>
        <p>{LocalDateTime}</p>
      </div>
    </div>
  );
};

export default EqStatus;
