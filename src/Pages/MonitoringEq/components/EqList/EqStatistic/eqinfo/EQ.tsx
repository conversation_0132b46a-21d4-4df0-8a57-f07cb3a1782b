import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import UseEqPopup from '@/Pages/MonitoringEq/components/EqList/popup/UseEqPopup';
import car from '@/assets/images/etc/car.png';
import { EquipmentType } from '@/types/EquipmentType';

const EQ = ({
  equipmentBasicInfo,
}: {
  equipmentId: string;
  equipmentBasicInfo: EquipmentType.BasicInfo | null | undefined;
}) => {
  const { t } = useTranslation();
  const [modelName, setModelName] = useState('');
  const [plateNo, setPlateNo] = useState('');
  const [serialNo, setSerialNo] = useState('');
  const [driverName, setDriverName] = useState('');

  const { openTruckPopup } = UseEqPopup();

  // 페이지 로드시 랜덤 데이터 생성
  useEffect(() => {
    if (equipmentBasicInfo) {
      setModelName(equipmentBasicInfo.modelName);
      setPlateNo(equipmentBasicInfo.plateNo);
      setSerialNo(
        equipmentBasicInfo.serialNo.trim() !== ''
          ? equipmentBasicInfo.serialNo
          : '-',
      );
      setDriverName('driver');
    }
  }, [equipmentBasicInfo]);

  return (
    <div className="p-5 f-c-c flex-col w-b-b-r">
      <div className="w-full py-2 f-c-c bg-gray-1 b-b-r">
        <img src={car} alt={'car'} />
      </div>
      <div className="w-full mt-4 [&>div]:py-3 [&>div:first-child]:pt-0 [&>div:last-child]:pb-0 [&>div]:border-b [&>div:last-child]:border-0 [&>div]:border-gray-6 [&_h3]:body2 [&_h3]:text-gray-10 [&_p]:body2">
        <div>
          <h2 className="subtitle3">100D-9V</h2>
          <p>{modelName}</p>
        </div>
        <div>
          <h3>{t('Driver')}</h3>
          <p>{driverName}</p>
        </div>
      </div>
      <div
        onClick={openTruckPopup} // 일반 차량 상세 사양
        // onClick={openTruopenRegularVehiclePopupckPopup} // 트럭 상세 사양
        className="w-fit mt-4 py-[2px] px-[10px] bg-none rounded-md body4 text-secondary-6 cursor-pointer transition-bg duration-200 hover:bg-secondary-0"
      >
        {t('ViewSpecifications')}
      </div>
    </div>
  );
};

export default EQ;
