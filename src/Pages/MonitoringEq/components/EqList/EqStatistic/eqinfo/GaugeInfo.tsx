import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Gauge, gaugeClasses } from '@mui/x-charts';
import Badge from '@/Common/Components/common/Badge';

interface GaugeInfoProps {
  /** 실제 표시할 값 (예: 잔여 연료량) */
  value: number;
  /** 애니메이션 목표 게이지 값 (0~100) */
  gauge: number;
  /** 값 뒤에 붙일 단위 (기본: 'L') */
  unit?: string;
  /** 텍스트 컬러 */
  textColor?: string;
  /** 좌측 빈 상태 라벨 (기본: 'E') */
  minLabel?: React.ReactNode;
  /** 우측 가득 상태 라벨 (기본: 'F') */
  maxLabel?: React.ReactNode;
  /** i18n 키 문자열 또는 ReactNode */
  label?: string;
  /** 게이지 채워지는 부분 색상 (기본: '#010542') */
  gaugeColor?: string;
}

const GaugeInfo: React.FC<GaugeInfoProps> = ({
  value,
  gauge,
  unit = 'L',
  textColor,
  minLabel = 'E',
  maxLabel = 'F',
  label,
  gaugeColor = '#010542',
}) => {
  const { t } = useTranslation();

  const minText = minLabel;
  const maxText = maxLabel;
  const type: string = label ? t(label) : t('Fuel');

  const maxGauge = 100;
  const minGauge = 0;
  const [animatedValue, setAnimatedValue] = useState(0);

  useEffect(() => {
    let current = 0;
    const step = 1.5;
    const interval = setInterval(() => {
      current += step;
      if (current >= gauge) {
        setAnimatedValue(gauge);
        clearInterval(interval);
      } else {
        setAnimatedValue(current);
      }
    }, 4);
    return () => clearInterval(interval);
  }, [gauge]);

  return (
    <div className="relative">
      <svg width="0" height="0" style={{ position: 'absolute' }}>
        <defs>
          <filter x="-50%" y="-50%" width="200%" height="200%">
            <feOffset dx="0" dy="0" />
            <feGaussianBlur stdDeviation="3" result="offset-blur" />
            <feComposite
              operator="out"
              in="SourceGraphic"
              in2="offset-blur"
              result="inverse"
            />
            <feFlood floodColor="rgba(0,0,0,0.27)" result="color" />
            <feComposite
              operator="in"
              in="color"
              in2="inverse"
              result="shadow"
            />
            <feComposite operator="over" in="shadow" in2="SourceGraphic" />
          </filter>
        </defs>
      </svg>

      <div className="f-c gap-[2px] subtitle3 absolute top-1/2 left-1/2 -translate-x-1/2">
        <span style={textColor ? { color: `#${textColor}` } : undefined}>
          {value === -999 ? 'N/A' : `${value}`}
        </span>
        <span className="body3 text-gray-10">{unit}</span>
      </div>

      {/* 빈(E), 가득(F), 라벨 */}

      {/* 게이지 차트 */}
      <Gauge
        sx={{
          [`& .${gaugeClasses.valueArc}`]: {
            fill: gaugeColor,
            filter: 'url(#innerShadow)',
          },
          [`& .${gaugeClasses.referenceArc}`]: {
            fill: '#EFEFEF',
            filter: 'url(#innerShadow)',
          },
          [`& .${gaugeClasses.valueText}`]: {
            visibility: 'hidden',
          },
        }}
        value={animatedValue}
        valueMax={maxGauge}
        valueMin={minGauge}
        startAngle={-115}
        endAngle={115}
      />

      <div className="w-[180px] f-c-b caption3 absolute top-[80%] [&>div]:text-gray-8">
        <div>{minText}</div>
        <div>{maxText}</div>
      </div>

      <div className="f-c-c">
        <Badge message={type} />
      </div>
    </div>
  );
};

export default GaugeInfo;
