import { generateEquipmentDailyInfo } from '@/helpers/equipmentDetailDataGenerator';
import DailyWorkAnalyics from '@/Pages/MonitoringEq/components/EqList/EqStatistic/daily/DailyWorkAnalyics';
import { DemoTest } from '@/types';
import { Tabs } from '@radix-ui/themes';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import DailyEqLocation from './daily/DailyEqLocation';
import { EquipmentType } from '@/types/EquipmentType';

const Daily = ({
  equipmentId,
  equipmentBasicInfo,
  controllerParams,
}: {
  equipmentId: string;
  equipmentBasicInfo: EquipmentType.BasicInfo | null | undefined;
  captureRef: React.RefObject<HTMLDivElement>;
  controllerParams: Record<string, string | number | undefined>;
}) => {
  /** params */

  const [eqDailyInfoParams, setEqDailyInfoParams] = useState({
    equipmentId: '',
    date: '',
  });

  /** useQuery */

  // 일별 통계 조회
  const { data: eqDailyInfo } = useQuery<EquipmentType.DailyInfo | null>({
    queryKey: ['equipment/daily/info', eqDailyInfoParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        const isElectric = equipmentBasicInfo?.powerType === 'E';
        return generateEquipmentDailyInfo(isElectric);
      } else {
        try {
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    initialData: null,
    enabled:
      eqDailyInfoParams.equipmentId.length > 0 &&
      eqDailyInfoParams.date.length > 0,
  });

  // 주소 조회
  const { data: eqAddress } = useQuery({
    queryKey: ['equipment/daily/address', eqDailyInfo?.location.latlng],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        const isElectric = equipmentBasicInfo?.powerType === 'E';
        return generateEquipmentDailyInfo(isElectric).location.address;
      } else {
        try {
          return '';
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    initialData: '',
    enabled: true,
  });

  //** useEffect */

  // equipmentId 변경되면 검색
  useEffect(() => {
    if (equipmentId && controllerParams.date) {
      setEqDailyInfoParams({
        equipmentId: equipmentId,
        date: controllerParams.date as string,
      });
    }
  }, [equipmentId, controllerParams.date]);

  return (
    <Tabs.Content value="Daily">
      {/* 일별 통계 */}
      <DailyWorkAnalyics eqDailyInfo={eqDailyInfo} />
      {/* 차량 위치 */}
      <DailyEqLocation
        center={eqDailyInfo?.location.latlng ?? { lat: 0.0, lng: 0.0 }}
        address={eqAddress ?? ''}
        updateDate={eqDailyInfo?.location.datetime ?? ''}
      />
    </Tabs.Content>
  );
};

export default Daily;
