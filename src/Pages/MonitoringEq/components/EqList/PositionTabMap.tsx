import { useEffect, useState } from 'react';
import ZoomController from '@/Common/Components/map/ZoomController';
import EqSingleMarker from '@/Common/Components/Marker/EqSingleMarker';
import EqPositionInfoWindow from '@/Common/Components/eqWindow/EqPositionInfoWindow';
import { MapEngine } from '@/types';
import { GeneralMap, GeneralMapAdapter } from '@/logiMaps/react/general/Map';
import {
  GeneralCircle,
  GeneralPolyline,
  GeneralPoint,
} from '@/logiMaps/react/general/Poly';

interface PositionTabMapProps {
  defaultCenter: { lat: number; lng: number };
  location?: {
    date: string;
    time: string;
    lat: number;
    lng: number;
    alt: number;
    address: string;
    geofenceR: number;
  };
  route?: {
    path: { lat: number; lng: number }[];
    first: { date: string; latlng: { lat: number; lng: number } };
    last: { date: string; latlng: { lat: number; lng: number } };
  };
}

const PositionTabMap: React.FC<PositionTabMapProps> = (props) => {
  const [mapAdapter, setMapAdapter] = useState<GeneralMapAdapter | null>(null);
  const maxZoomLevel = 18;
  const minZoomLevel = 4;
  const defaultLevel = 15;
  const [zoomGauge, setZoomGauge] = useState(defaultLevel);

  /** useEffect */

  useEffect(() => {
    if (mapAdapter) {
      if (!props.location && !props.route) {
        if (props.defaultCenter.lat != 0.0 && props.defaultCenter.lng != 0.0) {
          mapAdapter.setCenter(props.defaultCenter);
          mapAdapter.setZoom(defaultLevel);
        }
      }
    }
  }, [mapAdapter, props.defaultCenter]);

  useEffect(() => {
    if (mapAdapter) {
      if (props.location) {
        mapAdapter.setCenter({
          lat: props.location.lat,
          lng: props.location.lng,
        });
        mapAdapter.setZoom(defaultLevel);
      }
    }
  }, [mapAdapter, props.location]);

  useEffect(() => {
    if (mapAdapter) {
      if (props.route) {
        const bounds: { lat: number; lng: number }[] = [];
        props.route.path.forEach((loc) => {
          // 북미 영역 좌표만 필터
          if (
            loc.lat >= 10.0 &&
            loc.lat <= 82.0 &&
            loc.lng >= -168.0 &&
            loc.lng <= -40.0
          ) {
            bounds.push({ lat: loc.lat, lng: loc.lng });
          }
        });
        if (props.route.path.length > 0 && bounds.length > 0) {
          mapAdapter.fitBounds(bounds, {
            top: '0%',
            right: '10%',
            bottom: '10%',
            left: '0%',
          });
        }
      }
    }
  }, [mapAdapter, props.route]);

  /** Event Listener */

  // 지도 로드
  const handleMapInit = (generalMapAdapter: GeneralMapAdapter) => {
    setMapAdapter(generalMapAdapter);
  };

  // 지도 스케일 변경
  const handleMapZoomChanged = (zoom: number) => {
    setZoomGauge(zoom);
  };

  return (
    <div>
      <GeneralMap
        mapSource={MapEngine.source()}
        id={'location'}
        maxZoom={maxZoomLevel}
        minZoom={minZoomLevel}
        defaultZoom={defaultLevel}
        onInitMap={handleMapInit}
        onZoomChanged={handleMapZoomChanged}
      >
        {props.location && (
          <>
            <EqSingleMarker
              id={'loc_marker'}
              latlng={{ lat: props.location.lat, lng: props.location.lng }}
            />
            <EqPositionInfoWindow
              id={'loc_info'}
              position={{ lat: props.location.lat, lng: props.location.lng }}
              pixelOffset={[0, -44]}
              locInfo={{
                vehNum: `${props.location.date} ${props.location.time}`,
                driver: props.location.lat.toString(),
                latitude: props.location.lng.toString(),
                longitude: props.location.alt.toString(),
                location: props.location.address,
              }}
            />
            {props.location.geofenceR > 0 && (
              <GeneralCircle
                radius={props.location.geofenceR} // meter
                center={{ lat: props.location.lat, lng: props.location.lng }}
                fillColor={'#FF5900'}
                fillOpacity={0.2}
                strokeColor={'#FF5900'}
                strokeOpacity={0.8}
                strokeWeight={1}
              />
            )}
          </>
        )}

        {props.route && (
          <>
            <GeneralPolyline path={props.route.path} color={'#FF59007F'} />
            {props.route.path.map((point, index) => (
              <GeneralPoint
                key={`via_${index}`}
                id={`via_${index}`}
                pixelRadius={6} // pixel
                center={{ lat: point.lat, lng: point.lng }}
                fillColor={'#FF5900'}
                fillOpacity={1.0}
                strokeColor={'#FF5900'}
                strokeOpacity={0.25}
                strokeWeight={6}
              />
            ))}
            <EqPositionInfoWindow
              id={'route_info1'}
              position={props.route.first.latlng}
              pixelOffset={[0, -4]}
              routeInfo={{ date: props.route.first.date }}
            />
            <EqPositionInfoWindow
              id={'route_info2'}
              position={props.route.last.latlng}
              pixelOffset={[0, -4]}
              routeInfo={{ date: props.route.last.date }}
            />
          </>
        )}
      </GeneralMap>
      {/* 줌 컨트롤 */}
      <ZoomController
        right={'right-6'}
        bottom={'bottom-12'}
        plus={function (): void {
          mapAdapter?.zoomIn();
        }}
        minus={function (): void {
          mapAdapter?.zoomOut();
        }}
      />
    </div>
  );
};

export default PositionTabMap;
