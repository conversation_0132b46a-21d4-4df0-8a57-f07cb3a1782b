import { useTranslation } from 'react-i18next';
import todayCheck from '@/assets/images/etc/today_check.svg';
import distance from '@/assets/images/etc/distance.svg';

export interface DistanceInfo {
  todaysDistance: string;
  totalDistance: string;
}

export interface DistanceProps {
  className?: string;
  distanceInfo: DistanceInfo | null;
}

export default function Distance({ className, distanceInfo }: DistanceProps) {
  const { t } = useTranslation();

  return (
    <div
      className={`${className} py-5 px-[30px] [&>div:first-child]:pb-5 [&>div:last-child]:pt-5 [&>div]:border-b [&>div:last-child]:border-0 [&>div]:border-gray-6`}
    >
      <div className="f-s flex-col">
        <div className="w-full f-c-b subtitle4">
          {t('TodaysDistance')}
          <img src={todayCheck} alt={'TodaysDistance'} />
        </div>
        <div className="subtitle1">{distanceInfo?.todaysDistance ?? '0km'}</div>
      </div>
      <div className="f-s flex-col">
        <div className="w-full f-c-b subtitle4">
          {t('TotalDistance')}
          <img src={distance} alt={'TotalDistance'} />
        </div>
        <div className="subtitle1">{`${distanceInfo?.totalDistance ?? '0km'}`}</div>
      </div>
    </div>
  );
}
