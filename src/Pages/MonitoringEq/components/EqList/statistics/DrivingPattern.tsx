import { EquipmentType } from '@/types/EquipmentType';
import { useTranslation } from 'react-i18next';

export interface DrivingPatternProps {
  className?: string;
  item?: EquipmentType.DailyDrivingPatternInfo;
}

export default function DrivingPattern({
  className,
  item,
}: DrivingPatternProps) {
  const { t } = useTranslation();

  return (
    <div className={`${className} py-5 px-[30px]`}>
      <h2 className="mb-3 subtitle4">{t('DrivingPattern')}</h2>
      <div
        className="f-c
          [&>div]:w-full
          [&>div]:px-6
          [&>div:first-child]:pl-0
          [&>div:last-child]:pr-0
          [&>div]:f-c-b
          [&>div]:border-r
          [&>div:last-child]:border-0
          [&>div]:border-gray-6
          [&_h3]:body3
          [&_p]:f-c
          [&_p]:gap-1
          [&_p]:subtitle1
          [&_span]:mt-1
          [&_span]:body1
        "
      >
        <div key="OverspeedCount">
          <h3>{t('OverspeedCount')}</h3>
          <p>{item?.overspeedCount ?? 0}</p>
        </div>
        <div key="AverageSpeed">
          <h3>{t('AverageSpeed')}</h3>
          <p>
            {item?.averageSpeed ?? 0} <span>{t('Kmh')}</span>
          </p>
        </div>
        <div key="HarshBrakingCount">
          <h3>{t('HarshBrakingCount')}</h3>
          <p>{item?.harshBrakingCount ?? 0}</p>
        </div>
        <div key="HarshAccelerationCount">
          <h3>{t('HarshAccelerationCount')}</h3>
          <p>{item?.harshAccelerationCount ?? 0}</p>
        </div>
      </div>
    </div>
  );
}
