import { useTranslation } from 'react-i18next';

interface DistanceProps {
  className?: string;
  title: string;
  value: string;
  upDown: string;
}

export default function Distance({
  className,
  title,
  value,
  upDown,
}: DistanceProps) {
  const { t } = useTranslation();

  return (
    <div className={`${className} pt-5 pb-[15px] px-6`}>
      <div className="subtitle4">{t(title)}</div>
      <div className="f-e-b">
        <div className="subtitle1">{value}</div>
        <div className="f-c gap-2">
          <div className="px-[7px] bg-secondary-0 body4 text-secondary-6">
            {upDown}
          </div>
          <div className="caption5">{t('NewCases')}</div>
        </div>
      </div>
    </div>
  );
}
