import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import { useToast } from '@/Common/useToast.tsx';
import { useDistanceUnit } from '@/context/DistanceUnitContext.tsx';
import Layout from '@/Common/Popup/Layout.tsx';
import { Cross1Icon } from '@radix-ui/react-icons';
import { Button } from '@/Common/Components/common/Button';
import Input from '@/Common/Components/common/Input';

interface SetIntervalpopupProps extends AlertPopupProps {
  type: 'engine' | 'electric'; // 차량 타입 props
  onConfirm: () => void;
}

const initialEngineConsumableValues = [
  { key: 'EngineOil', value: '' },
  { key: 'OilFilter', value: '' },
  { key: 'FuelFilter', value: '' },
  { key: 'AirFilter', value: '' },
  { key: 'BrakePads', value: '' },
  { key: 'BrakeLining', value: '' },
  { key: 'Tires', value: '' },
  { key: 'Coolant', value: '' },
  { key: 'Battery', value: '' },
  { key: 'TransmissionOil', value: '' },
  { key: 'TireRotation', value: '' },
];
const initialElectricConsumableValues = [
  { key: 'AirFilter', value: '' },
  { key: 'BrakePads', value: '' },
  { key: 'BrakeLining', value: '' },
  { key: 'Tires', value: '' },
  { key: 'Battery', value: '' },
  { key: 'TireRotation', value: '' },
];

const SetIntervalpopup = ({
  isOpen,
  onClose,
  onConfirm,
  type = 'engine',
}: SetIntervalpopupProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { distanceUnit } = useDistanceUnit();

  const [showResetConfirm, setShowResetConfirm] = useState(false);

  // 각 차량타입별 label/suffix 정보
  const engineConsumables = [
    { key: 'EngineOil', label: t('EngineOil'), suffix: 'mi' },
    { key: 'OilFilter', label: t('OilFilter'), suffix: 'mi' },
    { key: 'FuelFilter', label: t('FuelFilter'), suffix: 'mi' },
    { key: 'AirFilter', label: t('AirFilter'), suffix: 'mi' },
    { key: 'BrakePads', label: t('BrakePads'), suffix: 'mi' },
    { key: 'BrakeLining', label: t('BrakeLining'), suffix: 'mi' },
    { key: 'Tires', label: t('Tires'), suffix: 'mi' },
    { key: 'Coolant', label: t('Coolant'), suffix: 'years' },
    { key: 'Battery', label: t('Battery'), suffix: 'years' },
    { key: 'TransmissionOil', label: t('TransmissionOil'), suffix: 'mi' },
    { key: 'TireRotation', label: t('TireRotation'), suffix: 'mi' },
  ];
  const electricConsumables = [
    { key: 'AirFilter', label: t('AirFilter'), suffix: 'mi' },
    { key: 'BrakePads', label: t('BrakePads'), suffix: 'mi' },
    { key: 'BrakeLining', label: t('BrakeLining'), suffix: 'mi' },
    { key: 'Tires', label: t('Tires'), suffix: 'mi' },
    { key: 'Battery', label: t('Battery'), suffix: 'years' },
    { key: 'TireRotation', label: t('TireRotation'), suffix: 'mi' },
  ];
  const consumables =
    type === 'engine' ? engineConsumables : electricConsumables;
  const initialValues =
    type === 'engine'
      ? initialEngineConsumableValues
      : initialElectricConsumableValues;

  const [consumableValues, setConsumableValues] = useState(initialValues);

  // type이 바뀔 때마다 입력값도 리셋
  // (필요하다면 useEffect로 추가)
  // useEffect(() => { setConsumableValues(initialValues); }, [type, isOpen]);

  // 입력값 변경
  const inputChange = (index: number, value: string) => {
    setConsumableValues((prev) =>
      prev.map((item, i) => (i === index ? { ...item, value } : item)),
    );
  };

  // 입력값 초기화
  const resetInput = () => {
    setConsumableValues(initialValues);
  };

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[600px] popup-wrap">
        <article>
          <h2>
            {showResetConfirm
              ? t('ResetReplacementDays')
              : t('SetReplacementInterval')}
          </h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/* 정보 입력 */}
        {!showResetConfirm && (
          <article>
            <div className="mb-5 f-c-e">
              <Button
                variant={'bt_tertiary_sm'}
                label={'Reset'}
                onClick={() => setShowResetConfirm(true)}
              />
            </div>
            <div>
              {consumables.map((item, idx) => (
                <div key={item.key} className="mb-3 f-c-b">
                  <h3 className="body1">{item.label}</h3>
                  <Input
                    widthSize={'md'}
                    suffix={item.suffix === 'mi' ? distanceUnit : item.suffix}
                    value={consumableValues[idx]?.value || ''}
                    onChange={(e) => inputChange(idx, e.target.value)}
                  />
                </div>
              ))}
            </div>
            <div className="mt-7 f-c-e gap-[10px]">
              <Button
                variant={'bt_secondary'}
                label={'Cancel'}
                onClick={onClose}
              />
              <Button
                variant={'bt_primary'}
                label={'Save'}
                onClick={onConfirm}
              />
            </div>
          </article>
        )}

        {/* 리셋 확인 */}
        {showResetConfirm && (
          <article>
            <p className="body1">
              {t(
                'AreYouSureYouWantToResetTheReplacementDaysForThisConsumableItem',
              )}
            </p>
            <div className="mt-10 f-c-e gap-[10px]">
              <Button
                variant={'bt_secondary'}
                label={t('Cancel')}
                onClick={() => setShowResetConfirm(false)}
              />
              <Button
                variant={'bt_primary'}
                label={t('Confirm')}
                onClick={() => {
                  resetInput();
                  toast({
                    types: 'success',
                    description: t('TheReplacementIntervalHasBeenUpdated'),
                  });
                  setShowResetConfirm(false);
                }}
              />
            </div>
          </article>
        )}
      </section>
    </Layout>
  );
};

export default SetIntervalpopup;
