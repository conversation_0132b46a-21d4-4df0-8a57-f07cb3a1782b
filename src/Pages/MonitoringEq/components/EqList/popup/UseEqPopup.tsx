import { useTranslation } from 'react-i18next';
import { useOverlay } from '@toss/use-overlay';
import { useToast } from '@/Common/useToast.tsx';
import RegularVehiclePopup from './RegularVehiclePopup.tsx';
import TruckPopup from './TruckPopup.tsx';
import ChangeStatusPopup from './ChangeStatusPopup.tsx';
import SetIntervalpopup from './SetIntervalpopup.tsx';

const UseEqPopup = (refreshFleetList?: () => void) => {
  const { t } = useTranslation();

  const overlay = useOverlay();

  const { toast } = useToast();

  const openRegularVehiclePopup = () => {
    overlay.open(({ isOpen, close }) => {
      return (
        <RegularVehiclePopup
          onClose={close}
          onConfirm={() => {
            toast({
              types: 'success',
              description: t('ANewFleetHasBeenAdded'),
            });
            close();
            if (refreshFleetList) {
              refreshFleetList();
            }
          }}
          isOpen={isOpen}
        />
      );
    });
  };
  const openTruckPopup = () => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TruckPopup onClose={close} onConfirm={() => {}} isOpen={isOpen} />
      );
    });
  };
  const openChangeStatusPopup = () => {
    overlay.open(({ isOpen, close }) => {
      return (
        <ChangeStatusPopup
          onClose={close}
          onConfirm={() => {
            close();
          }}
          isOpen={isOpen}
        />
      );
    });
  };
  const openSetIntervalpopup = () => {
    overlay.open(({ isOpen, close }) => {
      return (
        <SetIntervalpopup
          type="engine"
          onClose={close}
          onConfirm={() => {
            toast({
              types: 'success',
              description: t('TheReplacementIntervalHasBeenUpdated'),
            });
            close();
          }}
          isOpen={isOpen}
        />
      );
    });
  };

  return {
    openRegularVehiclePopup,
    openTruckPopup,
    openChangeStatusPopup,
    openSetIntervalpopup,
  };
};

export default UseEqPopup;
