import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import { Button } from '@/Common/Components/common/Button';
import SummaryData from '@/Common/Components/etc/SummaryData';
import { Cross1Icon } from '@radix-ui/react-icons';

const RegularVehiclePopup = ({ onClose, isOpen }: AlertPopupProps) => {
  const { t } = useTranslation();

  const summaryData1 = [
    { label: 'FuelEfficiency', value: '25 km/L' },
    { label: 'AerodynamicDragCoefficient', value: '0.30' },
    { label: 'TirePressure', value: '35 psi' },
    { label: 'DriveType', value: '4-Wheel Drive (4WD)' },
  ];

  const summaryData2 = [
    { label: 'Height', value: '12ft 3in' },
    { label: 'Width', value: '15ft 3in' },
    { label: 'Length', value: '15ft 3in' },
    { label: 'TireDiameter', value: '22.5 in' },
    { label: 'TireWidth', value: '295 mm' },
    { label: 'HazardousMaterialsType', value: 'Explosive, Gas, Flammable' },
    { label: 'NumberOfAxles', value: '3' },
    { label: 'NumberOfWheels', value: '10' },
    { label: 'NumberOfTrailers', value: '12' },
    { label: 'KingpinToRearAxleFtM', value: '134' },
    { label: 'LiftingEquipmentInstalled', value: 'Yes' },
    { label: 'RefrigerationUnitInstalled', value: 'Yes' },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[780px] popup-wrap">
        {/*  */}
        <article>
          <h2>{t('VehicleSpecifications')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        <article className="p-[30px]">
          {/* 요약 */}
          <div className="mb-5 pb-5 flex flex-col border-b border-gray-4">
            <div className="mb-4 subtitle4">
              {t('DrivingResistanceFactors')}
            </div>
            <SummaryData details={summaryData1} fs="lg" />
          </div>

          <div className="mb-10 flex flex-col">
            <div className="mb-4 subtitle4">{t('TruckConfiguration')}</div>
            <SummaryData details={summaryData2} fs="lg" />
          </div>

          {/* 버튼 */}
          <div className="f-je">
            <Button
              variant={'bt_secondary'}
              label={'Cancel'}
              onClick={onClose}
            />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default RegularVehiclePopup;
