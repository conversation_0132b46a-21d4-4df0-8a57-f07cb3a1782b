import { HTMLAttributes } from 'react';

const TabPopupSubMenu = (
  props: HTMLAttributes<HTMLDivElement> & { disabled?: boolean },
) => {
  return (
    <p
      style={props.disabled ? { color: '#CCCCCC' } : {}}
      onClick={(e) => {
        if (!props?.disabled) {
          if (props.onClick) {
            props.onClick(e);
          }
        }
      }}
    >
      {props.children}
    </p>
  );
};

export default TabPopupSubMenu;
