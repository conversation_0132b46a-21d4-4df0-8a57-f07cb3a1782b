import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import DropDown from '@/Common/Components/common/DropDown';
import { Button } from '@/Common/Components/common/Button';
import MonthSelector from '@/Common/Components/datePicker/MonthSelector';
import YearSelector from '@/Common/Components/datePicker/YearSelector';
import BasicCard from '@/Common/Components/common/BasicCard';
import { EngineConsumptionChart } from '@/Pages/MonitoringEq/components/EqList/moreInfo/engine/component/EngineConsumptionChart';
import CommonTable from '@/Common/Components/common/CommonTable';

const EngineConsumption = () => {
  const { t } = useTranslation();

  const [dateType, setDateType] = useState<string>('Monthly');

  const dateOptions = [
    { key: 'Monthly', value: 'Monthly' },
    { key: 'Year', value: 'Year' },
  ];

  const engineDummy = [
    { day: 1, consumption: 80 },
    { day: 2, consumption: 50 },
    { day: 3, consumption: 100 },
  ];

  const columns = [
    { header: t('Date'), accessorKey: 'date' },
    { header: t('Hourmeter'), accessorKey: 'hourmeter' },
    { header: t('EngineOperationTime'), accessorKey: 'engine' },
    { header: t('WorkTime'), accessorKey: 'work' },
    { header: t('DrivingTime'), accessorKey: 'driving' },
    { header: t('FuelLevel'), accessorKey: 'fuel' },
    { header: t('FuelConsumptionECM'), accessorKey: 'ecm' },
    { header: t('FuelConsumptionTank'), accessorKey: 'tank' },
  ];

  const data = [
    {
      date: '2025-04-08',
      hourmeter: '8,721',
      engine: '1h 43m',
      work: '4h 35m',
      driving: '3h 12m',
      fuel: '18%',
      ecm: '2L',
      tank: '0L',
    },
  ];

  return (
    <section className="wrap-layout">
      {/*  */}
      <h2 className="mb-[30px] subtitle3">{t('EngineConsumption')}</h2>

      {/*  */}
      <div className="mb-[30px] f-c-b">
        <div className="f-c gap-[10px]">
          <DropDown
            options={dateOptions}
            placeholder={t('Monthly')}
            selectedKey={dateType}
            onSelKey={(key) => setDateType(key)}
            size="fit"
          />
          {dateType === 'Monthly' && <MonthSelector />}
          {dateType === 'Year' && <YearSelector />}
        </div>
        <Button variant="bt_tertiary" label={t('Download')} />
      </div>

      {/*  */}
      <p className="mb-2 caption4 text-semantic-4">
        {t(
          'CautionTheMessageBelowWillBeSentTheTheDriverViaAppPushNotification',
        )}
      </p>

      {/*  */}
      <div className="mb-[10px] f-c gap-[10px]">
        <BasicCard title="Total" value="183" unit="%" />
        <BasicCard title="DailyAverage" value="183" unit="%" />
      </div>

      {/*  */}
      <div className="mb-[10px] border border-gray-6 rounded-md">
        <EngineConsumptionChart data={engineDummy} />
      </div>

      {/*  */}
      <CommonTable
        columns={columns}
        data={data}
        isPagination={false}
        isCheckbox={false}
      />
    </section>
  );
};

export default EngineConsumption;
