import { useTranslation } from 'react-i18next';
import { HoverCard } from '@radix-ui/themes';
import React, { HTMLAttributes, useEffect, useState } from 'react';
import TabPopupSubMenu from '@/Pages/MonitoringEq/components/EqList/TabPopupSubMenu';
import { useElectronicTab } from '@/store/detail-tab.ts';

export enum MoreInfoTabMenu {
  ENGINE_CONSUMPTION = 'Engine Consumption',
  TEMPERATURE_DISTRIBUTION = 'Temperature Distribution',
  DEALER_SERVICE_CENTER = 'Dealer Service Center',
  ACCIDENT_HISTORY = 'Accident History',

  BATTERY_CONSUMPTION = 'Battery Consumption_B',
  LITHIUM_BATTERY_INFO = 'Lithium Battery Info_B',
  TEMPERATURE_DISTRIBUTION_B = 'Temperature Distribution_B',
  DEALER_SERVICE_CENTER_B = 'Dealer Service Center_B',
  ACCIDENT_HISTORY_B = 'Accident History_B',
}

export enum MoreInfoTabMenuE {
  ENGINE_CONSUMPTION = 'Engine Consumption',
  TEMPERATURE_DISTRIBUTION = 'Temperature Distribution',
  DEALER_SERVICE_CENTER = 'Dealer Service Center',
  ACCIDENT_HISTORY = 'Accident History',

  BATTERY_CONSUMPTION = 'Battery Consumption',
  LITHIUM_BATTERY_INFO = 'Lithium Battery Info',
  TEMPERATURE_DISTRIBUTION_B = 'Temperature Distribution',
  DEALER_SERVICE_CENTER_B = 'Dealer Service Center',
  ACCIDENT_HISTORY_B = 'Accident History',
}

const MoreInfoTab = (props: HTMLAttributes<HTMLDivElement>) => {
  const { t } = useTranslation();

  const { setElectronic } = useElectronicTab((s) => s);

  const onClickFuel = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (props.onClick) props.onClick(e);
    setElectronic(false);
  };

  const onClickElectronic = (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>,
  ) => {
    if (props.onClick) props.onClick(e);
    setElectronic(true);
  };

  // API를 통해 전동식(전자식)인지 엔진식인지 판별하기 위한 상태 (null이면 아직 로딩 중)
  const [isElectronic, setIsElectronic] = useState<boolean | null>(null);

  useEffect(() => {
    async function fetchEquipmentType() {
      // 실제 API 호출 코드로 교체
      const response = await fakeApiCall();
      setIsElectronic(response.isElectronic);
    }
    fetchEquipmentType();
  }, []);

  // 가짜 API 함수: 항상 엔진식(isElectronic: false)을 반환
  const fakeApiCall = (): Promise<{ isElectronic: boolean }> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ isElectronic: false });
      }, 1);
    });
  };

  return (
    <HoverCard.Root>
      <HoverCard.Trigger>
        <span>{t('MoreInfo')}</span>
      </HoverCard.Trigger>
      <HoverCard.Content align="center" className="hover-design">
        {isElectronic ? (
          // 엔진식 메뉴
          <div>
            <TabPopupSubMenu onClick={onClickFuel}>
              {t('EngineConsumption')}
            </TabPopupSubMenu>
            <TabPopupSubMenu onClick={onClickFuel}>
              {t('TemperatureDistribution')}
            </TabPopupSubMenu>
            <TabPopupSubMenu onClick={onClickFuel}>
              {t('DealerServiceCenter')}
            </TabPopupSubMenu>
            <TabPopupSubMenu onClick={onClickFuel}>
              {t('AccidentHistory')}
            </TabPopupSubMenu>
          </div>
        ) : (
          // 전동식 메뉴
          <div>
            <TabPopupSubMenu onClick={onClickElectronic}>
              {t('BatteryConsumption')}
            </TabPopupSubMenu>
            <TabPopupSubMenu onClick={onClickElectronic}>
              {t('LithiumBatteryInfo')}
            </TabPopupSubMenu>
            <TabPopupSubMenu onClick={onClickElectronic}>
              {t('TemperatureDistribution')}
            </TabPopupSubMenu>
            <TabPopupSubMenu onClick={onClickElectronic}>
              {t('DealerServiceCenter')}
            </TabPopupSubMenu>
            <TabPopupSubMenu onClick={onClickElectronic}>
              {t('AccidentHistory')}
            </TabPopupSubMenu>
          </div>
        )}
      </HoverCard.Content>
    </HoverCard.Root>
  );
};

export default MoreInfoTab;
