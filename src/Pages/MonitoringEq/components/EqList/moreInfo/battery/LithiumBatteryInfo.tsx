import { useTranslation } from 'react-i18next';
import BasicCard from '@/Common/Components/common/BasicCard';

const LithiumBatteryInfo = () => {
  const { t } = useTranslation();

  return (
    <section className="wrap-layout">
      <h2 className="mb-[30px] subtitle3">{t('LithiumBatteryInformation')}</h2>

      <div className="grid grid-cols-4 grid-rows-5 gap-[10px]">
        {/* Total Charge Cycles */}
        <BasicCard title="TotalChargeCycles" value="303" />
        {/* Average SOH */}
        <BasicCard title="AverageSOH" value="68" unit="%" />
        {/* Pack Current */}
        <BasicCard title="PackCurrent" value="323" unit="A" />
        {/* Pack Voltage */}
        <BasicCard title="PackVoltage" value="73" unit="V" />
        {/* Coolent Temperature */}
        <div className="col-span-2 row-span-4 border border-gray-6 rounded-md">
          <h3 className="mb-5 mx-6 pt-[30px] pb-5 border-b border-gray-6 subtitle4">
            {t('CoolentTemperature')}
          </h3>
          <BasicCard
            title="BTMSToBattery"
            value="-23"
            unit="F"
            className="pb-0 border-0"
          />
          <BasicCard
            title="BatteryToBTMS"
            value="-23"
            unit="F"
            className="pb-0 border-0"
          />
          <BasicCard
            title="BTMSToVehicle"
            value="-23"
            unit="F"
            className="pb-0 border-0"
          />
          <BasicCard
            title="VehicleToBTMS"
            value="-23"
            unit="F"
            className="border-0"
          />
        </div>
        {/* BTMS Power Consumption */}
        <BasicCard title="BTMSPowerConsumption" value="303" unit="kW" />
        {/* Maximum Battery Temperature */}
        <BasicCard title="MaximumBatteryTemperature" value="68" unit="F" />
        {/* Cooling Fan Usage */}
        <BasicCard title="CoolingFanUsage" value="83" unit="%" />
        {/* Battery Pack Cycle Count */}
        <BasicCard title="BatteryPackCycleCount" value="2,173" />
        {/* Maximum Cell Voltage */}
        <BasicCard title="MaximumCellVoltage" value="4" unit="V" />
        {/* Minimum Cell Voltage */}
        <BasicCard title="MinimumCellVoltage" value="2" unit="V" />
        {/* Maximum Module Temperature */}
        <BasicCard title="MaximumModuleTemperature" value="4" unit="F" />
        {/* Minimum Module Temperature */}
        <BasicCard title="MinimumModuleTemperature" value="2" unit="F" />
      </div>
    </section>
  );
};

export default LithiumBatteryInfo;
