import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import ECharts from 'echarts-for-react';
import * as echarts from 'echarts';

interface BatteryConsumptionProps {
  data: { day: number; consumption: number }[];
  className?: string;
}

export const BatteryConsumptionChart = ({
  data,
  className,
}: BatteryConsumptionProps) => {
  const { t } = useTranslation();
  const [option, setOption] = useState({});

  useEffect(() => {
    const days = Array.from({ length: 31 }, (_, i) => i + 1);
    const consumptionArr = Array(31).fill(0);

    data.forEach((item) => {
      const idx = item.day - 1;
      if (idx >= 0 && idx < 31) {
        consumptionArr[idx] = item.consumption;
      }
    });

    setOption({
      textStyle: {
        fontFamily: 'Pretendard',
      },
      grid: {
        left: '0%',
        right: '4%',
        bottom: '0%',
        top: '16%',
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        valueFormatter: (value: number) => `${value}%`,
      },
      title: {
        left: '5',
        top: '10',
        textStyle: {
          fontWeight: 500,
          fontSize: 15,
        },
      },
      xAxis: {
        type: 'category',
        data: days,
        axisLabel: { fontSize: 12 },
        name: 'Day',
        nameLocation: 'end',
        nameTextStyle: {
          fontWeight: 'normal',
          fontSize: 13,
          padding: [4, 0, 0, 0],
        },
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 150,
        axisLabel: { fontSize: 13 },
        name: '%',
        nameLocation: 'end',
        nameTextStyle: {
          fontWeight: 'normal',
          fontSize: 13,
          padding: [0, 25, 0, 0],
        },
        splitLine: {
          show: true,
          lineStyle: { type: 'dashed', width: 1 },
        },
      },
      series: [
        {
          name: 'Battery Consumption',
          type: 'bar',
          data: consumptionArr,
          barWidth: 8,
          barBorderRadius: [4, 4, 0, 0],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              { offset: 0, color: '#FF5900' },
              { offset: 1, color: '#FFDECC' },
            ]),
          },
        },
      ],
    });
  }, [data]);

  return (
    <div className={`${className} py-5 px-6`}>
      <h2 className="subtitle4">{t('BatteryConsumption')}</h2>
      <ECharts option={option} style={{ height: 240, width: '100%' }} />
    </div>
  );
};
