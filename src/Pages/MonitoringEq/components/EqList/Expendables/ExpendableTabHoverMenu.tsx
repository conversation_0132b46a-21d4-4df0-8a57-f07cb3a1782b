import { useTranslation } from 'react-i18next';
import { HoverCard } from '@radix-ui/themes';
import { HTMLAttributes } from 'react';

const ExpendableTabHoverMenu = (props: HTMLAttributes<HTMLDivElement>) => {
  const { t } = useTranslation();

  return (
    <HoverCard.Root>
      <HoverCard.Trigger>
        <span>{t('Consumables')}</span>
      </HoverCard.Trigger>
      <HoverCard.Content align="center" className={'hover-design'}>
        <p onClick={props.onClick}>{t('ConsumablesStatus')}</p>
        <p onClick={props.onClick}>{t('ConsumablesHistory')}</p>
      </HoverCard.Content>
    </HoverCard.Root>
  );
};

export default ExpendableTabHoverMenu;
