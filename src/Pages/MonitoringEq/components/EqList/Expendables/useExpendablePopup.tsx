import { useTranslation } from 'react-i18next';
import { useToast } from '@/Common/useToast.tsx';
import { useOverlay } from '@toss/use-overlay';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';
import ExchangeTermPopup from '@/Pages/MonitoringEq/components/EqList/Expendables/components/ExchangeTermPopup';
import { ExpendableTabProps } from '@/types';

const useExpendablePopup = () => {
  const { t } = useTranslation();

  const { toast } = useToast();

  const overlay = useOverlay();

  // 교환 주기 변경 팝업
  const openExchangeTermPopup = (
    equipmentId: string,
    row: ExpendableTabProps,
    onSuccess: () => void,
  ) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <ExchangeTermPopup
          onClose={close}
          onConfirm={() => {
            close();
            onSuccess();
          }}
          isOpen={isOpen}
          equipmentId={equipmentId}
          row={row}
        ></ExchangeTermPopup>
      );
    });
  };

  // 교체 일수 리셋 팝업 (API 없이 단순 콜백)
  const openExchangeDayReset = (
    equipmentId: string,
    row: ExpendableTabProps,
    onSuccess: () => void,
  ) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TwoButtonPopup
          onClose={close}
          onConfirm={() => {
            close();
            onSuccess();
          }}
          isOpen={isOpen}
          text={t('DoYouWantToRequestMaintenanceInformation')}
          buttonText={t('Cancel')}
          secondButtonText={t('Reset2')}
        />
      );
    });
  };

  // 소모품 요청 팝업 (API 없이 단순 Toast)
  const openRequestExpendable = () => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TwoButtonPopup
          onClose={close}
          onConfirm={() => {
            toast({
              types: 'success',
              description: t('InformationHasBeenUpdated'),
            });
            close();
          }}
          isOpen={isOpen}
          text={t('DoYouWantToRequestMaintenanceInformation')}
          buttonText={t('Cancel')}
          secondButtonText={t('Request')}
        />
      );
    });
  };

  return { openExchangeTermPopup, openExchangeDayReset, openRequestExpendable };
};

export default useExpendablePopup;
