import { useTranslation } from 'react-i18next';
import { HoverCard } from '@radix-ui/themes';
import { HTMLAttributes } from 'react';

const AlarmTabHoverMenu = (props: HTMLAttributes<HTMLDivElement>) => {
  const { t } = useTranslation();

  return (
    <HoverCard.Root>
      <HoverCard.Trigger>
        <span>{t('Faults')}</span>
      </HoverCard.Trigger>
      <HoverCard.Content align="center" className={'hover-design'}>
        <div onClick={props.onClick}>{t('FaultHistory')}</div>
        <div onClick={props.onClick}>{t('CurrentFaultAlerts')}</div>
      </HoverCard.Content>
    </HoverCard.Root>
  );
};

export default AlarmTabHoverMenu;
