import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout.tsx';
import { PopupProps } from '@/types';
import close_popup from '@/assets/images/etc/close_popup.png';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { ColumnDef } from '@tanstack/react-table';
import DropDown from '@/Common/Components/common/DropDown.tsx';
import Input from '@/Common/Components/common/Input.tsx';
import { Button } from '@/Common/Components/common/Button.tsx';
import CommonTable from '@/Common/Components/common/CommonTable.tsx';
import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { EquipmentType } from '@/types/EquipmentType.ts';

const ElectricAlarmCodePopup = ({ onClose, isOpen }: PopupProps) => {
  const { t } = useTranslation();

  const electricAlarmCodeTableColumn: ColumnDef<EquipmentType.ElectricAlarmCodeTableColumnProps>[] =
    [
      {
        size: 140,
        header: () => <div>{t('Model')}</div>,
        accessorKey: 'model',
        cell: ({ row }) => <div>{row.original.model}</div>,
      },
      {
        size: 72,
        header: () => <div>{t('Alarm')}</div>,
        accessorKey: 'alarm',
        cell: ({ row }) => <div>{row.original.alarm}</div>,
      },
      {
        size: 52,
        header: () => <div>{t('Severity')}</div>,
        accessorKey: 'level',
        cell: ({ row }) => <div>{row.original.level}</div>,
      },
      {
        size: 328,
        header: () => <div>{t('DescriptionENG')}</div>,
        accessorKey: 'enExplain',
        cell: ({ row }) => <div>{row.original.enExplain}</div>,
      },
      {
        size: 328,
        header: () => <div>{t('DescriptionKOR')}</div>,
        accessorKey: 'koExplain',
        cell: ({ row }) => <div>{row.original.koExplain}</div>,
      },
    ];

  //모델
  const [modelSelKey, setModelSelKey] = useState('All');
  //알람
  const [alarm, setAlarm] = useState('');
  //설명
  const [desc, setDesc] = useState('');

  /** Query */

  const { data: modelOptions } = useQuery({
    queryKey: ['evalarmcode:equipment/alarm/model'],
    queryFn: async () => {
      try {
        return [{ key: 'All', value: 'ALL' }];
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    initialData: [{ key: 'All', value: 'ALL' }],
    enabled: true,
  });

  const [, setAlarmCodeZipParams] = useState({
    model: '',
    alarm: '',
    desc: '',
    pageNum: 0,
    pageSize: 0,
  });

  const alarmCodeZipData = {
    pageNum: 0,
    pageSize: 0,
    totalCnt: 0,
    rows: [],
  };

  const handleSearch = () => {
    setAlarmCodeZipParams({
      model:
        modelOptions.find((option) => option.key === modelSelKey)?.value ?? '',
      alarm: alarm,
      desc: desc,
      pageNum: 1,
      pageSize: 5,
    });
  };

  return (
    <Layout isOpen={isOpen}>
      <div>
        <div>
          <div>
            <div>{t('AlarmCodeCollection')}</div>
          </div>
          <img src={close_popup} onClick={onClose} />
        </div>
        <div>
          <SearchItemContainer>
            <SearchLabel>{t('Model')}</SearchLabel>
            <DropDown
              options={modelOptions}
              placeholder={modelSelKey}
              onSelKey={setModelSelKey}
            />
            <SearchLabel>{t('Alarm')}</SearchLabel>
            <Input
              placeholder={'Alarm'}
              onChange={(e) => {
                setAlarm(e.target.value);
              }}
            />
            <SearchLabel>{t('Description')}</SearchLabel>
            <Input
              placeholder={t('Description')}
              onChange={(e) => {
                setDesc(e.target.value);
              }}
            />
            <SearchItemContainer>
              <Button
                variant={'bt_primary'}
                label={'Search'}
                onClick={handleSearch}
              />
            </SearchItemContainer>
          </SearchItemContainer>
          <CommonTable
            data={alarmCodeZipData.rows}
            columns={electricAlarmCodeTableColumn}
            isPagination={true}
            customPageSize={alarmCodeZipData.pageSize}
            totalCount={alarmCodeZipData.totalCnt}
            currentPage={alarmCodeZipData.pageNum}
            onPageChange={(page: number) => {
              setAlarmCodeZipParams((prevState) => ({
                ...prevState,
                pageNum: page,
              }));
            }}
          />
        </div>
      </div>
    </Layout>
  );
};

export default ElectricAlarmCodePopup;
