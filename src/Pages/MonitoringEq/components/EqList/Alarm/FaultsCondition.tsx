import { useTranslation } from 'react-i18next';
import { Button } from '@/Common/Components/common/Button';
import Dropdown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import { useRef, useState } from 'react';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';

export interface SearchParams {
  severity: string;
  faultType: string;
  status: string;
  fromDate: string;
  toDate: string;
}

interface SearchFilterProps {
  isElectric: boolean;
  onSearch?: (params: SearchParams) => void;
}

const FaultsCondition: React.FC<SearchFilterProps> = ({ onSearch }) => {
  const { t } = useTranslation();

  // fault severity
  const [severitySelKey, setSeveritySelKey] = useState(t('AllFaultSeverity'));
  // fault type
  const [faultTypeSelKey, setfaultTypeSelKey] = useState(t('AllFaultType'));
  // fault type
  const [statusSelKey, setStatusSelKey] = useState(t('AllStatus'));

  const fromDate = useRef(dayjs().format('YYYY-MM-DD'));
  const toDate = useRef(dayjs().format('YYYY-MM-DD'));

  const handleFromToSelectorInit = (start: string, end: string) => {
    fromDate.current = dayjs(start).format('YYYY-MM-DD');
    toDate.current = dayjs(end).format('YYYY-MM-DD');
    searchAlarmHistory();
  };

  const handleFromToSelectorChange = (start: string, end: string) => {
    fromDate.current = dayjs(start).format('YYYY-MM-DD');
    toDate.current = dayjs(end).format('YYYY-MM-DD');
  };

  /** Query */

  const severityOptions: { key: string; value: string }[] = [
    { key: t('AllFaultSeverity'), value: 'severity' },
    { key: t('High'), value: 'high' },
    { key: t('Medium'), value: 'medium' },
    { key: t('Low'), value: 'low' },
  ];

  const faultTypeOptions: { key: string; value: string }[] = [
    { key: t('AllFaultType'), value: 'fault' },
  ];

  const statusOptions: { key: string; value: string }[] = [
    { key: t('AllStatus'), value: 'status' },
    { key: t('Received'), value: 'received' },
    { key: t('InProgress'), value: 'progress' },
    { key: t('Completed'), value: 'completed' },
  ];

  /** Function */
  const searchAlarmHistory = () => {
    const searchParams: SearchParams = {
      severity:
        severityOptions.find((option) => option.key === severitySelKey)
          ?.value ?? '',
      faultType:
        severityOptions.find((option) => option.key === severitySelKey)
          ?.value ?? '',
      status:
        statusOptions.find((option) => option.key === statusSelKey)?.value ??
        '',
      fromDate: fromDate.current,
      toDate: toDate.current,
    };
    onSearch?.(searchParams);
  };

  return (
    <div>
      <SearchItemContainer className="mb-10 gap-[10px]">
        <Dropdown
          options={severityOptions}
          placeholder={severitySelKey}
          onSelKey={setSeveritySelKey}
        />
        <Dropdown
          options={faultTypeOptions}
          placeholder={faultTypeSelKey}
          onSelKey={setfaultTypeSelKey}
        />
        <Dropdown
          options={statusOptions}
          placeholder={statusSelKey}
          onSelKey={setStatusSelKey}
        />
        <FromToSelector
          initValue={{
            start: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
            end: dayjs().format('YYYY-MM-DD'),
          }}
          onInit={handleFromToSelectorInit}
          onChange={handleFromToSelectorChange}
        />
        <Button
          variant={'bt_primary'}
          label={'Search'}
          onClick={searchAlarmHistory}
        />
      </SearchItemContainer>
    </div>
  );
};
FaultsCondition.propTypes = {
  isElectric: PropTypes.bool.isRequired,
  onSearch: PropTypes.func,
};

export default FaultsCondition;
