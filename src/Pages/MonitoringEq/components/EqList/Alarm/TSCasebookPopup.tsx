import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout.tsx';
import close_popup from '@/assets/images/etc/close_popup.png';
import { ColumnDef } from '@tanstack/react-table';
import CommonTable from '@/Common/Components/common/CommonTable.tsx';
import { AlertPopupProps } from '@/types/index.ts';
import { EquipmentType } from '@/types/EquipmentType.ts';

const TSCasebookPopup = ({ onClose, isOpen }: AlertPopupProps) => {
  const { t } = useTranslation();

  const TSCasebookTableColumn: ColumnDef<EquipmentType.TSCasebookColumnProps>[] =
    [
      {
        size: 112,
        header: () => <div>{t('No')}</div>,
        accessorKey: 'no',
        cell: ({ row }) => <div>{row.original.no}</div>,
      },
      {
        size: 306,
        header: () => <div>{t('File')}</div>,
        accessorKey: 'file',
        cell: ({ row }) => <div>{row.original.file}</div>,
      },
      {
        size: 232,
        header: () => <div>{t('RegistrationDate')}</div>,
        accessorKey: 'registDate',
        cell: ({ row }) => <div>{row.original.registDate}</div>,
      },
    ];

  return (
    <Layout isOpen={isOpen}>
      <div>
        <div>
          <div>
            <div>{t('TroubleShootingProcedure')}</div>
          </div>
          <img src={close_popup} onClick={onClose} />
        </div>
        <CommonTable
          data={[]}
          isPagination={false}
          columns={TSCasebookTableColumn}
        />
      </div>
    </Layout>
  );
};

export default TSCasebookPopup;
