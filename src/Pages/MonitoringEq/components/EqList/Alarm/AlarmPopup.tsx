import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout.tsx';
import { Button } from '@/Common/Components/common/Button.tsx';
import close_popup from '@/assets/images/etc/close_popup.png';
import Radio, { RadioOption } from '@/Common/Components/common/Radio.tsx';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { ColumnDef } from '@tanstack/react-table';
import additem from '@/assets/images/popup/add_item.png';
import receiver from '@/assets/images/popup/add_receiver.png';
import DropDown from '@/Common/Components/common/DropDown.tsx';
import Input from '@/Common/Components/common/Input.tsx';
import CommonTable from '@/Common/Components/common/CommonTable.tsx';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { EquipmentType } from '@/types/EquipmentType.ts';

const AlarmPopup = ({
  onClose,
  isOpen,
  serialNo,
  row,
}: EquipmentType.AlarmPopupProps) => {
  const { t, i18n } = useTranslation();

  const [selectedValue] = useState<string>('2');
  const [sendPageList, setSendPageList] = useState<
    EquipmentType.AlarmReceiverColumnProps[]
  >([]);

  const [permisionSelKey, setPermisionSelKey] = useState('-');
  const [countrySelKey, setCountrySelKey] = useState('All');
  const [name, setName] = useState('');
  const [mobileNo, setMobileNo] = useState('');
  const [email, setEmail] = useState('');
  const [content, setContent] = useState('');

  const [selected, setSelected] = useState<string>('0');

  const options: RadioOption[] = [
    { value: '0', label: 'EMail' },
    { value: '1', label: 'SMS' },
    { value: '2', label: 'EMailSMS' },
    { value: '3', label: 'App Push' },
  ];

  const AlarmReceiverTableColumn: ColumnDef<EquipmentType.AlarmReceiverColumnProps>[] =
    [
      {
        size: 72,
        header: () => <div className="">{t('No')}</div>,
        accessorKey: 'no',
        cell: ({ row }) => <div className="">{row.original.no}</div>,
      },
      {
        size: 112,
        header: () => <div className="">{t('Permission')}</div>,
        accessorKey: 'permission',
        cell: ({ row }) => <div className="">{row.original.permission}</div>,
      },
      {
        size: 112,
        header: () => <div className="">{t('Country')}</div>,
        accessorKey: 'country',
        cell: ({ row }) => <div className="">{row.original.country}</div>,
      },
      {
        size: 152,
        header: () => <div className="">{t('PhoneNumber')}</div>,
        accessorKey: 'mobile',
        cell: ({ row }) => <div className="">{row.original.mobile}</div>,
      },
      {
        size: 192,
        header: () => <div className="">{t('EMail')}</div>,
        accessorKey: 'email',
        cell: ({ row }) => <div className="">{row.original.email}</div>,
      },
      {
        size: 192,
        header: () => <div className="">{t('Name')}</div>,
        accessorKey: 'name',
        cell: ({ row }) => <div className="">{row.original.name}</div>,
      },
      {
        size: 92,
        header: () => <div>{t('Delete')}</div>,
        accessorKey: 'delete',
        cell: ({ row }) => (
          <Button
            variant={'bt_primary'}
            label={'Delete'}
            onClick={() => {
              handleDelClick(row.original);
            }}
          />
        ),
      },
    ];

  /** Query */

  const { data: permisionOptions } = useQuery({
    queryKey: ['alarm:common/permision', i18n.language === 'en'],
    queryFn: async () => {
      try {
        return [{ key: '-', value: '-' }];
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    initialData: [],
    enabled: true,
  });

  const countryOptions: { key: string; value: string }[] = [
    {
      key: '1',
      value: '1',
    },
    {
      key: '2',
      value: '2',
    },
  ];

  const sendPageData: EquipmentType.AlarmReceiverColumnProps[] = [];

  /** useEffect */
  useEffect(() => {
    setSendPageList(sendPageData);
  }, [sendPageData]);

  /** Event */

  const handleAddClick = () => {
    if (name.length == 0) {
      alert(`${t('InvalidValue')} (name)`);
      return;
    }
    if (/^\d*$/.test(mobileNo) == false) {
      alert(`${t('InvalidValue')} (mobile)`);
      return;
    }
    if (
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email) == false
    ) {
      alert(`${t('InvalidValue')} (email)`);
      return;
    }

    setSendPageList((prevItems) => [
      ...prevItems,
      {
        no: (prevItems.length + 1).toString(),
        permission:
          permisionOptions.find((option) => option.key === permisionSelKey)
            ?.value ?? '',
        userId: '',
        country:
          countryOptions.find((option) => option.key === countrySelKey)
            ?.value ?? '',
        mobile: mobileNo ?? '',
        email: email ?? '',
        name: name ?? '',
        delete: 'Delete',
      },
    ]);
  };

  const handleDelClick = (row: EquipmentType.AlarmReceiverColumnProps) => {
    setSendPageList((prevItems) => {
      //해당 값을 가진 항목을 삭제
      const filteredItems = prevItems.filter((item) => item.no !== row.no);
      //삭제 후, 번호 갱신
      return filteredItems.map((item, index) => {
        //추가된 항목만 대상 (delete 가능 항목)
        if (item.delete.length > 0) {
          return {
            ...item,
            no: (index + 1).toString(),
          };
        }
        return item;
      });
    });
  };

  const handleSend = () => {
    let gubun = '';
    switch (selectedValue) {
      case '2':
        gubun = 'E'; //E-Email
        break;
      case '1':
        gubun = 'S'; //S-SMS
        break;
      case '3':
        gubun = 'B'; //B-Both
        break;
      case '4':
        gubun = 'A'; //A-App
        break;
    }

    if (gubun.length == 0) {
      return;
    }

    if (content.length == 0) {
      return;
    }
  };

  return (
    <Layout isOpen={isOpen}>
      <div>
        <div>
          <div>
            <div>{t('AlarmMessageNotice')}</div>
          </div>
          <img src={close_popup} onClick={onClose} />
        </div>
        <div className={'space-y-6'}>
          <SearchItemContainer className="w-full justify-start">
            <SearchLabel>{t('NotificationMethod')}</SearchLabel>
            <Radio
              options={options}
              value={selected}
              onValueChange={setSelected}
            />
          </SearchItemContainer>
          <hr />
          <SearchItemContainer className={'w-full justify-start gap-5'}>
            <SearchLabel>{t('EquipmentInfo')}</SearchLabel>
            <SearchLabel>
              {t('Model')} {/* 모델 */}
            </SearchLabel>
            <SearchLabel>{row.smodel}</SearchLabel>{' '}
            <SearchLabel>
              {t('MachineID')} {/* 호기 */}
            </SearchLabel>
            <SearchLabel>{row.hogiNo}</SearchLabel>
            <SearchLabel>
              {t('SerialNo')} {/* 관리번호 */}
            </SearchLabel>
            <SearchLabel>{serialNo}</SearchLabel>{' '}
            <SearchLabel>
              {t('Mileage')} {/* Mileage */}
            </SearchLabel>
            <SearchLabel>{row.mileage}</SearchLabel>
          </SearchItemContainer>
          <SearchItemContainer>
            <SearchLabel>{t('AlarmMessage')}</SearchLabel>
            <SearchLabel>{row.explain}</SearchLabel>
          </SearchItemContainer>
          <div>
            <SearchLabel>{t('RecipientC')}</SearchLabel>
          </div>
          <div>
            <CommonTable
              data={sendPageList}
              columns={AlarmReceiverTableColumn}
              isPagination={false}
            />
          </div>
          <div>
            <SearchLabel>{t('AddRecipient')}</SearchLabel>
          </div>
          <div>
            <img src={additem} />
            <button>
              <img src={receiver} onClick={handleAddClick} />
            </button>
            <DropDown
              options={permisionOptions}
              placeholder={permisionSelKey}
              onSelKey={(key) => {
                setPermisionSelKey(key);
              }}
            ></DropDown>
            <DropDown
              options={countryOptions}
              placeholder={countrySelKey}
              onSelKey={(key) => {
                setCountrySelKey(key);
              }}
            ></DropDown>
            <SearchItemContainer className={'gap-3'}>
              <SearchLabel>{t('Name')}</SearchLabel>
              <Input
                placeholder={t('Name')}
                value={name}
                onChange={(e) => {
                  setName(e.target.value);
                }}
              />
            </SearchItemContainer>
            <SearchItemContainer className={'gap-3'}>
              <SearchLabel>{t('Phone')}</SearchLabel>
              <Input
                placeholder={t('Phone')}
                value={mobileNo}
                onChange={(e) => {
                  setMobileNo(e.target.value);
                }}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('EMail')}</SearchLabel>
              <Input
                placeholder={t('EMail')}
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                }}
              />
            </SearchItemContainer>
          </div>
          <div>
            <SearchLabel className={'w-24'}>{t('Content')}</SearchLabel>
            <textarea
              onChange={(e) => {
                setContent(e.target.value);
              }}
              value={`Check: ${row.smodel}(${row.hogiNo})\n${row.explain}`}
            />
          </div>
        </div>

        <div>
          <div>
            <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
            <Button
              variant={'bt_primary'}
              label={'Send'}
              onClick={handleSend}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AlarmPopup;
