import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout.tsx';
import close_popup from '@/assets/images/etc/close_popup.png';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { ColumnDef } from '@tanstack/react-table';
import DropDown from '@/Common/Components/common/DropDown.tsx';
import { Button } from '@/Common/Components/common/Button.tsx';
import CommonTable from '@/Common/Components/common/CommonTable.tsx';
import { useEffect, useState } from 'react';
import { EquipmentType } from '@/types/EquipmentType.ts';

const TSGListPopup = ({
  spn,
  fmi,
  onClose,
  isOpen,
}: EquipmentType.TSGListPopupProps) => {
  const { t } = useTranslation();

  const TSGListTableColumn: ColumnDef<EquipmentType.TSGListTableColumnProps>[] =
    [
      {
        size: 112,
        header: () => <div>{t('HCESPN')}</div>,
        accessorKey: 'hcespn',
        cell: ({ row }) => <div>{row.original.hcespn}</div>,
      },
      {
        size: 112,
        header: () => <div>{t('FMI')}</div>,
        accessorKey: 'fmi',
        cell: ({ row }) => <div>{row.original.fmi}</div>,
      },
      {
        size: 232,
        header: () => <div>{t('ModelGroup')}</div>,
        accessorKey: 'model',
        cell: ({ row }) => <div>{row.original.model}</div>,
      },
      {
        size: 656,
        header: () => <div>{t('TSGTitle')}</div>,
        accessorKey: 'maker',
        cell: ({ row }) => <div>{row.original.tsg}</div>,
      },
    ];

  //타입
  const [typeOptions, setTypeOptions] = useState([
    { key: 'All', value: 'All' },
  ]);
  const [typeSelKey, setTypeSelKey] = useState('All');
  //모델그룹명
  const [modelGroupOptions] = useState([{ key: 'All', value: 'All' }]);
  const [modelGroupSelKey, setModelGroupSelKey] = useState('All');
  //모델
  const [modelOptions] = useState([{ key: 'All', value: 'All' }]);
  const [modelSelKey, setModelSelKey] = useState('All');

  /** Query */

  const [, setTsgListParams] = useState({
    spn: '',
    fmi: '',
    sModel: '',
    tsgType: '',
    pageNum: 0,
    pageSize: 0,
  });

  const tsgListData = {
    pageNum: 0,
    pageSize: 0,
    totalCnt: 0,
    rows: [],
  };

  /** useEffect */

  useEffect(() => {
    setTypeOptions([
      { key: 'All', value: 'All' },
      { key: 'MCU', value: 'MCU' },
      { key: 'ECM', value: 'ECM' },
      { key: 'AAVM', value: 'AAVM' },
      { key: 'CMCU', value: 'CMCU' },
      { key: 'EHCU', value: 'EHCU' },
      { key: 'TCU', value: 'TCU' },
      { key: 'CNH_MCU', value: 'CNH_MCU' },
      { key: 'INVERTER', value: 'INVERTER' },
      { key: 'LIB_PACK', value: 'LIB_PACK' },
      { key: 'OBC', value: 'OBC' },
      { key: 'QC', value: 'QC' },
      { key: 'PUMP', value: 'PUMP' },
      { key: 'TRACTION', value: 'TRACTION' },
      { key: 'LEFT_MASTER', value: 'LEFT_MASTER' },
      { key: 'LEFT_SLAVE', value: 'LEFT_SLAVE' },
    ]);

    handleSearch();
  }, []);

  const handleSearch = () => {
    setTsgListParams({
      spn: spn,
      fmi: fmi,
      sModel:
        modelOptions.find((option) => option.key === modelSelKey)?.value ?? '',
      tsgType:
        typeOptions.find((option) => option.key === typeSelKey)?.value ?? '',
      pageNum: 1,
      pageSize: 5,
    });
  };

  return (
    <Layout isOpen={isOpen}>
      <div>
        <div>
          <div>
            <div>{t('TSGList')}</div>
          </div>
          <img src={close_popup} onClick={onClose} />
        </div>
        <div>
          <SearchItemContainer>
            <SearchLabel>{t('Type')}</SearchLabel>
            <DropDown
              options={typeOptions}
              placeholder={typeSelKey}
              onSelKey={setTypeSelKey}
            />

            <SearchLabel>{t('ModelGroup')}</SearchLabel>
            <DropDown
              options={modelGroupOptions}
              placeholder={modelGroupSelKey}
              onSelKey={setModelGroupSelKey}
            />

            <SearchLabel>{t('Model')}</SearchLabel>
            <DropDown
              options={modelOptions}
              placeholder={modelSelKey}
              onSelKey={setModelSelKey}
            />

            <SearchItemContainer>
              <Button
                variant={'bt_primary'}
                label={'Search'}
                onClick={handleSearch}
              />
            </SearchItemContainer>
          </SearchItemContainer>

          <CommonTable
            data={tsgListData.rows}
            columns={TSGListTableColumn}
            isPagination={true}
            customPageSize={tsgListData.pageSize}
            totalCount={tsgListData.totalCnt}
            currentPage={tsgListData.pageNum}
            onPageChange={(page: number) => {
              setTsgListParams((prevState) => ({
                ...prevState,
                pageNum: page,
              }));
            }}
          />
        </div>
      </div>
    </Layout>
  );
};

export default TSGListPopup;
