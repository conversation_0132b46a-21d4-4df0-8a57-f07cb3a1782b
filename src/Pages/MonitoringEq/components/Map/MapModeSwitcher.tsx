import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';
import { Tooltip } from '@radix-ui/themes';
import MapIcon from '@/assets/images/svg/28/Map.tsx';
import Tracking from '@/assets/images/svg/28/Tracking';

const MapModeSwitcher: React.FC<{
  className?: string;
  onChange?: (mode: 'vehicle' | 'tracking') => void;
}> = ({ className = '', onChange }) => {
  const { t } = useTranslation();

  const [selected, setSelected] = useState<'vehicle' | 'tracking'>('vehicle');

  const handleSelect = (mode: 'vehicle' | 'tracking') => {
    setSelected(mode);
    onChange?.(mode);
  };

  return (
    <div
      className={`${className} bg-white f-c-c rounded-10 absolute top-[94px] right-[30px] z-10 cursor-pointer [&_svg]:m-[10px]`}
    >
      <Tooltip content={t('EquipmentStatusMap')} side="bottom">
        <div onClick={() => handleSelect('vehicle')}>
          <MapIcon
            className={`
              ${selected === 'vehicle' ? '[&_path]:stroke-secondary-6' : ''}
            `}
          />
        </div>
      </Tooltip>
      <div className="divider-v mx-0" />
      <Tooltip content={t('TrackingMap')} side="bottom">
        <div onClick={() => handleSelect('tracking')}>
          <Tracking
            className={`
              ${selected === 'tracking' ? '[&_path]:stroke-secondary-6' : ''}
            `}
          />
        </div>
      </Tooltip>
    </div>
  );
};

export default MapModeSwitcher;
