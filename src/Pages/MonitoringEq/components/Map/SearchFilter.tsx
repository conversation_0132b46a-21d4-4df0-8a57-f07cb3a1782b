import React from 'react';
import VehicleSearchFilter from './VehicleSearchFilter';
import TrackingSearchFilter from './TrackingSearchFilter';
import { EquipmentType } from '@/types/EquipmentType';

interface SearchFilterProps {
  mode?: 'vehicle' | 'tracking';
  left?: string;
  style?: React.CSSProperties;
  filteredSort: string | null;
  onResult: (result: EquipmentType.FilteredMapItem[]) => void;
}

/**
 * 장비 필터 조회
 */
const SearchFilter: React.FC<SearchFilterProps> = ({
  mode,
  left,
  style,
  filteredSort,
  onResult,
}) => {
  if (mode === 'tracking') {
    return (
      <TrackingSearchFilter
        left={left}
        style={style}
        filteredSort={filteredSort}
        onResult={(result) => {
          onResult?.(result);
        }}
      />
    );
  } else {
    return (
      <VehicleSearchFilter
        left={left}
        style={style}
        onResult={(result) => {
          onResult?.(result);
        }}
      />
    );
  }
};
SearchFilter.displayName = 'SearchFilter';
export default SearchFilter;
