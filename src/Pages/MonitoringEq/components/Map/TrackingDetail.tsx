import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { DemoTest, EqBreakdownStatus, EqOperationStatus } from '@/types';
import { getEqStatIcon } from '@/Common/function/functions.ts';
import H2Title from '@/Common/Components/common/H2Title';
import arrowRight from '@/assets/images/ic/24/arrow_right.svg';
import arrowLeft from '@/assets/images/arrow/arrow_left.png';
import TrackingTruck from '@/assets/images/ic/24/tracking_truck.svg';
import { useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { generateTrackingDetailData } from '@/helpers/monitoringDataGenerator';
import { EquipmentType } from '@/types/EquipmentType';

type TrackingDetailItem = {
  id: string;
  operationStatus: EqOperationStatus;
  breakdownStatus: EqBreakdownStatus;
  equipmentId: string;
  modelName: string;
  plateNo: string;
  driver: string;
  phoneNum: string;
  mileage: string;
  dispatchInfo: EquipmentType.DispatchInfo[];
};

// 트래킹 상세정보
interface TrackingDetailProps {
  item: EquipmentType.FilteredMapItem;
  onTrackingPath: (dispatchInfo: EquipmentType.DispatchInfo[]) => void;
  onClose: () => void;
}

const TrackingDetail = ({
  item,
  onTrackingPath,
  onClose,
}: TrackingDetailProps) => {
  const { t } = useTranslation();

  /** useQuery */
  const { data: trackingInfo } = useQuery<TrackingDetailItem | null>({
    queryKey: ['monitoring/trackingdetail'],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return generateTrackingDetailData();
      } else {
        try {
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    //initialData: {},
    enabled: true,
  });

  /** useEffect */
  useEffect(() => {
    if (trackingInfo && onTrackingPath) {
      onTrackingPath(trackingInfo.dispatchInfo);
    }
  }, [trackingInfo]);

  return (
    <div className="w-[360px] h-[calc(100%-74px)] bg-white absolute bottom-0 left-0 z-20">
      {/* 상단: 제목 + 장비 기본 정보 */}
      <div className="flex w-full">
        <button
          onClick={(e) => {
            e.stopPropagation();
            onClose();
          }}
          className="ml-2"
        >
          <img src={arrowLeft} alt="close" />
        </button>
        <div style={{ flex: 1 }}>
          <H2Title>{t('EquipmentDetails')}</H2Title>
        </div>
      </div>

      <div className="h-full py-1 px-2">
        <div className="h-[calc(100%-65px)] overflow-y-scroll [&>div]:mt-4 [&>div]:mx-3 [&>div]:pb-4 [&>div]:border-b [&>div:last-child]:border-0 [&>div]:border-gray-6">
          {/* 차량 정보 */}
          <div>
            <Link
              to={'/eq_list'}
              state={{ equipmentId: trackingInfo?.equipmentId }}
              className="mb-3 f-c-b"
            >
              <div className="f-c gap-[10px]">
                <h2 className="subhead2">{trackingInfo?.modelName}</h2>
                <img
                  src={getEqStatIcon(
                    trackingInfo?.operationStatus,
                    trackingInfo?.breakdownStatus,
                  )}
                  alt="equipment status"
                />
              </div>
              <img src={arrowRight} alt="arrow_right" />
            </Link>
            <div className="space-y-1 [&>div]:f-c [&>div]:gap-[10px] [&_h3]:flex-shrink-0 [&_h3]:body4 [&_h3]:text-gray-10 [&_p]:caption2">
              <div key="VehicleNumber">
                <h3>{t('VehicleNumber')}</h3>
                <p>{trackingInfo?.plateNo ?? '-'}</p>
              </div>
              <div key="Driver">
                <h3>{t('Driver')}</h3>
                <p>{trackingInfo?.driver ?? '-'}</p>
              </div>
              <div key="PhoneNumber">
                <h3>{t('PhoneNumber')}</h3>
                <p>{trackingInfo?.phoneNum ?? '-'}</p>
              </div>
              <div key="Mileage">
                <h3>{t('Mileage')}</h3>
                <p>{trackingInfo?.mileage ?? '-'}</p>
              </div>
            </div>
          </div>

          {/* 배차 섹션 */}
          <div>
            {/*  */}
            <div className="mb-6 f-c-b gap-[34px]">
              <h2 className="f-c gap-2 subtitle4">
                <img src={TrackingTruck} alt="tracking truck" />
                {t('DispatchInfortmation')}
              </h2>
              <p className="f-c gap-[6px]">
                <span className="caption3">{t('TotalTasks')}</span>
                <span className="subtitle5">
                  {trackingInfo?.dispatchInfo?.length || 0}
                </span>
              </p>
            </div>

            {/* 배차 정보 */}
            {trackingInfo?.dispatchInfo?.map((info, idx) => (
              <div
                key={idx}
                className={[
                  'tracking-line',
                  info.completed && 'active',
                  idx > 0 &&
                    trackingInfo?.dispatchInfo[idx - 1].completed &&
                    !info.completed &&
                    'prev-completed',
                ]
                  .filter(Boolean)
                  .join(' ')}
              >
                <span />
                <div className="destination-item">
                  <h2>{info.name} Destination</h2>
                  <p>
                    <em>{t('DeliveryAddress')}</em>
                    {info.address}
                  </p>
                  <p>
                    <span>{t('CompletionTime')}</span>
                    {info.time ? info.time : '-'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrackingDetail;
