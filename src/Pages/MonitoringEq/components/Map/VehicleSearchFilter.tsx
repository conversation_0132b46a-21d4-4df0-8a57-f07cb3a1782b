import { useTranslation } from 'react-i18next';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Fragment } from 'react';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { Popover } from '@radix-ui/themes';
import H2Title from '@/Common/Components/common/H2Title';
import { Button } from '@/Common/Components/common/Button';
import Input from '@/Common/Components/common/Input';
import { fleetApi, countryApi, dealerApi, equipmentMonitoringApi } from '@/api';
import {
  AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoringRequest,
  AdminEquipmentStatusResDTOBreakdownStatusEnum,
  AdminEquipmentStatusResDTOGpsStatusEnum,
  AdminEquipmentStatusResDTOOperationStatusEnum,
  GetAdminEquipmentPageForMonitoringBreakdownStatusEnum,
  GetAdminEquipmentPageForMonitoringOperationStatusEnum,
} from '@/api/generated';
import { DemoTest, DropdownOption } from '@/types';
import { useForm } from 'react-hook-form';
import { AccordionContent, AccordionTrigger } from '@radix-ui/react-accordion';
import * as Accordion from '@radix-ui/react-accordion';
import CheckBadge from '@/Common/Components/common/CheckBadge';
import Filter from '@/assets/images/svg/etc/Filter.tsx';
import arrow from '@/assets/images/arrow/arrow_down.svg';
import refresh from '@/assets/images/ic/24/refresh.svg';
import { generateMapData } from '@/helpers/monitoringDataGenerator';
import { EquipmentType } from '@/types/EquipmentType';
import DropDownPaginated from '@/Common/Components/common/DropDownPaginated';

type FormValues = {
  fleet: { key: string; value: string };
  modelName: string;
  plateNo: string;
  detailOption: boolean;
  country: { key: string; value: string };
  dealer: { key: string; value: string };
  eqStat: {
    all: boolean;
    inoperation: boolean; //운행 중
    idle: boolean; //유휴
    fault: boolean; //고장
    maint: boolean; //정비중
    overdue: boolean; //소모품 교체 초과
  };
};

interface VehicleSearchFilterProps {
  left?: string;
  style?: React.CSSProperties;
  onResult: (result: EquipmentType.FilteredMapItem[]) => void;
}

/**
 * 장비 필터 조회
 */
const VehicleSearchFilter: React.FC<VehicleSearchFilterProps> = ({
  left = 'left-5',
  style,
  onResult,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      modelName: '',
      plateNo: '',
      serialNo: '',
    },
  });
  const { t, i18n } = useTranslation();
  const [open, setOpen] = useState(false);
  const [openDetail, setOpenDetail] = useState(false);
  const refDiv = useRef<HTMLDivElement>(null);
  const [searchParams, setSearchParams] =
    useState<AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoringRequest>();

  const [formValues, setFormValues] = useState<FormValues>({
    fleet: { key: '', value: '' },
    modelName: '',
    plateNo: '',
    detailOption: false,
    country: { key: '', value: '' },
    dealer: { key: '', value: '' },
    eqStat: {
      all: true,
      inoperation: false,
      idle: false,
      fault: false,
      maint: false,
      overdue: false,
    },
  });

  const statusFilters = [
    {
      key: 'all',
      name: t('All'),
      checked: formValues.eqStat.all,
    },
    {
      key: 'inoperation',
      name: t('InOperation'),
      checked: formValues.eqStat.inoperation,
    },
    {
      key: 'idle',
      name: t('Idle'),
      checked: formValues.eqStat.idle,
    },
    {
      key: 'fault',
      name: t('FaultK'),
      checked: formValues.eqStat.fault,
    },
    {
      key: 'maint',
      name: t('Maint'),
      checked: formValues.eqStat.maint,
    },
    {
      key: 'overdue',
      name: t('Overdue'),
      checked: formValues.eqStat.overdue,
    },
  ];

  /** useQuery */
  const fleetOptions = useCallback(
    async (page: number): Promise<DropdownOption[]> => {
      if (DemoTest.isRandomOn(false)) {
        return page === 0 ? [{ key: t('AllFleets'), value: '' }] : [];
      }

      // 실제 API 호출
      try {
        const response = await fleetApi.getAdminFleetPage({
          page: page,
          size: 100,
          sort: 'fleetName,asc',
        });

        const result = response?.data?.content ?? [];

        const options = result
          .filter((item) => item.fleetName && item.fleetId !== undefined)
          .map((item) => ({
            key: item.fleetName!,
            value: item.fleetId!.toString(),
          }));

        // 첫 페이지에만 "All" 옵션 추가
        return page === 0
          ? [{ key: t('AllFleets'), value: '' }, ...options]
          : options;
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    [],
  );

  const countryOptions = useCallback(
    async (page: number): Promise<DropdownOption[]> => {
      if (DemoTest.isRandomOn(false)) {
        return page === 0
          ? [
              { key: t('AllCountries'), value: '' },
              { key: 'KOR', value: '1' },
              { key: 'USA', value: '2' },
            ]
          : [];
      }

      // 실제 API 호출
      try {
        const response = await countryApi.getAdminCountryPage({
          page: page,
          size: 100,
          sort: 'countryName,asc',
        });

        const result = response?.data?.content ?? [];

        const options = result
          .filter((item) => item.countryName && item.countryId !== undefined)
          .map((item) => ({
            key: item.countryName!,
            value: item.countryId!.toString(),
          }));

        // 첫 페이지에만 "All" 옵션 추가
        return page === 0
          ? [{ key: t('AllCountries'), value: '' }, ...options]
          : options;
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    [],
  );

  const dealerOptions = useCallback(
    async (page: number): Promise<DropdownOption[]> => {
      if (DemoTest.isRandomOn(false)) {
        return page === 0 ? [{ key: t('AllDealers'), value: '' }] : [];
      }

      // 국가가 선택되지 않은 경우 빈 배열과 "모든 딜러" 옵션만 반환
      if (!formValues.country.value || Number(formValues.country.value) <= 0) {
        return [{ key: t('AllDealers'), value: '' }];
      }

      // 실제 API 호출
      try {
        const response = await dealerApi.getAdminDealerPage({
          countryId: Number(formValues.country.value),
          page: page,
          size: 100,
          sort: 'dealerName,asc',
        });

        const result = response?.data?.content ?? [];

        const options = result
          .filter((item) => item.dealerName && item.dealerId !== undefined)
          .map((item) => ({
            key: item.dealerName!,
            value: item.dealerId!.toString(),
          }));

        // 첫 페이지에만 "All" 옵션 추가
        return page === 0
          ? [{ key: t('AllDealers'), value: '' }, ...options]
          : options;
      } catch (error) {
        console.error('API 호출 에러:', error);
        return [{ key: t('AllDealers'), value: '' }];
      }
    },
    [formValues.country.value],
  );

  const { data: filteredMapItems } = useQuery<EquipmentType.FilteredMapItem[]>({
    queryKey: ['/api/admin/equipment/monitoring/page', searchParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return generateMapData(128);
      } else {
        try {
          const response =
            await equipmentMonitoringApi.getAdminEquipmentPageForMonitoring({
              ...searchParams,
            });

          if (!response.data) {
            return [];
          } else {
            return (
              response.data.content?.map((data, idx) => ({
                id: `${data.equipmentId?.toString()}-${idx.toString()}`,
                equipmentId: data.equipmentId?.toString() ?? '',
                equipmentType: data.equipmentType ?? 'TRUCK',
                vehicleType: data.vehicleType ?? 'TRUCK',
                manufacturer: data.manufacturer ?? 'Unknown',
                modelName: data.modelName ?? 'Unknown',
                trimName: data.trimName ?? 'Unknown',
                productYear: data.productYear ?? 0,
                plateNo: data.plateNo?.toString() ?? '',
                vinNumber: data.serialNo?.toString() ?? '',
                latlng: {
                  lat: data.status?.recentLocation?.y ?? 0.0,
                  lng: data.status?.recentLocation?.x ?? 0.0,
                },
                mileage: data.status?.mileage ?? 0,
                operationStatus: {
                  running:
                    data.status?.operationStatus ==
                    AdminEquipmentStatusResDTOOperationStatusEnum.Running,
                  idle:
                    data.status?.operationStatus ==
                    AdminEquipmentStatusResDTOOperationStatusEnum.Idle,
                },
                gpsStatus: {
                  connected:
                    data.status?.gpsStatus ==
                    AdminEquipmentStatusResDTOGpsStatusEnum.Connected,
                  disconnected:
                    data.status?.gpsStatus ==
                    AdminEquipmentStatusResDTOGpsStatusEnum.Disconnected,
                },
                breakdownStatus: {
                  breakdown:
                    data.status?.breakdownStatus ==
                    AdminEquipmentStatusResDTOBreakdownStatusEnum.Breakdown,
                  repairing:
                    data.status?.breakdownStatus ==
                    AdminEquipmentStatusResDTOBreakdownStatusEnum.Repairing,
                  none:
                    data.status?.breakdownStatus ==
                    AdminEquipmentStatusResDTOBreakdownStatusEnum.None,
                },
                driver: {
                  id: data.driver?.driverId?.toString() ?? '',
                  name: data.driver?.driverName ?? '',
                  countryDialCode: data.driver?.driverCountryDialCode ?? '',
                  phone: data.driver?.driverPhone ?? '',
                },
                dealer: {
                  id: data.dealer?.dealerId?.toString() ?? '',
                  name: data.dealer?.dealerName ?? '',
                  countryDialCode: data.dealer?.dealerCountryDialCode ?? '',
                  phone: data.dealer?.dealerPhone ?? '',
                },
                serviceCenter: {
                  id: data.serviceCenter?.serviceCenterId?.toString() ?? '',
                  name: data.serviceCenter?.serviceCenterName ?? '',
                  countryDialCode:
                    data.serviceCenter?.serviceCenterCountryDialCode ?? '',
                  phone: data.serviceCenter?.serviceCenterPhone ?? '',
                  address: data.serviceCenter?.serviceCenterAddress ?? '',
                },
                location: '',
                lastUpdate: '',
              })) ?? []
            );
          }
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: !!searchParams,
    initialData: [],
  });

  /** useEffect */
  // useEffect(() => {
  //   if (DemoTest.isRandomOn()) {
  //     setSearchParams({
  //       fleetId: undefined,
  //       modelName: '',
  //       plateNo: '',
  //       countryId: undefined,
  //       dealerId: undefined,
  //     });
  //   }
  // }, []);

  useEffect(() => {
    setSearchParams({
      fleetId: undefined,
      modelName: '',
      plateNo: '',
      countryId: undefined,
      dealerId: undefined,
    });
  }, []);

  useEffect(() => {
    onResult?.(filteredMapItems);
  }, [filteredMapItems]);

  const onSearch = () => {
    let operationStatus:
      | GetAdminEquipmentPageForMonitoringOperationStatusEnum
      | undefined = undefined;
    // operationStatus
    {
      if (
        formValues.eqStat.idle == false &&
        formValues.eqStat.inoperation == true
      ) {
        operationStatus =
          GetAdminEquipmentPageForMonitoringOperationStatusEnum.Running;
      }
      if (
        formValues.eqStat.idle == true &&
        formValues.eqStat.inoperation == false
      ) {
        operationStatus =
          GetAdminEquipmentPageForMonitoringOperationStatusEnum.Idle;
      }
    }

    let breakdownStatus:
      | GetAdminEquipmentPageForMonitoringBreakdownStatusEnum
      | undefined = undefined;
    // breakdownStatus
    {
      if (formValues.eqStat.fault == true && formValues.eqStat.maint == false) {
        breakdownStatus =
          GetAdminEquipmentPageForMonitoringBreakdownStatusEnum.Breakdown;
      }
      if (formValues.eqStat.fault == false && formValues.eqStat.maint == true) {
        breakdownStatus =
          GetAdminEquipmentPageForMonitoringBreakdownStatusEnum.Repairing;
      }
    }

    let existBreakdown: boolean | undefined = undefined;
    if (formValues.eqStat.fault == true) {
      existBreakdown = true;
    }

    setSearchParams({
      fleetId:
        formValues.fleet.value == ''
          ? undefined
          : Number(formValues.fleet.value),
      modelName: formValues.modelName,
      plateNo: formValues.plateNo,
      countryId:
        formValues.country.value == ''
          ? undefined
          : Number(formValues.country.value),
      dealerId:
        formValues.dealer.value == ''
          ? undefined
          : Number(formValues.dealer.value),
      operationStatus: operationStatus,
      //gpsStatus: gpsStatus,
      breakdownStatus: breakdownStatus,
    });
  };

  const getX = () => {
    if (left.includes('left-[')) {
      // left-[380px] 등에서 숫자만 추출
      const match = left.match(/left-\[(\d+)px\]/);
      if (match) return Number(match[1]);
    }
    if (left === 'left-5') return 20;
    return 0;
  };

  return (
    <Popover.Root open={open} onOpenChange={setOpen}>
      <Popover.Trigger>
        <motion.div
          initial={false}
          animate={{ x: getX() }}
          transition={{ duration: 0.4, ease: 'easeInOut' }}
          className={`
            p-2 rounded-xl absolute z-10 group cursor-pointer transition-colors duration-[30ms]
            ${open ? 'bg-secondary-6 [&_path]:stroke-white [&_circle]:stroke-white' : 'bg-white hover:bg-secondary-0'}
          `}
          style={{
            ...style,
            left: 0,
            zIndex: 10,
            position: 'absolute',
          }}
          tabIndex={0}
          role="button"
        >
          <Filter />
        </motion.div>
      </Popover.Trigger>
      <Popover.Content
        size="1"
        minWidth="213px"
        maxWidth="288px"
        className={'!p-0 !pb-6 py-5 px-6 bg-white rounded-lg shadow-custom'}
      >
        <Fragment>
          <H2Title>{t('Filter')}</H2Title>
          <form onSubmit={handleSubmit(onSearch)}>
            <div className={'py-5 px-6 space-y-[10px]'}>
              {/* 플릿 */}
              <DropDownPaginated
                className="w-[240px]"
                loadOptions={fleetOptions}
                placeholder={t('AllFleets')}
                selectedKey={formValues.fleet.key}
                onSelPair={(key: string, value: string) => {
                  setFormValues((prev) => ({
                    ...prev,
                    fleet: { key, value },
                  }));
                }}
              />
              {/* 모델명 */}
              <Input
                placeholder={t('ModelName')}
                value={formValues.modelName}
                {...register('modelName', {
                  maxLength: {
                    value: 50,
                    message: 'Maximum 50 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.modelName?.message}
                onChange={(e) => {
                  setFormValues((prev) => ({
                    ...prev,
                    modelName: e.target.value,
                  }));
                }}
              />
              {/* 차량 번호 */}
              <Input
                placeholder={t('EquipmentNumber')}
                value={formValues.plateNo}
                {...register('plateNo', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.plateNo?.message}
                onChange={(e) => {
                  setFormValues((prev) => ({
                    ...prev,
                    plateNo: e.target.value,
                  }));
                }}
              />
              {/* 관리 번호 */}
              {/* <Input
                placeholder={t('AssetID')}
                value={formValues.serialNo}
                {...register('serialNo', {
                  maxLength: {
                    value: 30,
                    message: 'Maximum 30 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.serialNo?.message}
                onChange={(e) => {
                  setFormValues((prev) => ({
                    ...prev,
                    serialNo: e.target.value,
                  }));
                }}
              /> */}
              {/* 상세 필터 */}
              <Accordion.Root
                className="AccordionRoot"
                type="single"
                collapsible
                onValueChange={(value) => {
                  const isOpen = value === 'item-1';
                  setOpenDetail(isOpen);
                }}
              >
                <Accordion.Item className="AccordionItem" value="item-1">
                  <AccordionContent className={'w-full pt-[6px] pb-4'}>
                    <div className={' space-y-[10px]'}>
                      {/* 국가 */}
                      <DropDownPaginated
                        loadOptions={countryOptions}
                        placeholder={t('AllCountries')}
                        selectedKey={formValues.country.key}
                        onSelPair={(key: string, value: string) => {
                          setFormValues((prev) => ({
                            ...prev,
                            country: { key, value },
                            dealer: { key: '', value: '' }, // 국가 변경 시 딜러 초기화
                          }));
                        }}
                        className="w-[240px]"
                      />
                      {/* 대리점 */}
                      <DropDownPaginated
                        key={`dealer-${formValues.country.value}`} // 국가 변경 시 리렌더링 강제
                        loadOptions={dealerOptions}
                        placeholder={t('AllDelarships')}
                        selectedKey={formValues.dealer.key}
                        onSelPair={(key: string, value: string) => {
                          setFormValues((prev) => ({
                            ...prev,
                            dealer: { key, value },
                          }));
                        }}
                        className="w-[240px]"
                      />
                      {/* 장비 상태 */}
                      <div className="body4 text-gray-10">
                        {t('EquipmentStatus')}
                      </div>
                      <div className="f-c flex-wrap gap-1">
                        {statusFilters.map((item) => (
                          <CheckBadge
                            key={item.key}
                            isChecked={item.checked}
                            onCheckedChange={(checked) => {
                              const newFilters = statusFilters.map((f) =>
                                f.key === item.key ? { ...f, checked } : f,
                              );
                              setFormValues((prev) => ({
                                ...prev,
                                eqStat: newFilters.reduce(
                                  (acc, cur) => ({
                                    ...acc,
                                    [cur.key]: cur.checked,
                                  }),
                                  {} as typeof prev.eqStat,
                                ),
                              }));
                            }}
                          >
                            {item.name}
                          </CheckBadge>
                        ))}
                      </div>
                    </div>
                  </AccordionContent>
                  <AccordionTrigger className={'w-full'}>
                    <div
                      ref={refDiv}
                      className={'w-full group'}
                      onClick={() => {
                        if (refDiv.current) {
                          if (refDiv.current.ariaChecked !== 'true') {
                            refDiv.current.ariaChecked = 'true';
                          } else {
                            refDiv.current.ariaChecked = 'false';
                          }
                        }
                      }}
                    >
                      <div className={'w-fit mx-auto f-c gap-[6px]'}>
                        <div className="body4 text-secondary-6">
                          {openDetail ? t('HideFilters') : t('ViewFilters')}
                        </div>
                        <img
                          src={arrow}
                          className={
                            'flex-shrink-0 transition-transform duration-300 ' +
                            (openDetail ? 'rotate-180' : '')
                          }
                          alt=""
                        />
                      </div>
                    </div>
                  </AccordionTrigger>
                </Accordion.Item>
              </Accordion.Root>
            </div>
            <div className={'px-4 f-c-b gap-[10px]'}>
              <Button
                variant={'bt_secondary'}
                label={
                  <div className="w-6 h-6 flex-shrink-0">
                    <img src={refresh} />
                  </div>
                }
                onClick={() => {
                  setFormValues({
                    fleet: { key: '', value: '' },
                    modelName: '',
                    plateNo: '',
                    detailOption: false,
                    country: { key: '', value: '' },
                    dealer: { key: '', value: '' },
                    eqStat: {
                      all: true,
                      inoperation: false,
                      idle: false,
                      fault: false,
                      maint: false,
                      overdue: false,
                    },
                  });
                }}
                className={'w-[56px]'}
              />
              <Button
                type="submit"
                variant={'bt_primary'}
                label={'Search'}
                className={'w-full'}
              />
            </div>
          </form>
        </Fragment>
      </Popover.Content>
    </Popover.Root>
  );
};
export default VehicleSearchFilter;
