import { useTranslation } from 'react-i18next';
import { <PERSON> } from 'react-router-dom';
import { getEqStatIcon } from '@/Common/function/functions.ts';
import arrowRight from '@/assets/images/ic/24/arrow_right.svg';
import { EquipmentType } from '@/types/EquipmentType';

// 장비 상세 맵 조회 결과 사이드 팝업 컴포넌트
const FilteredItem = (props: EquipmentType.FilterItemProps) => {
  const { t } = useTranslation();

  // Link 외 영역 클릭 이벤트 핸들러
  const handleItemClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Link 내부 클릭은 무시
    if ((e.target as HTMLElement).closest('a')) return;

    if (props.item) {
      props.onItemClick?.(props.item);
    }
  };

  return (
    <div
      className="mt-4 mx-3 pb-4 border-b border-gray-6"
      onClick={handleItemClick}
      style={{ cursor: 'pointer' }}
    >
      {/* 차량 리스트 Row */}
      <Link
        to={'/eq_list'}
        state={{ equipmentId: props.item?.equipmentId }}
        className="mb-3 f-c-b"
        onClick={(e) => e.stopPropagation()} // Link 클릭 시 부모 onClick 방지
      >
        <div className="f-c gap-[10px]">
          <h2 className="subhead2">{props.item?.modelName}</h2>
          <img
            src={getEqStatIcon(
              props.item?.operationStatus,
              props.item?.breakdownStatus,
            )}
            alt="장비 상태"
          />
        </div>
        <img src={arrowRight} alt="arrow_right" />
      </Link>

      {/*  */}
      <div className="p-[10px] space-y-1 bg-primary-0 rounded [&>div]:f-c [&>div]:gap-[10px] [&_h3]:flex-shrink-0 [&_h3]:body4 [&_h3]:text-gray-10 [&_p]:caption2">
        {/* 관리번호 정보 */}
        <div>
          <h3>{t('VehicleNumber')}</h3>
          <p>{props.item?.plateNo}</p>
        </div>
        {/* 기사 이름 */}
        <div>
          <h3>{t('Driver')}</h3>
          <p>{props.item?.driver.name}</p>
        </div>
        {/* 휴대폰 번호 */}
        <div>
          <h3>{t('PhoneNumber')}</h3>
          <p>{props.item?.driver.phone}</p>
        </div>
        {/* 작동 시간 정보 */}
        <div>
          <h3>{t('Mileage')}</h3>
          <p>{props.item?.mileage}</p>
        </div>
      </div>
    </div>
  );
};

export default FilteredItem;
