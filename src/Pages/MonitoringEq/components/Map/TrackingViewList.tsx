import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { Tabs } from '@radix-ui/themes';
import { motion } from 'framer-motion';
import TrackingAllTab from './TrackingAllTab';
import TrackingTransportTab from './TrackingTransportTab';
import { EquipmentType } from '@/types/EquipmentType';

export interface TrackingViewListProps {
  items: EquipmentType.FilteredMapItem[];
  onListItemClick?: (item: EquipmentType.FilteredMapItem) => void;
  onFilteredSort?: (item: string) => void;
}

const TrackingViewList = ({
  items,
  onListItemClick,
  onFilteredSort,
}: TrackingViewListProps) => {
  const { t } = useTranslation();
  const [value, setValue] = useState<'All' | 'VehicleInfo'>('All');

  return (
    <motion.div
      key="tracking-list"
      initial={{ opacity: 1, x: -360 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 1, x: -360 }}
      transition={{ duration: 0.4, ease: 'easeInOut' }}
      className="w-[360px] h-[calc(100%-74px)] bg-white absolute bottom-0 left-0 overflow-hidden z-20"
    >
      <TrackingAllTab
        items={items}
        onListItemClick={onListItemClick}
        onFilteredSort={onFilteredSort}
      />
      {/* <Tabs.Root
        value={value}
        onValueChange={(val) => setValue(val as 'All' | 'VehicleInfo')}
        className="[&_span]:w-[180px]"
      >
        <Tabs.List className="tab-design">
          <Tabs.Trigger value={'All'}>
            <span>{t('All')}</span>
          </Tabs.Trigger>
          <Tabs.Trigger value={'VehicleInfo'}>
            <span>{t('VehicleInfo')}</span>
          </Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="All">
          <TrackingAllTab
            items={items}
            onListItemClick={onListItemClick}
            onFilteredSortItemClick={onFilteredSortItemClick}
            onFilteredTypeItemClick={onFilteredTypeItemClick}
          />
        </Tabs.Content>

        <Tabs.Content value="VehicleInfo">
          <TrackingTransportTab items={items} />
        </Tabs.Content>
      </Tabs.Root> */}
    </motion.div>
  );
};

export default TrackingViewList;
