/* Timeline.tsx */
import { useTranslation } from 'react-i18next';
import React, {
  Fragment,
  useCallback,
  useState,
  useRef,
  useEffect,
} from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { DemoTest } from '@/types';
import TimelineIcon from '@/assets/images/svg/etc/Timeline.tsx';
import TimelineBadge from '@/assets/images/svg/30/TimelineBadge.tsx';
import ZoomIn from '@/assets/images/ic/20/zoom_in.svg';
import ZoomOut from '@/assets/images/ic/20/zoom_out.svg';
import { useQuery } from '@tanstack/react-query';
import { EquipmentType } from '@/types/EquipmentType';

const Timeline = ({
  items,
  onHeightChange,
  isChange,
}: EquipmentType.TimelineProps) => {
  const { t } = useTranslation();
  const [zoomedIn, setZoomedIn] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const timelineRef = useRef<HTMLDivElement>(null);
  const BASE_BUTTON_BOTTOM = 30;
  const [timelineHeight, setTimelineHeight] = useState(0);

  useEffect(() => {
    if (isOpen && timelineRef.current) {
      const { height } = timelineRef.current.getBoundingClientRect();
      setTimelineHeight(height);
      onHeightChange?.(height);
    } else {
      setTimelineHeight(0);
      onHeightChange?.(0);
    }
  }, [isOpen, items, onHeightChange]);

  const getWidth = useCallback((dur: string) => {
    const text = (dur ?? '0 min').trim().toLowerCase();
    let minutes = 0;
    if (text.includes('h')) {
      const [hPart, mPart] = text.split('h').map((s) => s.trim());
      minutes += parseInt(hPart, 10) * 60;
      if (mPart) minutes += parseInt(mPart.replace('min', ''), 10);
    } else {
      minutes = parseInt(text.replace('min', ''), 10);
    }
    if (minutes <= 15) return 95;
    if (minutes <= 30) return 190;
    if (minutes <= 45) return 285;
    if (minutes <= 90) return 570;
    return 570;
  }, []);

  const zoomIn = () => setZoomedIn(true);
  const zoomOut = () => setZoomedIn(false);

  //** useQuery */
  const { data: timelineItems } = useQuery<EquipmentType.TimelineItem[]>({
    queryKey: ['/api/timelineItems'],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        const sampleItems: EquipmentType.TimelineItem[] = [
          {
            vehNum: 'TRK-1001',
            driver: 'Alice',
            address: '123 Main St',
            segments: [
              {
                estimatedTime: '30 min',
                arrivalTime: '09:15',
                destination: 'Warehouse A',
              },
              {
                estimatedTime: '45 min',
                arrivalTime: '10:00',
                destination: 'Warehouse B',
              },
            ],
          },
          {
            vehNum: 'TRK-1002',
            driver: 'Bob',
            address: '456 Elm St',
            segments: [
              {
                estimatedTime: '15 min',
                arrivalTime: '09:30',
                destination: 'Depot X',
              },
              {
                estimatedTime: '1h 10min',
                arrivalTime: '10:40',
                destination: 'Depot Y',
              },
            ],
          },
          {
            vehNum: 'TRK-1002',
            driver: 'Bob',
            address: '456 Elm St',
            segments: [
              {
                estimatedTime: '15 min',
                arrivalTime: '09:30',
                destination: 'Depot X',
              },
              {
                estimatedTime: '1h 10min',
                arrivalTime: '10:40',
                destination: 'Depot Y',
              },
            ],
          },
          {
            vehNum: 'TRK-1002',
            driver: 'Bob',
            address: '456 Elm St',
            segments: [
              {
                estimatedTime: '15 min',
                arrivalTime: '09:30',
                destination: 'Depot X',
              },
              {
                estimatedTime: '1h 10min',
                arrivalTime: '10:40',
                destination: 'Depot Y',
              },
              {
                estimatedTime: '1h 10min',
                arrivalTime: '11:40',
                destination: 'Depot Z',
              },
            ],
          },
        ];
        return sampleItems;
      } else {
        try {
          return [];
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    initialData: [],
    enabled: true,
  });

  const getX = () => (isChange ? 436 : 76);

  return (
    <article>
      {/* 타임라인 토글 버튼 */}
      <motion.div
        onClick={() => setIsOpen((prev) => !prev)}
        initial={{ y: 40, opacity: 0 }} // <- 시작점
        animate={{ x: getX(), y: 0, opacity: 1 }} // <- 동시에 x, y, opacity
        transition={{ duration: 0.4, ease: 'easeInOut' }}
        className={`
        py-[9px] px-2 f-c gap-1 rounded-xl absolute z-10 cursor-pointer
        transition-colors duration-[30ms]
        ${
          isOpen
            ? 'bg-secondary-6 [&_span]:text-white [&_path]:stroke-white [&_circle]:stroke-white'
            : 'bg-white hover:bg-secondary-0'
        }
      `}
        style={{
          left: 0,
          bottom:
            timelineHeight > 0
              ? `${timelineHeight + 12}px`
              : `${BASE_BUTTON_BOTTOM}px`,
        }}
        tabIndex={0}
        role="button"
      >
        <TimelineIcon />
        <span className="body3">{t('Timeline')}</span>
      </motion.div>

      {/* 타임라인 전체 (열렸을 때만) */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={timelineRef}
            key="timeline-content"
            initial={{ y: '100%', opacity: 1 }}
            animate={{
              y: 0,
              opacity: 1,
              width: isChange ? 'calc(100% - 360px)' : '100%',
            }}
            exit={{ y: '100%', opacity: 1 }}
            transition={{ duration: 0.4, ease: 'easeInOut' }}
            className="timeline"
          >
            {/* 타이틀 바 */}
            <div className="title-wrap">
              <div className="title">{t('VehicleInformation')}</div>
              <div className="title">
                {t('DeliveryInformation')}
                <div className="f-c gap-[2px]">
                  <img src={ZoomIn} alt="zoom in" onClick={zoomIn} />
                  <img src={ZoomOut} alt="zoom out" onClick={zoomOut} />
                </div>
              </div>
            </div>
            {/* 현황 */}
            <div className="card-wrap overflow-y-auto">
              {timelineItems.length === 0 ? (
                <Fragment>
                  <div className="card-section">
                    <div className="card-con">
                      <div className="info card-info"></div>
                    </div>
                    <div className="time-con">
                      <div className="info time-info"></div>
                    </div>
                  </div>
                </Fragment>
              ) : (
                timelineItems.map((item, idx) => (
                  <Fragment key={idx}>
                    <div className="card-section">
                      <div className="card-con">
                        <div className="info card-info">
                          <h2>
                            {item.vehNum}
                            <span className="divider-v h-[14px] mx-[10px]" />
                            <span>{item.driver}</span>
                          </h2>
                          <p>{item.address}</p>
                        </div>
                      </div>
                      <div className="time-con">
                        <div className="info time-info">
                          {item.segments.map((seg, sidx) => {
                            const isArrived = sidx < item.segments.length - 1;
                            const w = zoomedIn
                              ? 100
                              : getWidth(seg.estimatedTime);
                            const gap = w - 100;

                            return (
                              <div
                                key={sidx}
                                className="time-bar"
                                data-estimatedtime={seg.estimatedTime}
                                style={
                                  {
                                    '--bar-width': `${w}px`,
                                    marginRight:
                                      sidx < item.segments.length - 1
                                        ? `${gap}px`
                                        : undefined,
                                  } as React.CSSProperties
                                }
                              >
                                <TimelineBadge arrived={isArrived} />
                                <span>{seg.arrivalTime}</span>
                                <p>{seg.destination}</p>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  </Fragment>
                ))
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </article>
  );
};

export default Timeline;
