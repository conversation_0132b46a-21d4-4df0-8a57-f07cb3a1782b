import { VariableSizeList as List } from 'react-window';
import { Button } from '@/Common/Components/common/Button';
import { useEffect, useRef, useState, useCallback } from 'react';
import { DemoTest } from '@/types';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { generateTransportStatusData } from '@/helpers/monitoringDataGenerator';
import { EquipmentType } from '@/types/EquipmentType';

type DeliveryInfo = {
  id: string;
  branchName: string;
  fullAddress: string;
  deliveryOrder: string;
  arrivalTime: string;
  status: string;
};
type TransportStatus = {
  awaitingCount: number;
  deliveredCount: number;
  failedCount: number;
  inTransitCount: number;
  deliveryInfo: DeliveryInfo[];
};

interface TrackingTransportTabProps {
  items: EquipmentType.FilteredMapItem[];
}

const TrackingTransportTab = ({ items }: TrackingTransportTabProps) => {
  const { t } = useTranslation();

  const listContainer = useRef<HTMLDivElement>(null);
  const listRef = useRef<List>(null);
  const [width, setWidth] = useState(0);
  const [height, setHeight] = useState(0);
  const heightMap = useRef<{ [key: number]: number }>({});
  const getItemHeight = useCallback(
    (index: number) => heightMap.current[index] ?? 168,
    [],
  );

  /** useQuery */

  const { data: transportStatus } = useQuery<TransportStatus | null>({
    queryKey: ['/api/equipment/page', items],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return generateTransportStatusData();
      } else {
        try {
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: !!items,
  });

  useEffect(() => {
    if (!listContainer.current) return;
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setWidth(entry.contentRect.width);
        setHeight(entry.contentRect.height);
      }
    });
    observer.observe(listContainer.current);
    setWidth(listContainer.current.offsetWidth);
    setHeight(listContainer.current.offsetHeight);
    return () => observer.disconnect();
  }, []);

  const Row = ({
    index,
    style,
  }: {
    index: number;
    style: React.CSSProperties;
  }) => {
    const rowRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      const measuredHeight = rowRef.current?.getBoundingClientRect().height;
      if (measuredHeight && measuredHeight !== heightMap.current[index]) {
        heightMap.current[index] = measuredHeight;
        listRef.current?.resetAfterIndex(index);
      }
    }, [transportStatus?.deliveryInfo[index]]);

    const section = transportStatus?.deliveryInfo[index]!;

    return (
      <div
        ref={rowRef}
        style={{ ...style, height: 'auto', paddingBottom: '4px' }}
      >
        <div className="mt-4 mx-3 pb-4 border-b border-gray-6">
          <div className="mb-3 f-c-b">
            <h2 className="subhead2">{section.id}</h2>
          </div>
          <div
            className="
              space-y-1 [&>div]:f-c [&>div]:gap-[10px]
              [&_h3]:flex-shrink-0 [&_h3]:body4 [&_h3]:text-gray-10 [&_p]:caption2
            "
          >
            <div>
              <h3>{t('BranchName')}</h3>
              <p>{section.branchName}</p>
            </div>
            <div>
              <h3>{t('FullAddress')}</h3>
              <p>{section.fullAddress}</p>
            </div>
            <div>
              <h3>{t('ArrivalTime')}</h3>
              <p>{section.arrivalTime}</p>
            </div>
            <div>
              <h3>{t('DeliveryOrder')}</h3>
              <p>{section.deliveryOrder}</p>
            </div>
            <div>
              <h3>{t('Status')}</h3>
              <p className={'text-semantic-2'}>{section.status}</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* 다운로드 버튼 */}
      <div className="py-4 px-5 f-je border-b border-gray-6">
        <Button variant={'bt_tertiary_sm'} label="Download" />
      </div>
      {/* 운송 상태 */}
      <div
        className="
          f-c-c py-5 px-[10px] border-b border-gray-6
          [&>div]:w-1/2 [&>div]:f-c [&>div]:flex-col
          [&>div]:gap-1 [&>div]:border-r [&>div:last-child]:border-0
          [&>div]:border-primary-1 [&_em]:subtitle6 [&_i]:caption0
        "
      >
        <div>
          <em>{t('Pending')}</em>
          <i className={'text-secondary-6'}>
            {transportStatus?.awaitingCount ?? 0}
          </i>
        </div>
        <div>
          <em>{t('Delivered')}</em>
          <i className={'text-semantic-2'}>
            {transportStatus?.deliveredCount ?? 0}
          </i>
        </div>
      </div>
      {/* 운송 현황 */}
      <div className="py-1 pl-2 pr-1 [&>div]:border-b [&>div:last-child]:border-0 [&>div]:border-gray-6">
        <div
          ref={listContainer}
          className="h-[calc(100vh-280px)] py-1 overflow-y-scroll"
        >
          <List
            ref={listRef}
            width={width}
            height={height}
            itemCount={transportStatus?.deliveryInfo.length ?? 0}
            itemSize={getItemHeight}
            overscanCount={5}
          >
            {Row}
          </List>
        </div>
      </div>
    </>
  );
};

export default TrackingTransportTab;
