import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { equipmentMonitoringApi } from '@/api';
import { generateDetailInfoData } from '@/helpers/monitoringDataGenerator';
import { EquipmentType } from '@/types/EquipmentType';
import { DemoTest, EqBreakdownStatus, EqOperationStatus } from '@/types';
import { getEqStatIcon } from '@/Common/function/functions.ts';
import H2Title from '@/Common/Components/common/H2Title';
import arrowRight from '@/assets/images/ic/24/arrow_right.svg';
import arrowLeft from '@/assets/images/arrow/arrow_left.png';
import fault from '@/assets/images/ic/24/fault.svg';
import maint from '@/assets/images/ic/24/maint.svg';
import dealership from '@/assets/images/ic/24/dealership.svg';
import service from '@/assets/images/ic/24/service.svg';
import passenger from '@/assets/images/vehicle/passenger.svg';
import suvmpv from '@/assets/images/vehicle/suv_mpv.svg';
import truck from '@/assets/images/vehicle/truck.svg';
import bus from '@/assets/images/vehicle/bus.svg';
import trailer from '@/assets/images/vehicle/trailer.svg';
import low from '@/assets/images/vehicle/low.svg';
import motocycle from '@/assets/images/vehicle/motocycle.svg';

type VehicleDetailPage = {
  id: string;
  operationStatus: EqOperationStatus;
  breakdownStatus: EqBreakdownStatus;
  equipmentId: string;
  modelName: string;
  plateNo: string;
  driver: string | null;
  phoneNum: string | null;
  mileage: string;
  faultInfo?: {
    date?: string;
    status?: string;
    mileage?: string;
    type?: string | null;
    code?: string | null;
    level?: string | null;
    symptoms?: string | null;
    photoPath?: string | null;
    tsgId?: string | null;
  };
  maintInfo?: {
    name?: string | null;
    interval?: string | null;
    status?: string | null;
    time?: string | null;
    date?: string | null;
    detail?: string | null;
  };
  dealerInfo: {
    name?: string;
    phoneNum?: string | null;
  };
  serviceInfo: {
    center?: string | null;
    phoneNum?: string | null;
    address?: string | null;
  };
};

interface VehicleDetailProps {
  item: EquipmentType.FilteredMapItem;
  onClose: () => void;
}

const VehicleDetail = ({ item, onClose }: VehicleDetailProps) => {
  const { t } = useTranslation();

  const [infoField, setInfoField] = useState<{
    equipmentId: string;
    modelName: string;
    operationStatus?: EqOperationStatus;
    breakdownStatus?: EqBreakdownStatus;
    fields: { label: string; value: string }[];
  }>();
  const [sections, setSections] = useState<
    {
      title: string;
      Icon: string;
      fields: { label: string; value: React.ReactNode }[];
    }[]
  >([]);

  /** useQuery */

  const { data: vehicleDetailPage } = useQuery<VehicleDetailPage>({
    queryKey: ['/api/equipment/monitoring/detail', item.equipmentId],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return generateDetailInfoData();
      } else {
        try {
          const response =
            await equipmentMonitoringApi.getAdminEquipmentForMonitoring({
              equipmentId: Number(item.equipmentId),
            });

          const result: VehicleDetailPage = {
            id: item.equipmentId,
            operationStatus: {
              running: true,
              idle: false,
            },
            breakdownStatus: {
              breakdown: false,
              repairing: false,
              none: true,
            },
            equipmentId: item.equipmentId,
            modelName: item.modelName,
            plateNo: item.plateNo,
            driver: '-',
            phoneNum: '-',
            mileage: '-',
            faultInfo: undefined,
            maintInfo: undefined,
            dealerInfo: {
              name: '-',
              phoneNum: '-',
            },
            serviceInfo: {
              center: '-',
              phoneNum: '-',
              address: '-',
            },
          };

          if (response.data) {
            result.modelName = response.data.modelName ?? '-';
            result.plateNo = response.data.plateNo ?? '-'; // 차량번호(plateNo)
            result.driver = response.data.driver?.driverName ?? '-'; // 운전자(driver.name)
            result.phoneNum = response.data.driver?.driverPhone ?? '-'; //전화번호(driver.phone)
            result.mileage = response.data.status?.mileage?.toString() ?? '-'; // 작동시간(status.mileage)

            // 고장 정보
            if (
              response.data.breakdowns &&
              response.data.breakdowns.length > 0
            ) {
              const breakdown = response.data.breakdowns[0];

              result.faultInfo = {
                date: breakdown.occurredAt ?? '-', //고장 일시
                status: breakdown.breakdownRepairStatus ?? '-', //진행 상태
                mileage: breakdown.mileage?.toString() ?? '-', //고장 시 작동 시간
                type: breakdown.alarmType ?? '-', //알림 타입
                code: breakdown.breakdownCode ?? '-', //고장 코드
                level: breakdown.severity ?? '-', //고장 심각도
                symptoms: breakdown.symptom ?? '-', //고장 증상
                photoPath: breakdown.photoPath ?? '-', //고장 사진 경로
                tsgId: breakdown.tsgId?.toString() ?? '-', //TSG 아이디
                //알림 전송
              };
            }

            // 유지보수 정보
            // result.maintInfo = {
            //   name: null,
            //   interval: null,
            //   status: null,
            //   time: null,
            //   date: null,
            //   detail: null,
            // };

            // 딜러 정보
            if (response.data.dealer) {
              const dealer = response.data.dealer;
              result.dealerInfo = {
                name: dealer.dealerName ?? '-', // 딜러사명
                phoneNum: dealer.dealerPhone ?? '-', // 전화번호
              };
            }

            // 서비스 센터 정보
            if (response.data.serviceCenter) {
              const serviceCenter = response.data.serviceCenter;
              result.serviceInfo = {
                center: serviceCenter.serviceCenterName ?? '-', // 정비센터
                phoneNum: serviceCenter.serviceCenterPhone ?? '-', // 전화번호
                address: serviceCenter.serviceCenterAddress ?? '-', // 주소
              };
            }
          }

          return result;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    //initialData: {},
    enabled: true,
  });

  /** useEffect */

  useEffect(() => {
    // infoField
    const _infoField: {
      equipmentId: string;
      modelName: string;
      operationStatus?: EqOperationStatus;
      breakdownStatus?: EqBreakdownStatus;
      fields: { label: string; value: string }[];
    } = {
      equipmentId: vehicleDetailPage?.equipmentId ?? '-',
      modelName: vehicleDetailPage?.modelName ?? '-',
      operationStatus: vehicleDetailPage?.operationStatus,
      breakdownStatus: vehicleDetailPage?.breakdownStatus,
      fields: [],
    };

    _infoField.fields.push({
      label: t('VehicleNo'),
      value: vehicleDetailPage?.plateNo ?? '-',
    });
    _infoField.fields.push({
      label: t('Driver'),
      value: vehicleDetailPage?.driver ?? '-',
    });
    _infoField.fields.push({
      label: t('Ph'),
      value: vehicleDetailPage?.phoneNum ?? '-',
    });
    _infoField.fields.push({
      label: t('Mileage'),
      value: vehicleDetailPage?.mileage ?? '-',
    });

    setInfoField(_infoField);

    // sections
    const sectionArr: {
      title: string;
      Icon: string;
      fields: { label: string; value: React.ReactNode }[];
    }[] = [];

    // FaultInformation
    const faultFields: { label: string; value: React.ReactNode }[] = [];
    if (!vehicleDetailPage?.faultInfo) {
      faultFields.push({
        label: '',
        value: t('NoReportedIssuesForThisVehicle'),
      });
    } else {
      faultFields.push({
        label: t('FaultDateTime'),
        value: vehicleDetailPage?.faultInfo.date ?? '-',
      });
      faultFields.push({
        label: t('Status'),
        value: vehicleDetailPage?.faultInfo.status ?? '-',
      });
      faultFields.push({
        label: t('Mileage'),
        value: vehicleDetailPage?.faultInfo.mileage ?? '-',
      });
      faultFields.push({
        label: t('AlertType'),
        value: vehicleDetailPage?.faultInfo.type ?? '-',
      });
      faultFields.push({
        label: t('FaultCode'),
        value: vehicleDetailPage?.faultInfo.code ?? '-',
      });
      faultFields.push({
        label: t('SeverityLevel'),
        value: vehicleDetailPage?.faultInfo.level ?? '-',
      });
      faultFields.push({
        label: t('FaultSymptoms'),
        value: vehicleDetailPage?.faultInfo.symptoms ?? '-',
      });
      faultFields.push({
        label: t('FaultPhoto'),
        value: (
          <div className="text-semantic-2 underline cursor-pointer">
            {t('View')}
          </div>
        ),
      });
      faultFields.push({
        label: t('TSG'),
        value: (
          <div className="text-semantic-2 underline cursor-pointer">
            {t('View')}
          </div>
        ),
      });
      faultFields.push({
        label: t('AlertSent'),
        value: (
          <div className="text-semantic-2 underline cursor-pointer">
            {t('SendAlert')}
          </div>
        ),
      });
    }

    sectionArr.push({
      title: t('FaultInformation'),
      Icon: fault,
      fields: faultFields,
    });

    // MaintenanceInformation
    const maintFields: { label: string; value: React.ReactNode }[] = [];
    if (!vehicleDetailPage?.maintInfo) {
      maintFields.push({
        label: '',
        value: t('NoConsumablesRequireInspectionOrReplacement'),
      });
    } else {
      maintFields.push({
        label: t('PartName'),
        value: vehicleDetailPage?.maintInfo.name ?? '-',
      });
      maintFields.push({
        label: t('ReplacementInterval'),
        value: vehicleDetailPage?.maintInfo.interval ?? '-',
      });
      maintFields.push({
        label: t('Status'),
        value: vehicleDetailPage?.maintInfo.status ?? '-',
      });
      maintFields.push({
        label: t('UsageTime'),
        value:
          vehicleDetailPage?.maintInfo.time != null
            ? `${vehicleDetailPage.maintInfo.time} ${t('HoursH')}`
            : '-',
      });
      maintFields.push({
        label: t('LastReplacementDate'),
        value: vehicleDetailPage?.maintInfo.date ?? '-',
      });
      maintFields.push({
        label: t('ReplacementDetail'),
        value: vehicleDetailPage?.maintInfo.detail ?? '-',
      });
      maintFields.push({
        label: t('AlertSent'),
        value: (
          <div className="text-semantic-2 underline cursor-pointer">
            {t('SendAlert')}
          </div>
        ),
      });
    }
    sectionArr.push({
      title: t('MaintenanceInformation'),
      Icon: maint,
      fields: maintFields,
    });

    // DealershipInformation
    const dealerFields: { label: string; value: React.ReactNode }[] = [];
    if (
      !vehicleDetailPage?.dealerInfo ||
      !vehicleDetailPage.dealerInfo.name ||
      vehicleDetailPage.dealerInfo.name === '-' ||
      vehicleDetailPage.dealerInfo.name === ''
    ) {
      dealerFields.push({
        label: '',
        value: t('NoDealerInformationAvailable'),
      });
    } else {
      dealerFields.push({
        label: t('DealershipName'),
        value: vehicleDetailPage.dealerInfo.name ?? '-',
      });
      dealerFields.push({
        label: t('PhoneNumber'),
        value: vehicleDetailPage.dealerInfo.phoneNum ?? '-',
      });
    }
    sectionArr.push({
      title: t('DealershipInformation'),
      Icon: dealership,
      fields: dealerFields,
    });

    // ServiceCenterInformation
    const serviceFields: { label: string; value: React.ReactNode }[] = [];
    if (
      !vehicleDetailPage?.serviceInfo ||
      !vehicleDetailPage.serviceInfo.center ||
      vehicleDetailPage.serviceInfo.center === '-' ||
      vehicleDetailPage.serviceInfo.center === ''
    ) {
      serviceFields.push({
        label: '',
        value: t('NoServiceCenterInformationAvailable'),
      });
    } else {
      serviceFields.push({
        label: t('ServiceCenter'),
        value: vehicleDetailPage.serviceInfo.center ?? '-',
      });
      serviceFields.push({
        label: t('PhoneNumber'),
        value: vehicleDetailPage.serviceInfo.phoneNum ?? '-',
      });
      serviceFields.push({
        label: t('Address'),
        value: vehicleDetailPage.serviceInfo.address ?? '-',
      });
    }
    sectionArr.push({
      title: t('ServiceCenterInformation'),
      Icon: service,
      fields: serviceFields,
    });

    setSections(sectionArr);
  }, [vehicleDetailPage]);

  // 차량 타입에 따른 이미지
  const getVehicleTypeIcon = (type?: string) => {
    switch (type?.toLowerCase()) {
      case 'passenger':
        return passenger;
      case 'suv':
      case 'mpv':
      case 'suv_mpv':
        return suvmpv;
      case 'truck':
        return truck;
      case 'bus':
        return bus;
      case 'trailer':
        return trailer;
      case 'low':
      case 'lowbed':
        return low;
      case 'motocycle':
      case 'motorcycle':
        return motocycle;
      default:
        return truck; // 기본값 필요시
    }
  };

  return (
    <div className="w-[360px] h-[calc(100%-74px)] bg-white absolute bottom-0 left-0 z-20">
      {/* 상단: 제목 + 장비 기본 정보 */}
      <div className="flex w-full">
        <H2Title className="w-full f-c gap-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            className="ml-[-4px]"
          >
            <img src={arrowLeft} alt="close" />
          </button>
          {t('EquipmentDetails')}
        </H2Title>
      </div>

      <div className="h-full py-1 pl-2 pr-1">
        <div className="h-[calc(100%-65px)] overflow-y-scroll [&>div]:mt-5 [&>div]:px-3 [&>div]:pb-5 [&>div]:border-b [&>div:last-child]:border-0 [&>div]:border-gray-6">
          <div>
            <Link
              to={'/eq_list'}
              state={{ equipmentId: infoField?.equipmentId }}
              className="mb-3 f-c-b"
            >
              <div className="f-c gap-[10px]">
                <h2 className="subhead2">{infoField?.modelName}</h2>
                <img
                  src={getEqStatIcon(
                    infoField?.operationStatus,
                    infoField?.breakdownStatus,
                  )}
                  alt="장비 상태"
                />
              </div>
              <img src={arrowRight} alt="arrow_right" />
            </Link>
            <div className="f-c gap-4">
              <div className="w-[114px] h-[92px] py-5 px-2 f-c-c bg-primary-0-1 rounded-md">
                <img
                  src={getVehicleTypeIcon(
                    item.equipmentType ?? item.vehicleType,
                  )}
                  alt="차량 타입"
                />
              </div>
              <div className="space-y-1 [&>div]:f-c [&>div]:gap-[10px] [&_h3]:flex-shrink-0 [&_h3]:body4 [&_h3]:text-gray-10 [&_p]:caption2">
                {infoField?.fields.map(({ label, value }) => (
                  <div key={label}>
                    <h3>{label}</h3>
                    {/* <div> cannot appear as a descendant of <p>. */}
                    {typeof value === 'string' ? <p>{value}</p> : value}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 하단: 4개 섹션 */}
          {sections.map(({ title, Icon, fields }) => (
            <div key={title}>
              <div className="mb-3 f-c gap-2">
                <img src={Icon} alt={title} className="w-6 h-6" />
                <h3 className="subhead2">{title}</h3>
              </div>
              {fields.length === 1 && fields[0].label === '' ? (
                <div className="py-[65px] f-c-c body4 text-gray-8 text-center">
                  {fields[0].value}
                </div>
              ) : (
                // 일반 필드
                <div className="[&>div]:py-[10px] [&>div]:f-c [&>div]:gap-[10px] [&_h3]:flex-shrink-0 [&>div]:border-b [&>div:last-child]:border-0 [&>div]:border-gray-4 [&_h3]:body4 [&_h3]:text-gray-10 [&_p]:caption2">
                  {fields.map(({ label, value }) => (
                    <div key={label}>
                      <h3 className="self-start">{label}</h3>
                      {typeof value === 'string' ? <p>{value}</p> : value}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default VehicleDetail;
