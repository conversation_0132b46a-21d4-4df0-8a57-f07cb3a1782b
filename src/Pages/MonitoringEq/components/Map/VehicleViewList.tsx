import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState, useCallback } from 'react';
import { VariableSizeList as List } from 'react-window';
import { motion } from 'framer-motion';
import Dropdown from '@/Common/Components/common/DropDown';
import FilteredItem from '@/Pages/MonitoringEq/components/Map/FilteredItem';
import { EquipmentType } from '@/types/EquipmentType';

interface VehicleViewListProps {
  items: EquipmentType.FilteredMapItem[];
  onListItemClick?: (item: EquipmentType.FilteredMapItem) => void;
  shouldAnimate?: boolean; //
}

const VehicleViewList = ({ items, onListItemClick }: VehicleViewListProps) => {
  const { t } = useTranslation();

  const [sortedItems, setSortedItems] = useState<
    EquipmentType.FilteredMapItem[]
  >([]);
  const listRef = useRef<List>(null);
  const listContainer = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState(0);
  const [height, setHeight] = useState(0);
  const heightMap = useRef<{ [key: number]: number }>({});
  const getItemHeight = useCallback(
    (index: number) => heightMap.current[index] ?? 168,
    [],
  );
  const sortOptions = [
    { key: t('ModelName'), value: 'model' },
    { key: t('VehicleNumber'), value: 'number' },
    { key: t('OperatingTime'), value: 'time' },
  ];
  const orderOptions = [
    { key: t('Descending'), value: 'des' },
    { key: t('Ascending'), value: 'asc' },
  ];
  const [sortSel, setSortSel] = useState({
    key: t('Recently'),
    value: 'LASTUPDATE',
  });
  const [orderSel, setOrderSel] = useState({
    key: t('DescendingOrder'),
    value: 'DESC',
  });

  /** useEffect */

  useEffect(() => {
    compareSortList(sortSel.value, orderSel.value === 'ASC');
  }, [items, sortSel, orderSel]);

  useEffect(() => {
    if (listRef.current) {
      listRef.current.resetAfterIndex(0);
    }
  }, [sortedItems]);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      for (const entry of entries) {
        setWidth(entry.contentRect.width);
        setHeight(entry.contentRect.height);
      }
    });
    if (listContainer.current) observer.observe(listContainer.current);
    return () => observer.disconnect();
  }, []);

  const Row = ({
    index,
    style,
  }: {
    index: number;
    style: React.CSSProperties;
  }) => {
    const rowRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      // 실제 DOM에서 높이 측정
      const measuredHeight = rowRef.current?.getBoundingClientRect().height;
      if (measuredHeight && measuredHeight !== heightMap.current[index]) {
        heightMap.current[index] = measuredHeight;
        listRef.current?.resetAfterIndex(index); // 갱신!
      }
    }, [sortedItems[index]]);

    return (
      <div
        ref={rowRef}
        style={{ ...style, height: 'auto', paddingBottom: '4px' }}
      >
        <FilteredItem item={sortedItems[index]} onItemClick={onListItemClick} />
      </div>
    );
  };

  const handleChangeSortFilter = (key: string, value: string) => {
    setSortSel({ key, value });
  };
  const handleChangeOrderFilter = (key: string, value: string) => {
    setOrderSel({ key, value });
  };

  const compareSort = (
    a: (string | number)[],
    b: (string | number)[],
    asc: boolean,
  ) => {
    for (let i = 0; i < a.length; i++) {
      if (a[i] !== b[i]) {
        return asc ? (a[i] > b[i] ? 1 : -1) : a[i] < b[i] ? 1 : -1;
      }
    }
    return 0;
  };

  const compareSortList = (type: string, asc: boolean) => {
    const sorted = [...items].sort((a, b) => {
      switch (type) {
        case 'MODEL':
          return compareSort(
            [a.modelName, a.plateNo],
            [b.modelName, b.plateNo],
            asc,
          );
        case 'MILEAGE':
          return compareSort([a.mileage], [b.mileage], asc);
        case 'LOCATION':
          return compareSort([a.location], [b.location], asc);
        case 'LASTUPDATE':
        default:
          return compareSort([a.lastUpdate], [b.lastUpdate], asc);
      }
    });
    setSortedItems(sorted);
  };

  return (
    <motion.div
      key="tracking-list"
      initial={{ opacity: 1, x: -360 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 1, x: -360 }}
      transition={{ duration: 0.4, ease: 'easeInOut' }}
      className="w-[360px] h-[calc(100%-74px)] bg-white absolute bottom-0 left-0 overflow-hidden z-20 flex flex-col"
    >
      <div className="py-4 px-5 f-c gap-[10px] border-b border-gray-6 flex-shrink-0">
        <Dropdown
          placeholder={sortSel.key}
          options={sortOptions}
          onSelPair={handleChangeSortFilter}
          size="sm"
        />
        <Dropdown
          placeholder={orderSel.key}
          options={orderOptions}
          onSelPair={handleChangeOrderFilter}
          size="sm"
        />
      </div>

      {sortedItems.length === 0 && <FilteredItem key={0} />}

      <div ref={listContainer} className="flex-1 py-1 px-2">
        <List
          ref={listRef}
          width={width}
          height={height}
          itemCount={sortedItems.length}
          itemSize={getItemHeight}
          overscanCount={5}
        >
          {Row}
        </List>
      </div>
    </motion.div>
  );
};

export default VehicleViewList;
