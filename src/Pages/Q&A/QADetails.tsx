import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { useState } from 'react';
import UseQAPopup from '@/Pages/Q&A/Component/UseQAPopup.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import ViewDetails from '@/Pages/Q&A/Q&ADetails/ViewDetails.tsx';
import AnswerDetails from '@/Pages/Q&A/Q&ADetails/AnswerDetails.tsx';

const QADetails = () => {
  const { i18n } = useTranslation();

  const navigate = useNavigate();

  const { openPageOutPopup } = UseQAPopup();

  const [showAnswer, setShowAnswer] = useState(false);

  const [qnaDetail, setQnaDetail] = useState<{
    qnaType?: string;
    regEmail?: string;
    userId?: string;
    regDt?: string;
    title?: string;
    detail?: string;
    questionFileName?: string[];
    answerFileName?: string[];
    answerQnaId?: string;
    answerName?: string;
    answerDt?: string;
    answer?: string;
  } | null>(null);
  const { qnaId } = useParams<{ qnaId: string }>();

  const isAnswer = showAnswer && !!qnaDetail;

  const isPageOut = () => {
    openPageOutPopup(() => {
      navigate(-1);
    });
  };

  return (
    <CustomFrame name="Q&A" back onBackClick={isAnswer ? isPageOut : undefined}>
      {isAnswer ? (
        <AnswerDetails
          qnaId={qnaId ?? ''}
          onRegisterClick={() => setShowAnswer(false)}
          qnaDetail={qnaDetail}
          langType={i18n.language === 'en' ? 'US' : 'KR'}
          userId={qnaDetail.userId ?? ''}
          regEmail={qnaDetail.regEmail ?? ''}
        />
      ) : (
        <ViewDetails
          onAnswerClick={() => setShowAnswer(true)}
          setQnaDetail={setQnaDetail}
        />
      )}
    </CustomFrame>
  );
};

export default QADetails;
