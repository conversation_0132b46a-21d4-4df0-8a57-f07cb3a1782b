import { useTranslation } from 'react-i18next';
import { useToast } from '@/Common/useToast.tsx';
import { useOverlay } from '@toss/use-overlay';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';
import TempSavePopup from './TempSavePopup.tsx';

const UseQAPopup = () => {
  const { t } = useTranslation();

  const { toast } = useToast();

  const overlay = useOverlay();

  const openPageOutPopup = (onConfirm: () => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TwoButtonPopup
          title={t('LeaveThisPage')}
          onClose={close}
          onConfirm={() => {
            close();
            onConfirm();
          }}
          isOpen={isOpen}
          text={t(
            'YouHaveUnsavedChangesIfYouLeaveThisPageNowAllUnsavedDataWillBeLost',
          )}
          buttonText={t('Cancel')}
          secondButtonText={t('Leave')}
        />
      );
    });
  };
  const openQADeletePopup = (onConfirm: (close: () => void) => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TwoButtonPopup
          onClose={close}
          onConfirm={() => {
            onConfirm(close);
            toast({
              types: 'success',
              description: t('TheQAHasBeenDeleted'),
            });
            close();
          }}
          isOpen={isOpen}
          title={t('DeleteQA')}
          text={t('AreYouSureYouWantToDeleteThisQA')}
          buttonText={t('Cancel')}
          secondButtonText={t('Delete')}
        />
      );
    });
  };
  const openQATempDeletePopup = (onConfirm: () => void) => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          onConfirm();
          close();
        }}
        isOpen={isOpen}
        text={t('AreYouSureYouWantToDeleteOnceDeletedItCannotBeRecovered')}
        buttonText={t('Cancel')}
        secondButtonText={t('Delete')}
      />
    ));
  };
  const openTempSavePopup = () => {
    overlay.open(({ isOpen, close }) => {
      return <TempSavePopup onClose={close} onLoad={close} isOpen={isOpen} />;
    });
  };

  return {
    openPageOutPopup,
    openQADeletePopup,
    openQATempDeletePopup,
    openTempSavePopup,
  };
};

export default UseQAPopup;
