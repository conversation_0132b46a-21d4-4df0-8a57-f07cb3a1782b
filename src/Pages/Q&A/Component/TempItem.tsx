import Checkbox from '@/Common/Components/common/CheckBox';
import delete_BK from '@/assets/images/ic/16/delete_BK.svg';
import { Fragment } from 'react';
import UseQAPopup from './UseQAPopup';

const TempItem = ({
  title,
  regDt,
  noticeId,
  onDeleted,
  isSelected,
  onSelect,
}: {
  title: string;
  regDt: string;
  noticeId: number;
  onDeleted: () => void;
  isSelected?: boolean;
  onSelect?: () => void;
}) => {
  const { openQATempDeletePopup } = UseQAPopup();

  const handleDelete = () => {
    // 실제 삭제 API 없이 바로 콜백 실행
    onDeleted();
  };

  return (
    <Fragment>
      <div
        style={{
          padding: 16,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: 24,
          cursor: 'pointer',
        }}
        onClick={onSelect}
      >
        <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
          <Checkbox checked={isSelected} />
          <div>
            <div>{title}</div>
            <div style={{ color: '#9D9D9D', textAlign: 'left' }}>{regDt}</div>
          </div>
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            openQATempDeletePopup(handleDelete);
          }}
        >
          <img src={delete_BK} alt={'delete'} />
        </button>
      </div>
    </Fragment>
  );
};

export default TempItem;
