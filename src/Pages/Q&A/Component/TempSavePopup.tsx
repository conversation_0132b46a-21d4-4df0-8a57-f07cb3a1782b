import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import Layout from '@/Common/Popup/Layout.tsx';
import close_popup from '@/assets/images/etc/close_popup.png';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import TempItem from '@/Pages/Q&A/Component/TempItem.tsx';

interface TempSavePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onLoad: (data: { qnaId?: string; title?: string; regDt?: string }) => void;
}

const TempSavePopup = ({ isOpen, onClose, onLoad }: TempSavePopupProps) => {
  const { t } = useTranslation();
  // 더미 QnA 임시 저장 데이터
  const [tempList, setTempList] = useState([
    { qnaId: '1', title: '임시 QnA 1', regDt: '2024-07-10' },
    { qnaId: '2', title: '임시 QnA 2', regDt: '2024-07-13' },
  ]);
  const [selected, setSelected] = useState<{ qnaId?: string } | null>(null);

  const handleDelete = (qnaId: string) => {
    setTempList((prev) => prev.filter((item) => item.qnaId !== qnaId));
    if (selected?.qnaId === qnaId) setSelected(null);
  };

  return (
    <Layout isOpen={isOpen}>
      <div
        style={{
          width: 480,
          padding: 40,
          background: 'white',
          borderRadius: 8,
        }}
      >
        <div
          style={{
            marginBottom: 34,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div>{t('TemporaryStorage')}</div>
          <img
            src={close_popup}
            onClick={onClose}
            alt="close"
            style={{ width: 24, height: 24, cursor: 'pointer' }}
          />
        </div>
        <div style={{ height: 400, overflowY: 'auto' }}>
          {tempList.length > 0 ? (
            tempList.map((item) => (
              <TempItem
                key={item.qnaId}
                title={item.title ?? ''}
                regDt={item.regDt ?? ''}
                noticeId={Number(item.qnaId)}
                isSelected={selected?.qnaId === item.qnaId}
                onSelect={() => setSelected(item)}
                onDeleted={() => handleDelete(item.qnaId!)}
              />
            ))
          ) : (
            <div style={{ textAlign: 'center', color: '#9ca3af' }}>
              {t('NoData')}
            </div>
          )}
        </div>
        <SearchItemContainer>
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
          <Button
            variant={'bt_primary'}
            label={'Load'}
            onClick={() => {
              if (selected) {
                onLoad(selected);
                onClose();
              }
            }}
            disabled={!selected}
          />
        </SearchItemContainer>
      </div>
    </Layout>
  );
};

export default TempSavePopup;
