import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { Editor } from '@tinymce/tinymce-react';
import UseQAPopup from '@/Pages/Q&A/Component/UseQAPopup.tsx';
import TempSavePopup from '@/Pages/Q&A/Component/TempSavePopup.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import FileDropDown from '@/Common/Components/common/FileDropDown';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import { Button } from '@/Common/Components/common/Button';
import DropDown from '@/Common/Components/common/DropDown';
import Input from '@/Common/Components/common/Input';

const QARegistration = () => {
  const { t, i18n } = useTranslation();

  const navigate = useNavigate();

  const { openPageOutPopup } = UseQAPopup();

  const { qnaId } = useParams<{ qnaId: string }>();
  const location = useLocation();
  const editDetail = location.state?.detail as
    | {
        qnaType?: string;
        regEmail?: string;
        title?: string;
        detail?: string;
        questionFileName?: string[];
      }
    | undefined;

  const [title, setTitle] = useState(editDetail?.title ?? '');
  const [content, setContent] = useState(editDetail?.detail ?? '');
  const [qnaType, setQnaType] = useState<string>(editDetail?.qnaType ?? '');
  const [files, setFiles] = useState<File[]>([]);
  const langType = i18n.language === 'en' ? 'US' : 'KR';
  const [isTempPopupOpen, setIsTempPopupOpen] = useState(false);
  const [email, setEmail] = useState(editDetail?.regEmail ?? '');
  const isEditMode = Boolean(qnaId && editDetail);
  const [existingFileNames, setExistingFileNames] = useState<string[]>(
    editDetail?.questionFileName ?? [],
  );

  const handleLoadTempItem = (item: {
    title?: string;
    detail?: string;
    qnaType?: string;
  }) => {
    setTitle(item.title ?? '');
    setContent(item.detail ?? '');
    setQnaType(item.qnaType ?? '');
  };

  const handleClickTempSavePopup = () => {
    setIsTempPopupOpen(true);
  };

  const options = [
    { key: t('AllInquiryType'), value: 'Account' },
    { key: t('SystemMaintenance'), value: 'system' },
    { key: t('TermsUpdate'), value: 'terms' },
    { key: t('Update'), value: 'update' },
  ];

  // 등록, 임시저장 기능은 모두 더미
  const handleRegister = () => {
    navigate('/qna');
  };

  const handleTempSave = () => {
    // 임시저장 더미
  };

  const handleFileChange = (newFiles: File[]) => {
    setFiles(newFiles);
  };

  const handleDropdownChange = (field: string, value: string) => {
    if (field === 'qnaType') setQnaType(value);
  };

  const handleRemoveExistingFileName = (fileName: string) => {
    setExistingFileNames((prev) => prev.filter((f) => f !== fileName));
  };

  const isPageOut = () => {
    openPageOutPopup(() => {
      navigate(-1);
    });
  };

  useEffect(() => {
    setEmail(editDetail?.regEmail ?? '<EMAIL>');
  }, [editDetail]);

  return (
    <CustomFrame name={t('QARegistration')} back={true} onBackClick={isPageOut}>
      <section className="wrap-layout">
        <article className="f-c-b">
          <div className="f-c gap-5">
            <SearchItemContainer>
              <SearchLabel>{t('InquiryType')}</SearchLabel>
              <DropDown
                placeholder={t('AllInquiryType')}
                options={options}
                selectedKey={qnaType}
                onChange={(value) =>
                  handleDropdownChange('qnaType', value.toString())
                }
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Email')}</SearchLabel>
              <Input placeholder={t('Email')} widthSize={'lg'} />
            </SearchItemContainer>
          </div>
          <div className="f-c gap-[10px]">
            <Button
              variant={'bt_tertiary'}
              label={'LoadDraft'}
              onClick={handleClickTempSavePopup}
            >
              {t('TemporaryStorage2')}
            </Button>
            <Button
              variant={'bt_secondary'}
              label={'SaveAsDraft'}
              onClick={handleTempSave}
              disabled={!title.trim() && !content.trim()}
            />
            <Button
              variant={'bt_primary'}
              label={isEditMode ? 'Edit' : 'Register'}
              disabled={!title.trim() || !content.trim() || !qnaType}
              onClick={handleRegister}
            />
          </div>
        </article>

        <div className="divider mt-5 mb-10" />

        <article className="mt-10 space-y-[30px] [&_p]:w-[100px]">
          <SearchItemContainer className="gap-5">
            <SearchLabel>
              <p>
                {t('Title')}
                <span className="ml-1 text-semantic-4">*</span>
              </p>
            </SearchLabel>
            <Input
              placeholder={t('Title')}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
            />
          </SearchItemContainer>
          <SearchItemContainer
            style={{ alignItems: 'flex-start' }}
            className="gap-5"
          >
            <SearchLabel>
              <p>
                {t('Content')}
                <span className="ml-1 text-semantic-4">*</span>
              </p>
            </SearchLabel>
            <Editor
              value={content}
              onEditorChange={(newContent) => setContent(newContent)}
              apiKey="o23x3crldmqswoqnwr5hpx0q4lcc75y4cxei5519iuo7mhbr"
              init={{
                height: 600,
                menubar: false,
                plugins: [
                  'anchor',
                  'autolink',
                  'charmap',
                  'codesample',
                  'link',
                  'lists',
                  'searchreplace',
                  'visualblocks',
                  'wordcount',
                ],
                toolbar:
                  'undo redo | blocks fontsize | bold italic underline strikethrough | link mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent',
                tinycomments_mode: 'embedded',
                tinycomments_author: 'Author name',
                ai_request: () => {},
              }}
              initialValue=""
            />
          </SearchItemContainer>
          <SearchItemContainer className="gap-5">
            <SearchLabel>
              <p>{t('AttatchFile')}</p>
            </SearchLabel>
            <FileDropDown
              onFilesChange={handleFileChange}
              existingFileNames={existingFileNames}
              onRemoveExistingFileName={handleRemoveExistingFileName}
            />
          </SearchItemContainer>
        </article>

        {isTempPopupOpen && (
          <TempSavePopup
            isOpen={isTempPopupOpen}
            onClose={() => setIsTempPopupOpen(false)}
            onLoad={handleLoadTempItem}
          />
        )}
      </section>
    </CustomFrame>
  );
};

export default QARegistration;
