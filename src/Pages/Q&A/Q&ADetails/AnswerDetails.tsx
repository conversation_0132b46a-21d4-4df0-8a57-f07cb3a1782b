import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Editor } from '@tinymce/tinymce-react';
import { Button } from '@/Common/Components/common/Button';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import FileDropDown from '@/Common/Components/common/FileDropDown';
import SearchLabel from '@/Common/Components/layout/SearchLabel';

// AnswerDetails.tsx

interface AnswerDetailsProps {
  onRegisterClick: () => void;
  qnaDetail: {
    qnaType?: string;
    author?: string;
    postedDate?: string;
    status?: string;
    questionFileName?: string[];
    title?: string;
    detail?: string;
    answerFileName?: string[];
  };
  langType: 'KR' | 'US';
  userId: string;
  regEmail: string;
  qnaId: string;
}

const AnswerDetails = ({
  onRegisterClick,
  qnaDetail,
  regEmail,
  langType,
  qnaId,
}: AnswerDetailsProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [answer, setAnswer] = useState('');
  const [content, setContent] = useState();
  const [files, setFiles] = useState<File[]>([]);
  const [isDirty, setIsDirty] = useState(false);

  const handleAnswerChange = (value: string) => {
    setAnswer(value);
    if (!isDirty && value.trim() !== '') {
      setIsDirty(true);
    }
  };

  const handleFileChange = (newFiles: File[]) => {
    setFiles(newFiles);
  };

  // 실제 등록/임시저장/저장 기능 없음(더미)
  const handleRegister = () => {
    navigate('/qna');
    onRegisterClick();
  };

  const handleFileDownload = (fileName: string) => {
    alert(fileName + ' 다운로드');
  };

  return (
    <section className="wrap-layout">
      <article className="f-s-b">
        <div>
          <h2 className="mb-3 subtitle2">{qnaDetail?.title}</h2>
          <div
            className="
                text-divider
                [&_h3]:subtitle5 [&_p]:body3
                [&_p]:text-gray-10"
          >
            <div>
              <h3>{t('QAType')}</h3>
              <p>{qnaDetail?.qnaType}</p>
            </div>
            <div>
              <h3>{t('Author')}</h3>
              <p>{qnaDetail?.author}</p>
            </div>
            <div>
              <h3>{t('PostedDate')}</h3>
              <p>{qnaDetail?.postedDate}</p>
            </div>
            <div>
              <h3>{t('Status')}</h3>
              <p>{qnaDetail?.status}</p>
            </div>
          </div>
        </div>
        <Button
          variant={'bt_primary'}
          label={'Register'}
          onClick={handleRegister}
          disabled={!isDirty}
        />
      </article>

      <article>
        <div className="mt-[30px] mb-10 py-5 f-c gap-5 border-y border-gray-6">
          <h3 className="subtitle4">{t('AttatchFile')}</h3>
          {qnaDetail?.answerFileName?.map((name) => (
            <div
              key={name}
              onClick={() => handleFileDownload(name)}
              className="caption3 blue-underline"
            >
              {name}
            </div>
          ))}
        </div>

        <p
          dangerouslySetInnerHTML={{ __html: qnaDetail?.detail ?? '' }}
          className="pb-[130px] border-b border-gray-6 body2"
        />
      </article>

      <article className="mt-10 space-y-[30px] [&_p]:w-[100px]">
        <SearchItemContainer
          style={{ alignItems: 'flex-start' }}
          className="gap-5"
        >
          <SearchLabel>
            <p>
              {t('Answer')}
              <span className="ml-1 text-semantic-4">*</span>
            </p>
          </SearchLabel>
          <Editor
            value={content}
            // onEditorChange={(newContent) => setContent(newContent)}
            apiKey="o23x3crldmqswoqnwr5hpx0q4lcc75y4cxei5519iuo7mhbr"
            init={{
              height: 600,
              menubar: false,
              plugins: [
                'anchor',
                'autolink',
                'charmap',
                'codesample',
                'link',
                'lists',
                'searchreplace',
                'visualblocks',
                'wordcount',
              ],
              toolbar:
                'undo redo | blocks fontsize | bold italic underline strikethrough | link mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent',
              tinycomments_mode: 'embedded',
              tinycomments_author: 'Author name',
              ai_request: () => {},
            }}
            initialValue=""
          />
        </SearchItemContainer>
        <SearchItemContainer className="gap-5">
          <SearchLabel>
            <p>{t('AttatchFile')}</p>
          </SearchLabel>
          <FileDropDown onFilesChange={handleFileChange} />
        </SearchItemContainer>
      </article>
    </section>
  );
};

export default AnswerDetails;
