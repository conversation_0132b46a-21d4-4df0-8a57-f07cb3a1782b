import Checkbox from '@/Common/Components/common/CheckBox.tsx';
import delete_BK from '@/assets/images/ic/16/delete_BK.svg';
import { Fragment, HTMLAttributes } from 'react';
import useNoticePopup from './useNoticePopup.tsx';

interface TempItemProps extends HTMLAttributes<HTMLDivElement> {
  title: string;
  regDt: string;
  onClickDelete: () => void;
  onClickLoad: () => void;
}

const TempItem = ({
  title,
  regDt,
  onClickDelete,
  onClickLoad,
}: TempItemProps) => {
  const { openNoticeTempDeletePopup } = useNoticePopup();

  return (
    <Fragment>
      <div
        style={{
          padding: 16,
          display: 'flex',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: 24,
          cursor: 'pointer',
        }}
        onClick={onClickLoad}
      >
        <Checkbox />
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <div style={{ textAlign: 'left' }}>
            <div>{title}</div>
            <div style={{ color: '#9D9D9D' }}>{regDt}</div>
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation(); // Load 이벤트 막기
              openNoticeTempDeletePopup(onClickDelete);
            }}
          >
            <img src={delete_BK} alt="delete" />
          </button>
        </div>
      </div>
      <hr
        style={{ width: '100%', background: '#D9D9D9', border: 0, height: 1 }}
      />
    </Fragment>
  );
};

export default TempItem;
