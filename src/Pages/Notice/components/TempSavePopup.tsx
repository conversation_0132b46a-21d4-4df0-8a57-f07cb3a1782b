import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout.tsx';
import close_popup from '@/assets/images/etc/close_popup.png';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import TempItem from '@/Pages/Notice/components/TempItem.tsx';

interface TempSavePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onLoad: (data: { noticeId?: number; title?: string; regDt?: string }) => void;
}

const TempSavePopup = ({ isOpen, onClose, onLoad }: TempSavePopupProps) => {
  const { t } = useTranslation();
  // 더미 임시저장 목록
  const [tempList, setTempList] = useState([
    { noticeId: 1, title: '임시 공지사항1', regDt: '2024-07-14' },
    { noticeId: 2, title: '임시 공지사항2', regDt: '2024-07-15' },
  ]);
  const [selected, setSelected] = useState<{
    noticeId?: number;
    title?: string;
    regDt?: string;
  } | null>(null);

  const handleDelete = (noticeId: number) => {
    setTempList((prev) => prev.filter((item) => item.noticeId !== noticeId));
    if (selected?.noticeId === noticeId) setSelected(null);
  };

  return (
    <Layout isOpen={isOpen}>
      <div
        style={{
          width: 480,
          padding: 40,
          background: 'white',
          borderRadius: 8,
        }}
      >
        <div
          style={{
            marginBottom: 34,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div>{t('TemporaryStorage')}</div>
          <img
            src={close_popup}
            style={{ width: 24, height: 24, cursor: 'pointer' }}
            onClick={onClose}
            alt="close"
          />
        </div>

        <div style={{ height: 400, overflowY: 'auto' }}>
          {tempList.length > 0 ? (
            tempList.map((item) => (
              <TempItem
                key={item.noticeId}
                title={item.title ?? ''}
                regDt={item.regDt ?? ''}
                onClickDelete={() => handleDelete(item.noticeId!)}
                onClickLoad={() => {
                  onLoad(item);
                  onClose();
                }}
                // 선택시(하이라이트, 단일선택 필요 시 onSelect 사용)
                onSelect={() => setSelected(item)}
              />
            ))
          ) : (
            <div style={{ textAlign: 'center', color: '#9ca3af' }}>
              {t('NoData')}
            </div>
          )}
        </div>

        <SearchItemContainer>
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
          <Button
            variant={'bt_primary'}
            label={'Load'}
            onClick={() => {
              if (selected) {
                onLoad(selected);
                onClose();
              }
            }}
            disabled={!selected}
          />
        </SearchItemContainer>
      </div>
    </Layout>
  );
};

export default TempSavePopup;
