import { useTranslation } from 'react-i18next';
import { useToast } from '@/Common/useToast.tsx';
import { useOverlay } from '@toss/use-overlay';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';

const UseNoticePopup = () => {
  const { t } = useTranslation();

  const { toast } = useToast();

  const overlay = useOverlay();

  const openNoticeDeletePopup = (onConfirm: (close: () => void) => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TwoButtonPopup
          onClose={close}
          onConfirm={() => {
            onConfirm(close);
            toast({
              types: 'success',
              description: t('TheNoticeHasBeenDeleted'),
            });
            close();
          }}
          isOpen={isOpen}
          title={t('DeleteNotice')}
          text={t('AreYouSureYouWantToDeleteThisNotice')}
          buttonText={t('Cancel')}
          secondButtonText={t('Delete')}
        />
      );
    });
  };
  const openNoticeDelete2Popup = () => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TwoButtonPopup
          onClose={close}
          onConfirm={close}
          isOpen={isOpen}
          text={t('AreYouSureYouWantToDeleteOnceDeletedItCannotBeRecovered')}
          buttonText={t('Cancel')}
          secondButtonText={t('Delete')}
        />
      );
    });
  };
  const openPageOutPopup = (onConfirm: () => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TwoButtonPopup
          title={t('LeaveThisPage')}
          onClose={close}
          onConfirm={() => {
            close();
            onConfirm();
          }}
          isOpen={isOpen}
          text={t(
            'YouHaveUnsavedChangesIfYouLeaveThisPageNowAllUnsavedDataWillBeLost',
          )}
          buttonText={t('Cancel')}
          secondButtonText={t('Leave')}
        />
      );
    });
  };
  const openNoticeTempDeletePopup = (
    onConfirm: (close: () => void) => void,
  ) => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          onConfirm(close);
          toast({
            types: 'success',
            description: t('TheTemporarySaveHasBeenDeleted'),
          });
          close();
        }}
        isOpen={isOpen}
        text={t(
          'AreYouSureYouWantToDeleteThisYemporarySaveThisActionCannotBeUndone',
        )}
        buttonText={t('Cancel')}
        secondButtonText={t('Leave')}
      />
    ));
  };

  return {
    openNoticeDeletePopup,
    openNoticeDelete2Popup,
    openPageOutPopup,
    openNoticeTempDeletePopup,
  };
};

export default UseNoticePopup;
