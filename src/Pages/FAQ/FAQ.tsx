import { useTranslation } from 'react-i18next';
import { useToast } from '@/Common/useToast.tsx';
import { ColumnDef, Row } from '@tanstack/react-table';
import { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { CustomFrame } from '@/Pages/CustomFrame';
import CommonTable from '@/Common/Components/common/CommonTable';
import { Button } from '@/Common/Components/common/Button';
import Input from '@/Common/Components/common/Input';
import DropDown from '@/Common/Components/common/DropDown';
import UseFAQPopup from '@/Pages/FAQ/Component/UseFAQPopup.tsx';

interface FAQData {
  no: string;
  type: string;
  question: string;
  answer: string;
  date: string;
  topFixed: boolean;
  fileName: string[];
  readPermission: string;
  langType: string;
}

const FAQ = () => {
  const { t } = useTranslation();
  const { openFaqDeletePopup } = UseFAQPopup();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [selectedCheck, setSelectedCheck] = useState<string[]>([]);
  const [pageNum, setPageNum] = useState(1);
  const [pageSize] = useState(10);
  const [faqType, setFaqType] = useState<string>('ALL');
  const [searchFaqType, setSearchFaqType] = useState<string>('ALL');

  const handleSelectionChange = (selectedRows: FAQData[]) => {
    if (Array.isArray(selectedRows)) {
      setSelectedCheck(selectedRows.map((row) => row.no ?? '0'));
    }
  };

  const routeRegistration = () => {
    navigate('/faq-registration');
  };

  //  FAQ 리스트 펼치기
  const [expandedRow, setExpandedRow] = useState<string[]>([]);
  const expandRow = (id: string) => {
    setExpandedRow((prev) =>
      prev.includes(id) ? prev.filter((rowId) => rowId !== id) : [...prev, id],
    );
  };

  const [imagePreviews, setImagePreviews] = useState<Record<string, string>>(
    {},
  );

  const columns: ColumnDef<FAQData>[] = [
    {
      header: t('No'),
      accessorKey: 'no',
      cell: ({ row }: { row: Row<FAQData> }) => (
        <div>
          {row.original.topFixed ? (
            <span className="body2 text-secondary-6">{t('Pinned')}</span>
          ) : (
            <span>{row.original.no}</span>
          )}
        </div>
      ),
    },
    {
      header: t('InquiryType'),
      accessorKey: 'type',
    },
    {
      header: t('Title'),
      accessorKey: 'question',
      cell: ({ row }: { row: Row<FAQData> }) => (
        <div
          className="w-[865px] cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            expandRow(row.original.no);
          }}
        >
          <div className="f-c-b">
            <div className="space-x-[10px]">
              <span className="text-secondary-6">Q.</span>
              <span>{row.original.question}</span>
            </div>
            <Button
              variant={'bt_tertiary_sm'}
              label={'Edit'}
              onClick={(e) => {
                e.stopPropagation();
                navigate('/faq-registration', {
                  state: {
                    faqItem: { ...row.original, faqType: row.original.type },
                    faqId: row.original.no,
                    faqType: row.original.type,
                    readPermission: row.original.readPermission,
                    langType: row.original.langType,
                  },
                });
              }}
            />
          </div>
        </div>
      ),
    },
    {
      header: t('PostedDate'),
      accessorKey: 'date',
    },
  ];

  // FAQ 리스트 조회
  const [question, setQuestion] = useState('');
  const [searchQuestion, setSearchQuestion] = useState('');

  // 삭제되고 체크표시 푸는 로직
  const [tableKey, setTableKey] = useState(0);

  // 리스트 삭제 로직
  const handleDeleteFaqs = async (close: () => void) => {
    try {
      toast({ types: 'warning', description: t('FAQDeleted') });
      close();
      setSelectedCheck([]);
      setTableKey((prev) => prev + 1);
    } catch (error) {
      console.error(error);
    }
  };

  // 드롭다운 옵션
  const [faqTypeOptions] = useState([
    { key: t('All'), value: 'ALL' },
    { key: t('HowToUse'), value: 'Usage' },
    { key: t('Account'), value: 'Account' },
    { key: t('Others'), value: 'Other' },
  ]);

  // 드롭다운 선택 시 FAQ타입 변경
  const handleDropdownChange = (field: string, value: string) => {
    if (field === 'faqType') {
      setFaqType(value);
    }
  };

  // Mock FAQ 데이터
  const mockFaqData: FAQData[] = useMemo(
    () => [
      {
        no: '0',
        type: 'Usage',
        question: '로그인이 안되는 경우 어떻게 해야 하나요?',
        answer:
          '아이디와 비밀번호를 다시 확인해보시고, 문제가 지속되면 관리자에게 문의하세요.',
        date: '2024-01-15',
        topFixed: true,
        fileName: [],
        readPermission: 'ALL',
        langType: 'KR',
      },
      {
        no: '1',
        type: 'Account',
        question: '계정 비밀번호를 어떻게 변경하나요?',
        answer:
          '설정 메뉴에서 계정 정보를 클릭하여 비밀번호를 변경할 수 있습니다.',
        date: '2024-01-20',
        topFixed: false,
        fileName: [],
        readPermission: 'ALL',
        langType: 'KR',
      },
      {
        no: '2',
        type: 'Usage',
        question: '데이터 내보내기는 어떻게 하나요?',
        answer:
          '리포트 페이지에서 Export 버튼을 클릭하여 데이터를 내보낼 수 있습니다.',
        date: '2024-02-01',
        topFixed: false,
        fileName: [],
        readPermission: 'ALL',
        langType: 'KR',
      },
      {
        no: '3',
        type: 'Other',
        question: '시스템 오류가 발생했을 때 어떻게 해야 하나요?',
        answer:
          '페이지를 새로고침하거나 브라우저를 재시작해보세요. 문제가 지속되면 기술지원팀에 문의하세요.',
        date: '2024-02-10',
        topFixed: false,
        fileName: [],
        readPermission: 'ALL',
        langType: 'KR',
      },
      {
        no: '4',
        type: 'Account',
        question: '새로운 사용자 계정은 어떻게 만드나요?',
        answer:
          '관리자 권한이 있는 계정으로 로그인하여 사용자 관리 메뉴에서 새 계정을 생성할 수 있습니다.',
        date: '2024-02-15',
        topFixed: false,
        fileName: [],
        readPermission: 'ADMIN',
        langType: 'KR',
      },
    ],
    [],
  );

  // 검색 및 필터링된 데이터
  const filteredFaqData = useMemo(() => {
    return mockFaqData.filter((item) => {
      const typeMatch = searchFaqType === 'ALL' || item.type === searchFaqType;
      const questionMatch =
        searchQuestion === '' ||
        item.question.toLowerCase().includes(searchQuestion.toLowerCase()) ||
        item.answer.toLowerCase().includes(searchQuestion.toLowerCase());

      return typeMatch && questionMatch;
    });
  }, [mockFaqData, searchFaqType, searchQuestion]);

  const faqData: FAQData[] = filteredFaqData;
  const [totalCount] = useState(filteredFaqData.length);

  // 검색 버튼
  const handleSearch = () => {
    setSearchFaqType(faqType);
    setSearchQuestion(question);
    setPageNum(1);
  };

  // 페이지 변경 시
  const handlePageChange = (newPage: number) => {
    setPageNum(newPage);
    setExpandedRow([]);
  };

  return (
    <CustomFrame name="FAQ" back={false}>
      <section className="wrap-layout">
        {/* 필터 검색창 */}
        <article className="f-c gap-4">
          <div className="f-c gap-[10px]">
            <DropDown
              onChange={(value) =>
                handleDropdownChange('faqType', value.toString())
              }
              options={faqTypeOptions}
              placeholder={t('AllInquiryType')}
            />
            <Input
              placeholder={t('TitleOrContent')}
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
            />
          </div>
          <Button
            variant={'bt_primary'}
            label={'Search'}
            onClick={handleSearch}
          />
        </article>

        {/* 테이블 버튼 */}
        <article className="mt-[18px] mb-[10px] f-c-e gap-[10px]">
          <Button
            variant={'bt_primary_sm'}
            label={'Delete'}
            onClick={() => openFaqDeletePopup(handleDeleteFaqs)}
            disabled={selectedCheck.length === 0}
          />
          <Button
            variant={'bt_primary_sm'}
            label={'Register'}
            onClick={routeRegistration}
          />
        </article>

        {/* 테이블 */}
        <CommonTable<FAQData>
          key={tableKey}
          columns={columns}
          expandedRow={expandedRow}
          expandRow={expandRow}
          data={faqData || []}
          isPagination={true}
          isCheckbox={true}
          onSelectionChange={handleSelectionChange}
          currentPage={pageNum}
          customPageSize={pageSize}
          totalCount={totalCount}
          onPageChange={handlePageChange}
        />
      </section>
    </CustomFrame>
  );
};

export default FAQ;
