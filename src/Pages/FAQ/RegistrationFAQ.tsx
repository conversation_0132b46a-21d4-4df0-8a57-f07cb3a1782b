import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useToast } from '@/Common/useToast.tsx';
import UseFAQPopup from '@/Pages/FAQ/Component/UseFAQPopup.tsx';
import TempSavePopup from '@/Pages/FAQ/Component/TempSavePopup.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Button } from '@/Common/Components/common/Button';
import CheckBox from '@/Common/Components/common/CheckBox';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import FileDropDown from '@/Common/Components/common/FileDropDown';

const RegistrationFAQ = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { toast } = useToast();

  const { openFaqOutPopup } = UseFAQPopup();

  // 상태 관리
  const [faqType, setFaqType] = useState<string>('ALL');
  const [readPermission, setReadPermission] = useState<string>('ALL');
  const [langType, setLangType] = useState<string>('KR');
  const [question, setQuestion] = useState<string>('');
  const [content, setContent] = useState<string>('');
  const [topFixed, setTopFixed] = useState<boolean>(false);
  const [uploadFiles, setUploadFiles] = useState<File[]>([]);
  const [existingFileNames, setExistingFileNames] = useState<string[]>([]);

  // FAQ 수정 시, 기존 데이터 불러오기
  const location = useLocation();
  const faqId = location.state?.faqId;
  const isEditMode = !!faqId; // 수정 모드 여부
  const faqItem = location.state?.faqItem;

  useEffect(() => {
    if (faqItem) {
      setExistingFileNames(faqItem.fileName ?? []);
    }
  }, [faqItem]);

  useEffect(() => {
    if (!faqItem) return;

    setFaqType(faqItem.type ?? '');
    setReadPermission(faqItem.readPermission ?? '');
    setLangType(faqItem.langType ?? 'KR');
    setQuestion(faqItem.question ?? '');
    setContent(faqItem.answer ?? '');
    setTopFixed(faqItem.topFixed ?? false);
  }, [faqItem]);

  // 옵션 배열만 하드코딩 (enum 등 삭제)
  const [faqTypeOptions] = useState([
    { key: t('AllInquiryType'), value: 'ALL' },
    { key: t('SystemMaintenance'), value: 'USAGE' },
    { key: t('TermsUpdate'), value: 'ACCOUNT' },
    { key: t('Update'), value: 'OTHER' },
  ]);
  const [readPermissionOptions] = useState([
    { key: t('SuperAdministrator'), value: 'MASTER' },
    { key: t('DelearAdministrator'), value: 'DEALER' },
    { key: t('RepairAdministrator'), value: 'REPAIR' },
    { key: t('MMXAdministrator'), value: 'MMX' },
    { key: t('Driver'), value: 'DRIVER' },
  ]);
  const [langTypeOptions] = useState([
    { key: t('Korean'), value: 'KR' },
    { key: t('English'), value: 'US' },
  ]);

  const handleDropdownChange = (field: string, value: string) => {
    switch (field) {
      case 'faqType':
        setFaqType(value);
        break;
      case 'readPermission':
        setReadPermission(value);
        break;
      case 'langType':
        setLangType(value);
        break;
      default:
        break;
    }
  };

  // FAQ 등록 및 수정 (API 없음)
  const handleRegisterFaq = async () => {
    // API 로직 제거됨
  };

  // 임시 저장 (API 없음)
  const handleTemporarySave = async () => {
    // API 로직 제거됨
  };

  const [isTempPopupOpen, setIsTempPopupOpen] = useState(false);

  const handleRemoveExistingFileName = (fileName: string) => {
    setExistingFileNames((prev) => prev.filter((f) => f !== fileName));
  };

  // 불필요한 타입 import 제거 (item any로)
  const handleLoadTempItem = (item: any) => {
    setFaqType(item.faqType ?? 'ALL');
    setReadPermission(item.readPermission ?? 'ALL');
    setLangType(item.langType ?? 'KR');
    setQuestion(item.question ?? '');
    setContent(item.answer ?? '');
  };

  const isPageOut = () => {
    openFaqOutPopup(() => {
      navigate(-1);
    });
  };

  return (
    <CustomFrame
      name={isEditMode ? t('FAQModify') : t('FAQRegistration')}
      back={true}
      onBackClick={isPageOut}
    >
      <section className="wrap-layout">
        {/* 필터 */}
        <article className="f-c-b">
          <div className="f-c gap-5">
            <SearchItemContainer>
              <SearchLabel>{t('InquiryType')}</SearchLabel>
              <DropDown
                selectedKey={faqType}
                onChange={(value) =>
                  handleDropdownChange('faqType', value.toString())
                }
                options={faqTypeOptions}
                placeholder={t('AllInquiryType')}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('ViewPermission')}</SearchLabel>
              <DropDown
                selectedKey={readPermission}
                onChange={(value) =>
                  handleDropdownChange('readPermission', value.toString())
                }
                options={readPermissionOptions}
                placeholder={t('All')}
              />
            </SearchItemContainer>
            <SearchItemContainer>
              <SearchLabel>{t('Language')}</SearchLabel>
              <DropDown
                selectedKey={langType}
                onChange={(value) =>
                  handleDropdownChange('langType', value.toString())
                }
                options={langTypeOptions}
                placeholder={t('Korean')}
              />
            </SearchItemContainer>
            <CheckBox
              label={t('PinnedToTop')}
              checked={topFixed}
              onCheckedChange={(checked) => setTopFixed(!!checked)}
            />
          </div>

          <div className="f-c gap-[10px]">
            <Button
              variant={'bt_tertiary'}
              label={t('LoadDraft')}
              onClick={() => setIsTempPopupOpen(true)}
            />
            <Button
              variant={'bt_secondary'}
              label={t('SaveAsDraft')}
              disabled={!question.trim() && !content.trim()}
              onClick={handleTemporarySave}
            />
            <Button
              variant={'bt_primary'}
              label={t('Register')}
              disabled={!question.trim() || !content.trim()}
              onClick={handleRegisterFaq}
            />
          </div>
        </article>

        <div className="divider my-10"></div>

        {/* 문의 입력 */}
        <article className="space-y-3">
          <SearchItemContainer
            style={{ alignItems: 'flex-start' }}
            className="gap-5"
          >
            <SearchLabel className="w-[102px]">
              {t('Question')}
              <em className="ml-1 text-semantic-4">*</em>
            </SearchLabel>
            <textarea
              placeholder={t('Question')}
              className="w-full min-h-[144px] py-3 px-4 border border-gray-6 rounded body2"
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
            />
          </SearchItemContainer>
          <SearchItemContainer
            style={{ alignItems: 'flex-start' }}
            className="gap-5"
          >
            <SearchLabel className="w-[102px]">
              {t('Answer')}
              <em className="ml-1 text-semantic-4">*</em>
            </SearchLabel>
            <Editor
              value={content}
              onEditorChange={(newContent) => setContent(newContent)}
              apiKey="o23x3crldmqswoqnwr5hpx0q4lcc75y4cxei5519iuo7mhbr"
              init={{
                height: 600,
                menubar: false,
                plugins: [
                  'anchor',
                  'autolink',
                  'charmap',
                  'codesample',
                  'link',
                  'lists',
                  'searchreplace',
                  'visualblocks',
                  'wordcount',
                ],
                toolbar:
                  'undo redo | blocks fontsize | bold italic underline strikethrough | link mergetags | addcomment showcomments | spellcheckdialog a11ycheck typography | align lineheight | checklist numlist bullist indent outdent',
                tinycomments_mode: 'embedded',
                tinycomments_author: 'Author name',
                ai_request: () => {},
              }}
            />
          </SearchItemContainer>
          <SearchItemContainer className="gap-5">
            <SearchLabel className="w-[102px]">{t('AttatchFile')}</SearchLabel>
            <FileDropDown
              onFilesChange={setUploadFiles}
              existingFileNames={existingFileNames}
              onRemoveExistingFileName={handleRemoveExistingFileName}
            />
          </SearchItemContainer>
        </article>

        {isTempPopupOpen && (
          <TempSavePopup
            isOpen={isTempPopupOpen}
            onClose={() => setIsTempPopupOpen(false)}
            onLoad={handleLoadTempItem}
          />
        )}
      </section>
    </CustomFrame>
  );
};

export default RegistrationFAQ;
