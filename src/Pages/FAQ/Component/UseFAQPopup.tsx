import { useTranslation } from 'react-i18next';
import { useToast } from '@/Common/useToast.tsx';
import { useOverlay } from '@toss/use-overlay';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';
import TempSavePopup from './TempSavePopup.tsx';

const UseFAQPopup = () => {
  const { t } = useTranslation();

  const { toast } = useToast();

  const overlay = useOverlay();

  const openFaqDeletePopup = (onConfirm: (close: () => void) => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TwoButtonPopup
          onClose={close}
          onConfirm={() => {
            onConfirm(close);
            toast({
              types: 'success',
              description: t('TheFAQHasBeenDeleted'),
            });
            close();
          }}
          isOpen={isOpen}
          title={t('DeleteFAQ')}
          text={t('AreYouSureYouWantToDeleteThisFAQ')}
          buttonText={t('Cancel')}
          secondButtonText={t('Delete')}
        />
      );
    });
  };
  const openFaqTempDeletePopup = (onConfirm: () => void) => {
    overlay.open(({ isOpen, close }) => (
      <TwoButtonPopup
        onClose={close}
        onConfirm={() => {
          onConfirm();
          close();
        }}
        isOpen={isOpen}
        text={t('AreYouSureYouWantToDeleteOnceDeletedItCannotBeRecovered')}
        buttonText={t('Cancel')}
        secondButtonText={t('Delete')}
      />
    ));
  };
  const openFaqOutPopup = (onConfirm: (close: () => void) => void) => {
    overlay.open(({ isOpen, close }) => {
      return (
        <TwoButtonPopup
          onClose={close}
          onConfirm={() => {
            onConfirm(close);
            toast({
              types: 'success',
              description: t('TheFAQHasBeenPosted'),
            });
            close();
          }}
          isOpen={isOpen}
          title={t('LeaveThisPage')}
          text={t(
            'TitleThereAreUnsavedChangesIfYouLeaveWithoutSavingAnyUnsavedContentWillBeLost',
          )}
          buttonText={t('Cancel')}
          secondButtonText={t('Deleted')}
        />
      );
    });
  };
  const openTempSavePopup = () => {
    overlay.open(({ isOpen, close }) => {
      return <TempSavePopup onClose={close} onLoad={close} isOpen={isOpen} />;
    });
  };

  return {
    openFaqDeletePopup,
    openFaqTempDeletePopup,
    openFaqOutPopup,
    openTempSavePopup,
  };
};

export default UseFAQPopup;
