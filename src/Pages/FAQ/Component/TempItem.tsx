import Checkbox from '@/Common/Components/common/CheckBox';
import delete_BK from '@/assets/images/ic/16/delete_BK.svg';
import { Fragment } from 'react';
import UseFAQPopup from './UseFAQPopup';

const TempItem = ({
  title,
  createdAt,
  faqId,
  onDeleted,
  isSelected,
  onSelect,
}: {
  title: string;
  createdAt: string;
  faqId: number;
  onDeleted: () => void;
  isSelected?: boolean;
  onSelect?: () => void;
}) => {
  const { openFaqTempDeletePopup } = UseFAQPopup();

  // 더미 삭제 핸들러(실제 API 없음)
  const handleDelete = () => {
    openFaqTempDeletePopup(() => {
      onDeleted();
    });
  };

  return (
    <Fragment>
      <div
        onClick={onSelect}
        style={{
          padding: 16,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: 24,
          cursor: 'pointer',
        }}
      >
        <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
          <Checkbox checked={isSelected} />
          <div>
            <div>{title}</div>
            <div style={{ color: '#9D9D9D', textAlign: 'left' }}>
              {createdAt}
            </div>
          </div>
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleDelete();
          }}
        >
          <img src={delete_BK} alt={'delete'} />
        </button>
      </div>
    </Fragment>
  );
};

export default TempItem;
