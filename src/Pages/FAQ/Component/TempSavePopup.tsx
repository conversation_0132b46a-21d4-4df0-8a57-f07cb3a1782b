import { useTranslation } from 'react-i18next';
import { useState, useMemo } from 'react';
import { useToast } from '@/Common/useToast.tsx';
import Layout from '@/Common/Popup/Layout.tsx';
import close_popup from '@/assets/images/etc/close_popup.png';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import TempItem from '@/Pages/FAQ/Component/TempItem.tsx';

interface FaqInfo {
  faqId?: number;
  question?: string;
  answer?: string;
  faqType?: string;
  regDt?: string;
  topFixed?: boolean;
  readPermission?: string;
  langType?: string;
}

interface TempSavePopupProps {
  isOpen: boolean;
  onClose: () => void;
  onLoad: (data: FaqInfo) => void;
}

const TempSavePopup = ({ isOpen, onClose, onLoad }: TempSavePopupProps) => {
  const { t } = useTranslation();

  const { toast } = useToast();

  // Mock 임시저장 데이터
  const mockTempList: FaqInfo[] = useMemo(
    () => [
      {
        faqId: 1,
        question: '로그인 문제 해결 방법',
        answer: '아이디와 비밀번호를 확인하세요...',
        faqType: 'Usage',
        regDt: '2024-07-14 10:30:00',
        topFixed: false,
        readPermission: 'ALL',
        langType: 'KR',
      },
      {
        faqId: 2,
        question: '계정 설정 변경하기',
        answer: '설정 메뉴에서 계정 정보를 변경할 수 있습니다...',
        faqType: 'Account',
        regDt: '2024-07-13 15:45:00',
        topFixed: false,
        readPermission: 'ALL',
        langType: 'KR',
      },
      {
        faqId: 3,
        question: '데이터 백업 방법',
        answer: '정기적으로 데이터를 백업하는 것이 중요합니다...',
        faqType: 'Other',
        regDt: '2024-07-12 09:20:00',
        topFixed: false,
        readPermission: 'ADMIN',
        langType: 'KR',
      },
    ],
    [],
  );

  const [tempList] = useState<FaqInfo[]>(mockTempList);
  const [selected, setSelected] = useState<FaqInfo | null>(null);

  const handleDelete = (faqId: number) => {
    console.log('Delete FAQ with ID:', faqId);
    // Mock 삭제 로직 - 실제로는 상태를 업데이트하지 않음
    if (selected?.faqId === faqId) {
      setSelected(null);
    }
  };

  return (
    <Layout isOpen={isOpen}>
      <div
        style={{
          width: 480,
          padding: 40,
          background: 'white',
          borderRadius: 8,
        }}
      >
        <div>
          <div>{t('TemporaryStorage')}</div>
          <img
            src={close_popup}
            onClick={onClose}
            style={{ width: 24, height: 24, cursor: 'pointer', float: 'right' }}
          />
        </div>
        <div style={{ height: 400, overflowY: 'auto' }}>
          {tempList.map((item) => (
            <TempItem
              key={item.faqId}
              title={item.question ?? ''}
              createdAt={item.regDt ?? ''}
              faqId={item.faqId ?? 0}
              isSelected={selected?.faqId === item.faqId}
              onSelect={() => setSelected(item)}
              onDeleted={() => handleDelete(item.faqId ?? 0)}
            />
          ))}
        </div>
        <SearchItemContainer>
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
          <Button
            variant={'bt_primary'}
            label={'Load'}
            onClick={() => {
              if (selected) {
                onLoad(selected);
                onClose();
              }
              toast({
                types: 'success',
                description: t('YourDraftHasBeenSaved'),
              });
            }}
            disabled={!selected}
          />
        </SearchItemContainer>
      </div>
    </Layout>
  );
};

export default TempSavePopup;
