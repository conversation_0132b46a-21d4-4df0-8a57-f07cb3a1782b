import { useTranslation } from 'react-i18next';
import React from 'react';
import { Button } from '@/Common/Components/common/Button';
import error from '@/assets/images/error/error.svg';

const Error401 = () => {
  const { t } = useTranslation();

  return (
    <React.Fragment>
      <section className="h-[calc(100%-121px)] f-c-c flex-col text-center">
        <img src={error} alt="error" className="mb-6" />
        <h2 className="mb-[14px] display1">{t('401error')}</h2>
        <p className="mb-[110px] subtitle3 whitespace-pre">
          {t('YouAreNotAuthorizedToAccessThisPagePleaseLogInAndTryAgain')}
        </p>
        <Button
          variant={'bt_primary'}
          label={'Login'}
          onClick={() => (window.location.href = '/')}
          className="w-[460px]"
        />
      </section>
    </React.Fragment>
  );
};

export default Error401;
