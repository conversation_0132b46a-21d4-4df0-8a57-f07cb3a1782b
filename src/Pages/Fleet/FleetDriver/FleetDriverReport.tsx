import { useTranslation } from 'react-i18next';
import { Tabs } from '@radix-ui/themes';
import DropDown from '@/Common/Components/common/DropDown';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { useEffect, useState } from 'react';
import { formatDate } from '@/Common/function/date.ts';
import { useQuery } from '@tanstack/react-query';
import { DemoTest } from '@/types';
import dayjs from 'dayjs';
import { ColumnDef } from '@tanstack/react-table';
import { useForm } from 'react-hook-form';
import { use } from 'i18next';

type DriverReportParams = {
  startDate: string;
  endDate: string;
  driverName: string;
};

type DriverReportPage = {
  rows: DriverReportRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type DriverReportRow = {
  driverName: string;
  plateNo: string;
  workStartTime: string;
  workEndTime: string;
  totalWorkDuration: string;
  distanceTraveled: string;
};

const FleetDriverReport = () => {
  const { t } = useTranslation();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      fleetName: '',
      modelName: '',
      driverName: '',
    },
  });

  // 입력 폼 값
  const [formValues, setFormValues] = useState({
    startDate: formatDate(new Date()),
    endDate: formatDate(new Date()),
    driverName: '',
  });

  /** Params */
  const [driverReportParams, setDriverReportParams] = useState<
    DriverReportParams | undefined
  >();

  /** useQuery */
  const { data: driverReportPage } = useQuery<DriverReportPage | null>({
    queryKey: ['/api/', driverReportParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return {
          rows: [
            {
              driverName: 'John Smith',
              plateNo: 'ABC1234',
              workStartTime: '2025-01-15',
              workEndTime: '2025-06-24',
              totalWorkDuration: '32 hrs',
              distanceTraveled: '433km',
            },
          ],
          page: {
            pageSize: 10,
            totalCnt: 1,
            pageNum: 1,
          },
        };
      } else {
        try {
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: !!driverReportParams,
  });

  const handleSearch = () => {
    setDriverReportParams({
      startDate: formValues.startDate,
      endDate: formValues.endDate,
      driverName: formValues.driverName,
    });
  };

  useEffect(() => {
    handleSearch();
  }, []);

  const columns: ColumnDef<DriverReportRow>[] = [
    {
      header: t('DriverName'),
      accessorKey: 'driverName',
    },
    {
      header: t('VehicleNumber'),
      accessorKey: 'plateNo',
    },
    {
      header: t('WorkStartTime'),
      accessorKey: 'workStartTime',
    },
    {
      header: t('WorkEndTime'),
      accessorKey: 'workEndTime',
    },
    {
      header: t('TotalWorkDuration'),
      accessorKey: 'totalWorkDuration',
    },
    {
      header: t('DistanceTraveled'),
      accessorKey: 'distanceTraveled',
    },
  ];

  return (
    <Tabs.Content value={'AllItineraryHistory'} className={'wrap-layout'}>
      {/* 필터 */}
      <form onSubmit={handleSubmit(handleSearch)}>
        <article className="mb-[10px]">
          <div className="f-e-b">
            <div className="flex items-start gap-4">
              <div className="flex items-start gap-[10px]">
                <FromToSelector
                  initValue={{
                    start: dayjs().subtract(1, 'month').format('YYYY-MM-DD'),
                    end: dayjs().format('YYYY-MM-DD'),
                  }}
                  onInit={(startDate, endDate) => {
                    setFormValues((prev) => ({
                      ...prev,
                      startDate: startDate,
                      endDate: endDate,
                    }));
                  }}
                  onChange={(startDate, endDate) =>
                    setFormValues((prev) => ({
                      ...prev,
                      startDate: startDate,
                      endDate: endDate,
                    }))
                  }
                />
                <Input
                  placeholder={t('DriverName')}
                  value={formValues.driverName}
                  {...register('driverName', {
                    maxLength: {
                      value: 20,
                      message: 'Maximum 20 characters allowed.',
                    },
                  })}
                  error={errors.driverName?.message}
                  onChange={(e) =>
                    setFormValues((prev) => ({
                      ...prev,
                      driverName: e.target.value,
                    }))
                  }
                  reset={() =>
                    setFormValues((prev) => ({
                      ...prev,
                      driverName: '',
                    }))
                  }
                />
              </div>
              <Button type="submit" variant={'bt_primary'} label={'Search'} />
            </div>
            <Button variant={'bt_tertiary_sm'} label={'Download'} />
          </div>
        </article>
      </form>

      {/* 테이블 */}
      <article>
        <CommonTable
          columns={columns}
          data={driverReportPage?.rows || []}
          isCheckbox={false}
          isPagination={true}
          customPageSize={driverReportPage?.page.pageSize ?? 0}
          totalCount={driverReportPage?.page.totalCnt ?? 0}
          currentPage={
            driverReportPage?.page.pageNum
              ? driverReportPage.page.pageNum + 1
              : 1
          }
          onPageChange={(page: number) => {
            setDriverReportParams((prevState) =>
              prevState ? { ...prevState, page: page - 1 } : undefined,
            );
          }}
        />
      </article>
    </Tabs.Content>
  );
};

export default FleetDriverReport;
