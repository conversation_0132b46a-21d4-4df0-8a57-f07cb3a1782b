import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Tabs } from '@radix-ui/themes';
import { DemoTest } from '@/types';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import SummaryData from '@/Common/Components/etc/SummaryData';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { useQuery } from '@tanstack/react-query';
import { driverApi } from '@/api';
import { ColumnDef } from '@tanstack/react-table';

type SummaryResult = {
  driverName: string;
  personalInfo: {
    label: string;
    value?: string | number | React.ReactNode | null;
  }[];
  licenseInfo: {
    label: string;
    value?: string | number | React.ReactNode | null;
  }[];
  basicInfo: {
    label: string;
    value?: string | number | React.ReactNode | null;
  }[];
};

type WorkHistoryParams = {
  driverId: number;
  page: number;
  size: number;
  sort: string;
};

type WorkHistoryPage = {
  rows: WorkHistoryRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type WorkHistoryRow = {
  modelName: string;
  plateNo: string;
  workingStart: string;
  workingEnd: string;
  drivingDistance: string;
  drivingTime: string;
};

type EquipmentParams = {
  driverId: number;
  page: number;
  size: number;
  sort: string;
};

type EquipmentPage = {
  rows: EquipmentRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type EquipmentRow = {
  equipmentId: number;
  vinNumber: string;
  manufacturer: string;
  modelName: string;
  trimName: string;
  manufacturerYear: string;
  plateNo: string;
  vehicleType: string;
};

const FleetDriverDetails = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();

  const [value, setValue] = useState('WorkHistory');
  const [checkedRows, setCheckedRows] = useState<number[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  const driverId: number = location.state?.driverId || 0;

  const {
    openFDDriverModPopup,
    openFDDriverEqAddPopup,
    openFDDriverEqDelPopup,
  } = UseFleetPopup();

  const routeModify = () => {
    navigate(`/fleet-driver/fleetDriverModify`, {
      state: { driverId: driverId },
    });
  };

  /** Params */
  const [workHistoryParams, setWorkHistoryParams] = useState<WorkHistoryParams>(
    {
      driverId: driverId,
      page: 0,
      size: 10,
      sort: 'driverName,asc',
    },
  );

  const [equipmentParams, setEquipmentParams] = useState<EquipmentParams>({
    driverId: driverId,
    page: 0,
    size: 10,
    sort: 'modelName,asc',
  });

  /** useQuery */
  const { data: summaryResult } = useQuery<SummaryResult>({
    queryKey: ['/api/driver/all-detail', driverId],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        const result: SummaryResult = {
          driverName: 'Emily Carter',
          personalInfo: [
            { label: 'PhoneNumber', value: '(*************' },
            { label: 'Email', value: '<EMAIL>' },
          ],
          licenseInfo: [
            { label: 'IssuingState', value: 'Alabama' },
            { label: 'LicenseNumber', value: '1A123455' },
            { label: 'Class', value: 'Class A' },
            { label: 'ExpirationDate', value: '12-12-2024' },
          ],
          basicInfo: [
            { label: 'Gender', value: 'Male' },
            { label: 'IDNumber', value: '1A-12345620' },
          ],
        };
        return result;
      } else {
        try {
          const response = await driverApi.getAdminDriverAllDetail({
            driverId: driverId,
          });

          const result: SummaryResult = {
            driverName: response.data.driverName ?? '-',
            personalInfo: [],
            licenseInfo: [],
            basicInfo: [],
          };

          if (response.data) {
            result.personalInfo.push({
              label: t('PhoneNumber'),
              value: response.data.driverPhone ?? '-',
            });
            result.personalInfo.push({
              label: t('Email'),
              value: response.data.loginId ?? '-', //(운전자로그인 아이디, 이메일)
            });
            result.licenseInfo.push({
              label: t('IssuingState'),
              value: response.data.driverStatus ?? '-',
            });
            result.licenseInfo.push({
              label: t('LicenseNumber'),
              value: response.data.licenseNo ?? '-',
            });
            result.licenseInfo.push({
              label: t('Class'),
              value: response.data.licenseClass ?? '-',
            });
            result.licenseInfo.push({
              label: t('ExpirationDate'),
              value: response.data.licenseExpireDt || '-',
            });
            result.basicInfo.push({
              label: t('Gender'),
              value: response.data.driverGender || '-',
            });
            result.basicInfo.push({
              label: t('IDNumber'),
              value: response.data.driverManagementId || '-',
            });
          }
          return result;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    initialData: {
      driverName: '',
      personalInfo: [],
      licenseInfo: [],
      basicInfo: [],
    },
    //initialData: {},
    enabled: true,
  });

  const { data: workHistoryPage } = useQuery<WorkHistoryPage | null>({
    queryKey: ['/api/equipment/driver/page', workHistoryParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        return {
          rows: [
            {
              modelName: '25B-X',
              plateNo: 'ABC123',
              workingStart: '2025-12-12 12:12',
              workingEnd: '2025-12-12 12:12',
              drivingDistance: '100 km',
              drivingTime: '1 hr',
            },
          ],
          page: {
            pageSize: 10,
            totalCnt: 1,
            pageNum: 0,
          },
        };
      } else {
        try {
          return {
            rows: [],
            page: {
              pageSize: 0,
              totalCnt: 0,
              pageNum: 0,
            },
          };
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    //initialData: {},
    enabled: true,
  });

  const { data: equipmentPage } = useQuery<EquipmentPage | null>({
    queryKey: ['/api/driver/equipment/page', equipmentParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn(false)) {
        return null;
      } else {
        try {
          const response =
            await driverApi.getAdminEquipmentPageOfDriver(equipmentParams);
          if (response.data && response.data.content && response.data.page) {
            const result: EquipmentPage = {
              rows: [],
              page: {
                pageSize: 0,
                totalCnt: 0,
                pageNum: 0,
              },
            };

            response.data.content.forEach((row, index) => {
              result.rows.push({
                equipmentId: row.equipmentId ?? 0,
                vinNumber: row.serialNo ?? '',
                manufacturer: row.manufacturer ?? '',
                modelName: row.modelName ?? '',
                trimName: row.trimName ?? '',
                manufacturerYear: row.productYear?.toString() ?? '',
                plateNo: row.plateNo ?? '',
                vehicleType: row.vehicleType ?? '',
              });

              if (response.data.page) {
                result.page.pageSize =
                  response.data.page.size ?? equipmentParams.size;
                result.page.totalCnt = response.data.page.totalElements ?? 0;
                result.page.pageNum =
                  response.data.page.number ?? equipmentParams.page;
              }
            });
            return result;
          }
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    //initialData: {},
    enabled: true,
  });

  const refreshList = () => {
    resetListCheck();
    setEquipmentParams((prevState) => ({
      ...prevState,
      page: 0,
      _refresh: Date.now(),
    }));
  };

  const resetListCheck = () => {
    setCheckedRows([]);
    setRowSelection({});
  };

  // 행 선택 시 체크박스 값 관리
  const handleSelectionChange = (selectedRows: EquipmentRow[]) => {
    setCheckedRows(selectedRows.map((row) => row.equipmentId));
  };

  // rowSelection 변경 핸들러
  const handleRowSelectionChange = (
    newRowSelection: Record<string, boolean>,
  ) => {
    setRowSelection(newRowSelection);
  };

  // Work History Table
  const workHistoryColumns: ColumnDef<WorkHistoryRow>[] = [
    {
      header: t('ModelName'),
      accessorKey: 'modelName',
    },
    {
      header: t('VehicleNumber'),
      accessorKey: 'plateNo',
    },
    {
      header: t('WorkStartTime'),
      accessorKey: 'workingStart',
    },
    {
      header: t('WorkEndTime'),
      accessorKey: 'workingEnd',
    },
    {
      header: t('DrivingDistance'),
      accessorKey: 'drivingDistance',
    },
    {
      header: t('DrivingTime'),
      accessorKey: 'drivingTime',
    },
  ];

  // Assigned Equipment Table
  const equipmentColumns: ColumnDef<EquipmentRow>[] = [
    {
      header: t('VINNumber'),
      accessorKey: 'vinNumber',
    },
    {
      header: t('Manufacturer'),
      accessorKey: 'manufacturer',
    },
    {
      header: t('ModelName'),
      accessorKey: 'modelName',
    },
    {
      header: t('Trim'),
      accessorKey: 'trimName',
    },
    {
      header: t('YearOfManufacture'),
      accessorKey: 'manufacturerYear',
    },
    {
      header: t('VehicleNumber'),
      accessorKey: 'plateNo',
    },
    {
      header: t('VehicleType'),
      accessorKey: 'vehicleType',
    },
  ];

  return (
    <CustomFrame name={t('DriverDetails')} back={true}>
      <section className="wrap-layout">
        {/* 요약 데이터 */}
        <article className="mb-10">
          <h2 className="mb-3 f-c-b subtitle3">
            {summaryResult?.driverName || '-'}
            <Button
              variant={'bt_tertiary_sm'}
              label={t('Edit')}
              onClick={routeModify}
            />
          </h2>
          <div className="flex flex-col gap-2 [&>div]:flex [&>div]:gap-3 [&_h3]:w-[170px] [&_h3]:subtitle4 ">
            <div>
              <h3>{t('Personal Information')}</h3>
              <SummaryData
                details={summaryResult?.personalInfo || []}
                fs="lg"
              />
            </div>
            <div>
              <h3>{t('License Information')}</h3>
              <SummaryData details={summaryResult?.licenseInfo || []} fs="lg" />
            </div>
            <div>
              <h3>{t('Basic Information')}</h3>
              <SummaryData details={summaryResult?.basicInfo || []} fs="lg" />
            </div>
          </div>
        </article>

        <Tabs.Root value={value} onValueChange={setValue}>
          <Tabs.List className="mb-[30px] tab-design">
            <Tabs.Trigger value={'WorkHistory'}>
              <span>{t('WorkHistory')}</span>
            </Tabs.Trigger>
            <Tabs.Trigger value={'AssignedVehicle'}>
              <span>{t('AssignedVehicle')}</span>
            </Tabs.Trigger>
          </Tabs.List>

          <Tabs.Content value={'WorkHistory'}>
            <CommonTable
              columns={workHistoryColumns}
              data={workHistoryPage?.rows || []}
              isPagination={true}
              customPageSize={workHistoryPage?.page.pageSize ?? 0}
              totalCount={workHistoryPage?.page.totalCnt ?? 0}
              currentPage={
                workHistoryPage?.page.pageNum
                  ? workHistoryPage.page.pageNum + 1
                  : 1
              }
              onPageChange={(page: number) => {
                setWorkHistoryParams((prevState) => ({
                  ...prevState,
                  page: page - 1,
                }));
              }}
            />
          </Tabs.Content>

          <Tabs.Content value={'AssignedVehicle'}>
            <div className="mb-[10px] f-c-b">
              <div className="f-c gap-3">
                <h2 className="body2">{t('AssignableEquipment')}</h2>
                <p className="subtitle4 text-primary-10">
                  {equipmentPage?.page.totalCnt} {t('Unit')}
                </p>
              </div>
              <div className="f-c gap-2">
                <Button
                  variant={'bt_primary_sm'}
                  label={'Delete'}
                  disabled={checkedRows.length === 0}
                  onClick={() =>
                    openFDDriverEqDelPopup(driverId, checkedRows, refreshList)
                  }
                />
                <Button
                  variant={'bt_primary_sm'}
                  label={'Add'}
                  onClick={() => openFDDriverEqAddPopup(driverId, refreshList)}
                />
              </div>
            </div>
            <CommonTable
              columns={equipmentColumns}
              data={equipmentPage?.rows || []}
              isCheckbox={true}
              onSelectionChange={handleSelectionChange}
              rowSelection={rowSelection}
              onRowSelectionChange={handleRowSelectionChange}
              isPagination={true}
              customPageSize={equipmentPage?.page.pageSize ?? 0}
              totalCount={equipmentPage?.page.totalCnt ?? 0}
              currentPage={
                equipmentPage?.page.pageNum ? equipmentPage.page.pageNum + 1 : 1
              }
              onPageChange={(page: number) => {
                setEquipmentParams((prevState) => ({
                  ...prevState,
                  page: page - 1,
                }));
                resetListCheck;
              }}
            />
          </Tabs.Content>
        </Tabs.Root>
      </section>
    </CustomFrame>
  );
};

export default FleetDriverDetails;
