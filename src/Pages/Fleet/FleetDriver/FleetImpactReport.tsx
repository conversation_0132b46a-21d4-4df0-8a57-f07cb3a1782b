import { useTranslation } from 'react-i18next';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { Tabs } from '@radix-ui/themes';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import Dropdown from '@/Common/Components/common/DropDown';

const FleetImpactReport = () => {
  const { t } = useTranslation();

  const { openDShockInfoPopup } = UseFleetPopup();

  const fleetOptions = [
    { key: 'Fleet1', value: 'fleet1' },
    { key: 'Fleet2', value: 'fleet2' },
    { key: 'Fleet3', value: 'fleet3' },
  ];

  const columns = [
    {
      header: t('FleetName'),
      accessorKey: 'fleet',
      cell: () => (
        <Dropdown
          size={'no'}
          onChange={() => undefined}
          options={fleetOptions}
          placeholder={'CARTA FLEET'}
        />
      ),
    },
    {
      header: t('ImpactTime'),
      accessorKey: 'date',
    },
    {
      header: t('ModelName'),
      accessorKey: 'model',
    },
    {
      header: t('VehicleNumber'),
      accessorKey: 'unit',
    },
    {
      header: t('DriverName'),
      accessorKey: 'driver',
    },
    {
      header: t('ImpactInformation'),
      accessorKey: 'info',
      cell: () => (
        <span onClick={openDShockInfoPopup} className="blue-underline">
          {t('View')}
        </span>
      ),
    },
  ];
  const data = [
    {
      fleet: 'fleet',
      date: '2025-05-10',
      model: '180B-CA',
      unit: '25B-123412',
      driver: 'John Smith',
      info: 'info',
    },
  ];

  return (
    <Tabs.Content value={'FleetImpactReport'} className="wrap-layout">
      {/* 필터 */}
      <article>
        <div className="mb-[10px] f-e-b gap-4">
          <div className="f-c gap-[10px]">
            <FromToSelector />
            <Input placeholder={t('FleetName')} />
            <Input placeholder={t('ModelName ')} />
            <Input placeholder={t('VehicleNumber')} />
            <Input placeholder={t('DriverName')} />
            <Button variant={'bt_primary'} label={'Search'} />
          </div>
          <Button variant={'bt_tertiary_sm'} label={'Download'} />
        </div>
      </article>

      {/* 테이블 */}
      <article className="w-full overflow-x-auto">
        <CommonTable
          columns={columns}
          data={data}
          isPagination={true}
          isCheckbox={false}
        />
      </article>
    </Tabs.Content>
  );
};

export default FleetImpactReport;
