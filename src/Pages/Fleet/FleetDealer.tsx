import { useTranslation } from 'react-i18next';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { ChangeEvent, useState, useMemo } from 'react';

interface FleetSelectItem {
  key: string;
  value: string;
}

interface ExpendablesData {
  status: string;
  fleet: string;
  model: string;
  equipmentId: string;
  expendablesName: string;
  cycle: string;
  remain: string;
  regDt: string;
  placeOrder: string;
}

interface FleetDealerParameters {
  pageNum: number;
  pageSize: number;
  langType: string;
  status: string;
  startDate: string;
  endDate: string;
  fleet?: string;
  model?: string;
  equipmentId?: string;
}

const FleetDealer = () => {
  const { t } = useTranslation();

  const [pageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(50); // Mock total count
  const [pageNum] = useState(1);

  // 현재 날짜와 한 달 전 날짜 계산
  const today = new Date();
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(today.getMonth() - 1);

  // 날짜 포맷 함수 (YYYY-MM-DD)
  const formatDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 입력 필드 값을 저장할 상태
  const [inputValues, setInputValues] = useState({
    status: 'ALL',
    startDate: formatDate(oneMonthAgo),
    endDate: formatDate(today),
    fleet: '',
    model: '',
    equipmentId: '',
  });

  // 상태 초기화
  const [FleetDealerParameters, setFleetDealerParameters] =
    useState<FleetDealerParameters>({
      pageNum: pageNum,
      pageSize: pageSize,
      langType: 'KR',
      status: 'ALL',
      startDate: formatDate(oneMonthAgo),
      endDate: formatDate(today),
      fleet: undefined,
      model: undefined,
      equipmentId: undefined,
    });

  const handleSearch = async () => {
    try {
      // 페이지 번호 초기화 및 파라미터 설정
      const newParams: FleetDealerParameters = {
        ...FleetDealerParameters,
        pageNum: 1,
        status: inputValues.status,
        fleet: inputValues.fleet || undefined,
        model: inputValues.model || undefined,
        equipmentId: inputValues.equipmentId || undefined,
        startDate: inputValues.startDate,
        endDate: inputValues.endDate,
      };
      setFleetDealerParameters(newParams);
      console.log('Search parameters:', newParams);
    } catch (error) {
      console.error('Search error:', error);
    }
  };

  // Mock 소모품 데이터
  const mockExpendablesData: ExpendablesData[] = useMemo(
    () => [
      {
        status: 'EXCHANGE',
        fleet: 'Fleet A',
        model: 'EX120-5',
        equipmentId: 'EQ001',
        expendablesName: 'Engine Oil',
        cycle: '500',
        remain: '50',
        regDt: '2024-01-15',
        placeOrder: 'SN123456',
      },
      {
        status: 'IMMINENT',
        fleet: 'Fleet B',
        model: 'EX200-6',
        equipmentId: 'EQ002',
        expendablesName: 'Air Filter',
        cycle: '1000',
        remain: '100',
        regDt: '2024-02-10',
        placeOrder: 'SN789012',
      },
      {
        status: 'EXCESS',
        fleet: 'Fleet C',
        model: 'DX140LC-5',
        equipmentId: 'EQ003',
        expendablesName: 'Hydraulic Oil',
        cycle: '2000',
        remain: '150',
        regDt: '2024-03-05',
        placeOrder: 'SN345678',
      },
      {
        status: 'EXCHANGE',
        fleet: 'Fleet A',
        model: 'EX120-5',
        equipmentId: 'EQ004',
        expendablesName: 'Fuel Filter',
        cycle: '800',
        remain: '80',
        regDt: '2024-04-12',
        placeOrder: 'SN901234',
      },
      {
        status: 'IMMINENT',
        fleet: 'Fleet D',
        model: 'EX300-7',
        equipmentId: 'EQ005',
        expendablesName: 'Transmission Oil',
        cycle: '1500',
        remain: '120',
        regDt: '2024-05-20',
        placeOrder: 'SN567890',
      },
    ],
    [],
  );

  // Mock 플릿 드롭다운 데이터
  const mockFleetDropData: FleetSelectItem[] = useMemo(
    () => [
      { key: '전체', value: 'ALL' },
      { key: 'Fleet A', value: 'Fleet A' },
      { key: 'Fleet B', value: 'Fleet B' },
      { key: 'Fleet C', value: 'Fleet C' },
      { key: 'Fleet D', value: 'Fleet D' },
    ],
    [],
  );

  // 드롭다운 변경 핸들러
  const handleDropdownChange = (field: string, value: string) => {
    console.log(value);
    setInputValues((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // 입력 필드 변경 핸들러
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // 초기화 버튼 핸들러
  const resetModelInput = () => {
    setInputValues((prev) => ({
      ...prev,
      model: '',
    }));
  };

  const resetHogiInput = () => {
    setInputValues((prev) => ({
      ...prev,
      equipmentId: '',
    }));
  };

  // 날짜 범위 변경 핸들러
  const handleDateRangeChange = (startDate: string, endDate: string) => {
    const formattedStartDate = formatDate(new Date(startDate));
    const formattedEndDate = formatDate(new Date(endDate));
    console.log(formattedStartDate);
    console.log(formattedEndDate);

    setInputValues((prev) => ({
      ...prev,
      startDate: formattedStartDate,
      endDate: formattedEndDate,
    }));
  };

  // 검색 조건 변경 함수
  const updateSearchParams = (
    field: keyof FleetDealerParameters,
    value: string | number | boolean,
  ) => {
    setFleetDealerParameters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // 페이지 변경 핸들러
  const handlePageChange = (newPage: number) => {
    updateSearchParams('pageNum', newPage);
  };

  const statusOptions = [
    { key: t('All'), value: 'ALL' },
    { key: t('Overdue'), value: 'EXCHANGE' },
    { key: t('Approaching'), value: 'IMMINENT' },
    { key: t('Replacement'), value: 'EXCESS' },
  ];

  const variantColor = (status: string) => {
    switch (status) {
      case 'EXCHANGE':
        return 'blue-lg';
      case 'IMMINENT':
        return 'yellow-lg';
      case 'EXCESS':
        return 'red-lg';
      default:
        return 'blue-lg';
    }
  };

  const columns = [
    {
      header: t('State'),
      accessorKey: 'status',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => {
        const status = cell.getValue() as string;
        const color = variantColor(status);
        return <Button variant={'bt_primary'} label={status} />;
      },
    },
    {
      header: t('Fleet'),
      accessorKey: 'fleet',
    },
    {
      header: t('Model'),
      accessorKey: 'model',
    },
    {
      header: t('MachineID'),
      accessorKey: 'equipmentId',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => (
        <span className="blue-underline">{cell.getValue() as string}</span>
      ),
    },
    {
      header: t('Item'),
      accessorKey: 'expendablesName',
    },
    {
      header: t('MaintenanceIntervalH'),
      accessorKey: 'cycle',
    },
    {
      header: t('RemainingTimeToNextMaintenanceH'),
      accessorKey: 'remain',
    },
    {
      header: t('Date'),
      accessorKey: 'regDt',
    },
    {
      header: t('PlaceOrder'),
      accessorKey: 'placeOrder',
      cell: () => <span className="blue-underline">{t('Order')}</span>,
    },
  ];

  return (
    <CustomFrame name={t('DealerServiceCenterManagement')} back={false}>
      {/* 필터 */}
      <section className="p-4 sm:p-6 md:p-10">
        <article>
          <div className="mb-6 md:mb-10 flex flex-col md:flex-row md:items-end justify-between gap-5 md:gap-0">
            <div className="flex flex-col items-start">
              {/*  */}
              <div className="mb-5 flex flex-col md:flex-row md:items-center gap-4 md:gap-6">
                <SearchItemContainer className="w-full md:w-auto">
                  <SearchLabel>{t('State')}</SearchLabel>
                  <DropDown
                    onChange={(value) =>
                      handleDropdownChange('status', value.toString())
                    }
                    options={statusOptions}
                    placeholder={t('All')}
                  />
                </SearchItemContainer>
                <SearchItemContainer className="w-full md:w-auto">
                  <SearchLabel>{t('Fleet')}</SearchLabel>
                  <DropDown
                    onChange={(value) =>
                      handleDropdownChange('fleet', value.toString())
                    }
                    options={mockFleetDropData}
                    placeholder={t('All')}
                  />
                </SearchItemContainer>
                <div className="flex items-center gap-3 md:gap-6 w-full md:w-auto">
                  <span className="body1-b">{t('Model')}</span>
                  <Input
                    placeholder={t('Model')}
                    name="model"
                    value={inputValues.model}
                    onChange={handleInputChange}
                    reset={resetModelInput}
                  />
                </div>
                <div className="flex items-center gap-3 md:gap-6 w-full md:w-auto">
                  <span className="body1-b">{t('MachineID')}</span>
                  <Input
                    placeholder={t('MachineID')}
                    name="equipmentId"
                    value={inputValues.equipmentId}
                    onChange={handleInputChange}
                    reset={resetHogiInput}
                  />
                </div>
              </div>
              {/*  */}
              <SearchItemContainer className="w-full md:w-auto">
                <SearchLabel>{t('DateFaultOccurenceDate')}</SearchLabel>
                <FromToSelector
                  onChange={(startDate, endDate) =>
                    handleDateRangeChange(startDate, endDate)
                  }
                />
              </SearchItemContainer>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant={'bt_primary'}
                label={'Search'}
                onClick={handleSearch}
              />
              <Button variant={'bt_primary'} label={'Print'} />
            </div>
          </div>
        </article>

        {/* 테이블 */}
        <article>
          <div className="overflow-x-auto md:overflow-visible">
            <CommonTable
              columns={columns}
              data={mockExpendablesData}
              isPagination={true}
              isCheckbox={true}
              customPageSize={pageSize}
              currentPage={FleetDealerParameters.pageNum}
              totalCount={totalCount}
              onPageChange={handlePageChange}
            />
          </div>
        </article>
      </section>
    </CustomFrame>
  );
};

export default FleetDealer;
