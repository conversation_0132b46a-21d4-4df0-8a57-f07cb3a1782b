import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { Cross1Icon } from '@radix-ui/react-icons';
import { useToast } from '@/Common/useToast';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import FileCard from '@/Common/Components/common/FileCard.tsx';
import FileExcel from '@/Common/Components/common/FileExcel';
import Excel from '@/assets/images/svg/28/Excel';
import Pen from '@/assets/images/svg/28/Pen';

interface FDDriverRegistrationPopupProps extends AlertPopupProps {
  onManualRegister?: () => void;
}

const FDDriverRegistrationPopup = ({
  isOpen,
  onClose,
  onManualRegister,
}: FDDriverRegistrationPopupProps) => {
  const { t } = useTranslation();

  const { toast } = useToast();

  const [step, setStep] = useState<'select' | 'upload' | 'registration'>(
    'select',
  );
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  // 엑셀 업로드 완료 시
  const excelUploadConfirm = () => {
    toast({
      types: 'success',
      description: t('DriverListHasBeenSuccessfullyUploaded'),
    });
    onClose?.();
  };

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[600px] popup-wrap">
        <article>
          <h2>
            {showCancelConfirm ? t('UploadCanceled') : t('DriverRegistration')}
          </h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/* 선택 화면 */}
        {step === 'select' && (
          <article className="mb-5">
            <h3 className="mb-6 body1 text-center">
              {t('PleaseChooseHowYoudLikeToRegisterDrivers')}
            </h3>
            <div className="f-c-c gap-[10px]">
              <FileCard
                title={t('UploadViaExcel')}
                icon={<Excel />}
                onClick={() => setStep('upload')}
              />
              <FileCard
                title={t('RegisterManually')}
                icon={<Pen />}
                onClick={onManualRegister}
              />
            </div>
          </article>
        )}

        {/* 엑셀 업로드 */}
        {step === 'upload' && (
          <article>
            <FileExcel
              onShowCancelConfirmChange={setShowCancelConfirm}
              onConfirm={excelUploadConfirm}
            />
          </article>
        )}
      </section>
    </Layout>
  );
};

export default FDDriverRegistrationPopup;
