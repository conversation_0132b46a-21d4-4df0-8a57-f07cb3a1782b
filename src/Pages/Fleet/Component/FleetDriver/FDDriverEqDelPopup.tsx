import { driverApi, fleetApi } from '@/api';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';
import { toast } from '@/Common/useToast';
import { AlertPopupProps } from '@/types';
import { useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

const FDDriverEqDelPopup = (
  props: AlertPopupProps & {
    driverId: number;
    equipmentIds: number[] | undefined;
  },
) => {
  const { t } = useTranslation();

  const handleConfirm = () => {
    if (!props.equipmentIds) return;
    delFleetMutation.mutate({
      driverId: props.driverId,
      equipmentIdList: props.equipmentIds,
    });
  };

  const delFleetMutation = useMutation({
    mutationFn: (params: { driverId: number; equipmentIdList: number[] }) => {
      return driverApi.unregisterAdminEquipmentListFromDriver(params);
    },
    onSuccess: () => {
      toast({
        types: 'success',
        description: t('DeleteSuccess'),
      });
      props.onConfirm?.();
    },
    onError: () => {
      toast({
        types: 'error',
        description: t('DeleteFail'),
      });
    },
  });

  return <TwoButtonPopup {...props} onConfirm={handleConfirm} />;
};

export default FDDriverEqDelPopup;
