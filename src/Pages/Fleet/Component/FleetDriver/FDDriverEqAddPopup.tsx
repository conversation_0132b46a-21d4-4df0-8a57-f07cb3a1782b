import { useTranslation } from 'react-i18next';
import { AlertPopupProps, DemoTest } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import Input from '@/Common/Components/common/Input';
import CommonTable from '@/Common/Components/common/CommonTable';
import { Button } from '@/Common/Components/common/Button';
import { Cross1Icon } from '@radix-ui/react-icons';
import { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { useMutation, useQuery } from '@tanstack/react-query';
import { driverApi } from '@/api';
import { toast } from '@/Common/useToast';

type AvailEquipmentParams = {
  driverId: number;
  serialNo?: string;
  manufacturer?: string;
  modelName?: string;
  plateNo?: string;
  page: number;
  size: number;
  sort: string;
};

type AvailEquipmentPage = {
  rows: AvailEquipmentRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type AvailEquipmentRow = {
  equipmentId: number;
  vinNumber: string;
  manufacturer: string;
  modelName: string;
  trimName: string;
  manufacturerYear: string;
  plateNo: string;
  vehicleType: string;
};

const FDDriverEqAddPopup = ({
  driverId,
  isOpen,
  onClose,
  onConfirm,
}: AlertPopupProps & { driverId: number }) => {
  const { t } = useTranslation();

  const [checkedRows, setCheckedRows] = useState<number[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [formValues, setFormValues] = useState({
    vinNumber: '',
    manufacturer: '',
    modelName: '',
    plateNo: '',
  });

  const columns: ColumnDef<AvailEquipmentRow>[] = [
    {
      header: t('VINNumber'),
      accessorKey: 'vinNumber',
    },
    {
      header: t('Manufacturer'),
      accessorKey: 'manufacturer',
    },
    {
      header: t('ModelName'),
      accessorKey: 'modelName',
    },
    {
      header: t('TrimName'),
      accessorKey: 'trimName',
    },
    {
      header: t('YearOfManufacture'),
      accessorKey: 'manufacturerYear',
    },
    {
      header: t('VehicleNumber'),
      accessorKey: 'plateNo',
    },
    {
      header: t('VehicleType'),
      accessorKey: 'vehicleType',
    },
  ];

  /** Params */
  const [availEquipmentParams, setAvailEquipmentParams] =
    useState<AvailEquipmentParams>({
      driverId: driverId,
      page: 0,
      size: 10,
      sort: 'driverName,asc',
    });

  /** useQuery */
  const { data: availEquipmentPage } = useQuery({
    queryKey: [
      '/api/driver/equipment/for-registration/page',
      availEquipmentParams,
    ],
    queryFn: async () => {
      if (DemoTest.isRandomOn(false)) {
        return null;
      } else {
        try {
          const response =
            await driverApi.getAdminEquipmentPageForRegistration1(
              availEquipmentParams,
            );

          if (response.data && response.data.content && response.data.page) {
            const result: AvailEquipmentPage = {
              rows: [],
              page: {
                pageSize: 0,
                totalCnt: 0,
                pageNum: 0,
              },
            };

            response.data.content.forEach((row, index) => {
              result.rows.push({
                equipmentId: row.equipmentId ?? 0,
                vinNumber: row.serialNo ?? '',
                manufacturer: row.manufacturer ?? '',
                modelName: row.modelName ?? '',
                trimName: row.trimName ?? '',
                manufacturerYear: row.productYear?.toString() ?? '',
                plateNo: row.plateNo ?? '',
                vehicleType: row.vehicleType ?? '',
              });
            });
            if (response.data.page) {
              result.page.pageSize =
                response.data.page.size ?? availEquipmentParams.size;
              result.page.totalCnt = response.data.page.totalElements ?? 0;
              result.page.pageNum = response.data.page.number ?? 0;
            }
            return result;
          }
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: true,
  });

  const handleConfirm = () => {
    if (checkedRows.length == 0) return;
    addEquipmentMutation.mutate(checkedRows);
  };

  const addEquipmentMutation = useMutation({
    mutationFn: (params: number[]) => {
      return driverApi.registerAdminEquipmentListToDriver({
        driverId: driverId,
        equipmentIdList: params,
      });
    },
    onSuccess: () => {
      toast({
        types: 'success',
        description: t('AddSuccess'),
      });
      onConfirm?.();
    },
    onError: () => {
      toast({
        types: 'error',
        description: t('AddFail'),
      });
    },
  });

  const resetListCheck = () => {
    setCheckedRows([]);
    setRowSelection({});
  };

  const handleSearch = () => {
    setAvailEquipmentParams({
      driverId: driverId,
      serialNo: formValues.vinNumber,
      manufacturer: formValues.manufacturer,
      modelName: formValues.modelName,
      plateNo: formValues.plateNo,
      page: 0,
      size: 10,
      sort: 'driverName,asc',
    });
  };

  // 행 선택 시 체크박스 값 관리
  const handleSelectionChange = (selectedRows: AvailEquipmentRow[]) => {
    setCheckedRows(selectedRows.map((row) => row.equipmentId));
  };

  // rowSelection 변경 핸들러
  const handleRowSelectionChange = (
    newRowSelection: Record<string, boolean>,
  ) => {
    setRowSelection(newRowSelection);
  };

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[1100px] popup-wrap">
        {/*  */}
        <article>
          <h2>{t('AddVehicle')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/*  */}
        <article>
          <div className="mb-6 f-c gap-[10px]">
            <Input
              placeholder={t('VINNumber')}
              onChange={(e) =>
                setFormValues({ ...formValues, vinNumber: e.target.value })
              }
            />
            <Input
              placeholder={t('Manufacturer')}
              onChange={(e) =>
                setFormValues({ ...formValues, manufacturer: e.target.value })
              }
            />
            <Input
              placeholder={t('ModelName')}
              onChange={(e) =>
                setFormValues({ ...formValues, modelName: e.target.value })
              }
            />
            <Input
              placeholder={t('VehicleNumber')}
              onChange={(e) =>
                setFormValues({ ...formValues, plateNo: e.target.value })
              }
            />
            <Button
              variant={'bt_primary'}
              label={'Search'}
              onClick={handleSearch}
            />
          </div>

          <div className="table-border">
            <CommonTable
              columns={columns}
              data={availEquipmentPage?.rows || []}
              isCheckbox={true}
              onSelectionChange={handleSelectionChange}
              rowSelection={rowSelection}
              onRowSelectionChange={handleRowSelectionChange}
              isPagination={true}
              customPageSize={availEquipmentPage?.page.pageSize ?? 0}
              totalCount={availEquipmentPage?.page.totalCnt ?? 0}
              currentPage={
                availEquipmentPage?.page.pageNum
                  ? availEquipmentPage.page.pageNum + 1
                  : 1
              }
              onPageChange={(page: number) => {
                setAvailEquipmentParams((prevState) => ({
                  ...prevState,
                  page: page - 1,
                }));
                resetListCheck();
              }}
            />
          </div>

          <div className="mt-[30px] f-c-e gap-[10px]">
            <Button
              variant={'bt_secondary'}
              label={'Cancel'}
              onClick={onClose}
            />
            <Button
              variant={'bt_primary'}
              label={'Add'}
              onClick={handleConfirm}
              disabled={checkedRows.length === 0}
            />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default FDDriverEqAddPopup;
