import { driverApi } from '@/api';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';
import { toast } from '@/Common/useToast';
import { AlertPopupProps } from '@/types';
import { useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

const DriverDelPopup = (
  props: AlertPopupProps & {
    driverIds: number[] | undefined;
  },
) => {
  const { t } = useTranslation();

  const handleConfirm = () => {
    if (!props.driverIds) return;
    delDriverMutation.mutate({
      driverIdList: props.driverIds,
    });
  };

  const delDriverMutation = useMutation({
    //'/api/driver'
    mutationFn: (params: { driverIdList: number[] }) => {
      return driverApi.deleteAdminDriver(params);
    },
    onSuccess: () => {
      toast({
        types: 'success',
        description: t('DeleteSuccess'),
      });
      props.onConfirm?.();
    },
    onError: () => {
      toast({
        types: 'error',
        description: t('DeleteFail'),
      });
    },
  });

  return <TwoButtonPopup {...props} onConfirm={handleConfirm} />;
};

export default DriverDelPopup;
