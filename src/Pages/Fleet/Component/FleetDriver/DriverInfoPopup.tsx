import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import { Button } from '@/Common/Components/common/Button';
import SummaryData from '@/Common/Components/etc/SummaryData';
import CommonTable from '@/Common/Components/common/CommonTable';
import { Cross1Icon } from '@radix-ui/react-icons';

const DriverInfoPopup = ({ onClose, isOpen }: AlertPopupProps) => {
  const { t } = useTranslation();

  const summaryData = [
    { label: 'Country', value: 'United States' },
    { label: 'Model', value: '25B-X' },
    { label: 'MachineID', value: '00010' },
    { label: 'SerialNo', value: 'HHKHFT23JF0000919' },
    { label: 'DealerD', value: 'SHRIVENKATESHA HEAVY EQUIPMENT' },
    {
      label: 'Location',
      value: 'Wilshire Blvd, Los Angeles, CA 90036 USA',
    },
  ];

  const columns = [
    {
      header: t('Driver'),
      accessorKey: 'driver',
    },
    {
      header: t('IDType'),
      accessorKey: 'id',
    },
    {
      header: t('LicenseStartDate'),
      accessorKey: 'startD',
    },
    {
      header: t('LicenseExpiryDate'),
      accessorKey: 'endD',
    },
    {
      header: t('StartTime'),
      accessorKey: 'startH',
    },
    {
      header: t('EndTime'),
      accessorKey: 'endH',
    },
    {
      header: t('OperableEquipment'),
      accessorKey: 'eq',
    },
  ];
  const data = [
    {
      driver: 'Daniel',
      id: 'PIN',
      startD: '2021-04-15',
      endD: '2032-04-15',
      startH: '',
      endH: '',
      eq: '8',
    },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[1236px] p-10 bg-white rounded-lg">
        {/*  */}
        <article className="heading2 mb-[34px] flex items-center justify-between">
          {t('DriverInformation')}
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/* 요약 */}
        <article className="mb-8 flex justify-start">
          <div className="body1-b mr-[26px]">{t('MachineInformation')}</div>
          <SummaryData details={summaryData} fs="lg" />
        </article>

        {/* 테이블 */}
        <article className="border-t border-x border-gray-1">
          <CommonTable columns={columns} data={data} isPagination={false} />
        </article>

        {/* 버튼 */}
        <article className="mt-10 flex justify-end">
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
        </article>
      </section>
    </Layout>
  );
};

export default DriverInfoPopup;
