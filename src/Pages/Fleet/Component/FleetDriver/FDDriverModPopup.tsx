import { useTranslation } from 'react-i18next';
import { AlertPopupProps, DemoTest, DropdownOption } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import DropDown from '@/Common/Components/common/DropDown';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import DaySelector from '@/Common/Components/datePicker/DaySelector';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import { Cross1Icon } from '@radix-ui/react-icons';
import { useCallback, useState } from 'react';
import { countryApi, driverApi } from '@/api';
import DropDownPaginated from '@/Common/Components/common/DropDownPaginated';
import { useForm } from 'react-hook-form';
import { useMutation } from '@tanstack/react-query';
import { toast } from '@/Common/useToast';

type FormValues = {
  driverName: string;
  idNumber: string;
  country: { key: string; value: string };
  issuingState: { key: string; value: string };
  licenseNumber: string;
  class: { key: string; value: string };
  licenseStartDate: string;
  licenseExpiryDate: string;
};

const FDDriverModPopup = ({ isOpen, onClose, onConfirm }: AlertPopupProps) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      driverName: '',
      idNumber: '',
      licenseNumber: '',
    },
  });

  const [formValues, setFormValues] = useState<FormValues>({
    driverName: '',
    idNumber: '',
    country: { key: '', value: '' },
    issuingState: { key: '', value: '' },
    licenseNumber: '',
    class: { key: '', value: '' },
    licenseStartDate: '',
    licenseExpiryDate: '',
  });

  const countryOptions = useCallback(
    async (page: number): Promise<DropdownOption[]> => {
      if (DemoTest.isRandomOn(false)) {
        return page === 0
          ? [
              { key: t('AllCountries'), value: '' },
              { key: 'KOR', value: '1' },
              { key: 'USA', value: '2' },
            ]
          : [];
      }

      // 실제 API 호출
      try {
        const response = await countryApi.getAdminCountryPage({
          page: page,
          size: 100,
          sort: 'countryName,asc',
        });

        const result = response?.data?.content ?? [];

        const options = result
          .filter((item) => item.countryName && item.countryId !== undefined)
          .map((item) => ({
            key: item.countryName!,
            value: item.countryId!.toString(),
          }));

        // 첫 페이지에만 "All" 옵션 추가
        return page === 0
          ? [{ key: t('AllCountries'), value: '' }, ...options]
          : options;
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    [],
  );

  const issuingStateOptions = useCallback(
    async (page: number): Promise<DropdownOption[]> => {
      if (DemoTest.isRandomOn()) {
        return page === 0 ? [{ key: t('AllStates'), value: '' }] : [];
      }

      // 실제 API 호출
      try {
        // const response = await countryApi.getStatePage({
        //   page: page,
        //   size: 100,
        //   sort: 'stateName,asc',
        // });

        // const result = response?.data?.content ?? [];

        // const options = result
        //   .filter((item) => item.stateName && item.stateId !== undefined)
        //   .map((item) => ({
        //     key: item.stateName!,
        //     value: item.stateId!.toString(),
        //   }));

        // // 첫 페이지에만 "All" 옵션 추가
        // return page === 0
        //   ? [{ key: t('AllStates'), value: '' }, ...options]
        //   : options;
        return page === 0 ? [{ key: t('AllStates'), value: '' }] : [];
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    [],
  );

  const classOptions = useCallback(
    async (page: number): Promise<DropdownOption[]> => {
      if (DemoTest.isRandomOn()) {
        return page === 0 ? [{ key: t('AllClasses'), value: '' }] : [];
      }

      // 실제 API 호출
      try {
        // const response = await countryApi.getStatePage({
        //   page: page,
        //   size: 100,
        //   sort: 'stateName,asc',
        // });

        // const result = response?.data?.content ?? [];

        // const options = result
        //   .filter((item) => item.stateName && item.stateId !== undefined)
        //   .map((item) => ({
        //     key: item.stateName!,
        //     value: item.stateId!.toString(),
        //   }));

        // // 첫 페이지에만 "All" 옵션 추가
        // return page === 0
        //   ? [{ key: t('AllStates'), value: '' }, ...options]
        //   : options;
        return page === 0 ? [{ key: t('AllClasses'), value: '' }] : [];
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    [],
  );

  const handleConfirm = () => {
    // 검색 로직 구현
  };

  const modDriverMutation = useMutation({
    //'/api/driver'
    mutationFn: (params: {
      driverIdList: number[];
      driverName: string;
      driverPhone: string;
    }) => {
      //return driverApi.modifyDriver(params);
      return Promise.reject(new Error('Not implemented'));
    },
    onSuccess: () => {
      toast({
        types: 'success',
        description: t('ModifySuccess'),
      });
      onConfirm?.();
    },
    onError: () => {
      toast({
        types: 'error',
        description: t('ModifyFail'),
      });
    },
  });

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[600px] popup-wrap">
        {/*  */}
        <article>
          <h2>{t('EditDriver')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/*  */}
        <form onSubmit={handleSubmit(handleConfirm)}>
          <article
            className={`w-full f-s flex-col gap-3 [&>div]:w-full [&>div]:f-c [&_span]:w-[140px] [&_span]:mr-[26px] [&_span]:flex-shrink-0 ${isEnglish ? '[&_span]:w-[186px] ' : ''}`}
          >
            {/* 운전자명 */}
            <div>
              <span className="body1">
                {t('DriverName')} <em className="text-semantic-4">*</em>
              </span>
              <Input
                placeholder={t('DriverName')}
                {...register('driverName', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                })}
                error={errors.driverName?.message}
              />
            </div>
            {/* ID 번호 */}
            <div>
              <span className="body1">
                {t('DriverIDNumber')} <em className="text-semantic-4">*</em>
              </span>
              <Input
                placeholder={t('DriverIDNumber')}
                {...register('idNumber', {
                  maxLength: {
                    value: 10,
                    message: 'Maximum 10 characters allowed.',
                  },
                  pattern: {
                    value: /^[0-9]*$/,
                    message: 'Only numbers allowed.',
                  },
                })}
                error={errors.idNumber?.message}
              />
            </div>
            {/* 국가 */}
            <SearchItemContainer className="gap-0">
              <span className="body1">
                {t('Country')} <em className="text-semantic-4">*</em>
              </span>
              <DropDownPaginated
                className="max-w-full"
                loadOptions={countryOptions}
                placeholder={t('AllCountries')}
                selectedKey={formValues.country.key}
                onSelPair={(key: string, value: string) => {
                  setFormValues((prev) => ({
                    ...prev,
                    country: { key, value },
                  }));
                }}
              />
            </SearchItemContainer>
            {/* 면허 발급 주 */}
            <SearchItemContainer className="gap-0">
              <span className="body1">
                {t('IssuingState')} <em className="text-semantic-4">*</em>
              </span>
              <DropDownPaginated
                className="max-w-full"
                loadOptions={issuingStateOptions}
                placeholder={t('IssuingState')}
                selectedKey={formValues.issuingState.key}
                onSelPair={(key: string, value: string) => {
                  setFormValues((prev) => ({
                    ...prev,
                    issuingState: { key, value },
                  }));
                }}
              />
            </SearchItemContainer>
            {/* 면허 번호 */}
            <div>
              <span className="body1">
                {t('LicenseNumber')} <em className="text-semantic-4">*</em>
              </span>
              <Input
                placeholder={t('LicenseNumber')}
                {...register('licenseNumber', {
                  maxLength: {
                    value: 12,
                    message: 'Maximum 12 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.licenseNumber?.message}
              />
            </div>
            {/* 면허 등급 */}
            <SearchItemContainer className="gap-0">
              <span className="body1">
                {t('Class')} <em className="text-semantic-4">*</em>
              </span>
              <DropDownPaginated
                className="max-w-full"
                loadOptions={classOptions}
                placeholder={t('Class')}
                selectedKey={formValues.class.key}
                onSelPair={(key: string, value: string) => {
                  setFormValues((prev) => ({
                    ...prev,
                    class: { key, value },
                  }));
                }}
              />
            </SearchItemContainer>
            {/* 면허 시작일 */}
            <SearchItemContainer className="gap-0">
              <span className="body1">
                {t('LicenseStartDate')} <em className="text-semantic-4">*</em>
              </span>
              <DaySelector />
            </SearchItemContainer>
            {/* 면허 만료일 */}
            <SearchItemContainer className="gap-0">
              <span className="body1 whitespace-pre-line">
                {t('LicenseExpiryDate')} <em className="text-semantic-4">*</em>
              </span>
              <DaySelector />
            </SearchItemContainer>

            <div className="w-full f-c-e gap-[10px]">
              <Button
                variant={'bt_secondary'}
                label={'Cancel'}
                onClick={onClose}
              />
              <Button type="submit" variant={'bt_primary'} label={'Confirm'} />
            </div>
          </article>
        </form>
      </section>
    </Layout>
  );
};

export default FDDriverModPopup;
