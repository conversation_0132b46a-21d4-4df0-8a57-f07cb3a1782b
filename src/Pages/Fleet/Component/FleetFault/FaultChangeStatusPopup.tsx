import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { Cross1Icon } from '@radix-ui/react-icons';
import Layout from '@/Common/Popup/Layout';
import { Button } from '@/Common/Components/common/Button';
import Radio, { RadioOption } from '@/Common/Components/common/Radio';
import { AlertPopupProps } from '@/types';

interface FaultChangeStatusPopupProps
  extends Omit<AlertPopupProps, 'onConfirm'> {
  onConfirm?: (selected: string) => void;
}

const FaultChangeStatusPopup: React.FC<FaultChangeStatusPopupProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  const { t } = useTranslation();

  const [selected, setSelected] = useState<string>('0');
  const options: RadioOption[] = [
    { value: '0', label: 'NotSubmitted' },
    { value: '1', label: 'Submitted' },
    { value: '2', label: 'InProgress' },
    { value: '3', label: 'Completed' },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[600px] popup-wrap">
        <article>
          <h2>{t('ChangeStatus')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        <article>
          <Radio
            options={options}
            value={selected}
            onValueChange={setSelected}
            className="flex-col gap-5"
          />
          <span className="ml-[30px] caption4 text-gray-8">
            {t(
              'ChangingTheStatusToMaintenanceCompletedWillLeadToTheReportSubmissionStep',
            )}
          </span>

          <div className="mt-10 f-je gap-[10px]">
            <Button
              variant="bt_secondary"
              label={t('Cancel')}
              onClick={onClose}
            />
            <Button
              variant="bt_primary"
              label={t('Change')}
              onClick={() => onConfirm?.(selected)}
            />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default FaultChangeStatusPopup;
