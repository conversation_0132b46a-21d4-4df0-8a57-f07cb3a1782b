import { useTranslation } from 'react-i18next';
import React, { useCallback, useMemo, useState } from 'react';
import { Cross1Icon } from '@radix-ui/react-icons';
import { AlertPopupProps, MapEngine } from '@/types';
import { GeneralMap, GeneralMapAdapter } from '@/logiMaps/react/general/Map';
import EqSingleMarker from '@/Common/Components/Marker/EqSingleMarker';
import EqPositionInfoWindow from '@/Common/Components/eqWindow/EqPositionInfoWindow';
import ZoomController from '@/Common/Components/map/ZoomController';
import Layout from '@/Common/Popup/Layout';
import { Button } from '@/Common/Components/common/Button';

const LocationPopup: React.FC<AlertPopupProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const [mapAdapter, setMapAdapter] = useState<GeneralMapAdapter | null>(null);

  const vehicleLocation = useMemo(() => ({ lat: 37.5665, lng: 126.978 }), []);

  const MAX_ZOOM_LEVEL = 21;
  const MIN_ZOOM_LEVEL = 3;
  const DEFAULT_ZOOM = 17;

  const handleMapInit = useCallback((adapter: GeneralMapAdapter) => {
    setMapAdapter(adapter);
  }, []);

  const zoomIn = useCallback(() => mapAdapter?.zoomIn(), [mapAdapter]);
  const zoomOut = useCallback(() => mapAdapter?.zoomOut(), [mapAdapter]);

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[1000px] popup-wrap">
        <article>
          <h2>{t('Location')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        <article>
          <div className="mb-[30px] h-full relative">
            <GeneralMap
              mapSource={MapEngine.source()}
              className="w-full h-[370px]"
              id="location-general-map"
              maxZoom={MAX_ZOOM_LEVEL}
              minZoom={MIN_ZOOM_LEVEL}
              defaultZoom={DEFAULT_ZOOM}
              defaultCenter={vehicleLocation}
              onInitMap={handleMapInit}
            >
              <EqSingleMarker id="marker-popup" latlng={vehicleLocation} />
              <EqPositionInfoWindow
                id="info1-popup"
                position={vehicleLocation}
                pixelOffset={[0, -4]}
                routeInfo={{ date: '2024-07-21' }}
              />

              <ZoomController
                right="right-5"
                bottom="bottom-5"
                plus={zoomIn}
                minus={zoomOut}
              />
            </GeneralMap>
          </div>

          {/* 닫기 버튼 */}
          <div className="f-je">
            <Button
              variant="bt_secondary"
              label={t('Cancel')}
              onClick={onClose}
            />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default LocationPopup;
