import { fleetApi } from '@/api';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';
import { toast } from '@/Common/useToast';
import { AlertPopupProps } from '@/types';
import { useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

const FMFleetDelPopup = (
  props: AlertPopupProps & { fleets: number[] | undefined },
) => {
  const { t } = useTranslation();

  const handleConfirm = () => {
    if (!props.fleets) return;
    delFleetMutation.mutate(props.fleets);
  };

  const delFleetMutation = useMutation({
    //'/api/fleet'
    mutationFn: (params: number[]) => {
      return fleetApi.deleteAdminFleet({ fleetIdList: params });
    },
    onSuccess: () => {
      toast({
        types: 'success',
        description: t('TheFleetHasBeenDeleted'),
      });
      props.onConfirm?.();
    },
    onError: () => {
      toast({
        types: 'error',
        description: t('FailedToDeleteFleet'),
      });
    },
  });

  return <TwoButtonPopup {...props} onConfirm={handleConfirm} />;
};

export default FMFleetDelPopup;
