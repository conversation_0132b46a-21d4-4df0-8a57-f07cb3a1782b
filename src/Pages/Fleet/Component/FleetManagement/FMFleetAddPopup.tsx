import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import { Cross1Icon } from '@radix-ui/react-icons';
import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { toast } from '@/Common/useToast';
import { fleetApi } from '@/api';
import { useForm } from 'react-hook-form';

const FMFleetAddPopup = ({ isOpen, onClose, onConfirm }: AlertPopupProps) => {
  const { t } = useTranslation();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      fleetName: '',
    },
  });

  const [fleetName, setFleetName] = useState('');

  const handleConfirm = () => {
    if (!fleetName.trim()) return;
    addFleetMutation.mutate(fleetName.trim());
  };

  const addFleetMutation = useMutation({
    //'/api/fleet'
    mutationFn: (params: string) => {
      return fleetApi.createAdminFleet({
        adminFleetCreateReqDTO: { fleetName: params },
      });
    },
    onSuccess: () => {
      toast({
        types: 'success',
        description: t('AddSuccess'),
      });
      onConfirm?.();
    },
    onError: () => {
      toast({
        types: 'error',
        description: t('AddFail'),
      });
    },
  });

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[600px] popup-wrap">
        {/* 타이틀 */}
        <article>
          <h2> {t('AddFleet')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/* 입력필드 */}
        <article>
          <form onSubmit={handleSubmit(handleConfirm)}>
            <div className="w-full mb-10 f-c gap-5">
              <p>{t('FleetName')}</p>
              <Input
                placeholder={t('FleetName')}
                value={fleetName}
                {...register('fleetName', {
                  maxLength: {
                    value: 30,
                    message: 'Maximum 30 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9가-힣\s\-_]*$/,
                    message: 'Only English, Korean, and numbers allowed.',
                  },
                })}
                error={errors.fleetName?.message}
                onChange={(e) => setFleetName(e.target.value)}
                reset={() => setFleetName('')}
                disabled={false}
              />
            </div>

            <div className="f-je gap-[10px]">
              <Button
                variant={'bt_secondary'}
                label={'Cancel'}
                onClick={onClose}
              />
              <Button
                type="submit"
                variant={'bt_primary'}
                label={'Add'}
                disabled={!fleetName.trim()}
              />
            </div>
          </form>
        </article>
      </section>
    </Layout>
  );
};

export default FMFleetAddPopup;
