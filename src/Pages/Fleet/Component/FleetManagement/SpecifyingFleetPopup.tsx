import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import Input from '@/Common/Components/common/Input';
import CommonTable from '@/Common/Components/common/CommonTable';
import { Button } from '@/Common/Components/common/Button';
import { Cross1Icon } from '@radix-ui/react-icons';

const SpecifyingFleetPopup = ({
  isOpen,
  onClose,
  onConfirm,
}: AlertPopupProps) => {
  const { t } = useTranslation();

  const columns1 = [
    {
      header: t('FleetNameN'),
      accessorKey: 'fleet',
    },
  ];
  const data1 = [
    {
      fleet: '25B-X',
    },
  ];

  const columns2 = [
    {
      header: t('Country'),
      accessorKey: 'nation',
    },
    {
      header: t('Model'),
      accessorKey: 'model',
    },
    {
      header: t('MachineID'),
      accessorKey: 'unit',
    },
    {
      header: t('SerialNo'),
      accessorKey: 'num',
    },
    {
      header: t('DealershipName'),
      accessorKey: 'agency',
    },
  ];
  const data2 = [
    {
      nation: '대한민국',
      model: '25B-X',
      unit: '00010',
      num: '',
      agency: 'JR SALES & SERVICE',
    },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[1280px] p-10 bg-white rounded-lg">
        {/*  */}
        <article className="heading2 mb-[34px] flex items-center justify-between">
          {t('SpecifyFleet')}
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/*  */}
        <article className="grid grid-cols-3 gap-10">
          <div className="col-span-1 flex flex-col gap-[30px]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <span className="body1-b">{t('Fleet')}</span>
                <Input placeholder={t('Fleet')} className="w-[160px]" />
              </div>
              <Button variant={'bt_primary'} label={'Search'} />
            </div>
            <div className="border-t border-x border-gray-1">
              <CommonTable
                columns={columns1}
                data={data1}
                isPagination={false}
                isCheckbox={true}
              />
            </div>
          </div>
          <div className="col-span-2 flex flex-col gap-[30px]">
            <div className="body1-b h-12 flex items-center">
              {t('SelectedMachineF')}
            </div>
            <div className="border-t border-x border-gray-1">
              <CommonTable
                columns={columns2}
                data={data2}
                isPagination={false}
                isCheckbox={true}
              />
            </div>
          </div>
        </article>

        {/* 버튼 */}
        <article className="mt-10 flex justify-end gap-3">
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
          <Button variant={'bt_primary'} label={'Assign'} onClick={onConfirm} />
        </article>
      </section>
    </Layout>
  );
};

export default SpecifyingFleetPopup;
