import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import Input from '@/Common/Components/common/Input';
import CommonTable from '@/Common/Components/common/CommonTable';
import { Button } from '@/Common/Components/common/Button';
import { Cross1Icon } from '@radix-ui/react-icons';

const AddDriverPopup = ({ isOpen, onClose, onConfirm }: AlertPopupProps) => {
  const { t } = useTranslation();

  const columns = [
    {
      header: t('Driver'),
      accessorKey: 'driver',
    },
    {
      header: t('IDNumber'),
      accessorKey: 'id',
    },
  ];
  const data = [
    {
      driver: '김운전',
      id: '00010',
    },
    {
      driver: '김운전',
      id: '00010',
    },
    {
      driver: '김운전',
      id: '00010',
    },
    {
      driver: '김운전',
      id: '00010',
    },
    {
      driver: '김운전',
      id: '00010',
    },
    {
      driver: '김운전',
      id: '00010',
    },
    {
      driver: '김운전',
      id: '00010',
    },
    {
      driver: '김운전',
      id: '00010',
    },
    {
      driver: '김운전',
      id: '00010',
    },
    {
      driver: '김운전',
      id: '00010',
    },
    {
      driver: '김운전',
      id: '00010',
    },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[800px] p-10 bg-white rounded-lg space-y-8">
        {/*  */}
        <article className="heading2 mb-[34px] f-c-b">
          {t('AddDriver')}
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/*  */}
        <article className="f-c-b">
          <div className="flex gap-6">
            <div className="f-c gap-6">
              <span className="body1-b">{t('Driver')}</span>
              <Input placeholder={t('Driver')} className="w-[180px]" />
            </div>
            <div className="f-c gap-6">
              <span className="body1-b">{t('IDNumber')}</span>
              <Input placeholder={t('IDNumber')} className="w-[140px]" />
            </div>
          </div>
          <Button variant={'bt_primary'} label={'Search'} onClick={onClose} />
        </article>

        <article className="border border-gray-1">
          <CommonTable
            columns={columns}
            data={data}
            isPagination={false}
            isCheckbox={true}
            maxHeight="400px"
          />
        </article>

        {/* 버튼 */}
        <article className="f-je gap-3">
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
          <Button
            variant={'bt_primary'}
            label={'Register'}
            onClick={onConfirm}
          />
        </article>
      </section>
    </Layout>
  );
};

export default AddDriverPopup;
