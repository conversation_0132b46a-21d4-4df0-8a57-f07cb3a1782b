import { useTranslation } from 'react-i18next';
import { AlertPopupProps, DemoTest } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import Input from '@/Common/Components/common/Input';
import CommonTable from '@/Common/Components/common/CommonTable';
import { Button } from '@/Common/Components/common/Button';
import { Cross1Icon } from '@radix-ui/react-icons';
import { useMutation, useQuery } from '@tanstack/react-query';
import { fleetApi } from '@/api';
import { useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import { toast } from '@/Common/useToast';
import { useForm } from 'react-hook-form';

type AvailEquipmentParams = {
  fleetId: number; //플릿 ID
  vinNumber: string; //VIN 번호
  manufacturer: string; //제조사 이름
  modelName: string; //모델 이름
  plateNo: string; //차량 번호
  page: number;
  size: number;
  sort: string;
};

type AvailEquipmentPage = {
  rows: AvailEquipmentRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type AvailEquipmentRow = {
  fleetId: number;
  vinNumber: number;
  equipmentId: number;
  equipmentType: string;
  manufacturer: string;
  modelName: string;
  trim: string;
  manufactureYear: number;
  plateNo: string;
  vehicleType: string;
};

const FMAddVehiclePopup = ({
  fleetId,
  isOpen,
  onClose,
  onConfirm,
}: AlertPopupProps & { fleetId: number }) => {
  const { t } = useTranslation();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      vinNumber: '',
      manufacturer: '',
      modelName: '',
      plateNo: '',
    },
  });

  const [checkedRows, setCheckedRows] = useState<number[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [formValues, setFormValues] = useState({
    fleetId: '',
    vinNumber: '',
    manufacturer: '',
    modelName: '',
    plateNo: '',
  });

  const columns: ColumnDef<AvailEquipmentRow>[] = [
    {
      header: t('VINNo'),
      accessorKey: 'vinNumber',
    },
    {
      header: t('ManufacturerName'),
      accessorKey: 'manufacturer',
    },
    {
      header: t('ModelName'),
      accessorKey: 'modelName',
    },
    {
      header: t('TrimName'),
      accessorKey: 'trim',
    },
    {
      header: t('ManufactureYear'),
      accessorKey: 'manufactureYear',
    },
    {
      header: t('VehicleNumber'),
      accessorKey: 'plateNo',
    },
    {
      header: t('VehicleType'),
      accessorKey: 'vehicleType',
    },
  ];

  /** Params */
  const [availEquipmentParams, setAvailEquipmentParams] =
    useState<AvailEquipmentParams>({
      fleetId: fleetId,
      vinNumber: '',
      manufacturer: '',
      modelName: '',
      plateNo: '',
      page: 0,
      size: 10,
      sort: 'modelName,asc',
    });

  /** useQuery */
  const { data: availEquipmentPage } = useQuery({
    queryKey: [
      '/api/fleet/equipment/for-registration/page',
      availEquipmentParams,
    ],
    queryFn: async () => {
      if (DemoTest.isRandomOn(false)) {
        return null;
      } else {
        try {
          const response =
            await fleetApi.getAdminEquipmentPageForRegistration(
              availEquipmentParams,
            );

          if (response.data && response.data.content && response.data.page) {
            const result: AvailEquipmentPage = {
              rows: [],
              page: {
                pageSize: 0,
                totalCnt: 0,
                pageNum: 0,
              },
            };

            response.data.content.forEach((row, index) => {
              result.rows.push({
                fleetId: row.fleetId ?? 0,
                vinNumber: /*row.vinNumber ??*/ 0,
                equipmentId: row.equipmentId ?? 0,
                equipmentType: row.equipmentType ?? '',
                manufacturer: row.manufacturer ?? '',
                modelName: row.modelName ?? '',
                trim: row.trimName ?? '',
                manufactureYear: Number(row.productYear) || 0,
                plateNo: row.plateNo ?? '',
                vehicleType: row.equipmentType ?? '',
              });
            });
            if (response.data.page) {
              result.page.pageSize =
                response.data.page.size ?? availEquipmentParams.size;
              result.page.totalCnt = response.data.page.totalElements ?? 0;
              result.page.pageNum = response.data.page.number ?? 0;
            }
            return result;
          }
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: true,
  });

  const handleConfirm = () => {
    if (checkedRows.length == 0) return;
    addEquipmentMutation.mutate(checkedRows);
  };

  const addEquipmentMutation = useMutation({
    //'/api/fleet/equipment'
    mutationFn: (params: number[]) => {
      return fleetApi.registerAdminEquipmentListToFleet({
        fleetId: fleetId,
        equipmentIdList: params,
      });
    },
    onSuccess: () => {
      toast({
        types: 'success',
        description: t('AddSuccess'),
      });
      onConfirm?.();
    },
    onError: () => {
      toast({
        types: 'error',
        description: t('AddFail'),
      });
    },
  });

  const resetListCheck = () => {
    setCheckedRows([]);
    setRowSelection({});
  };

  const handleSearch = () => {
    setAvailEquipmentParams({
      fleetId: fleetId,
      vinNumber: formValues.vinNumber,
      manufacturer: formValues.manufacturer,
      modelName: formValues.modelName,
      plateNo: formValues.plateNo,
      page: 0,
      size: 10,
      sort: 'modelName,asc',
    });
  };

  // 행 선택 시 체크박스 값 관리
  const handleSelectionChange = (selectedRows: AvailEquipmentRow[]) => {
    setCheckedRows(selectedRows.map((row) => row.equipmentId));
  };

  // rowSelection 변경 핸들러
  const handleRowSelectionChange = (
    newRowSelection: Record<string, boolean>,
  ) => {
    setRowSelection(newRowSelection);
  };

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[1210px] popup-wrap">
        {/*  */}
        <article>
          <h2>{t('AddVehicle')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>
        {/*  */}
        <article>
          <form onSubmit={handleSubmit(handleSearch)}>
            <div className="mb-6 f-s gap-[10px] [&>div]:w-[180px]">
              <Input
                placeholder={t('VINNumber')}
                value={formValues.vinNumber}
                {...register('vinNumber', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9]*$/,
                    message: 'Only A-Z, 0-9 allowed.',
                  },
                })}
                error={errors.vinNumber?.message}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    vinNumber: e.target.value,
                  })
                }
                reset={() =>
                  setFormValues({
                    ...formValues,
                    vinNumber: '',
                  })
                }
              />
              <Input
                placeholder={t('ManufacturerName')}
                value={formValues.manufacturer}
                {...register('manufacturer', {
                  maxLength: {
                    value: 50,
                    message: 'Maximum 50 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9가-힣\s\-_]*$/,
                    message: 'Only English, Korean, and numbers allowed.',
                  },
                })}
                error={errors.manufacturer?.message}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    manufacturer: e.target.value,
                  })
                }
                reset={() =>
                  setFormValues({
                    ...formValues,
                    manufacturer: '',
                  })
                }
              />
              <Input
                placeholder={t('ModelName')}
                value={formValues.modelName}
                {...register('modelName', {
                  maxLength: {
                    value: 50,
                    message: 'Maximum 50 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.modelName?.message}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    modelName: e.target.value,
                  })
                }
                reset={() =>
                  setFormValues({
                    ...formValues,
                    modelName: '',
                  })
                }
              />
              <Input
                placeholder={t('VehicleNumber')}
                value={formValues.plateNo}
                {...register('plateNo', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.plateNo?.message}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    plateNo: e.target.value,
                  })
                }
                reset={() =>
                  setFormValues({
                    ...formValues,
                    plateNo: '',
                  })
                }
              />

              <Button type="submit" variant={'bt_primary'} label={'Search'} />
            </div>
          </form>

          <div className="mb-[30px] table-border">
            <CommonTable
              columns={columns}
              data={availEquipmentPage?.rows || []}
              isCheckbox={true}
              onSelectionChange={handleSelectionChange}
              rowSelection={rowSelection}
              onRowSelectionChange={handleRowSelectionChange}
              isPagination={true}
              customPageSize={availEquipmentPage?.page.pageSize ?? 0}
              totalCount={availEquipmentPage?.page.totalCnt ?? 0}
              currentPage={
                availEquipmentPage?.page.pageNum
                  ? availEquipmentPage.page.pageNum + 1
                  : 1
              }
              onPageChange={(page: number) => {
                setAvailEquipmentParams((prevState) => ({
                  ...prevState,
                  page: page - 1,
                }));
                resetListCheck();
              }}
            />
          </div>

          <div className="f-c-e gap-[10px]">
            <Button
              variant={'bt_secondary'}
              label={'Cancel'}
              onClick={onClose}
            />
            <Button
              variant={'bt_primary'}
              label={'Add'}
              onClick={handleConfirm}
              disabled={checkedRows.length === 0}
            />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default FMAddVehiclePopup;
