import { fleetApi } from '@/api';
import TwoButtonPopup from '@/Common/Popup/TwoButtonPopup.tsx';
import { toast } from '@/Common/useToast';
import { AlertPopupProps } from '@/types';
import { useMutation } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';

const FMEquipmentDelPopup = (
  props: AlertPopupProps & {
    fleetId: number;
    equipmentIds: number[] | undefined;
  },
) => {
  const { t } = useTranslation();

  const handleConfirm = () => {
    if (!props.equipmentIds) return;
    delFleetMutation.mutate({
      fleetId: props.fleetId,
      equipmentIdList: props.equipmentIds,
    });
  };

  const delFleetMutation = useMutation({
    //'/api/fleet/equipment'
    mutationFn: (params: { fleetId: number; equipmentIdList: number[] }) => {
      return fleetApi.unregisterAdminEquipmentListFromFleet(params);
    },
    onSuccess: () => {
      toast({
        types: 'success',
        description: t('DeleteSuccess'),
      });
      props.onConfirm?.();
    },
    onError: () => {
      toast({
        types: 'error',
        description: t('DeleteFail'),
      });
    },
  });

  return <TwoButtonPopup {...props} onConfirm={handleConfirm} />;
};

export default FMEquipmentDelPopup;
