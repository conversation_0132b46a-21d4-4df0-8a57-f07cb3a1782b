import { useTranslation } from 'react-i18next';
import { AlertPopupProps } from '@/types';
import Layout from '@/Common/Popup/Layout.tsx';
import Input from '@/Common/Components/common/Input';
import CommonTable from '@/Common/Components/common/CommonTable';
import { Button } from '@/Common/Components/common/Button';
import { Cross1Icon } from '@radix-ui/react-icons';

// onConfirm 콜백을 추가로 받도록 타입 확장 (AlertPopupProps와 함께)
interface EqSettingPopupProps extends AlertPopupProps {
  onConfirm: () => void;
}

const EqSettingPopup = ({
  onClose,
  isOpen,
  onConfirm,
}: EqSettingPopupProps) => {
  const { t } = useTranslation();

  const columns = [
    {
      header: t('Model'),
      accessorKey: 'model',
    },
    {
      header: t('MachineID'),
      accessorKey: 'unit',
    },
    {
      header: t('SerialNo'),
      accessorKey: 'num',
    },
    {
      header: t('Dealership'),
      accessorKey: 'agency',
    },
  ];
  const data = [
    {
      model: '25B-X',
      unit: '00010',
      num: '00010',
      agency: 'JR SALES & SERVICE',
    },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[1236px] p-10 bg-white rounded-lg space-y-8">
        {/* 헤더 영역 */}
        <article className="heading2 mb-[34px] flex items-center justify-between">
          {t('SelectEquipment')}
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        {/* 검색 필터 */}
        <article className="flex items-center justify-between">
          <div className="flex gap-6">
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('Model')}</span>
              <Input placeholder={t('Model')} className="w-[200px]" />
            </div>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('MachineID')}</span>
              <Input placeholder={t('MachineID')} className="w-[200px]" />
            </div>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('SerialNo')}</span>
              <Input placeholder={t('SerialNo')} className="w-[200px]" />
            </div>
          </div>
          <Button variant={'bt_primary'} label={'Search'} onClick={onClose} />
        </article>

        {/* 테이블 영역 */}
        <article className="border-t border-x border-gray-1">
          <CommonTable
            columns={columns}
            data={data}
            isPagination={false}
            isCheckbox={true}
          />
        </article>

        {/* 하단 버튼 영역 */}
        <article className="flex justify-end gap-3">
          <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
          <Button
            variant={'bt_primary'}
            label={'ConfirmS'}
            onClick={onConfirm}
          />
        </article>
      </section>
    </Layout>
  );
};

export default EqSettingPopup;
