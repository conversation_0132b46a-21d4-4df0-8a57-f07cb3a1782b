import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import AccidentDetail from './FleetAccident/AccidentDetail.tsx';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import completed from '@/assets/images/status/completed.svg';
import maintenance from '@/assets/images/status/maintenance.svg';
import submitted from '@/assets/images/status/submitted.svg';
import not from '@/assets/images/status/not.svg';

const FleetAccident = () => {
  const { t } = useTranslation();

  const { openFAAccidentChangeStatusPopup } = UseFleetPopup();

  const [open, setOpen] = useState(false);

  const statusImg: Record<string, string> = {
    completed, // 정비 완료
    maintenance, // 정비중
    submitted, // 접수
    not, // 미접수
  };

  const columns = [
    { header: t('AccidentTime'), accessorKey: 'time' },
    { header: t('VehicleNumber'), accessorKey: 'num' },
    { header: t('DriverName'), accessorKey: 'driver' },
    { header: t('AccidentLocation'), accessorKey: 'location' },
    {
      header: t('ResponseStatus'),
      accessorKey: 'status',
      cell: ({ getValue }: { getValue: () => unknown }) => {
        const key = String(getValue() ?? '').toLowerCase();
        const src = statusImg[key];
        return (
          <img
            src={src}
            alt={key}
            title={key}
            onClick={(e) => {
              e.stopPropagation();
              openFAAccidentChangeStatusPopup();
            }}
          />
        );
      },
    },
  ];

  const data = [
    {
      time: '2025-06-24 13:55',
      num: 'ABC-1234',
      driver: 'John Michael Smith',
      location: '736 Garner Rd, Modesto, CA 95357, USA',
      status: 'not',
    },
  ];

  const detailData = {
    accidentTime: '2025-06-24 13:55',
    vehicleNumber: 'ABC-1234',
    driverName: 'John Michael Smith',
    location: '736 Garner Rd, Modesto, CA 95357, USA',
    replacementItem: 'Replacement Item Contents',
    repairDescription: `Repair Description Contents`,
  };

  return (
    <CustomFrame name={t('AccidentManagement')} back={false}>
      <section className="wrap-layout">
        {/* 검색 필터 */}
        <article className="mb-[56px] f-c gap-4">
          <div className="f-c gap-[10px]">
            <FromToSelector />
            <Input widthSize="md" placeholder={t('VehicleNumber')} />
            <Input widthSize="md" placeholder={t('DriverName')} />
          </div>
          <Button variant={'bt_primary'} label={t('Search')} />
        </article>

        {/* 다운로드 버튼 및 테이블 */}
        <article>
          <CommonTable
            columns={columns}
            data={data}
            isPagination={true}
            onRowClick={() => {
              setOpen(true);
            }}
            tdclassName="cursor-pointer"
          />
        </article>

        {/* 사고 이력 내용 */}
        <AccidentDetail open={open} onOpenChange={setOpen} data={detailData} />
      </section>
    </CustomFrame>
  );
};

export default FleetAccident;
