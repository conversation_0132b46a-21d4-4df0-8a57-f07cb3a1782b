import { useTranslation } from 'react-i18next';
import { Tabs } from '@radix-ui/themes';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { useState, useMemo } from 'react';
import MonthSelector from '@/Common/Components/datePicker/MonthSelector';
import dayjs from 'dayjs';
import { Value } from 'react-calendar/src/shared/types.ts';

interface FleetReportData {
  fleet: string;
  entire: number;
  operation: number;
  day: number;
  driver: string;
  hour: string;
  report: string;
}

interface ReportFleetReportInfoDTO {
  fleetName?: string;
  fleetTotalCount?: number;
  fleetActiveCount?: number;
  fleetWorkingDay?: number;
  driverCount?: string;
  engRunHour: string;
  workingHour: string;
  travelHour: string;
  idleHour: string;
}

const FleetReport = () => {
  const { t } = useTranslation();

  // 초기 날짜를 현재 월로 설정
  const [selectedDate, setSelectedDate] = useState<Date>(
    dayjs().subtract(1, 'month').toDate(),
  );
  // 선택된 날짜를 YYYYMM 형식의 문자열로 변환
  const [month, setMonth] = useState<string>(
    dayjs().subtract(1, 'month').format('YYYYMM'),
  );

  // MonthSelector에서 날짜가 변경될 때 호출되는 함수
  const handleMonthChange = (value: Value) => {
    if (value instanceof Date) {
      // 단일 Date 객체인 경우
      setSelectedDate(value);
      // 선택된 날짜를 YYYYMM 형식으로 변환하여 month 상태 업데이트
      const formattedMonth = dayjs(value).format('YYYYMM');
      setMonth(formattedMonth);
    } else if (
      Array.isArray(value) &&
      value.length > 0 &&
      value[0] instanceof Date
    ) {
      // Date 배열인 경우 첫 번째 날짜 사용
      setSelectedDate(value[0]);
      const formattedMonth = dayjs(value[0]).format('YYYYMM');
      setMonth(formattedMonth);
    } else if (
      value &&
      typeof value === 'object' &&
      'from' in value &&
      value.from instanceof Date
    ) {
      // Range<ValuePiece> 타입인 경우 from 값 사용
      setSelectedDate(value.from);
      const formattedMonth = dayjs(value.from).format('YYYYMM');
      setMonth(formattedMonth);
    }
  };

  const hourString = (item: ReportFleetReportInfoDTO) => {
    return (
      item.engRunHour +
      ' / ' +
      item.workingHour +
      ' / ' +
      item.travelHour +
      ' / ' +
      item.idleHour
    );
  };

  // Mock 데이터
  const mockFleetReportInfo: ReportFleetReportInfoDTO[] = useMemo(
    () => [
      {
        fleetName: 'Fleet A',
        fleetTotalCount: 25,
        fleetActiveCount: 20,
        fleetWorkingDay: 22,
        driverCount: '15',
        engRunHour: '180',
        workingHour: '150',
        travelHour: '20',
        idleHour: '10',
      },
      {
        fleetName: 'Fleet B',
        fleetTotalCount: 30,
        fleetActiveCount: 28,
        fleetWorkingDay: 25,
        driverCount: '22',
        engRunHour: '220',
        workingHour: '200',
        travelHour: '15',
        idleHour: '5',
      },
      {
        fleetName: 'Fleet C',
        fleetTotalCount: 18,
        fleetActiveCount: 15,
        fleetWorkingDay: 20,
        driverCount: '12',
        engRunHour: '160',
        workingHour: '140',
        travelHour: '12',
        idleHour: '8',
      },
      {
        fleetName: 'Fleet D',
        fleetTotalCount: 35,
        fleetActiveCount: 32,
        fleetWorkingDay: 28,
        driverCount: '28',
        engRunHour: '280',
        workingHour: '250',
        travelHour: '25',
        idleHour: '5',
      },
    ],
    [],
  );

  const fleetReportData: FleetReportData[] = useMemo(() => {
    return mockFleetReportInfo.map((item: ReportFleetReportInfoDTO) => ({
      fleet: item.fleetName ?? '',
      entire: item.fleetTotalCount ?? 0,
      operation: item.fleetActiveCount ?? 0,
      day: item.fleetWorkingDay ?? 0,
      driver: item.driverCount ?? '',
      hour: hourString(item),
      report: 'Report',
    }));
  }, [mockFleetReportInfo]);

  const columns = [
    {
      header: t('Fleet'),
      accessorKey: 'fleet',
    },
    {
      header: t('TotalMachine'),
      accessorKey: 'entire',
    },
    {
      header: t('ActiveMachine'),
      accessorKey: 'operation',
    },
    {
      header: t('WorkingDays'),
      accessorKey: 'day',
    },
    {
      header: t('TotalDrivers'),
      accessorKey: 'driver',
    },
    {
      header: t('OperationHoursHEngineRunWorkingTravelingIdling'),
      accessorKey: 'hour',
    },
    {
      header: t('Report'),
      accessorKey: 'report',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => (
        <span className="blue-underline">{cell.getValue() as string}</span>
      ),
    },
  ];

  return (
    <Tabs.Content value={'FleetReport'} className={'space-y-10'}>
      {/* 필터 */}
      <article>
        <div className="mb-10 flex items-center justify-between gap-3 [&_input]:w-[166px]">
          <SearchItemContainer>
            <SearchLabel>{t('Date')}</SearchLabel>
            <MonthSelector
              value={selectedDate}
              onValueChange={handleMonthChange}
            />
          </SearchItemContainer>
          <div className="flex gap-3">
            <Button variant={'bt_primary'} label={'Search'} />
            <Button variant={'bt_primary'} label={'Print'} />
          </div>
        </div>
      </article>

      {/* 테이블  */}
      <article>
        <CommonTable
          columns={columns}
          data={fleetReportData || []}
          isPagination={false}
        />
      </article>
    </Tabs.Content>
  );
};

export default FleetReport;
