import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Tabs } from '@radix-ui/themes';
import SearchLabel from '@/Common/Components/layout/SearchLabel';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { useState } from 'react';
import MonthSelector from '@/Common/Components/datePicker/MonthSelector';
import dayjs from 'dayjs';
import { Value } from 'react-calendar/src/shared/types.ts';

interface EquipmentReportItem {
  model: string;
  unit: string;
  num: string;
  mileage: number;
  location: string;
  report: string;
}

const EqReport = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [pageNum, setPageNum] = useState(1);
  const [totalCount] = useState(2); // 더미 총 갯수

  // 날짜 관련 상태
  const [selectedDate, setSelectedDate] = useState<Date>(
    dayjs().subtract(1, 'month').toDate(),
  );
  const [month, setMonth] = useState<string>(
    dayjs().subtract(1, 'month').format('YYYYMM'),
  );

  // MonthSelector에서 날짜가 변경될 때 호출되는 함수
  const handleMonthChange = (value: Value) => {
    if (value instanceof Date) {
      setSelectedDate(value);
      setMonth(dayjs(value).format('YYYYMM'));
    } else if (
      Array.isArray(value) &&
      value.length > 0 &&
      value[0] instanceof Date
    ) {
      setSelectedDate(value[0]);
      setMonth(dayjs(value[0]).format('YYYYMM'));
    } else if (
      value &&
      typeof value === 'object' &&
      'from' in value &&
      value.from instanceof Date
    ) {
      setSelectedDate(value.from);
      setMonth(dayjs(value.from).format('YYYYMM'));
    }
  };

  const handlePageChange = (newPage: number) => {
    setPageNum(newPage);
  };

  // 더미 데이터
  const fleetEquipmentReportData: EquipmentReportItem[] = [
    {
      model: 'HW150',
      unit: 'M1001',
      num: 'SN12345',
      mileage: 1200,
      location: '서울사업소',
      report: 'Report',
    },
    {
      model: 'HW210',
      unit: 'M1002',
      num: 'SN54321',
      mileage: 950,
      location: '부산사업소',
      report: 'Report',
    },
  ];

  const routeDetails = () => {
    navigate('/fleet-report/reportDetails');
  };

  const columns = [
    {
      header: t('Model'),
      accessorKey: 'model',
    },
    {
      header: t('MachineID'),
      accessorKey: 'unit',
    },
    {
      header: t('SerialNo'),
      accessorKey: 'num',
    },
    {
      header: t('Mileage'),
      accessorKey: 'mileage',
    },
    {
      header: t('Location'),
      accessorKey: 'location',
    },
    {
      header: t('Report'),
      accessorKey: 'report',
      cell: () => (
        <span onClick={routeDetails} className="blue-underline">
          {t('Report')}
        </span>
      ),
    },
  ];

  return (
    <Tabs.Content value={'MachineReport'} className={'space-y-10'}>
      {/* 필터 */}
      <article>
        <div className="mb-10 flex items-center justify-between gap-3 [&_input]:w-[166px]">
          <div className="flex items-center gap-6">
            <SearchItemContainer>
              <SearchLabel>{t('Date')}</SearchLabel>
              <MonthSelector
                value={selectedDate}
                onValueChange={handleMonthChange}
              />
            </SearchItemContainer>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('SerialNo')}</span>
              <Input placeholder={t('SerialNo')} />
            </div>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('Model')}</span>
              <Input placeholder={t('Model')} />
            </div>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('MachineID')}</span>
              <Input placeholder={t('MachineID')} />
            </div>
          </div>
          <div className="flex gap-3">
            <Button variant={'bt_primary'} label={'Search'} />
            <Button variant={'bt_primary'} label={'Print'} />
          </div>
        </div>
      </article>

      {/* 테이블  */}
      <article>
        <CommonTable
          columns={columns}
          data={fleetEquipmentReportData}
          isPagination={true}
          customPageSize={10}
          currentPage={pageNum}
          totalCount={fleetEquipmentReportData.length}
          onPageChange={handlePageChange}
        />
      </article>
    </Tabs.Content>
  );
};

export default EqReport;
