import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import UseFleetPopup from './Component/UseFleetPopup';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import Input from '@/Common/Components/common/Input';
import DropDown from '@/Common/Components/common/DropDown';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import completed from '@/assets/images/status/completed.svg';
import due from '@/assets/images/status/due.svg';
import overdue from '@/assets/images/status/overdue.svg';

const FleetConsumables = () => {
  const { t } = useTranslation();

  const navigate = useNavigate();

  const { openFCConsumablesChangeStatusPopup } = UseFleetPopup();

  const statusIcon: Record<string, string> = {
    completed,
    due,
    overdue,
  };

  const columns = [
    { header: t('FleetName'), accessorKey: 'fleet' },
    { header: t('ModelName'), accessorKey: 'model' },
    { header: t('VehicleNumber'), accessorKey: 'vehicle' },
    { header: t('ConsumableItem'), accessorKey: 'consumable' },
    { header: t('ReplacementCycleH'), accessorKey: 'cycle' },
    { header: t('NextReplacementDateH'), accessorKey: 'date' },
    {
      header: t('Status'),
      accessorKey: 'status',
      cell: ({ getValue }: { getValue: () => unknown }) => {
        const key = String(getValue() ?? '').toLowerCase();
        const src = statusIcon[key];
        return (
          <img
            src={src}
            alt={key}
            title={key}
            onClick={openFCConsumablesChangeStatusPopup}
            className="cursor-pointer"
          />
        );
      },
    },
  ];

  const data = [
    {
      fleet: 'Fleet A',
      model: '25B-X',
      vehicle: '25B-1234124',
      consumable: 'Aircleaner Element',
      cycle: '500',
      date: '+68',
      status: 'overdue',
    },
  ];

  return (
    <CustomFrame name={t('ConsumablesManagement')} back={false}>
      <section className="wrap-layout">
        {/* 검색 필터 */}
        <article className="mb-[18px] f-c gap-4">
          <div className="f-c gap-[10px]">
            <DropDown size="md" placeholder={t('AllStatus')} options={[]} />
            <Input widthSize="lg" placeholder={t('FleetName')} />
            <Input widthSize="md" placeholder={t('ModelName')} />
            <Input widthSize="md" placeholder={t('VehicleNumber')} />
          </div>
          <Button variant={'bt_primary'} label={t('Search')} />
        </article>

        {/* 다운로드 버튼 및 테이블 */}
        <article>
          <div className="mb-[10px] f-je">
            <Button variant={'bt_tertiary_sm'} label={t('Download')} />
          </div>

          <CommonTable columns={columns} data={data} isPagination={true} />
        </article>
      </section>
    </CustomFrame>
  );
};

export default FleetConsumables;
