import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Tabs } from '@radix-ui/themes';
import EqReport from '@/Pages/Fleet/FleetReport/EqReport';
import FtReport from '@/Pages/Fleet/FleetReport/FtReport';

const FleetReport = () => {
  const { t } = useTranslation();

  const [value, setValue] = useState('MachineReport');

  return (
    <CustomFrame name={t('Report')} back={false}>
      <section>
        <Tabs.Root value={value} onValueChange={setValue}>
          <Tabs.List className="tab-design">
            <Tabs.Trigger value={'MachineReport'}>
              <span> {t('MachineReport')}</span>
            </Tabs.Trigger>
            <Tabs.Trigger value={'FleetReport'}>
              <span>{t('FleetReport')}</span>
            </Tabs.Trigger>
          </Tabs.List>

          {/*  */}
          <div className={'tab-wrap'}>
            <EqReport />
            <FtReport />
          </div>
        </Tabs.Root>
      </section>
    </CustomFrame>
  );
};

export default FleetReport;
