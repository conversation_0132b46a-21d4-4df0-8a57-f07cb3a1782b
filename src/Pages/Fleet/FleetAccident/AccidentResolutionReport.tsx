import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup';
import { CustomFrame } from '@/Pages/CustomFrame';
import LineCon from '@/Common/Components/layout/LineCon';
import LineLabel from '@/Common/Components/layout/LineLabel';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';

const AccidentResolutionReport = () => {
  const { t } = useTranslation();

  const navigate = useNavigate();

  const { openFAReportOutPopup } = UseFleetPopup();

  const isPageOut = () => {
    openFAReportOutPopup(() => {
      navigate(-1);
    });
  };

  // 글자 수 제한
  const [details, setDetails] = useState('');
  const MAX_DETAILS_LEN = 500;

  return (
    <CustomFrame
      name={t('AccidentResolutionReport')}
      back
      onBackClick={isPageOut}
    >
      <section className="wrap-layout [&_h2]:mb-[23px] [&_span]:w-[110px]">
        {/* Replacement Item */}
        <article>
          <LineCon className="gap-5">
            <LineLabel label={'ReplacementItem'} className="subtitle4" />
            <div className="w-full f-c-b">
              <Input
                placeholder={t('ReplacementItem')}
                className="w-[1000px]"
              />
              <Button variant={'bt_primary'} label={'Register'} />
            </div>
          </LineCon>
        </article>

        <div className="divider my-10" />

        {/* Resolution Details */}
        <article>
          <LineCon style={{ alignItems: 'start' }} className="gap-5">
            <LineLabel label={'ResolutionDetails'} className="subtitle4" />
            <div className="w-full">
              <textarea
                name="details"
                id="details"
                placeholder={t('ResolutionDetails')}
                value={details}
                onChange={(e) => setDetails(e.currentTarget.value)}
                maxLength={MAX_DETAILS_LEN}
                className="w-full h-[270px] min-h-[200px] max-h-[350px] p-4 pr-14 border border-gray-6 rounded-md placeholder:body2 placeholder:text-gray-7 focus:outline-none focus:ring-0 focus:border-gray-6"
                aria-describedby="details-counter"
              />
              <p
                id="details-counter"
                className="mt-[-4px] f-je caption4 text-gray-12"
              >
                {details.length}/{MAX_DETAILS_LEN}
              </p>
            </div>
          </LineCon>
        </article>
      </section>
    </CustomFrame>
  );
};

export default AccidentResolutionReport;
