import * as Dialog from '@radix-ui/react-dialog';
import { useTranslation } from 'react-i18next';
import LineCon from '@/Common/Components/layout/LineCon';
import LineLabel from '@/Common/Components/layout/LineLabel';
import { Button } from '@/Common/Components/common/Button';

type AccidentDetailProps = {
  accidentTime: string;
  vehicleNumber: string;
  driverName: string;
  location: string;
  replacementItem: string;
  repairDescription: string;
};

interface ReportListDetailsProps {
  open: boolean;
  onOpenChange: (v: boolean) => void;
  data: AccidentDetailProps | null;
}

const AccidentResolutionReport = ({
  open,
  onOpenChange,
  data,
}: ReportListDetailsProps) => {
  const { t } = useTranslation();

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        {/* Dialog Content */}
        <Dialog.Content
          className="
            w-[360px] h-[calc(100%-74px)] bg-white shadow-card2 fixed bottom-0 right-[4px] z-[20]
            [&>article]:p-5
          "
        >
          {/*  */}
          <article>
            <div className="p-[10px] space-y-[6px] bg-primary-0-1 rounded [&_div]:gap-[10px] [&_span]:body4 [&_span]:text-gray-10 [&_p]:caption2">
              <LineCon>
                <LineLabel label={'AccidentTime'} />
                <p>{data?.accidentTime}</p>
              </LineCon>
              <LineCon>
                <LineLabel label={'VehicleNumber'} />
                <p>{data?.vehicleNumber}</p>
              </LineCon>
              <LineCon>
                <LineLabel label={'DriverName'} />
                <p>{data?.driverName}</p>
              </LineCon>
              <LineCon>
                <LineLabel label={'Location'} />
                <p>{data?.location}</p>
              </LineCon>
            </div>
          </article>

          <div className="divider w-[calc(100%-40px)] mx-5" />

          <article className="space-y-10 [&_h3]:mb-[9px] [&_h3]:subhead2 [&_p]:caption1">
            <div className="">
              <h3>{t('ReplacementItem')}</h3>
              <p>{data?.replacementItem}</p>
            </div>

            <div className="">
              <h3>{t('RepairDescription')}</h3>
              <p>{data?.repairDescription}</p>
            </div>
          </article>

          {/*  */}
          <article className="w-full mt-[70px] absolute bottom-0 left-0">
            <Dialog.Close asChild>
              <Button
                variant={'bt_primary'}
                label={'Close'}
                className="w-full"
              />
            </Dialog.Close>
          </article>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};

export default AccidentResolutionReport;
