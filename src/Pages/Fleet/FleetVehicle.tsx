import { useTranslation } from 'react-i18next';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import CommonTable from '@/Common/Components/common/CommonTable';
import DropDown from '@/Common/Components/common/DropDown';
import DropDownPaginated from '@/Common/Components/common/DropDownPaginated';
import { Button } from '@/Common/Components/common/Button';
import Input from '@/Common/Components/common/Input';
import { useQuery } from '@tanstack/react-query';
import { DemoTest, DropdownOption } from '@/types';
import { useForm } from 'react-hook-form';
import { dealerApi, equipmentApi } from '@/api';

type EquipmentInfoParams = {
  serialNo: string;
  manufacturer: string;
  modelName: string;
  plateNo: string;
  page: number;
  size: number;
  sort: string;
};

type EquipmentInfoPage = {
  rows: EquipmentInfoRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type EquipmentInfoRow = {
  vinNumber: string;
  manufacturer: string;
  modelName: string;
  trimName: string;
  manufactureYear: string;
  plateNo: string;
  vehicleType: string;
};

const FleetVehicle = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      modelName: '',
      manufacturer: '',
      plateNo: '',
      vinNumber: '',
    },
  });

  const columns = [
    { header: t('VINNumber'), accessorKey: 'vinNumber' },
    { header: t('ManufacturerName'), accessorKey: 'manufacturer' },
    { header: t('ModelName'), accessorKey: 'modelName' },
    { header: t('TrimName'), accessorKey: 'trimName' },
    { header: t('ManufactureYear'), accessorKey: 'manufactureYear' },
    { header: t('VehicleNumber'), accessorKey: 'vehicleNumber' },
    { header: t('VehicleType'), accessorKey: 'vehicleType' },
  ];

  const sortKeyOptions = [
    { key: t('VINNumber'), value: 'serialNo' },
    { key: t('ManufacturerName'), value: 'manufacturer' },
    { key: t('ModelName'), value: 'modelName' },
    { key: t('VehicleNumber'), value: 'plateNo' },
  ];

  const sortOrderOptions = [
    { key: t('Descending'), value: 'desc' },
    { key: t('Ascending'), value: 'asc' },
  ];

  // 입력 폼 값
  const [formValues, setFormValues] = useState({
    vinNumber: '',
    manufacturer: '',
    modelName: '',
    plateNo: '',
    sortKey: { key: sortKeyOptions[0].key, value: sortKeyOptions[0].value },
    sortOrder: {
      key: sortOrderOptions[0].key,
      value: sortOrderOptions[0].value,
    },
  });

  /** Params */
  const [equipmentInfoParams, setEquipmentInfoParams] = useState<
    EquipmentInfoParams | undefined
  >();

  /** useQuery */
  const { data: equipmentInfoPage } = useQuery<EquipmentInfoPage | null>({
    queryKey: ['/api/equipment/page', equipmentInfoParams],
    queryFn: async () => {
      console.log('Fetching equipment info with params:', equipmentInfoParams);
      if (DemoTest.isRandomOn()) {
        return {
          rows: [
            {
              vinNumber: '12345678901234567',
              manufacturer: 'Volvo Trucks',
              modelName: 'FL-2020-001',
              trimName: 'Lariat',
              manufactureYear: '2020',
              plateNo: 'ABC1234',
              vehicleType: 'Truck',
            },
          ],
          page: {
            pageSize: 10,
            totalCnt: 1,
            pageNum: 0,
          },
        };
      } else {
        try {
          if (equipmentInfoParams) {
            const response =
              await equipmentApi.getAdminEquipmentPage(equipmentInfoParams);
            if (response.data && response.data.content && response.data.page) {
              const result: EquipmentInfoPage = {
                rows: [],
                page: {
                  pageSize: 0,
                  totalCnt: 0,
                  pageNum: 0,
                },
              };

              response.data.content.forEach((row, index) => {
                result.rows.push({
                  vinNumber: row.serialNo ?? '',
                  manufacturer: row.manufacturer ?? '',
                  modelName: row.modelName ?? '',
                  trimName: row.trimName ?? '',
                  manufactureYear: row.productYear?.toString() ?? '',
                  plateNo: row.plateNo ?? '',
                  vehicleType: row.vehicleType ?? '',
                });

                if (response.data.page) {
                  result.page.pageSize =
                    response.data.page.size ?? equipmentInfoParams.size;
                  result.page.totalCnt = response.data.page.totalElements ?? 0;
                  result.page.pageNum =
                    response.data.page.number ?? equipmentInfoParams.page;
                }
              });
              return result;
            }
          }
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: !!equipmentInfoParams,
  });

  // 검색 실행
  const handleSearch = () => {
    setEquipmentInfoParams({
      serialNo: formValues.vinNumber,
      manufacturer: formValues.manufacturer,
      modelName: formValues.modelName,
      plateNo: formValues.plateNo,
      page: 0,
      size: 10,
      sort: `${formValues.sortKey.value},${formValues.sortOrder.value}`,
    });
  };

  useEffect(() => {
    handleSearch();
  }, []);

  return (
    <CustomFrame name={t('VehicleManagement')} back={false}>
      <section className="wrap-layout">
        {/*  */}
        <form onSubmit={handleSubmit(handleSearch)}>
          <article className="mb-[14px] f-c gap-4">
            <div className="f-c gap-[10px]">
              <Input
                placeholder={t('VINNumber')}
                value={formValues.vinNumber}
                {...register('vinNumber', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9]*$/,
                    message: 'Only A-Z, 0-9 allowed.',
                  },
                })}
                error={errors.vinNumber?.message}
                onChange={(e) =>
                  setFormValues((prev) => ({
                    ...prev,
                    vinNumber: e.target.value,
                  }))
                }
                reset={() =>
                  setFormValues((prev) => ({
                    ...prev,
                    vinNumber: '',
                  }))
                }
              />
              <Input
                placeholder={t('ManufacturerName')}
                value={formValues.manufacturer}
                {...register('manufacturer', {
                  maxLength: {
                    value: 50,
                    message: 'Maximum 50 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9가-힣\s\-_]*$/,
                    message: 'Only English, Korean, and numbers allowed.',
                  },
                })}
                error={errors.manufacturer?.message}
                onChange={(e) =>
                  setFormValues((prev) => ({
                    ...prev,
                    manufacturer: e.target.value,
                  }))
                }
                reset={() =>
                  setFormValues((prev) => ({
                    ...prev,
                    vinNumber: '',
                  }))
                }
              />
              <Input
                placeholder={t('ModelName')}
                value={formValues.modelName}
                {...register('modelName', {
                  maxLength: {
                    value: 50,
                    message: 'Maximum 50 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.modelName?.message}
                onChange={(e) =>
                  setFormValues((prev) => ({
                    ...prev,
                    modelName: e.target.value,
                  }))
                }
                reset={() =>
                  setFormValues((prev) => ({
                    ...prev,
                    modelName: '',
                  }))
                }
              />
              <Input
                placeholder={t('VehicleNumber')}
                value={formValues.plateNo}
                {...register('plateNo', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9]*$/,
                    message: 'Only A-Z, 0-9 allowed.',
                  },
                })}
                error={errors.plateNo?.message}
                onChange={(e) =>
                  setFormValues((prev) => ({
                    ...prev,
                    plateNo: e.target.value,
                  }))
                }
                reset={() =>
                  setFormValues((prev) => ({
                    ...prev,
                    vehNum: '',
                  }))
                }
              />
            </div>

            <Button type="submit" variant={'bt_primary'} label={'Search'} />
          </article>
        </form>

        {/* 정렬 및 테이블 영역 */}
        <article>
          <div className="mb-[6px] f-c-b [&>div]:f-c [&>div]:gap-[10px]">
            <div>
              <DropDown
                options={sortKeyOptions}
                placeholder={sortKeyOptions[0].key}
                selectedKey={formValues.sortKey.key}
                onSelPair={(key: string, value: string) => {
                  setFormValues((prev) => ({
                    ...prev,
                    sortKey: { key: key, value: value },
                  }));
                }}
              />
              <DropDown
                options={sortOrderOptions}
                placeholder={sortOrderOptions[0].key}
                selectedKey={formValues.sortOrder.key}
                onSelPair={(key: string, value: string) => {
                  setFormValues((prev) => ({
                    ...prev,
                    sortOrder: { key: key, value: value },
                  }));
                }}
              />
            </div>
            <div>
              <Button
                variant={'bt_primary_sm'}
                label={'Register'}
                onClick={() => navigate('/fleet-vehicle-add')}
              />
              <Button variant={'bt_tertiary_sm'} label={'Download'} />
            </div>
          </div>

          <CommonTable
            columns={columns}
            data={equipmentInfoPage?.rows || []}
            isCheckbox={false}
            isPagination={true}
            customPageSize={equipmentInfoPage?.page.pageSize ?? 0}
            totalCount={equipmentInfoPage?.page.totalCnt ?? 0}
            currentPage={
              equipmentInfoPage?.page.pageNum
                ? equipmentInfoPage.page.pageNum + 1
                : 1
            }
            onPageChange={(page: number) => {
              setEquipmentInfoParams((prevState) =>
                prevState ? { ...prevState, page: page - 1 } : undefined,
              );
            }}
            //onSelectionChange={handleSelectionChange}
            //sortKey={appliedFilters.sortBy}
            //sortOrder={appliedFilters.sortDirection as 'asc' | 'desc'}
          />
        </article>
      </section>
    </CustomFrame>
  );
};

export default FleetVehicle;
