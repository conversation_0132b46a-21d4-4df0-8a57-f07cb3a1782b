import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Tabs, HoverCard } from '@radix-ui/themes';
import FleetDriverList from '@/Pages/Fleet/FleetDriver/FleetDriverList.tsx';
import FleetDriverReport from '@/Pages/Fleet/FleetDriver/FleetDriverReport.tsx';
import FleetImpactList from '@/Pages/Fleet/FleetDriver/FleetImpactList';
import FleetImpactReport from '@/Pages/Fleet/FleetDriver/FleetImpactReport';

const FleetDriver = () => {
  const { t } = useTranslation();

  const [value, setValue] = useState('DriverManagement');

  return (
    <CustomFrame name={t('DriverManagement')} back={false}>
      <section>
        <Tabs.Root value={value} onValueChange={setValue}>
          <Tabs.List className="tab-design">
            <Tabs.Trigger value={'DriverManagement'}>
              <span>{t('DriverManagement')}</span>
            </Tabs.Trigger>
            <Tabs.Trigger value={'AllItineraryHistory'}>
              <span>{t('AllItineraryHistory')}</span>
            </Tabs.Trigger>
          </Tabs.List>

          <div className="tab-wrap">
            {value === 'DriverManagement' && <FleetDriverList />}
            {value === 'AllItineraryHistory' && <FleetDriverReport />}
            {value === 'FleetImpactList' && <FleetImpactList />}
            {value === 'FleetImpactReport' && <FleetImpactReport />}
          </div>
        </Tabs.Root>
      </section>
    </CustomFrame>
  );
};

export default FleetDriver;
