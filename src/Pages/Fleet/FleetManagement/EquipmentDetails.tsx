import { useTranslation } from 'react-i18next';
import UseFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import SummaryData from '@/Common/Components/etc/SummaryData';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';

const EqDetails = () => {
  const { t } = useTranslation();

  const { openFmDriverDeletePopup, openMAddDriverPopup } = UseFleetPopup();

  const summaryData = [
    { label: 'Model', value: '110TT' },
    { label: 'MachineID', value: 'LC-9B' },
    { label: 'SerialNo', value: 'HHKHFT23JF0000919' },
    { label: 'RMCU', value: 'TRW1D0220616' },
    { label: 'Country', value: '브라질' },
    { label: 'OperatingHour', value: '06:56' },
    { label: 'DeliveryDate', value: '2024-12-12' },
    { label: 'WarrantyExpiryDate', value: '2034-05-12' },
    { label: 'DealershipName', value: 'JR SALES & SERVICE' },
    { label: 'DealerName', value: '딜러123' },
    { label: 'ServiceContact', value: '55(11) - 4371 - 0443 ' },
    { label: 'FaultAlarm', value: 'A' },
    { label: 'ConsumableAlarm', value: 'F' },
    { label: 'LocationDeviationStatus', value: '-' },
    { label: 'CommunicationStatus', value: 'Y' },
  ];

  const columns = [
    {
      header: t('Driver'),
      accessorKey: 'driver',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => (
        <span className="blue-underline">{cell.getValue() as string}</span>
      ),
    },
    {
      header: t('IDType'),
      accessorKey: 'id',
    },
    {
      header: t('LicenseStartDate'),
      accessorKey: 'startD',
    },
    {
      header: t('LicenseExpiryDate'),
      accessorKey: 'expirationD',
    },
    {
      header: t('StartTime'),
      accessorKey: 'startT',
    },
    {
      header: t('EndTime'),
      accessorKey: 'expirationT',
    },
    {
      header: t('OperableEquipment'),
      accessorKey: 'eq',
    },
  ];
  const data = [
    {
      driver: '김운전',
      id: 'PIN',
      startD: '2021-04-15',
      expirationD: '2032-04-15',
      startT: '',
      expirationT: '',
      eq: '8',
    },
  ];

  return (
    <CustomFrame name={t('MachineInfo')} back={true}>
      <section>
        <div className="divider mt-0"></div>

        {/* 요약 데이터 */}
        <article>
          <SummaryData details={summaryData} fs="lg" />
        </article>

        {/* 테이블 */}
        <article>
          {/* 테이블 버튼 */}
          <div className="mt-10 mb-3 flex items-center gap-2">
            <div className="element1-m mr-6">
              <span>{t('Select')} | </span>
              <span>
                {t('Total')} 5{t('People')}
              </span>
            </div>
            <Button
              variant={'bt_primary'}
              label={'Delete'}
              onClick={openFmDriverDeletePopup}
            />
            <Button
              variant={'bt_primary'}
              label={'Add'}
              onClick={openMAddDriverPopup}
            />
          </div>
          <CommonTable
            columns={columns}
            data={data}
            isPagination={true}
            isCheckbox={true}
          />
        </article>
      </section>
    </CustomFrame>
  );
};

export default EqDetails;
