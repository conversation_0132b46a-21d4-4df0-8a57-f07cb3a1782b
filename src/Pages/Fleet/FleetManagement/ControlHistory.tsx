import { useTranslation } from 'react-i18next';
import { Tabs } from '@radix-ui/themes';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';

const ControlHistory = () => {
  const { t } = useTranslation();

  const openMSendItemPopup = () => {};

  const columns = [
    {
      header: t('Model'),
      accessorKey: 'model',
    },
    {
      header: t('MachineID'),
      accessorKey: 'unit',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => (
        <span className="blue-underline">{cell.getValue() as string}</span>
      ),
    },
    {
      header: t('SerialNo'),
      accessorKey: 'num',
      cell: ({ cell }: { cell: { getValue: () => unknown } }) => {
        const value = cell.getValue() as string;
        return (
          <span className={value === 'Fail' ? 'text-error' : ''}>{value}</span>
        );
      },
    },
    {
      header: t('ControlStatus'),
      accessorKey: 'control',
    },
    {
      header: t('LastUpdateE'),
      accessorKey: 'date',
    },
    {
      header: t('ViewHistoryE'),
      accessorKey: 'send',
      cell: () => (
        <span onClick={openMSendItemPopup} className="blue-underline">
          {t('View')}
        </span>
      ),
    },
  ];
  const data = [
    {
      model: 'TESTM3_TAEHA',
      unit: '00010',
      num: 'Fail',
      control: '',
      date: '2025-01-02 12:11',
      send: 'send',
    },
  ];

  return (
    <Tabs.Content value={'ControlHistory'} className={'space-y-10'}>
      {/* 필터 */}
      <article>
        <div className="mb-10 flex items-center justify-between gap-3">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('Model')}</span>
              <Input placeholder={t('Model')} />
            </div>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('MachineID')}</span>
              <Input placeholder={t('MachineID')} />
            </div>
            <div className="flex items-center gap-6">
              <span className="body1-b">{t('SerialNo')}</span>
              <Input placeholder={t('SerialNo')} />
            </div>
          </div>
          <Button variant={'bt_primary'} label={'Search'} />
        </div>
      </article>

      {/* 테이블 */}
      <article>
        <CommonTable
          columns={columns}
          data={data}
          isPagination={true}
          isCheckbox={false}
        />
      </article>
    </Tabs.Content>
  );
};

export default ControlHistory;
