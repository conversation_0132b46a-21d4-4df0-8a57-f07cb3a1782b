import { useTranslation } from 'react-i18next';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import VehicleList from '@/Pages/Fleet/FleetManagement/VehicleList';
import { useNavigate } from 'react-router-dom';
import { useLocation } from 'react-router-dom';

const FleetInfoDetails = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();

  const fleetId: number = location.state?.fleetId || 0;

  const handleBack = () => {
    navigate('/fleet-management', {
      state: { prevParams: location.state?.prevParams },
    });
  };

  return (
    <CustomFrame
      name={t('FleetInfomation')}
      back={true}
      onBackClick={handleBack}
    >
      <section>
        <VehicleList fleetId={fleetId} />
      </section>
    </CustomFrame>
  );
};

export default FleetInfoDetails;
