import { useTranslation } from 'react-i18next';
import useFleetPopup from '@/Pages/Fleet/Component/UseFleetPopup.tsx';
import { Tabs } from '@radix-ui/themes';
import StatusBadge from '@/Common/Components/common/StatusBadge.tsx';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  BreakdownStatusType,
  DemoTest,
  OperationStatusType,
  toBreakdownStatusType,
  toOperationStatusType,
} from '@/types';
import { fleetApi } from '@/api';
import { ColumnDef, Row } from '@tanstack/react-table';
import { useForm } from 'react-hook-form';

interface EqListProps {
  fleetId: number;
}

type FleetEquipmentParams = {
  fleetId: number; //플릿 ID
  vinNumber: string; //VIN 번호
  manufacturer: string; //제조사 이름
  modelName: string; //모델 이름
  plateNo: string; //차량 번호
  page: number;
  size: number;
  sort: string;
};

type FleetEquipmentPage = {
  rows: FleetEquipmentRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type FleetEquipmentRow = {
  fleetId: number;
  vinNumber: number;
  equipmentId: number;
  equipmentType: string;
  manufacturer: string;
  modelName: string;
  plateNo: string;
  operationStatus: OperationStatusType;
  breakdownStatus: BreakdownStatusType;
};

const VehicleList: React.FC<EqListProps> = ({ fleetId }) => {
  const { t } = useTranslation();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: {
      vinNumber: '',
      manufacturer: '',
      modelName: '',
      plateNo: '',
    },
  });

  const [checkedRows, setCheckedRows] = useState<number[]>([]);
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});

  // 입력 폼 값을 저장하는 상태
  const [formValues, setFormValues] = useState({
    vinNumber: '',
    manufacturer: '',
    modelName: '',
    plateNo: '',
  });

  useEffect(() => {
    handleSearch();
  }, []);

  /** Params */
  const [fleetEquipmentPageParams, setFleetEquipmentPageParams] = useState<
    FleetEquipmentParams | undefined
  >();

  /** useQuery */
  const { data: fleetEquipmentPage } = useQuery<FleetEquipmentPage | null>({
    queryKey: ['/api/fleet/equipment/page', fleetEquipmentPageParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn(false)) {
        return null;
      } else {
        try {
          if (fleetEquipmentPageParams) {
            const response = await fleetApi.getAdminFleetEquipmentPage(
              fleetEquipmentPageParams,
            );
            if (response.data && response.data.content && response.data.page) {
              const result: FleetEquipmentPage = {
                rows: [],
                page: {
                  pageSize: 0,
                  totalCnt: 0,
                  pageNum: 0,
                },
              };

              response.data.content.forEach((row, index) => {
                if (
                  row.fleetId !== undefined &&
                  row.equipmentId !== undefined &&
                  row.modelName !== undefined
                ) {
                  result.rows.push({
                    fleetId: row.fleetId,
                    vinNumber: /*row.vinNumber ??*/ 0,
                    equipmentId: row.equipmentId,
                    equipmentType: row.equipmentType ?? '-',
                    manufacturer: row.manufacturer ?? '-',
                    modelName: row.modelName ?? '-',
                    plateNo: row.plateNo ?? '-',
                    operationStatus: toOperationStatusType(
                      row.status?.operationStatus,
                    ),
                    breakdownStatus: toBreakdownStatusType(
                      row.status?.breakdownStatus,
                    ),
                  });
                }
                if (response.data.page) {
                  result.page.pageSize =
                    response.data.page.size ?? fleetEquipmentPageParams.size;
                  result.page.totalCnt = response.data.page.totalElements ?? 0;
                  result.page.pageNum =
                    response.data.page.number ?? fleetEquipmentPageParams.page;
                }
              });
              return result;
            }
          }
          return null;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: !!fleetEquipmentPageParams,
  });

  const refreshList = () => {
    resetListCheck();
    setFleetEquipmentPageParams((prevState) =>
      prevState ? { ...prevState, page: 0, _refresh: Date.now() } : undefined,
    );
  };

  const resetListCheck = () => {
    setCheckedRows([]);
    setRowSelection({});
  };

  const handleSearch = () => {
    setFleetEquipmentPageParams({
      fleetId: fleetId,
      vinNumber: formValues.vinNumber,
      manufacturer: formValues.manufacturer,
      modelName: formValues.modelName,
      plateNo: formValues.plateNo,
      page: 0,
      size: 10,
      sort: 'modelName,asc',
    });
  };

  // 행 선택 시 체크박스 값 관리
  const handleSelectionChange = (selectedRows: FleetEquipmentRow[]) => {
    setCheckedRows(selectedRows.map((row) => row.equipmentId));
  };

  // rowSelection 변경 핸들러
  const handleRowSelectionChange = (
    newRowSelection: Record<string, boolean>,
  ) => {
    setRowSelection(newRowSelection);
  };

  const columns: ColumnDef<FleetEquipmentRow>[] = [
    {
      header: t('VIN Number'),
      accessorKey: 'vinNumber',
    },
    {
      header: t('ManufacturerName'),
      accessorKey: 'manufacturer',
    },
    {
      header: t('ModelName'),
      accessorKey: 'modelName',
    },
    {
      header: t('VehicleNumber'),
      accessorKey: 'plateNo',
    },
    {
      header: t('Status'),
      accessorKey: 'status',
      cell: ({ row }: { row: Row<FleetEquipmentRow> }) => (
        <StatusBadge
          operationStatus={row.original.operationStatus}
          breakdownStatus={row.original.breakdownStatus}
        />
      ),
    },
  ];

  const { openFMAddVehiclePopup, openFMEquipmentDelPopup } = useFleetPopup();

  return (
    <div className={'wrap-layout'}>
      {/* 필터 */}
      <form onSubmit={handleSubmit(handleSearch)}>
        <article className="mb-[57px]">
          <div className="flex items-start gap-4">
            <div className="flex items-start gap-[10px]">
              <Input
                className="w-[200px]"
                placeholder={t('VIN Number')}
                value={formValues.vinNumber}
                {...register('vinNumber', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9]*$/,
                    message: 'Only A-Z, 0-9 allowed.',
                  },
                })}
                error={errors.vinNumber?.message}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    vinNumber: e.target.value,
                  })
                }
                reset={() =>
                  setFormValues({
                    ...formValues,
                    vinNumber: '',
                  })
                }
              />
              <Input
                className="w-[200px]"
                placeholder={t('ManufacturerName')}
                value={formValues.manufacturer}
                {...register('manufacturer', {
                  maxLength: {
                    value: 50,
                    message: 'Maximum 50 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9가-힣\s\-_]*$/,
                    message: 'Only English, Korean, and numbers allowed.',
                  },
                })}
                error={errors.manufacturer?.message}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    manufacturer: e.target.value,
                  })
                }
                reset={() =>
                  setFormValues({
                    ...formValues,
                    manufacturer: '',
                  })
                }
              />
              <Input
                className="w-[200px]"
                placeholder={t('ModelName')}
                value={formValues.modelName}
                {...register('modelName', {
                  maxLength: {
                    value: 50,
                    message: 'Maximum 50 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.modelName?.message}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    modelName: e.target.value,
                  })
                }
                reset={() =>
                  setFormValues({
                    ...formValues,
                    modelName: '',
                  })
                }
              />
              <Input
                className="w-[200px]"
                placeholder={t('VehicleNumber')}
                value={formValues.plateNo}
                {...register('plateNo', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.plateNo?.message}
                onChange={(e) =>
                  setFormValues({
                    ...formValues,
                    plateNo: e.target.value,
                  })
                }
                reset={() =>
                  setFormValues({
                    ...formValues,
                    plateNo: '',
                  })
                }
              />
            </div>
            <Button type="submit" variant={'bt_primary'} label={'Search'} />
          </div>
        </article>
      </form>

      {/* 테이블  */}
      <article>
        {/* 테이블 버튼 */}
        <div className="mb-[10px] f-c-b gap-2">
          <div className="f-c subtitle4">
            <span>Fleet 1</span>
            <div className="divider-v h-[14px] mx-[10px]" />
            <span>{t('Total')} 103</span>
          </div>
          <div className="f-c gap-2">
            <Button
              variant={'bt_primary_sm'}
              label={'Delete'}
              disabled={checkedRows.length === 0}
              onClick={() =>
                openFMEquipmentDelPopup(fleetId, checkedRows, refreshList)
              }
            />
            <Button
              variant={'bt_primary_sm'}
              label={'Add'}
              onClick={() => openFMAddVehiclePopup(fleetId, refreshList)}
            />
          </div>
        </div>
        <CommonTable
          columns={columns}
          data={fleetEquipmentPage?.rows || []}
          isCheckbox={true}
          onSelectionChange={handleSelectionChange}
          rowSelection={rowSelection}
          onRowSelectionChange={handleRowSelectionChange}
          isPagination={true}
          customPageSize={fleetEquipmentPage?.page.pageSize ?? 0}
          totalCount={fleetEquipmentPage?.page.totalCnt ?? 0}
          currentPage={
            fleetEquipmentPage?.page.pageNum
              ? fleetEquipmentPage.page.pageNum + 1
              : 1
          }
          onPageChange={(page: number) => {
            setFleetEquipmentPageParams((prevState) =>
              prevState ? { ...prevState, page: page - 1 } : undefined,
            );
            resetListCheck();
          }}
        />
      </article>
    </div>
  );
};

export default VehicleList;
