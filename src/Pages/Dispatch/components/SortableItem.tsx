import { useTranslation } from 'react-i18next';
import { useRef, useState, useEffect } from 'react';
import * as Popover from '@radix-ui/react-popover';
import { CSS } from '@dnd-kit/utilities';
import { searchAddressSimple } from '@/lbsCoreApi/SearchApi';
import { useSortable } from '@dnd-kit/sortable';
import Input from '@/Common/Components/common/Input';
import drag from '@/assets/images/etc/drag.svg';

export type AddressType = {
  address: string;
  lat: number;
  lng: number;
};

type SortableItemProps = {
  id: string;
  text: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onAddressSelect: (address: AddressType, idx: number) => void;
  showCancel?: boolean;
  onCancel?: () => void;
};

function SortableItem({
  id,
  text,
  value,
  onChange,
  onAddressSelect,
  showCancel,
  onCancel,
}: SortableItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const { t } = useTranslation();

  // Popover는 focus/blur로만 제어
  const [open, setOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // API 검색 결과 상태
  const [searchResults, setSearchResults] = useState<AddressType[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  // API 호출 함수
  const searchAddresses = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    setSearchError(null);

    try {
      const results = await searchAddressSimple(query);
      setSearchResults(results);
    } catch {
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // 입력값 변경 시 API 호출 (디바운싱)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (value.trim()) {
        searchAddresses(value);
      } else {
        setSearchResults([]);
      }
    }, 500); // 500ms 디바운싱

    return () => clearTimeout(timeoutId);
  }, [value]);

  const handleAddressBlur = () => {
    requestAnimationFrame(() => {
      if (
        contentRef.current &&
        contentRef.current.contains(document.activeElement)
      ) {
        return; // Popover 내부 클릭이면 닫지 않음
      }
      setOpen(false);
    });
  };

  // 주소 입력값 변경 처리
  const handleAddressInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    onChange(e); // 기존 onChange 호출
    // 수동 입력 시 선택 상태 초기화
    if (inputValue && !open) {
      setOpen(true);
    }
  };

  // style 그대로
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    background: isDragging ? '#F5F4F3' : '#fff',
  };

  return (
    <div ref={setNodeRef} className="f-c-b gap-3">
      <Popover.Root open={open}>
        <Popover.Trigger asChild>
          <Input
            placeholder={text}
            value={value}
            onChange={handleAddressInputChange}
            showCancel={showCancel}
            onCancel={onCancel}
            onMouseDown={() => setOpen(true)}
            onBlur={handleAddressBlur}
            style={style}
          />
        </Popover.Trigger>
        <Popover.Content
          ref={contentRef}
          align="center"
          className="!p-[5px] mt-1 w-[260px] max-h-[325px] space-y-[5px] bg-white border border-gray-6 rounded-md overflow-y-auto"
        >
          {isSearching ? (
            <div className="d-s-t">{t('Searching')}</div>
          ) : searchError ? (
            <div className="d-s-t">{searchError}</div>
          ) : searchResults.length > 0 ? (
            searchResults.map((addr: AddressType, idx: number) => {
              return (
                <p
                  key={`${addr.address}-${idx}`}
                  className="py-[10px] px-[15px] body4 rounded-md cursor-pointer hover:bg-primary-0"
                  onClick={() => {
                    onAddressSelect(addr, idx);
                    setOpen(false);
                  }}
                >
                  {addr.address}
                </p>
              );
            })
          ) : (
            <div className="d-s-t">
              {value.trim()
                ? t('SearchResultsNotFound')
                : t('NoAddressesFound')}
            </div>
          )}
        </Popover.Content>
      </Popover.Root>
      <span
        {...attributes}
        {...listeners}
        title="순서 변경"
        tabIndex={0}
        aria-label="드래그 핸들"
      >
        <img src={drag} alt="drag" />
      </span>
    </div>
  );
}

export default SortableItem;
