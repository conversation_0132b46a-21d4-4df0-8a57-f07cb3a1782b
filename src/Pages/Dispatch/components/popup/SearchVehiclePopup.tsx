import { useTranslation } from 'react-i18next';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ColumnDef, Row } from '@tanstack/react-table';
import { useQuery } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { Cross1Icon } from '@radix-ui/react-icons';
import { equipmentApi } from '@/api';
import {
  AlertPopupProps,
  BreakdownStatusType,
  DemoTest,
  OperationStatusType,
  toBreakdownStatusType,
  toOperationStatusType,
} from '@/types';
import Layout from '@/Common/Popup/Layout';
import CommonTable from '@/Common/Components/common/CommonTable';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import StatusBadge from '@/Common/Components/common/StatusBadge.tsx';

/** Types */
type EquipmentInfoParams = {
  modelName: string;
  plateNo: string;
  page: number;
  size: number;
  sort: string;
};

type EquipmentInfoPage = {
  rows: EquipmentInfoRow[];
  page: {
    pageSize: number;
    totalCnt: number;
    pageNum: number;
  };
};

type EquipmentInfoRow = {
  equipmentId: number;
  modelName: string;
  plateNo: string;
  operationStatus: OperationStatusType;
  breakdownStatus: BreakdownStatusType;
};

/** Component */
const SearchVehiclePopup: React.FC<
  AlertPopupProps & {
    onConfirm: () => void;
    setOption: (option: { key: string; value: string } | null) => void;
  }
> = ({ isOpen, onClose, onConfirm, setOption }) => {
  const { t } = useTranslation();

  // 선택 제어 (단일 선택)
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const selectedRowRef = useRef<EquipmentInfoRow | null>(null);

  // react-hook-form (controlled와 혼용 제거)
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<{ modelName: string; plateNo: string }>({
    defaultValues: { modelName: '', plateNo: '' },
  });

  // 테이블 컬럼
  const columns = useMemo<ColumnDef<EquipmentInfoRow>[]>(
    () => [
      { header: t('ModelName'), accessorKey: 'modelName' },
      { header: t('VehicleNumber'), accessorKey: 'plateNo' },
      {
        header: t('Status'),
        accessorKey: 'operationStatus',
        cell: ({ row }: { row: Row<EquipmentInfoRow> }) => (
          <StatusBadge
            operationStatus={row.original.operationStatus}
            breakdownStatus={row.original.breakdownStatus}
          />
        ),
      },
    ],
    [t],
  );

  /** Params */
  const [equipmentInfoParams, setEquipmentInfoParams] = useState<
    EquipmentInfoParams | undefined
  >();

  /** Query */
  const { data: equipmentInfoPage } = useQuery<EquipmentInfoPage | null>({
    queryKey: ['/api/equipment/page', equipmentInfoParams],
    queryFn: async () => {
      if (DemoTest.isRandomOn(true)) {
        return {
          rows: [
            {
              equipmentId: 1,
              modelName: 'Model-A',
              plateNo: 'AB1234CD',
              operationStatus: toOperationStatusType('inOperation'),
              breakdownStatus: toBreakdownStatusType('none'),
            },
            {
              equipmentId: 2,
              modelName: 'Model-B',
              plateNo: 'XY5678ZT',
              operationStatus: toOperationStatusType('idle'),
              breakdownStatus: toBreakdownStatusType('none'),
            },
            {
              equipmentId: 3,
              modelName: 'Model-C',
              plateNo: 'LM9101NO',
              operationStatus: toOperationStatusType('idle'),
              breakdownStatus: toBreakdownStatusType('inMaintenance'),
            },
            {
              equipmentId: 4,
              modelName: 'Model-D',
              plateNo: 'PQ2345RS',
              operationStatus: toOperationStatusType('idle'),
              breakdownStatus: toBreakdownStatusType('fault'),
            },
          ],
          page: { pageSize: 10, totalCnt: 4, pageNum: 0 },
        } satisfies EquipmentInfoPage;
      }

      if (!equipmentInfoParams) return null;

      const response =
        await equipmentApi.getAdminEquipmentPage(equipmentInfoParams);
      const result: EquipmentInfoPage = {
        rows: [],
        page: {
          pageSize: response.data?.page?.size ?? equipmentInfoParams.size,
          totalCnt: response.data?.page?.totalElements ?? 0,
          pageNum: response.data?.page?.number ?? equipmentInfoParams.page,
        },
      };

      response.data?.content?.forEach((row) => {
        result.rows.push({
          equipmentId: row.equipmentId ?? 0,
          modelName: row.modelName ?? '',
          plateNo: row.plateNo ?? '',
          operationStatus: toOperationStatusType(row.status?.operationStatus),
          breakdownStatus: toBreakdownStatusType(row.status?.breakdownStatus),
        });
      });

      return result;
    },
    enabled: !!equipmentInfoParams,
  });

  /** Handlers */
  const onSubmit = handleSubmit(({ modelName, plateNo }) => {
    setEquipmentInfoParams({
      modelName,
      plateNo,
      page: 0,
      size: 10,
      sort: 'modelName,asc',
    });
    // 검색마다 선택 초기화
    setRowSelection({});
    selectedRowRef.current = null;
  });

  const handleSelectionChange = (selectedRows: EquipmentInfoRow[]) => {
    // 단일 선택 강제
    const row = selectedRows[0] ?? null;
    selectedRowRef.current = row;
  };

  const handleRowSelectionChange = (next: Record<string, boolean>) => {
    // 가장 먼저 선택된 것만 유지
    const keys = Object.keys(next);
    const normalized: Record<string, boolean> = {};
    if (keys[0]) normalized[keys[0]] = true;
    setRowSelection(normalized);
  };

  /** Effects */
  useEffect(() => {
    // 최초 자동 검색
    setEquipmentInfoParams({
      modelName: '',
      plateNo: '',
      page: 0,
      size: 10,
      sort: 'modelName,asc',
    });
  }, []);

  /** Render */
  return (
    <Layout isOpen={isOpen}>
      <section className="w-[600px] popup-wrap">
        <article className="f-c-b">
          <h2>{t('SearchVehicle')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        <article>
          {/* 검색 필터 */}
          <form onSubmit={onSubmit}>
            <div className="mb-6 f-c gap-[10px] [&_div]:w-[180px]">
              <Input
                placeholder={t('ModelName')}
                {...register('modelName', {
                  maxLength: {
                    value: 50,
                    message: 'Maximum 50 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9\-_ ]*$/,
                    message: 'Only A-Z, 0-9, -, _ allowed.',
                  },
                })}
                error={errors.modelName?.message}
                reset={() => setValue('modelName', '')}
              />

              <Input
                placeholder={t('VehicleNumber')}
                {...register('plateNo', {
                  maxLength: {
                    value: 20,
                    message: 'Maximum 20 characters allowed.',
                  },
                  pattern: {
                    value: /^[A-Za-z0-9]*$/,
                    message: 'Only A-Z, 0-9 allowed.',
                  },
                })}
                error={errors.plateNo?.message}
                reset={() => setValue('plateNo', '')}
              />

              <Button type="submit" variant="bt_primary" label={t('Search')} />
            </div>
          </form>

          {/* 테이블 */}
          <div className="table-border mb-[30px]">
            <CommonTable
              maxHeight="540px"
              columns={columns}
              data={equipmentInfoPage?.rows ?? []}
              isPagination
              isCheckbox
              onSelectionChange={handleSelectionChange}
              rowSelection={rowSelection}
              onRowSelectionChange={handleRowSelectionChange}
              customPageSize={equipmentInfoPage?.page.pageSize ?? 0}
              totalCount={equipmentInfoPage?.page.totalCnt ?? 0}
              currentPage={(equipmentInfoPage?.page.pageNum ?? 0) + 1}
              onPageChange={(page: number) => {
                setEquipmentInfoParams((prev) =>
                  prev ? { ...prev, page: page - 1 } : prev,
                );
                setRowSelection({});
                selectedRowRef.current = null;
              }}
            />
          </div>

          {/* 버튼 */}
          <div className="f-je gap-[10px]">
            <Button
              variant="bt_secondary"
              label={t('Cancel')}
              onClick={onClose}
            />
            <Button
              variant="bt_primary"
              label={t('Confirm')}
              onClick={() => {
                const row = selectedRowRef.current;
                if (!row) {
                  setOption(null);
                } else {
                  setOption({
                    key: row.modelName,
                    value: String(row.equipmentId),
                  });
                }
                onConfirm();
              }}
            />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default SearchVehiclePopup;
