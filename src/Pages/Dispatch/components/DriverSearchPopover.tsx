import { useTranslation } from 'react-i18next';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from 'react';
import * as Popover from '@radix-ui/react-popover';
import { DriverStatusType } from '@/types';
import Input from '@/Common/Components/common/Input';

export type DriverInfo = {
  driverId: number;
  driverName: string;
  phoneNumber: string;
  status: DriverStatusType;
};

export type DriverSearchPopoverRef = {
  reset: () => void;
};

export type DriverSearchPopoverProps = {
  placeholder: string;
  showCancel?: boolean;
  onSearch: (query: string) => Promise<DriverInfo[]>;
  onSelect: (value: DriverInfo | null) => void;
  renderResult: (result: DriverInfo) => React.ReactNode;
  classNames?: { content?: string };
};

const DriverSearchPopover = forwardRef<
  DriverSearchPopoverRef,
  DriverSearchPopoverProps
>(
  (
    {
      placeholder,
      showCancel = false,
      onSearch,
      onSelect,
      renderResult,
      classNames,
    },
    ref,
  ) => {
    const { t } = useTranslation();

    const [open, setOpen] = useState(false);
    const contentRef = useRef<HTMLDivElement>(null);

    const [value, setValue] = useState('');
    const [results, setResults] = useState<DriverInfo[]>([]);
    const [isSearching, setIsSearching] = useState(false);
    const [searchError, setSearchError] = useState<string | null>(null);
    const [selectedDriver, setSelectedDriver] = useState<DriverInfo | null>(
      null,
    );

    // 최신 검색 요청만 반영하기 위한 토큰
    const searchTokenRef = useRef(0);

    // 디바운스 지연 상수 분리
    const DEBOUNCE_MS = 1000;

    // 검색 실행
    const executeSearch = useCallback(
      async (query: string, token: number) => {
        try {
          setIsSearching(true);
          setSearchError(null);
          const data = await onSearch(query);
          // 가장 최신 요청만 반영
          if (searchTokenRef.current === token) {
            setResults(Array.isArray(data) ? data : []);
          }
        } catch (e) {
          if (searchTokenRef.current === token) {
            setResults([]);
            setSearchError(t('LoadFailed') || 'Load failed');
          }
        } finally {
          if (searchTokenRef.current === token) {
            setIsSearching(false);
          }
        }
      },
      [onSearch, t],
    );

    // 입력값 변경 시 디바운스 검색
    useEffect(() => {
      const trimmed = value.trim();
      const token = ++searchTokenRef.current;

      if (!trimmed) {
        setResults([]);
        setIsSearching(false);
        setSearchError(null);
        return;
      }

      // 기존 선택된 기사의 이름과 같으면 검색하지 않음
      if (selectedDriver && trimmed === selectedDriver.driverName) {
        return;
      }

      const id = setTimeout(() => {
        executeSearch(trimmed, token);
        if (!open) setOpen(true);
      }, DEBOUNCE_MS);

      return () => clearTimeout(id);
    }, [value, executeSearch, open, selectedDriver]);

    const handleSearchBlur = useCallback(() => {
      requestAnimationFrame(() => {
        if (contentRef.current?.contains(document.activeElement)) return; // 내부 포커스면 유지
        setOpen(false);
      });
    }, []);

    const handleInputChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        setValue(inputValue);
        // 기존 선택된 기사의 이름과 다를 때만 선택 해제
        if (selectedDriver && inputValue !== selectedDriver.driverName) {
          setSelectedDriver(null);
          onSelect(null);
        }
      },
      [onSelect, selectedDriver],
    );

    // 키보드 단축: Enter 시 첫 결과 선택
    const handleInputKeyDown = useCallback(
      (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter' && results.length > 0) {
          const r = results[0];
          if (r.status === DriverStatusType.Idle) {
            setValue(r.driverName);
            setSelectedDriver(r); // 내부 상태 업데이트
            onSelect(r);
            setResults([]); // 검색 결과 초기화
            setOpen(false);
          }
        }
      },
      [onSelect, results],
    );

    // 리셋 함수
    const reset = useCallback(() => {
      setValue('');
      setResults([]);
      setSelectedDriver(null);
      setOpen(false);
      setIsSearching(false);
      setSearchError(null);
      // 부모 컴포넌트에 선택 해제 알림
      onSelect(null);
    }, [onSelect]);

    // 부모 컴포넌트에서 ref를 통해 리셋 함수를 호출할 수 있도록 함
    useImperativeHandle(ref, () => ({
      reset,
    }));

    // 안내 문구 결정
    const emptyMessage = useMemo(
      () =>
        value.trim() ? t('SearchResultsNotFound') : t('SearchResultsNotFound'),
      [t, value],
    );

    return (
      <Popover.Root open={open} onOpenChange={setOpen}>
        <Popover.Trigger asChild>
          <Input
            type="search"
            placeholder={placeholder}
            value={value}
            onChange={handleInputChange}
            showCancel={showCancel}
            onMouseDown={() => setOpen(true)}
            onBlur={handleSearchBlur}
            onKeyDown={handleInputKeyDown}
            aria-autocomplete="list"
            aria-expanded={open}
          />
        </Popover.Trigger>
        <Popover.Content
          ref={contentRef}
          align="center"
          className={classNames?.content || ''}
          sideOffset={-8}
          role="listbox"
        >
          {isSearching ? (
            <div className="d-s-t">{t('Searching')}</div>
          ) : searchError ? (
            <div className="d-s-t">{searchError}</div>
          ) : results.length > 0 ? (
            results.map((result) => (
              <div
                key={result.driverId}
                role="option"
                tabIndex={0}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (result.status === DriverStatusType.Idle) {
                    setValue(result.driverName);
                    setSelectedDriver(result); // 내부 상태 업데이트
                    onSelect(result);
                    setResults([]); // 검색 결과 초기화
                    setOpen(false);
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    if (result.status === DriverStatusType.Idle) {
                      setValue(result.driverName);
                      setSelectedDriver(result); // 내부 상태 업데이트
                      onSelect(result);
                      setResults([]); // 검색 결과 초기화
                      setOpen(false);
                    }
                  }
                }}
              >
                {renderResult(result)}
              </div>
            ))
          ) : (
            <div className="d-s-t">{emptyMessage}</div>
          )}
        </Popover.Content>
      </Popover.Root>
    );
  },
);

export default DriverSearchPopover;
