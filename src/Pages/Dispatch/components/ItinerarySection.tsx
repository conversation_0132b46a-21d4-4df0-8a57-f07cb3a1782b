import { useTranslation } from 'react-i18next';
import {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import type { DragEndEvent } from '@dnd-kit/core';
import { searchRoute } from '@/lbsCoreApi/RouteApi';
import { cn } from '@/Common/function/utils.ts';
import { arrayMove } from '@dnd-kit/sortable';
import SortableItem, { AddressType } from './SortableItem';
import { Button } from '@/Common/Components/common/Button';
import itinerary from '@/assets/images/ic/24/itinerary.svg';

type ItemType = {
  id: string;
  type: string;
  text: string;
};

const initialItems = [
  { id: 'item-1', type: 'origin', text: 'Origin' },
  { id: 'item-3', type: 'waypoint', text: 'Waypoint' },
  { id: 'item-2', type: 'destination', text: 'Destination' },
];

export type ItinerarySectionRef = {
  reset: () => void;
};

export type ItinerarySectionProps = {
  onAddressChange?: (addresses: {
    [itemIndex: number]: AddressType | null;
  }) => void;
  onRouteChange?: (
    route: {
      routeResult: unknown;
      waypoints: [number, number][];
    } | null,
  ) => void;
  onReset?: () => void;
};

const ItinerarySection = forwardRef<ItinerarySectionRef, ItinerarySectionProps>(
  ({ onAddressChange, onRouteChange, onReset }, ref) => {
    const { t } = useTranslation();

    // DnD 리스트 state
    const [items, setItems] = useState<ItemType[]>(initialItems);
    // Itinerary input 값
    const [inputs, setInputs] = useState<{ [id: string]: string }>({});
    const [selectedAddresses, setSelectedAddresses] = useState<{
      [itemIndex: number]: AddressType | null;
    }>({});
    // 아이템 ID 카운터 (초기값: 4, item-1,2,3이 이미 있으므로)
    const itemCounterRef = useRef(4);

    // waypoint 갯수 (Origin, Destination 제외) 20개 까지
    const waypointCount = items.filter(
      (item) => item.type === 'waypoint',
    ).length;
    const canAddWaypoint = waypointCount < 20;

    // dnd-kit sensors
    const sensors = useSensors(
      useSensor(PointerSensor, { activationConstraint: { distance: 5 } }),
    );

    // selectedAddresses 변경 시 방문 순서 최적화 API 호출
    useEffect(() => {
      const calcRoute = async () => {
        // 유효한 주소 선택 (실제 선택된 항목들만)
        const validAddresses = Object.entries(selectedAddresses)
          .filter(([_, addressObj]) => addressObj !== null)
          .map(([indexStr, addressObj]) => {
            const itemIndex = parseInt(indexStr);
            const item = items[itemIndex];

            return {
              itemIndex: itemIndex + 1, // 1부터 시작하는 순서
              itemId: item?.id || 'unknown',
              itemType: item?.type || 'unknown',
              address: addressObj?.address,
              coordinates: addressObj
                ? { lat: addressObj.lat, lng: addressObj.lng }
                : null,
              inputValue: inputs[item?.id || ''],
            };
          })
          .sort((a, b) => a.itemIndex - b.itemIndex); // 순서대로 정렬

        // 2개 이상의 유효한 주소가 있을 때만 API 호출
        if (validAddresses.length >= 2) {
          const startPoint = validAddresses[0];
          const destinations = validAddresses.slice(1);

          if (startPoint.coordinates && destinations.length > 0) {
            // searchRoute 함수 호출을 위한 파라미터 준비
            const startCoords: [number, number] = [
              startPoint.coordinates.lng, // longitude
              startPoint.coordinates.lat, // latitude
            ];

            const destCoords: [number, number][] = destinations
              .filter((dest) => dest.coordinates)
              .map((dest) => [
                dest.coordinates!.lng, // longitude
                dest.coordinates!.lat, // latitude
              ]);

            try {
              const routeResult = await searchRoute(startCoords, destCoords);
              if (!routeResult) {
                onRouteChange?.(null);
              } else {
                onRouteChange?.({
                  routeResult: routeResult,
                  waypoints: [startCoords, ...destCoords],
                });
              }
            } catch (error) {
              console.error('경로 검색 API 호출 실패:', error);
              onRouteChange?.(null);
            }
          }
        } else {
          // 주소가 2개 미만일 때
          onRouteChange?.(null);
        }
      };

      calcRoute();
    }, [selectedAddresses, items]);

    useEffect(() => {
      // 주소 선택이 변경되면 전체 상태를 외부 콜백으로 전달
      onAddressChange?.(selectedAddresses);
    }, [selectedAddresses]);

    // 모든 아이템 드래그로 순서 변경
    const handleDragEnd = (event: DragEndEvent) => {
      const { active, over } = event;
      if (!over || active.id === over.id) return;
      const oldIndex = items.findIndex((i) => i.id === active.id);
      const newIndex = items.findIndex((i) => i.id === over.id);
      const moved = arrayMove(items, oldIndex, newIndex);

      const updated = moved.map((item, idx) => {
        if (idx === 0) return { ...item, type: 'origin', text: 'Origin' };
        if (idx === moved.length - 1)
          return { ...item, type: 'destination', text: 'Destination' };
        return { ...item, type: 'waypoint', text: 'Waypoint' };
      });
      setItems(updated);

      // 주소 선택 상태도 새로운 순서에 맞게 재정렬
      setSelectedAddresses((prev) => {
        const newSelectedAddresses: {
          [itemIndex: number]: AddressType | null;
        } = {};

        // 기존 선택된 주소들을 새로운 인덱스에 맞게 재배치
        Object.entries(prev).forEach(([indexStr, addressObj]) => {
          const currentIndex = parseInt(indexStr);
          let newMappedIndex = currentIndex;

          // 드래그된 아이템의 새로운 위치 계산
          if (currentIndex === oldIndex) {
            newMappedIndex = newIndex;
          } else if (oldIndex < newIndex) {
            // 앞에서 뒤로 이동한 경우
            if (currentIndex > oldIndex && currentIndex <= newIndex) {
              newMappedIndex = currentIndex - 1;
            }
          } else {
            // 뒤에서 앞으로 이동한 경우
            if (currentIndex >= newIndex && currentIndex < oldIndex) {
              newMappedIndex = currentIndex + 1;
            }
          }

          newSelectedAddresses[newMappedIndex] = addressObj;
        });

        return newSelectedAddresses;
      });
    };

    // 주소 입력값 변경 시 선택 상태 초기화
    const handleAddressInputChange = (
      itemIndex: number,
      e: React.ChangeEvent<HTMLInputElement>,
    ) => {
      const item = items[itemIndex];
      if (!item) return;

      setInputs((prev) => ({
        ...prev,
        [item.id]: e.target.value,
      }));
      setSelectedAddresses((prev) => {
        if (prev[itemIndex] === null) {
          return prev; // 값이 이미 null이면 변경하지 않음
        }
        return {
          ...prev,
          [itemIndex]: null,
        };
      });
    };

    // 주소 선택 처리
    const handleAddressSelect = (
      itemIndex: number,
      addressObj: AddressType,
      idx: number,
    ) => {
      const item = items[itemIndex];
      if (!item) return;

      setSelectedAddresses((prev) => ({
        ...prev,
        [itemIndex]: addressObj,
      }));
      setInputs((prev) => ({
        ...prev,
        [item.id]: addressObj.address,
      }));
    };

    const handleRemoveWaypoint = (id: string) => {
      const currentWaypoints = items.filter((i) => i.type === 'waypoint');
      if (currentWaypoints.length <= 0) return;

      // 제거할 아이템의 인덱스 찾기
      const removeIndex = items.findIndex((i) => i.id === id);

      setItems((prev) => prev.filter((i) => i.id !== id));
      setInputs((prev) => {
        const { [id]: _, ...rest } = prev;
        return rest;
      });

      // 인덱스 기반으로 주소 선택 상태 정리 및 재정렬
      setSelectedAddresses((prev) => {
        const newSelectedAddresses: {
          [itemIndex: number]: AddressType | null;
        } = {};
        Object.entries(prev).forEach(([indexStr, addressObj]) => {
          const index = parseInt(indexStr);
          if (index < removeIndex) {
            // 제거되는 인덱스보다 앞에 있는 것들은 그대로
            newSelectedAddresses[index] = addressObj;
          } else if (index > removeIndex) {
            // 제거되는 인덱스보다 뒤에 있는 것들은 인덱스를 하나씩 앞당김
            newSelectedAddresses[index - 1] = addressObj;
          }
          // index === removeIndex인 경우는 제거됨
        });
        return newSelectedAddresses;
      });
    };

    const handleAddWaypoint = () => {
      if (!canAddWaypoint) return;
      const newId = `item-${itemCounterRef.current}`;
      itemCounterRef.current += 1;
      const destIdx = items.findIndex((i) => i.type === 'destination');
      const newItems = [
        ...items.slice(0, destIdx),
        { id: newId, type: 'waypoint', text: 'Waypoint' },
        ...items.slice(destIdx),
      ];
      setItems(newItems);
      setInputs((prev) => ({ ...prev, [newId]: '' }));

      // 인덱스 기반으로 주소 선택 상태 재정렬 (destination의 인덱스가 하나 뒤로 밀림)
      setSelectedAddresses((prev) => {
        const newSelectedAddresses: {
          [itemIndex: number]: AddressType | null;
        } = {};
        Object.entries(prev).forEach(([indexStr, addressObj]) => {
          const index = parseInt(indexStr);
          if (index < destIdx) {
            // destination 앞에 있는 것들은 그대로
            newSelectedAddresses[index] = addressObj;
          } else {
            // destination부터는 인덱스를 하나씩 뒤로 밀어줌
            newSelectedAddresses[index + 1] = addressObj;
          }
        });
        // 새로 추가된 waypoint는 null로 초기화
        newSelectedAddresses[destIdx] = null;
        return newSelectedAddresses;
      });
    };

    // Reset 버튼
    const handleReset = () => {
      setItems(initialItems);
      setInputs({
        'item-1': '',
        'item-2': '',
        'item-3': '',
      });
      setSelectedAddresses({});
      itemCounterRef.current = 4; // 카운터도 초기값으로 리셋

      // 부모 컴포넌트에 리셋 완료 알림
      onReset?.();

      //reorderItemsAndAddresses([2, 0, 1]);
    };

    // 부모 컴포넌트에서 ref를 통해 리셋 함수를 호출할 수 있도록 함
    useImperativeHandle(ref, () => ({
      reset: handleReset,
    }));

    const reorderItemsAndAddresses = (entries: number[]) => {
      if (entries.length !== items.length) return; // 개수 맞지 않으면 무시

      //items 순서 업데이트
      {
        const sorted = entries
          .map((oldIndex, newIndex) => {
            return { item: items[oldIndex], newIndex };
          })
          .sort((a, b) => a.newIndex - b.newIndex)
          .map((entry) => entry.item);

        const updatedItems = sorted.map((item, idx) => {
          if (idx === 0) return { ...item, type: 'origin', text: 'Origin' };
          if (idx === sorted.length - 1)
            return { ...item, type: 'destination', text: 'Destination' };
          return { ...item, type: 'waypoint', text: 'Waypoint' };
        });
        console.log('Updated items:', updatedItems);
        setItems(updatedItems);
      }

      // 선택된 주소 상태 업데이트
      {
        const sorted = entries
          .map((oldIndex, newIndex) => {
            return { item: selectedAddresses[oldIndex], newIndex };
          })
          .sort((a, b) => a.newIndex - b.newIndex)
          .map((entry) => entry.item);

        const updatedAddresses: {
          [itemIndex: number]: AddressType | null;
        } = {};
        sorted.forEach((item, idx) => {
          updatedAddresses[idx] = item;
        });
        setSelectedAddresses(updatedAddresses);
      }
    };

    return (
      <div className="w-full">
        {/* Itinerary 타이틀 / Reset 버튼 */}
        <div className="mb-6 f-c-b">
          <h3 className="f-c gap-2 subtitle3">
            <img src={itinerary} alt="itinerary" />
            {t('Itinerary')}
          </h3>
          <button
            className="caption2 text-gray-10 underline underline-offset-2"
            onClick={handleReset}
          >
            {t('Reset')}
          </button>
        </div>
        {/* Drag and Drop Context */}
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext
            items={items.map((i) => i.id)}
            strategy={verticalListSortingStrategy}
          >
            {/* Itinerary content 영역 */}
            <div className="h-full max-h-[440px] pr-1 space-y-3 overflow-y-scroll overflow-x-hidden">
              {items.map((item, idx) => {
                const isLastwaypoint =
                  (item.type === 'waypoint' || item.type === 'origin') &&
                  (idx === items.length - 1 ||
                    items[idx + 1].type !== 'waypoint');
                const hasValue = Boolean(inputs[item.id]?.trim());
                const isFirst = idx === 0;
                const isLast = idx === items.length - 1;
                return (
                  <div
                    key={item.id}
                    className={cn(
                      'dispatch-line',
                      'first:dispatch-line--first',
                      'last:dispatch-line--last',
                      +isFirst && hasValue && 'dispatch-line--first-filled',
                      +isLast && hasValue && 'dispatch-line--last-filled',
                      +!isFirst &&
                        !isLast &&
                        hasValue &&
                        'dispatch-line--filled',
                    )}
                  >
                    {/* drag input */}
                    <SortableItem
                      id={item.id}
                      text={item.text}
                      value={inputs[item.id] || ''}
                      onChange={(e) => handleAddressInputChange(idx, e)}
                      onAddressSelect={(addressObj, addressIdx) =>
                        handleAddressSelect(idx, addressObj, addressIdx)
                      }
                      showCancel={item.type === 'waypoint'}
                      onCancel={
                        item.type === 'waypoint' && waypointCount > 1
                          ? () => handleRemoveWaypoint(item.id)
                          : undefined
                      }
                    />
                    {/* waypoint 바로 아래에만 버튼 노출 */}
                    {isLastwaypoint && canAddWaypoint && (
                      <Button
                        variant="bt_tertiary"
                        label={t('AddWaypoint')}
                        onClick={handleAddWaypoint}
                        className="w-[260.5px] ml-[34px] my-3"
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </SortableContext>
        </DndContext>
      </div>
    );
  },
);

export default ItinerarySection;
