import React, { useCallback, useEffect, useState } from 'react';
import { AnimatePresence } from 'framer-motion';
import {
  EqGroupMarkerSet,
  EqSingleMarkerSet,
} from '@/Common/constants/Maps.ts';
import { useLayoutStore } from '@/store/layout.ts';
import { MapEngine } from '@/types';
import { GeneralMap, GeneralMapAdapter } from '@/logiMaps/react/general/Map';
import ZoomController from '@/Common/Components/map/ZoomController.tsx';
import GeneralPolyline from '@/logiMaps/react/general/Poly/GeneralPolyline.tsx';
import DispatchMarker, {
  DispatchMarkerProps,
} from '@/Common/Components/Marker/DispatchMarker.tsx';
import DispatchSearch from './DispatchSearch.tsx';
import dispatchRange from '@/assets/images/ic/40/dispatch_range.svg';

type LatLng = { lat: number; lng: number };

/**
 *  배차
 */
const Dispatch: React.FC = () => {
  const { move: layoutMove } = useLayoutStore((state) => state);
  const [mapAdapter, setMapAdapter] = useState<GeneralMapAdapter | null>(null);
  const maxZoomLevel = 18;
  const minZoomLevel = 4;
  const defaultLevel = 15;
  const [dispatchPath, setDispatchPath] = useState<LatLng[] | null>(null);
  const [dispatchMarkers, setDispatchMarkers] = useState<DispatchMarkerProps[]>(
    [],
  );

  /** useEffect */

  // 맵 업데이트
  useEffect(() => {
    if (mapAdapter && dispatchPath) {
      const bounds: { lat: number; lng: number }[] = [];
      for (const item of dispatchPath) {
        // 북미 영역 좌표만 필터
        if (
          item.lat >= 10.0 &&
          item.lat <= 82.0 &&
          item.lng >= -168.0 &&
          item.lng <= -40.0
        ) {
          bounds.push({ lat: item.lat, lng: item.lng });
        }
      }
      if (dispatchPath.length > 0) {
        fitBounds(bounds);
      }
    }
  }, [mapAdapter, dispatchPath]);

  // 지도 로드
  const handleMapInit = (generalMapAdapter: GeneralMapAdapter) => {
    setMapAdapter(generalMapAdapter);
  };

  // 지도 화면 조정
  const fitBounds = (bounds: { lat: number; lng: number }[]) => {
    if (mapAdapter) {
      const { width, height } = mapAdapter.getScreenSize();
      const padding = {
        top: `${height * 0.1}px`,
        right: `${width * 0.1}px`,
        bottom: `${height * 0.1}px`,
        left: `${width * 0.1}px`,
      };
      if (layoutMove == true) {
        const VehicleViewListWidth = 412;
        const visibleWidth = width - VehicleViewListWidth;
        padding.right = `${visibleWidth * 0.1 + VehicleViewListWidth}px`;
        padding.left = `${visibleWidth * 0.1}px`;
      }
      mapAdapter.fitBounds(bounds, padding);
    }
  };

  // DispatchSearch에서 경로 데이터를 받아 처리하는 핸들러
  const handleRouteChange = useCallback((routeData: any) => {
    setDispatchPath(null);
    setDispatchMarkers([]);

    if (routeData && routeData.routeResult && routeData.routeResult.pnts) {
      // pnts 배열의 모든 구간을 하나의 경로로 합치기
      const pathPoints: LatLng[] = [];
      routeData.routeResult.pnts.forEach((segment: any[]) => {
        segment.forEach((point: { dx: number; dy: number }) => {
          pathPoints.push({
            lat: point.dy, // dy는 latitude
            lng: point.dx, // dx는 longitude
          });
        });
      });
      setDispatchPath(pathPoints);

      // waypoints를 이용하여 DispatchMarker 생성
      if (routeData.waypoints && Array.isArray(routeData.waypoints)) {
        const markers: DispatchMarkerProps[] = routeData.waypoints.map(
          (waypoint: [number, number], index: number) => ({
            id: `dispatch-marker-${index}`,
            latlng: {
              lat: waypoint[1], // waypoint[1]은 latitude
              lng: waypoint[0], // waypoint[0]은 longitude
            },
            destination: index === routeData.waypoints.length - 1, // 마지막 waypoint는 destination
          }),
        );
        setDispatchMarkers(markers);
      }
    }
  }, []);

  return (
    <div className={'w-full h-full relative'}>
      {/* 지도 영역 */}
      <GeneralMap
        mapSource={MapEngine.source()}
        className={'w-full h-full overflow-hidden'}
        id={'dispatch-map'}
        maxZoom={maxZoomLevel}
        minZoom={minZoomLevel}
        defaultZoom={defaultLevel}
        onInitMap={handleMapInit}
      >
        {dispatchPath && (
          <GeneralPolyline path={dispatchPath} width={3} color={'#FF5900'} />
        )}
        {/* Dispatch 마커들 */}
        {dispatchMarkers.map((markerProps, index) => (
          <DispatchMarker
            key={markerProps.id}
            {...markerProps}
            destination={index === dispatchMarkers.length - 1}
          />
        ))}
      </GeneralMap>

      {/* 줌 컨트롤 */}
      <ZoomController
        bottom="bottom-9"
        right="right-[30px]"
        plus={() => mapAdapter?.zoomIn()}
        minus={() => mapAdapter?.zoomOut()}
      />

      <AnimatePresence>
        <DispatchSearch onRouteChange={handleRouteChange} />
      </AnimatePresence>
    </div>
  );
};

export default Dispatch;
