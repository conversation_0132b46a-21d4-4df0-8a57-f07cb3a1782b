import { useTranslation } from 'react-i18next';
import { FormEvent, useState } from 'react';
import { useDistanceUnit } from '@/context/DistanceUnitContext.tsx';
import usePopup from '@/hooks/usePopup.tsx';
import { Tabs } from '@radix-ui/themes';
import { Button } from '@/Common/Components/common/Button';
import DropDown from '@/Common/Components/common/DropDown';
import Input from '@/Common/Components/common/Input';
import Radio from '@/Common/Components/common/Radio';
import { isValidEmail } from '@/Common/function/utils.ts';
import { StatisticsType } from '@/types/StatisticsType';

const PersonalSetting = ({ title }: StatisticsType.MenuTabPros) => {
  const { t } = useTranslation();

  const { distanceUnit, setDistanceUnit } = useDistanceUnit();

  // 단위, 형식 Radio 선택 상태
  const [lengthSelected, setLengthSelected] = useState<string>('0');
  const [dateSelected, setDateSelected] = useState<string>('0');
  const [volumeSelected, setVolumeSelected] = useState<string>('0');
  const [tempSelected, setTempSelected] = useState<string>('0');
  const [pressSelected, setPressSelected] = useState<string>('0');
  const [weightSelected, setWeightSelected] = useState<string>('0');
  const { openPasswordPopup } = usePopup();

  // 전화번호/이메일/시간/단위 편집 상태
  const [phoneEdit, setPhoneEdit] = useState(false);
  const togglePhoneEdit = () => setPhoneEdit((prev) => !prev);

  const [mailEdit, setMailEdit] = useState(false);
  const toggleMailEdit = () => setMailEdit((prev) => !prev);

  const [timeEdit, setTimeEdit] = useState(false);
  const toggleTimeEdit = () => setTimeEdit((prev) => !prev);

  const [dateUnitEdit, setDateUnitEdit] = useState(false);
  const toggleDateUnitEdit = () => setDateUnitEdit((prev) => !prev);

  // 더미 데이터
  const dummyDayFormatDataList = [
    { key: 'YYYY-MM-DD', value: 'YMD' },
    { key: 'DD-MM-YYYY', value: 'DMY' },
    { key: 'MM-DD-YYYY', value: 'MDY' },
  ];
  const dummyCountryListData = [
    { key: '+82', value: 'KR' },
    { key: '+81', value: 'JP' },
    { key: '+1', value: 'US' },
  ];
  const dummyRegionListData = [
    { key: 'Korea', value: 'KR' },
    { key: 'Japan', value: 'JP' },
    { key: 'USA', value: 'US' },
  ];
  const dummyCityListData = [
    { key: 'Seoul', value: 'SEOUL' },
    { key: 'Tokyo', value: 'TOKYO' },
    { key: 'New York', value: 'NY' },
  ];
  const dummyGmtListData = [
    { key: '0', value: 'UTC+9' },
    { key: '1', value: 'UTC+8' },
    { key: '2', value: 'UTC-5' },
  ];

  // 내정보, 단위, 연락처 상태
  const [infoDataUnit, setInfoDataUnit] = useState({
    mobile: '010-1234-5678',
    email: '<EMAIL>',
    countryCode: 'KR',
    selectYn: 'Y',
  });
  const [error, setError] = useState<string>('');
  const [dayFormatAndUnit, setDayFormatAndUnit] = useState({
    dateFormat: 'YMD',
    length: 'KM',
    volume: 'LT',
    temp: 'DF',
    press: 'KGF',
    weight: 'TON',
  });

  // 시간설정 State
  const [cityEdit, setCityEdit] = useState({ region: 'KR' });
  const [timeZoneNo, setTimeZoneNo] = useState({ timeZoneNo: 0 });

  // 더미 내정보
  const profileAllData = {
    personalInfo: {
      userName: '홍길동',
      userId: 'hong123',
      mobileNo: '010-1234-5678',
      mailAddr: '<EMAIL>',
    },
    timeSettingInfo: {
      state: 'Korea',
      city: 'Seoul',
      timeZoneName: 'UTC+9',
    },
    screenSettingInfo: {
      dateFormat: 'YMD',
      length: 'KM',
      volume: 'LT',
      temp: 'DF',
      press: 'KGF',
      weight: 'TON',
    },
  };

  // 더미 GMT
  const getGMTData = (dcode: number) => {
    const data = dummyGmtListData.find((item) => item.key === dcode.toString());
    return data ? data.value : null;
  };

  // 더미 날짜형식 표시
  const getDataByDcode = (dcode: string | undefined) => {
    const data = dummyDayFormatDataList.find((item) => item.value === dcode);
    return data ? data.key : '';
  };

  // 편집 중 리셋 함수 (원래값으로)
  const resetMobile = () =>
    setInfoDataUnit((prev) => ({
      ...prev,
      mobile: profileAllData.personalInfo.mobileNo,
    }));
  const resetEmail = () =>
    setInfoDataUnit((prev) => ({
      ...prev,
      email: profileAllData.personalInfo.mailAddr,
    }));

  return (
    <Tabs.Content
      value={'PersonalSetting'}
      className="
        space-y-10
        [&>article]:py-5
        [&>article]:px-[30px]
        [&>article]:bg-white
        [&>article]:border
        [&>article]:border-gray-6
        [&>article]:rounded-md
        [&>article>div]:py-[14px]
        [&>article>div]:px-5
        [&>article>div]:f-c
        [&>article>div]:gap-5
        [&>article>div]:border-b
        [&>article>div]:border-gray-6
        [&>article>div:last-child]:border-b-0
        [&>article>div>span]:block
        [&>article>div>span]:w-[225px]
        [&_h2]:py-[14px]
        [&_h2]:px-5
        [&_h2]:f-c
        [&_h2]:gap-6
        [&_h2]:subtitle3
        [&_span]:body2
      "
    >
      {/* Personal Information  */}
      <article>
        <h2>{t('PersonalInformation')}</h2>
        <div>
          <span>{t('UserName')}</span>
          <span>{profileAllData.personalInfo.userName}</span>
        </div>
        <div>
          <span>{t('PhoneNumber')}</span>
          <span>
            {phoneEdit ? (
              <div className="f-c gap-3">
                <DropDown
                  onChange={(value) => {
                    setInfoDataUnit((prev) => ({
                      ...prev,
                      countryCode: value.toString(),
                    }));
                  }}
                  options={dummyCountryListData}
                  placeholder={infoDataUnit.countryCode}
                />
                <Input
                  placeholder={infoDataUnit.mobile}
                  value={infoDataUnit.mobile}
                  onChange={(e) => {
                    setInfoDataUnit((prev) => ({
                      ...prev,
                      mobile: e.target.value,
                    }));
                  }}
                  reset={resetMobile}
                />
              </div>
            ) : (
              <em>
                {infoDataUnit.countryCode}{' '}
                {profileAllData.personalInfo.mobileNo}
              </em>
            )}
          </span>
          <Button
            variant={phoneEdit ? 'bt_primary_sm' : 'bt_tertiary_sm'}
            label={phoneEdit ? 'Done' : 'Edit'}
            onClick={togglePhoneEdit}
            disabled={mailEdit}
          />
        </div>
      </article>

      {/* Account Information  */}
      <article>
        <h2>{t('AccountInformation')}</h2>
        <div>
          <span>{t('UserPermission')}</span>
          <span>{t('SuperAdministrator')}</span>
        </div>
        <div>
          <span>{t('UserId')}</span>
          <span>{profileAllData.personalInfo.userId}</span>
        </div>
        <div>
          <span>{t('Password')}</span>
          <span>*****</span>
          <Button
            variant={'bt_tertiary_sm'}
            label="EditC"
            onClick={openPasswordPopup}
          />
        </div>
        <div>
          <span>{t('EMail')}</span>
          <span>
            {mailEdit ? (
              <div>
                <Input
                  onChange={(e) => {
                    setInfoDataUnit((prev) => ({
                      ...prev,
                      email: e.target.value,
                    }));
                    if (!isValidEmail(e.target.value)) {
                      setError(t('PleaseEnterValidEmailAddress'));
                    } else {
                      setError('');
                    }
                  }}
                  value={infoDataUnit.email}
                  placeholder={infoDataUnit.email}
                  reset={resetEmail}
                  error={error}
                />
              </div>
            ) : (
              <em>{profileAllData.personalInfo.mailAddr}</em>
            )}
          </span>
          <Button
            variant={mailEdit ? 'bt_primary_sm' : 'bt_tertiary_sm'}
            label={mailEdit ? 'Done' : 'Edit'}
            onClick={toggleMailEdit}
            disabled={phoneEdit && error.length > 0}
          />
        </div>
      </article>

      {/* Time Settings */}
      <article>
        <h2>
          {t('TimeSettings')}
          <Button
            variant={timeEdit ? 'bt_primary_sm' : 'bt_tertiary_sm'}
            label={timeEdit ? 'Done' : 'Edit'}
            onClick={toggleTimeEdit}
          />
        </h2>
        <div>
          <span>{t('Country')}</span>
          {timeEdit ? (
            <DropDown
              onChange={(value) => {
                setCityEdit({ region: value.toString() });
              }}
              options={dummyRegionListData}
              placeholder={profileAllData.timeSettingInfo.state}
            />
          ) : (
            <span>{profileAllData.timeSettingInfo.state}</span>
          )}
        </div>
        <div>
          <span>{t('City')}</span>
          {timeEdit ? (
            <DropDown
              onChange={(value) => {
                // 시티는 상태로 관리 가능
              }}
              options={dummyCityListData}
              placeholder={profileAllData.timeSettingInfo.city}
            />
          ) : (
            <span>{profileAllData.timeSettingInfo.city}</span>
          )}
        </div>
        <div>
          <span>{t('TimeZoneUTC')}</span>
          {timeEdit ? (
            <span>
              {getGMTData(timeZoneNo.timeZoneNo) ||
                profileAllData.timeSettingInfo.timeZoneName}
            </span>
          ) : (
            <span>{profileAllData.timeSettingInfo.timeZoneName}</span>
          )}
        </div>
      </article>

      {/* Format & Unit Settings */}
      <article>
        <h2>
          {t('FormatUnitSettings')}
          <Button
            variant={dateUnitEdit ? 'bt_primary_sm' : 'bt_tertiary_sm'}
            label={dateUnitEdit ? 'Done' : 'Edit'}
            onClick={toggleDateUnitEdit}
          />
        </h2>
        <div
          className="
            grid grid-cols-2
            [&>div>div]:py-[14px]
            [&>div>div]:px-5
            [&>div>div]:f-c
            [&>div>div]:border-b
            [&>div>div]:border-gray-6
            [&>div>div:last-child]:border-b-0
             [&>div>div>span]:block
             [&>div>div>span]:w-[225px]
             [&>div>div>span]:flex-shrink-0
             [&>div>div>span>div]:w-max
          "
        >
          <div className="w-full col-span-1">
            <div>
              <span>{t('DateFormat')}</span>
              <span>
                {dateUnitEdit ? (
                  <Radio
                    options={dummyDayFormatDataList.map((item) => ({
                      value: item.value,
                      label: item.key,
                    }))}
                    value={dayFormatAndUnit.dateFormat}
                    onValueChange={setDateSelected}
                  />
                ) : (
                  <span>{getDataByDcode(dayFormatAndUnit?.dateFormat)}</span>
                )}
              </span>
            </div>
            <div>
              <span>{t('DistanceUnit')}</span>
              <span>
                {dateUnitEdit ? (
                  <Radio
                    options={[
                      { value: 'km', label: t('MKm') },
                      { value: 'mi', label: t('FtMi') },
                    ]}
                    value={distanceUnit}
                    onValueChange={(value) => {
                      setDistanceUnit(value as 'km' | 'mi');
                      setDayFormatAndUnit((prev) => ({
                        ...prev,
                        length: value === 'mi' ? 'MI' : 'KM',
                      }));
                    }}
                  />
                ) : (
                  <span>{distanceUnit === 'mi' ? 'mi' : 'km'}</span>
                )}
              </span>
            </div>
            <div>
              <span>{t('VolumeUnit')}</span>
              {dateUnitEdit ? (
                <Radio
                  options={[
                    { value: '0', label: t('L') },
                    { value: '1', label: t('Gal') },
                  ]}
                  value={volumeSelected}
                  onValueChange={setVolumeSelected}
                />
              ) : (
                <span>
                  {dayFormatAndUnit?.volume === 'LT' ? t('L') : t('Gal')}
                </span>
              )}
            </div>
          </div>
          <div className="divider-v h-[150px] mx-[30px]" />
          <div className="w-full col-span-1">
            <div>
              <span>{t('TemperatureUnit')}</span>
              {dateUnitEdit ? (
                <Radio
                  options={[
                    { value: '0', label: t('F') },
                    { value: '1', label: t('C') },
                  ]}
                  value={tempSelected}
                  onValueChange={setTempSelected}
                />
              ) : (
                <span>{dayFormatAndUnit?.temp === 'DF' ? t('C') : t('F')}</span>
              )}
            </div>
            <div>
              <span>{t('PressureUnit')}</span>
              {dateUnitEdit ? (
                <Radio
                  options={[
                    { value: '0', label: t('kgfCm') },
                    { value: '1', label: t('psi') },
                  ]}
                  value={pressSelected}
                  onValueChange={setPressSelected}
                />
              ) : (
                <span>
                  {dayFormatAndUnit?.press === 'PSI' ? t('psi') : t('kgfCm')}
                </span>
              )}
            </div>
            <div>
              <span>{t('WeightUnit')}</span>
              {dateUnitEdit ? (
                <Radio
                  options={[
                    { value: '0', label: 'kg' },
                    { value: '1', label: 'lb' },
                  ]}
                  value={weightSelected}
                  onValueChange={setWeightSelected}
                />
              ) : (
                <span>
                  {dayFormatAndUnit?.weight === 'TON'
                    ? 'ton'
                    : dayFormatAndUnit?.weight === 'USTON'
                      ? 'US ton'
                      : 'lb'}
                </span>
              )}
            </div>
          </div>
        </div>
      </article>
    </Tabs.Content>
  );
};

export default PersonalSetting;
