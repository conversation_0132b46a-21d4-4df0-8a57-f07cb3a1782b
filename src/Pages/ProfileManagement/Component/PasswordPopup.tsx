import { useTranslation } from 'react-i18next';
import Layout from '@/Common/Popup/Layout.tsx';
import Input from '@/Common/Components/common/Input';
import { Button } from '@/Common/Components/common/Button';
import { AlertPopupProps } from '@/types';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

const passwordSchema = z
  .object({
    currentPassword: z.string().min(1, '현재 비밀번호를 입력하세요.'),
    newPassword: z
      .string()
      .min(10, '비밀번호는 최소 10자 이상이어야 합니다.')
      .max(16, '비밀번호는 최대 16자까지 입력 가능합니다.')
      .regex(
        /^(?=(.*[a-z]))(?=(.*[A-Z]))(?=(.*\d))(?=(.*[\W_])).{10,16}$/,
        '영문 대문자, 소문자, 숫자, 특수문자 중 3가지 이상 포함해야 합니다.',
      )
      .refine(
        (password) =>
          !/(123|234|345|456|567|678|789|890|qwe|wer|ert|rty|tyu|yui|uio|iop|asd|sdf|dfg|fgh|ghj|hjk|jkl|zxc|xcv|cvb|vbn|bnm)/i.test(
            password,
          ),
        {
          message:
            '연속된 키보드 문자 또는 숫자열(예: 123, qwe)은 사용할 수 없습니다.',
        },
      ),
    newPasswordVerify: z
      .string()
      .min(10, '비밀번호 확인은 최소 10자 이상 입력해야 합니다.'), // 🔥 추가
  })
  .refine((data) => data.newPassword === data.newPasswordVerify, {
    message: '새 비밀번호가 일치하지 않습니다.',
    path: ['newPasswordVerify'],
  });

type PasswordChangeValues = z.infer<typeof passwordSchema>;

const PasswordPopup = ({ onClose, isOpen }: AlertPopupProps) => {
  const { t } = useTranslation();

  const {
    register,
    formState: { errors, isValid },
  } = useForm<PasswordChangeValues>({
    resolver: zodResolver(passwordSchema),
    mode: 'onChange',
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      newPasswordVerify: '',
    },
  });

  console.log('Form Errors:', errors);

  // const createFleetMutation = useMutation({
  //   mutationFn: (body: profileApiChangePassword1Request) => {
  //     return profileApi.changePassword1(body);
  //   },
  //   onSuccess: () => {
  //     logout();
  //   },
  //   onError: (error) => {
  //     console.error('API 호출 에러:', error);
  //   },
  // });

  // const onSubmit = async (data: PasswordChangeValues) => {
  //   console.log('Form Submitted:', data); // 데이터 확인
  //   // API에 맞게 데이터 변환
  //   const requestBody: profileApiChangePassword1Request = {
  //     currentPassword: data.currentPassword,
  //     newPassword: data.newPassword,
  //   };
  //
  //   console.log('Request Body:', requestBody); // 변환된 데이터 확인
  //   createFleetMutation.mutate(requestBody);
  // };

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[595px] py-[30px] px-5 bg-white rounded-lg">
        {/*  */}
        <article className="heading2 mt-5 text-center">
          {t('ChangePassword')}
        </article>

        {/*<form onSubmit={handleSubmit(onSubmit)}>*/}
        <form>
          {/*  */}
          <article className="mt-10 mb-6 space-y-4">
            <Input
              type="password"
              placeholder={t('CurrentPassword')}
              {...register('currentPassword')}
              error={errors.currentPassword?.message}
            />
            <Input
              type="password"
              placeholder={t('NewPassword')}
              {...register('newPassword')}
              error={errors.newPassword?.message}
            />
            <Input
              type="password"
              placeholder={t('ConfirmNewPassword')}
              {...register('newPasswordVerify')}
              error={errors.newPasswordVerify?.message}
            />
          </article>

          {/*  */}
          <article className="mb-10 py-[30px] px-5 bg-[#F6F6F6] space-y-4 rounded-sm">
            <ul className="caption2 text-gray-12">
              <li>{t('EnterBetween10To16Characters')}</li>
              <li>{t('MixTypesLettersNumbersAndSpecialCharacters')}</li>
              <li>
                {t('NoConsecutive3CharactersOrNumbersInARowOnTheKeyboard')}
              </li>
            </ul>
          </article>

          {/* 버튼 */}
          <div className="mt-10 flex justify-center space-x-3">
            <Button
              variant={'bt_primary'}
              label={'Cancel'}
              onClick={onClose}
              className="w-full h-12"
            />
            <Button
              variant={'bt_primary'}
              label={'ChangeE'}
              className="w-full h-12"
              disabled={!isValid}
            />
          </div>
        </form>
      </section>
    </Layout>
  );
};

export default PasswordPopup;
