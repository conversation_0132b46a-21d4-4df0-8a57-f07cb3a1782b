import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Tabs } from '@radix-ui/themes';
import PersonalSetting from '@/Pages/ProfileManagement/PersonalSetting.tsx';
import NotificationSettings from '@/Pages/ProfileManagement/NotificationSettings.tsx';

const ProfileManagement = () => {
  const { t } = useTranslation();

  const location = useLocation();
  const changeTab = location.state?.tab || 'PersonalSetting';
  const [value, setValue] = useState(changeTab);

  useEffect(() => {
    console.log(location.state?.tab);
    if (location.state?.tab) {
      setValue(location.state.tab);
    }
  }, [location.state?.tab]);

  return (
    <CustomFrame name={t('ProfileManagement')} back={false}>
      <section>
        <Tabs.Root value={value} onValueChange={setValue}>
          <Tabs.List className="tab-design">
            <Tabs.Trigger value={'PersonalSetting'}>
              <span>{t('PersonalSetting')}</span>
            </Tabs.Trigger>
            <Tabs.Trigger value={'NotificationSettings'}>
              <span>{t('NotificationSettings')}</span>
            </Tabs.Trigger>
          </Tabs.List>

          <div className="py-8 flex-1">
            {value === 'PersonalSetting' && <PersonalSetting title={value} />}
            {value === 'NotificationSettings' && (
              <NotificationSettings title={value} />
            )}
          </div>
        </Tabs.Root>
      </section>
    </CustomFrame>
  );
};

export default ProfileManagement;
