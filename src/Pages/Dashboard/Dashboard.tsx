import { useTranslation } from 'react-i18next';
import React, { useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Tabs } from '@radix-ui/themes';
import { DashboardType } from '@/types/DashboardType.ts';
import { StatisticsType } from '@/types/StatisticsType.ts';
import {
  generateFleetList,
  generateDashboardData,
  generateWeatherData,
} from '@/helpers/dashboardDataGenerator';
import { DemoTest } from '@/types';
import { useSelectedType } from '@/context/SelectedTypeContext.tsx';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import PeriodDropdown from './components/PeriodDropdown.tsx';
import Accident from './components/Accident.tsx';
import StatCardAGroup from './components/StatCardAGroup.tsx';
import StatCardBGroup from './components/StatCardBGroup.tsx';
import ErrorAlertChart from '@/Pages/Dashboard/components/ErrorAlertChart.tsx';
import MaintenanceReminderChart from '@/Pages/Dashboard/components/MaintenanceReminderChart.tsx';
import DashboardMap from '@/Pages/Dashboard/components/DashboardMap.tsx';
import DeliveryStatus from '@/Pages/Dashboard/components/DeliveryStatus.tsx';
import FuelEfficiencyCard, {
  FuelEfficiencyCardProps,
} from './components/FuelEfficiencyCard.tsx';
import EvEfficiencyCard, {
  EvEfficiencyCardProps,
} from './components/EvEfficiencyCard.tsx';
import Notice from '@/Pages/Dashboard/components/Notice.tsx';
import LineGraph, {
  LineGraphData,
} from '@/Pages/Dashboard/components/LineGraph.tsx';
import DrivingEfficiency from '@/Pages/Dashboard/components/DrivingEfficiency.tsx';
import VehicleImpact from '@/Pages/Dashboard/components/VehicleImpact.tsx';
import DriverImpact from '@/Pages/Dashboard/components/DriverImpact.tsx';
import NumberOfCollisions, {
  NumberOfCollisionsData,
} from '@/Pages/Dashboard/components/NumberOfCollisions.tsx';
import Weather from '@/Pages/Dashboard/components/Weather.tsx';
import setting from '@/assets/images/ic/24/setting.svg';

/** Types */
type DashboardData = {
  statCard: {
    workingTimeData: DashboardType.StatCardData;
    totalOperationTimeData: DashboardType.StatCardData;
    drivingTimeData: DashboardType.StatCardData;
    idleTimeData: DashboardType.StatCardData;
  };
  errorAlertChartData: Array<{ name: string; value: number }>;
  maintenanceReminderChartData: Array<{ name: string; value: number }>;
  chargingData: EvEfficiencyCardProps;
  fuelData: FuelEfficiencyCardProps;
  statCard2: {
    operatingMachines?: {
      totalWorkingTimeData: DashboardType.StatCardData;
      totalIdlingData: DashboardType.StatCardData;
    };
    workers?: {
      totalDrivingTimeData: DashboardType.StatCardData;
      totalWorkingTimeData: DashboardType.StatCardData;
    };
  };
  lineChartData: Record<string, LineGraphData>;
  operationEfficiencyData: StatisticsType.DataPoint[];
  impactData: {
    eq: StatisticsType.RowProps[];
    user: StatisticsType.RowProps[];
  };
  NumberOfCollisions: NumberOfCollisionsData;
};

/**
 * 대시보드
 */
const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { selectedType } = useSelectedType();

  // 플릿(탭) 선택 상태
  const [selectedFleetTab, setSelectedFleetTab] = useState<string>('Fleet1');
  // 기간 선택 상태
  const [selectedPeriod, setSelectedPeriod] = useState<{
    key: string;
    value: string;
  }>();

  /** Queries */
  // 플릿(탭) 리스트
  const { data: fleetTabList = ['All'] } = useQuery<string[]>({
    queryKey: ['dashboard/fleet/tablist'],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        const resp = generateFleetList();
        return ['All', ...(resp?.map((fleet: string) => fleet) ?? [])];
      }
      const resp: string[] = [];
      return ['All', ...(resp?.map((fleet: string) => fleet) ?? [])];
    },
    staleTime: 60_000,
  });

  // 대시보드 데이터
  const { data: dashboardData } = useQuery<DashboardData | null>({
    queryKey: ['dashboard/data', selectedFleetTab, selectedPeriod?.value],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) return generateDashboardData();
      return null;
    },
    enabled: Boolean(selectedFleetTab && selectedPeriod),
  });

  // 날씨 데이터
  const { data: weatherData = [] } = useQuery<DashboardType.WeatherPageRow[]>({
    queryKey: ['weatherData/list'],
    queryFn: async () => (DemoTest.isRandomOn() ? generateWeatherData() : []),
    staleTime: 60_000,
  });

  /** Derived */
  const fleetProp = useMemo(
    () =>
      selectedFleetTab === 'All'
        ? 'all'
        : selectedFleetTab.replace('Fleet', ''),
    [selectedFleetTab],
  );

  const deliveryStatusData = useMemo(
    () => [
      { type: 'incomplete' as const, count: 513 },
      { type: 'complete' as const, count: 32 },
    ],
    [],
  );

  return (
    <CustomFrame name={t('')} back={false}>
      {/* 상단 우측 액션 */}
      <div className="w-full relative">
        <div className="f-c gap-2 absolute top-0 right-0">
          <PeriodDropdown onChange={(option) => setSelectedPeriod(option)} />
          <button className="w-12 h-11 f-c-c flex-shrink-0 b-b-r pointer-events-none opacity-30">
            <img src={setting} alt="setting" className="w-6 h-6" />
          </button>
        </div>
      </div>

      <Tabs.Root
        defaultValue={'all'}
        onValueChange={(val) =>
          setSelectedFleetTab(val === 'all' ? 'All' : 'Fleet1')
        }
      >
        <Tabs.List className="tab-design-bt">
          {/* 필요 시 fleetTabList 기반으로 동적 탭 구성 가능 */}
          <Tabs.Trigger value="all">All</Tabs.Trigger>
          <Tabs.Trigger value="fleet1">Fleet 1</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content
          value="all"
          className="mt-8 grid grid-cols-4 row-auto gap-7 [&>div]:bg-white [&>div]:rounded-md [&>div]:shadow-custom"
        >
          {/* Accident */}
          <Accident
            onClick={() => navigate('/eq_list')}
            className="col-span-4"
          />

          {/* 총 운영 장비, 작업 장비, 작업자 수, 유휴인원 */}
          <StatCardAGroup
            workingTimeData={dashboardData?.statCard.workingTimeData}
            totalOperationTimeData={
              dashboardData?.statCard.totalOperationTimeData
            }
            drivingTimeData={dashboardData?.statCard.drivingTimeData}
            idleTimeData={dashboardData?.statCard.idleTimeData}
            variant="first"
            className="col-span-4"
          />

          {/* 고장 알람 */}
          <ErrorAlertChart
            title={t('ErrorAlert')}
            data={dashboardData?.errorAlertChartData}
            className="col-span-1"
          />

          {/* 소모품 교체 알람 */}
          <MaintenanceReminderChart
            title={t('MaintenanceReminder')}
            data={dashboardData?.maintenanceReminderChartData}
            className="col-span-1"
          />

          {/* 장비 위치 */}
          <DashboardMap fleet={fleetProp} className="col-span-2" />

          {/* Delivery Status */}
          {selectedType !== 'HeavyEquip' && (
            <DeliveryStatus data={deliveryStatusData} className="col-span-4" />
          )}

          {/* 충전 횟수, 배터리 소모량 */}
          <EvEfficiencyCard
            mainSection={dashboardData?.chargingData.mainSection}
            subSection={dashboardData?.chargingData.subSection}
            mainUnit={dashboardData?.chargingData.mainUnit ?? 'Times'}
            subUnit={dashboardData?.chargingData.subUnit ?? 'kWh'}
            className="col-span-1"
          />

          {/* 주유 횟수, 연료 소모량 */}
          <FuelEfficiencyCard
            mainSection={dashboardData?.fuelData.mainSection}
            subSection={dashboardData?.fuelData.subSection}
            mainUnit={dashboardData?.fuelData.mainUnit ?? 'L'}
            subUnit={dashboardData?.fuelData.subUnit ?? 'L'}
            className="col-span-1"
          />

          {/* 공지사항 */}
          <Notice auth="G" userId="1" className="col-span-2" />

          {selectedType === 'HeavyEquip' && (
            <StatCardBGroup
              operatingMachines={dashboardData?.statCard2.operatingMachines}
              workers={dashboardData?.statCard2.workers}
              variant="second"
              className="col-span-4"
            />
          )}

          {/* 평균 장비 가동률 */}
          <LineGraph
            dataset={dashboardData?.lineChartData}
            title={t('AvgVehicleUtilization')}
            className="col-span-2"
          />

          {/* 운행 효율 */}
          <DrivingEfficiency
            title={t('DrivingEfficiency')}
            data={dashboardData?.operationEfficiencyData}
            className="col-span-2"
          />

          {/* 장비별 충격 횟수 */}
          <VehicleImpact
            eq={dashboardData?.impactData.eq}
            className="col-span-2"
          />

          {/* 운전자별 충격 횟수 */}
          <DriverImpact
            user={dashboardData?.impactData.user}
            className="col-span-2"
          />

          {selectedType === 'HeavyEquip' && (
            <>
              {/* 방향별 충격 횟수 */}
              <NumberOfCollisions
                data={dashboardData?.NumberOfCollisions}
                className="col-span-1"
              />
              {/* 기상 정보 */}
              <Weather weather={weatherData} className="col-span-3" />
            </>
          )}
        </Tabs.Content>

        <Tabs.Content value="fleet1">Fleet 1</Tabs.Content>
      </Tabs.Root>
    </CustomFrame>
  );
};

export default Dashboard;
