import { useTranslation } from 'react-i18next';
import React, { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { formatDate } from '@/Common/function/functions.ts';
import { DemoTest } from '@/types';
import { DashboardType } from '@/types/DashboardType';
import { generateNoticeData } from '@/helpers/dashboardDataGenerator';
import H2Title from '@/Common/Components/common/H2Title';

/** Types */
interface NoticeInfo {
  id?: string | number;
  title: string;
  startDate: string | Date;
}

/** Utilities */
const normalizeDateString = (d: string | Date): string =>
  typeof d === 'string' ? d : d.toISOString();

/** Data fetcher */
const fetchNotices = async (
  _auth: any,
  _userId: any,
): Promise<NoticeInfo[]> => {
  if (DemoTest.isRandomOn()) {
    return generateNoticeData(5);
  }
  // TODO: 실제 API 연동 시 여기서 호출
  return [];
};

const Notice: React.FC<DashboardType.NoticeProps> = ({
  auth,
  userId,
  className = '',
}) => {
  const { t } = useTranslation();

  const {
    data: noticeData = [],
    isLoading,
    isError,
  } = useQuery<NoticeInfo[]>({
    queryKey: ['notices', auth, userId],
    queryFn: () => fetchNotices(auth, userId),
    initialData: [],
    staleTime: 60_000,
  });

  const content = useMemo(() => {
    if (isLoading) {
      return (
        <div className="py-[10px] pb-6 px-6 caption3 text-gray-8 ">
          {t('Loading')}...
        </div>
      );
    }

    if (isError) {
      return (
        <div className="py-[10px] pb-6 px-6 caption3 text-semantic-1 ">
          {t('LoadFailed')}
        </div>
      );
    }

    if (!noticeData.length) {
      return (
        <div className="py-[10px] pb-6 px-6 caption3 text-gray-8 ">
          {t('NoNotice') ?? 'No notices'}
        </div>
      );
    }

    return (
      <div className="py-[10px] pb-6 px-6">
        {noticeData.map((n) => (
          <Row
            key={n.id ?? `${n.title}-${normalizeDateString(n.startDate)}`}
            {...n}
          />
        ))}
      </div>
    );
  }, [isLoading, isError, noticeData, t]);

  return (
    <div className={className}>
      <H2Title>{t('Notice')}</H2Title>
      {content}
    </div>
  );
};

const Row: React.FC<NoticeInfo> = ({ title, startDate }) => (
  <div className="py-3 px-5 f-c-b border-b border-gray-6 cursor-pointer transition-colors hover:bg-gray-1">
    <div className="body4">{title}</div>
    <div className="caption3 text-gray-8">
      {formatDate(normalizeDateString(startDate))}
    </div>
  </div>
);

export default Notice;
