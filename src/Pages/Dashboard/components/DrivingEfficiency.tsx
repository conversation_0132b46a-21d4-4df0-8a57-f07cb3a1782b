import React, { useEffect, useRef, useState } from 'react';
import ECharts from 'echarts-for-react';
import { StatisticsType } from '@/types/StatisticsType';
import { InteractiveInfoOption as defaultOption } from '@/Common/constants/GraphOptions.ts';
import H2Title from '@/Common/Components/common/H2Title';

export interface DrivingEfficiencyProps {
  data: StatisticsType.DataPoint[] | undefined;
  title?: string;
  className?: string;
}

/**
 * 운행 효율을 보여주는 바 그래프 컴포넌트
 * @param data - 일별 운행 효율 데이터 배열
 */
const DrivingEfficiency: React.FC<DrivingEfficiencyProps> = ({
  title,
  data = [],
  className,
}) => {
  const chartRef = useRef<ECharts>(null);

  const [option, setOption] = useState(() => ({
    ...defaultOption,
    xAxis: { ...defaultOption.xAxis, data: data.map((e) => e.day) },
    series: [{ ...defaultOption.series[0], data: data.map((e) => e.value) }],
  }));

  useEffect(() => {
    if (!data || data.length === 0) return;
    setOption((prev) => ({
      ...prev,
      xAxis: { ...prev.xAxis, data: data.map((e) => e.day) },
      series: [{ ...prev.series?.[0], data: data.map((e) => e.value) }],
    }));
  }, [data]);

  useEffect(() => {
    const handleResize = () => {
      if (chartRef.current) {
        chartRef.current.getEchartsInstance().resize();
      }
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className={`${className}`}>
      {/* 헤더 */}
      <H2Title>{title}</H2Title>
      <div>
        <ECharts
          ref={chartRef}
          option={option}
          style={{ width: '100%', height: '210px' }}
          opts={{ renderer: 'svg' }}
          notMerge={true}
          lazyUpdate={true}
        />
      </div>
    </div>
  );
};

export default DrivingEfficiency;
