import { useTranslation } from 'react-i18next';
import { v4 } from 'uuid';
import { StatisticsType } from '@/types/StatisticsType';
import H2Title from '@/Common/Components/common/H2Title';
import Row from '@/Common/Components/common/Row';

type DriverImpactProps = {
  user: StatisticsType.RowProps[] | undefined;
  className?: string;
};

const DriverImpact = ({ user, className }: DriverImpactProps) => {
  const { t } = useTranslation();

  return (
    <div className={`${className}`}>
      <H2Title>{t('DriverImpactCount')}</H2Title>
      <div className="pt-[10px] pb-6 px-6">
        {user?.map((e: StatisticsType.RowProps, index) => (
          <Row key={v4()} {...e} rowNum={index + 1} type="driver" />
        ))}
      </div>
    </div>
  );
};

export default DriverImpact;
