import { useTranslation } from 'react-i18next';
import H2Title from '@/Common/Components/common/H2Title';
import forklift from '@/assets/images/dashboard/forklift.svg';

export type NumberOfCollisionsData = {
  vertical: number;
  side: number;
  fr: number;
};

interface NumberOfCollisionsProps {
  className?: string;
  data: NumberOfCollisionsData | undefined;
}

const NumberOfCollisions = ({
  className,
  data = {
    vertical: 0,
    side: 0,
    fr: 0,
  },
}: NumberOfCollisionsProps) => {
  const { t } = useTranslation();

  return (
    <div className={className}>
      <H2Title>{t('NumberOfCollisions')}</H2Title>

      <div className="py-10 px-5 f-c-c gap-4">
        <img src={forklift} alt="forklift" />

        <div
          className="
            caption3
            [&>div]:f-c-s
            [&>div:first-child]:[&>div]:w-[140px]
            [&>div:first-child]:[&>div]:f-c
            [&>div:first-child]:[&>div]:gap-2
          [&>div:nth-child(1)>div:first-child>span]:bg-semantic-4
          [&>div:nth-child(2)>div:first-child>span]:bg-semantic-1
          [&>div:nth-child(3)>div:first-child>span]:bg-semantic-3
            [&_span]:w-3
            [&_span]:h-3
            [&_span]:rounded-full
          "
        >
          <div>
            <div>
              <span />
              {t('FrontRearImpact')}
            </div>
            <div>
              {data.vertical}
              {t('Times')}
            </div>
          </div>
          <div>
            <div>
              <span />
              {t('SideImpact')}
            </div>
            <div>
              {data.side}
              {t('Times')}
            </div>
          </div>
          <div>
            <div>
              <span />
              {t('VerticalImpact')}
            </div>
            <div>
              {data.fr}
              {t('Times')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NumberOfCollisions;
