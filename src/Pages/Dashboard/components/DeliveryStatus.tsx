import { useTranslation } from 'react-i18next';
import incomplete from '@/assets/images/ic/36/incomplete.svg';
import complete from '@/assets/images/ic/36/complete.svg';
import fail from '@/assets/images/ic/36/fail.svg';

export interface DeliveryStatusData {
  type: 'incomplete' | 'complete' | 'fail';
  count: number;
}

interface DeliveryStatusProps {
  className?: string;
  data: readonly DeliveryStatusData[];
}

const iconMap = {
  incomplete,
  complete,
  fail,
};

const status = {
  incomplete: 'Incomplete',
  complete: 'Completed',
  fail: 'Failed',
};

const DeliveryStatus = ({ className, data }: DeliveryStatusProps) => {
  const { t } = useTranslation();

  return (
    <div className={`${className} p-6`}>
      <h2 className="mb-7 subtitle3">{t('DeliveryStatus')}</h2>
      <div
        className="f-c-b
          [&>div]:w-full [&>div]:px-[50px] [&>div:last-child]:pr-0 [&>div]:f-c-b [&>div]:border-r [&>div:last-child]:border-0 [&>div]:border-gray-6
          [&_h3]:f-c [&_h3]:gap-3 [&_h3]:subtitle3
          [&_p]:subtitle1
        "
      >
        {data.map((item) => (
          <div key={item.type}>
            <h3>
              <img src={iconMap[item.type]} alt={item.type} />{' '}
              {t(status[item.type])}
            </h3>
            <p>{item.count}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DeliveryStatus;
