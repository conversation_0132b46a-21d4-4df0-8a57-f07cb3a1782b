import { useTranslation } from 'react-i18next';
import * as React from 'react';
import { v4 } from 'uuid';
import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import {
  CustomTable,
  CustomTableBody,
  CustomTableCell,
  CustomTableHead,
  CustomTableHeader,
  CustomTableProps,
  CustomTableRow,
} from '@/Common/Components/common/CustomTable';

export const NoticeTable = <T,>({
  data = [],
  columns = [],
  onClickRow,
  id,
}: CustomTableProps<T>) => {
  const { t } = useTranslation();

  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    [],
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = React.useState({});
  const table = useReactTable<(typeof data)[0]>({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <CustomTable>
      <CustomTableHeader className={'bg-primary-0'}>
        {table.getHeaderGroups().map((headerGroup) => (
          <CustomTableRow key={headerGroup.id} className={''}>
            {headerGroup.headers.map((header) => (
              <CustomTableHead
                key={header.id}
                style={{
                  whiteSpace: 'nowrap',
                  width: header.column.getSize(),
                }}
                className={'border-x first:border-l-0 last:border-r-0'}
              >
                {header.isPlaceholder
                  ? null
                  : flexRender(
                      header.column.columnDef.header,
                      header.getContext(),
                    )}
              </CustomTableHead>
            ))}
          </CustomTableRow>
        ))}
      </CustomTableHeader>
      <CustomTableBody>
        {table?.getCoreRowModel().rows.length ? (
          table?.getCoreRowModel().rows.map((row) => (
            <CustomTableRow
              onClick={() => {
                if (id) {
                  if (onClickRow) {
                    const original = row.original;
                    onClickRow(original[id]);
                  }
                }
              }}
              key={v4()}
              style={{ cursor: 'pointer' }}
            >
              {row?.getAllCells().map((cell) => (
                <CustomTableCell
                  className={'py-3 border-x last:border-r-0 first:border-l-0'}
                  style={{ width: cell.column.getSize() }}
                  key={v4()}
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </CustomTableCell>
              ))}
            </CustomTableRow>
          ))
        ) : (
          <CustomTableRow className={'h-[500px]'}>
            <CustomTableCell colSpan={columns?.length} className="text-center">
              {t('NoDataAvailable')}
            </CustomTableCell>
          </CustomTableRow>
        )}
      </CustomTableBody>
    </CustomTable>
  );
};

export default NoticeTable;
