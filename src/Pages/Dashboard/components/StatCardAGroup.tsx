import { t } from 'i18next';
import { DashboardType } from '@/types/DashboardType';
import { useSelectedType } from '@/context/SelectedTypeContext.tsx';
import StatCard from './StatCard';
import first from '@/assets/images/dashboard/first.svg';
import second from '@/assets/images/dashboard/second.svg';
import third from '@/assets/images/dashboard/third.svg';
import fourth from '@/assets/images/dashboard/fourth.svg';

interface StatCardAGroupProps {
  workingTimeData?: DashboardType.StatCardData;
  totalOperationTimeData?: DashboardType.StatCardData;
  drivingTimeData?: DashboardType.StatCardData;
  idleTimeData?: DashboardType.StatCardData;
  className?: string;
  variant?: 'first' | 'second';
}

const StatCardAGroup = ({
  workingTimeData,
  totalOperationTimeData,
  drivingTimeData,
  idleTimeData,
  className,
  variant,
}: StatCardAGroupProps) => {
  const { selectedType } = useSelectedType();
  const isHeavyEquip = selectedType === 'HeavyEquip';

  return (
    <div className={`${className} py-5 f-c-b [&>div:last-child]:border-0`}>
      <StatCard
        icon={first}
        title={t(isHeavyEquip ? 'TotalEquipment' : 'TotalVehicles')}
        unit={t('UnitsN')}
        value={workingTimeData?.value ?? 0}
        comparison={{
          text: t('VsPerviousDay'),
          value: workingTimeData?.comparison.value ?? '0',
          isIncrease: workingTimeData?.comparison.isIncrease ?? false,
        }}
        variant={variant}
      />
      <StatCard
        icon={second}
        title={t(
          isHeavyEquip ? 'TotalEquipmentOperations' : 'TotalVehicleOperations',
        )}
        unit={t('UnitsN')}
        value={totalOperationTimeData?.value ?? 0}
        comparison={{
          text: t('VsPerviousDay'),
          value: totalOperationTimeData?.comparison.value ?? '0',
          isIncrease: totalOperationTimeData?.comparison.isIncrease ?? false,
        }}
        variant={variant}
      />
      <StatCard
        icon={third}
        title={t(isHeavyEquip ? 'NumberOfWorkers' : 'IdleVehicles')}
        unit={t('People')}
        value={drivingTimeData?.value ?? 0}
        comparison={{
          text: t('VsPerviousDay'),
          value: drivingTimeData?.comparison.value ?? '0',
          isIncrease: drivingTimeData?.comparison.isIncrease ?? false,
        }}
        variant={variant}
      />
      <StatCard
        icon={fourth}
        title={t(isHeavyEquip ? 'IdlePersonnel' : 'UnderMaintenance')}
        unit={t('People')}
        value={idleTimeData?.value ?? 0}
        comparison={{
          text: t('VsPerviousDay'),
          value: idleTimeData?.comparison.value ?? '0',
          isIncrease: idleTimeData?.comparison.isIncrease ?? false,
        }}
        variant={variant}
      />
    </div>
  );
};

export default StatCardAGroup;
