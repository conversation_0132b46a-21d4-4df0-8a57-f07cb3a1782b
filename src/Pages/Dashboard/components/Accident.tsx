import { useTranslation } from 'react-i18next';
import warning from '@/assets/images/ic/28/warning.svg';

interface AccidentProps {
  onClick?: () => void;
  className?: string;
}

const Accident = ({ onClick, className }: AccidentProps) => {
  const { t } = useTranslation();

  return (
    <div
      onClick={onClick}
      className={`${className} py-[21px] px-[25px] f-c gap-[10px] cursor-pointer transition-colors duration-150 hover:bg-semantic-4-1`}
    >
      <img src={warning} alt="warning" />
      <h2 className="subtitle3 text-semantic-4">
        {t('ThereIsAVehicleInvolvedInAnAccident')}
      </h2>
    </div>
  );
};

export default Accident;
