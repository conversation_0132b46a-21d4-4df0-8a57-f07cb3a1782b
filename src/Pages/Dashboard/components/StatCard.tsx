// StatCard.tsx
import { useTranslation } from 'react-i18next';
import React from 'react';
import { DashboardType } from '@/types/DashboardType';

const StatCard: React.FC<DashboardType.StatCardProps> = ({
  title,
  icon,
  value,
  unit = '',
  comparison,
  variant = 'second', // 기본은 두번째 스타일
}) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';

  return (
    <div className="w-full px-6 space-y-[9px] border-r border-gray-6">
      <h2 className="f-c gap-3 body1">
        {variant !== 'second' && <img src={icon} alt={title} />}
        {title}
      </h2>

      {variant === 'first' ? (
        <>
          <div className="f-c gap-2">
            <div className="subtitle1">{value}</div>
            <div className="body1">{unit}</div>
          </div>

          {isEnglish ? (
            <div className="f-c gap-[6px]">
              <div
                className={`w-fit px-[7px] rounded ${
                  comparison.isIncrease
                    ? 'bg-semantic-1-1 [&>span]:text-semantic-1'
                    : 'bg-semantic-4-1 [&>span]:text-semantic-4'
                }`}
              >
                <span>{comparison.isIncrease ? '+' : '-'}</span>
                <span>
                  {comparison.value}
                  {unit}
                </span>
                <span>{comparison.isIncrease ? t('Up') : t('Down')}</span>
              </div>
              <div className="caption4 text-gray-10">{comparison.text}</div>
            </div>
          ) : (
            <div className="f-c gap-[6px]">
              <div className="caption4 text-gray-10">{comparison.text}</div>
              <div
                className={`w-fit px-[7px] rounded ${
                  comparison.isIncrease
                    ? 'bg-semantic-1-1 [&>span]:text-semantic-1'
                    : 'bg-semantic-4-1 [&>span]:text-semantic-4'
                }`}
              >
                <span>{comparison.isIncrease ? '+' : '-'}</span>
                <span>
                  {comparison.value}
                  {unit}
                </span>
                <span>{comparison.isIncrease ? t('Up') : t('Down')}</span>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="f-s-b">
          <div>
            {isEnglish ? (
              <div className="f-c gap-[6px]">
                <div
                  className={`w-fit px-[7px] rounded ${
                    comparison.isIncrease
                      ? 'bg-semantic-1-1 [&>span]:text-semantic-1'
                      : 'bg-semantic-4-1 [&>span]:text-semantic-4'
                  }`}
                >
                  <span>{comparison.isIncrease ? '+' : '-'}</span>
                  <span>
                    {comparison.value}
                    {unit}
                  </span>
                  <span>{comparison.isIncrease ? t('Up') : t('Down')}</span>
                </div>
                <div className="caption4 text-gray-10">{comparison.text}</div>
              </div>
            ) : (
              <div className="f-c gap-[6px]">
                <div className="caption4 text-gray-10">{comparison.text}</div>
                <div
                  className={`w-fit px-[7px] rounded ${
                    comparison.isIncrease
                      ? 'bg-semantic-1-1 [&>span]:text-semantic-1'
                      : 'bg-semantic-4-1 [&>span]:text-semantic-4'
                  }`}
                >
                  <span>{comparison.isIncrease ? '+' : '-'}</span>
                  <span>
                    {comparison.value}
                    {unit}
                  </span>
                  <span>{comparison.isIncrease ? t('Up') : t('Down')}</span>
                </div>
              </div>
            )}
          </div>

          <div className="f-c gap-2">
            <div className="subtitle1">{value}</div>
            <div className="body1">{unit}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StatCard;
