import { useTranslation } from 'react-i18next';
import React, { useEffect, useRef, useState } from 'react';
import { MapRandomData } from '@/Common/constants/Maps.ts';
import { DemoTest, MapEngine } from '@/types';
import { equipmentApi } from '@/api';
import { useQuery } from '@tanstack/react-query';
import ZoomController from '@/Common/Components/map/ZoomController';
import {
  EqSingleMarkerSet,
  EqGroupMarkerSet,
  MapHelper,
} from '@/Common/constants/Maps.ts';
import EqGroupMarker, {
  EqGroupMarkerProps,
} from '@/Common/Components/Marker/EqGroupMarker';
import EqSingleMarker, {
  EqSingleMarkerProps,
} from '@/Common/Components/Marker/EqSingleMarker';
import { GeneralMap, GeneralMapAdapter } from '@/logiMaps/react/general/Map';
import EqAddressInfoWindow, {
  EqAddressInfoWindowProps,
} from '@/Common/Components/eqWindow/EqAddressInfoWindow';
import H2Title from '@/Common/Components/common/H2Title';
import { generateMapData } from '@/helpers/dashboardDataGenerator';
import { DashboardType } from '@/types/DashboardType';
import {
  AdminEquipmentStatusResDTOBreakdownStatusEnum,
  AdminEquipmentStatusResDTOOperationStatusEnum,
} from '@/api/generated';

export interface DashboardMapProps {
  fleet: string;
  className?: string;
}

/**
 * UI.******* FMS 대시보드
 * 플릿에 소속된 모든 장비를 표시
 */
const DashboardMap: React.FC<DashboardMapProps & { className?: string }> = (
  props,
) => {
  const { t } = useTranslation();
  const [mapAdapter, setMapAdapter] = useState<GeneralMapAdapter | null>(null);
  const maxZoomLevel = 18;
  const minZoomLevel = 4;
  const defaultLevel = 15;
  const zoomLevelRef = useRef(defaultLevel);
  const markerTypeRef = useRef(0);
  // Singles
  const [eqSingleMarkerSets] = useState<Map<number, EqSingleMarkerSet>>(
    new Map(),
  );
  const [eqSingleMarkerProps, setEqSingleMarkerProps] = useState<
    EqSingleMarkerProps[]
  >([]);
  const [eqSingleInfoWindowProps, setEqSingleInfoWindowProps] =
    useState<EqAddressInfoWindowProps>();
  // Groups
  const [eqGroupMarkerSets] = useState<Map<number, EqGroupMarkerSet>>(
    new Map(),
  );
  const [eqGroupMarkerProps, setEqGroupMarkerProps] = useState<
    EqGroupMarkerProps[]
  >([]);

  /** Query */

  const { data: filteredMapItems } = useQuery({
    queryKey: ['/api/equipment/page', props.fleet],
    queryFn: async () => {
      if (DemoTest.isRandomOn()) {
        const response = generateMapData(10);
        const result: DashboardType.FilteredMapItem[] = [];
        if (response) {
          response?.forEach((data, idx) => {
            result.push({
              id: idx.toString(),
              fleet: idx.toString(),
              latlng: {
                lat: data.latlng?.lat ?? 0.0,
                lng: data.latlng?.lng ?? 0.0,
              },
              operationStatus: {
                running: MapRandomData.getRandomBoolean(),
                idle: MapRandomData.getRandomBoolean(),
              },
              breakdownStatus: {
                breakdown: MapRandomData.getRandomBoolean(),
                repairing: MapRandomData.getRandomBoolean(),
                none: !MapRandomData.getRandomBoolean(),
              },
              lastUpdate: '',
            });
          });
        }
        return result;
      } else {
        try {
          const response = await equipmentApi.getAdminEquipmentPage({
            fleetId:
              isNaN(Number(props.fleet)) == false
                ? Number(props.fleet)
                : undefined,
            modelName: '',
            serialNo: '',
            plateNo: '',
            countryId: undefined, //2 (USA)
            dealerId: undefined,
          });

          const result: DashboardType.FilteredMapItem[] = [];
          if (response.data) {
            response.data.content?.forEach((data, idx) => {
              result.push({
                id: data.equipmentId?.toString() ?? idx.toString(),
                fleet: idx.toString(),
                latlng: {
                  lat: data.status?.recentLocation?.y ?? 0.0,
                  lng: data.status?.recentLocation?.x ?? 0.0,
                },
                operationStatus: {
                  running:
                    data.status?.operationStatus ==
                    AdminEquipmentStatusResDTOOperationStatusEnum.Running,
                  idle:
                    data.status?.operationStatus ==
                    AdminEquipmentStatusResDTOOperationStatusEnum.Idle,
                },
                breakdownStatus: {
                  breakdown:
                    data.status?.breakdownStatus ==
                    AdminEquipmentStatusResDTOBreakdownStatusEnum.Breakdown,
                  repairing:
                    data.status?.breakdownStatus ==
                    AdminEquipmentStatusResDTOBreakdownStatusEnum.Repairing,
                  none:
                    data.status?.breakdownStatus ==
                    AdminEquipmentStatusResDTOBreakdownStatusEnum.None,
                },
                lastUpdate: '',
              });
            });
          }
          return result;
        } catch (error) {
          console.error('API 호출 에러:', error);
          throw error;
        }
      }
    },
    enabled: true,
    initialData: [],
  });

  // 검색 결과 갱신되면 맵 업데이트
  useEffect(() => {
    if (mapAdapter && filteredMapItems) {
      const bounds: { lat: number; lng: number }[] = [];
      for (const item of filteredMapItems) {
        // 북미 영역 좌표만 필터
        if (
          item.latlng.lat >= 10.0 &&
          item.latlng.lat <= 82.0 &&
          item.latlng.lng >= -168.0 &&
          item.latlng.lng <= -40.0
        ) {
          bounds.push(item.latlng);
        }
      }
      if (filteredMapItems.length > 0 && bounds.length > 0) {
        mapAdapter.fitBounds(bounds, {
          top: '10%',
          right: '10%',
          bottom: '0%',
          left: '0%',
        });
      }
      eqSingleMarkerSets.clear();
      eqGroupMarkerSets.clear();
      updateEqMarkers();
    }
  }, [mapAdapter, filteredMapItems]);

  /** Event Listener */

  // 지도 로드
  const handleMapInit = (generalMapAdapter: GeneralMapAdapter) => {
    setMapAdapter(generalMapAdapter);
  };

  // 지도 클릭되면 InfoWindow 닫기
  const handleMapClick = () => {
    setEqSingleInfoWindowProps(undefined);
  };

  // 지도 스케일 변경되면 마커 업데이트
  const handleMapZoomChanged = (zoom: number) => {
    zoomLevelRef.current = zoom;
    updateEqMarkers();
  };

  // 지도 사이즈 체크
  const handleMapBoundsChanged = () => {
    updateEqMarkers();
  };

  // 핀 마커 클릭하면 마커 주소 표시
  const handleClickSingleMark = (
    id: string,
    latlng: { lat: number; lng: number },
  ) => {
    const result = filteredMapItems.find((data) => data.id == id);
    if (!result) {
      setEqSingleInfoWindowProps(undefined);
    } else {
      setEqSingleInfoWindowProps({
        id: result.id,
        position: result.latlng,
        pixelOffset: [0, -14],
        address: '-----',
      });
    }
  };

  // 그룹 마커 클릭하면 마커 위치를 지도 중심으로 변경
  const handleClickGroupMark = (
    id: string,
    latlng: { lat: number; lng: number },
    markers: {
      id: string;
      latlng: {
        lat: number;
        lng: number;
      };
    }[],
  ) => {
    mapAdapter?.setCenter(latlng);
  };

  /** Function */

  // 마커 정보 업데이트
  const updateEqMarkers = () => {
    if (Math.floor(zoomLevelRef.current) <= 9) {
      // spec.1~7 (google api. 3~9)
      markerTypeRef.current = 0;
      setEqSingleInfoWindowProps(undefined);
    } else if (Math.floor(zoomLevelRef.current) <= 15) {
      // spec.8~13 (google api. 10~15)
      markerTypeRef.current = 1;
    } else {
      // spec.14~ (google api. 16~)
      markerTypeRef.current = 2;
    }

    const dataLevel = Math.floor(zoomLevelRef.current / 0.5) * 0.5;
    if (markerTypeRef.current == 0) {
      updateEqGroupMarkers(dataLevel);
    } else {
      updateEqSingleMarkers(dataLevel);
    }
  };

  // 핀 마커 정보 업데이트
  const updateEqSingleMarkers = (dataLevel: number) => {
    // const defaultOverlapDist = markerType.current == 0 ? 64.0 : 32.0;
    const defaultOverlapDist = 1; // boundary 검사 안함 (최소 거리만 체크)
    if (mapAdapter) {
      let result = eqSingleMarkerSets.get(dataLevel);
      if (result == null) {
        const eqSingleMarkerSet = MapHelper.makeEqSingleMarkerSet(
          filteredMapItems,
          mapAdapter,
          dataLevel,
          defaultOverlapDist,
        );
        eqSingleMarkerSets.set(dataLevel, eqSingleMarkerSet);
        result = eqSingleMarkerSet;
      }
      if (result) {
        const _eqSingleMarkerProps: EqSingleMarkerProps[] = [];
        for (const row of result.items) {
          if (mapAdapter.isInBoundary(row.latlng) == true) {
            _eqSingleMarkerProps.push({
              id: row.id,
              latlng: row.latlng,
              operationStatus: row.operationStatus,
              breakdownStatus: row.breakdownStatus,
              onClick: handleClickSingleMark,
            });
          }
        }
        setEqSingleMarkerProps(_eqSingleMarkerProps);
        setEqGroupMarkerProps([]);
        return;
      }
    }
    setEqSingleMarkerProps([]);
    setEqGroupMarkerProps([]);
  };

  // 그룹 마커 정보 업데이트
  const updateEqGroupMarkers = (dataLevel: number) => {
    //버블 사이즈 중에서 가장 큰 사이즈 (100)
    const defaultOverlapDist = 100.0;
    if (mapAdapter) {
      let result = eqGroupMarkerSets.get(dataLevel);
      if (result == null) {
        const eqGroupMarkerSet = MapHelper.makeEqGroupMarkerSet(
          filteredMapItems,
          mapAdapter,
          dataLevel,
          defaultOverlapDist,
        );
        eqGroupMarkerSets.set(dataLevel, eqGroupMarkerSet);
        result = eqGroupMarkerSet;
      }
      if (result) {
        const _eqGroupMarkerProps: EqGroupMarkerProps[] = [];
        for (const row of result.items) {
          if (mapAdapter.isInBoundary(row.latlng) == true) {
            _eqGroupMarkerProps.push({
              id: row.id,
              latlng: row.latlng,
              markers: row.markers,
              onClick: handleClickGroupMark,
            });
          }
        }
        setEqSingleMarkerProps([]);
        setEqGroupMarkerProps(_eqGroupMarkerProps);
        return;
      }
    }
    setEqSingleMarkerProps([]);
    setEqGroupMarkerProps([]);
  };

  return (
    <div className={`${props.className ?? ''}`}>
      <H2Title>{t('LocationE')}</H2Title>
      {/* 지도 영역 */}
      <div className={'w-full h-fit pb-5 px-6 relative overflow-hidden'}>
        <GeneralMap
          mapSource={MapEngine.source()}
          id={'dashboard'}
          maxZoom={maxZoomLevel}
          minZoom={minZoomLevel}
          defaultZoom={defaultLevel}
          onInitMap={handleMapInit}
          onClick={handleMapClick}
          onZoomChanged={handleMapZoomChanged}
          onBoundsChanged={handleMapBoundsChanged}
          className={'w-full h-[236px] overflow-hidden'}
        >
          {markerTypeRef.current == 0 ? (
            <>
              {/* group mark */}
              {eqGroupMarkerProps.map((data) => {
                return <EqGroupMarker key={data.id} {...data} />;
              })}
            </>
          ) : (
            <>
              {/* single mark */}
              {eqSingleMarkerProps.map((data) => {
                return (
                  <EqSingleMarker
                    key={data.id}
                    markerType={markerTypeRef.current}
                    {...data}
                  />
                );
              })}
              {eqSingleInfoWindowProps && (
                <EqAddressInfoWindow {...eqSingleInfoWindowProps} />
              )}
            </>
          )}
        </GeneralMap>
        {/* 줌 컨트롤 */}
        <ZoomController
          right={'right-8'}
          bottom={'bottom-7'}
          plus={function (): void {
            mapAdapter?.zoomIn();
          }}
          minus={function (): void {
            mapAdapter?.zoomOut();
          }}
        />
      </div>
    </div>
  );
};

export default DashboardMap;
