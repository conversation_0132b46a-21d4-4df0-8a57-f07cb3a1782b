import { t } from 'i18next';
import { DashboardType } from '@/types/DashboardType';
import StatCard from './StatCard';

interface StatCardBGroupProps {
  operatingMachines?: {
    totalWorkingTimeData: DashboardType.StatCardData;
    totalIdlingData: DashboardType.StatCardData;
  };
  workers?: {
    totalDrivingTimeData: DashboardType.StatCardData;
    totalWorkingTimeData: DashboardType.StatCardData;
  };
  className?: string;
  variant?: 'first' | 'second';
}

const StatCardBGroup = ({
  operatingMachines,
  workers,
  className,
  variant,
}: StatCardBGroupProps) => (
  <div className={`${className} py-5 f-c-b [&>div:last-child]:border-0`}>
    <StatCard
      title={t('TotalWorkingTimeO')}
      unit={t('H')}
      value={operatingMachines?.totalWorkingTimeData.value ?? 0}
      comparison={{
        text: t('VsPerviousDay'),
        value: operatingMachines?.totalWorkingTimeData.comparison.value ?? '0',
        isIncrease:
          operatingMachines?.totalWorkingTimeData.comparison.isIncrease ??
          false,
      }}
      variant={variant}
    />
    <StatCard
      title={t('TotalDrivingTime')}
      unit={t('H')}
      value={workers?.totalDrivingTimeData.value ?? 0}
      comparison={{
        text: t('VsPerviousDay'),
        value: workers?.totalDrivingTimeData.comparison.value ?? '0',
        isIncrease:
          workers?.totalDrivingTimeData.comparison.isIncrease ?? false,
      }}
      variant={variant}
    />
    <StatCard
      title={t('TotalIdling')}
      unit={t('H')}
      value={operatingMachines?.totalIdlingData.value ?? 0}
      comparison={{
        text: t('VsPerviousDay'),
        value: operatingMachines?.totalIdlingData.comparison.value ?? '0',
        isIncrease:
          operatingMachines?.totalIdlingData.comparison.isIncrease ?? false,
      }}
      variant={variant}
    />
    <StatCard
      title={t('TotalWorkingTime')}
      unit={t('H')}
      value={workers?.totalWorkingTimeData.value ?? 0}
      comparison={{
        text: t('VsPerviousDay'),
        value: workers?.totalWorkingTimeData.comparison.value ?? '0',
        isIncrease:
          workers?.totalWorkingTimeData.comparison.isIncrease ?? false,
      }}
      variant={variant}
    />
  </div>
);

export default StatCardBGroup;
