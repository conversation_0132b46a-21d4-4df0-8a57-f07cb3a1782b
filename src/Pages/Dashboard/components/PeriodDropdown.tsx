import { useTranslation } from 'react-i18next';
import React, { useEffect } from 'react';
import Dropdown from '@/Common/Components/common/DropDown';

interface PeriodDropdownProps {
  onChange: (option: { key: string; value: string }) => void;
}

const PeriodDropdown: React.FC<PeriodDropdownProps> = ({ onChange }) => {
  const { t } = useTranslation();

  // 오늘, 이번주, 이번달 옵션 정의
  const periodOptions = [
    { key: t('Today'), value: 'Today' },
    { key: t('Week'), value: 'Week' },
    { key: t('Month'), value: 'Month' },
  ];

  useEffect(() => {
    onChange(periodOptions[0]);
  }, []);

  return (
    <Dropdown
      options={periodOptions}
      placeholder={t('Today')}
      onSelPair={(key, value) => onChange({ key, value })}
    />
  );
};

export default PeriodDropdown;
