import { useTranslation } from 'react-i18next';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import ECharts from 'echarts-for-react';
import Dropdown from '@/Common/Components/common/DropDown';
import H2Title from '@/Common/Components/common/H2Title';
import { LineGraphOption as defaultOption } from '@/Common/constants/GraphOptions.ts';

/**
 * Types
 */
export type LineGraphItem = { time: string; value: number };
export type LineGraphData = LineGraphItem[];

export interface LineGraphProps {
  title: string;
  dataset?: Record<string, LineGraphData>;
  className?: string;
}

/**
 * Constants
 */
const DROPDOWN_OPTIONS = [
  { key: 'AvgVehicleUtilization', value: '0' },
  { key: 'NumberOfWorkers', value: '1' },
  { key: 'AlarmF', value: '2' },
  { key: 'AverageFuelEfficiency', value: '3' },
  { key: 'FuelConsumption', value: '4' },
  { key: 'FuelCost', value: '5' },
  { key: 'TotalWorkingTimeO', value: '6' },
] as const;

const CHART_HEIGHT = 210;

const LineGraph: React.FC<LineGraphProps> = ({
  title,
  dataset = {},
  className,
}) => {
  const { t } = useTranslation();
  const chartRef = useRef<ECharts>(null);

  // 선택된 옵션 "value"를 보관 (데이터 매핑이 간단해짐)
  const [selectedValue, setSelectedValue] = useState<string>(
    DROPDOWN_OPTIONS[0].value,
  );

  const selectedKey = useMemo(() => {
    return (
      DROPDOWN_OPTIONS.find((o) => o.value === selectedValue)?.key ??
      DROPDOWN_OPTIONS[0].key
    );
  }, [selectedValue]);

  // Dataset에서 현재 선택된 시리즈를 추출
  const selectedData: LineGraphData = useMemo(() => {
    return dataset?.[selectedValue] ?? [];
  }, [dataset, selectedValue]);

  // ECharts option 생성 (필요한 부분만 override)
  const option = useMemo(() => {
    return {
      ...defaultOption,
      xAxis: { ...defaultOption.xAxis, data: selectedData.map((e) => e.time) },
      series: [
        {
          ...defaultOption.series?.[0],
          data: selectedData.map((e) => [e.time, e.value]),
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'line' },
        formatter: (params: any[]) => {
          const point = params?.[0];
          const name = point?.name ?? '';
          const val = Array.isArray(point?.value)
            ? point.value[1]
            : (point?.value ?? '');
          return `
            <div class="body4 text-center">
              ${name}<br/>
              ${val}
            </div>
          `;
        },
      },
    } as any;
  }, [selectedData]);

  // 리사이즈 대응
  useEffect(() => {
    const handleResize = () => {
      chartRef.current?.getEchartsInstance().resize();
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleDropdownChange = useCallback((value: string) => {
    setSelectedValue(String(value));
  }, []);

  return (
    <div className={className ?? ''}>
      {/* 헤더 */}
      <H2Title className="f-c-b">
        {title}
        <Dropdown
          placeholder={t(selectedKey)}
          className="!w-[200px]"
          options={DROPDOWN_OPTIONS.map((opt) => ({
            key: t(opt.key),
            value: opt.value,
          }))}
          onChange={(value) => handleDropdownChange(value as string)}
        />
      </H2Title>

      {/* 차트 */}
      <div>
        <ECharts
          ref={chartRef}
          option={option}
          style={{ width: '100%', height: `${CHART_HEIGHT}px` }}
          opts={{ renderer: 'svg' }}
          notMerge
          lazyUpdate
        />
      </div>
    </div>
  );
};

export default LineGraph;
