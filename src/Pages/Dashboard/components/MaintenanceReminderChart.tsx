import { useTranslation } from 'react-i18next';
import React, { useEffect, useMemo, useRef } from 'react';
import ECharts from 'echarts-for-react';
import { CircleGraphOption } from '@/Common/constants/GraphOptions.ts';
import H2Title from '@/Common/Components/common/H2Title';

export interface MaintenanceReminderChartProps {
  title: string;
  data?: Array<{ name: string; value: number }>;
  className?: string;
}

const CHART_SIZE = 180;

const MaintenanceReminderChart: React.FC<MaintenanceReminderChartProps> = ({
  title,
  data = [],
  className,
}) => {
  const { t } = useTranslation();
  const chartRef = useRef<ECharts>(null);

  // 안전 가드: 음수/NaN 방지
  const safeData = useMemo(
    () =>
      data.map((d) => ({
        name: d.name ?? '',
        value: Math.max(0, Number(d.value) || 0),
      })),
    [data],
  );

  const { totalCount, label0, label1, label2 } = useMemo(() => {
    const total = safeData.reduce((sum, item) => sum + item.value, 0);
    return {
      totalCount: total,
      label0: safeData[0]?.value || 0, // Replaced
      label1: safeData[1]?.value || 0, // Overdue
      label2: safeData[2]?.value || 0, // DueSoon
    };
  }, [safeData]);

  const chartOption = useMemo(
    () => ({
      ...CircleGraphOption,
      series: [
        {
          ...CircleGraphOption.series[0],
          data: safeData.map((item) => ({ ...item, name: '' })), // 내부 라벨 숨김
        },
      ],
      title: {
        ...CircleGraphOption.title,
        text: String(totalCount),
      },
    }),
    [safeData, totalCount],
  );

  const rows = useMemo(
    () => [
      { color: 'bg-semantic-3', label: t('DueSoon'), value: label2 },
      { color: 'bg-semantic-1', label: t('Overdue'), value: label1 },
      { color: 'bg-semantic-2', label: t('Replaced'), value: label0 },
    ],
    [label0, label1, label2, t],
  );

  useEffect(() => {
    const handleResize = () => chartRef.current?.getEchartsInstance().resize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className={className ?? ''}>
      <H2Title>{title}</H2Title>
      <div className="p-[30px] f-c-s gap-[30px] [&>div]:[&>div]:f-c-b [&>div]:[&>div]:gap-2 [&_span]:f-c [&_span]:gap-2 [&_span]:caption3">
        <div>
          <ECharts
            ref={chartRef}
            style={{ height: `${CHART_SIZE}px`, width: `${CHART_SIZE}px` }}
            option={chartOption}
            opts={{ renderer: 'svg' }}
            notMerge
            lazyUpdate
          />
        </div>
        <div className="space-y-[10px]">
          {rows.map((row, idx) => (
            <div key={idx}>
              <span>
                <em className={`w-3 h-3 block ${row.color} rounded-full`} />
                {row.label}
              </span>
              <span>
                {row.value} {t('Cases')}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default MaintenanceReminderChart;
