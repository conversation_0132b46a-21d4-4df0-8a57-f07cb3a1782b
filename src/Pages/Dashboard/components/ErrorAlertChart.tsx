import { useTranslation } from 'react-i18next';
import { useEffect, useRef } from 'react';
import ECharts from 'echarts-for-react';
import { CircleGraphOption } from '@/Common/constants/GraphOptions.ts';
import H2Title from '@/Common/Components/common/H2Title';

export interface ErrorAlertChartProps {
  title: string;
  data: Array<{ name: string; value: number }> | undefined;
  className?: string;
}

const ErrorAlertChart = ({
  title,
  data = [],
  className,
}: ErrorAlertChartProps) => {
  const { t } = useTranslation();
  const chartRef = useRef<ECharts>(null);

  const totalCount = data.reduce((sum, item) => sum + item.value, 0);
  const label0Data = data[0]?.value || 0;
  const label1Data = data[1]?.value || 0;
  const label2Data = data[2]?.value || 0;

  const chartOption = {
    ...CircleGraphOption,
    series: [
      {
        ...CircleGraphOption.series[0],
        data: data.map((item) => ({
          ...item,
          name: '',
        })),
      },
    ],
    title: {
      ...CircleGraphOption.title,
      text: totalCount.toString(),
    },
  };

  const rows = [
    {
      color: 'bg-semantic-3',
      label: t('Pending'),
      value: label2Data,
    },
    {
      color: 'bg-semantic-1',
      label: t('InProgress'),
      value: label1Data,
    },
    {
      color: 'bg-semantic-2',
      label: t('Resolved'),
      value: label0Data,
    },
  ];

  useEffect(() => {
    const handleResize = () => {
      chartRef.current?.getEchartsInstance().resize();
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className={`${className}`}>
      <H2Title>{title}</H2Title>
      <div className="p-[30px] f-c-s gap-[30px] [&>div]:[&>div]:f-c-b [&>div]:[&>div]:gap-2 [&_span]:f-c [&_span]:gap-2 [&_span]:caption3">
        <div>
          <ECharts
            ref={chartRef}
            style={{ height: '180px', width: '180px' }}
            option={chartOption}
            opts={{ renderer: 'svg' }}
            notMerge={true}
            lazyUpdate={true}
          />
        </div>
        <div className="space-y-[10px]">
          {rows.map((row, idx) => (
            <div key={idx}>
              <span>
                <em className={`w-3 h-3 block ${row.color} rounded-full`} />
                {row.label}
              </span>
              <span>
                {row.value} {t('Cases')}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ErrorAlertChart;
