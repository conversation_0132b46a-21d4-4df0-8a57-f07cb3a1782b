import { useTranslation } from 'react-i18next';
import React from 'react';
import refueling from '@/assets/images/ic/36/refueling_count.svg';
import fuel from '@/assets/images/ic/36/fuel_consumption.svg';

export interface FuelEfficiencyCardProps {
  mainSection:
    | {
        value: number;
        comparison: {
          value: string;
          isIncrease: boolean;
        };
      }
    | undefined;
  subSection:
    | {
        value: number;
        comparison: {
          value: string;
          isIncrease: boolean;
        };
      }
    | undefined;
  mainUnit: string;
  subUnit: string;
  change?: boolean;
  className?: string;
}

/**
 * 충전 및 연비 정보를 보여주는 카드 컴포넌트
 */
const FuelEfficiencyCard: React.FC<FuelEfficiencyCardProps> = ({
  mainSection = {
    value: 0,
    comparison: {
      value: '',
      isIncrease: false,
    },
  },
  subSection = {
    value: 0,
    comparison: {
      value: '',
      isIncrease: false,
    },
  },
  mainUnit,
  subUnit,
  className,
}) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';

  return (
    <div
      className={`${className} p-5 [&>div]:border-b [&>div:last-child]:border-0 [&>div]:border-gray-6 [&_h2]:subtitle3`}
    >
      <div className="pb-6 space-y-[35px]">
        {/* 메인 타이틀 + 아이콘 */}
        <div className="f-c-b gap-2">
          <h2>{t('RefuelingCount')}</h2>
          <img src={refueling} alt={t('alarm')} />
        </div>
        {/* 메인 값 + 비교 지표 */}
        <div className="f-e-b">
          <div className="f-c-c gap-2">
            <span className="subtitle1">{mainSection.value}</span>
            <span className="body1">{mainUnit}</span>
          </div>
          {isEnglish ? (
            <div className="pb-2 f-c gap-[6px]">
              <div
                className={`w-fit px-[7px] rounded ${
                  mainSection.comparison.isIncrease
                    ? 'bg-semantic-1-1 [&>span]:text-semantic-1'
                    : 'bg-semantic-4-1 [&>span]:text-semantic-4'
                }`}
              >
                <span>{mainSection.comparison.isIncrease ? '+' : '-'}</span>
                <span>{mainSection.comparison.value}</span>
                <span>
                  {mainSection.comparison.isIncrease ? t('Up') : t('Down')}
                </span>
              </div>
              <div className="caption4 text-gray-10">{t('VsPerviousDay')}</div>
            </div>
          ) : (
            <div className="pb-2 f-c gap-[6px]">
              <div className="caption4 text-gray-10">{t('VsPerviousDay')}</div>
              <div
                className={`w-fit px-[7px] rounded ${
                  mainSection.comparison.isIncrease
                    ? 'bg-semantic-1-1 [&>span]:text-semantic-1'
                    : 'bg-semantic-4-1 [&>span]:text-semantic-4'
                }`}
              >
                <span>{mainSection.comparison.isIncrease ? '+' : '-'}</span>
                <span>{mainSection.comparison.value}</span>
                <span>
                  {mainSection.comparison.isIncrease ? t('Up') : t('Down')}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="pt-6 space-y-[35px]">
        {/* 서브 타이틀 */}
        <div className="f-c-b gap-2">
          <h2>{t('FuelConsumption')}</h2>
          <img src={fuel} alt={t('alarm')} />
        </div>
        {/* 서브 값 + 비교 지표 */}
        <div className="f-e-b">
          <div className="f-c-c gap-2">
            <span className="subtitle1">{subSection.value}</span>
            <span className="body1">{subUnit}</span>
          </div>
          {isEnglish ? (
            <div className="pb-2 f-c gap-[6px]">
              <div
                className={`w-fit px-[7px] rounded ${
                  subSection.comparison.isIncrease
                    ? 'bg-semantic-1-1 [&>span]:text-semantic-1'
                    : 'bg-semantic-4-1 [&>span]:text-semantic-4'
                }`}
              >
                <span>{subSection.comparison.isIncrease ? '+' : '-'}</span>
                <span>{subSection.comparison.value}</span>
                <span>
                  {subSection.comparison.isIncrease ? t('Up') : t('Down')}
                </span>
              </div>
              <div className="caption4 text-gray-10">{t('VsPerviousDay')}</div>
            </div>
          ) : (
            <div className="pb-2 f-c gap-[6px]">
              <div className="caption4 text-gray-10">{t('VsPerviousDay')}</div>
              <div
                className={`w-fit px-[7px] rounded ${
                  subSection.comparison.isIncrease
                    ? 'bg-semantic-1-1 [&>span]:text-semantic-1'
                    : 'bg-semantic-4-1 [&>span]:text-semantic-4'
                }`}
              >
                <span>{subSection.comparison.isIncrease ? '+' : '-'}</span>
                <span>{subSection.comparison.value}</span>
                <span>
                  {subSection.comparison.isIncrease ? t('Up') : t('Down')}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FuelEfficiencyCard;
