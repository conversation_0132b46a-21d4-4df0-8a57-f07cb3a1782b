import { useTranslation } from 'react-i18next';
import { useOverlay } from '@toss/use-overlay';
import FuelPrice from '@/Common/Popup/FuelPrice.tsx';
import TemperaturePrice from '@/Common/Popup/TemperaturePrice.tsx';

const useDashboardPopup = () => {
  const { t } = useTranslation();

  const overlay = useOverlay();
  const openFuelSettingPopup = () => {
    overlay.open((props) => {
      return (
        <FuelPrice
          title={t('FuelUnitPriceSettings')}
          buttonText={t('Cancel')}
          secondButtonText={t('ConfirmS')}
          onClose={function (): void {
            props.close();
          }}
          onConfirm={function (): void {
            props.close();
          }}
          isOpen={props.isOpen}
        />
      );
    });
  };
  const openTemperatureSettingPopup = () => {
    overlay.open((props) => {
      return (
        <TemperaturePrice
          title={t('SetTemperatureCriteria')}
          buttonText={t('Cancel')}
          secondButtonText={t('ConfirmS')}
          onClose={function (): void {
            props.close();
          }}
          onConfirm={function (): void {
            props.close();
          }}
          isOpen={props.isOpen}
        />
      );
    });
  };

  return {
    openFuelSettingPopup,
    openTemperatureSettingPopup,
  };
};
export default useDashboardPopup;
