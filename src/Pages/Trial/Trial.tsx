import { useTranslation } from 'react-i18next';
import { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Input from '@/Common/Components/common/Input';
import PasswordRegex from '@/Common/Components/common/PasswordRegex';
import { Button } from '@/Common/Components/common/Button';
import mainLogoO from '@/assets/images/logo/mainLogoF.svg';
import {
  validateField,
  TrialFormValues,
} from '@/utils/regex/validatorsTrial.ts';
import { isPasswordValid } from '@/utils/regex/validators.ts';
import { validatePassword } from '@/utils/regex/regex';

const Trial = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [values, setValues] = useState<TrialFormValues>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirm: '',
  });

  const [touched, setTouched] = useState<
    Record<keyof TrialFormValues, boolean>
  >({
    firstName: false,
    lastName: false,
    email: false,
    password: false,
    confirm: false,
  });

  const setField = <K extends keyof TrialFormValues>(key: K, v: string) =>
    setValues((prev) => ({ ...prev, [key]: v }));

  const blurTouch = (key: keyof TrialFormValues) =>
    setTouched((prev) => ({ ...prev, [key]: true }));

  const inputError = (key: keyof TrialFormValues) =>
    touched[key] ? validateField(key, values, t) : '';

  // 상세 규칙 결과 (아이콘 색상/버튼 활성화에 사용)
  const detail = useMemo(
    () => validatePassword(values.password),
    [values.password],
  );
  const { lengthOk, atLeast3Ok, tripleSeqOk } = detail;

  // 최종 비밀번호 유효성
  const pwValid = isPasswordValid(values.password);
  const same = values.password === values.confirm;

  // 이름/이메일만 개별 검증
  const nonPwValid = ['firstName', 'lastName', 'email'].every(
    (k) => validateField(k as keyof TrialFormValues, values, t) === '',
  );

  const finalValid = nonPwValid && pwValid && same;

  const onSubmit = () => {
    setTouched({
      firstName: true,
      lastName: true,
      email: true,
      password: true,
      confirm: true,
    });

    if (!finalValid) return;

    navigate('/trial/success');
    console.log('submit', values);
  };

  // 비밀번호/확인 에러 메시지
  const pwError = touched.password && !pwValid ? t('') : '';

  const confirmError = touched.confirm && !same ? t('PasswordsDoNotMatch') : '';

  return (
    <section className="py-10 px-20 bg-white rounded-[20px]">
      {/* 헤더 */}
      <article className="f-c-c flex-col">
        <img src={mainLogoO} alt="logo" />
        <h2 className="mt-[10px] subtitle3">{t('CreateAccount')}</h2>
        <p className="mt-1 caption1">
          {t('PleaseCreateAnAccountToStartYourTrial')}
        </p>
      </article>

      <div className="divider w-[calc(100%+160px)] my-5 ml-[-80px]" />

      {/* 폼 */}
      <article className="space-y-3 [&>div]:f-c [&>div]:gap-5 [&_h3]:w-[150px] [&_h3]:flex-shrink-0 [&_h3]:subtitle4 [&_input]:w-[420px]">
        <div>
          <h3>{t('FirstName')}</h3>
          <Input
            placeholder={t('FirstName')}
            value={values.firstName}
            onChange={(e) => setField('firstName', e.target.value)}
            onBlur={() => blurTouch('firstName')}
            error={inputError('firstName')}
          />
        </div>

        <div>
          <h3>{t('LastName')}</h3>
          <Input
            placeholder={t('LastName')}
            value={values.lastName}
            onChange={(e) => setField('lastName', e.target.value)}
            onBlur={() => blurTouch('lastName')}
            error={inputError('lastName')}
          />
        </div>

        <div>
          <h3>{t('Email')}</h3>
          <Input
            placeholder={t('Email')}
            value={values.email}
            onChange={(e) => setField('email', e.target.value)}
            onBlur={() => blurTouch('email')}
            error={inputError('email')}
          />
        </div>

        <div>
          <h3>{t('Password')}</h3>
          <Input
            placeholder={t('Password')}
            type="password"
            value={values.password}
            onChange={(e) => setField('password', e.target.value)}
            onBlur={() => blurTouch('password')}
            error={pwError}
          />
        </div>

        <div>
          <h3>{t('PasswordConfirm')}</h3>
          <Input
            placeholder={t('PasswordConfirm')}
            type="password"
            value={values.confirm}
            onChange={(e) => setField('confirm', e.target.value)}
            onBlur={() => blurTouch('confirm')}
            error={confirmError}
            // 두 필드 모두 값이 있을 때만 매칭 성공 표시
            success={
              values.password && values.confirm && same
                ? t('PasswordsMatch')
                : ''
            }
          />
        </div>

        <PasswordRegex
          status={{
            lengthOk,
            atLeast3Ok,
            tripleSeqOk,
          }}
        />
      </article>

      {/* 하단 */}
      <article className="mt-6 f-c-c flex-col gap-3">
        <p className="body2 text-gray-8">
          {t('AlreadyHaveAnAccount')}
          <a href="/login" className="ml-[6px] text-semantic-2">
            {t('Login')}
          </a>
        </p>
        <Button
          variant="bt_primary"
          label={t('Next')}
          className="w-[460px]"
          onClick={onSubmit}
          disabled={!finalValid}
        />
      </article>
    </section>
  );
};

export default Trial;
