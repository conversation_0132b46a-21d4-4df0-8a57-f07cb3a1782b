import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/Common/Components/common/Button';
import mainLogoO from '@/assets/images/logo/mainLogoF.svg';

const TrialSuccess = () => {
  const { t } = useTranslation();

  const navigate = useNavigate();

  return (
    <section className="py-10 px-8 bg-white rounded-[20px]">
      <article className="f-c-c flex-col">
        <img src={mainLogoO} alt="logo" />
        <h2 className="mt-[30px] subtitle3">{t('AccountVerification')}</h2>
      </article>

      <div className="divider w-[calc(100%+64px)] mt-7 mb-[70px] ml-[-32px]" />

      <article className="f-c-c flex-col text-center">
        <h3 className="mb-10 body2 whitespace-pre">
          {t(
            'AVerificationEmailHasNeenSentToTheAddressYouProvidedPleaseClickTheVerifyEmailButtonBelowToCompleteYourRegistration',
          )}
        </h3>

        <Button variant={'bt_primary'} label={t('VerifyEmail')} />

        <p className="mt-3 body4">
          {t('DidntReceiveTheEmail')}
          <a href="" className="ml-1 text-semantic-2">
            {t('ResendEmail')}
          </a>
        </p>

        <p className="mt-[135px] caption3 text-gray-8">
          {t('COPYRIGHT2025CARTAMobilityConfidentialProprietary')}
        </p>
      </article>
    </section>
  );
};

export default TrialSuccess;
