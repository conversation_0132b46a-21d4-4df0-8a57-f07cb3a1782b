import { useTranslation } from 'react-i18next';
import SearchLabel from '@/Common/Components/layout/SearchLabel';

export default function TsgPage3() {
  const { t } = useTranslation();

  return (
    <div className={'space-y-5'}>
      <div className={'space-y-5'}>
        <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
          {t('CheckingWay')}
        </SearchLabel>
        <div>
          <SearchLabel className={'block'}>Key on → 모니터 조작</SearchLabel>
          <SearchLabel className={'block'}>
            MENU → 상태진단 → 고장 진단 → 현재 고장 진단 - EPS → 충전기
          </SearchLabel>
        </div>
      </div>
      <div className="h-px bg-[#cccccc] relative w-full" />
      <div className={'space-y-5'}>
        <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
          {t('CheckW')}
        </SearchLabel>
        <SearchLabel className={'block'}>작성 정보</SearchLabel>
      </div>
      <div className="h-px bg-[#cccccc] relative w-full" />
      <div className={'space-y-5'}>
        <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
          {t('Judgement')}
        </SearchLabel>
        <SearchLabel className={'block'}>
          현재 고장 진단 - EPS 화면에 HCESPN 8083 / FMI 9이 활성화되어 있습니까?
        </SearchLabel>
      </div>
      <div className="h-px bg-[#cccccc] relative w-full" />
      <div className={'space-y-5'}>
        <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
          {t('YesNextProcedure')}
        </SearchLabel>
        <SearchLabel className={'block'}>{`'Step 2-1' 이동`}</SearchLabel>
      </div>
      <div className="h-px bg-[#cccccc] relative w-full" />
      <div className={'space-y-5'}>
        <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
          {t('NoNextProcedure')}
        </SearchLabel>
        <SearchLabel className={'block'}>{`'Step 1-2' 이동`}</SearchLabel>
      </div>
    </div>
  );
}
