import { useTranslation } from 'react-i18next';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import SearchLabel from '@/Common/Components/layout/SearchLabel';

const TsgPage0 = () => {
  const { t } = useTranslation();

  const reason =
    ' FIRMWARE NOT VALID\n' +
    '- OBC가 전압, 전류 또는 어플리케이션 유형에 적합하지 않은 Firmware로 업데이트 되었을 경우\n' +
    '- Firmware 업데이트 후 J1772/EN6151-1 프로토콜이 활성화되었지만 로직 보드의 보조 마이크로 컨트롤러가 업데이트 되지 않았을 때 기본 마이크로컨트롤러의  펌웨어에서 알람 발생';
  return (
    <div className={'space-y-5'}>
      <div className={'space-y-5'}>
        <SearchLabel>{t('StandardInformation')}</SearchLabel>
        <SearchItemContainer className={'justify-start  min-h-11'}>
          <SearchItemContainer className={'gap-5'}>
            {/* <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
              작성 정보
            </SearchLabel> */}
            <SearchItemContainer className={'gap-5'}>
              <SearchItemContainer className={'gap-5'}>
                <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
                  {t('RegisterW')}
                </SearchLabel>
                <SearchLabel>임관리</SearchLabel>
              </SearchItemContainer>
              <SearchItemContainer className={'gap-5'}>
                <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
                  {t('Approver')}
                </SearchLabel>
                <SearchLabel>-</SearchLabel>
              </SearchItemContainer>
              <SearchItemContainer className={'gap-5'}>
                <SearchLabel className={'text-[#7b7b7b]'}>
                  {t('DateW')}
                </SearchLabel>
                <SearchLabel>2024-10-22 10:07:06</SearchLabel>
              </SearchItemContainer>
              <SearchItemContainer className={'gap-5'}>
                <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
                  {t('StatusD')}
                </SearchLabel>
                <SearchLabel>승인 완료</SearchLabel>
              </SearchItemContainer>
            </SearchItemContainer>
          </SearchItemContainer>
        </SearchItemContainer>
        <SearchItemContainer className={'justify-start  min-h-11'}>
          <SearchItemContainer className={'gap-5'}>
            <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
              {t('TSGTitle')}
            </SearchLabel>
            <SearchLabel>FIRMWARE NOT VALID</SearchLabel>
          </SearchItemContainer>
        </SearchItemContainer>
        <SearchItemContainer className={'justify-start  min-h-11'}>
          <SearchItemContainer className={'gap-5'}>
            <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
              {t('ModelName')}
            </SearchLabel>
            <SearchLabel>미니전기굴착기(HX19E)</SearchLabel>
          </SearchItemContainer>
        </SearchItemContainer>
        <SearchItemContainer className={'justify-start  min-h-11'}>
          <SearchItemContainer className={'gap-5'}>
            <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
              {t('Code')}
            </SearchLabel>
            <SearchItemContainer className={'gap-5'}>
              <SearchItemContainer className={'gap-5'}>
                <SearchLabel className={'text-[#7b7b7b]'}>
                  {t('HCESPNN')}
                </SearchLabel>
                <SearchLabel>519368</SearchLabel>
              </SearchItemContainer>
              <SearchItemContainer className={'gap-5'}>
                <SearchLabel className={'text-[#7b7b7b]'}>
                  {t('FMI')}
                </SearchLabel>
                <SearchLabel>31(A59)</SearchLabel>
              </SearchItemContainer>
              <SearchItemContainer className={'gap-5'}>
                <SearchLabel className={'text-[#7b7b7b]'}>
                  {t('Level')}
                </SearchLabel>
                <SearchLabel>Warning</SearchLabel>
              </SearchItemContainer>
              <SearchItemContainer className={'gap-5'}>
                <SearchLabel className={'text-[#7b7b7b]'}>
                  {t('Type')}
                </SearchLabel>
                <SearchLabel>OBC</SearchLabel>
              </SearchItemContainer>
            </SearchItemContainer>
          </SearchItemContainer>
        </SearchItemContainer>
        <SearchItemContainer className={'justify-start  min-h-11'}>
          <SearchItemContainer className={'gap-5 items-start flex-nowrap'}>
            <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
              {t('Reason')}
            </SearchLabel>
            <div>
              {reason?.split('\n').map((line) => (
                <SearchLabel key={line}>
                  {/*<SearchLabel>{line}</SearchLabel>*/}
                  {line}
                  <br />
                </SearchLabel>
              ))}
            </div>
          </SearchItemContainer>
        </SearchItemContainer>
        <SearchItemContainer className={'justify-start  min-h-11'}>
          <SearchItemContainer className={'gap-5 items-start flex-nowrap'}>
            <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
              {t('Result')}
            </SearchLabel>
            <SearchLabel>충전 불가</SearchLabel>
          </SearchItemContainer>
        </SearchItemContainer>
      </div>
      <div className="h-px bg-[#cccccc] relative w-full" />
      <div className={'space-y-5'}>
        <SearchLabel className={'text-[#7b7b7b] w-[78px]'}>
          {t('Circuit')}
        </SearchLabel>
        <div className="justify-start items-center gap-4 flex flex-wrap">
          <img
            className="w-[490px] h-[490px]"
            src="https://placehold.co/490x490"
          />
          <div className="w-[490px] h-[490px] bg-white" />
          <div className="w-[490px] h-[490px] bg-white" />
        </div>
      </div>
      <div className="text-xl font-semibold">
        OBC #1 MASTER(L)의 B+ 전원(CN-910 #1)은 BMS의 B+ 전원(CN-911 #6)과
        연결됨
        <br />
        OBC #1 MASTER(L)의 IG 전원(CN-910 #3)은 BMS의 IG 전원(CN-911 #5)과
        연결됨
        <br />
        OBC #1 MASTER(L)의 접지(CN-920 #9, CN-910 #2)는 차체에 Ground되어 있음
        <br />
        OBC #1 MASTER(L)의 CAN H 신호(CN-910 #7)은 OBC #2 SLAVE(R)의 CAN H
        신호(CN-919 #1) 및 BMS CAN H 신호(CN-911 #3)로 연결됨
        <br />
        OBC #1 MASTER(L)의 CAN L 신호(CN-910 #8)은 OBC #2 SLAVE(R)의 CAN L
        신호(CN-919 #2) 및 BMS CAN L 신호(CN-911 #9)로 연결됨
      </div>
    </div>
  );
};

export default TsgPage0;
