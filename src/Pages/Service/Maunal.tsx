import { useTranslation } from 'react-i18next';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import Service_Manual from '@/assets/images/ic/36/service_manual.png';
import User_Manual from '@/assets/images/ic/36/user_manual.png';
import { Button } from '@/Common/Components/common/Button';

const Manual = () => {
  const { t } = useTranslation();

  return (
    <CustomFrame name={t('ManualDownload')}>
      <div className={'space-y-6'}>
        <div className="h-[180px] bg-white rounded-lg p-9 flex justify-start items-center gap-20">
          <div className="w-[106px] h-[106px] bg-[#4c8af7]/10 rounded-[20px] p-4">
            <img src={Service_Manual} alt={'Service_Manual'} />
          </div>
          <div className=" relative">
            <div className="text-[#4c8af7] text-[32px] font-bold leading-[41.60px]">
              {t('DriverManual')}
            </div>
            <div className=" text-[#7b7b7b] text-xl font-medium leading-7">
              {t('TheManualProvidedForTheOperatorIncludes')}
              <br />
              {t('UsageInstructionsSafetyWarningsAndMaintenanceInformation')}
            </div>
          </div>
          <Button
            variant={'bt_primary'}
            label={'Download'}
            className={'w-80 ml-auto'}
          />
        </div>
        <div className="h-[180px] bg-white rounded-lg p-9 flex justify-start items-center gap-20">
          <div className="w-[106px] h-[106px] bg-[#4c8af7]/10 rounded-[20px] p-4">
            <img src={User_Manual} alt={'Service_Manual'} />
          </div>
          <div className="relative">
            <div className="text-[#4c8af7] text-[32px] font-bold leading-[41.60px]">
              {t('ServiceManual')}
            </div>
            <div className="text-[#7b7b7b] text-xl font-medium leading-7">
              {t('TheManualProvidedForTheOperatorIncludes2')}
              <br />
              {t('UsageInstructionsSafetywarningsAndMaintenanceInformation2')}
            </div>
          </div>
          <Button
            variant={'bt_primary'}
            label={'Download'}
            className={'w-80 ml-auto'}
          />
        </div>

        <div className="h-[180px] p-4">
          <div>
            <span className="text-[#7b7b7b] text-2xl font-bold">
              {t('WarningNotice')}
            </span>
            <span className="text-[#ff0000] text-2xl font-bold">*</span>
          </div>
          <div className="mt-2 text-[#7b7b7b] text-xl font-medium leading-7 [&_em]:inline [&_em]:w-[6px] [&_em]:mr-3 [&_em]:text-[#7b7b7b] ">
            <em>1.</em>
            {t(
              'ThisManualIsIntendedForTheSoleAndExclusiveUseOfYourselfAndContainsInformationThatIscCnfidential',
            )}
            <br />
            <em>2.</em>
            {t(
              'YouQAreHerebyNotifiedThatAnyUseiDsseminationDistributionOrcopyingOfTheManualOrTheInformationContainedThereinIsSTRICTLYPROHIBITED',
            )}
          </div>
        </div>
      </div>
    </CustomFrame>
  );
};

export default Manual;
