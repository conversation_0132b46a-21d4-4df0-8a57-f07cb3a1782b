import { useTranslation } from 'react-i18next';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Tabs } from '@radix-ui/themes';
import TsgNotice from '@/Pages/Service/components/TSGNotice.tsx';

const TSG = () => {
  const { t } = useTranslation();

  return (
    <CustomFrame name={t('TSG')}>
      <Tabs.Root defaultValue={t('TSGBoard')}>
        <Tabs.List className={'tab-design'}>
          <Tabs.Trigger
            value={t('TSGCreate')}
            disabled
            className="cursor-not-allowed opacity-50"
            onClick={(e) => e.preventDefault()}
          >
            <span>{t('TSGCreate')}</span>
          </Tabs.Trigger>

          <Tabs.Trigger
            value={t('TSGManage')}
            disabled
            className="cursor-not-allowed opacity-50"
            onClick={(e) => e.preventDefault()}
          >
            <span>{t('TSGManage')}</span>
          </Tabs.Trigger>

          <Tabs.Trigger value={t('TSGBoard')} className="cursor-pointer">
            <span>{t('TSGBoard')}</span>
          </Tabs.Trigger>

          <Tabs.Trigger
            value={t('ImageBox')}
            disabled
            className="cursor-not-allowed opacity-50"
            onClick={(e) => e.preventDefault()}
          >
            <span>{t('ImageBox')}</span>
          </Tabs.Trigger>
        </Tabs.List>
        <div className={'px-10 py-6'}>
          <TsgNotice />
        </div>
      </Tabs.Root>
    </CustomFrame>
  );
};

export default TSG;
