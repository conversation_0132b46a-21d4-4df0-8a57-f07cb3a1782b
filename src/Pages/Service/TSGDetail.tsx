import { useTranslation } from 'react-i18next';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import { Button } from '@/Common/Components/common/Button';
import right from '@/assets/images/etc/right 01_B.png';
import { useState } from 'react';
import TsgPage0 from '@/Pages/Service/components/TsgPage0.tsx';
import TsgPage1 from '@/Pages/Service/components/TsgPage1.tsx';
import TsgPage2 from '@/Pages/Service/components/TsgPage2.tsx';
import TsgPage3 from '@/Pages/Service/components/TsgPage3.tsx';

const TsgDetail = () => {
  const { t } = useTranslation();

  const [page, setPage] = useState(0);
  const Contents = () => {
    switch (page) {
      case 0:
        return <TsgPage0 />;
      case 1:
        return <TsgPage1 />;
      case 2:
        return <TsgPage2 />;
      case 3:
        return <TsgPage3 />;
      default:
        return <TsgPage0 />;
    }
  };
  return (
    <CustomFrame name={'TSG'} back={true}>
      <div className={'space-y-6 pb-6'}>
        <div className="h-px bg-[#cccccc] relative w-full">
          <Button
            variant={'bt_primary'}
            label={'aaa'}
            className={'absolute -top-10 right-32'}
          >
            {' '}
            {t('RequestA')}
          </Button>
          <Button
            variant={'bt_primary'}
            label={'Delete'}
            className={'absolute -top-10 right-16'}
          />
          <Button
            variant={'bt_primary'}
            label={'Edit'}
            className={'absolute -top-10 right-0'}
          />
        </div>
        <Contents />
        <div className="h-px bg-[#cccccc] relative w-full" />
        <div className="w-full text-center">
          <Button
            variant={'bt_primary'}
            label={''}
            onClick={() => setPage(page + 1)}
            className={'rounded-full gap-0 pl-6 pr-4 py-2.5 w-[100px]'}
          >
            {t('NextT')}
            <img src={right} alt={'right'} />
          </Button>
        </div>
      </div>
    </CustomFrame>
  );
};

export default TsgDetail;
