import { create } from 'zustand';
import * as randomDataGenerator from '@/helpers/randomDataGenerator';

/**
 * 장비 기본 정보 타입
 */
export interface EquipmentBasicInfo {
  model: string;
  serialNumber: string;
  equipmentId: string;
  driverName: string;
}

/**
 * 장비 상태 정보 타입
 */
export interface EquipmentStatusInfo {
  deliveryDate: string;
  servicePeriod: string;
  communicationStatus: {
    status: string;
    isOnline: boolean;
  };
}

/**
 * 장비 작업 상태 정보 타입
 */
export interface EquipmentWorkStatusInfo {
  date: string;
  workStatus: {
    status: string;
    isWorking: boolean;
  };
  operatingStartTime: string;
  operatingEndTime: string;
  totalOperatingHours: string;
  workingHoursChange: {
    text1: string;
    text2: string;
    isIncrease: boolean;
  };
}

/**
 * 배터리 정보 타입
 */
export interface BatteryInfo {
  level: number;
  capacity: string;
}

/**
 * 장비 데이터 스토어 상태 타입
 */
interface EquipmentDataState {
  basicInfo: EquipmentBasicInfo;
  statusInfo: EquipmentStatusInfo;
  workStatusInfo: EquipmentWorkStatusInfo;
  batteryInfo: BatteryInfo;
  generateRandomData: () => void;
}

/**
 * 랜덤 장비 데이터 생성 함수
 */
const generateRandomEquipmentData = () => {
  return {
    basicInfo: {
      model: randomDataGenerator.getRandomEquipmentModel(),
      serialNumber: randomDataGenerator.getRandomSerialNumber(),
      equipmentId: randomDataGenerator.getRandomEquipmentId(),
      driverName: randomDataGenerator.getRandomDriverName(),
    },
    statusInfo: {
      deliveryDate: randomDataGenerator.getRandomDateString(),
      servicePeriod: randomDataGenerator.getRandomServicePeriod(),
      communicationStatus: randomDataGenerator.getRandomCommunicationStatus(),
    },
    workStatusInfo: {
      date: new Date()
        .toLocaleDateString('ko-KR', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        })
        .replace(/\. /g, '.')
        .replace(/\.$/, ''),
      workStatus: randomDataGenerator.getRandomWorkStatus(),
      operatingStartTime: randomDataGenerator.getRandomTime(),
      operatingEndTime: randomDataGenerator.getRandomTime(),
      totalOperatingHours: randomDataGenerator.getRandomWorkingHours(),
      workingHoursChange: randomDataGenerator.getRandomWorkingHoursChange(),
    },
    batteryInfo: {
      level: randomDataGenerator.getRandomBatteryLevel(),
      capacity: randomDataGenerator.getRandomBatteryCapacity(),
    },
  };
};

/**
 * 장비 데이터 스토어
 */
export const useEquipmentDataStore = create<EquipmentDataState>((set) => {
  // 초기 데이터 생성
  const initialData = generateRandomEquipmentData();

  return {
    ...initialData,
    generateRandomData: () => {
      const newData = generateRandomEquipmentData();
      set(newData);
    },
  };
});
