import {
  CommonAuthentication<PERSON>pi,
  AdminCountryApi,
  AdminServiceCenterApi,
  AdminDealerApi,
  AdminDriverApi,
  AdminEquipmentApi,
  AdminEquipmentMonitoringApi,
  AdminFleetApi,
  AdminUserApi,
  CommonEnumApi,
  AdminItineraryDispatchApi,
} from './generated/api';

export const authenticationApi = new CommonAuthenticationApi();
export const countryApi = new AdminCountryApi();
export const serviceCenterApi = new AdminServiceCenterApi();
export const dealerApi = new AdminDealerApi();
export const driverApi = new AdminDriverApi();
export const equipmentApi = new AdminEquipmentApi();
export const equipmentMonitoringApi = new AdminEquipmentMonitoringApi();
export const fleetApi = new AdminFleetApi();
export const userApi = new AdminUserApi();
export const enumApi = new CommonEnumApi();
export const dispatchApi = new AdminItineraryDispatchApi();
