/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { PaymentHistoryResponseDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PaymentResponseDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PaymentUpdateDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { SubscriptionRequestDTO } from '../../../../src/api/generated/models';
/**
 * DriverPaymentApi - axios parameter creator
 * @export
 */
export const DriverPaymentApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 현재 구독과 관련된 모든 히스토리를 삭제합니다.  DB 에서 삭제되므로 일시 정지나 탈퇴는 PaymentStatus를 변경해야 합니다.
         * @summary 구독 삭제
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSubscription: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/payments`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 현재 구독 정보를 조회합니다.
         * @summary 현재 구독 정보 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCurrentSubscription: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/payments/current`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 전체 구독 히스토리를 조회합니다. 구독 시작, 구독 취소, 구독 정보 변경 히스토리를 조회합니다.
         * @summary 구독 히스토리 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSubscriptionHistory: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/payments/history`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 이미 구독이 있는 경우 해당 구독을 반환하고, 그렇지 않은 경우에만 새로운 구독을 생성합니다. 구독 시작 시 구독 상태를 지정해야 합니다.
         * @summary 구독 생성
         * @param {SubscriptionRequestDTO} subscriptionRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        startSubscription: async (subscriptionRequestDTO: SubscriptionRequestDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'subscriptionRequestDTO' is not null or undefined
            assertParamExists('startSubscription', 'subscriptionRequestDTO', subscriptionRequestDTO)
            const localVarPath = `/api/payments/subscribe`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(subscriptionRequestDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 구독 정보를 변경합니다. 업데이트하고자 하는 항목만 넣습니다. 업데이트 하지 않는 항목은 넣지 않거나 null 을 넣습니다. 구독 상태 변경이외의 값을 업데이트 합니다. 예를 들어 결제가 완료되면 구독 상태를 ACTIVE 로 변경,만료일(expirationDate )도 같이 업데이트 합니다.
         * @summary 구독 정보 변경
         * @param {PaymentUpdateDTO} paymentUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSubscription: async (paymentUpdateDTO: PaymentUpdateDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'paymentUpdateDTO' is not null or undefined
            assertParamExists('updateSubscription', 'paymentUpdateDTO', paymentUpdateDTO)
            const localVarPath = `/api/payments/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(paymentUpdateDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DriverPaymentApi - functional programming interface
 * @export
 */
export const DriverPaymentApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DriverPaymentApiAxiosParamCreator(configuration)
    return {
        /**
         * 현재 구독과 관련된 모든 히스토리를 삭제합니다.  DB 에서 삭제되므로 일시 정지나 탈퇴는 PaymentStatus를 변경해야 합니다.
         * @summary 구독 삭제
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteSubscription(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteSubscription(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverPaymentApi.deleteSubscription']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 현재 구독 정보를 조회합니다.
         * @summary 현재 구독 정보 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCurrentSubscription(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PaymentResponseDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCurrentSubscription(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverPaymentApi.getCurrentSubscription']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 전체 구독 히스토리를 조회합니다. 구독 시작, 구독 취소, 구독 정보 변경 히스토리를 조회합니다.
         * @summary 구독 히스토리 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSubscriptionHistory(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<PaymentHistoryResponseDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSubscriptionHistory(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverPaymentApi.getSubscriptionHistory']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 이미 구독이 있는 경우 해당 구독을 반환하고, 그렇지 않은 경우에만 새로운 구독을 생성합니다. 구독 시작 시 구독 상태를 지정해야 합니다.
         * @summary 구독 생성
         * @param {SubscriptionRequestDTO} subscriptionRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async startSubscription(subscriptionRequestDTO: SubscriptionRequestDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PaymentResponseDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.startSubscription(subscriptionRequestDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverPaymentApi.startSubscription']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 구독 정보를 변경합니다. 업데이트하고자 하는 항목만 넣습니다. 업데이트 하지 않는 항목은 넣지 않거나 null 을 넣습니다. 구독 상태 변경이외의 값을 업데이트 합니다. 예를 들어 결제가 완료되면 구독 상태를 ACTIVE 로 변경,만료일(expirationDate )도 같이 업데이트 합니다.
         * @summary 구독 정보 변경
         * @param {PaymentUpdateDTO} paymentUpdateDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateSubscription(paymentUpdateDTO: PaymentUpdateDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PaymentResponseDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateSubscription(paymentUpdateDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverPaymentApi.updateSubscription']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DriverPaymentApi - factory interface
 * @export
 */
export const DriverPaymentApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DriverPaymentApiFp(configuration)
    return {
        /**
         * 현재 구독과 관련된 모든 히스토리를 삭제합니다.  DB 에서 삭제되므로 일시 정지나 탈퇴는 PaymentStatus를 변경해야 합니다.
         * @summary 구독 삭제
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteSubscription(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteSubscription(options).then((request) => request(axios, basePath));
        },
        /**
         * 현재 구독 정보를 조회합니다.
         * @summary 현재 구독 정보 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCurrentSubscription(options?: RawAxiosRequestConfig): AxiosPromise<PaymentResponseDTO> {
            return localVarFp.getCurrentSubscription(options).then((request) => request(axios, basePath));
        },
        /**
         * 전체 구독 히스토리를 조회합니다. 구독 시작, 구독 취소, 구독 정보 변경 히스토리를 조회합니다.
         * @summary 구독 히스토리 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSubscriptionHistory(options?: RawAxiosRequestConfig): AxiosPromise<Array<PaymentHistoryResponseDTO>> {
            return localVarFp.getSubscriptionHistory(options).then((request) => request(axios, basePath));
        },
        /**
         * 이미 구독이 있는 경우 해당 구독을 반환하고, 그렇지 않은 경우에만 새로운 구독을 생성합니다. 구독 시작 시 구독 상태를 지정해야 합니다.
         * @summary 구독 생성
         * @param {DriverPaymentApiStartSubscriptionRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        startSubscription(requestParameters: DriverPaymentApiStartSubscriptionRequest, options?: RawAxiosRequestConfig): AxiosPromise<PaymentResponseDTO> {
            return localVarFp.startSubscription(requestParameters.subscriptionRequestDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 구독 정보를 변경합니다. 업데이트하고자 하는 항목만 넣습니다. 업데이트 하지 않는 항목은 넣지 않거나 null 을 넣습니다. 구독 상태 변경이외의 값을 업데이트 합니다. 예를 들어 결제가 완료되면 구독 상태를 ACTIVE 로 변경,만료일(expirationDate )도 같이 업데이트 합니다.
         * @summary 구독 정보 변경
         * @param {DriverPaymentApiUpdateSubscriptionRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSubscription(requestParameters: DriverPaymentApiUpdateSubscriptionRequest, options?: RawAxiosRequestConfig): AxiosPromise<PaymentResponseDTO> {
            return localVarFp.updateSubscription(requestParameters.paymentUpdateDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for startSubscription operation in DriverPaymentApi.
 * @export
 * @interface DriverPaymentApiStartSubscriptionRequest
 */
export interface DriverPaymentApiStartSubscriptionRequest {
    /**
     * 
     * @type {SubscriptionRequestDTO}
     * @memberof DriverPaymentApiStartSubscription
     */
    readonly subscriptionRequestDTO: SubscriptionRequestDTO
}

/**
 * Request parameters for updateSubscription operation in DriverPaymentApi.
 * @export
 * @interface DriverPaymentApiUpdateSubscriptionRequest
 */
export interface DriverPaymentApiUpdateSubscriptionRequest {
    /**
     * 
     * @type {PaymentUpdateDTO}
     * @memberof DriverPaymentApiUpdateSubscription
     */
    readonly paymentUpdateDTO: PaymentUpdateDTO
}

/**
 * DriverPaymentApi - object-oriented interface
 * @export
 * @class DriverPaymentApi
 * @extends {BaseAPI}
 */
export class DriverPaymentApi extends BaseAPI {
    /**
     * 현재 구독과 관련된 모든 히스토리를 삭제합니다.  DB 에서 삭제되므로 일시 정지나 탈퇴는 PaymentStatus를 변경해야 합니다.
     * @summary 구독 삭제
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverPaymentApi
     */
    public deleteSubscription(options?: RawAxiosRequestConfig) {
        return DriverPaymentApiFp(this.configuration).deleteSubscription(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 현재 구독 정보를 조회합니다.
     * @summary 현재 구독 정보 조회
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverPaymentApi
     */
    public getCurrentSubscription(options?: RawAxiosRequestConfig) {
        return DriverPaymentApiFp(this.configuration).getCurrentSubscription(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 전체 구독 히스토리를 조회합니다. 구독 시작, 구독 취소, 구독 정보 변경 히스토리를 조회합니다.
     * @summary 구독 히스토리 조회
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverPaymentApi
     */
    public getSubscriptionHistory(options?: RawAxiosRequestConfig) {
        return DriverPaymentApiFp(this.configuration).getSubscriptionHistory(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 이미 구독이 있는 경우 해당 구독을 반환하고, 그렇지 않은 경우에만 새로운 구독을 생성합니다. 구독 시작 시 구독 상태를 지정해야 합니다.
     * @summary 구독 생성
     * @param {DriverPaymentApiStartSubscriptionRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverPaymentApi
     */
    public startSubscription(requestParameters: DriverPaymentApiStartSubscriptionRequest, options?: RawAxiosRequestConfig) {
        return DriverPaymentApiFp(this.configuration).startSubscription(requestParameters.subscriptionRequestDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 구독 정보를 변경합니다. 업데이트하고자 하는 항목만 넣습니다. 업데이트 하지 않는 항목은 넣지 않거나 null 을 넣습니다. 구독 상태 변경이외의 값을 업데이트 합니다. 예를 들어 결제가 완료되면 구독 상태를 ACTIVE 로 변경,만료일(expirationDate )도 같이 업데이트 합니다.
     * @summary 구독 정보 변경
     * @param {DriverPaymentApiUpdateSubscriptionRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverPaymentApi
     */
    public updateSubscription(requestParameters: DriverPaymentApiUpdateSubscriptionRequest, options?: RawAxiosRequestConfig) {
        return DriverPaymentApiFp(this.configuration).updateSubscription(requestParameters.paymentUpdateDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

