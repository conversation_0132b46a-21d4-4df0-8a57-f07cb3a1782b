/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { AdminItineraryDispatchCreateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { AdminItineraryDispatchDetailResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelAdminItineraryDispatchListItemResDTO } from '../../../../src/api/generated/models';
/**
 * AdminItineraryDispatchApi - axios parameter creator
 * @export
 */
export const AdminItineraryDispatchApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 배차를 생성합니다.
         * @summary 배차 생성
         * @param {AdminItineraryDispatchCreateReqDTO} adminItineraryDispatchCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminItineraryDispatch: async (adminItineraryDispatchCreateReqDTO: AdminItineraryDispatchCreateReqDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'adminItineraryDispatchCreateReqDTO' is not null or undefined
            assertParamExists('createAdminItineraryDispatch', 'adminItineraryDispatchCreateReqDTO', adminItineraryDispatchCreateReqDTO)
            const localVarPath = `/api/admin/itinerary-dispatch`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(adminItineraryDispatchCreateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 배차 상세정보를 조회합니다.
         * @summary 배차 상세정보 조회
         * @param {number} itineraryDispatchId 배차아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminItineraryDispatch: async (itineraryDispatchId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itineraryDispatchId' is not null or undefined
            assertParamExists('getAdminItineraryDispatch', 'itineraryDispatchId', itineraryDispatchId)
            const localVarPath = `/api/admin/itinerary-dispatch/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (itineraryDispatchId !== undefined) {
                localVarQueryParameter['itineraryDispatchId'] = itineraryDispatchId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 배차 목록을 조회합니다.
         * @summary 배차 목록 조회
         * @param {string} [driverName] 운전자명
         * @param {string} [plateNo] 차량번호
         * @param {Array<GetAdminItineraryDispatchPageItineraryStatusListEnum>} [itineraryStatusList] 운행상태
         * @param {string} [startScheduledDt] 운행일자 시작범위
         * @param {string} [endScheduledDt] 운행일자 끝범위
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminItineraryDispatchPage: async (driverName?: string, plateNo?: string, itineraryStatusList?: Array<GetAdminItineraryDispatchPageItineraryStatusListEnum>, startScheduledDt?: string, endScheduledDt?: string, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/admin/itinerary-dispatch/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (driverName !== undefined) {
                localVarQueryParameter['driverName'] = driverName;
            }

            if (plateNo !== undefined) {
                localVarQueryParameter['plateNo'] = plateNo;
            }

            if (itineraryStatusList) {
                localVarQueryParameter['itineraryStatusList'] = itineraryStatusList;
            }

            if (startScheduledDt !== undefined) {
                localVarQueryParameter['startScheduledDt'] = (startScheduledDt as any instanceof Date) ?
                    (startScheduledDt as any).toISOString().substring(0,10) :
                    startScheduledDt;
            }

            if (endScheduledDt !== undefined) {
                localVarQueryParameter['endScheduledDt'] = (endScheduledDt as any instanceof Date) ?
                    (endScheduledDt as any).toISOString().substring(0,10) :
                    endScheduledDt;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AdminItineraryDispatchApi - functional programming interface
 * @export
 */
export const AdminItineraryDispatchApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AdminItineraryDispatchApiAxiosParamCreator(configuration)
    return {
        /**
         * 배차를 생성합니다.
         * @summary 배차 생성
         * @param {AdminItineraryDispatchCreateReqDTO} adminItineraryDispatchCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createAdminItineraryDispatch(adminItineraryDispatchCreateReqDTO: AdminItineraryDispatchCreateReqDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createAdminItineraryDispatch(adminItineraryDispatchCreateReqDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminItineraryDispatchApi.createAdminItineraryDispatch']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 배차 상세정보를 조회합니다.
         * @summary 배차 상세정보 조회
         * @param {number} itineraryDispatchId 배차아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminItineraryDispatch(itineraryDispatchId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AdminItineraryDispatchDetailResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminItineraryDispatch(itineraryDispatchId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminItineraryDispatchApi.getAdminItineraryDispatch']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 배차 목록을 조회합니다.
         * @summary 배차 목록 조회
         * @param {string} [driverName] 운전자명
         * @param {string} [plateNo] 차량번호
         * @param {Array<GetAdminItineraryDispatchPageItineraryStatusListEnum>} [itineraryStatusList] 운행상태
         * @param {string} [startScheduledDt] 운행일자 시작범위
         * @param {string} [endScheduledDt] 운행일자 끝범위
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminItineraryDispatchPage(driverName?: string, plateNo?: string, itineraryStatusList?: Array<GetAdminItineraryDispatchPageItineraryStatusListEnum>, startScheduledDt?: string, endScheduledDt?: string, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminItineraryDispatchListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminItineraryDispatchPage(driverName, plateNo, itineraryStatusList, startScheduledDt, endScheduledDt, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminItineraryDispatchApi.getAdminItineraryDispatchPage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AdminItineraryDispatchApi - factory interface
 * @export
 */
export const AdminItineraryDispatchApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AdminItineraryDispatchApiFp(configuration)
    return {
        /**
         * 배차를 생성합니다.
         * @summary 배차 생성
         * @param {AdminItineraryDispatchApiCreateAdminItineraryDispatchRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminItineraryDispatch(requestParameters: AdminItineraryDispatchApiCreateAdminItineraryDispatchRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.createAdminItineraryDispatch(requestParameters.adminItineraryDispatchCreateReqDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 배차 상세정보를 조회합니다.
         * @summary 배차 상세정보 조회
         * @param {AdminItineraryDispatchApiGetAdminItineraryDispatchRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminItineraryDispatch(requestParameters: AdminItineraryDispatchApiGetAdminItineraryDispatchRequest, options?: RawAxiosRequestConfig): AxiosPromise<AdminItineraryDispatchDetailResDTO> {
            return localVarFp.getAdminItineraryDispatch(requestParameters.itineraryDispatchId, options).then((request) => request(axios, basePath));
        },
        /**
         * 배차 목록을 조회합니다.
         * @summary 배차 목록 조회
         * @param {AdminItineraryDispatchApiGetAdminItineraryDispatchPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminItineraryDispatchPage(requestParameters: AdminItineraryDispatchApiGetAdminItineraryDispatchPageRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminItineraryDispatchListItemResDTO> {
            return localVarFp.getAdminItineraryDispatchPage(requestParameters.driverName, requestParameters.plateNo, requestParameters.itineraryStatusList, requestParameters.startScheduledDt, requestParameters.endScheduledDt, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createAdminItineraryDispatch operation in AdminItineraryDispatchApi.
 * @export
 * @interface AdminItineraryDispatchApiCreateAdminItineraryDispatchRequest
 */
export interface AdminItineraryDispatchApiCreateAdminItineraryDispatchRequest {
    /**
     * 
     * @type {AdminItineraryDispatchCreateReqDTO}
     * @memberof AdminItineraryDispatchApiCreateAdminItineraryDispatch
     */
    readonly adminItineraryDispatchCreateReqDTO: AdminItineraryDispatchCreateReqDTO
}

/**
 * Request parameters for getAdminItineraryDispatch operation in AdminItineraryDispatchApi.
 * @export
 * @interface AdminItineraryDispatchApiGetAdminItineraryDispatchRequest
 */
export interface AdminItineraryDispatchApiGetAdminItineraryDispatchRequest {
    /**
     * 배차아이디
     * @type {number}
     * @memberof AdminItineraryDispatchApiGetAdminItineraryDispatch
     */
    readonly itineraryDispatchId: number
}

/**
 * Request parameters for getAdminItineraryDispatchPage operation in AdminItineraryDispatchApi.
 * @export
 * @interface AdminItineraryDispatchApiGetAdminItineraryDispatchPageRequest
 */
export interface AdminItineraryDispatchApiGetAdminItineraryDispatchPageRequest {
    /**
     * 운전자명
     * @type {string}
     * @memberof AdminItineraryDispatchApiGetAdminItineraryDispatchPage
     */
    readonly driverName?: string

    /**
     * 차량번호
     * @type {string}
     * @memberof AdminItineraryDispatchApiGetAdminItineraryDispatchPage
     */
    readonly plateNo?: string

    /**
     * 운행상태
     * @type {Array<'SCHEDULED' | 'RUNNING' | 'COMPLETED'>}
     * @memberof AdminItineraryDispatchApiGetAdminItineraryDispatchPage
     */
    readonly itineraryStatusList?: Array<GetAdminItineraryDispatchPageItineraryStatusListEnum>

    /**
     * 운행일자 시작범위
     * @type {string}
     * @memberof AdminItineraryDispatchApiGetAdminItineraryDispatchPage
     */
    readonly startScheduledDt?: string

    /**
     * 운행일자 끝범위
     * @type {string}
     * @memberof AdminItineraryDispatchApiGetAdminItineraryDispatchPage
     */
    readonly endScheduledDt?: string

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminItineraryDispatchApiGetAdminItineraryDispatchPage
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminItineraryDispatchApiGetAdminItineraryDispatchPage
     */
    readonly size?: number

    /**
     * 정렬 조건 (driverName 등)
     * @type {string}
     * @memberof AdminItineraryDispatchApiGetAdminItineraryDispatchPage
     */
    readonly sort?: string
}

/**
 * AdminItineraryDispatchApi - object-oriented interface
 * @export
 * @class AdminItineraryDispatchApi
 * @extends {BaseAPI}
 */
export class AdminItineraryDispatchApi extends BaseAPI {
    /**
     * 배차를 생성합니다.
     * @summary 배차 생성
     * @param {AdminItineraryDispatchApiCreateAdminItineraryDispatchRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminItineraryDispatchApi
     */
    public createAdminItineraryDispatch(requestParameters: AdminItineraryDispatchApiCreateAdminItineraryDispatchRequest, options?: RawAxiosRequestConfig) {
        return AdminItineraryDispatchApiFp(this.configuration).createAdminItineraryDispatch(requestParameters.adminItineraryDispatchCreateReqDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 배차 상세정보를 조회합니다.
     * @summary 배차 상세정보 조회
     * @param {AdminItineraryDispatchApiGetAdminItineraryDispatchRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminItineraryDispatchApi
     */
    public getAdminItineraryDispatch(requestParameters: AdminItineraryDispatchApiGetAdminItineraryDispatchRequest, options?: RawAxiosRequestConfig) {
        return AdminItineraryDispatchApiFp(this.configuration).getAdminItineraryDispatch(requestParameters.itineraryDispatchId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 배차 목록을 조회합니다.
     * @summary 배차 목록 조회
     * @param {AdminItineraryDispatchApiGetAdminItineraryDispatchPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminItineraryDispatchApi
     */
    public getAdminItineraryDispatchPage(requestParameters: AdminItineraryDispatchApiGetAdminItineraryDispatchPageRequest = {}, options?: RawAxiosRequestConfig) {
        return AdminItineraryDispatchApiFp(this.configuration).getAdminItineraryDispatchPage(requestParameters.driverName, requestParameters.plateNo, requestParameters.itineraryStatusList, requestParameters.startScheduledDt, requestParameters.endScheduledDt, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const GetAdminItineraryDispatchPageItineraryStatusListEnum = {
    Scheduled: 'SCHEDULED',
    Running: 'RUNNING',
    Completed: 'COMPLETED'
} as const;
export type GetAdminItineraryDispatchPageItineraryStatusListEnum = typeof GetAdminItineraryDispatchPageItineraryStatusListEnum[keyof typeof GetAdminItineraryDispatchPageItineraryStatusListEnum];
