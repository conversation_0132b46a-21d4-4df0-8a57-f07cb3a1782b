/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { EquipmentAllDetailResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { EquipmentCreateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { EquipmentDetailResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { EquipmentUpdateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelEquipmentListItemResDTO } from '../../../../src/api/generated/models';
/**
 * DriverEquipmentApi - axios parameter creator
 * @export
 */
export const DriverEquipmentApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 주 사용 장비를 설정합니다.
         * @summary 주 사용 장비 설정
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        changeEquipmentName: async (equipmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('changeEquipmentName', 'equipmentId', equipmentId)
            const localVarPath = `/api/equipment/primary-equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비명을 변경합니다.
         * @summary 장비명 변경
         * @param {number} equipmentId 장비아이디
         * @param {string} equipmentName 장비명
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        changeEquipmentName1: async (equipmentId: number, equipmentName: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('changeEquipmentName1', 'equipmentId', equipmentId)
            // verify required parameter 'equipmentName' is not null or undefined
            assertParamExists('changeEquipmentName1', 'equipmentName', equipmentName)
            const localVarPath = `/api/equipment/equipment-name`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }

            if (equipmentName !== undefined) {
                localVarQueryParameter['equipmentName'] = equipmentName;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비를 생성합니다.
         * @summary 장비 생성
         * @param {Array<EquipmentCreateReqDTO>} equipmentCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createEquipment: async (equipmentCreateReqDTO: Array<EquipmentCreateReqDTO>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentCreateReqDTO' is not null or undefined
            assertParamExists('createEquipment', 'equipmentCreateReqDTO', equipmentCreateReqDTO)
            const localVarPath = `/api/equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(equipmentCreateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비를 삭제합니다.
         * @summary 장비 삭제
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteEquipment: async (equipmentIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentIdList' is not null or undefined
            assertParamExists('deleteEquipment', 'equipmentIdList', equipmentIdList)
            const localVarPath = `/api/equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentIdList) {
                localVarQueryParameter['equipmentIdList'] = equipmentIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비 상세정보를 조회합니다.
         * @summary 장비 상세정보 조회
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getEquipment: async (equipmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('getEquipment', 'equipmentId', equipmentId)
            const localVarPath = `/api/equipment/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비 모든 상세정보를 조회합니다.
         * @summary 장비 모든 상세정보 조회
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getEquipmentAllDetail: async (equipmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('getEquipmentAllDetail', 'equipmentId', equipmentId)
            const localVarPath = `/api/equipment/all-detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비 목록을 조회합니다.
         * @summary 장비 목록 조회
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (equipmentName,manufacturer,modelName,plateNo,serialNo,mileage 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getEquipmentPage: async (manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/equipment/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (manufacturer !== undefined) {
                localVarQueryParameter['manufacturer'] = manufacturer;
            }

            if (modelName !== undefined) {
                localVarQueryParameter['modelName'] = modelName;
            }

            if (serialNo !== undefined) {
                localVarQueryParameter['serialNo'] = serialNo;
            }

            if (plateNo !== undefined) {
                localVarQueryParameter['plateNo'] = plateNo;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비의 VIN No가 이미 등록되어 있는지 확인합니다.<br> 이미 등록되어 있으면 true, 등록되어 있지 않다면 false를 반환합니다. 
         * @summary 장비 VIN No 등록 확인
         * @param {string} serialNo VIN No
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        lookupEquipmentSerialNo: async (serialNo: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'serialNo' is not null or undefined
            assertParamExists('lookupEquipmentSerialNo', 'serialNo', serialNo)
            const localVarPath = `/api/equipment/lookup`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (serialNo !== undefined) {
                localVarQueryParameter['serialNo'] = serialNo;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 장비 수정
         * @param {EquipmentUpdateReqDTO} equipmentUpdateReqDTO 
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateEquipment: async (equipmentUpdateReqDTO: EquipmentUpdateReqDTO, equipmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentUpdateReqDTO' is not null or undefined
            assertParamExists('updateEquipment', 'equipmentUpdateReqDTO', equipmentUpdateReqDTO)
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('updateEquipment', 'equipmentId', equipmentId)
            const localVarPath = `/api/equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(equipmentUpdateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DriverEquipmentApi - functional programming interface
 * @export
 */
export const DriverEquipmentApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DriverEquipmentApiAxiosParamCreator(configuration)
    return {
        /**
         * 주 사용 장비를 설정합니다.
         * @summary 주 사용 장비 설정
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async changeEquipmentName(equipmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.changeEquipmentName(equipmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverEquipmentApi.changeEquipmentName']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비명을 변경합니다.
         * @summary 장비명 변경
         * @param {number} equipmentId 장비아이디
         * @param {string} equipmentName 장비명
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async changeEquipmentName1(equipmentId: number, equipmentName: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.changeEquipmentName1(equipmentId, equipmentName, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverEquipmentApi.changeEquipmentName1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비를 생성합니다.
         * @summary 장비 생성
         * @param {Array<EquipmentCreateReqDTO>} equipmentCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createEquipment(equipmentCreateReqDTO: Array<EquipmentCreateReqDTO>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createEquipment(equipmentCreateReqDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverEquipmentApi.createEquipment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비를 삭제합니다.
         * @summary 장비 삭제
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteEquipment(equipmentIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteEquipment(equipmentIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverEquipmentApi.deleteEquipment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비 상세정보를 조회합니다.
         * @summary 장비 상세정보 조회
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getEquipment(equipmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<EquipmentDetailResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getEquipment(equipmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverEquipmentApi.getEquipment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비 모든 상세정보를 조회합니다.
         * @summary 장비 모든 상세정보 조회
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getEquipmentAllDetail(equipmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<EquipmentAllDetailResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getEquipmentAllDetail(equipmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverEquipmentApi.getEquipmentAllDetail']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비 목록을 조회합니다.
         * @summary 장비 목록 조회
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (equipmentName,manufacturer,modelName,plateNo,serialNo,mileage 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getEquipmentPage(manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelEquipmentListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getEquipmentPage(manufacturer, modelName, serialNo, plateNo, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverEquipmentApi.getEquipmentPage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비의 VIN No가 이미 등록되어 있는지 확인합니다.<br> 이미 등록되어 있으면 true, 등록되어 있지 않다면 false를 반환합니다. 
         * @summary 장비 VIN No 등록 확인
         * @param {string} serialNo VIN No
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async lookupEquipmentSerialNo(serialNo: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<boolean>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.lookupEquipmentSerialNo(serialNo, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverEquipmentApi.lookupEquipmentSerialNo']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 장비 수정
         * @param {EquipmentUpdateReqDTO} equipmentUpdateReqDTO 
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateEquipment(equipmentUpdateReqDTO: EquipmentUpdateReqDTO, equipmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateEquipment(equipmentUpdateReqDTO, equipmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverEquipmentApi.updateEquipment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DriverEquipmentApi - factory interface
 * @export
 */
export const DriverEquipmentApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DriverEquipmentApiFp(configuration)
    return {
        /**
         * 주 사용 장비를 설정합니다.
         * @summary 주 사용 장비 설정
         * @param {DriverEquipmentApiChangeEquipmentNameRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        changeEquipmentName(requestParameters: DriverEquipmentApiChangeEquipmentNameRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.changeEquipmentName(requestParameters.equipmentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비명을 변경합니다.
         * @summary 장비명 변경
         * @param {DriverEquipmentApiChangeEquipmentName1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        changeEquipmentName1(requestParameters: DriverEquipmentApiChangeEquipmentName1Request, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.changeEquipmentName1(requestParameters.equipmentId, requestParameters.equipmentName, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비를 생성합니다.
         * @summary 장비 생성
         * @param {DriverEquipmentApiCreateEquipmentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createEquipment(requestParameters: DriverEquipmentApiCreateEquipmentRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.createEquipment(requestParameters.equipmentCreateReqDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비를 삭제합니다.
         * @summary 장비 삭제
         * @param {DriverEquipmentApiDeleteEquipmentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteEquipment(requestParameters: DriverEquipmentApiDeleteEquipmentRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteEquipment(requestParameters.equipmentIdList, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비 상세정보를 조회합니다.
         * @summary 장비 상세정보 조회
         * @param {DriverEquipmentApiGetEquipmentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getEquipment(requestParameters: DriverEquipmentApiGetEquipmentRequest, options?: RawAxiosRequestConfig): AxiosPromise<EquipmentDetailResDTO> {
            return localVarFp.getEquipment(requestParameters.equipmentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비 모든 상세정보를 조회합니다.
         * @summary 장비 모든 상세정보 조회
         * @param {DriverEquipmentApiGetEquipmentAllDetailRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getEquipmentAllDetail(requestParameters: DriverEquipmentApiGetEquipmentAllDetailRequest, options?: RawAxiosRequestConfig): AxiosPromise<EquipmentAllDetailResDTO> {
            return localVarFp.getEquipmentAllDetail(requestParameters.equipmentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비 목록을 조회합니다.
         * @summary 장비 목록 조회
         * @param {DriverEquipmentApiGetEquipmentPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getEquipmentPage(requestParameters: DriverEquipmentApiGetEquipmentPageRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelEquipmentListItemResDTO> {
            return localVarFp.getEquipmentPage(requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비의 VIN No가 이미 등록되어 있는지 확인합니다.<br> 이미 등록되어 있으면 true, 등록되어 있지 않다면 false를 반환합니다. 
         * @summary 장비 VIN No 등록 확인
         * @param {DriverEquipmentApiLookupEquipmentSerialNoRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        lookupEquipmentSerialNo(requestParameters: DriverEquipmentApiLookupEquipmentSerialNoRequest, options?: RawAxiosRequestConfig): AxiosPromise<boolean> {
            return localVarFp.lookupEquipmentSerialNo(requestParameters.serialNo, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 장비 수정
         * @param {DriverEquipmentApiUpdateEquipmentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateEquipment(requestParameters: DriverEquipmentApiUpdateEquipmentRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.updateEquipment(requestParameters.equipmentUpdateReqDTO, requestParameters.equipmentId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for changeEquipmentName operation in DriverEquipmentApi.
 * @export
 * @interface DriverEquipmentApiChangeEquipmentNameRequest
 */
export interface DriverEquipmentApiChangeEquipmentNameRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof DriverEquipmentApiChangeEquipmentName
     */
    readonly equipmentId: number
}

/**
 * Request parameters for changeEquipmentName1 operation in DriverEquipmentApi.
 * @export
 * @interface DriverEquipmentApiChangeEquipmentName1Request
 */
export interface DriverEquipmentApiChangeEquipmentName1Request {
    /**
     * 장비아이디
     * @type {number}
     * @memberof DriverEquipmentApiChangeEquipmentName1
     */
    readonly equipmentId: number

    /**
     * 장비명
     * @type {string}
     * @memberof DriverEquipmentApiChangeEquipmentName1
     */
    readonly equipmentName: string
}

/**
 * Request parameters for createEquipment operation in DriverEquipmentApi.
 * @export
 * @interface DriverEquipmentApiCreateEquipmentRequest
 */
export interface DriverEquipmentApiCreateEquipmentRequest {
    /**
     * 
     * @type {Array<EquipmentCreateReqDTO>}
     * @memberof DriverEquipmentApiCreateEquipment
     */
    readonly equipmentCreateReqDTO: Array<EquipmentCreateReqDTO>
}

/**
 * Request parameters for deleteEquipment operation in DriverEquipmentApi.
 * @export
 * @interface DriverEquipmentApiDeleteEquipmentRequest
 */
export interface DriverEquipmentApiDeleteEquipmentRequest {
    /**
     * 장비아이디목록
     * @type {Array<number>}
     * @memberof DriverEquipmentApiDeleteEquipment
     */
    readonly equipmentIdList: Array<number>
}

/**
 * Request parameters for getEquipment operation in DriverEquipmentApi.
 * @export
 * @interface DriverEquipmentApiGetEquipmentRequest
 */
export interface DriverEquipmentApiGetEquipmentRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof DriverEquipmentApiGetEquipment
     */
    readonly equipmentId: number
}

/**
 * Request parameters for getEquipmentAllDetail operation in DriverEquipmentApi.
 * @export
 * @interface DriverEquipmentApiGetEquipmentAllDetailRequest
 */
export interface DriverEquipmentApiGetEquipmentAllDetailRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof DriverEquipmentApiGetEquipmentAllDetail
     */
    readonly equipmentId: number
}

/**
 * Request parameters for getEquipmentPage operation in DriverEquipmentApi.
 * @export
 * @interface DriverEquipmentApiGetEquipmentPageRequest
 */
export interface DriverEquipmentApiGetEquipmentPageRequest {
    /**
     * 제조사
     * @type {string}
     * @memberof DriverEquipmentApiGetEquipmentPage
     */
    readonly manufacturer?: string

    /**
     * 모델명
     * @type {string}
     * @memberof DriverEquipmentApiGetEquipmentPage
     */
    readonly modelName?: string

    /**
     * VIN No
     * @type {string}
     * @memberof DriverEquipmentApiGetEquipmentPage
     */
    readonly serialNo?: string

    /**
     * 차량번호
     * @type {string}
     * @memberof DriverEquipmentApiGetEquipmentPage
     */
    readonly plateNo?: string

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof DriverEquipmentApiGetEquipmentPage
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof DriverEquipmentApiGetEquipmentPage
     */
    readonly size?: number

    /**
     * 정렬 조건 (equipmentName,manufacturer,modelName,plateNo,serialNo,mileage 등)
     * @type {string}
     * @memberof DriverEquipmentApiGetEquipmentPage
     */
    readonly sort?: string
}

/**
 * Request parameters for lookupEquipmentSerialNo operation in DriverEquipmentApi.
 * @export
 * @interface DriverEquipmentApiLookupEquipmentSerialNoRequest
 */
export interface DriverEquipmentApiLookupEquipmentSerialNoRequest {
    /**
     * VIN No
     * @type {string}
     * @memberof DriverEquipmentApiLookupEquipmentSerialNo
     */
    readonly serialNo: string
}

/**
 * Request parameters for updateEquipment operation in DriverEquipmentApi.
 * @export
 * @interface DriverEquipmentApiUpdateEquipmentRequest
 */
export interface DriverEquipmentApiUpdateEquipmentRequest {
    /**
     * 
     * @type {EquipmentUpdateReqDTO}
     * @memberof DriverEquipmentApiUpdateEquipment
     */
    readonly equipmentUpdateReqDTO: EquipmentUpdateReqDTO

    /**
     * 장비아이디
     * @type {number}
     * @memberof DriverEquipmentApiUpdateEquipment
     */
    readonly equipmentId: number
}

/**
 * DriverEquipmentApi - object-oriented interface
 * @export
 * @class DriverEquipmentApi
 * @extends {BaseAPI}
 */
export class DriverEquipmentApi extends BaseAPI {
    /**
     * 주 사용 장비를 설정합니다.
     * @summary 주 사용 장비 설정
     * @param {DriverEquipmentApiChangeEquipmentNameRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverEquipmentApi
     */
    public changeEquipmentName(requestParameters: DriverEquipmentApiChangeEquipmentNameRequest, options?: RawAxiosRequestConfig) {
        return DriverEquipmentApiFp(this.configuration).changeEquipmentName(requestParameters.equipmentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비명을 변경합니다.
     * @summary 장비명 변경
     * @param {DriverEquipmentApiChangeEquipmentName1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverEquipmentApi
     */
    public changeEquipmentName1(requestParameters: DriverEquipmentApiChangeEquipmentName1Request, options?: RawAxiosRequestConfig) {
        return DriverEquipmentApiFp(this.configuration).changeEquipmentName1(requestParameters.equipmentId, requestParameters.equipmentName, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비를 생성합니다.
     * @summary 장비 생성
     * @param {DriverEquipmentApiCreateEquipmentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverEquipmentApi
     */
    public createEquipment(requestParameters: DriverEquipmentApiCreateEquipmentRequest, options?: RawAxiosRequestConfig) {
        return DriverEquipmentApiFp(this.configuration).createEquipment(requestParameters.equipmentCreateReqDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비를 삭제합니다.
     * @summary 장비 삭제
     * @param {DriverEquipmentApiDeleteEquipmentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverEquipmentApi
     */
    public deleteEquipment(requestParameters: DriverEquipmentApiDeleteEquipmentRequest, options?: RawAxiosRequestConfig) {
        return DriverEquipmentApiFp(this.configuration).deleteEquipment(requestParameters.equipmentIdList, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비 상세정보를 조회합니다.
     * @summary 장비 상세정보 조회
     * @param {DriverEquipmentApiGetEquipmentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverEquipmentApi
     */
    public getEquipment(requestParameters: DriverEquipmentApiGetEquipmentRequest, options?: RawAxiosRequestConfig) {
        return DriverEquipmentApiFp(this.configuration).getEquipment(requestParameters.equipmentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비 모든 상세정보를 조회합니다.
     * @summary 장비 모든 상세정보 조회
     * @param {DriverEquipmentApiGetEquipmentAllDetailRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverEquipmentApi
     */
    public getEquipmentAllDetail(requestParameters: DriverEquipmentApiGetEquipmentAllDetailRequest, options?: RawAxiosRequestConfig) {
        return DriverEquipmentApiFp(this.configuration).getEquipmentAllDetail(requestParameters.equipmentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비 목록을 조회합니다.
     * @summary 장비 목록 조회
     * @param {DriverEquipmentApiGetEquipmentPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverEquipmentApi
     */
    public getEquipmentPage(requestParameters: DriverEquipmentApiGetEquipmentPageRequest = {}, options?: RawAxiosRequestConfig) {
        return DriverEquipmentApiFp(this.configuration).getEquipmentPage(requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비의 VIN No가 이미 등록되어 있는지 확인합니다.<br> 이미 등록되어 있으면 true, 등록되어 있지 않다면 false를 반환합니다. 
     * @summary 장비 VIN No 등록 확인
     * @param {DriverEquipmentApiLookupEquipmentSerialNoRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverEquipmentApi
     */
    public lookupEquipmentSerialNo(requestParameters: DriverEquipmentApiLookupEquipmentSerialNoRequest, options?: RawAxiosRequestConfig) {
        return DriverEquipmentApiFp(this.configuration).lookupEquipmentSerialNo(requestParameters.serialNo, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
     * @summary 장비 수정
     * @param {DriverEquipmentApiUpdateEquipmentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverEquipmentApi
     */
    public updateEquipment(requestParameters: DriverEquipmentApiUpdateEquipmentRequest, options?: RawAxiosRequestConfig) {
        return DriverEquipmentApiFp(this.configuration).updateEquipment(requestParameters.equipmentUpdateReqDTO, requestParameters.equipmentId, options).then((request) => request(this.axios, this.basePath));
    }
}

