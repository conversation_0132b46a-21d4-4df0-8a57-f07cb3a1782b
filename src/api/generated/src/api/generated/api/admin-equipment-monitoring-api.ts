/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { AdminEquipmentMonitoringDetailResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { AdminEquipmentTrackingDetailResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelAdminEquipmentMonitoringListItemResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelAdminEquipmentTimeLineListItemResDTO } from '../../../../src/api/generated/models';
/**
 * AdminEquipmentMonitoringApi - axios parameter creator
 * @export
 */
export const AdminEquipmentMonitoringApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 장비 모니터링 상세를 조회합니다.
         * @summary 장비 모니터링 상세 조회
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentForMonitoring: async (equipmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('getAdminEquipmentForMonitoring', 'equipmentId', equipmentId)
            const localVarPath = `/api/admin/equipment/monitoring/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비 트래킹 상세를 조회합니다.
         * @summary 장비 트래킹 상세 조회 (작성 중)
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentForTracking: async (equipmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('getAdminEquipmentForTracking', 'equipmentId', equipmentId)
            const localVarPath = `/api/admin/equipment/monitoring/track`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비 모니터링 목록을 조회합니다.
         * @summary 장비 모니터링 목록 조회
         * @param {number} [countryId] 국가아이디
         * @param {number} [dealerId] 딜러아이디
         * @param {number} [fleetId] 플릿아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {string} [driverName] 운전자명
         * @param {GetAdminEquipmentPageForMonitoringOperationStatusEnum} [operationStatus] 동작상태
         * @param {GetAdminEquipmentPageForMonitoringGpsStatusEnum} [gpsStatus] GPS상태
         * @param {GetAdminEquipmentPageForMonitoringBreakdownStatusEnum} [breakdownStatus] 고장상태
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (manufacturer,modelName,plateNo,serialNo,mileage 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPageForMonitoring: async (countryId?: number, dealerId?: number, fleetId?: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, driverName?: string, operationStatus?: GetAdminEquipmentPageForMonitoringOperationStatusEnum, gpsStatus?: GetAdminEquipmentPageForMonitoringGpsStatusEnum, breakdownStatus?: GetAdminEquipmentPageForMonitoringBreakdownStatusEnum, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/admin/equipment/monitoring/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (countryId !== undefined) {
                localVarQueryParameter['countryId'] = countryId;
            }

            if (dealerId !== undefined) {
                localVarQueryParameter['dealerId'] = dealerId;
            }

            if (fleetId !== undefined) {
                localVarQueryParameter['fleetId'] = fleetId;
            }

            if (manufacturer !== undefined) {
                localVarQueryParameter['manufacturer'] = manufacturer;
            }

            if (modelName !== undefined) {
                localVarQueryParameter['modelName'] = modelName;
            }

            if (serialNo !== undefined) {
                localVarQueryParameter['serialNo'] = serialNo;
            }

            if (plateNo !== undefined) {
                localVarQueryParameter['plateNo'] = plateNo;
            }

            if (driverName !== undefined) {
                localVarQueryParameter['driverName'] = driverName;
            }

            if (operationStatus !== undefined) {
                localVarQueryParameter['operationStatus'] = operationStatus;
            }

            if (gpsStatus !== undefined) {
                localVarQueryParameter['gpsStatus'] = gpsStatus;
            }

            if (breakdownStatus !== undefined) {
                localVarQueryParameter['breakdownStatus'] = breakdownStatus;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비 타임라인 목록을 조회합니다.
         * @summary 장비 타임라인 목록 조회 (작성 중)
         * @param {number} [countryId] 국가아이디
         * @param {number} [dealerId] 딜러아이디
         * @param {number} [fleetId] 플릿아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {string} [driverName] 운전자명
         * @param {GetAdminEquipmentPageForTimeLineOperationStatusEnum} [operationStatus] 동작상태
         * @param {GetAdminEquipmentPageForTimeLineGpsStatusEnum} [gpsStatus] GPS상태
         * @param {GetAdminEquipmentPageForTimeLineBreakdownStatusEnum} [breakdownStatus] 고장상태
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (manufacturer,modelName,plateNo,serialNo,mileage 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPageForTimeLine: async (countryId?: number, dealerId?: number, fleetId?: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, driverName?: string, operationStatus?: GetAdminEquipmentPageForTimeLineOperationStatusEnum, gpsStatus?: GetAdminEquipmentPageForTimeLineGpsStatusEnum, breakdownStatus?: GetAdminEquipmentPageForTimeLineBreakdownStatusEnum, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/admin/equipment/monitoring/timeline/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (countryId !== undefined) {
                localVarQueryParameter['countryId'] = countryId;
            }

            if (dealerId !== undefined) {
                localVarQueryParameter['dealerId'] = dealerId;
            }

            if (fleetId !== undefined) {
                localVarQueryParameter['fleetId'] = fleetId;
            }

            if (manufacturer !== undefined) {
                localVarQueryParameter['manufacturer'] = manufacturer;
            }

            if (modelName !== undefined) {
                localVarQueryParameter['modelName'] = modelName;
            }

            if (serialNo !== undefined) {
                localVarQueryParameter['serialNo'] = serialNo;
            }

            if (plateNo !== undefined) {
                localVarQueryParameter['plateNo'] = plateNo;
            }

            if (driverName !== undefined) {
                localVarQueryParameter['driverName'] = driverName;
            }

            if (operationStatus !== undefined) {
                localVarQueryParameter['operationStatus'] = operationStatus;
            }

            if (gpsStatus !== undefined) {
                localVarQueryParameter['gpsStatus'] = gpsStatus;
            }

            if (breakdownStatus !== undefined) {
                localVarQueryParameter['breakdownStatus'] = breakdownStatus;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AdminEquipmentMonitoringApi - functional programming interface
 * @export
 */
export const AdminEquipmentMonitoringApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AdminEquipmentMonitoringApiAxiosParamCreator(configuration)
    return {
        /**
         * 장비 모니터링 상세를 조회합니다.
         * @summary 장비 모니터링 상세 조회
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminEquipmentForMonitoring(equipmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AdminEquipmentMonitoringDetailResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminEquipmentForMonitoring(equipmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentMonitoringApi.getAdminEquipmentForMonitoring']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비 트래킹 상세를 조회합니다.
         * @summary 장비 트래킹 상세 조회 (작성 중)
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminEquipmentForTracking(equipmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AdminEquipmentTrackingDetailResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminEquipmentForTracking(equipmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentMonitoringApi.getAdminEquipmentForTracking']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비 모니터링 목록을 조회합니다.
         * @summary 장비 모니터링 목록 조회
         * @param {number} [countryId] 국가아이디
         * @param {number} [dealerId] 딜러아이디
         * @param {number} [fleetId] 플릿아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {string} [driverName] 운전자명
         * @param {GetAdminEquipmentPageForMonitoringOperationStatusEnum} [operationStatus] 동작상태
         * @param {GetAdminEquipmentPageForMonitoringGpsStatusEnum} [gpsStatus] GPS상태
         * @param {GetAdminEquipmentPageForMonitoringBreakdownStatusEnum} [breakdownStatus] 고장상태
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (manufacturer,modelName,plateNo,serialNo,mileage 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminEquipmentPageForMonitoring(countryId?: number, dealerId?: number, fleetId?: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, driverName?: string, operationStatus?: GetAdminEquipmentPageForMonitoringOperationStatusEnum, gpsStatus?: GetAdminEquipmentPageForMonitoringGpsStatusEnum, breakdownStatus?: GetAdminEquipmentPageForMonitoringBreakdownStatusEnum, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminEquipmentMonitoringListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminEquipmentPageForMonitoring(countryId, dealerId, fleetId, manufacturer, modelName, serialNo, plateNo, driverName, operationStatus, gpsStatus, breakdownStatus, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentMonitoringApi.getAdminEquipmentPageForMonitoring']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비 타임라인 목록을 조회합니다.
         * @summary 장비 타임라인 목록 조회 (작성 중)
         * @param {number} [countryId] 국가아이디
         * @param {number} [dealerId] 딜러아이디
         * @param {number} [fleetId] 플릿아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {string} [driverName] 운전자명
         * @param {GetAdminEquipmentPageForTimeLineOperationStatusEnum} [operationStatus] 동작상태
         * @param {GetAdminEquipmentPageForTimeLineGpsStatusEnum} [gpsStatus] GPS상태
         * @param {GetAdminEquipmentPageForTimeLineBreakdownStatusEnum} [breakdownStatus] 고장상태
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (manufacturer,modelName,plateNo,serialNo,mileage 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminEquipmentPageForTimeLine(countryId?: number, dealerId?: number, fleetId?: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, driverName?: string, operationStatus?: GetAdminEquipmentPageForTimeLineOperationStatusEnum, gpsStatus?: GetAdminEquipmentPageForTimeLineGpsStatusEnum, breakdownStatus?: GetAdminEquipmentPageForTimeLineBreakdownStatusEnum, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminEquipmentTimeLineListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminEquipmentPageForTimeLine(countryId, dealerId, fleetId, manufacturer, modelName, serialNo, plateNo, driverName, operationStatus, gpsStatus, breakdownStatus, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentMonitoringApi.getAdminEquipmentPageForTimeLine']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AdminEquipmentMonitoringApi - factory interface
 * @export
 */
export const AdminEquipmentMonitoringApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AdminEquipmentMonitoringApiFp(configuration)
    return {
        /**
         * 장비 모니터링 상세를 조회합니다.
         * @summary 장비 모니터링 상세 조회
         * @param {AdminEquipmentMonitoringApiGetAdminEquipmentForMonitoringRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentForMonitoring(requestParameters: AdminEquipmentMonitoringApiGetAdminEquipmentForMonitoringRequest, options?: RawAxiosRequestConfig): AxiosPromise<AdminEquipmentMonitoringDetailResDTO> {
            return localVarFp.getAdminEquipmentForMonitoring(requestParameters.equipmentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비 트래킹 상세를 조회합니다.
         * @summary 장비 트래킹 상세 조회 (작성 중)
         * @param {AdminEquipmentMonitoringApiGetAdminEquipmentForTrackingRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentForTracking(requestParameters: AdminEquipmentMonitoringApiGetAdminEquipmentForTrackingRequest, options?: RawAxiosRequestConfig): AxiosPromise<AdminEquipmentTrackingDetailResDTO> {
            return localVarFp.getAdminEquipmentForTracking(requestParameters.equipmentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비 모니터링 목록을 조회합니다.
         * @summary 장비 모니터링 목록 조회
         * @param {AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoringRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPageForMonitoring(requestParameters: AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoringRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminEquipmentMonitoringListItemResDTO> {
            return localVarFp.getAdminEquipmentPageForMonitoring(requestParameters.countryId, requestParameters.dealerId, requestParameters.fleetId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.driverName, requestParameters.operationStatus, requestParameters.gpsStatus, requestParameters.breakdownStatus, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비 타임라인 목록을 조회합니다.
         * @summary 장비 타임라인 목록 조회 (작성 중)
         * @param {AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLineRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPageForTimeLine(requestParameters: AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLineRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminEquipmentTimeLineListItemResDTO> {
            return localVarFp.getAdminEquipmentPageForTimeLine(requestParameters.countryId, requestParameters.dealerId, requestParameters.fleetId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.driverName, requestParameters.operationStatus, requestParameters.gpsStatus, requestParameters.breakdownStatus, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getAdminEquipmentForMonitoring operation in AdminEquipmentMonitoringApi.
 * @export
 * @interface AdminEquipmentMonitoringApiGetAdminEquipmentForMonitoringRequest
 */
export interface AdminEquipmentMonitoringApiGetAdminEquipmentForMonitoringRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentForMonitoring
     */
    readonly equipmentId: number
}

/**
 * Request parameters for getAdminEquipmentForTracking operation in AdminEquipmentMonitoringApi.
 * @export
 * @interface AdminEquipmentMonitoringApiGetAdminEquipmentForTrackingRequest
 */
export interface AdminEquipmentMonitoringApiGetAdminEquipmentForTrackingRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentForTracking
     */
    readonly equipmentId: number
}

/**
 * Request parameters for getAdminEquipmentPageForMonitoring operation in AdminEquipmentMonitoringApi.
 * @export
 * @interface AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoringRequest
 */
export interface AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoringRequest {
    /**
     * 국가아이디
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly countryId?: number

    /**
     * 딜러아이디
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly dealerId?: number

    /**
     * 플릿아이디
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly fleetId?: number

    /**
     * 제조사
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly manufacturer?: string

    /**
     * 모델명
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly modelName?: string

    /**
     * VIN No
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly serialNo?: string

    /**
     * 차량번호
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly plateNo?: string

    /**
     * 운전자명
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly driverName?: string

    /**
     * 동작상태
     * @type {'RUNNING' | 'IDLE'}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly operationStatus?: GetAdminEquipmentPageForMonitoringOperationStatusEnum

    /**
     * GPS상태
     * @type {'CONNECTED' | 'DISCONNECTED'}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly gpsStatus?: GetAdminEquipmentPageForMonitoringGpsStatusEnum

    /**
     * 고장상태
     * @type {'BREAKDOWN' | 'REPAIRING' | 'NONE'}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly breakdownStatus?: GetAdminEquipmentPageForMonitoringBreakdownStatusEnum

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly size?: number

    /**
     * 정렬 조건 (manufacturer,modelName,plateNo,serialNo,mileage 등)
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoring
     */
    readonly sort?: string
}

/**
 * Request parameters for getAdminEquipmentPageForTimeLine operation in AdminEquipmentMonitoringApi.
 * @export
 * @interface AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLineRequest
 */
export interface AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLineRequest {
    /**
     * 국가아이디
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly countryId?: number

    /**
     * 딜러아이디
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly dealerId?: number

    /**
     * 플릿아이디
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly fleetId?: number

    /**
     * 제조사
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly manufacturer?: string

    /**
     * 모델명
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly modelName?: string

    /**
     * VIN No
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly serialNo?: string

    /**
     * 차량번호
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly plateNo?: string

    /**
     * 운전자명
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly driverName?: string

    /**
     * 동작상태
     * @type {'RUNNING' | 'IDLE'}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly operationStatus?: GetAdminEquipmentPageForTimeLineOperationStatusEnum

    /**
     * GPS상태
     * @type {'CONNECTED' | 'DISCONNECTED'}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly gpsStatus?: GetAdminEquipmentPageForTimeLineGpsStatusEnum

    /**
     * 고장상태
     * @type {'BREAKDOWN' | 'REPAIRING' | 'NONE'}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly breakdownStatus?: GetAdminEquipmentPageForTimeLineBreakdownStatusEnum

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly size?: number

    /**
     * 정렬 조건 (manufacturer,modelName,plateNo,serialNo,mileage 등)
     * @type {string}
     * @memberof AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLine
     */
    readonly sort?: string
}

/**
 * AdminEquipmentMonitoringApi - object-oriented interface
 * @export
 * @class AdminEquipmentMonitoringApi
 * @extends {BaseAPI}
 */
export class AdminEquipmentMonitoringApi extends BaseAPI {
    /**
     * 장비 모니터링 상세를 조회합니다.
     * @summary 장비 모니터링 상세 조회
     * @param {AdminEquipmentMonitoringApiGetAdminEquipmentForMonitoringRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentMonitoringApi
     */
    public getAdminEquipmentForMonitoring(requestParameters: AdminEquipmentMonitoringApiGetAdminEquipmentForMonitoringRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentMonitoringApiFp(this.configuration).getAdminEquipmentForMonitoring(requestParameters.equipmentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비 트래킹 상세를 조회합니다.
     * @summary 장비 트래킹 상세 조회 (작성 중)
     * @param {AdminEquipmentMonitoringApiGetAdminEquipmentForTrackingRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentMonitoringApi
     */
    public getAdminEquipmentForTracking(requestParameters: AdminEquipmentMonitoringApiGetAdminEquipmentForTrackingRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentMonitoringApiFp(this.configuration).getAdminEquipmentForTracking(requestParameters.equipmentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비 모니터링 목록을 조회합니다.
     * @summary 장비 모니터링 목록 조회
     * @param {AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoringRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentMonitoringApi
     */
    public getAdminEquipmentPageForMonitoring(requestParameters: AdminEquipmentMonitoringApiGetAdminEquipmentPageForMonitoringRequest = {}, options?: RawAxiosRequestConfig) {
        return AdminEquipmentMonitoringApiFp(this.configuration).getAdminEquipmentPageForMonitoring(requestParameters.countryId, requestParameters.dealerId, requestParameters.fleetId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.driverName, requestParameters.operationStatus, requestParameters.gpsStatus, requestParameters.breakdownStatus, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비 타임라인 목록을 조회합니다.
     * @summary 장비 타임라인 목록 조회 (작성 중)
     * @param {AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLineRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentMonitoringApi
     */
    public getAdminEquipmentPageForTimeLine(requestParameters: AdminEquipmentMonitoringApiGetAdminEquipmentPageForTimeLineRequest = {}, options?: RawAxiosRequestConfig) {
        return AdminEquipmentMonitoringApiFp(this.configuration).getAdminEquipmentPageForTimeLine(requestParameters.countryId, requestParameters.dealerId, requestParameters.fleetId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.driverName, requestParameters.operationStatus, requestParameters.gpsStatus, requestParameters.breakdownStatus, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const GetAdminEquipmentPageForMonitoringOperationStatusEnum = {
    Running: 'RUNNING',
    Idle: 'IDLE'
} as const;
export type GetAdminEquipmentPageForMonitoringOperationStatusEnum = typeof GetAdminEquipmentPageForMonitoringOperationStatusEnum[keyof typeof GetAdminEquipmentPageForMonitoringOperationStatusEnum];
/**
 * @export
 */
export const GetAdminEquipmentPageForMonitoringGpsStatusEnum = {
    Connected: 'CONNECTED',
    Disconnected: 'DISCONNECTED'
} as const;
export type GetAdminEquipmentPageForMonitoringGpsStatusEnum = typeof GetAdminEquipmentPageForMonitoringGpsStatusEnum[keyof typeof GetAdminEquipmentPageForMonitoringGpsStatusEnum];
/**
 * @export
 */
export const GetAdminEquipmentPageForMonitoringBreakdownStatusEnum = {
    Breakdown: 'BREAKDOWN',
    Repairing: 'REPAIRING',
    None: 'NONE'
} as const;
export type GetAdminEquipmentPageForMonitoringBreakdownStatusEnum = typeof GetAdminEquipmentPageForMonitoringBreakdownStatusEnum[keyof typeof GetAdminEquipmentPageForMonitoringBreakdownStatusEnum];
/**
 * @export
 */
export const GetAdminEquipmentPageForTimeLineOperationStatusEnum = {
    Running: 'RUNNING',
    Idle: 'IDLE'
} as const;
export type GetAdminEquipmentPageForTimeLineOperationStatusEnum = typeof GetAdminEquipmentPageForTimeLineOperationStatusEnum[keyof typeof GetAdminEquipmentPageForTimeLineOperationStatusEnum];
/**
 * @export
 */
export const GetAdminEquipmentPageForTimeLineGpsStatusEnum = {
    Connected: 'CONNECTED',
    Disconnected: 'DISCONNECTED'
} as const;
export type GetAdminEquipmentPageForTimeLineGpsStatusEnum = typeof GetAdminEquipmentPageForTimeLineGpsStatusEnum[keyof typeof GetAdminEquipmentPageForTimeLineGpsStatusEnum];
/**
 * @export
 */
export const GetAdminEquipmentPageForTimeLineBreakdownStatusEnum = {
    Breakdown: 'BREAKDOWN',
    Repairing: 'REPAIRING',
    None: 'NONE'
} as const;
export type GetAdminEquipmentPageForTimeLineBreakdownStatusEnum = typeof GetAdminEquipmentPageForTimeLineBreakdownStatusEnum[keyof typeof GetAdminEquipmentPageForTimeLineBreakdownStatusEnum];
