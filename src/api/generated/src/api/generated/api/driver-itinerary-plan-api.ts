/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { ItineraryPlanCreateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ItineraryPlanCreateResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ItineraryPlanDestinationCreateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ItineraryPlanDestinationCreateResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ItineraryPlanDetailResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ItineraryPlanListItemResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ItineraryPlanUpdateReqDTO } from '../../../../src/api/generated/models';
/**
 * DriverItineraryPlanApi - axios parameter creator
 * @export
 */
export const DriverItineraryPlanApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 신규 여정계획을 생성합니다.<br> 목적지 목록과 함께 요청할 수도 있고, 목적지 목록 없이 요청할 수도 있습니다. 응답에 포함된 여정계획 아이디는 다른 API 호출 시 사용됩니다. 
         * @summary 여정계획 생성
         * @param {ItineraryPlanCreateReqDTO} itineraryPlanCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createItineraryPlan: async (itineraryPlanCreateReqDTO: ItineraryPlanCreateReqDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itineraryPlanCreateReqDTO' is not null or undefined
            assertParamExists('createItineraryPlan', 'itineraryPlanCreateReqDTO', itineraryPlanCreateReqDTO)
            const localVarPath = `/api/itinerary-plan`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(itineraryPlanCreateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 여정계획 아이디에 해당하는 여정계획에 신규 여정계획의 목적지 목록을 생성합니다.<br> 기존 등록된 목적지 목록이 있다면 먼저 삭제되고, 요청된 목적지 목록으로 새로 생성합니다.<br> 따라서, 앱에서 목적지 목록을 편집(추가, 삭제, 순서변경 등)한 후에 모든 목적지 목록과 함께 호출해야 합니다. 
         * @summary 여정계획목적지목록 생성
         * @param {number} itineraryPlanId 여정계획 아이디
         * @param {Array<ItineraryPlanDestinationCreateReqDTO>} [itineraryPlanDestinationCreateReqDTO] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createItineraryPlanDestination: async (itineraryPlanId: number, itineraryPlanDestinationCreateReqDTO?: Array<ItineraryPlanDestinationCreateReqDTO>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itineraryPlanId' is not null or undefined
            assertParamExists('createItineraryPlanDestination', 'itineraryPlanId', itineraryPlanId)
            const localVarPath = `/api/itinerary-plan/destination`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (itineraryPlanId !== undefined) {
                localVarQueryParameter['itineraryPlanId'] = itineraryPlanId;
            }


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(itineraryPlanDestinationCreateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 여정계획 아이디 목록에 해당하는 여정계획을 삭제합니다.
         * @summary 여정계획 삭제
         * @param {Array<number>} [itineraryPlanIds] 여정계획 아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteItineraryPlan: async (itineraryPlanIds?: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/itinerary-plan`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (itineraryPlanIds) {
                localVarQueryParameter['itineraryPlanIds'] = itineraryPlanIds;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 여정계획 아이디에 해당하는 여정계획의 상세정보를 조회합니다.
         * @summary 여정계획상세 조회
         * @param {number} itineraryPlanId 여정계획 아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getItineraryPlanDetail: async (itineraryPlanId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itineraryPlanId' is not null or undefined
            assertParamExists('getItineraryPlanDetail', 'itineraryPlanId', itineraryPlanId)
            const localVarPath = `/api/itinerary-plan/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (itineraryPlanId !== undefined) {
                localVarQueryParameter['itineraryPlanId'] = itineraryPlanId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 현재 등록된 여정계획 목록을 조회합니다.
         * @summary 여정계획목록 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getItineraryPlanList: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/itinerary-plan/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 여정계획 아이디에 해당하는 여정계획을 수정합니다.
         * @summary 여정계획 수정
         * @param {ItineraryPlanUpdateReqDTO} itineraryPlanUpdateReqDTO 
         * @param {number} itineraryPlanId 여정계획 아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateItineraryPlan: async (itineraryPlanUpdateReqDTO: ItineraryPlanUpdateReqDTO, itineraryPlanId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itineraryPlanUpdateReqDTO' is not null or undefined
            assertParamExists('updateItineraryPlan', 'itineraryPlanUpdateReqDTO', itineraryPlanUpdateReqDTO)
            // verify required parameter 'itineraryPlanId' is not null or undefined
            assertParamExists('updateItineraryPlan', 'itineraryPlanId', itineraryPlanId)
            const localVarPath = `/api/itinerary-plan`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (itineraryPlanId !== undefined) {
                localVarQueryParameter['itineraryPlanId'] = itineraryPlanId;
            }


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(itineraryPlanUpdateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DriverItineraryPlanApi - functional programming interface
 * @export
 */
export const DriverItineraryPlanApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DriverItineraryPlanApiAxiosParamCreator(configuration)
    return {
        /**
         * 신규 여정계획을 생성합니다.<br> 목적지 목록과 함께 요청할 수도 있고, 목적지 목록 없이 요청할 수도 있습니다. 응답에 포함된 여정계획 아이디는 다른 API 호출 시 사용됩니다. 
         * @summary 여정계획 생성
         * @param {ItineraryPlanCreateReqDTO} itineraryPlanCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createItineraryPlan(itineraryPlanCreateReqDTO: ItineraryPlanCreateReqDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ItineraryPlanCreateResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createItineraryPlan(itineraryPlanCreateReqDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryPlanApi.createItineraryPlan']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 여정계획 아이디에 해당하는 여정계획에 신규 여정계획의 목적지 목록을 생성합니다.<br> 기존 등록된 목적지 목록이 있다면 먼저 삭제되고, 요청된 목적지 목록으로 새로 생성합니다.<br> 따라서, 앱에서 목적지 목록을 편집(추가, 삭제, 순서변경 등)한 후에 모든 목적지 목록과 함께 호출해야 합니다. 
         * @summary 여정계획목적지목록 생성
         * @param {number} itineraryPlanId 여정계획 아이디
         * @param {Array<ItineraryPlanDestinationCreateReqDTO>} [itineraryPlanDestinationCreateReqDTO] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createItineraryPlanDestination(itineraryPlanId: number, itineraryPlanDestinationCreateReqDTO?: Array<ItineraryPlanDestinationCreateReqDTO>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ItineraryPlanDestinationCreateResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createItineraryPlanDestination(itineraryPlanId, itineraryPlanDestinationCreateReqDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryPlanApi.createItineraryPlanDestination']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 여정계획 아이디 목록에 해당하는 여정계획을 삭제합니다.
         * @summary 여정계획 삭제
         * @param {Array<number>} [itineraryPlanIds] 여정계획 아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteItineraryPlan(itineraryPlanIds?: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteItineraryPlan(itineraryPlanIds, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryPlanApi.deleteItineraryPlan']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 여정계획 아이디에 해당하는 여정계획의 상세정보를 조회합니다.
         * @summary 여정계획상세 조회
         * @param {number} itineraryPlanId 여정계획 아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getItineraryPlanDetail(itineraryPlanId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ItineraryPlanDetailResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getItineraryPlanDetail(itineraryPlanId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryPlanApi.getItineraryPlanDetail']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 현재 등록된 여정계획 목록을 조회합니다.
         * @summary 여정계획목록 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getItineraryPlanList(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<ItineraryPlanListItemResDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getItineraryPlanList(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryPlanApi.getItineraryPlanList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 여정계획 아이디에 해당하는 여정계획을 수정합니다.
         * @summary 여정계획 수정
         * @param {ItineraryPlanUpdateReqDTO} itineraryPlanUpdateReqDTO 
         * @param {number} itineraryPlanId 여정계획 아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateItineraryPlan(itineraryPlanUpdateReqDTO: ItineraryPlanUpdateReqDTO, itineraryPlanId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateItineraryPlan(itineraryPlanUpdateReqDTO, itineraryPlanId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryPlanApi.updateItineraryPlan']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DriverItineraryPlanApi - factory interface
 * @export
 */
export const DriverItineraryPlanApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DriverItineraryPlanApiFp(configuration)
    return {
        /**
         * 신규 여정계획을 생성합니다.<br> 목적지 목록과 함께 요청할 수도 있고, 목적지 목록 없이 요청할 수도 있습니다. 응답에 포함된 여정계획 아이디는 다른 API 호출 시 사용됩니다. 
         * @summary 여정계획 생성
         * @param {DriverItineraryPlanApiCreateItineraryPlanRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createItineraryPlan(requestParameters: DriverItineraryPlanApiCreateItineraryPlanRequest, options?: RawAxiosRequestConfig): AxiosPromise<ItineraryPlanCreateResDTO> {
            return localVarFp.createItineraryPlan(requestParameters.itineraryPlanCreateReqDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 여정계획 아이디에 해당하는 여정계획에 신규 여정계획의 목적지 목록을 생성합니다.<br> 기존 등록된 목적지 목록이 있다면 먼저 삭제되고, 요청된 목적지 목록으로 새로 생성합니다.<br> 따라서, 앱에서 목적지 목록을 편집(추가, 삭제, 순서변경 등)한 후에 모든 목적지 목록과 함께 호출해야 합니다. 
         * @summary 여정계획목적지목록 생성
         * @param {DriverItineraryPlanApiCreateItineraryPlanDestinationRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createItineraryPlanDestination(requestParameters: DriverItineraryPlanApiCreateItineraryPlanDestinationRequest, options?: RawAxiosRequestConfig): AxiosPromise<ItineraryPlanDestinationCreateResDTO> {
            return localVarFp.createItineraryPlanDestination(requestParameters.itineraryPlanId, requestParameters.itineraryPlanDestinationCreateReqDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 여정계획 아이디 목록에 해당하는 여정계획을 삭제합니다.
         * @summary 여정계획 삭제
         * @param {DriverItineraryPlanApiDeleteItineraryPlanRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteItineraryPlan(requestParameters: DriverItineraryPlanApiDeleteItineraryPlanRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteItineraryPlan(requestParameters.itineraryPlanIds, options).then((request) => request(axios, basePath));
        },
        /**
         * 여정계획 아이디에 해당하는 여정계획의 상세정보를 조회합니다.
         * @summary 여정계획상세 조회
         * @param {DriverItineraryPlanApiGetItineraryPlanDetailRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getItineraryPlanDetail(requestParameters: DriverItineraryPlanApiGetItineraryPlanDetailRequest, options?: RawAxiosRequestConfig): AxiosPromise<ItineraryPlanDetailResDTO> {
            return localVarFp.getItineraryPlanDetail(requestParameters.itineraryPlanId, options).then((request) => request(axios, basePath));
        },
        /**
         * 현재 등록된 여정계획 목록을 조회합니다.
         * @summary 여정계획목록 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getItineraryPlanList(options?: RawAxiosRequestConfig): AxiosPromise<Array<ItineraryPlanListItemResDTO>> {
            return localVarFp.getItineraryPlanList(options).then((request) => request(axios, basePath));
        },
        /**
         * 여정계획 아이디에 해당하는 여정계획을 수정합니다.
         * @summary 여정계획 수정
         * @param {DriverItineraryPlanApiUpdateItineraryPlanRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateItineraryPlan(requestParameters: DriverItineraryPlanApiUpdateItineraryPlanRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.updateItineraryPlan(requestParameters.itineraryPlanUpdateReqDTO, requestParameters.itineraryPlanId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createItineraryPlan operation in DriverItineraryPlanApi.
 * @export
 * @interface DriverItineraryPlanApiCreateItineraryPlanRequest
 */
export interface DriverItineraryPlanApiCreateItineraryPlanRequest {
    /**
     * 
     * @type {ItineraryPlanCreateReqDTO}
     * @memberof DriverItineraryPlanApiCreateItineraryPlan
     */
    readonly itineraryPlanCreateReqDTO: ItineraryPlanCreateReqDTO
}

/**
 * Request parameters for createItineraryPlanDestination operation in DriverItineraryPlanApi.
 * @export
 * @interface DriverItineraryPlanApiCreateItineraryPlanDestinationRequest
 */
export interface DriverItineraryPlanApiCreateItineraryPlanDestinationRequest {
    /**
     * 여정계획 아이디
     * @type {number}
     * @memberof DriverItineraryPlanApiCreateItineraryPlanDestination
     */
    readonly itineraryPlanId: number

    /**
     * 
     * @type {Array<ItineraryPlanDestinationCreateReqDTO>}
     * @memberof DriverItineraryPlanApiCreateItineraryPlanDestination
     */
    readonly itineraryPlanDestinationCreateReqDTO?: Array<ItineraryPlanDestinationCreateReqDTO>
}

/**
 * Request parameters for deleteItineraryPlan operation in DriverItineraryPlanApi.
 * @export
 * @interface DriverItineraryPlanApiDeleteItineraryPlanRequest
 */
export interface DriverItineraryPlanApiDeleteItineraryPlanRequest {
    /**
     * 여정계획 아이디목록
     * @type {Array<number>}
     * @memberof DriverItineraryPlanApiDeleteItineraryPlan
     */
    readonly itineraryPlanIds?: Array<number>
}

/**
 * Request parameters for getItineraryPlanDetail operation in DriverItineraryPlanApi.
 * @export
 * @interface DriverItineraryPlanApiGetItineraryPlanDetailRequest
 */
export interface DriverItineraryPlanApiGetItineraryPlanDetailRequest {
    /**
     * 여정계획 아이디
     * @type {number}
     * @memberof DriverItineraryPlanApiGetItineraryPlanDetail
     */
    readonly itineraryPlanId: number
}

/**
 * Request parameters for updateItineraryPlan operation in DriverItineraryPlanApi.
 * @export
 * @interface DriverItineraryPlanApiUpdateItineraryPlanRequest
 */
export interface DriverItineraryPlanApiUpdateItineraryPlanRequest {
    /**
     * 
     * @type {ItineraryPlanUpdateReqDTO}
     * @memberof DriverItineraryPlanApiUpdateItineraryPlan
     */
    readonly itineraryPlanUpdateReqDTO: ItineraryPlanUpdateReqDTO

    /**
     * 여정계획 아이디
     * @type {number}
     * @memberof DriverItineraryPlanApiUpdateItineraryPlan
     */
    readonly itineraryPlanId: number
}

/**
 * DriverItineraryPlanApi - object-oriented interface
 * @export
 * @class DriverItineraryPlanApi
 * @extends {BaseAPI}
 */
export class DriverItineraryPlanApi extends BaseAPI {
    /**
     * 신규 여정계획을 생성합니다.<br> 목적지 목록과 함께 요청할 수도 있고, 목적지 목록 없이 요청할 수도 있습니다. 응답에 포함된 여정계획 아이디는 다른 API 호출 시 사용됩니다. 
     * @summary 여정계획 생성
     * @param {DriverItineraryPlanApiCreateItineraryPlanRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryPlanApi
     */
    public createItineraryPlan(requestParameters: DriverItineraryPlanApiCreateItineraryPlanRequest, options?: RawAxiosRequestConfig) {
        return DriverItineraryPlanApiFp(this.configuration).createItineraryPlan(requestParameters.itineraryPlanCreateReqDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 여정계획 아이디에 해당하는 여정계획에 신규 여정계획의 목적지 목록을 생성합니다.<br> 기존 등록된 목적지 목록이 있다면 먼저 삭제되고, 요청된 목적지 목록으로 새로 생성합니다.<br> 따라서, 앱에서 목적지 목록을 편집(추가, 삭제, 순서변경 등)한 후에 모든 목적지 목록과 함께 호출해야 합니다. 
     * @summary 여정계획목적지목록 생성
     * @param {DriverItineraryPlanApiCreateItineraryPlanDestinationRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryPlanApi
     */
    public createItineraryPlanDestination(requestParameters: DriverItineraryPlanApiCreateItineraryPlanDestinationRequest, options?: RawAxiosRequestConfig) {
        return DriverItineraryPlanApiFp(this.configuration).createItineraryPlanDestination(requestParameters.itineraryPlanId, requestParameters.itineraryPlanDestinationCreateReqDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 여정계획 아이디 목록에 해당하는 여정계획을 삭제합니다.
     * @summary 여정계획 삭제
     * @param {DriverItineraryPlanApiDeleteItineraryPlanRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryPlanApi
     */
    public deleteItineraryPlan(requestParameters: DriverItineraryPlanApiDeleteItineraryPlanRequest = {}, options?: RawAxiosRequestConfig) {
        return DriverItineraryPlanApiFp(this.configuration).deleteItineraryPlan(requestParameters.itineraryPlanIds, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 여정계획 아이디에 해당하는 여정계획의 상세정보를 조회합니다.
     * @summary 여정계획상세 조회
     * @param {DriverItineraryPlanApiGetItineraryPlanDetailRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryPlanApi
     */
    public getItineraryPlanDetail(requestParameters: DriverItineraryPlanApiGetItineraryPlanDetailRequest, options?: RawAxiosRequestConfig) {
        return DriverItineraryPlanApiFp(this.configuration).getItineraryPlanDetail(requestParameters.itineraryPlanId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 현재 등록된 여정계획 목록을 조회합니다.
     * @summary 여정계획목록 조회
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryPlanApi
     */
    public getItineraryPlanList(options?: RawAxiosRequestConfig) {
        return DriverItineraryPlanApiFp(this.configuration).getItineraryPlanList(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 여정계획 아이디에 해당하는 여정계획을 수정합니다.
     * @summary 여정계획 수정
     * @param {DriverItineraryPlanApiUpdateItineraryPlanRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryPlanApi
     */
    public updateItineraryPlan(requestParameters: DriverItineraryPlanApiUpdateItineraryPlanRequest, options?: RawAxiosRequestConfig) {
        return DriverItineraryPlanApiFp(this.configuration).updateItineraryPlan(requestParameters.itineraryPlanUpdateReqDTO, requestParameters.itineraryPlanId, options).then((request) => request(this.axios, this.basePath));
    }
}

