/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { PolicyAppProperties } from '../../../../src/api/generated/models';
/**
 * CommonPolicyApi - axios parameter creator
 * @export
 */
export const CommonPolicyApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 앱 정책을 조회합니다.
         * @summary 앱 정책 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppPolicy: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/policy/app`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CommonPolicyApi - functional programming interface
 * @export
 */
export const CommonPolicyApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CommonPolicyApiAxiosParamCreator(configuration)
    return {
        /**
         * 앱 정책을 조회합니다.
         * @summary 앱 정책 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAppPolicy(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PolicyAppProperties>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAppPolicy(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommonPolicyApi.getAppPolicy']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CommonPolicyApi - factory interface
 * @export
 */
export const CommonPolicyApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CommonPolicyApiFp(configuration)
    return {
        /**
         * 앱 정책을 조회합니다.
         * @summary 앱 정책 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAppPolicy(options?: RawAxiosRequestConfig): AxiosPromise<PolicyAppProperties> {
            return localVarFp.getAppPolicy(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * CommonPolicyApi - object-oriented interface
 * @export
 * @class CommonPolicyApi
 * @extends {BaseAPI}
 */
export class CommonPolicyApi extends BaseAPI {
    /**
     * 앱 정책을 조회합니다.
     * @summary 앱 정책 조회
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommonPolicyApi
     */
    public getAppPolicy(options?: RawAxiosRequestConfig) {
        return CommonPolicyApiFp(this.configuration).getAppPolicy(options).then((request) => request(this.axios, this.basePath));
    }
}

