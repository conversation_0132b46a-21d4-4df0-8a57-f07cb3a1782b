/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { AdminFleetCreateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelAdminFleetEquipmentListItemResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelAdminFleetListItemResDTO } from '../../../../src/api/generated/models';
/**
 * AdminFleetApi - axios parameter creator
 * @export
 */
export const AdminFleetApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 플릿을 생성합니다.
         * @summary 플릿 생성
         * @param {AdminFleetCreateReqDTO} adminFleetCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminFleet: async (adminFleetCreateReqDTO: AdminFleetCreateReqDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'adminFleetCreateReqDTO' is not null or undefined
            assertParamExists('createAdminFleet', 'adminFleetCreateReqDTO', adminFleetCreateReqDTO)
            const localVarPath = `/api/admin/fleet`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(adminFleetCreateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 플릿을 삭제합니다.
         * @summary 플릿 삭제
         * @param {Array<number>} fleetIdList 플릿아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAdminFleet: async (fleetIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'fleetIdList' is not null or undefined
            assertParamExists('deleteAdminFleet', 'fleetIdList', fleetIdList)
            const localVarPath = `/api/admin/fleet`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (fleetIdList) {
                localVarQueryParameter['fleetIdList'] = fleetIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 플릿에 등록가능한 장비 목록을 조회합니다.
         * @summary 플릿에 등록가능한 장비 목록 조회
         * @param {number} fleetId 플릿아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPageForRegistration: async (fleetId: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'fleetId' is not null or undefined
            assertParamExists('getAdminEquipmentPageForRegistration', 'fleetId', fleetId)
            const localVarPath = `/api/admin/fleet/equipment/for-registration/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (fleetId !== undefined) {
                localVarQueryParameter['fleetId'] = fleetId;
            }

            if (manufacturer !== undefined) {
                localVarQueryParameter['manufacturer'] = manufacturer;
            }

            if (modelName !== undefined) {
                localVarQueryParameter['modelName'] = modelName;
            }

            if (serialNo !== undefined) {
                localVarQueryParameter['serialNo'] = serialNo;
            }

            if (plateNo !== undefined) {
                localVarQueryParameter['plateNo'] = plateNo;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 플릿에 등록된 장비 목록을 조회합니다.
         * @summary 플릿에 등록된 장비 목록 조회
         * @param {number} fleetId 플릿아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (manufacturer,modelName,plateNo,serialNo 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminFleetEquipmentPage: async (fleetId: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'fleetId' is not null or undefined
            assertParamExists('getAdminFleetEquipmentPage', 'fleetId', fleetId)
            const localVarPath = `/api/admin/fleet/equipment/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (fleetId !== undefined) {
                localVarQueryParameter['fleetId'] = fleetId;
            }

            if (manufacturer !== undefined) {
                localVarQueryParameter['manufacturer'] = manufacturer;
            }

            if (modelName !== undefined) {
                localVarQueryParameter['modelName'] = modelName;
            }

            if (serialNo !== undefined) {
                localVarQueryParameter['serialNo'] = serialNo;
            }

            if (plateNo !== undefined) {
                localVarQueryParameter['plateNo'] = plateNo;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 플릿 목록을 조회합니다.
         * @summary 플릿 목록 조회
         * @param {string} [fleetName] 플릿명
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (fleetName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminFleetPage: async (fleetName?: string, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/admin/fleet/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (fleetName !== undefined) {
                localVarQueryParameter['fleetName'] = fleetName;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 플릿에 장비를 등록합니다.
         * @summary 플릿에 장비 등록
         * @param {number} fleetId 플릿아이디
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        registerAdminEquipmentListToFleet: async (fleetId: number, equipmentIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'fleetId' is not null or undefined
            assertParamExists('registerAdminEquipmentListToFleet', 'fleetId', fleetId)
            // verify required parameter 'equipmentIdList' is not null or undefined
            assertParamExists('registerAdminEquipmentListToFleet', 'equipmentIdList', equipmentIdList)
            const localVarPath = `/api/admin/fleet/equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (fleetId !== undefined) {
                localVarQueryParameter['fleetId'] = fleetId;
            }

            if (equipmentIdList) {
                localVarQueryParameter['equipmentIdList'] = equipmentIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 플릿에 등록된 장비를 해지합니다.
         * @summary 플릿에 등록된 장비 해지
         * @param {number} fleetId 플릿아이디
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        unregisterAdminEquipmentListFromFleet: async (fleetId: number, equipmentIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'fleetId' is not null or undefined
            assertParamExists('unregisterAdminEquipmentListFromFleet', 'fleetId', fleetId)
            // verify required parameter 'equipmentIdList' is not null or undefined
            assertParamExists('unregisterAdminEquipmentListFromFleet', 'equipmentIdList', equipmentIdList)
            const localVarPath = `/api/admin/fleet/equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (fleetId !== undefined) {
                localVarQueryParameter['fleetId'] = fleetId;
            }

            if (equipmentIdList) {
                localVarQueryParameter['equipmentIdList'] = equipmentIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AdminFleetApi - functional programming interface
 * @export
 */
export const AdminFleetApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AdminFleetApiAxiosParamCreator(configuration)
    return {
        /**
         * 플릿을 생성합니다.
         * @summary 플릿 생성
         * @param {AdminFleetCreateReqDTO} adminFleetCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createAdminFleet(adminFleetCreateReqDTO: AdminFleetCreateReqDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createAdminFleet(adminFleetCreateReqDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminFleetApi.createAdminFleet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 플릿을 삭제합니다.
         * @summary 플릿 삭제
         * @param {Array<number>} fleetIdList 플릿아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteAdminFleet(fleetIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteAdminFleet(fleetIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminFleetApi.deleteAdminFleet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 플릿에 등록가능한 장비 목록을 조회합니다.
         * @summary 플릿에 등록가능한 장비 목록 조회
         * @param {number} fleetId 플릿아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminEquipmentPageForRegistration(fleetId: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminFleetEquipmentListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminEquipmentPageForRegistration(fleetId, manufacturer, modelName, serialNo, plateNo, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminFleetApi.getAdminEquipmentPageForRegistration']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 플릿에 등록된 장비 목록을 조회합니다.
         * @summary 플릿에 등록된 장비 목록 조회
         * @param {number} fleetId 플릿아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (manufacturer,modelName,plateNo,serialNo 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminFleetEquipmentPage(fleetId: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminFleetEquipmentListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminFleetEquipmentPage(fleetId, manufacturer, modelName, serialNo, plateNo, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminFleetApi.getAdminFleetEquipmentPage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 플릿 목록을 조회합니다.
         * @summary 플릿 목록 조회
         * @param {string} [fleetName] 플릿명
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (fleetName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminFleetPage(fleetName?: string, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminFleetListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminFleetPage(fleetName, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminFleetApi.getAdminFleetPage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 플릿에 장비를 등록합니다.
         * @summary 플릿에 장비 등록
         * @param {number} fleetId 플릿아이디
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async registerAdminEquipmentListToFleet(fleetId: number, equipmentIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.registerAdminEquipmentListToFleet(fleetId, equipmentIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminFleetApi.registerAdminEquipmentListToFleet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 플릿에 등록된 장비를 해지합니다.
         * @summary 플릿에 등록된 장비 해지
         * @param {number} fleetId 플릿아이디
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async unregisterAdminEquipmentListFromFleet(fleetId: number, equipmentIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.unregisterAdminEquipmentListFromFleet(fleetId, equipmentIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminFleetApi.unregisterAdminEquipmentListFromFleet']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AdminFleetApi - factory interface
 * @export
 */
export const AdminFleetApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AdminFleetApiFp(configuration)
    return {
        /**
         * 플릿을 생성합니다.
         * @summary 플릿 생성
         * @param {AdminFleetApiCreateAdminFleetRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminFleet(requestParameters: AdminFleetApiCreateAdminFleetRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.createAdminFleet(requestParameters.adminFleetCreateReqDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 플릿을 삭제합니다.
         * @summary 플릿 삭제
         * @param {AdminFleetApiDeleteAdminFleetRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAdminFleet(requestParameters: AdminFleetApiDeleteAdminFleetRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteAdminFleet(requestParameters.fleetIdList, options).then((request) => request(axios, basePath));
        },
        /**
         * 플릿에 등록가능한 장비 목록을 조회합니다.
         * @summary 플릿에 등록가능한 장비 목록 조회
         * @param {AdminFleetApiGetAdminEquipmentPageForRegistrationRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPageForRegistration(requestParameters: AdminFleetApiGetAdminEquipmentPageForRegistrationRequest, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminFleetEquipmentListItemResDTO> {
            return localVarFp.getAdminEquipmentPageForRegistration(requestParameters.fleetId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 플릿에 등록된 장비 목록을 조회합니다.
         * @summary 플릿에 등록된 장비 목록 조회
         * @param {AdminFleetApiGetAdminFleetEquipmentPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminFleetEquipmentPage(requestParameters: AdminFleetApiGetAdminFleetEquipmentPageRequest, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminFleetEquipmentListItemResDTO> {
            return localVarFp.getAdminFleetEquipmentPage(requestParameters.fleetId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 플릿 목록을 조회합니다.
         * @summary 플릿 목록 조회
         * @param {AdminFleetApiGetAdminFleetPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminFleetPage(requestParameters: AdminFleetApiGetAdminFleetPageRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminFleetListItemResDTO> {
            return localVarFp.getAdminFleetPage(requestParameters.fleetName, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 플릿에 장비를 등록합니다.
         * @summary 플릿에 장비 등록
         * @param {AdminFleetApiRegisterAdminEquipmentListToFleetRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        registerAdminEquipmentListToFleet(requestParameters: AdminFleetApiRegisterAdminEquipmentListToFleetRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.registerAdminEquipmentListToFleet(requestParameters.fleetId, requestParameters.equipmentIdList, options).then((request) => request(axios, basePath));
        },
        /**
         * 플릿에 등록된 장비를 해지합니다.
         * @summary 플릿에 등록된 장비 해지
         * @param {AdminFleetApiUnregisterAdminEquipmentListFromFleetRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        unregisterAdminEquipmentListFromFleet(requestParameters: AdminFleetApiUnregisterAdminEquipmentListFromFleetRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.unregisterAdminEquipmentListFromFleet(requestParameters.fleetId, requestParameters.equipmentIdList, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createAdminFleet operation in AdminFleetApi.
 * @export
 * @interface AdminFleetApiCreateAdminFleetRequest
 */
export interface AdminFleetApiCreateAdminFleetRequest {
    /**
     * 
     * @type {AdminFleetCreateReqDTO}
     * @memberof AdminFleetApiCreateAdminFleet
     */
    readonly adminFleetCreateReqDTO: AdminFleetCreateReqDTO
}

/**
 * Request parameters for deleteAdminFleet operation in AdminFleetApi.
 * @export
 * @interface AdminFleetApiDeleteAdminFleetRequest
 */
export interface AdminFleetApiDeleteAdminFleetRequest {
    /**
     * 플릿아이디목록
     * @type {Array<number>}
     * @memberof AdminFleetApiDeleteAdminFleet
     */
    readonly fleetIdList: Array<number>
}

/**
 * Request parameters for getAdminEquipmentPageForRegistration operation in AdminFleetApi.
 * @export
 * @interface AdminFleetApiGetAdminEquipmentPageForRegistrationRequest
 */
export interface AdminFleetApiGetAdminEquipmentPageForRegistrationRequest {
    /**
     * 플릿아이디
     * @type {number}
     * @memberof AdminFleetApiGetAdminEquipmentPageForRegistration
     */
    readonly fleetId: number

    /**
     * 제조사
     * @type {string}
     * @memberof AdminFleetApiGetAdminEquipmentPageForRegistration
     */
    readonly manufacturer?: string

    /**
     * 모델명
     * @type {string}
     * @memberof AdminFleetApiGetAdminEquipmentPageForRegistration
     */
    readonly modelName?: string

    /**
     * VIN No
     * @type {string}
     * @memberof AdminFleetApiGetAdminEquipmentPageForRegistration
     */
    readonly serialNo?: string

    /**
     * 차량번호
     * @type {string}
     * @memberof AdminFleetApiGetAdminEquipmentPageForRegistration
     */
    readonly plateNo?: string

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminFleetApiGetAdminEquipmentPageForRegistration
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminFleetApiGetAdminEquipmentPageForRegistration
     */
    readonly size?: number

    /**
     * 정렬 조건 (driverName 등)
     * @type {string}
     * @memberof AdminFleetApiGetAdminEquipmentPageForRegistration
     */
    readonly sort?: string
}

/**
 * Request parameters for getAdminFleetEquipmentPage operation in AdminFleetApi.
 * @export
 * @interface AdminFleetApiGetAdminFleetEquipmentPageRequest
 */
export interface AdminFleetApiGetAdminFleetEquipmentPageRequest {
    /**
     * 플릿아이디
     * @type {number}
     * @memberof AdminFleetApiGetAdminFleetEquipmentPage
     */
    readonly fleetId: number

    /**
     * 제조사
     * @type {string}
     * @memberof AdminFleetApiGetAdminFleetEquipmentPage
     */
    readonly manufacturer?: string

    /**
     * 모델명
     * @type {string}
     * @memberof AdminFleetApiGetAdminFleetEquipmentPage
     */
    readonly modelName?: string

    /**
     * VIN No
     * @type {string}
     * @memberof AdminFleetApiGetAdminFleetEquipmentPage
     */
    readonly serialNo?: string

    /**
     * 차량번호
     * @type {string}
     * @memberof AdminFleetApiGetAdminFleetEquipmentPage
     */
    readonly plateNo?: string

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminFleetApiGetAdminFleetEquipmentPage
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminFleetApiGetAdminFleetEquipmentPage
     */
    readonly size?: number

    /**
     * 정렬 조건 (manufacturer,modelName,plateNo,serialNo 등)
     * @type {string}
     * @memberof AdminFleetApiGetAdminFleetEquipmentPage
     */
    readonly sort?: string
}

/**
 * Request parameters for getAdminFleetPage operation in AdminFleetApi.
 * @export
 * @interface AdminFleetApiGetAdminFleetPageRequest
 */
export interface AdminFleetApiGetAdminFleetPageRequest {
    /**
     * 플릿명
     * @type {string}
     * @memberof AdminFleetApiGetAdminFleetPage
     */
    readonly fleetName?: string

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminFleetApiGetAdminFleetPage
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminFleetApiGetAdminFleetPage
     */
    readonly size?: number

    /**
     * 정렬 조건 (fleetName 등)
     * @type {string}
     * @memberof AdminFleetApiGetAdminFleetPage
     */
    readonly sort?: string
}

/**
 * Request parameters for registerAdminEquipmentListToFleet operation in AdminFleetApi.
 * @export
 * @interface AdminFleetApiRegisterAdminEquipmentListToFleetRequest
 */
export interface AdminFleetApiRegisterAdminEquipmentListToFleetRequest {
    /**
     * 플릿아이디
     * @type {number}
     * @memberof AdminFleetApiRegisterAdminEquipmentListToFleet
     */
    readonly fleetId: number

    /**
     * 장비아이디목록
     * @type {Array<number>}
     * @memberof AdminFleetApiRegisterAdminEquipmentListToFleet
     */
    readonly equipmentIdList: Array<number>
}

/**
 * Request parameters for unregisterAdminEquipmentListFromFleet operation in AdminFleetApi.
 * @export
 * @interface AdminFleetApiUnregisterAdminEquipmentListFromFleetRequest
 */
export interface AdminFleetApiUnregisterAdminEquipmentListFromFleetRequest {
    /**
     * 플릿아이디
     * @type {number}
     * @memberof AdminFleetApiUnregisterAdminEquipmentListFromFleet
     */
    readonly fleetId: number

    /**
     * 장비아이디목록
     * @type {Array<number>}
     * @memberof AdminFleetApiUnregisterAdminEquipmentListFromFleet
     */
    readonly equipmentIdList: Array<number>
}

/**
 * AdminFleetApi - object-oriented interface
 * @export
 * @class AdminFleetApi
 * @extends {BaseAPI}
 */
export class AdminFleetApi extends BaseAPI {
    /**
     * 플릿을 생성합니다.
     * @summary 플릿 생성
     * @param {AdminFleetApiCreateAdminFleetRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminFleetApi
     */
    public createAdminFleet(requestParameters: AdminFleetApiCreateAdminFleetRequest, options?: RawAxiosRequestConfig) {
        return AdminFleetApiFp(this.configuration).createAdminFleet(requestParameters.adminFleetCreateReqDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 플릿을 삭제합니다.
     * @summary 플릿 삭제
     * @param {AdminFleetApiDeleteAdminFleetRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminFleetApi
     */
    public deleteAdminFleet(requestParameters: AdminFleetApiDeleteAdminFleetRequest, options?: RawAxiosRequestConfig) {
        return AdminFleetApiFp(this.configuration).deleteAdminFleet(requestParameters.fleetIdList, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 플릿에 등록가능한 장비 목록을 조회합니다.
     * @summary 플릿에 등록가능한 장비 목록 조회
     * @param {AdminFleetApiGetAdminEquipmentPageForRegistrationRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminFleetApi
     */
    public getAdminEquipmentPageForRegistration(requestParameters: AdminFleetApiGetAdminEquipmentPageForRegistrationRequest, options?: RawAxiosRequestConfig) {
        return AdminFleetApiFp(this.configuration).getAdminEquipmentPageForRegistration(requestParameters.fleetId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 플릿에 등록된 장비 목록을 조회합니다.
     * @summary 플릿에 등록된 장비 목록 조회
     * @param {AdminFleetApiGetAdminFleetEquipmentPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminFleetApi
     */
    public getAdminFleetEquipmentPage(requestParameters: AdminFleetApiGetAdminFleetEquipmentPageRequest, options?: RawAxiosRequestConfig) {
        return AdminFleetApiFp(this.configuration).getAdminFleetEquipmentPage(requestParameters.fleetId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 플릿 목록을 조회합니다.
     * @summary 플릿 목록 조회
     * @param {AdminFleetApiGetAdminFleetPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminFleetApi
     */
    public getAdminFleetPage(requestParameters: AdminFleetApiGetAdminFleetPageRequest = {}, options?: RawAxiosRequestConfig) {
        return AdminFleetApiFp(this.configuration).getAdminFleetPage(requestParameters.fleetName, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 플릿에 장비를 등록합니다.
     * @summary 플릿에 장비 등록
     * @param {AdminFleetApiRegisterAdminEquipmentListToFleetRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminFleetApi
     */
    public registerAdminEquipmentListToFleet(requestParameters: AdminFleetApiRegisterAdminEquipmentListToFleetRequest, options?: RawAxiosRequestConfig) {
        return AdminFleetApiFp(this.configuration).registerAdminEquipmentListToFleet(requestParameters.fleetId, requestParameters.equipmentIdList, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 플릿에 등록된 장비를 해지합니다.
     * @summary 플릿에 등록된 장비 해지
     * @param {AdminFleetApiUnregisterAdminEquipmentListFromFleetRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminFleetApi
     */
    public unregisterAdminEquipmentListFromFleet(requestParameters: AdminFleetApiUnregisterAdminEquipmentListFromFleetRequest, options?: RawAxiosRequestConfig) {
        return AdminFleetApiFp(this.configuration).unregisterAdminEquipmentListFromFleet(requestParameters.fleetId, requestParameters.equipmentIdList, options).then((request) => request(this.axios, this.basePath));
    }
}

