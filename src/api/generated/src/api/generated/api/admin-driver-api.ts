/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { AdminDriverAllDetailResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { AdminDriverCreateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { AdminDriverDetailResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { AdminDriverUpdateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelAdminDriverEquipmentListItemResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelAdminDriverListItemResDTO } from '../../../../src/api/generated/models';
/**
 * AdminDriverApi - axios parameter creator
 * @export
 */
export const AdminDriverApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 운전자를 생성합니다.
         * @summary 운전자 생성
         * @param {Array<AdminDriverCreateReqDTO>} adminDriverCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminDriver: async (adminDriverCreateReqDTO: Array<AdminDriverCreateReqDTO>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'adminDriverCreateReqDTO' is not null or undefined
            assertParamExists('createAdminDriver', 'adminDriverCreateReqDTO', adminDriverCreateReqDTO)
            const localVarPath = `/api/admin/driver`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(adminDriverCreateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 운전자를 삭제합니다.
         * @summary 운전자 삭제
         * @param {Array<number>} driverIdList 운전자아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAdminDriver: async (driverIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'driverIdList' is not null or undefined
            assertParamExists('deleteAdminDriver', 'driverIdList', driverIdList)
            const localVarPath = `/api/admin/driver`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (driverIdList) {
                localVarQueryParameter['driverIdList'] = driverIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 운전자 상세정보를 조회합니다.
         * @summary 운전자 상세정보 조회
         * @param {number} driverId 운전자아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDriver: async (driverId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'driverId' is not null or undefined
            assertParamExists('getAdminDriver', 'driverId', driverId)
            const localVarPath = `/api/admin/driver/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (driverId !== undefined) {
                localVarQueryParameter['driverId'] = driverId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 운전자 모든 상세정보를 조회합니다.
         * @summary 운전자 모든 상세정보 조회
         * @param {number} driverId 운전자아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDriverAllDetail: async (driverId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'driverId' is not null or undefined
            assertParamExists('getAdminDriverAllDetail', 'driverId', driverId)
            const localVarPath = `/api/admin/driver/all-detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (driverId !== undefined) {
                localVarQueryParameter['driverId'] = driverId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 운전자 목록을 조회합니다.
         * @summary 운전자 목록 조회
         * @param {number} [fleetId] 플릿아이디
         * @param {string} [driverName] 운전자명
         * @param {Array<GetAdminDriverPageDriverStatusListEnum>} [driverStatusList] 운전자상태목록
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDriverPage: async (fleetId?: number, driverName?: string, driverStatusList?: Array<GetAdminDriverPageDriverStatusListEnum>, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/admin/driver/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (fleetId !== undefined) {
                localVarQueryParameter['fleetId'] = fleetId;
            }

            if (driverName !== undefined) {
                localVarQueryParameter['driverName'] = driverName;
            }

            if (driverStatusList) {
                localVarQueryParameter['driverStatusList'] = driverStatusList;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 운전자에 등록가능한 장비 목록을 조회합니다.
         * @summary 운전자에 등록가능한 장비 목록 조회
         * @param {number} driverId 운전자아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPageForRegistration1: async (driverId: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'driverId' is not null or undefined
            assertParamExists('getAdminEquipmentPageForRegistration1', 'driverId', driverId)
            const localVarPath = `/api/admin/driver/equipment/for-registration/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (driverId !== undefined) {
                localVarQueryParameter['driverId'] = driverId;
            }

            if (manufacturer !== undefined) {
                localVarQueryParameter['manufacturer'] = manufacturer;
            }

            if (modelName !== undefined) {
                localVarQueryParameter['modelName'] = modelName;
            }

            if (serialNo !== undefined) {
                localVarQueryParameter['serialNo'] = serialNo;
            }

            if (plateNo !== undefined) {
                localVarQueryParameter['plateNo'] = plateNo;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 운전자에 등록된 장비 목록을 조회합니다.
         * @summary 운전자에 등록된 장비 목록 조회
         * @param {number} driverId 운전자아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (manufacturer,modelName,plateNo,serialNo 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPageOfDriver: async (driverId: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'driverId' is not null or undefined
            assertParamExists('getAdminEquipmentPageOfDriver', 'driverId', driverId)
            const localVarPath = `/api/admin/driver/equipment/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (driverId !== undefined) {
                localVarQueryParameter['driverId'] = driverId;
            }

            if (manufacturer !== undefined) {
                localVarQueryParameter['manufacturer'] = manufacturer;
            }

            if (modelName !== undefined) {
                localVarQueryParameter['modelName'] = modelName;
            }

            if (serialNo !== undefined) {
                localVarQueryParameter['serialNo'] = serialNo;
            }

            if (plateNo !== undefined) {
                localVarQueryParameter['plateNo'] = plateNo;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 운전자에 장비를 등록합니다.
         * @summary 운전자에 장비 등록
         * @param {number} driverId 운전자아이디
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        registerAdminEquipmentListToDriver: async (driverId: number, equipmentIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'driverId' is not null or undefined
            assertParamExists('registerAdminEquipmentListToDriver', 'driverId', driverId)
            // verify required parameter 'equipmentIdList' is not null or undefined
            assertParamExists('registerAdminEquipmentListToDriver', 'equipmentIdList', equipmentIdList)
            const localVarPath = `/api/admin/driver/equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (driverId !== undefined) {
                localVarQueryParameter['driverId'] = driverId;
            }

            if (equipmentIdList) {
                localVarQueryParameter['equipmentIdList'] = equipmentIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 운전자에 등록된 장비를 해지합니다.
         * @summary 운전자에 등록된 장비 해지
         * @param {number} driverId 운전자아이디
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        unregisterAdminEquipmentListFromDriver: async (driverId: number, equipmentIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'driverId' is not null or undefined
            assertParamExists('unregisterAdminEquipmentListFromDriver', 'driverId', driverId)
            // verify required parameter 'equipmentIdList' is not null or undefined
            assertParamExists('unregisterAdminEquipmentListFromDriver', 'equipmentIdList', equipmentIdList)
            const localVarPath = `/api/admin/driver/equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (driverId !== undefined) {
                localVarQueryParameter['driverId'] = driverId;
            }

            if (equipmentIdList) {
                localVarQueryParameter['equipmentIdList'] = equipmentIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 운전자를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 운전자 수정
         * @param {AdminDriverUpdateReqDTO} adminDriverUpdateReqDTO 
         * @param {number} driverId 운전자아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAdminDriver: async (adminDriverUpdateReqDTO: AdminDriverUpdateReqDTO, driverId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'adminDriverUpdateReqDTO' is not null or undefined
            assertParamExists('updateAdminDriver', 'adminDriverUpdateReqDTO', adminDriverUpdateReqDTO)
            // verify required parameter 'driverId' is not null or undefined
            assertParamExists('updateAdminDriver', 'driverId', driverId)
            const localVarPath = `/api/admin/driver`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (driverId !== undefined) {
                localVarQueryParameter['driverId'] = driverId;
            }


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(adminDriverUpdateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AdminDriverApi - functional programming interface
 * @export
 */
export const AdminDriverApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AdminDriverApiAxiosParamCreator(configuration)
    return {
        /**
         * 운전자를 생성합니다.
         * @summary 운전자 생성
         * @param {Array<AdminDriverCreateReqDTO>} adminDriverCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createAdminDriver(adminDriverCreateReqDTO: Array<AdminDriverCreateReqDTO>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createAdminDriver(adminDriverCreateReqDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDriverApi.createAdminDriver']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 운전자를 삭제합니다.
         * @summary 운전자 삭제
         * @param {Array<number>} driverIdList 운전자아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteAdminDriver(driverIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteAdminDriver(driverIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDriverApi.deleteAdminDriver']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 운전자 상세정보를 조회합니다.
         * @summary 운전자 상세정보 조회
         * @param {number} driverId 운전자아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminDriver(driverId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AdminDriverDetailResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminDriver(driverId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDriverApi.getAdminDriver']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 운전자 모든 상세정보를 조회합니다.
         * @summary 운전자 모든 상세정보 조회
         * @param {number} driverId 운전자아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminDriverAllDetail(driverId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AdminDriverAllDetailResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminDriverAllDetail(driverId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDriverApi.getAdminDriverAllDetail']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 운전자 목록을 조회합니다.
         * @summary 운전자 목록 조회
         * @param {number} [fleetId] 플릿아이디
         * @param {string} [driverName] 운전자명
         * @param {Array<GetAdminDriverPageDriverStatusListEnum>} [driverStatusList] 운전자상태목록
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminDriverPage(fleetId?: number, driverName?: string, driverStatusList?: Array<GetAdminDriverPageDriverStatusListEnum>, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminDriverListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminDriverPage(fleetId, driverName, driverStatusList, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDriverApi.getAdminDriverPage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 운전자에 등록가능한 장비 목록을 조회합니다.
         * @summary 운전자에 등록가능한 장비 목록 조회
         * @param {number} driverId 운전자아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminEquipmentPageForRegistration1(driverId: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminDriverEquipmentListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminEquipmentPageForRegistration1(driverId, manufacturer, modelName, serialNo, plateNo, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDriverApi.getAdminEquipmentPageForRegistration1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 운전자에 등록된 장비 목록을 조회합니다.
         * @summary 운전자에 등록된 장비 목록 조회
         * @param {number} driverId 운전자아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (manufacturer,modelName,plateNo,serialNo 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminEquipmentPageOfDriver(driverId: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminDriverEquipmentListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminEquipmentPageOfDriver(driverId, manufacturer, modelName, serialNo, plateNo, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDriverApi.getAdminEquipmentPageOfDriver']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 운전자에 장비를 등록합니다.
         * @summary 운전자에 장비 등록
         * @param {number} driverId 운전자아이디
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async registerAdminEquipmentListToDriver(driverId: number, equipmentIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.registerAdminEquipmentListToDriver(driverId, equipmentIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDriverApi.registerAdminEquipmentListToDriver']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 운전자에 등록된 장비를 해지합니다.
         * @summary 운전자에 등록된 장비 해지
         * @param {number} driverId 운전자아이디
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async unregisterAdminEquipmentListFromDriver(driverId: number, equipmentIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.unregisterAdminEquipmentListFromDriver(driverId, equipmentIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDriverApi.unregisterAdminEquipmentListFromDriver']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 운전자를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 운전자 수정
         * @param {AdminDriverUpdateReqDTO} adminDriverUpdateReqDTO 
         * @param {number} driverId 운전자아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateAdminDriver(adminDriverUpdateReqDTO: AdminDriverUpdateReqDTO, driverId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateAdminDriver(adminDriverUpdateReqDTO, driverId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDriverApi.updateAdminDriver']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AdminDriverApi - factory interface
 * @export
 */
export const AdminDriverApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AdminDriverApiFp(configuration)
    return {
        /**
         * 운전자를 생성합니다.
         * @summary 운전자 생성
         * @param {AdminDriverApiCreateAdminDriverRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminDriver(requestParameters: AdminDriverApiCreateAdminDriverRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.createAdminDriver(requestParameters.adminDriverCreateReqDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 운전자를 삭제합니다.
         * @summary 운전자 삭제
         * @param {AdminDriverApiDeleteAdminDriverRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAdminDriver(requestParameters: AdminDriverApiDeleteAdminDriverRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteAdminDriver(requestParameters.driverIdList, options).then((request) => request(axios, basePath));
        },
        /**
         * 운전자 상세정보를 조회합니다.
         * @summary 운전자 상세정보 조회
         * @param {AdminDriverApiGetAdminDriverRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDriver(requestParameters: AdminDriverApiGetAdminDriverRequest, options?: RawAxiosRequestConfig): AxiosPromise<AdminDriverDetailResDTO> {
            return localVarFp.getAdminDriver(requestParameters.driverId, options).then((request) => request(axios, basePath));
        },
        /**
         * 운전자 모든 상세정보를 조회합니다.
         * @summary 운전자 모든 상세정보 조회
         * @param {AdminDriverApiGetAdminDriverAllDetailRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDriverAllDetail(requestParameters: AdminDriverApiGetAdminDriverAllDetailRequest, options?: RawAxiosRequestConfig): AxiosPromise<AdminDriverAllDetailResDTO> {
            return localVarFp.getAdminDriverAllDetail(requestParameters.driverId, options).then((request) => request(axios, basePath));
        },
        /**
         * 운전자 목록을 조회합니다.
         * @summary 운전자 목록 조회
         * @param {AdminDriverApiGetAdminDriverPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDriverPage(requestParameters: AdminDriverApiGetAdminDriverPageRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminDriverListItemResDTO> {
            return localVarFp.getAdminDriverPage(requestParameters.fleetId, requestParameters.driverName, requestParameters.driverStatusList, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 운전자에 등록가능한 장비 목록을 조회합니다.
         * @summary 운전자에 등록가능한 장비 목록 조회
         * @param {AdminDriverApiGetAdminEquipmentPageForRegistration1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPageForRegistration1(requestParameters: AdminDriverApiGetAdminEquipmentPageForRegistration1Request, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminDriverEquipmentListItemResDTO> {
            return localVarFp.getAdminEquipmentPageForRegistration1(requestParameters.driverId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 운전자에 등록된 장비 목록을 조회합니다.
         * @summary 운전자에 등록된 장비 목록 조회
         * @param {AdminDriverApiGetAdminEquipmentPageOfDriverRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPageOfDriver(requestParameters: AdminDriverApiGetAdminEquipmentPageOfDriverRequest, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminDriverEquipmentListItemResDTO> {
            return localVarFp.getAdminEquipmentPageOfDriver(requestParameters.driverId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 운전자에 장비를 등록합니다.
         * @summary 운전자에 장비 등록
         * @param {AdminDriverApiRegisterAdminEquipmentListToDriverRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        registerAdminEquipmentListToDriver(requestParameters: AdminDriverApiRegisterAdminEquipmentListToDriverRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.registerAdminEquipmentListToDriver(requestParameters.driverId, requestParameters.equipmentIdList, options).then((request) => request(axios, basePath));
        },
        /**
         * 운전자에 등록된 장비를 해지합니다.
         * @summary 운전자에 등록된 장비 해지
         * @param {AdminDriverApiUnregisterAdminEquipmentListFromDriverRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        unregisterAdminEquipmentListFromDriver(requestParameters: AdminDriverApiUnregisterAdminEquipmentListFromDriverRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.unregisterAdminEquipmentListFromDriver(requestParameters.driverId, requestParameters.equipmentIdList, options).then((request) => request(axios, basePath));
        },
        /**
         * 운전자를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 운전자 수정
         * @param {AdminDriverApiUpdateAdminDriverRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAdminDriver(requestParameters: AdminDriverApiUpdateAdminDriverRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.updateAdminDriver(requestParameters.adminDriverUpdateReqDTO, requestParameters.driverId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createAdminDriver operation in AdminDriverApi.
 * @export
 * @interface AdminDriverApiCreateAdminDriverRequest
 */
export interface AdminDriverApiCreateAdminDriverRequest {
    /**
     * 
     * @type {Array<AdminDriverCreateReqDTO>}
     * @memberof AdminDriverApiCreateAdminDriver
     */
    readonly adminDriverCreateReqDTO: Array<AdminDriverCreateReqDTO>
}

/**
 * Request parameters for deleteAdminDriver operation in AdminDriverApi.
 * @export
 * @interface AdminDriverApiDeleteAdminDriverRequest
 */
export interface AdminDriverApiDeleteAdminDriverRequest {
    /**
     * 운전자아이디목록
     * @type {Array<number>}
     * @memberof AdminDriverApiDeleteAdminDriver
     */
    readonly driverIdList: Array<number>
}

/**
 * Request parameters for getAdminDriver operation in AdminDriverApi.
 * @export
 * @interface AdminDriverApiGetAdminDriverRequest
 */
export interface AdminDriverApiGetAdminDriverRequest {
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverApiGetAdminDriver
     */
    readonly driverId: number
}

/**
 * Request parameters for getAdminDriverAllDetail operation in AdminDriverApi.
 * @export
 * @interface AdminDriverApiGetAdminDriverAllDetailRequest
 */
export interface AdminDriverApiGetAdminDriverAllDetailRequest {
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverApiGetAdminDriverAllDetail
     */
    readonly driverId: number
}

/**
 * Request parameters for getAdminDriverPage operation in AdminDriverApi.
 * @export
 * @interface AdminDriverApiGetAdminDriverPageRequest
 */
export interface AdminDriverApiGetAdminDriverPageRequest {
    /**
     * 플릿아이디
     * @type {number}
     * @memberof AdminDriverApiGetAdminDriverPage
     */
    readonly fleetId?: number

    /**
     * 운전자명
     * @type {string}
     * @memberof AdminDriverApiGetAdminDriverPage
     */
    readonly driverName?: string

    /**
     * 운전자상태목록
     * @type {Array<'ON_DUTY' | 'IDLE'>}
     * @memberof AdminDriverApiGetAdminDriverPage
     */
    readonly driverStatusList?: Array<GetAdminDriverPageDriverStatusListEnum>

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminDriverApiGetAdminDriverPage
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminDriverApiGetAdminDriverPage
     */
    readonly size?: number

    /**
     * 정렬 조건 (driverName 등)
     * @type {string}
     * @memberof AdminDriverApiGetAdminDriverPage
     */
    readonly sort?: string
}

/**
 * Request parameters for getAdminEquipmentPageForRegistration1 operation in AdminDriverApi.
 * @export
 * @interface AdminDriverApiGetAdminEquipmentPageForRegistration1Request
 */
export interface AdminDriverApiGetAdminEquipmentPageForRegistration1Request {
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverApiGetAdminEquipmentPageForRegistration1
     */
    readonly driverId: number

    /**
     * 제조사
     * @type {string}
     * @memberof AdminDriverApiGetAdminEquipmentPageForRegistration1
     */
    readonly manufacturer?: string

    /**
     * 모델명
     * @type {string}
     * @memberof AdminDriverApiGetAdminEquipmentPageForRegistration1
     */
    readonly modelName?: string

    /**
     * VIN No
     * @type {string}
     * @memberof AdminDriverApiGetAdminEquipmentPageForRegistration1
     */
    readonly serialNo?: string

    /**
     * 차량번호
     * @type {string}
     * @memberof AdminDriverApiGetAdminEquipmentPageForRegistration1
     */
    readonly plateNo?: string

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminDriverApiGetAdminEquipmentPageForRegistration1
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminDriverApiGetAdminEquipmentPageForRegistration1
     */
    readonly size?: number

    /**
     * 정렬 조건 (driverName 등)
     * @type {string}
     * @memberof AdminDriverApiGetAdminEquipmentPageForRegistration1
     */
    readonly sort?: string
}

/**
 * Request parameters for getAdminEquipmentPageOfDriver operation in AdminDriverApi.
 * @export
 * @interface AdminDriverApiGetAdminEquipmentPageOfDriverRequest
 */
export interface AdminDriverApiGetAdminEquipmentPageOfDriverRequest {
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverApiGetAdminEquipmentPageOfDriver
     */
    readonly driverId: number

    /**
     * 제조사
     * @type {string}
     * @memberof AdminDriverApiGetAdminEquipmentPageOfDriver
     */
    readonly manufacturer?: string

    /**
     * 모델명
     * @type {string}
     * @memberof AdminDriverApiGetAdminEquipmentPageOfDriver
     */
    readonly modelName?: string

    /**
     * VIN No
     * @type {string}
     * @memberof AdminDriverApiGetAdminEquipmentPageOfDriver
     */
    readonly serialNo?: string

    /**
     * 차량번호
     * @type {string}
     * @memberof AdminDriverApiGetAdminEquipmentPageOfDriver
     */
    readonly plateNo?: string

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminDriverApiGetAdminEquipmentPageOfDriver
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminDriverApiGetAdminEquipmentPageOfDriver
     */
    readonly size?: number

    /**
     * 정렬 조건 (manufacturer,modelName,plateNo,serialNo 등)
     * @type {string}
     * @memberof AdminDriverApiGetAdminEquipmentPageOfDriver
     */
    readonly sort?: string
}

/**
 * Request parameters for registerAdminEquipmentListToDriver operation in AdminDriverApi.
 * @export
 * @interface AdminDriverApiRegisterAdminEquipmentListToDriverRequest
 */
export interface AdminDriverApiRegisterAdminEquipmentListToDriverRequest {
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverApiRegisterAdminEquipmentListToDriver
     */
    readonly driverId: number

    /**
     * 장비아이디목록
     * @type {Array<number>}
     * @memberof AdminDriverApiRegisterAdminEquipmentListToDriver
     */
    readonly equipmentIdList: Array<number>
}

/**
 * Request parameters for unregisterAdminEquipmentListFromDriver operation in AdminDriverApi.
 * @export
 * @interface AdminDriverApiUnregisterAdminEquipmentListFromDriverRequest
 */
export interface AdminDriverApiUnregisterAdminEquipmentListFromDriverRequest {
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverApiUnregisterAdminEquipmentListFromDriver
     */
    readonly driverId: number

    /**
     * 장비아이디목록
     * @type {Array<number>}
     * @memberof AdminDriverApiUnregisterAdminEquipmentListFromDriver
     */
    readonly equipmentIdList: Array<number>
}

/**
 * Request parameters for updateAdminDriver operation in AdminDriverApi.
 * @export
 * @interface AdminDriverApiUpdateAdminDriverRequest
 */
export interface AdminDriverApiUpdateAdminDriverRequest {
    /**
     * 
     * @type {AdminDriverUpdateReqDTO}
     * @memberof AdminDriverApiUpdateAdminDriver
     */
    readonly adminDriverUpdateReqDTO: AdminDriverUpdateReqDTO

    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverApiUpdateAdminDriver
     */
    readonly driverId: number
}

/**
 * AdminDriverApi - object-oriented interface
 * @export
 * @class AdminDriverApi
 * @extends {BaseAPI}
 */
export class AdminDriverApi extends BaseAPI {
    /**
     * 운전자를 생성합니다.
     * @summary 운전자 생성
     * @param {AdminDriverApiCreateAdminDriverRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDriverApi
     */
    public createAdminDriver(requestParameters: AdminDriverApiCreateAdminDriverRequest, options?: RawAxiosRequestConfig) {
        return AdminDriverApiFp(this.configuration).createAdminDriver(requestParameters.adminDriverCreateReqDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 운전자를 삭제합니다.
     * @summary 운전자 삭제
     * @param {AdminDriverApiDeleteAdminDriverRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDriverApi
     */
    public deleteAdminDriver(requestParameters: AdminDriverApiDeleteAdminDriverRequest, options?: RawAxiosRequestConfig) {
        return AdminDriverApiFp(this.configuration).deleteAdminDriver(requestParameters.driverIdList, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 운전자 상세정보를 조회합니다.
     * @summary 운전자 상세정보 조회
     * @param {AdminDriverApiGetAdminDriverRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDriverApi
     */
    public getAdminDriver(requestParameters: AdminDriverApiGetAdminDriverRequest, options?: RawAxiosRequestConfig) {
        return AdminDriverApiFp(this.configuration).getAdminDriver(requestParameters.driverId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 운전자 모든 상세정보를 조회합니다.
     * @summary 운전자 모든 상세정보 조회
     * @param {AdminDriverApiGetAdminDriverAllDetailRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDriverApi
     */
    public getAdminDriverAllDetail(requestParameters: AdminDriverApiGetAdminDriverAllDetailRequest, options?: RawAxiosRequestConfig) {
        return AdminDriverApiFp(this.configuration).getAdminDriverAllDetail(requestParameters.driverId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 운전자 목록을 조회합니다.
     * @summary 운전자 목록 조회
     * @param {AdminDriverApiGetAdminDriverPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDriverApi
     */
    public getAdminDriverPage(requestParameters: AdminDriverApiGetAdminDriverPageRequest = {}, options?: RawAxiosRequestConfig) {
        return AdminDriverApiFp(this.configuration).getAdminDriverPage(requestParameters.fleetId, requestParameters.driverName, requestParameters.driverStatusList, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 운전자에 등록가능한 장비 목록을 조회합니다.
     * @summary 운전자에 등록가능한 장비 목록 조회
     * @param {AdminDriverApiGetAdminEquipmentPageForRegistration1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDriverApi
     */
    public getAdminEquipmentPageForRegistration1(requestParameters: AdminDriverApiGetAdminEquipmentPageForRegistration1Request, options?: RawAxiosRequestConfig) {
        return AdminDriverApiFp(this.configuration).getAdminEquipmentPageForRegistration1(requestParameters.driverId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 운전자에 등록된 장비 목록을 조회합니다.
     * @summary 운전자에 등록된 장비 목록 조회
     * @param {AdminDriverApiGetAdminEquipmentPageOfDriverRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDriverApi
     */
    public getAdminEquipmentPageOfDriver(requestParameters: AdminDriverApiGetAdminEquipmentPageOfDriverRequest, options?: RawAxiosRequestConfig) {
        return AdminDriverApiFp(this.configuration).getAdminEquipmentPageOfDriver(requestParameters.driverId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 운전자에 장비를 등록합니다.
     * @summary 운전자에 장비 등록
     * @param {AdminDriverApiRegisterAdminEquipmentListToDriverRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDriverApi
     */
    public registerAdminEquipmentListToDriver(requestParameters: AdminDriverApiRegisterAdminEquipmentListToDriverRequest, options?: RawAxiosRequestConfig) {
        return AdminDriverApiFp(this.configuration).registerAdminEquipmentListToDriver(requestParameters.driverId, requestParameters.equipmentIdList, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 운전자에 등록된 장비를 해지합니다.
     * @summary 운전자에 등록된 장비 해지
     * @param {AdminDriverApiUnregisterAdminEquipmentListFromDriverRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDriverApi
     */
    public unregisterAdminEquipmentListFromDriver(requestParameters: AdminDriverApiUnregisterAdminEquipmentListFromDriverRequest, options?: RawAxiosRequestConfig) {
        return AdminDriverApiFp(this.configuration).unregisterAdminEquipmentListFromDriver(requestParameters.driverId, requestParameters.equipmentIdList, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 운전자를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
     * @summary 운전자 수정
     * @param {AdminDriverApiUpdateAdminDriverRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDriverApi
     */
    public updateAdminDriver(requestParameters: AdminDriverApiUpdateAdminDriverRequest, options?: RawAxiosRequestConfig) {
        return AdminDriverApiFp(this.configuration).updateAdminDriver(requestParameters.adminDriverUpdateReqDTO, requestParameters.driverId, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const GetAdminDriverPageDriverStatusListEnum = {
    OnDuty: 'ON_DUTY',
    Idle: 'IDLE'
} as const;
export type GetAdminDriverPageDriverStatusListEnum = typeof GetAdminDriverPageDriverStatusListEnum[keyof typeof GetAdminDriverPageDriverStatusListEnum];
