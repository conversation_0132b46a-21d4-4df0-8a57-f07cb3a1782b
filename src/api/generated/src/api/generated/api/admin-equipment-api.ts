/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { AdminEquipmentAllDetailResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { AdminEquipmentCreateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { AdminEquipmentDetailResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { AdminEquipmentUpdateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelAdminEquipmentDriverResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelAdminEquipmentListItemResDTO } from '../../../../src/api/generated/models';
/**
 * AdminEquipmentApi - axios parameter creator
 * @export
 */
export const AdminEquipmentApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 장비를 생성합니다.
         * @summary 장비 생성
         * @param {Array<AdminEquipmentCreateReqDTO>} adminEquipmentCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminEquipment: async (adminEquipmentCreateReqDTO: Array<AdminEquipmentCreateReqDTO>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'adminEquipmentCreateReqDTO' is not null or undefined
            assertParamExists('createAdminEquipment', 'adminEquipmentCreateReqDTO', adminEquipmentCreateReqDTO)
            const localVarPath = `/api/admin/equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(adminEquipmentCreateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비를 삭제합니다.
         * @summary 장비 삭제
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAdminEquipment: async (equipmentIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentIdList' is not null or undefined
            assertParamExists('deleteAdminEquipment', 'equipmentIdList', equipmentIdList)
            const localVarPath = `/api/admin/equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentIdList) {
                localVarQueryParameter['equipmentIdList'] = equipmentIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비에 등록가능한 운전자 목록을 조회합니다.
         * @summary 장비에 등록가능한 운전자 목록 조회
         * @param {number} equipmentId 장비아이디
         * @param {string} [driverName] 운전자명
         * @param {Array<GetAdminDriverPageForRegistrationDriverStatusListEnum>} [driverStatusList] 운전자상태목록
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDriverPageForRegistration: async (equipmentId: number, driverName?: string, driverStatusList?: Array<GetAdminDriverPageForRegistrationDriverStatusListEnum>, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('getAdminDriverPageForRegistration', 'equipmentId', equipmentId)
            const localVarPath = `/api/admin/equipment/driver/for-registration/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }

            if (driverName !== undefined) {
                localVarQueryParameter['driverName'] = driverName;
            }

            if (driverStatusList) {
                localVarQueryParameter['driverStatusList'] = driverStatusList;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비에 등록된 운전자 목록을 조회합니다.
         * @summary 장비에 등록된 운전자 목록 조회
         * @param {number} equipmentId 장비아이디
         * @param {string} [driverName] 운전자명
         * @param {Array<GetAdminDriverPageOfEquipmentDriverStatusListEnum>} [driverStatusList] 운전자상태목록
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDriverPageOfEquipment: async (equipmentId: number, driverName?: string, driverStatusList?: Array<GetAdminDriverPageOfEquipmentDriverStatusListEnum>, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('getAdminDriverPageOfEquipment', 'equipmentId', equipmentId)
            const localVarPath = `/api/admin/equipment/driver/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }

            if (driverName !== undefined) {
                localVarQueryParameter['driverName'] = driverName;
            }

            if (driverStatusList) {
                localVarQueryParameter['driverStatusList'] = driverStatusList;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비 상세정보를 조회합니다.
         * @summary 장비 상세정보 조회
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipment: async (equipmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('getAdminEquipment', 'equipmentId', equipmentId)
            const localVarPath = `/api/admin/equipment/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비 모든 상세정보를 조회합니다.
         * @summary 장비 모든 상세정보 조회
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentAllDetail: async (equipmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('getAdminEquipmentAllDetail', 'equipmentId', equipmentId)
            const localVarPath = `/api/admin/equipment/all-detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비 목록을 조회합니다.
         * @summary 장비 목록 조회
         * @param {number} [countryId] 국가아이디
         * @param {number} [dealerId] 딜러아이디
         * @param {number} [fleetId] 플릿아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (manufacturer,modelName,plateNo,serialNo,mileage 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPage: async (countryId?: number, dealerId?: number, fleetId?: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/admin/equipment/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (countryId !== undefined) {
                localVarQueryParameter['countryId'] = countryId;
            }

            if (dealerId !== undefined) {
                localVarQueryParameter['dealerId'] = dealerId;
            }

            if (fleetId !== undefined) {
                localVarQueryParameter['fleetId'] = fleetId;
            }

            if (manufacturer !== undefined) {
                localVarQueryParameter['manufacturer'] = manufacturer;
            }

            if (modelName !== undefined) {
                localVarQueryParameter['modelName'] = modelName;
            }

            if (serialNo !== undefined) {
                localVarQueryParameter['serialNo'] = serialNo;
            }

            if (plateNo !== undefined) {
                localVarQueryParameter['plateNo'] = plateNo;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비의 VIN No가 이미 등록되어 있는지 확인합니다.<br> 이미 등록되어 있으면 true, 등록되어 있지 않다면 false를 반환합니다. 
         * @summary 장비 VIN No 등록 확인
         * @param {string} serialNo VIN No
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        lookupAdminEquipmentSerialNo: async (serialNo: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'serialNo' is not null or undefined
            assertParamExists('lookupAdminEquipmentSerialNo', 'serialNo', serialNo)
            const localVarPath = `/api/admin/equipment/lookup`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (serialNo !== undefined) {
                localVarQueryParameter['serialNo'] = serialNo;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비에 운전자를 등록합니다.
         * @summary 장비에 운전자 등록
         * @param {number} equipmentId 장비아이디
         * @param {Array<number>} driverIdList 운전자아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        registerAdminDriverListToEquipment: async (equipmentId: number, driverIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('registerAdminDriverListToEquipment', 'equipmentId', equipmentId)
            // verify required parameter 'driverIdList' is not null or undefined
            assertParamExists('registerAdminDriverListToEquipment', 'driverIdList', driverIdList)
            const localVarPath = `/api/admin/equipment/driver`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }

            if (driverIdList) {
                localVarQueryParameter['driverIdList'] = driverIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비에 등록된 운전자를 해지합니다.
         * @summary 장비에 등록된 운전자 해지
         * @param {number} equipmentId 장비아이디
         * @param {Array<number>} driverIdList 운전자아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        unregisterAdminDriverListFromEquipment: async (equipmentId: number, driverIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('unregisterAdminDriverListFromEquipment', 'equipmentId', equipmentId)
            // verify required parameter 'driverIdList' is not null or undefined
            assertParamExists('unregisterAdminDriverListFromEquipment', 'driverIdList', driverIdList)
            const localVarPath = `/api/admin/equipment/driver`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }

            if (driverIdList) {
                localVarQueryParameter['driverIdList'] = driverIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 장비를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 장비 수정
         * @param {AdminEquipmentUpdateReqDTO} adminEquipmentUpdateReqDTO 
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAdminEquipment: async (adminEquipmentUpdateReqDTO: AdminEquipmentUpdateReqDTO, equipmentId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'adminEquipmentUpdateReqDTO' is not null or undefined
            assertParamExists('updateAdminEquipment', 'adminEquipmentUpdateReqDTO', adminEquipmentUpdateReqDTO)
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('updateAdminEquipment', 'equipmentId', equipmentId)
            const localVarPath = `/api/admin/equipment`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(adminEquipmentUpdateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AdminEquipmentApi - functional programming interface
 * @export
 */
export const AdminEquipmentApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AdminEquipmentApiAxiosParamCreator(configuration)
    return {
        /**
         * 장비를 생성합니다.
         * @summary 장비 생성
         * @param {Array<AdminEquipmentCreateReqDTO>} adminEquipmentCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createAdminEquipment(adminEquipmentCreateReqDTO: Array<AdminEquipmentCreateReqDTO>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createAdminEquipment(adminEquipmentCreateReqDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentApi.createAdminEquipment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비를 삭제합니다.
         * @summary 장비 삭제
         * @param {Array<number>} equipmentIdList 장비아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteAdminEquipment(equipmentIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteAdminEquipment(equipmentIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentApi.deleteAdminEquipment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비에 등록가능한 운전자 목록을 조회합니다.
         * @summary 장비에 등록가능한 운전자 목록 조회
         * @param {number} equipmentId 장비아이디
         * @param {string} [driverName] 운전자명
         * @param {Array<GetAdminDriverPageForRegistrationDriverStatusListEnum>} [driverStatusList] 운전자상태목록
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminDriverPageForRegistration(equipmentId: number, driverName?: string, driverStatusList?: Array<GetAdminDriverPageForRegistrationDriverStatusListEnum>, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminEquipmentDriverResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminDriverPageForRegistration(equipmentId, driverName, driverStatusList, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentApi.getAdminDriverPageForRegistration']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비에 등록된 운전자 목록을 조회합니다.
         * @summary 장비에 등록된 운전자 목록 조회
         * @param {number} equipmentId 장비아이디
         * @param {string} [driverName] 운전자명
         * @param {Array<GetAdminDriverPageOfEquipmentDriverStatusListEnum>} [driverStatusList] 운전자상태목록
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (driverName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminDriverPageOfEquipment(equipmentId: number, driverName?: string, driverStatusList?: Array<GetAdminDriverPageOfEquipmentDriverStatusListEnum>, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminEquipmentDriverResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminDriverPageOfEquipment(equipmentId, driverName, driverStatusList, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentApi.getAdminDriverPageOfEquipment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비 상세정보를 조회합니다.
         * @summary 장비 상세정보 조회
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminEquipment(equipmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AdminEquipmentDetailResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminEquipment(equipmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentApi.getAdminEquipment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비 모든 상세정보를 조회합니다.
         * @summary 장비 모든 상세정보 조회
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminEquipmentAllDetail(equipmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AdminEquipmentAllDetailResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminEquipmentAllDetail(equipmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentApi.getAdminEquipmentAllDetail']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비 목록을 조회합니다.
         * @summary 장비 목록 조회
         * @param {number} [countryId] 국가아이디
         * @param {number} [dealerId] 딜러아이디
         * @param {number} [fleetId] 플릿아이디
         * @param {string} [manufacturer] 제조사
         * @param {string} [modelName] 모델명
         * @param {string} [serialNo] VIN No
         * @param {string} [plateNo] 차량번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (manufacturer,modelName,plateNo,serialNo,mileage 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminEquipmentPage(countryId?: number, dealerId?: number, fleetId?: number, manufacturer?: string, modelName?: string, serialNo?: string, plateNo?: string, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminEquipmentListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminEquipmentPage(countryId, dealerId, fleetId, manufacturer, modelName, serialNo, plateNo, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentApi.getAdminEquipmentPage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비의 VIN No가 이미 등록되어 있는지 확인합니다.<br> 이미 등록되어 있으면 true, 등록되어 있지 않다면 false를 반환합니다. 
         * @summary 장비 VIN No 등록 확인
         * @param {string} serialNo VIN No
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async lookupAdminEquipmentSerialNo(serialNo: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<boolean>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.lookupAdminEquipmentSerialNo(serialNo, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentApi.lookupAdminEquipmentSerialNo']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비에 운전자를 등록합니다.
         * @summary 장비에 운전자 등록
         * @param {number} equipmentId 장비아이디
         * @param {Array<number>} driverIdList 운전자아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async registerAdminDriverListToEquipment(equipmentId: number, driverIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.registerAdminDriverListToEquipment(equipmentId, driverIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentApi.registerAdminDriverListToEquipment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비에 등록된 운전자를 해지합니다.
         * @summary 장비에 등록된 운전자 해지
         * @param {number} equipmentId 장비아이디
         * @param {Array<number>} driverIdList 운전자아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async unregisterAdminDriverListFromEquipment(equipmentId: number, driverIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.unregisterAdminDriverListFromEquipment(equipmentId, driverIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentApi.unregisterAdminDriverListFromEquipment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 장비를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 장비 수정
         * @param {AdminEquipmentUpdateReqDTO} adminEquipmentUpdateReqDTO 
         * @param {number} equipmentId 장비아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateAdminEquipment(adminEquipmentUpdateReqDTO: AdminEquipmentUpdateReqDTO, equipmentId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateAdminEquipment(adminEquipmentUpdateReqDTO, equipmentId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminEquipmentApi.updateAdminEquipment']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AdminEquipmentApi - factory interface
 * @export
 */
export const AdminEquipmentApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AdminEquipmentApiFp(configuration)
    return {
        /**
         * 장비를 생성합니다.
         * @summary 장비 생성
         * @param {AdminEquipmentApiCreateAdminEquipmentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminEquipment(requestParameters: AdminEquipmentApiCreateAdminEquipmentRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.createAdminEquipment(requestParameters.adminEquipmentCreateReqDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비를 삭제합니다.
         * @summary 장비 삭제
         * @param {AdminEquipmentApiDeleteAdminEquipmentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAdminEquipment(requestParameters: AdminEquipmentApiDeleteAdminEquipmentRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteAdminEquipment(requestParameters.equipmentIdList, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비에 등록가능한 운전자 목록을 조회합니다.
         * @summary 장비에 등록가능한 운전자 목록 조회
         * @param {AdminEquipmentApiGetAdminDriverPageForRegistrationRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDriverPageForRegistration(requestParameters: AdminEquipmentApiGetAdminDriverPageForRegistrationRequest, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminEquipmentDriverResDTO> {
            return localVarFp.getAdminDriverPageForRegistration(requestParameters.equipmentId, requestParameters.driverName, requestParameters.driverStatusList, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비에 등록된 운전자 목록을 조회합니다.
         * @summary 장비에 등록된 운전자 목록 조회
         * @param {AdminEquipmentApiGetAdminDriverPageOfEquipmentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDriverPageOfEquipment(requestParameters: AdminEquipmentApiGetAdminDriverPageOfEquipmentRequest, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminEquipmentDriverResDTO> {
            return localVarFp.getAdminDriverPageOfEquipment(requestParameters.equipmentId, requestParameters.driverName, requestParameters.driverStatusList, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비 상세정보를 조회합니다.
         * @summary 장비 상세정보 조회
         * @param {AdminEquipmentApiGetAdminEquipmentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipment(requestParameters: AdminEquipmentApiGetAdminEquipmentRequest, options?: RawAxiosRequestConfig): AxiosPromise<AdminEquipmentDetailResDTO> {
            return localVarFp.getAdminEquipment(requestParameters.equipmentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비 모든 상세정보를 조회합니다.
         * @summary 장비 모든 상세정보 조회
         * @param {AdminEquipmentApiGetAdminEquipmentAllDetailRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentAllDetail(requestParameters: AdminEquipmentApiGetAdminEquipmentAllDetailRequest, options?: RawAxiosRequestConfig): AxiosPromise<AdminEquipmentAllDetailResDTO> {
            return localVarFp.getAdminEquipmentAllDetail(requestParameters.equipmentId, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비 목록을 조회합니다.
         * @summary 장비 목록 조회
         * @param {AdminEquipmentApiGetAdminEquipmentPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminEquipmentPage(requestParameters: AdminEquipmentApiGetAdminEquipmentPageRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminEquipmentListItemResDTO> {
            return localVarFp.getAdminEquipmentPage(requestParameters.countryId, requestParameters.dealerId, requestParameters.fleetId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비의 VIN No가 이미 등록되어 있는지 확인합니다.<br> 이미 등록되어 있으면 true, 등록되어 있지 않다면 false를 반환합니다. 
         * @summary 장비 VIN No 등록 확인
         * @param {AdminEquipmentApiLookupAdminEquipmentSerialNoRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        lookupAdminEquipmentSerialNo(requestParameters: AdminEquipmentApiLookupAdminEquipmentSerialNoRequest, options?: RawAxiosRequestConfig): AxiosPromise<boolean> {
            return localVarFp.lookupAdminEquipmentSerialNo(requestParameters.serialNo, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비에 운전자를 등록합니다.
         * @summary 장비에 운전자 등록
         * @param {AdminEquipmentApiRegisterAdminDriverListToEquipmentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        registerAdminDriverListToEquipment(requestParameters: AdminEquipmentApiRegisterAdminDriverListToEquipmentRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.registerAdminDriverListToEquipment(requestParameters.equipmentId, requestParameters.driverIdList, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비에 등록된 운전자를 해지합니다.
         * @summary 장비에 등록된 운전자 해지
         * @param {AdminEquipmentApiUnregisterAdminDriverListFromEquipmentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        unregisterAdminDriverListFromEquipment(requestParameters: AdminEquipmentApiUnregisterAdminDriverListFromEquipmentRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.unregisterAdminDriverListFromEquipment(requestParameters.equipmentId, requestParameters.driverIdList, options).then((request) => request(axios, basePath));
        },
        /**
         * 장비를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 장비 수정
         * @param {AdminEquipmentApiUpdateAdminEquipmentRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAdminEquipment(requestParameters: AdminEquipmentApiUpdateAdminEquipmentRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.updateAdminEquipment(requestParameters.adminEquipmentUpdateReqDTO, requestParameters.equipmentId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createAdminEquipment operation in AdminEquipmentApi.
 * @export
 * @interface AdminEquipmentApiCreateAdminEquipmentRequest
 */
export interface AdminEquipmentApiCreateAdminEquipmentRequest {
    /**
     * 
     * @type {Array<AdminEquipmentCreateReqDTO>}
     * @memberof AdminEquipmentApiCreateAdminEquipment
     */
    readonly adminEquipmentCreateReqDTO: Array<AdminEquipmentCreateReqDTO>
}

/**
 * Request parameters for deleteAdminEquipment operation in AdminEquipmentApi.
 * @export
 * @interface AdminEquipmentApiDeleteAdminEquipmentRequest
 */
export interface AdminEquipmentApiDeleteAdminEquipmentRequest {
    /**
     * 장비아이디목록
     * @type {Array<number>}
     * @memberof AdminEquipmentApiDeleteAdminEquipment
     */
    readonly equipmentIdList: Array<number>
}

/**
 * Request parameters for getAdminDriverPageForRegistration operation in AdminEquipmentApi.
 * @export
 * @interface AdminEquipmentApiGetAdminDriverPageForRegistrationRequest
 */
export interface AdminEquipmentApiGetAdminDriverPageForRegistrationRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminDriverPageForRegistration
     */
    readonly equipmentId: number

    /**
     * 운전자명
     * @type {string}
     * @memberof AdminEquipmentApiGetAdminDriverPageForRegistration
     */
    readonly driverName?: string

    /**
     * 운전자상태목록
     * @type {Array<'ON_DUTY' | 'IDLE'>}
     * @memberof AdminEquipmentApiGetAdminDriverPageForRegistration
     */
    readonly driverStatusList?: Array<GetAdminDriverPageForRegistrationDriverStatusListEnum>

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminDriverPageForRegistration
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminDriverPageForRegistration
     */
    readonly size?: number

    /**
     * 정렬 조건 (driverName 등)
     * @type {string}
     * @memberof AdminEquipmentApiGetAdminDriverPageForRegistration
     */
    readonly sort?: string
}

/**
 * Request parameters for getAdminDriverPageOfEquipment operation in AdminEquipmentApi.
 * @export
 * @interface AdminEquipmentApiGetAdminDriverPageOfEquipmentRequest
 */
export interface AdminEquipmentApiGetAdminDriverPageOfEquipmentRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminDriverPageOfEquipment
     */
    readonly equipmentId: number

    /**
     * 운전자명
     * @type {string}
     * @memberof AdminEquipmentApiGetAdminDriverPageOfEquipment
     */
    readonly driverName?: string

    /**
     * 운전자상태목록
     * @type {Array<'ON_DUTY' | 'IDLE'>}
     * @memberof AdminEquipmentApiGetAdminDriverPageOfEquipment
     */
    readonly driverStatusList?: Array<GetAdminDriverPageOfEquipmentDriverStatusListEnum>

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminDriverPageOfEquipment
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminDriverPageOfEquipment
     */
    readonly size?: number

    /**
     * 정렬 조건 (driverName 등)
     * @type {string}
     * @memberof AdminEquipmentApiGetAdminDriverPageOfEquipment
     */
    readonly sort?: string
}

/**
 * Request parameters for getAdminEquipment operation in AdminEquipmentApi.
 * @export
 * @interface AdminEquipmentApiGetAdminEquipmentRequest
 */
export interface AdminEquipmentApiGetAdminEquipmentRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminEquipment
     */
    readonly equipmentId: number
}

/**
 * Request parameters for getAdminEquipmentAllDetail operation in AdminEquipmentApi.
 * @export
 * @interface AdminEquipmentApiGetAdminEquipmentAllDetailRequest
 */
export interface AdminEquipmentApiGetAdminEquipmentAllDetailRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminEquipmentAllDetail
     */
    readonly equipmentId: number
}

/**
 * Request parameters for getAdminEquipmentPage operation in AdminEquipmentApi.
 * @export
 * @interface AdminEquipmentApiGetAdminEquipmentPageRequest
 */
export interface AdminEquipmentApiGetAdminEquipmentPageRequest {
    /**
     * 국가아이디
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminEquipmentPage
     */
    readonly countryId?: number

    /**
     * 딜러아이디
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminEquipmentPage
     */
    readonly dealerId?: number

    /**
     * 플릿아이디
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminEquipmentPage
     */
    readonly fleetId?: number

    /**
     * 제조사
     * @type {string}
     * @memberof AdminEquipmentApiGetAdminEquipmentPage
     */
    readonly manufacturer?: string

    /**
     * 모델명
     * @type {string}
     * @memberof AdminEquipmentApiGetAdminEquipmentPage
     */
    readonly modelName?: string

    /**
     * VIN No
     * @type {string}
     * @memberof AdminEquipmentApiGetAdminEquipmentPage
     */
    readonly serialNo?: string

    /**
     * 차량번호
     * @type {string}
     * @memberof AdminEquipmentApiGetAdminEquipmentPage
     */
    readonly plateNo?: string

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminEquipmentPage
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminEquipmentApiGetAdminEquipmentPage
     */
    readonly size?: number

    /**
     * 정렬 조건 (manufacturer,modelName,plateNo,serialNo,mileage 등)
     * @type {string}
     * @memberof AdminEquipmentApiGetAdminEquipmentPage
     */
    readonly sort?: string
}

/**
 * Request parameters for lookupAdminEquipmentSerialNo operation in AdminEquipmentApi.
 * @export
 * @interface AdminEquipmentApiLookupAdminEquipmentSerialNoRequest
 */
export interface AdminEquipmentApiLookupAdminEquipmentSerialNoRequest {
    /**
     * VIN No
     * @type {string}
     * @memberof AdminEquipmentApiLookupAdminEquipmentSerialNo
     */
    readonly serialNo: string
}

/**
 * Request parameters for registerAdminDriverListToEquipment operation in AdminEquipmentApi.
 * @export
 * @interface AdminEquipmentApiRegisterAdminDriverListToEquipmentRequest
 */
export interface AdminEquipmentApiRegisterAdminDriverListToEquipmentRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentApiRegisterAdminDriverListToEquipment
     */
    readonly equipmentId: number

    /**
     * 운전자아이디목록
     * @type {Array<number>}
     * @memberof AdminEquipmentApiRegisterAdminDriverListToEquipment
     */
    readonly driverIdList: Array<number>
}

/**
 * Request parameters for unregisterAdminDriverListFromEquipment operation in AdminEquipmentApi.
 * @export
 * @interface AdminEquipmentApiUnregisterAdminDriverListFromEquipmentRequest
 */
export interface AdminEquipmentApiUnregisterAdminDriverListFromEquipmentRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentApiUnregisterAdminDriverListFromEquipment
     */
    readonly equipmentId: number

    /**
     * 운전자아이디목록
     * @type {Array<number>}
     * @memberof AdminEquipmentApiUnregisterAdminDriverListFromEquipment
     */
    readonly driverIdList: Array<number>
}

/**
 * Request parameters for updateAdminEquipment operation in AdminEquipmentApi.
 * @export
 * @interface AdminEquipmentApiUpdateAdminEquipmentRequest
 */
export interface AdminEquipmentApiUpdateAdminEquipmentRequest {
    /**
     * 
     * @type {AdminEquipmentUpdateReqDTO}
     * @memberof AdminEquipmentApiUpdateAdminEquipment
     */
    readonly adminEquipmentUpdateReqDTO: AdminEquipmentUpdateReqDTO

    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentApiUpdateAdminEquipment
     */
    readonly equipmentId: number
}

/**
 * AdminEquipmentApi - object-oriented interface
 * @export
 * @class AdminEquipmentApi
 * @extends {BaseAPI}
 */
export class AdminEquipmentApi extends BaseAPI {
    /**
     * 장비를 생성합니다.
     * @summary 장비 생성
     * @param {AdminEquipmentApiCreateAdminEquipmentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentApi
     */
    public createAdminEquipment(requestParameters: AdminEquipmentApiCreateAdminEquipmentRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentApiFp(this.configuration).createAdminEquipment(requestParameters.adminEquipmentCreateReqDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비를 삭제합니다.
     * @summary 장비 삭제
     * @param {AdminEquipmentApiDeleteAdminEquipmentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentApi
     */
    public deleteAdminEquipment(requestParameters: AdminEquipmentApiDeleteAdminEquipmentRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentApiFp(this.configuration).deleteAdminEquipment(requestParameters.equipmentIdList, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비에 등록가능한 운전자 목록을 조회합니다.
     * @summary 장비에 등록가능한 운전자 목록 조회
     * @param {AdminEquipmentApiGetAdminDriverPageForRegistrationRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentApi
     */
    public getAdminDriverPageForRegistration(requestParameters: AdminEquipmentApiGetAdminDriverPageForRegistrationRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentApiFp(this.configuration).getAdminDriverPageForRegistration(requestParameters.equipmentId, requestParameters.driverName, requestParameters.driverStatusList, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비에 등록된 운전자 목록을 조회합니다.
     * @summary 장비에 등록된 운전자 목록 조회
     * @param {AdminEquipmentApiGetAdminDriverPageOfEquipmentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentApi
     */
    public getAdminDriverPageOfEquipment(requestParameters: AdminEquipmentApiGetAdminDriverPageOfEquipmentRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentApiFp(this.configuration).getAdminDriverPageOfEquipment(requestParameters.equipmentId, requestParameters.driverName, requestParameters.driverStatusList, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비 상세정보를 조회합니다.
     * @summary 장비 상세정보 조회
     * @param {AdminEquipmentApiGetAdminEquipmentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentApi
     */
    public getAdminEquipment(requestParameters: AdminEquipmentApiGetAdminEquipmentRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentApiFp(this.configuration).getAdminEquipment(requestParameters.equipmentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비 모든 상세정보를 조회합니다.
     * @summary 장비 모든 상세정보 조회
     * @param {AdminEquipmentApiGetAdminEquipmentAllDetailRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentApi
     */
    public getAdminEquipmentAllDetail(requestParameters: AdminEquipmentApiGetAdminEquipmentAllDetailRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentApiFp(this.configuration).getAdminEquipmentAllDetail(requestParameters.equipmentId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비 목록을 조회합니다.
     * @summary 장비 목록 조회
     * @param {AdminEquipmentApiGetAdminEquipmentPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentApi
     */
    public getAdminEquipmentPage(requestParameters: AdminEquipmentApiGetAdminEquipmentPageRequest = {}, options?: RawAxiosRequestConfig) {
        return AdminEquipmentApiFp(this.configuration).getAdminEquipmentPage(requestParameters.countryId, requestParameters.dealerId, requestParameters.fleetId, requestParameters.manufacturer, requestParameters.modelName, requestParameters.serialNo, requestParameters.plateNo, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비의 VIN No가 이미 등록되어 있는지 확인합니다.<br> 이미 등록되어 있으면 true, 등록되어 있지 않다면 false를 반환합니다. 
     * @summary 장비 VIN No 등록 확인
     * @param {AdminEquipmentApiLookupAdminEquipmentSerialNoRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentApi
     */
    public lookupAdminEquipmentSerialNo(requestParameters: AdminEquipmentApiLookupAdminEquipmentSerialNoRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentApiFp(this.configuration).lookupAdminEquipmentSerialNo(requestParameters.serialNo, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비에 운전자를 등록합니다.
     * @summary 장비에 운전자 등록
     * @param {AdminEquipmentApiRegisterAdminDriverListToEquipmentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentApi
     */
    public registerAdminDriverListToEquipment(requestParameters: AdminEquipmentApiRegisterAdminDriverListToEquipmentRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentApiFp(this.configuration).registerAdminDriverListToEquipment(requestParameters.equipmentId, requestParameters.driverIdList, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비에 등록된 운전자를 해지합니다.
     * @summary 장비에 등록된 운전자 해지
     * @param {AdminEquipmentApiUnregisterAdminDriverListFromEquipmentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentApi
     */
    public unregisterAdminDriverListFromEquipment(requestParameters: AdminEquipmentApiUnregisterAdminDriverListFromEquipmentRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentApiFp(this.configuration).unregisterAdminDriverListFromEquipment(requestParameters.equipmentId, requestParameters.driverIdList, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 장비를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
     * @summary 장비 수정
     * @param {AdminEquipmentApiUpdateAdminEquipmentRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminEquipmentApi
     */
    public updateAdminEquipment(requestParameters: AdminEquipmentApiUpdateAdminEquipmentRequest, options?: RawAxiosRequestConfig) {
        return AdminEquipmentApiFp(this.configuration).updateAdminEquipment(requestParameters.adminEquipmentUpdateReqDTO, requestParameters.equipmentId, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const GetAdminDriverPageForRegistrationDriverStatusListEnum = {
    OnDuty: 'ON_DUTY',
    Idle: 'IDLE'
} as const;
export type GetAdminDriverPageForRegistrationDriverStatusListEnum = typeof GetAdminDriverPageForRegistrationDriverStatusListEnum[keyof typeof GetAdminDriverPageForRegistrationDriverStatusListEnum];
/**
 * @export
 */
export const GetAdminDriverPageOfEquipmentDriverStatusListEnum = {
    OnDuty: 'ON_DUTY',
    Idle: 'IDLE'
} as const;
export type GetAdminDriverPageOfEquipmentDriverStatusListEnum = typeof GetAdminDriverPageOfEquipmentDriverStatusListEnum[keyof typeof GetAdminDriverPageOfEquipmentDriverStatusListEnum];
