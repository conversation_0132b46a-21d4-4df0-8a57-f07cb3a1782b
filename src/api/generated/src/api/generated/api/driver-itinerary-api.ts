/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { ItineraryCountResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ItineraryCreateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ItineraryCreateResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ItineraryDetailResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ItineraryListItemResDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ItineraryTrackCreateReqDTO } from '../../../../src/api/generated/models';
/**
 * DriverItineraryApi - axios parameter creator
 * @export
 */
export const DriverItineraryApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 여정경로 아이디에 해당하는 여정경로를 종료합니다.<br> 내비게이션에서 경유지 및 최종목적지 도착 시에 각종 정보와 함께 호출해야 합니다. 
         * @summary 여정경로 도착
         * @param {number} itineraryRouteId 여정경로 아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        arriveItineraryRoute: async (itineraryRouteId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itineraryRouteId' is not null or undefined
            assertParamExists('arriveItineraryRoute', 'itineraryRouteId', itineraryRouteId)
            const localVarPath = `/api/itinerary/route/arrival`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (itineraryRouteId !== undefined) {
                localVarQueryParameter['itineraryRouteId'] = itineraryRouteId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 여정 아이디에 해당하는 여정을 종료합니다.<br> 내비게이션에서 안내 종료 시에 각종 정보와 함께 호출해야 합니다.<br> (사용자에 의한 수동 안내 종료 및 최종목적지 도착에 의한 자동 안내 종료)<br> 서버 관점에서 종료되지 않은 여정은 주행 중인 여정으로 판단될 수 밖에 없어서 조회 비용이 올라갑니다.<br> 따라서, \"내비게이션 안내 종료\" 뿐 아니라 \"앱 종료\" 등 이벤트 발생 시 호출해주세요. 
         * @summary 여정 종료
         * @param {number} itineraryId 여정 아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeItinerary: async (itineraryId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itineraryId' is not null or undefined
            assertParamExists('completeItinerary', 'itineraryId', itineraryId)
            const localVarPath = `/api/itinerary/completion`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (itineraryId !== undefined) {
                localVarQueryParameter['itineraryId'] = itineraryId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 신규 여정을 생성합니다.<br> 내비게이션에서 안내 시작 시에 각종 정보와 함께 호출해야 합니다.<br> 여정계획을 기반으로 한 여정이라면 Request Body의 itineraryPlanId(여정계획 아이디)를 설정한 후에 호출하고,<br> 그렇지 않다면 Request Body의 itineraryPlanId(여정계획 아이디)는 설정하지 않고 호출해야 합니다.<br> 여정 주행 중에 경유지나 목적지 변경이 발생하면, 기존 여정에 대해서는 \"여정 종료\"를 하고, 새로 \"여정 생성\"을 해야 합니다.<br> 응답에 포함된 itineraryId(여정 아이디)와 itineraryRouteIds(여정경로 아이디 목록)은 다른 API 호출 시 사용됩니다. 
         * @summary 여정 생성
         * @param {ItineraryCreateReqDTO} itineraryCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createItinerary: async (itineraryCreateReqDTO: ItineraryCreateReqDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itineraryCreateReqDTO' is not null or undefined
            assertParamExists('createItinerary', 'itineraryCreateReqDTO', itineraryCreateReqDTO)
            const localVarPath = `/api/itinerary`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(itineraryCreateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 차량 주행 중에 각종 정보와 함께 호출해야 합니다.<br> 화물잇고 과제에서 적용된 주행 속도에 따라 가변 시간 간격으로 호출해 주시기 바랍니다.<br> routeRemainingDistance, routeRemainingTime, routeDepartureTime, routeArrivalTime, itineraryRemainingDistance, itineraryRemainingTime 필드들은 이전 호출과 비교하여 변경사항이 있을때만 채워주시기 바랍니다. 
         * @summary 여정위치정보 생성
         * @param {ItineraryTrackCreateReqDTO} itineraryTrackCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createItineraryTrack: async (itineraryTrackCreateReqDTO: ItineraryTrackCreateReqDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itineraryTrackCreateReqDTO' is not null or undefined
            assertParamExists('createItineraryTrack', 'itineraryTrackCreateReqDTO', itineraryTrackCreateReqDTO)
            const localVarPath = `/api/itinerary/route/track`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(itineraryTrackCreateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 주어진 기준월의 각 날짜별 여정의 개수를 조회합니다.
         * @summary 여정개수 조회
         * @param {string} baseYearMonth 기준월
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getItineraryCountList: async (baseYearMonth: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'baseYearMonth' is not null or undefined
            assertParamExists('getItineraryCountList', 'baseYearMonth', baseYearMonth)
            const localVarPath = `/api/itinerary/count`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (baseYearMonth !== undefined) {
                localVarQueryParameter['baseYearMonth'] = baseYearMonth;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 여정 아이디에 해당하는 여정의 상세정보를 조회합니다.
         * @summary 여정상세 조회
         * @param {number} itineraryId 여정 아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getItineraryDetail: async (itineraryId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'itineraryId' is not null or undefined
            assertParamExists('getItineraryDetail', 'itineraryId', itineraryId)
            const localVarPath = `/api/itinerary/detail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (itineraryId !== undefined) {
                localVarQueryParameter['itineraryId'] = itineraryId;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 주어진 기준일의 여정 목록을 조회합니다.
         * @summary 여정목록 조회
         * @param {string} baseDate 기준일
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getItineraryList: async (baseDate: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'baseDate' is not null or undefined
            assertParamExists('getItineraryList', 'baseDate', baseDate)
            const localVarPath = `/api/itinerary/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (baseDate !== undefined) {
                localVarQueryParameter['baseDate'] = (baseDate as any instanceof Date) ?
                    (baseDate as any).toISOString().substring(0,10) :
                    baseDate;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DriverItineraryApi - functional programming interface
 * @export
 */
export const DriverItineraryApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = DriverItineraryApiAxiosParamCreator(configuration)
    return {
        /**
         * 여정경로 아이디에 해당하는 여정경로를 종료합니다.<br> 내비게이션에서 경유지 및 최종목적지 도착 시에 각종 정보와 함께 호출해야 합니다. 
         * @summary 여정경로 도착
         * @param {number} itineraryRouteId 여정경로 아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async arriveItineraryRoute(itineraryRouteId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.arriveItineraryRoute(itineraryRouteId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryApi.arriveItineraryRoute']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 여정 아이디에 해당하는 여정을 종료합니다.<br> 내비게이션에서 안내 종료 시에 각종 정보와 함께 호출해야 합니다.<br> (사용자에 의한 수동 안내 종료 및 최종목적지 도착에 의한 자동 안내 종료)<br> 서버 관점에서 종료되지 않은 여정은 주행 중인 여정으로 판단될 수 밖에 없어서 조회 비용이 올라갑니다.<br> 따라서, \"내비게이션 안내 종료\" 뿐 아니라 \"앱 종료\" 등 이벤트 발생 시 호출해주세요. 
         * @summary 여정 종료
         * @param {number} itineraryId 여정 아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async completeItinerary(itineraryId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.completeItinerary(itineraryId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryApi.completeItinerary']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 신규 여정을 생성합니다.<br> 내비게이션에서 안내 시작 시에 각종 정보와 함께 호출해야 합니다.<br> 여정계획을 기반으로 한 여정이라면 Request Body의 itineraryPlanId(여정계획 아이디)를 설정한 후에 호출하고,<br> 그렇지 않다면 Request Body의 itineraryPlanId(여정계획 아이디)는 설정하지 않고 호출해야 합니다.<br> 여정 주행 중에 경유지나 목적지 변경이 발생하면, 기존 여정에 대해서는 \"여정 종료\"를 하고, 새로 \"여정 생성\"을 해야 합니다.<br> 응답에 포함된 itineraryId(여정 아이디)와 itineraryRouteIds(여정경로 아이디 목록)은 다른 API 호출 시 사용됩니다. 
         * @summary 여정 생성
         * @param {ItineraryCreateReqDTO} itineraryCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createItinerary(itineraryCreateReqDTO: ItineraryCreateReqDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ItineraryCreateResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createItinerary(itineraryCreateReqDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryApi.createItinerary']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 차량 주행 중에 각종 정보와 함께 호출해야 합니다.<br> 화물잇고 과제에서 적용된 주행 속도에 따라 가변 시간 간격으로 호출해 주시기 바랍니다.<br> routeRemainingDistance, routeRemainingTime, routeDepartureTime, routeArrivalTime, itineraryRemainingDistance, itineraryRemainingTime 필드들은 이전 호출과 비교하여 변경사항이 있을때만 채워주시기 바랍니다. 
         * @summary 여정위치정보 생성
         * @param {ItineraryTrackCreateReqDTO} itineraryTrackCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createItineraryTrack(itineraryTrackCreateReqDTO: ItineraryTrackCreateReqDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createItineraryTrack(itineraryTrackCreateReqDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryApi.createItineraryTrack']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 주어진 기준월의 각 날짜별 여정의 개수를 조회합니다.
         * @summary 여정개수 조회
         * @param {string} baseYearMonth 기준월
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getItineraryCountList(baseYearMonth: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<ItineraryCountResDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getItineraryCountList(baseYearMonth, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryApi.getItineraryCountList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 여정 아이디에 해당하는 여정의 상세정보를 조회합니다.
         * @summary 여정상세 조회
         * @param {number} itineraryId 여정 아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getItineraryDetail(itineraryId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ItineraryDetailResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getItineraryDetail(itineraryId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryApi.getItineraryDetail']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 주어진 기준일의 여정 목록을 조회합니다.
         * @summary 여정목록 조회
         * @param {string} baseDate 기준일
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getItineraryList(baseDate: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<ItineraryListItemResDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getItineraryList(baseDate, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['DriverItineraryApi.getItineraryList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * DriverItineraryApi - factory interface
 * @export
 */
export const DriverItineraryApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = DriverItineraryApiFp(configuration)
    return {
        /**
         * 여정경로 아이디에 해당하는 여정경로를 종료합니다.<br> 내비게이션에서 경유지 및 최종목적지 도착 시에 각종 정보와 함께 호출해야 합니다. 
         * @summary 여정경로 도착
         * @param {DriverItineraryApiArriveItineraryRouteRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        arriveItineraryRoute(requestParameters: DriverItineraryApiArriveItineraryRouteRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.arriveItineraryRoute(requestParameters.itineraryRouteId, options).then((request) => request(axios, basePath));
        },
        /**
         * 여정 아이디에 해당하는 여정을 종료합니다.<br> 내비게이션에서 안내 종료 시에 각종 정보와 함께 호출해야 합니다.<br> (사용자에 의한 수동 안내 종료 및 최종목적지 도착에 의한 자동 안내 종료)<br> 서버 관점에서 종료되지 않은 여정은 주행 중인 여정으로 판단될 수 밖에 없어서 조회 비용이 올라갑니다.<br> 따라서, \"내비게이션 안내 종료\" 뿐 아니라 \"앱 종료\" 등 이벤트 발생 시 호출해주세요. 
         * @summary 여정 종료
         * @param {DriverItineraryApiCompleteItineraryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        completeItinerary(requestParameters: DriverItineraryApiCompleteItineraryRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.completeItinerary(requestParameters.itineraryId, options).then((request) => request(axios, basePath));
        },
        /**
         * 신규 여정을 생성합니다.<br> 내비게이션에서 안내 시작 시에 각종 정보와 함께 호출해야 합니다.<br> 여정계획을 기반으로 한 여정이라면 Request Body의 itineraryPlanId(여정계획 아이디)를 설정한 후에 호출하고,<br> 그렇지 않다면 Request Body의 itineraryPlanId(여정계획 아이디)는 설정하지 않고 호출해야 합니다.<br> 여정 주행 중에 경유지나 목적지 변경이 발생하면, 기존 여정에 대해서는 \"여정 종료\"를 하고, 새로 \"여정 생성\"을 해야 합니다.<br> 응답에 포함된 itineraryId(여정 아이디)와 itineraryRouteIds(여정경로 아이디 목록)은 다른 API 호출 시 사용됩니다. 
         * @summary 여정 생성
         * @param {DriverItineraryApiCreateItineraryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createItinerary(requestParameters: DriverItineraryApiCreateItineraryRequest, options?: RawAxiosRequestConfig): AxiosPromise<ItineraryCreateResDTO> {
            return localVarFp.createItinerary(requestParameters.itineraryCreateReqDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 차량 주행 중에 각종 정보와 함께 호출해야 합니다.<br> 화물잇고 과제에서 적용된 주행 속도에 따라 가변 시간 간격으로 호출해 주시기 바랍니다.<br> routeRemainingDistance, routeRemainingTime, routeDepartureTime, routeArrivalTime, itineraryRemainingDistance, itineraryRemainingTime 필드들은 이전 호출과 비교하여 변경사항이 있을때만 채워주시기 바랍니다. 
         * @summary 여정위치정보 생성
         * @param {DriverItineraryApiCreateItineraryTrackRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createItineraryTrack(requestParameters: DriverItineraryApiCreateItineraryTrackRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.createItineraryTrack(requestParameters.itineraryTrackCreateReqDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 주어진 기준월의 각 날짜별 여정의 개수를 조회합니다.
         * @summary 여정개수 조회
         * @param {DriverItineraryApiGetItineraryCountListRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getItineraryCountList(requestParameters: DriverItineraryApiGetItineraryCountListRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<ItineraryCountResDTO>> {
            return localVarFp.getItineraryCountList(requestParameters.baseYearMonth, options).then((request) => request(axios, basePath));
        },
        /**
         * 여정 아이디에 해당하는 여정의 상세정보를 조회합니다.
         * @summary 여정상세 조회
         * @param {DriverItineraryApiGetItineraryDetailRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getItineraryDetail(requestParameters: DriverItineraryApiGetItineraryDetailRequest, options?: RawAxiosRequestConfig): AxiosPromise<ItineraryDetailResDTO> {
            return localVarFp.getItineraryDetail(requestParameters.itineraryId, options).then((request) => request(axios, basePath));
        },
        /**
         * 주어진 기준일의 여정 목록을 조회합니다.
         * @summary 여정목록 조회
         * @param {DriverItineraryApiGetItineraryListRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getItineraryList(requestParameters: DriverItineraryApiGetItineraryListRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<ItineraryListItemResDTO>> {
            return localVarFp.getItineraryList(requestParameters.baseDate, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for arriveItineraryRoute operation in DriverItineraryApi.
 * @export
 * @interface DriverItineraryApiArriveItineraryRouteRequest
 */
export interface DriverItineraryApiArriveItineraryRouteRequest {
    /**
     * 여정경로 아이디
     * @type {number}
     * @memberof DriverItineraryApiArriveItineraryRoute
     */
    readonly itineraryRouteId: number
}

/**
 * Request parameters for completeItinerary operation in DriverItineraryApi.
 * @export
 * @interface DriverItineraryApiCompleteItineraryRequest
 */
export interface DriverItineraryApiCompleteItineraryRequest {
    /**
     * 여정 아이디
     * @type {number}
     * @memberof DriverItineraryApiCompleteItinerary
     */
    readonly itineraryId: number
}

/**
 * Request parameters for createItinerary operation in DriverItineraryApi.
 * @export
 * @interface DriverItineraryApiCreateItineraryRequest
 */
export interface DriverItineraryApiCreateItineraryRequest {
    /**
     * 
     * @type {ItineraryCreateReqDTO}
     * @memberof DriverItineraryApiCreateItinerary
     */
    readonly itineraryCreateReqDTO: ItineraryCreateReqDTO
}

/**
 * Request parameters for createItineraryTrack operation in DriverItineraryApi.
 * @export
 * @interface DriverItineraryApiCreateItineraryTrackRequest
 */
export interface DriverItineraryApiCreateItineraryTrackRequest {
    /**
     * 
     * @type {ItineraryTrackCreateReqDTO}
     * @memberof DriverItineraryApiCreateItineraryTrack
     */
    readonly itineraryTrackCreateReqDTO: ItineraryTrackCreateReqDTO
}

/**
 * Request parameters for getItineraryCountList operation in DriverItineraryApi.
 * @export
 * @interface DriverItineraryApiGetItineraryCountListRequest
 */
export interface DriverItineraryApiGetItineraryCountListRequest {
    /**
     * 기준월
     * @type {string}
     * @memberof DriverItineraryApiGetItineraryCountList
     */
    readonly baseYearMonth: string
}

/**
 * Request parameters for getItineraryDetail operation in DriverItineraryApi.
 * @export
 * @interface DriverItineraryApiGetItineraryDetailRequest
 */
export interface DriverItineraryApiGetItineraryDetailRequest {
    /**
     * 여정 아이디
     * @type {number}
     * @memberof DriverItineraryApiGetItineraryDetail
     */
    readonly itineraryId: number
}

/**
 * Request parameters for getItineraryList operation in DriverItineraryApi.
 * @export
 * @interface DriverItineraryApiGetItineraryListRequest
 */
export interface DriverItineraryApiGetItineraryListRequest {
    /**
     * 기준일
     * @type {string}
     * @memberof DriverItineraryApiGetItineraryList
     */
    readonly baseDate: string
}

/**
 * DriverItineraryApi - object-oriented interface
 * @export
 * @class DriverItineraryApi
 * @extends {BaseAPI}
 */
export class DriverItineraryApi extends BaseAPI {
    /**
     * 여정경로 아이디에 해당하는 여정경로를 종료합니다.<br> 내비게이션에서 경유지 및 최종목적지 도착 시에 각종 정보와 함께 호출해야 합니다. 
     * @summary 여정경로 도착
     * @param {DriverItineraryApiArriveItineraryRouteRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryApi
     */
    public arriveItineraryRoute(requestParameters: DriverItineraryApiArriveItineraryRouteRequest, options?: RawAxiosRequestConfig) {
        return DriverItineraryApiFp(this.configuration).arriveItineraryRoute(requestParameters.itineraryRouteId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 여정 아이디에 해당하는 여정을 종료합니다.<br> 내비게이션에서 안내 종료 시에 각종 정보와 함께 호출해야 합니다.<br> (사용자에 의한 수동 안내 종료 및 최종목적지 도착에 의한 자동 안내 종료)<br> 서버 관점에서 종료되지 않은 여정은 주행 중인 여정으로 판단될 수 밖에 없어서 조회 비용이 올라갑니다.<br> 따라서, \"내비게이션 안내 종료\" 뿐 아니라 \"앱 종료\" 등 이벤트 발생 시 호출해주세요. 
     * @summary 여정 종료
     * @param {DriverItineraryApiCompleteItineraryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryApi
     */
    public completeItinerary(requestParameters: DriverItineraryApiCompleteItineraryRequest, options?: RawAxiosRequestConfig) {
        return DriverItineraryApiFp(this.configuration).completeItinerary(requestParameters.itineraryId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 신규 여정을 생성합니다.<br> 내비게이션에서 안내 시작 시에 각종 정보와 함께 호출해야 합니다.<br> 여정계획을 기반으로 한 여정이라면 Request Body의 itineraryPlanId(여정계획 아이디)를 설정한 후에 호출하고,<br> 그렇지 않다면 Request Body의 itineraryPlanId(여정계획 아이디)는 설정하지 않고 호출해야 합니다.<br> 여정 주행 중에 경유지나 목적지 변경이 발생하면, 기존 여정에 대해서는 \"여정 종료\"를 하고, 새로 \"여정 생성\"을 해야 합니다.<br> 응답에 포함된 itineraryId(여정 아이디)와 itineraryRouteIds(여정경로 아이디 목록)은 다른 API 호출 시 사용됩니다. 
     * @summary 여정 생성
     * @param {DriverItineraryApiCreateItineraryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryApi
     */
    public createItinerary(requestParameters: DriverItineraryApiCreateItineraryRequest, options?: RawAxiosRequestConfig) {
        return DriverItineraryApiFp(this.configuration).createItinerary(requestParameters.itineraryCreateReqDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 차량 주행 중에 각종 정보와 함께 호출해야 합니다.<br> 화물잇고 과제에서 적용된 주행 속도에 따라 가변 시간 간격으로 호출해 주시기 바랍니다.<br> routeRemainingDistance, routeRemainingTime, routeDepartureTime, routeArrivalTime, itineraryRemainingDistance, itineraryRemainingTime 필드들은 이전 호출과 비교하여 변경사항이 있을때만 채워주시기 바랍니다. 
     * @summary 여정위치정보 생성
     * @param {DriverItineraryApiCreateItineraryTrackRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryApi
     */
    public createItineraryTrack(requestParameters: DriverItineraryApiCreateItineraryTrackRequest, options?: RawAxiosRequestConfig) {
        return DriverItineraryApiFp(this.configuration).createItineraryTrack(requestParameters.itineraryTrackCreateReqDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 주어진 기준월의 각 날짜별 여정의 개수를 조회합니다.
     * @summary 여정개수 조회
     * @param {DriverItineraryApiGetItineraryCountListRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryApi
     */
    public getItineraryCountList(requestParameters: DriverItineraryApiGetItineraryCountListRequest, options?: RawAxiosRequestConfig) {
        return DriverItineraryApiFp(this.configuration).getItineraryCountList(requestParameters.baseYearMonth, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 여정 아이디에 해당하는 여정의 상세정보를 조회합니다.
     * @summary 여정상세 조회
     * @param {DriverItineraryApiGetItineraryDetailRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryApi
     */
    public getItineraryDetail(requestParameters: DriverItineraryApiGetItineraryDetailRequest, options?: RawAxiosRequestConfig) {
        return DriverItineraryApiFp(this.configuration).getItineraryDetail(requestParameters.itineraryId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 주어진 기준일의 여정 목록을 조회합니다.
     * @summary 여정목록 조회
     * @param {DriverItineraryApiGetItineraryListRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DriverItineraryApi
     */
    public getItineraryList(requestParameters: DriverItineraryApiGetItineraryListRequest, options?: RawAxiosRequestConfig) {
        return DriverItineraryApiFp(this.configuration).getItineraryList(requestParameters.baseDate, options).then((request) => request(this.axios, this.basePath));
    }
}

