/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { AdminServiceCenterCreateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { AdminServiceCenterUpdateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelAdminServiceCenterListItemResDTO } from '../../../../src/api/generated/models';
/**
 * AdminServiceCenterApi - axios parameter creator
 * @export
 */
export const AdminServiceCenterApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 서비스센터를 생성합니다.
         * @summary 서비스센터 생성
         * @param {Array<AdminServiceCenterCreateReqDTO>} adminServiceCenterCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminServiceCenter: async (adminServiceCenterCreateReqDTO: Array<AdminServiceCenterCreateReqDTO>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'adminServiceCenterCreateReqDTO' is not null or undefined
            assertParamExists('createAdminServiceCenter', 'adminServiceCenterCreateReqDTO', adminServiceCenterCreateReqDTO)
            const localVarPath = `/api/admin/service-center`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(adminServiceCenterCreateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 서비스센터를 삭제합니다.
         * @summary 서비스센터 삭제
         * @param {Array<number>} serviceCenterIdList 서비스센터아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAdminServiceCenter: async (serviceCenterIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'serviceCenterIdList' is not null or undefined
            assertParamExists('deleteAdminServiceCenter', 'serviceCenterIdList', serviceCenterIdList)
            const localVarPath = `/api/admin/service-center`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (serviceCenterIdList) {
                localVarQueryParameter['serviceCenterIdList'] = serviceCenterIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 서비스센터 목록을 조회합니다.
         * @summary 서비스센터 목록 조회
         * @param {number} [countryId] 국가아이디
         * @param {string} [serviceCenterName] 서비스센터명
         * @param {string} [serviceCenterPhone] 서비스센터전화번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (serviceCenterName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminServiceCenterPage: async (countryId?: number, serviceCenterName?: string, serviceCenterPhone?: string, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/admin/service-center/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (countryId !== undefined) {
                localVarQueryParameter['countryId'] = countryId;
            }

            if (serviceCenterName !== undefined) {
                localVarQueryParameter['serviceCenterName'] = serviceCenterName;
            }

            if (serviceCenterPhone !== undefined) {
                localVarQueryParameter['serviceCenterPhone'] = serviceCenterPhone;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 서비스센터를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 서비스센터 수정
         * @param {AdminServiceCenterUpdateReqDTO} adminServiceCenterUpdateReqDTO 
         * @param {number} serviceCenterId 서비스센터아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAdminServiceCenter: async (adminServiceCenterUpdateReqDTO: AdminServiceCenterUpdateReqDTO, serviceCenterId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'adminServiceCenterUpdateReqDTO' is not null or undefined
            assertParamExists('updateAdminServiceCenter', 'adminServiceCenterUpdateReqDTO', adminServiceCenterUpdateReqDTO)
            // verify required parameter 'serviceCenterId' is not null or undefined
            assertParamExists('updateAdminServiceCenter', 'serviceCenterId', serviceCenterId)
            const localVarPath = `/api/admin/service-center`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (serviceCenterId !== undefined) {
                localVarQueryParameter['serviceCenterId'] = serviceCenterId;
            }


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(adminServiceCenterUpdateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AdminServiceCenterApi - functional programming interface
 * @export
 */
export const AdminServiceCenterApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AdminServiceCenterApiAxiosParamCreator(configuration)
    return {
        /**
         * 서비스센터를 생성합니다.
         * @summary 서비스센터 생성
         * @param {Array<AdminServiceCenterCreateReqDTO>} adminServiceCenterCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createAdminServiceCenter(adminServiceCenterCreateReqDTO: Array<AdminServiceCenterCreateReqDTO>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createAdminServiceCenter(adminServiceCenterCreateReqDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminServiceCenterApi.createAdminServiceCenter']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 서비스센터를 삭제합니다.
         * @summary 서비스센터 삭제
         * @param {Array<number>} serviceCenterIdList 서비스센터아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteAdminServiceCenter(serviceCenterIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteAdminServiceCenter(serviceCenterIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminServiceCenterApi.deleteAdminServiceCenter']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 서비스센터 목록을 조회합니다.
         * @summary 서비스센터 목록 조회
         * @param {number} [countryId] 국가아이디
         * @param {string} [serviceCenterName] 서비스센터명
         * @param {string} [serviceCenterPhone] 서비스센터전화번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (serviceCenterName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminServiceCenterPage(countryId?: number, serviceCenterName?: string, serviceCenterPhone?: string, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminServiceCenterListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminServiceCenterPage(countryId, serviceCenterName, serviceCenterPhone, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminServiceCenterApi.getAdminServiceCenterPage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 서비스센터를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 서비스센터 수정
         * @param {AdminServiceCenterUpdateReqDTO} adminServiceCenterUpdateReqDTO 
         * @param {number} serviceCenterId 서비스센터아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateAdminServiceCenter(adminServiceCenterUpdateReqDTO: AdminServiceCenterUpdateReqDTO, serviceCenterId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateAdminServiceCenter(adminServiceCenterUpdateReqDTO, serviceCenterId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminServiceCenterApi.updateAdminServiceCenter']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AdminServiceCenterApi - factory interface
 * @export
 */
export const AdminServiceCenterApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AdminServiceCenterApiFp(configuration)
    return {
        /**
         * 서비스센터를 생성합니다.
         * @summary 서비스센터 생성
         * @param {AdminServiceCenterApiCreateAdminServiceCenterRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminServiceCenter(requestParameters: AdminServiceCenterApiCreateAdminServiceCenterRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.createAdminServiceCenter(requestParameters.adminServiceCenterCreateReqDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 서비스센터를 삭제합니다.
         * @summary 서비스센터 삭제
         * @param {AdminServiceCenterApiDeleteAdminServiceCenterRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAdminServiceCenter(requestParameters: AdminServiceCenterApiDeleteAdminServiceCenterRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteAdminServiceCenter(requestParameters.serviceCenterIdList, options).then((request) => request(axios, basePath));
        },
        /**
         * 서비스센터 목록을 조회합니다.
         * @summary 서비스센터 목록 조회
         * @param {AdminServiceCenterApiGetAdminServiceCenterPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminServiceCenterPage(requestParameters: AdminServiceCenterApiGetAdminServiceCenterPageRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminServiceCenterListItemResDTO> {
            return localVarFp.getAdminServiceCenterPage(requestParameters.countryId, requestParameters.serviceCenterName, requestParameters.serviceCenterPhone, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 서비스센터를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 서비스센터 수정
         * @param {AdminServiceCenterApiUpdateAdminServiceCenterRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAdminServiceCenter(requestParameters: AdminServiceCenterApiUpdateAdminServiceCenterRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.updateAdminServiceCenter(requestParameters.adminServiceCenterUpdateReqDTO, requestParameters.serviceCenterId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createAdminServiceCenter operation in AdminServiceCenterApi.
 * @export
 * @interface AdminServiceCenterApiCreateAdminServiceCenterRequest
 */
export interface AdminServiceCenterApiCreateAdminServiceCenterRequest {
    /**
     * 
     * @type {Array<AdminServiceCenterCreateReqDTO>}
     * @memberof AdminServiceCenterApiCreateAdminServiceCenter
     */
    readonly adminServiceCenterCreateReqDTO: Array<AdminServiceCenterCreateReqDTO>
}

/**
 * Request parameters for deleteAdminServiceCenter operation in AdminServiceCenterApi.
 * @export
 * @interface AdminServiceCenterApiDeleteAdminServiceCenterRequest
 */
export interface AdminServiceCenterApiDeleteAdminServiceCenterRequest {
    /**
     * 서비스센터아이디목록
     * @type {Array<number>}
     * @memberof AdminServiceCenterApiDeleteAdminServiceCenter
     */
    readonly serviceCenterIdList: Array<number>
}

/**
 * Request parameters for getAdminServiceCenterPage operation in AdminServiceCenterApi.
 * @export
 * @interface AdminServiceCenterApiGetAdminServiceCenterPageRequest
 */
export interface AdminServiceCenterApiGetAdminServiceCenterPageRequest {
    /**
     * 국가아이디
     * @type {number}
     * @memberof AdminServiceCenterApiGetAdminServiceCenterPage
     */
    readonly countryId?: number

    /**
     * 서비스센터명
     * @type {string}
     * @memberof AdminServiceCenterApiGetAdminServiceCenterPage
     */
    readonly serviceCenterName?: string

    /**
     * 서비스센터전화번호
     * @type {string}
     * @memberof AdminServiceCenterApiGetAdminServiceCenterPage
     */
    readonly serviceCenterPhone?: string

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminServiceCenterApiGetAdminServiceCenterPage
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminServiceCenterApiGetAdminServiceCenterPage
     */
    readonly size?: number

    /**
     * 정렬 조건 (serviceCenterName 등)
     * @type {string}
     * @memberof AdminServiceCenterApiGetAdminServiceCenterPage
     */
    readonly sort?: string
}

/**
 * Request parameters for updateAdminServiceCenter operation in AdminServiceCenterApi.
 * @export
 * @interface AdminServiceCenterApiUpdateAdminServiceCenterRequest
 */
export interface AdminServiceCenterApiUpdateAdminServiceCenterRequest {
    /**
     * 
     * @type {AdminServiceCenterUpdateReqDTO}
     * @memberof AdminServiceCenterApiUpdateAdminServiceCenter
     */
    readonly adminServiceCenterUpdateReqDTO: AdminServiceCenterUpdateReqDTO

    /**
     * 서비스센터아이디
     * @type {number}
     * @memberof AdminServiceCenterApiUpdateAdminServiceCenter
     */
    readonly serviceCenterId: number
}

/**
 * AdminServiceCenterApi - object-oriented interface
 * @export
 * @class AdminServiceCenterApi
 * @extends {BaseAPI}
 */
export class AdminServiceCenterApi extends BaseAPI {
    /**
     * 서비스센터를 생성합니다.
     * @summary 서비스센터 생성
     * @param {AdminServiceCenterApiCreateAdminServiceCenterRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminServiceCenterApi
     */
    public createAdminServiceCenter(requestParameters: AdminServiceCenterApiCreateAdminServiceCenterRequest, options?: RawAxiosRequestConfig) {
        return AdminServiceCenterApiFp(this.configuration).createAdminServiceCenter(requestParameters.adminServiceCenterCreateReqDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 서비스센터를 삭제합니다.
     * @summary 서비스센터 삭제
     * @param {AdminServiceCenterApiDeleteAdminServiceCenterRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminServiceCenterApi
     */
    public deleteAdminServiceCenter(requestParameters: AdminServiceCenterApiDeleteAdminServiceCenterRequest, options?: RawAxiosRequestConfig) {
        return AdminServiceCenterApiFp(this.configuration).deleteAdminServiceCenter(requestParameters.serviceCenterIdList, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 서비스센터 목록을 조회합니다.
     * @summary 서비스센터 목록 조회
     * @param {AdminServiceCenterApiGetAdminServiceCenterPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminServiceCenterApi
     */
    public getAdminServiceCenterPage(requestParameters: AdminServiceCenterApiGetAdminServiceCenterPageRequest = {}, options?: RawAxiosRequestConfig) {
        return AdminServiceCenterApiFp(this.configuration).getAdminServiceCenterPage(requestParameters.countryId, requestParameters.serviceCenterName, requestParameters.serviceCenterPhone, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 서비스센터를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
     * @summary 서비스센터 수정
     * @param {AdminServiceCenterApiUpdateAdminServiceCenterRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminServiceCenterApi
     */
    public updateAdminServiceCenter(requestParameters: AdminServiceCenterApiUpdateAdminServiceCenterRequest, options?: RawAxiosRequestConfig) {
        return AdminServiceCenterApiFp(this.configuration).updateAdminServiceCenter(requestParameters.adminServiceCenterUpdateReqDTO, requestParameters.serviceCenterId, options).then((request) => request(this.axios, this.basePath));
    }
}

