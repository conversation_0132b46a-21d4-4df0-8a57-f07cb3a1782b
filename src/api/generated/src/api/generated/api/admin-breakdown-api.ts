/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { PagedModelAdminBreakdownListItemResDTO } from '../../../../src/api/generated/models';
/**
 * AdminBreakdownApi - axios parameter creator
 * @export
 */
export const AdminBreakdownApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 고장 정보를 조회합니다.
         * @summary 고장 정보 조회
         * @param {number} equipmentId 장비아이디
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (code,severity,status,occurredAt 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminBreakdownPage: async (equipmentId: number, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'equipmentId' is not null or undefined
            assertParamExists('getAdminBreakdownPage', 'equipmentId', equipmentId)
            const localVarPath = `/api/admin/breakdown/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (equipmentId !== undefined) {
                localVarQueryParameter['equipmentId'] = equipmentId;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AdminBreakdownApi - functional programming interface
 * @export
 */
export const AdminBreakdownApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AdminBreakdownApiAxiosParamCreator(configuration)
    return {
        /**
         * 고장 정보를 조회합니다.
         * @summary 고장 정보 조회
         * @param {number} equipmentId 장비아이디
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (code,severity,status,occurredAt 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminBreakdownPage(equipmentId: number, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminBreakdownListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminBreakdownPage(equipmentId, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminBreakdownApi.getAdminBreakdownPage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AdminBreakdownApi - factory interface
 * @export
 */
export const AdminBreakdownApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AdminBreakdownApiFp(configuration)
    return {
        /**
         * 고장 정보를 조회합니다.
         * @summary 고장 정보 조회
         * @param {AdminBreakdownApiGetAdminBreakdownPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminBreakdownPage(requestParameters: AdminBreakdownApiGetAdminBreakdownPageRequest, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminBreakdownListItemResDTO> {
            return localVarFp.getAdminBreakdownPage(requestParameters.equipmentId, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getAdminBreakdownPage operation in AdminBreakdownApi.
 * @export
 * @interface AdminBreakdownApiGetAdminBreakdownPageRequest
 */
export interface AdminBreakdownApiGetAdminBreakdownPageRequest {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminBreakdownApiGetAdminBreakdownPage
     */
    readonly equipmentId: number

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminBreakdownApiGetAdminBreakdownPage
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminBreakdownApiGetAdminBreakdownPage
     */
    readonly size?: number

    /**
     * 정렬 조건 (code,severity,status,occurredAt 등)
     * @type {string}
     * @memberof AdminBreakdownApiGetAdminBreakdownPage
     */
    readonly sort?: string
}

/**
 * AdminBreakdownApi - object-oriented interface
 * @export
 * @class AdminBreakdownApi
 * @extends {BaseAPI}
 */
export class AdminBreakdownApi extends BaseAPI {
    /**
     * 고장 정보를 조회합니다.
     * @summary 고장 정보 조회
     * @param {AdminBreakdownApiGetAdminBreakdownPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminBreakdownApi
     */
    public getAdminBreakdownPage(requestParameters: AdminBreakdownApiGetAdminBreakdownPageRequest, options?: RawAxiosRequestConfig) {
        return AdminBreakdownApiFp(this.configuration).getAdminBreakdownPage(requestParameters.equipmentId, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }
}

