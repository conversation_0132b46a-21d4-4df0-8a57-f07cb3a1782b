/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { AuthRequestDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { AuthResponseDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { ChangePasswordRequestDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { CheckAppleAccountDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { CheckAppleAccountResponseDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { CheckGoogleAccountDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { CheckGoogleAccountResponseDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { EmailVerificationCodeRequestDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { GoogleAuthRequestDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { RefreshTokenRequestDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { RegisterRequestDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { VerifyCodeRequestDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { VerifyCodeResponseDTO } from '../../../../src/api/generated/models';
/**
 * CommonAuthenticationApi - axios parameter creator
 * @export
 */
export const CommonAuthenticationApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 비밀번호 변경 프로세스( 비로그인 상태):  1. 이메일 인증코드 전송 (/api/auth/email/request-code)    ( 현재 보내는 이메일은 회사 메일만 가능합니다. 보내는 메일 회사 서버에서 막고 있습니다.) 2. 이메일을 확인하여 에서 인증코드 6자리 확인 3.  /api/auth/email/verify-code 에서 6자리 코드 입력후 이메일 인증함.     이때 completedVerificationId값 을 저장한다. 3. 받은 completedVerificationId로 비밀 번호 변경 진행  주의사항: - 인증코드 입력 후 5분 이내 가입 필요 (개발모드: 500분), 
         * @summary 비밀번호변경
         * @param {ChangePasswordRequestDTO} changePasswordRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        changePassword1: async (changePasswordRequestDTO: ChangePasswordRequestDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'changePasswordRequestDTO' is not null or undefined
            assertParamExists('changePassword1', 'changePasswordRequestDTO', changePasswordRequestDTO)
            const localVarPath = `/api/auth/change-password`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(changePasswordRequestDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Apple 토큰을 통해 계정 존재 여부를 확인합니다.  - Apple ID 토큰 유효성 검증 - 이메일 기반 계정 존재 여부 확인 - 계정이 존재하고 토큰이 유효한 경우 로그인 토큰 발급 
         * @summary Apple 계정 확인
         * @param {CheckAppleAccountDTO} checkAppleAccountDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        checkAppleAccount: async (checkAppleAccountDTO: CheckAppleAccountDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'checkAppleAccountDTO' is not null or undefined
            assertParamExists('checkAppleAccount', 'checkAppleAccountDTO', checkAppleAccountDTO)
            const localVarPath = `/api/auth/account/check-apple`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(checkAppleAccountDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Google 토큰을 통해 계정 존재 여부를 확인합니다.  - 토큰 유효성 검증 - 이메일 기반 계정 존재 여부 확인 - 계정이 존재하고 토큰이 유효한 경우 로그인 토큰 발급 
         * @summary Google 계정 확인
         * @param {CheckGoogleAccountDTO} checkGoogleAccountDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        checkGoogleAccount: async (checkGoogleAccountDTO: CheckGoogleAccountDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'checkGoogleAccountDTO' is not null or undefined
            assertParamExists('checkGoogleAccount', 'checkGoogleAccountDTO', checkGoogleAccountDTO)
            const localVarPath = `/api/auth/account/check-google`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(checkGoogleAccountDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Google 계정을 통한 로그인 또는 회원가입. Google OAuth를 통해 받은 사용자 정보로 인증을 수행합니다.  - 계정이 존재하는 경우: 로그인 처리 - 계정이 존재하지 않는 경우: 자동 회원가입 후 로그인 
         * @summary Google 계정 인증
         * @param {GoogleAuthRequestDTO} googleAuthRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        googleRegisterOrLogin: async (googleAuthRequestDTO: GoogleAuthRequestDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'googleAuthRequestDTO' is not null or undefined
            assertParamExists('googleRegisterOrLogin', 'googleAuthRequestDTO', googleAuthRequestDTO)
            const localVarPath = `/api/auth/google/authentification`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(googleAuthRequestDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 사용자 로그인을 처리합니다. 이메일과 비밀번호를 받아 인증을 수행하고, 성공 시 액세스 토큰과 리프레시 토큰을 반환합니다.  응답: - accessToken: API 호출에 사용할 JWT 토큰 (유효기간 1년 - 테스트 ) - refreshToken: 액세스 토큰 갱신에 사용할 토큰 (유효기간 1년) - loginId: 로그인한 사용자의 ID - 현재는 개발 모드로 인증 없이 호출 가능함 
         * @summary 로그인
         * @param {AuthRequestDTO} authRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        login: async (authRequestDTO: AuthRequestDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'authRequestDTO' is not null or undefined
            assertParamExists('login', 'authRequestDTO', authRequestDTO)
            const localVarPath = `/api/auth/login`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(authRequestDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 만료된 토큰을 갱신 합니다. RefreshToken 을 사용하여 새로운 AccessToken 을 발급합니다. 이때 새로운 refreshToken 을 발급합니다. 기존의 refreshToken 도 만료일까지 사용이 가능합니다만 새로운 refreshToken 을 저장하여 사용하여도 무방합니다. refreshToken 의 만료일은 1년 입니다. accessToken 의 만료일은 1일 입니다. 
         * @summary 토큰 갱신
         * @param {RefreshTokenRequestDTO} refreshTokenRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        refreshToken: async (refreshTokenRequestDTO: RefreshTokenRequestDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'refreshTokenRequestDTO' is not null or undefined
            assertParamExists('refreshToken', 'refreshTokenRequestDTO', refreshTokenRequestDTO)
            const localVarPath = `/api/auth/refresh-token`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(refreshTokenRequestDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 회원가입 프로세스:  1. 이메일 인증코드 전송 (/api/auth/email/request-code) 2. 인증코드 확인 (/api/auth/email/verify-code) 3. 받은 completedVerificationId로 회원가입 진행  주의사항: - 인증코드 입력 후 60분 이내 가입 필요 - 이메일 최대 길이: 20자 
         * @summary 회원가입
         * @param {RegisterRequestDTO} registerRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        register: async (registerRequestDTO: RegisterRequestDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'registerRequestDTO' is not null or undefined
            assertParamExists('register', 'registerRequestDTO', registerRequestDTO)
            const localVarPath = `/api/auth/register`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(registerRequestDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         *  회원 가입/ 패스워드 재설정을 위한 사용자의 이메일 인증 코드를 전송합니다. 패스워드 변경은  CHANGE_PASSWORD 로 설정합니다  이후에 인증 이후에 인증코드 확인을 진행합니다. ( /api/auth/email/verify-code ) 
         * @summary 이메일 인증 코드 전송
         * @param {EmailVerificationCodeRequestDTO} emailVerificationCodeRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        sendVerificationCode: async (emailVerificationCodeRequestDTO: EmailVerificationCodeRequestDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'emailVerificationCodeRequestDTO' is not null or undefined
            assertParamExists('sendVerificationCode', 'emailVerificationCodeRequestDTO', emailVerificationCodeRequestDTO)
            const localVarPath = `/api/auth/email/request-code`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(emailVerificationCodeRequestDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 이메일 인증 코드 전송 이후에 이메일을 확인한후 이메일을 통해 받은 인증 코드를 확인합니다. 수신한 completedVerificationId 값을 저장하여서 회원가입/패스워드 변경시 사용합니다 completedVerificationId 의 유효 기간은 60분 입니다. (이메일 전송후 확인 유효 기간은 5분(개발모드: 500분)) 
         * @summary 인증 코드 확인
         * @param {VerifyCodeRequestDTO} verifyCodeRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        verifyEmailVerificationCode: async (verifyCodeRequestDTO: VerifyCodeRequestDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'verifyCodeRequestDTO' is not null or undefined
            assertParamExists('verifyEmailVerificationCode', 'verifyCodeRequestDTO', verifyCodeRequestDTO)
            const localVarPath = `/api/auth/email/verify-code`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(verifyCodeRequestDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CommonAuthenticationApi - functional programming interface
 * @export
 */
export const CommonAuthenticationApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CommonAuthenticationApiAxiosParamCreator(configuration)
    return {
        /**
         * 비밀번호 변경 프로세스( 비로그인 상태):  1. 이메일 인증코드 전송 (/api/auth/email/request-code)    ( 현재 보내는 이메일은 회사 메일만 가능합니다. 보내는 메일 회사 서버에서 막고 있습니다.) 2. 이메일을 확인하여 에서 인증코드 6자리 확인 3.  /api/auth/email/verify-code 에서 6자리 코드 입력후 이메일 인증함.     이때 completedVerificationId값 을 저장한다. 3. 받은 completedVerificationId로 비밀 번호 변경 진행  주의사항: - 인증코드 입력 후 5분 이내 가입 필요 (개발모드: 500분), 
         * @summary 비밀번호변경
         * @param {ChangePasswordRequestDTO} changePasswordRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async changePassword1(changePasswordRequestDTO: ChangePasswordRequestDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<string>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.changePassword1(changePasswordRequestDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommonAuthenticationApi.changePassword1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Apple 토큰을 통해 계정 존재 여부를 확인합니다.  - Apple ID 토큰 유효성 검증 - 이메일 기반 계정 존재 여부 확인 - 계정이 존재하고 토큰이 유효한 경우 로그인 토큰 발급 
         * @summary Apple 계정 확인
         * @param {CheckAppleAccountDTO} checkAppleAccountDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async checkAppleAccount(checkAppleAccountDTO: CheckAppleAccountDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CheckAppleAccountResponseDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.checkAppleAccount(checkAppleAccountDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommonAuthenticationApi.checkAppleAccount']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Google 토큰을 통해 계정 존재 여부를 확인합니다.  - 토큰 유효성 검증 - 이메일 기반 계정 존재 여부 확인 - 계정이 존재하고 토큰이 유효한 경우 로그인 토큰 발급 
         * @summary Google 계정 확인
         * @param {CheckGoogleAccountDTO} checkGoogleAccountDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async checkGoogleAccount(checkGoogleAccountDTO: CheckGoogleAccountDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<CheckGoogleAccountResponseDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.checkGoogleAccount(checkGoogleAccountDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommonAuthenticationApi.checkGoogleAccount']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * Google 계정을 통한 로그인 또는 회원가입. Google OAuth를 통해 받은 사용자 정보로 인증을 수행합니다.  - 계정이 존재하는 경우: 로그인 처리 - 계정이 존재하지 않는 경우: 자동 회원가입 후 로그인 
         * @summary Google 계정 인증
         * @param {GoogleAuthRequestDTO} googleAuthRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async googleRegisterOrLogin(googleAuthRequestDTO: GoogleAuthRequestDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AuthResponseDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.googleRegisterOrLogin(googleAuthRequestDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommonAuthenticationApi.googleRegisterOrLogin']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 사용자 로그인을 처리합니다. 이메일과 비밀번호를 받아 인증을 수행하고, 성공 시 액세스 토큰과 리프레시 토큰을 반환합니다.  응답: - accessToken: API 호출에 사용할 JWT 토큰 (유효기간 1년 - 테스트 ) - refreshToken: 액세스 토큰 갱신에 사용할 토큰 (유효기간 1년) - loginId: 로그인한 사용자의 ID - 현재는 개발 모드로 인증 없이 호출 가능함 
         * @summary 로그인
         * @param {AuthRequestDTO} authRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async login(authRequestDTO: AuthRequestDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AuthResponseDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.login(authRequestDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommonAuthenticationApi.login']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 만료된 토큰을 갱신 합니다. RefreshToken 을 사용하여 새로운 AccessToken 을 발급합니다. 이때 새로운 refreshToken 을 발급합니다. 기존의 refreshToken 도 만료일까지 사용이 가능합니다만 새로운 refreshToken 을 저장하여 사용하여도 무방합니다. refreshToken 의 만료일은 1년 입니다. accessToken 의 만료일은 1일 입니다. 
         * @summary 토큰 갱신
         * @param {RefreshTokenRequestDTO} refreshTokenRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async refreshToken(refreshTokenRequestDTO: RefreshTokenRequestDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AuthResponseDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.refreshToken(refreshTokenRequestDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommonAuthenticationApi.refreshToken']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 회원가입 프로세스:  1. 이메일 인증코드 전송 (/api/auth/email/request-code) 2. 인증코드 확인 (/api/auth/email/verify-code) 3. 받은 completedVerificationId로 회원가입 진행  주의사항: - 인증코드 입력 후 60분 이내 가입 필요 - 이메일 최대 길이: 20자 
         * @summary 회원가입
         * @param {RegisterRequestDTO} registerRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async register(registerRequestDTO: RegisterRequestDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<AuthResponseDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.register(registerRequestDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommonAuthenticationApi.register']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         *  회원 가입/ 패스워드 재설정을 위한 사용자의 이메일 인증 코드를 전송합니다. 패스워드 변경은  CHANGE_PASSWORD 로 설정합니다  이후에 인증 이후에 인증코드 확인을 진행합니다. ( /api/auth/email/verify-code ) 
         * @summary 이메일 인증 코드 전송
         * @param {EmailVerificationCodeRequestDTO} emailVerificationCodeRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async sendVerificationCode(emailVerificationCodeRequestDTO: EmailVerificationCodeRequestDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<EmailVerificationCodeRequestDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.sendVerificationCode(emailVerificationCodeRequestDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommonAuthenticationApi.sendVerificationCode']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 이메일 인증 코드 전송 이후에 이메일을 확인한후 이메일을 통해 받은 인증 코드를 확인합니다. 수신한 completedVerificationId 값을 저장하여서 회원가입/패스워드 변경시 사용합니다 completedVerificationId 의 유효 기간은 60분 입니다. (이메일 전송후 확인 유효 기간은 5분(개발모드: 500분)) 
         * @summary 인증 코드 확인
         * @param {VerifyCodeRequestDTO} verifyCodeRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async verifyEmailVerificationCode(verifyCodeRequestDTO: VerifyCodeRequestDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<VerifyCodeResponseDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.verifyEmailVerificationCode(verifyCodeRequestDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommonAuthenticationApi.verifyEmailVerificationCode']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CommonAuthenticationApi - factory interface
 * @export
 */
export const CommonAuthenticationApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CommonAuthenticationApiFp(configuration)
    return {
        /**
         * 비밀번호 변경 프로세스( 비로그인 상태):  1. 이메일 인증코드 전송 (/api/auth/email/request-code)    ( 현재 보내는 이메일은 회사 메일만 가능합니다. 보내는 메일 회사 서버에서 막고 있습니다.) 2. 이메일을 확인하여 에서 인증코드 6자리 확인 3.  /api/auth/email/verify-code 에서 6자리 코드 입력후 이메일 인증함.     이때 completedVerificationId값 을 저장한다. 3. 받은 completedVerificationId로 비밀 번호 변경 진행  주의사항: - 인증코드 입력 후 5분 이내 가입 필요 (개발모드: 500분), 
         * @summary 비밀번호변경
         * @param {CommonAuthenticationApiChangePassword1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        changePassword1(requestParameters: CommonAuthenticationApiChangePassword1Request, options?: RawAxiosRequestConfig): AxiosPromise<string> {
            return localVarFp.changePassword1(requestParameters.changePasswordRequestDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * Apple 토큰을 통해 계정 존재 여부를 확인합니다.  - Apple ID 토큰 유효성 검증 - 이메일 기반 계정 존재 여부 확인 - 계정이 존재하고 토큰이 유효한 경우 로그인 토큰 발급 
         * @summary Apple 계정 확인
         * @param {CommonAuthenticationApiCheckAppleAccountRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        checkAppleAccount(requestParameters: CommonAuthenticationApiCheckAppleAccountRequest, options?: RawAxiosRequestConfig): AxiosPromise<CheckAppleAccountResponseDTO> {
            return localVarFp.checkAppleAccount(requestParameters.checkAppleAccountDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * Google 토큰을 통해 계정 존재 여부를 확인합니다.  - 토큰 유효성 검증 - 이메일 기반 계정 존재 여부 확인 - 계정이 존재하고 토큰이 유효한 경우 로그인 토큰 발급 
         * @summary Google 계정 확인
         * @param {CommonAuthenticationApiCheckGoogleAccountRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        checkGoogleAccount(requestParameters: CommonAuthenticationApiCheckGoogleAccountRequest, options?: RawAxiosRequestConfig): AxiosPromise<CheckGoogleAccountResponseDTO> {
            return localVarFp.checkGoogleAccount(requestParameters.checkGoogleAccountDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * Google 계정을 통한 로그인 또는 회원가입. Google OAuth를 통해 받은 사용자 정보로 인증을 수행합니다.  - 계정이 존재하는 경우: 로그인 처리 - 계정이 존재하지 않는 경우: 자동 회원가입 후 로그인 
         * @summary Google 계정 인증
         * @param {CommonAuthenticationApiGoogleRegisterOrLoginRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        googleRegisterOrLogin(requestParameters: CommonAuthenticationApiGoogleRegisterOrLoginRequest, options?: RawAxiosRequestConfig): AxiosPromise<AuthResponseDTO> {
            return localVarFp.googleRegisterOrLogin(requestParameters.googleAuthRequestDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 사용자 로그인을 처리합니다. 이메일과 비밀번호를 받아 인증을 수행하고, 성공 시 액세스 토큰과 리프레시 토큰을 반환합니다.  응답: - accessToken: API 호출에 사용할 JWT 토큰 (유효기간 1년 - 테스트 ) - refreshToken: 액세스 토큰 갱신에 사용할 토큰 (유효기간 1년) - loginId: 로그인한 사용자의 ID - 현재는 개발 모드로 인증 없이 호출 가능함 
         * @summary 로그인
         * @param {CommonAuthenticationApiLoginRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        login(requestParameters: CommonAuthenticationApiLoginRequest, options?: RawAxiosRequestConfig): AxiosPromise<AuthResponseDTO> {
            return localVarFp.login(requestParameters.authRequestDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 만료된 토큰을 갱신 합니다. RefreshToken 을 사용하여 새로운 AccessToken 을 발급합니다. 이때 새로운 refreshToken 을 발급합니다. 기존의 refreshToken 도 만료일까지 사용이 가능합니다만 새로운 refreshToken 을 저장하여 사용하여도 무방합니다. refreshToken 의 만료일은 1년 입니다. accessToken 의 만료일은 1일 입니다. 
         * @summary 토큰 갱신
         * @param {CommonAuthenticationApiRefreshTokenRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        refreshToken(requestParameters: CommonAuthenticationApiRefreshTokenRequest, options?: RawAxiosRequestConfig): AxiosPromise<AuthResponseDTO> {
            return localVarFp.refreshToken(requestParameters.refreshTokenRequestDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 회원가입 프로세스:  1. 이메일 인증코드 전송 (/api/auth/email/request-code) 2. 인증코드 확인 (/api/auth/email/verify-code) 3. 받은 completedVerificationId로 회원가입 진행  주의사항: - 인증코드 입력 후 60분 이내 가입 필요 - 이메일 최대 길이: 20자 
         * @summary 회원가입
         * @param {CommonAuthenticationApiRegisterRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        register(requestParameters: CommonAuthenticationApiRegisterRequest, options?: RawAxiosRequestConfig): AxiosPromise<AuthResponseDTO> {
            return localVarFp.register(requestParameters.registerRequestDTO, options).then((request) => request(axios, basePath));
        },
        /**
         *  회원 가입/ 패스워드 재설정을 위한 사용자의 이메일 인증 코드를 전송합니다. 패스워드 변경은  CHANGE_PASSWORD 로 설정합니다  이후에 인증 이후에 인증코드 확인을 진행합니다. ( /api/auth/email/verify-code ) 
         * @summary 이메일 인증 코드 전송
         * @param {CommonAuthenticationApiSendVerificationCodeRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        sendVerificationCode(requestParameters: CommonAuthenticationApiSendVerificationCodeRequest, options?: RawAxiosRequestConfig): AxiosPromise<EmailVerificationCodeRequestDTO> {
            return localVarFp.sendVerificationCode(requestParameters.emailVerificationCodeRequestDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 이메일 인증 코드 전송 이후에 이메일을 확인한후 이메일을 통해 받은 인증 코드를 확인합니다. 수신한 completedVerificationId 값을 저장하여서 회원가입/패스워드 변경시 사용합니다 completedVerificationId 의 유효 기간은 60분 입니다. (이메일 전송후 확인 유효 기간은 5분(개발모드: 500분)) 
         * @summary 인증 코드 확인
         * @param {CommonAuthenticationApiVerifyEmailVerificationCodeRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        verifyEmailVerificationCode(requestParameters: CommonAuthenticationApiVerifyEmailVerificationCodeRequest, options?: RawAxiosRequestConfig): AxiosPromise<VerifyCodeResponseDTO> {
            return localVarFp.verifyEmailVerificationCode(requestParameters.verifyCodeRequestDTO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for changePassword1 operation in CommonAuthenticationApi.
 * @export
 * @interface CommonAuthenticationApiChangePassword1Request
 */
export interface CommonAuthenticationApiChangePassword1Request {
    /**
     * 
     * @type {ChangePasswordRequestDTO}
     * @memberof CommonAuthenticationApiChangePassword1
     */
    readonly changePasswordRequestDTO: ChangePasswordRequestDTO
}

/**
 * Request parameters for checkAppleAccount operation in CommonAuthenticationApi.
 * @export
 * @interface CommonAuthenticationApiCheckAppleAccountRequest
 */
export interface CommonAuthenticationApiCheckAppleAccountRequest {
    /**
     * 
     * @type {CheckAppleAccountDTO}
     * @memberof CommonAuthenticationApiCheckAppleAccount
     */
    readonly checkAppleAccountDTO: CheckAppleAccountDTO
}

/**
 * Request parameters for checkGoogleAccount operation in CommonAuthenticationApi.
 * @export
 * @interface CommonAuthenticationApiCheckGoogleAccountRequest
 */
export interface CommonAuthenticationApiCheckGoogleAccountRequest {
    /**
     * 
     * @type {CheckGoogleAccountDTO}
     * @memberof CommonAuthenticationApiCheckGoogleAccount
     */
    readonly checkGoogleAccountDTO: CheckGoogleAccountDTO
}

/**
 * Request parameters for googleRegisterOrLogin operation in CommonAuthenticationApi.
 * @export
 * @interface CommonAuthenticationApiGoogleRegisterOrLoginRequest
 */
export interface CommonAuthenticationApiGoogleRegisterOrLoginRequest {
    /**
     * 
     * @type {GoogleAuthRequestDTO}
     * @memberof CommonAuthenticationApiGoogleRegisterOrLogin
     */
    readonly googleAuthRequestDTO: GoogleAuthRequestDTO
}

/**
 * Request parameters for login operation in CommonAuthenticationApi.
 * @export
 * @interface CommonAuthenticationApiLoginRequest
 */
export interface CommonAuthenticationApiLoginRequest {
    /**
     * 
     * @type {AuthRequestDTO}
     * @memberof CommonAuthenticationApiLogin
     */
    readonly authRequestDTO: AuthRequestDTO
}

/**
 * Request parameters for refreshToken operation in CommonAuthenticationApi.
 * @export
 * @interface CommonAuthenticationApiRefreshTokenRequest
 */
export interface CommonAuthenticationApiRefreshTokenRequest {
    /**
     * 
     * @type {RefreshTokenRequestDTO}
     * @memberof CommonAuthenticationApiRefreshToken
     */
    readonly refreshTokenRequestDTO: RefreshTokenRequestDTO
}

/**
 * Request parameters for register operation in CommonAuthenticationApi.
 * @export
 * @interface CommonAuthenticationApiRegisterRequest
 */
export interface CommonAuthenticationApiRegisterRequest {
    /**
     * 
     * @type {RegisterRequestDTO}
     * @memberof CommonAuthenticationApiRegister
     */
    readonly registerRequestDTO: RegisterRequestDTO
}

/**
 * Request parameters for sendVerificationCode operation in CommonAuthenticationApi.
 * @export
 * @interface CommonAuthenticationApiSendVerificationCodeRequest
 */
export interface CommonAuthenticationApiSendVerificationCodeRequest {
    /**
     * 
     * @type {EmailVerificationCodeRequestDTO}
     * @memberof CommonAuthenticationApiSendVerificationCode
     */
    readonly emailVerificationCodeRequestDTO: EmailVerificationCodeRequestDTO
}

/**
 * Request parameters for verifyEmailVerificationCode operation in CommonAuthenticationApi.
 * @export
 * @interface CommonAuthenticationApiVerifyEmailVerificationCodeRequest
 */
export interface CommonAuthenticationApiVerifyEmailVerificationCodeRequest {
    /**
     * 
     * @type {VerifyCodeRequestDTO}
     * @memberof CommonAuthenticationApiVerifyEmailVerificationCode
     */
    readonly verifyCodeRequestDTO: VerifyCodeRequestDTO
}

/**
 * CommonAuthenticationApi - object-oriented interface
 * @export
 * @class CommonAuthenticationApi
 * @extends {BaseAPI}
 */
export class CommonAuthenticationApi extends BaseAPI {
    /**
     * 비밀번호 변경 프로세스( 비로그인 상태):  1. 이메일 인증코드 전송 (/api/auth/email/request-code)    ( 현재 보내는 이메일은 회사 메일만 가능합니다. 보내는 메일 회사 서버에서 막고 있습니다.) 2. 이메일을 확인하여 에서 인증코드 6자리 확인 3.  /api/auth/email/verify-code 에서 6자리 코드 입력후 이메일 인증함.     이때 completedVerificationId값 을 저장한다. 3. 받은 completedVerificationId로 비밀 번호 변경 진행  주의사항: - 인증코드 입력 후 5분 이내 가입 필요 (개발모드: 500분), 
     * @summary 비밀번호변경
     * @param {CommonAuthenticationApiChangePassword1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommonAuthenticationApi
     */
    public changePassword1(requestParameters: CommonAuthenticationApiChangePassword1Request, options?: RawAxiosRequestConfig) {
        return CommonAuthenticationApiFp(this.configuration).changePassword1(requestParameters.changePasswordRequestDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Apple 토큰을 통해 계정 존재 여부를 확인합니다.  - Apple ID 토큰 유효성 검증 - 이메일 기반 계정 존재 여부 확인 - 계정이 존재하고 토큰이 유효한 경우 로그인 토큰 발급 
     * @summary Apple 계정 확인
     * @param {CommonAuthenticationApiCheckAppleAccountRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommonAuthenticationApi
     */
    public checkAppleAccount(requestParameters: CommonAuthenticationApiCheckAppleAccountRequest, options?: RawAxiosRequestConfig) {
        return CommonAuthenticationApiFp(this.configuration).checkAppleAccount(requestParameters.checkAppleAccountDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Google 토큰을 통해 계정 존재 여부를 확인합니다.  - 토큰 유효성 검증 - 이메일 기반 계정 존재 여부 확인 - 계정이 존재하고 토큰이 유효한 경우 로그인 토큰 발급 
     * @summary Google 계정 확인
     * @param {CommonAuthenticationApiCheckGoogleAccountRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommonAuthenticationApi
     */
    public checkGoogleAccount(requestParameters: CommonAuthenticationApiCheckGoogleAccountRequest, options?: RawAxiosRequestConfig) {
        return CommonAuthenticationApiFp(this.configuration).checkGoogleAccount(requestParameters.checkGoogleAccountDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * Google 계정을 통한 로그인 또는 회원가입. Google OAuth를 통해 받은 사용자 정보로 인증을 수행합니다.  - 계정이 존재하는 경우: 로그인 처리 - 계정이 존재하지 않는 경우: 자동 회원가입 후 로그인 
     * @summary Google 계정 인증
     * @param {CommonAuthenticationApiGoogleRegisterOrLoginRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommonAuthenticationApi
     */
    public googleRegisterOrLogin(requestParameters: CommonAuthenticationApiGoogleRegisterOrLoginRequest, options?: RawAxiosRequestConfig) {
        return CommonAuthenticationApiFp(this.configuration).googleRegisterOrLogin(requestParameters.googleAuthRequestDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 사용자 로그인을 처리합니다. 이메일과 비밀번호를 받아 인증을 수행하고, 성공 시 액세스 토큰과 리프레시 토큰을 반환합니다.  응답: - accessToken: API 호출에 사용할 JWT 토큰 (유효기간 1년 - 테스트 ) - refreshToken: 액세스 토큰 갱신에 사용할 토큰 (유효기간 1년) - loginId: 로그인한 사용자의 ID - 현재는 개발 모드로 인증 없이 호출 가능함 
     * @summary 로그인
     * @param {CommonAuthenticationApiLoginRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommonAuthenticationApi
     */
    public login(requestParameters: CommonAuthenticationApiLoginRequest, options?: RawAxiosRequestConfig) {
        return CommonAuthenticationApiFp(this.configuration).login(requestParameters.authRequestDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 만료된 토큰을 갱신 합니다. RefreshToken 을 사용하여 새로운 AccessToken 을 발급합니다. 이때 새로운 refreshToken 을 발급합니다. 기존의 refreshToken 도 만료일까지 사용이 가능합니다만 새로운 refreshToken 을 저장하여 사용하여도 무방합니다. refreshToken 의 만료일은 1년 입니다. accessToken 의 만료일은 1일 입니다. 
     * @summary 토큰 갱신
     * @param {CommonAuthenticationApiRefreshTokenRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommonAuthenticationApi
     */
    public refreshToken(requestParameters: CommonAuthenticationApiRefreshTokenRequest, options?: RawAxiosRequestConfig) {
        return CommonAuthenticationApiFp(this.configuration).refreshToken(requestParameters.refreshTokenRequestDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 회원가입 프로세스:  1. 이메일 인증코드 전송 (/api/auth/email/request-code) 2. 인증코드 확인 (/api/auth/email/verify-code) 3. 받은 completedVerificationId로 회원가입 진행  주의사항: - 인증코드 입력 후 60분 이내 가입 필요 - 이메일 최대 길이: 20자 
     * @summary 회원가입
     * @param {CommonAuthenticationApiRegisterRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommonAuthenticationApi
     */
    public register(requestParameters: CommonAuthenticationApiRegisterRequest, options?: RawAxiosRequestConfig) {
        return CommonAuthenticationApiFp(this.configuration).register(requestParameters.registerRequestDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     *  회원 가입/ 패스워드 재설정을 위한 사용자의 이메일 인증 코드를 전송합니다. 패스워드 변경은  CHANGE_PASSWORD 로 설정합니다  이후에 인증 이후에 인증코드 확인을 진행합니다. ( /api/auth/email/verify-code ) 
     * @summary 이메일 인증 코드 전송
     * @param {CommonAuthenticationApiSendVerificationCodeRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommonAuthenticationApi
     */
    public sendVerificationCode(requestParameters: CommonAuthenticationApiSendVerificationCodeRequest, options?: RawAxiosRequestConfig) {
        return CommonAuthenticationApiFp(this.configuration).sendVerificationCode(requestParameters.emailVerificationCodeRequestDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 이메일 인증 코드 전송 이후에 이메일을 확인한후 이메일을 통해 받은 인증 코드를 확인합니다. 수신한 completedVerificationId 값을 저장하여서 회원가입/패스워드 변경시 사용합니다 completedVerificationId 의 유효 기간은 60분 입니다. (이메일 전송후 확인 유효 기간은 5분(개발모드: 500분)) 
     * @summary 인증 코드 확인
     * @param {CommonAuthenticationApiVerifyEmailVerificationCodeRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommonAuthenticationApi
     */
    public verifyEmailVerificationCode(requestParameters: CommonAuthenticationApiVerifyEmailVerificationCodeRequest, options?: RawAxiosRequestConfig) {
        return CommonAuthenticationApiFp(this.configuration).verifyEmailVerificationCode(requestParameters.verifyCodeRequestDTO, options).then((request) => request(this.axios, this.basePath));
    }
}

