/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { PagedModelAdminCountryListItemResDTO } from '../../../../src/api/generated/models';
/**
 * AdminCountryApi - axios parameter creator
 * @export
 */
export const AdminCountryApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 국가 목록을 조회합니다.
         * @summary 국가 목록 조회
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (countryIsoCode 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminCountryPage: async (page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/admin/country/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AdminCountryApi - functional programming interface
 * @export
 */
export const AdminCountryApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AdminCountryApiAxiosParamCreator(configuration)
    return {
        /**
         * 국가 목록을 조회합니다.
         * @summary 국가 목록 조회
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (countryIsoCode 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminCountryPage(page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminCountryListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminCountryPage(page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminCountryApi.getAdminCountryPage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AdminCountryApi - factory interface
 * @export
 */
export const AdminCountryApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AdminCountryApiFp(configuration)
    return {
        /**
         * 국가 목록을 조회합니다.
         * @summary 국가 목록 조회
         * @param {AdminCountryApiGetAdminCountryPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminCountryPage(requestParameters: AdminCountryApiGetAdminCountryPageRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminCountryListItemResDTO> {
            return localVarFp.getAdminCountryPage(requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getAdminCountryPage operation in AdminCountryApi.
 * @export
 * @interface AdminCountryApiGetAdminCountryPageRequest
 */
export interface AdminCountryApiGetAdminCountryPageRequest {
    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminCountryApiGetAdminCountryPage
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminCountryApiGetAdminCountryPage
     */
    readonly size?: number

    /**
     * 정렬 조건 (countryIsoCode 등)
     * @type {string}
     * @memberof AdminCountryApiGetAdminCountryPage
     */
    readonly sort?: string
}

/**
 * AdminCountryApi - object-oriented interface
 * @export
 * @class AdminCountryApi
 * @extends {BaseAPI}
 */
export class AdminCountryApi extends BaseAPI {
    /**
     * 국가 목록을 조회합니다.
     * @summary 국가 목록 조회
     * @param {AdminCountryApiGetAdminCountryPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminCountryApi
     */
    public getAdminCountryPage(requestParameters: AdminCountryApiGetAdminCountryPageRequest = {}, options?: RawAxiosRequestConfig) {
        return AdminCountryApiFp(this.configuration).getAdminCountryPage(requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }
}

