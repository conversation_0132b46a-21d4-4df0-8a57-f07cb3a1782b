/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { AdminDealerCreateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { AdminDealerUpdateReqDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { PagedModelAdminDealerListItemResDTO } from '../../../../src/api/generated/models';
/**
 * AdminDealerApi - axios parameter creator
 * @export
 */
export const AdminDealerApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 딜러를 생성합니다.
         * @summary 딜러 생성
         * @param {Array<AdminDealerCreateReqDTO>} adminDealerCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminDealer: async (adminDealerCreateReqDTO: Array<AdminDealerCreateReqDTO>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'adminDealerCreateReqDTO' is not null or undefined
            assertParamExists('createAdminDealer', 'adminDealerCreateReqDTO', adminDealerCreateReqDTO)
            const localVarPath = `/api/admin/dealer`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(adminDealerCreateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 딜러를 삭제합니다.
         * @summary 딜러 삭제
         * @param {Array<number>} dealerIdList 딜러아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAdminDealer: async (dealerIdList: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'dealerIdList' is not null or undefined
            assertParamExists('deleteAdminDealer', 'dealerIdList', dealerIdList)
            const localVarPath = `/api/admin/dealer`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (dealerIdList) {
                localVarQueryParameter['dealerIdList'] = dealerIdList;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 딜러 목록을 조회합니다.
         * @summary 딜러 목록 조회
         * @param {number} [countryId] 국가아이디
         * @param {string} [dealerName] 딜러명
         * @param {string} [dealerPhone] 딜러전화번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (dealerName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDealerPage: async (countryId?: number, dealerName?: string, dealerPhone?: string, page?: number, size?: number, sort?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/admin/dealer/page`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (countryId !== undefined) {
                localVarQueryParameter['countryId'] = countryId;
            }

            if (dealerName !== undefined) {
                localVarQueryParameter['dealerName'] = dealerName;
            }

            if (dealerPhone !== undefined) {
                localVarQueryParameter['dealerPhone'] = dealerPhone;
            }

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }

            if (sort !== undefined) {
                localVarQueryParameter['sort'] = sort;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 딜러를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 딜러 수정
         * @param {AdminDealerUpdateReqDTO} adminDealerUpdateReqDTO 
         * @param {number} dealerId 딜러아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAdminDealer: async (adminDealerUpdateReqDTO: AdminDealerUpdateReqDTO, dealerId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'adminDealerUpdateReqDTO' is not null or undefined
            assertParamExists('updateAdminDealer', 'adminDealerUpdateReqDTO', adminDealerUpdateReqDTO)
            // verify required parameter 'dealerId' is not null or undefined
            assertParamExists('updateAdminDealer', 'dealerId', dealerId)
            const localVarPath = `/api/admin/dealer`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (dealerId !== undefined) {
                localVarQueryParameter['dealerId'] = dealerId;
            }


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(adminDealerUpdateReqDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AdminDealerApi - functional programming interface
 * @export
 */
export const AdminDealerApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AdminDealerApiAxiosParamCreator(configuration)
    return {
        /**
         * 딜러를 생성합니다.
         * @summary 딜러 생성
         * @param {Array<AdminDealerCreateReqDTO>} adminDealerCreateReqDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createAdminDealer(adminDealerCreateReqDTO: Array<AdminDealerCreateReqDTO>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createAdminDealer(adminDealerCreateReqDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDealerApi.createAdminDealer']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 딜러를 삭제합니다.
         * @summary 딜러 삭제
         * @param {Array<number>} dealerIdList 딜러아이디목록
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteAdminDealer(dealerIdList: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteAdminDealer(dealerIdList, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDealerApi.deleteAdminDealer']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 딜러 목록을 조회합니다.
         * @summary 딜러 목록 조회
         * @param {number} [countryId] 국가아이디
         * @param {string} [dealerName] 딜러명
         * @param {string} [dealerPhone] 딜러전화번호
         * @param {number} [page] 페이지 번호 (0부터 시작)
         * @param {number} [size] 페이지 크기
         * @param {string} [sort] 정렬 조건 (dealerName 등)
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getAdminDealerPage(countryId?: number, dealerName?: string, dealerPhone?: string, page?: number, size?: number, sort?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<PagedModelAdminDealerListItemResDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getAdminDealerPage(countryId, dealerName, dealerPhone, page, size, sort, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDealerApi.getAdminDealerPage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 딜러를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 딜러 수정
         * @param {AdminDealerUpdateReqDTO} adminDealerUpdateReqDTO 
         * @param {number} dealerId 딜러아이디
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateAdminDealer(adminDealerUpdateReqDTO: AdminDealerUpdateReqDTO, dealerId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateAdminDealer(adminDealerUpdateReqDTO, dealerId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminDealerApi.updateAdminDealer']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AdminDealerApi - factory interface
 * @export
 */
export const AdminDealerApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AdminDealerApiFp(configuration)
    return {
        /**
         * 딜러를 생성합니다.
         * @summary 딜러 생성
         * @param {AdminDealerApiCreateAdminDealerRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createAdminDealer(requestParameters: AdminDealerApiCreateAdminDealerRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.createAdminDealer(requestParameters.adminDealerCreateReqDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 딜러를 삭제합니다.
         * @summary 딜러 삭제
         * @param {AdminDealerApiDeleteAdminDealerRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteAdminDealer(requestParameters: AdminDealerApiDeleteAdminDealerRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteAdminDealer(requestParameters.dealerIdList, options).then((request) => request(axios, basePath));
        },
        /**
         * 딜러 목록을 조회합니다.
         * @summary 딜러 목록 조회
         * @param {AdminDealerApiGetAdminDealerPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getAdminDealerPage(requestParameters: AdminDealerApiGetAdminDealerPageRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<PagedModelAdminDealerListItemResDTO> {
            return localVarFp.getAdminDealerPage(requestParameters.countryId, requestParameters.dealerName, requestParameters.dealerPhone, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(axios, basePath));
        },
        /**
         * 딜러를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
         * @summary 딜러 수정
         * @param {AdminDealerApiUpdateAdminDealerRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateAdminDealer(requestParameters: AdminDealerApiUpdateAdminDealerRequest, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.updateAdminDealer(requestParameters.adminDealerUpdateReqDTO, requestParameters.dealerId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createAdminDealer operation in AdminDealerApi.
 * @export
 * @interface AdminDealerApiCreateAdminDealerRequest
 */
export interface AdminDealerApiCreateAdminDealerRequest {
    /**
     * 
     * @type {Array<AdminDealerCreateReqDTO>}
     * @memberof AdminDealerApiCreateAdminDealer
     */
    readonly adminDealerCreateReqDTO: Array<AdminDealerCreateReqDTO>
}

/**
 * Request parameters for deleteAdminDealer operation in AdminDealerApi.
 * @export
 * @interface AdminDealerApiDeleteAdminDealerRequest
 */
export interface AdminDealerApiDeleteAdminDealerRequest {
    /**
     * 딜러아이디목록
     * @type {Array<number>}
     * @memberof AdminDealerApiDeleteAdminDealer
     */
    readonly dealerIdList: Array<number>
}

/**
 * Request parameters for getAdminDealerPage operation in AdminDealerApi.
 * @export
 * @interface AdminDealerApiGetAdminDealerPageRequest
 */
export interface AdminDealerApiGetAdminDealerPageRequest {
    /**
     * 국가아이디
     * @type {number}
     * @memberof AdminDealerApiGetAdminDealerPage
     */
    readonly countryId?: number

    /**
     * 딜러명
     * @type {string}
     * @memberof AdminDealerApiGetAdminDealerPage
     */
    readonly dealerName?: string

    /**
     * 딜러전화번호
     * @type {string}
     * @memberof AdminDealerApiGetAdminDealerPage
     */
    readonly dealerPhone?: string

    /**
     * 페이지 번호 (0부터 시작)
     * @type {number}
     * @memberof AdminDealerApiGetAdminDealerPage
     */
    readonly page?: number

    /**
     * 페이지 크기
     * @type {number}
     * @memberof AdminDealerApiGetAdminDealerPage
     */
    readonly size?: number

    /**
     * 정렬 조건 (dealerName 등)
     * @type {string}
     * @memberof AdminDealerApiGetAdminDealerPage
     */
    readonly sort?: string
}

/**
 * Request parameters for updateAdminDealer operation in AdminDealerApi.
 * @export
 * @interface AdminDealerApiUpdateAdminDealerRequest
 */
export interface AdminDealerApiUpdateAdminDealerRequest {
    /**
     * 
     * @type {AdminDealerUpdateReqDTO}
     * @memberof AdminDealerApiUpdateAdminDealer
     */
    readonly adminDealerUpdateReqDTO: AdminDealerUpdateReqDTO

    /**
     * 딜러아이디
     * @type {number}
     * @memberof AdminDealerApiUpdateAdminDealer
     */
    readonly dealerId: number
}

/**
 * AdminDealerApi - object-oriented interface
 * @export
 * @class AdminDealerApi
 * @extends {BaseAPI}
 */
export class AdminDealerApi extends BaseAPI {
    /**
     * 딜러를 생성합니다.
     * @summary 딜러 생성
     * @param {AdminDealerApiCreateAdminDealerRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDealerApi
     */
    public createAdminDealer(requestParameters: AdminDealerApiCreateAdminDealerRequest, options?: RawAxiosRequestConfig) {
        return AdminDealerApiFp(this.configuration).createAdminDealer(requestParameters.adminDealerCreateReqDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 딜러를 삭제합니다.
     * @summary 딜러 삭제
     * @param {AdminDealerApiDeleteAdminDealerRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDealerApi
     */
    public deleteAdminDealer(requestParameters: AdminDealerApiDeleteAdminDealerRequest, options?: RawAxiosRequestConfig) {
        return AdminDealerApiFp(this.configuration).deleteAdminDealer(requestParameters.dealerIdList, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 딜러 목록을 조회합니다.
     * @summary 딜러 목록 조회
     * @param {AdminDealerApiGetAdminDealerPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDealerApi
     */
    public getAdminDealerPage(requestParameters: AdminDealerApiGetAdminDealerPageRequest = {}, options?: RawAxiosRequestConfig) {
        return AdminDealerApiFp(this.configuration).getAdminDealerPage(requestParameters.countryId, requestParameters.dealerName, requestParameters.dealerPhone, requestParameters.page, requestParameters.size, requestParameters.sort, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 딜러를 수정합니다. Request Body에는 수정되지 않은 필드의 기존 값도 모두 채워주셔야 합니다.
     * @summary 딜러 수정
     * @param {AdminDealerApiUpdateAdminDealerRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminDealerApi
     */
    public updateAdminDealer(requestParameters: AdminDealerApiUpdateAdminDealerRequest, options?: RawAxiosRequestConfig) {
        return AdminDealerApiFp(this.configuration).updateAdminDealer(requestParameters.adminDealerUpdateReqDTO, requestParameters.dealerId, options).then((request) => request(this.axios, this.basePath));
    }
}

