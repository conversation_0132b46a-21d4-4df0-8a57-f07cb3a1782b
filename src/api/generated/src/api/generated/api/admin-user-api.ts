/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { ChangePasswordRequestDTO } from '../../../../src/api/generated/models';
// @ts-ignore
import type { UserCommonDTO } from '../../../../src/api/generated/models';
/**
 * AdminUserApi - axios parameter creator
 * @export
 */
export const AdminUserApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 사용자 비밀번호를 변경합니다. PssUser 정보를 기반으로 L_USER 테이블에 비밀번호를 업데이트합니다. 사용자가 L_USER 테이블에 없는 경우 PssUser 정보를 기반으로 새로 생성합니다. 
         * @summary 비밀번호 변경
         * @param {ChangePasswordRequestDTO} changePasswordRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        changePassword: async (changePasswordRequestDTO: ChangePasswordRequestDTO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'changePasswordRequestDTO' is not null or undefined
            assertParamExists('changePassword', 'changePasswordRequestDTO', changePasswordRequestDTO)
            const localVarPath = `/api/user/change-password`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(changePasswordRequestDTO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 현재 사용자의 계정을 비활성화합니다. 물리적 삭제가 아닌 소프트 삭제로 처리됩니다. 탈퇴 처리 시 관련 토큰과 설정 정보가 삭제됩니다.  응답: 성공 시 204 No Content 
         * @summary 사용자 탈퇴
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteUser: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/user`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 현재 로그인된 사용자의 정보를 조회합니다. 로그인 상태에서 Bearer 토큰으로 주면 토큰의 정보를 보여줍니다. 
         * @summary 현재 사용자 정보 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCurrentUser: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/user`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 인증 없이 테스트 사용자 정보를 조회합니다.
         * @summary 테스트용 사용자 정보 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTestUser: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/user/test`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 지정된 ID를 가진 사용자의 정보를 조회합니다.
         * @summary 사용자 ID로 정보 조회
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserById: async (id: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getUserById', 'id', id)
            const localVarPath = `/api/user/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 현재 사용자의 모든 토큰을 삭제합니다. 앱에서 로그 아웃시 호출합니다. 호출 이후에 앱에 저장된 토큰을 삭제해야 합니다.
         * @summary 로그아웃
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        logout: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/user/logout`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AdminUserApi - functional programming interface
 * @export
 */
export const AdminUserApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AdminUserApiAxiosParamCreator(configuration)
    return {
        /**
         * 사용자 비밀번호를 변경합니다. PssUser 정보를 기반으로 L_USER 테이블에 비밀번호를 업데이트합니다. 사용자가 L_USER 테이블에 없는 경우 PssUser 정보를 기반으로 새로 생성합니다. 
         * @summary 비밀번호 변경
         * @param {ChangePasswordRequestDTO} changePasswordRequestDTO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async changePassword(changePasswordRequestDTO: ChangePasswordRequestDTO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserCommonDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.changePassword(changePasswordRequestDTO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminUserApi.changePassword']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 현재 사용자의 계정을 비활성화합니다. 물리적 삭제가 아닌 소프트 삭제로 처리됩니다. 탈퇴 처리 시 관련 토큰과 설정 정보가 삭제됩니다.  응답: 성공 시 204 No Content 
         * @summary 사용자 탈퇴
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteUser(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteUser(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminUserApi.deleteUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 현재 로그인된 사용자의 정보를 조회합니다. 로그인 상태에서 Bearer 토큰으로 주면 토큰의 정보를 보여줍니다. 
         * @summary 현재 사용자 정보 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCurrentUser(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserCommonDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCurrentUser(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminUserApi.getCurrentUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 인증 없이 테스트 사용자 정보를 조회합니다.
         * @summary 테스트용 사용자 정보 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getTestUser(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserCommonDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getTestUser(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminUserApi.getTestUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 지정된 ID를 가진 사용자의 정보를 조회합니다.
         * @summary 사용자 ID로 정보 조회
         * @param {string} id 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getUserById(id: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserCommonDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getUserById(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminUserApi.getUserById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 현재 사용자의 모든 토큰을 삭제합니다. 앱에서 로그 아웃시 호출합니다. 호출 이후에 앱에 저장된 토큰을 삭제해야 합니다.
         * @summary 로그아웃
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async logout(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<UserCommonDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.logout(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminUserApi.logout']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AdminUserApi - factory interface
 * @export
 */
export const AdminUserApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AdminUserApiFp(configuration)
    return {
        /**
         * 사용자 비밀번호를 변경합니다. PssUser 정보를 기반으로 L_USER 테이블에 비밀번호를 업데이트합니다. 사용자가 L_USER 테이블에 없는 경우 PssUser 정보를 기반으로 새로 생성합니다. 
         * @summary 비밀번호 변경
         * @param {AdminUserApiChangePasswordRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        changePassword(requestParameters: AdminUserApiChangePasswordRequest, options?: RawAxiosRequestConfig): AxiosPromise<UserCommonDTO> {
            return localVarFp.changePassword(requestParameters.changePasswordRequestDTO, options).then((request) => request(axios, basePath));
        },
        /**
         * 현재 사용자의 계정을 비활성화합니다. 물리적 삭제가 아닌 소프트 삭제로 처리됩니다. 탈퇴 처리 시 관련 토큰과 설정 정보가 삭제됩니다.  응답: 성공 시 204 No Content 
         * @summary 사용자 탈퇴
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteUser(options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteUser(options).then((request) => request(axios, basePath));
        },
        /**
         * 현재 로그인된 사용자의 정보를 조회합니다. 로그인 상태에서 Bearer 토큰으로 주면 토큰의 정보를 보여줍니다. 
         * @summary 현재 사용자 정보 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCurrentUser(options?: RawAxiosRequestConfig): AxiosPromise<UserCommonDTO> {
            return localVarFp.getCurrentUser(options).then((request) => request(axios, basePath));
        },
        /**
         * 인증 없이 테스트 사용자 정보를 조회합니다.
         * @summary 테스트용 사용자 정보 조회
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTestUser(options?: RawAxiosRequestConfig): AxiosPromise<UserCommonDTO> {
            return localVarFp.getTestUser(options).then((request) => request(axios, basePath));
        },
        /**
         * 지정된 ID를 가진 사용자의 정보를 조회합니다.
         * @summary 사용자 ID로 정보 조회
         * @param {AdminUserApiGetUserByIdRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUserById(requestParameters: AdminUserApiGetUserByIdRequest, options?: RawAxiosRequestConfig): AxiosPromise<UserCommonDTO> {
            return localVarFp.getUserById(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * 현재 사용자의 모든 토큰을 삭제합니다. 앱에서 로그 아웃시 호출합니다. 호출 이후에 앱에 저장된 토큰을 삭제해야 합니다.
         * @summary 로그아웃
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        logout(options?: RawAxiosRequestConfig): AxiosPromise<UserCommonDTO> {
            return localVarFp.logout(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for changePassword operation in AdminUserApi.
 * @export
 * @interface AdminUserApiChangePasswordRequest
 */
export interface AdminUserApiChangePasswordRequest {
    /**
     * 
     * @type {ChangePasswordRequestDTO}
     * @memberof AdminUserApiChangePassword
     */
    readonly changePasswordRequestDTO: ChangePasswordRequestDTO
}

/**
 * Request parameters for getUserById operation in AdminUserApi.
 * @export
 * @interface AdminUserApiGetUserByIdRequest
 */
export interface AdminUserApiGetUserByIdRequest {
    /**
     * 
     * @type {string}
     * @memberof AdminUserApiGetUserById
     */
    readonly id: string
}

/**
 * AdminUserApi - object-oriented interface
 * @export
 * @class AdminUserApi
 * @extends {BaseAPI}
 */
export class AdminUserApi extends BaseAPI {
    /**
     * 사용자 비밀번호를 변경합니다. PssUser 정보를 기반으로 L_USER 테이블에 비밀번호를 업데이트합니다. 사용자가 L_USER 테이블에 없는 경우 PssUser 정보를 기반으로 새로 생성합니다. 
     * @summary 비밀번호 변경
     * @param {AdminUserApiChangePasswordRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminUserApi
     */
    public changePassword(requestParameters: AdminUserApiChangePasswordRequest, options?: RawAxiosRequestConfig) {
        return AdminUserApiFp(this.configuration).changePassword(requestParameters.changePasswordRequestDTO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 현재 사용자의 계정을 비활성화합니다. 물리적 삭제가 아닌 소프트 삭제로 처리됩니다. 탈퇴 처리 시 관련 토큰과 설정 정보가 삭제됩니다.  응답: 성공 시 204 No Content 
     * @summary 사용자 탈퇴
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminUserApi
     */
    public deleteUser(options?: RawAxiosRequestConfig) {
        return AdminUserApiFp(this.configuration).deleteUser(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 현재 로그인된 사용자의 정보를 조회합니다. 로그인 상태에서 Bearer 토큰으로 주면 토큰의 정보를 보여줍니다. 
     * @summary 현재 사용자 정보 조회
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminUserApi
     */
    public getCurrentUser(options?: RawAxiosRequestConfig) {
        return AdminUserApiFp(this.configuration).getCurrentUser(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 인증 없이 테스트 사용자 정보를 조회합니다.
     * @summary 테스트용 사용자 정보 조회
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminUserApi
     */
    public getTestUser(options?: RawAxiosRequestConfig) {
        return AdminUserApiFp(this.configuration).getTestUser(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 지정된 ID를 가진 사용자의 정보를 조회합니다.
     * @summary 사용자 ID로 정보 조회
     * @param {AdminUserApiGetUserByIdRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminUserApi
     */
    public getUserById(requestParameters: AdminUserApiGetUserByIdRequest, options?: RawAxiosRequestConfig) {
        return AdminUserApiFp(this.configuration).getUserById(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 현재 사용자의 모든 토큰을 삭제합니다. 앱에서 로그 아웃시 호출합니다. 호출 이후에 앱에 저장된 토큰을 삭제해야 합니다.
     * @summary 로그아웃
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminUserApi
     */
    public logout(options?: RawAxiosRequestConfig) {
        return AdminUserApiFp(this.configuration).logout(options).then((request) => request(this.axios, this.basePath));
    }
}

