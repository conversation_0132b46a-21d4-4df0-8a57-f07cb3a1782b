/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
// @ts-ignore
import type { EnumResDTO } from '../../../../src/api/generated/models';
/**
 * CommonEnumApi - axios parameter creator
 * @export
 */
export const CommonEnumApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Enum 값 목록을 조회합니다.<br> 현재 지원되는 enumType은 equipmentType, vehicleType, fuelType, vehicleBodyClass, driverGender, driverStatus, countryDialCode, usState, licenseClass, operationStatus, breakdownStatus, breakdownRepairStatus, breakdownSeverity, itineraryStatus입니다. 
         * @summary Enum 값 목록 조회
         * @param {string} enumType Enum 타입
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getEnumMap: async (enumType: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'enumType' is not null or undefined
            assertParamExists('getEnumMap', 'enumType', enumType)
            const localVarPath = `/api/common/enum`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)

            if (enumType !== undefined) {
                localVarQueryParameter['enumType'] = enumType;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CommonEnumApi - functional programming interface
 * @export
 */
export const CommonEnumApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = CommonEnumApiAxiosParamCreator(configuration)
    return {
        /**
         * Enum 값 목록을 조회합니다.<br> 현재 지원되는 enumType은 equipmentType, vehicleType, fuelType, vehicleBodyClass, driverGender, driverStatus, countryDialCode, usState, licenseClass, operationStatus, breakdownStatus, breakdownRepairStatus, breakdownSeverity, itineraryStatus입니다. 
         * @summary Enum 값 목록 조회
         * @param {string} enumType Enum 타입
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getEnumMap(enumType: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<EnumResDTO>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getEnumMap(enumType, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['CommonEnumApi.getEnumMap']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * CommonEnumApi - factory interface
 * @export
 */
export const CommonEnumApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = CommonEnumApiFp(configuration)
    return {
        /**
         * Enum 값 목록을 조회합니다.<br> 현재 지원되는 enumType은 equipmentType, vehicleType, fuelType, vehicleBodyClass, driverGender, driverStatus, countryDialCode, usState, licenseClass, operationStatus, breakdownStatus, breakdownRepairStatus, breakdownSeverity, itineraryStatus입니다. 
         * @summary Enum 값 목록 조회
         * @param {CommonEnumApiGetEnumMapRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getEnumMap(requestParameters: CommonEnumApiGetEnumMapRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<EnumResDTO>> {
            return localVarFp.getEnumMap(requestParameters.enumType, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getEnumMap operation in CommonEnumApi.
 * @export
 * @interface CommonEnumApiGetEnumMapRequest
 */
export interface CommonEnumApiGetEnumMapRequest {
    /**
     * Enum 타입
     * @type {string}
     * @memberof CommonEnumApiGetEnumMap
     */
    readonly enumType: string
}

/**
 * CommonEnumApi - object-oriented interface
 * @export
 * @class CommonEnumApi
 * @extends {BaseAPI}
 */
export class CommonEnumApi extends BaseAPI {
    /**
     * Enum 값 목록을 조회합니다.<br> 현재 지원되는 enumType은 equipmentType, vehicleType, fuelType, vehicleBodyClass, driverGender, driverStatus, countryDialCode, usState, licenseClass, operationStatus, breakdownStatus, breakdownRepairStatus, breakdownSeverity, itineraryStatus입니다. 
     * @summary Enum 값 목록 조회
     * @param {CommonEnumApiGetEnumMapRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CommonEnumApi
     */
    public getEnumMap(requestParameters: CommonEnumApiGetEnumMapRequest, options?: RawAxiosRequestConfig) {
        return CommonEnumApiFp(this.configuration).getEnumMap(requestParameters.enumType, options).then((request) => request(this.axios, this.basePath));
    }
}

