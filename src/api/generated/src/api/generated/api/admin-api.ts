/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../../../../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../../../../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../../../../base';
/**
 * AdminApi - axios parameter creator
 * @export
 */
export const AdminApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 관리자 권한으로 특정 사용자를 삭제합니다. 물리적 삭제가 아닌 소프트 삭제로 처리됩니다. 사용자의 관련 데이터(토큰, 설정 등)도 함께 정리됩니다.  Path Parameter: - userId: 삭제할 사용자의 ID  주의사항: - 관리자 권한이 필요합니다 - 삭제된 사용자는 시스템에 접근할 수 없습니다 - 관련 데이터는 보존되지만 사용자 계정은 비활성화됩니다  응답: 성공 시 204 No Content 
         * @summary 관리자 사용자 삭제
         * @param {string} userId 삭제할 사용자 ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteUser1: async (userId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('deleteUser1', 'userId', userId)
            const localVarPath = `/api/admin/delete-user/{userId}`
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication bearerAuth required
            // http bearer authentication required
            await setBearerAuthToObject(localVarHeaderParameter, configuration)


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * AdminApi - functional programming interface
 * @export
 */
export const AdminApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = AdminApiAxiosParamCreator(configuration)
    return {
        /**
         * 관리자 권한으로 특정 사용자를 삭제합니다. 물리적 삭제가 아닌 소프트 삭제로 처리됩니다. 사용자의 관련 데이터(토큰, 설정 등)도 함께 정리됩니다.  Path Parameter: - userId: 삭제할 사용자의 ID  주의사항: - 관리자 권한이 필요합니다 - 삭제된 사용자는 시스템에 접근할 수 없습니다 - 관련 데이터는 보존되지만 사용자 계정은 비활성화됩니다  응답: 성공 시 204 No Content 
         * @summary 관리자 사용자 삭제
         * @param {string} userId 삭제할 사용자 ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteUser1(userId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteUser1(userId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['AdminApi.deleteUser1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * AdminApi - factory interface
 * @export
 */
export const AdminApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = AdminApiFp(configuration)
    return {
        /**
         * 관리자 권한으로 특정 사용자를 삭제합니다. 물리적 삭제가 아닌 소프트 삭제로 처리됩니다. 사용자의 관련 데이터(토큰, 설정 등)도 함께 정리됩니다.  Path Parameter: - userId: 삭제할 사용자의 ID  주의사항: - 관리자 권한이 필요합니다 - 삭제된 사용자는 시스템에 접근할 수 없습니다 - 관련 데이터는 보존되지만 사용자 계정은 비활성화됩니다  응답: 성공 시 204 No Content 
         * @summary 관리자 사용자 삭제
         * @param {AdminApiDeleteUser1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteUser1(requestParameters: AdminApiDeleteUser1Request, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp.deleteUser1(requestParameters.userId, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for deleteUser1 operation in AdminApi.
 * @export
 * @interface AdminApiDeleteUser1Request
 */
export interface AdminApiDeleteUser1Request {
    /**
     * 삭제할 사용자 ID
     * @type {string}
     * @memberof AdminApiDeleteUser1
     */
    readonly userId: string
}

/**
 * AdminApi - object-oriented interface
 * @export
 * @class AdminApi
 * @extends {BaseAPI}
 */
export class AdminApi extends BaseAPI {
    /**
     * 관리자 권한으로 특정 사용자를 삭제합니다. 물리적 삭제가 아닌 소프트 삭제로 처리됩니다. 사용자의 관련 데이터(토큰, 설정 등)도 함께 정리됩니다.  Path Parameter: - userId: 삭제할 사용자의 ID  주의사항: - 관리자 권한이 필요합니다 - 삭제된 사용자는 시스템에 접근할 수 없습니다 - 관련 데이터는 보존되지만 사용자 계정은 비활성화됩니다  응답: 성공 시 204 No Content 
     * @summary 관리자 사용자 삭제
     * @param {AdminApiDeleteUser1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof AdminApi
     */
    public deleteUser1(requestParameters: AdminApiDeleteUser1Request, options?: RawAxiosRequestConfig) {
        return AdminApiFp(this.configuration).deleteUser1(requestParameters.userId, options).then((request) => request(this.axios, this.basePath));
    }
}

