/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ItineraryRouteTimeLineResDTO } from './itinerary-route-time-line-res-dto';

/**
 * 여정상세 조회응답
 * @export
 * @interface ItineraryTimeLineResDTO
 */
export interface ItineraryTimeLineResDTO {
    /**
     * 여정 아이디
     * @type {number}
     * @memberof ItineraryTimeLineResDTO
     */
    'itineraryId'?: number;
    /**
     * 여정 이름
     * @type {string}
     * @memberof ItineraryTimeLineResDTO
     */
    'itineraryName'?: string;
    /**
     * 차량 이름
     * @type {string}
     * @memberof ItineraryTimeLineResDTO
     */
    'vehicleName'?: string;
    /**
     * 여정의 종료시각
     * @type {string}
     * @memberof ItineraryTimeLineResDTO
     */
    'completionTime'?: string;
    /**
     * 여정경로 목록
     * @type {Array<ItineraryRouteTimeLineResDTO>}
     * @memberof ItineraryTimeLineResDTO
     */
    'routesTimeLine'?: Array<ItineraryRouteTimeLineResDTO>;
}

