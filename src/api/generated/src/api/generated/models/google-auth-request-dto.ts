/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * Google 인증 요청 DTO
 * @export
 * @interface GoogleAuthRequestDTO
 */
export interface GoogleAuthRequestDTO {
    /**
     * 사용자의 Google 이메일 주소.
     * @type {string}
     * @memberof GoogleAuthRequestDTO
     */
    'email'?: string;
    /**
     * 사용자의 이름
     * @type {string}
     * @memberof GoogleAuthRequestDTO
     */
    'firstName'?: string;
    /**
     * 사용자의 성
     * @type {string}
     * @memberof GoogleAuthRequestDTO
     */
    'lastName'?: string;
    /**
     * (사용안함-삭제예정)사용자의 암호값. 아무나 호출하지 못하게 고정값으로 사용합니다. 고정으로 \'*******\'을 넣어주세요, 구글 인증완료시 삭제 예정
     * @type {string}
     * @memberof GoogleAuthRequestDTO
     */
    'clientSecret'?: string;
    /**
     * 사용자의 역할. 미입력시 USER로 설정됩니다.
     * @type {string}
     * @memberof GoogleAuthRequestDTO
     */
    'role'?: GoogleAuthRequestDTORoleEnum;
    /**
     * 사용자의 프로필 사진 URL
     * @type {string}
     * @memberof GoogleAuthRequestDTO
     */
    'photoUrl'?: string;
    /**
     * Google Access Token (OAuth 2.0). 테스트용: null, 빈값, 또는 \'force-accept\' 시 Google 검증을 건너뛰고 강제로 통과 (이후 삭제 예정)
     * @type {string}
     * @memberof GoogleAuthRequestDTO
     */
    'accessToken'?: string;
    /**
     * Google ID Token (JWT). ID Token과 Access Token 중 하나 이상 제공해야 합니다. 둘 다 제공시 둘 다 검증됩니다.
     * @type {string}
     * @memberof GoogleAuthRequestDTO
     */
    'idToken'?: string;
}

export const GoogleAuthRequestDTORoleEnum = {
    User: 'USER',
    Admin: 'ADMIN'
} as const;

export type GoogleAuthRequestDTORoleEnum = typeof GoogleAuthRequestDTORoleEnum[keyof typeof GoogleAuthRequestDTORoleEnum];


