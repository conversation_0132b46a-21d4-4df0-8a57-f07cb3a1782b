/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 장비 수정요청
 * @export
 * @interface AdminEquipmentUpdateReqDTO
 */
export interface AdminEquipmentUpdateReqDTO {
    /**
     * 국가아이디
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'countryId'?: number;
    /**
     * 딜러아이디
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'dealerId'?: number;
    /**
     * 서비스센터아이디
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'serviceCenterId'?: number;
    /**
     * 플릿아이디목록
     * @type {Array<number>}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'fleetIdList'?: Array<number>;
    /**
     * 장비타입:<br> VEHICLE: 차량<br> TRUCK: 트럭<br> HEAVY_EQUIPMENT: 중장비<br> AGRICULTURAL: 농기계<br> DRONE: 드론<br> ROBOT: 로봇<br> 
     * @type {string}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'equipmentType'?: AdminEquipmentUpdateReqDTOEquipmentTypeEnum;
    /**
     * 차량타입:<br> CAR: 일반 승용차<br> SUV_RV: 다목적 승용차(SUV, RV 등)<br> TRUCK: 트럭<br> BUS: 버스<br> TRAILER: 트레일러<br> LOW_SPEED: 저속 차량<br> BIKE: 이륜차(오토바이 등)<br> 
     * @type {string}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'vehicleType': AdminEquipmentUpdateReqDTOVehicleTypeEnum;
    /**
     * 제조사
     * @type {string}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'manufacturer': string;
    /**
     * 모델명
     * @type {string}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'modelName': string;
    /**
     * 트림명
     * @type {string}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'trimName': string;
    /**
     * 생산년도
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'productYear': number;
    /**
     * 이미지경로
     * @type {string}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'imagePath'?: string;
    /**
     * VIN No
     * @type {string}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'serialNo': string;
    /**
     * 차량번호
     * @type {string}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'plateNo': string;
    /**
     * 차체형식:<br> SEDAN: 세단<br> HATCHBACK: 해치백<br> COUPE: 쿠페<br> CONVERTIBLE: 컨버터블<br> SUV: SUV<br> VAN: 밴<br> MINIVAN: 미니밴<br> WAGON: 웨건<br> PICKUP_TRUCK: 픽업트럭<br> BUS: 버스<br> MINIBUS: 소형버스<br> HEAVY_TRUCK: 대형화물차<br> TRIKE: 삼륜오토바이<br> OFF_ROAD: 오프로드 차량<br> TRAILER: 트레일러<br> 
     * @type {string}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'vehicleBodyClass'?: AdminEquipmentUpdateReqDTOVehicleBodyClassEnum;
    /**
     * 전장: 장비 길이(mm)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'bodyLength': number;
    /**
     * 전고: 장비 높이(mm)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'bodyHeight': number;
    /**
     * 전폭: 장비 너비(mm)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'bodyWidth': number;
    /**
     * 위험물질: Explosives
     * @type {boolean}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'hazmatExplosives'?: boolean;
    /**
     * 위험물질: Gas
     * @type {boolean}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'hazmatGas'?: boolean;
    /**
     * 위험물질: Flammable
     * @type {boolean}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'hazmatFlammable'?: boolean;
    /**
     * 위험물질: Organic
     * @type {boolean}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'hazmatOrganic'?: boolean;
    /**
     * 위험물질: Poison
     * @type {boolean}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'hazmatPoison'?: boolean;
    /**
     * 위험물질: Radioactive
     * @type {boolean}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'hazmatRadioactive'?: boolean;
    /**
     * 위험물질: Corrosive
     * @type {boolean}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'hazmatCorrosive'?: boolean;
    /**
     * 위험물질: Harmful For Water
     * @type {boolean}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'hazmatHarmfulForWater'?: boolean;
    /**
     * 위험물질: Poisonous Inhalation Hazard
     * @type {boolean}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'hazmatPoisonousInhalationHazard'?: boolean;
    /**
     * 위험물질: Other
     * @type {boolean}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'hazmatOther'?: boolean;
    /**
     * 연료타입:<br> DIESEL: 경유<br> GASOLINE: 휘발유<br> HYBRID: 하이브리드<br> ELECTRIC: 전기<br> 
     * @type {string}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'fuelType': AdminEquipmentUpdateReqDTOFuelTypeEnum;
    /**
     * 연비(km/L or km/kWh)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'fuelEfficiency'?: number;
    /**
     * 연료탱크용량(L or kWh)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'fuelTankCapacity'?: number;
    /**
     * 타이어 지름(inch)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'tireDiameter'?: number;
    /**
     * 타이어 폭(mm)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'tireWidth'?: number;
    /**
     * 엔진오일 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'engineOilInterval'?: number;
    /**
     * 오일필터 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'oilFilterInterval'?: number;
    /**
     * 연료필터 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'fuelFilterInterval'?: number;
    /**
     * 에어필터 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'airFilterInterval'?: number;
    /**
     * 브레이크패드 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'brakePadInterval'?: number;
    /**
     * 브레이크 라이닝 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'brakeLiningInterval'?: number;
    /**
     * 타이어 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'tireInterval'?: number;
    /**
     * 냉각수 교체주기(년)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'coolantInterval'?: number;
    /**
     * 배터리 교체주기(년)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'batteryInterval'?: number;
    /**
     * 트랜스미션오일 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'transmissionOilInterval'?: number;
    /**
     * 타이어위치 교환주기(km)
     * @type {number}
     * @memberof AdminEquipmentUpdateReqDTO
     */
    'tireRotationInterval'?: number;
}

export const AdminEquipmentUpdateReqDTOEquipmentTypeEnum = {
    Vehicle: 'VEHICLE',
    Truck: 'TRUCK',
    HeavyEquipment: 'HEAVY_EQUIPMENT',
    Agricultural: 'AGRICULTURAL',
    Drone: 'DRONE',
    Robot: 'ROBOT'
} as const;

export type AdminEquipmentUpdateReqDTOEquipmentTypeEnum = typeof AdminEquipmentUpdateReqDTOEquipmentTypeEnum[keyof typeof AdminEquipmentUpdateReqDTOEquipmentTypeEnum];
export const AdminEquipmentUpdateReqDTOVehicleTypeEnum = {
    Car: 'CAR',
    SuvRv: 'SUV_RV',
    Truck: 'TRUCK',
    Bus: 'BUS',
    Trailer: 'TRAILER',
    LowSpeed: 'LOW_SPEED',
    Bike: 'BIKE'
} as const;

export type AdminEquipmentUpdateReqDTOVehicleTypeEnum = typeof AdminEquipmentUpdateReqDTOVehicleTypeEnum[keyof typeof AdminEquipmentUpdateReqDTOVehicleTypeEnum];
export const AdminEquipmentUpdateReqDTOVehicleBodyClassEnum = {
    Sedan: 'SEDAN',
    Hatchback: 'HATCHBACK',
    Coupe: 'COUPE',
    Convertible: 'CONVERTIBLE',
    Suv: 'SUV',
    Van: 'VAN',
    Minivan: 'MINIVAN',
    Wagon: 'WAGON',
    PickupTruck: 'PICKUP_TRUCK',
    Bus: 'BUS',
    Minibus: 'MINIBUS',
    HeavyTruck: 'HEAVY_TRUCK',
    Trike: 'TRIKE',
    OffRoad: 'OFF_ROAD',
    Trailer: 'TRAILER'
} as const;

export type AdminEquipmentUpdateReqDTOVehicleBodyClassEnum = typeof AdminEquipmentUpdateReqDTOVehicleBodyClassEnum[keyof typeof AdminEquipmentUpdateReqDTOVehicleBodyClassEnum];
export const AdminEquipmentUpdateReqDTOFuelTypeEnum = {
    Diesel: 'DIESEL',
    Gasoline: 'GASOLINE',
    Hybrid: 'HYBRID',
    Electric: 'ELECTRIC'
} as const;

export type AdminEquipmentUpdateReqDTOFuelTypeEnum = typeof AdminEquipmentUpdateReqDTOFuelTypeEnum[keyof typeof AdminEquipmentUpdateReqDTOFuelTypeEnum];


