/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface Envelope
 */
export interface Envelope {
    /**
     * 
     * @type {number}
     * @memberof Envelope
     */
    'area'?: number;
    /**
     * 
     * @type {number}
     * @memberof Envelope
     */
    'maxX'?: number;
    /**
     * 
     * @type {number}
     * @memberof Envelope
     */
    'maxY'?: number;
    /**
     * 
     * @type {number}
     * @memberof Envelope
     */
    'diameter'?: number;
    /**
     * 
     * @type {number}
     * @memberof Envelope
     */
    'width'?: number;
    /**
     * 
     * @type {number}
     * @memberof Envelope
     */
    'height'?: number;
    /**
     * 
     * @type {number}
     * @memberof Envelope
     */
    'minX'?: number;
    /**
     * 
     * @type {number}
     * @memberof Envelope
     */
    'minY'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof Envelope
     */
    'null'?: boolean;
}

