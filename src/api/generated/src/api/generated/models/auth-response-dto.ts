/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 인증 응답 DTO
 * @export
 * @interface AuthResponseDTO
 */
export interface AuthResponseDTO {
    /**
     * 계정타입
     * @type {string}
     * @memberof AuthResponseDTO
     */
    'accountType'?: AuthResponseDTOAccountTypeEnum;
    /**
     * 회원 로그인 유저아이디 
     * @type {string}
     * @memberof AuthResponseDTO
     */
    'loginId'?: string;
    /**
     * 사용자 ID
     * @type {number}
     * @memberof AuthResponseDTO
     */
    'userId'?: number;
    /**
     * 가입상태
     * @type {string}
     * @memberof AuthResponseDTO
     */
    'registerStatus'?: AuthResponseDTORegisterStatusEnum;
    /**
     * 액세스 토큰
     * @type {string}
     * @memberof AuthResponseDTO
     */
    'access_token'?: string;
    /**
     * 리프레시 토큰
     * @type {string}
     * @memberof AuthResponseDTO
     */
    'refresh_token'?: string;
}

export const AuthResponseDTOAccountTypeEnum = {
    User: 'USER',
    Driver: 'DRIVER'
} as const;

export type AuthResponseDTOAccountTypeEnum = typeof AuthResponseDTOAccountTypeEnum[keyof typeof AuthResponseDTOAccountTypeEnum];
export const AuthResponseDTORegisterStatusEnum = {
    Registered: 'REGISTERED',
    AlreadyRegistered: 'ALREADY_REGISTERED'
} as const;

export type AuthResponseDTORegisterStatusEnum = typeof AuthResponseDTORegisterStatusEnum[keyof typeof AuthResponseDTORegisterStatusEnum];


