/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { LineString } from './line-string';
// May contain unused imports in some cases
// @ts-ignore
import type { SwaggerPointMixin } from './swagger-point-mixin';

/**
 * 여정경로 조회응답
 * @export
 * @interface ItineraryRouteTrackingResDTO
 */
export interface ItineraryRouteTrackingResDTO {
    /**
     * 여정경로 아이디
     * @type {number}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'itineraryRouteId'?: number;
    /**
     * 여정경로의 목적지 Title
     * @type {string}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'arrivalName'?: string;
    /**
     * 여정경로의 목적지 주소
     * @type {string}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'arrivalAddress'?: string;
    /**
     * 
     * @type {SwaggerPointMixin}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'arrivalLocation'?: SwaggerPointMixin;
    /**
     * 여정경로의 목적지까지 남은 거리 (m)
     * @type {number}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'routeRemainingDistance'?: number;
    /**
     * 여정경로의 목적지까지 남은 시간 (sec)
     * @type {number}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'routeRemainingTime'?: number;
    /**
     * 
     * @type {LineString}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'routeRemainingGeometry'?: LineString;
    /**
     * 여정경로의 총 주행거리 (m)
     * @type {number}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'routeDrivedDistance'?: number;
    /**
     * 여정경로의 총 주행시간 (sec)
     * @type {number}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'routeDrivedTime'?: number;
    /**
     * 
     * @type {LineString}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'routeDrivedGeometry'?: LineString;
    /**
     * 여정경로의 출발지를 출발한 시각
     * @type {string}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'departureTime'?: string;
    /**
     * 여정경로의 목적지에 도착한 시각, 완료된 경우 설정됩니다
     * @type {string}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'arrivalTime'?: string;
    /**
     * 여정경로의 목적지까지 소요된 시간 (sec), 완료된 경우 설정되는데 알 수 없는 경우 null로 설정됩니다
     * @type {number}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'elapsedTime'?: number;
    /**
     * 현재 기준 여정경로의 목적지에 도착 예상 시각, 미완료인 경우 설정되는데 알 수 없는 경우 null로 설정됩니다
     * @type {string}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'currentEstimatedArrivalTime'?: string;
    /**
     * 현재 기준 여정경로의 목적지까지 소요 예상 시간 (sec), 미완료인 경우 설정되는데 알 수 없는 경우 null로 설정됩니다
     * @type {number}
     * @memberof ItineraryRouteTrackingResDTO
     */
    'currentEstimatedElapsedTime'?: number;
}

