/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 장비 기본 정보
 * @export
 * @interface AdminBreakdownListItemResDTO
 */
export interface AdminBreakdownListItemResDTO {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminBreakdownListItemResDTO
     */
    'equipmentId'?: number;
    /**
     * 마일리지
     * @type {number}
     * @memberof AdminBreakdownListItemResDTO
     */
    'mileage'?: number;
    /**
     * 고장알람타입:<br> A: 모바일 통신감도가 낮은상태 (기준이하, -100dBm)<br> B: 경계 이탈<br> F: 장비 고장<br> G: GPS 신호 유실<br> I: SOS 활성<br> L: 통신 두절<br> O: SOS 비활성<br> S: 시스템 에러 알람<br> W: 경고<br> 
     * @type {string}
     * @memberof AdminBreakdownListItemResDTO
     */
    'alarmType'?: AdminBreakdownListItemResDTOAlarmTypeEnum;
    /**
     * 고장코드
     * @type {string}
     * @memberof AdminBreakdownListItemResDTO
     */
    'breakdownCode'?: string;
    /**
     * 고장심각도:<br> HIGH: 높음<br> MEDIUM: 보통<br> LOW: 낮음<br> 
     * @type {string}
     * @memberof AdminBreakdownListItemResDTO
     */
    'severity'?: AdminBreakdownListItemResDTOSeverityEnum;
    /**
     * 고장증상
     * @type {string}
     * @memberof AdminBreakdownListItemResDTO
     */
    'symptom'?: string;
    /**
     * 고장사진경로
     * @type {string}
     * @memberof AdminBreakdownListItemResDTO
     */
    'photoPath'?: string;
    /**
     * 고장정비상태:<br> SUBMITTED: 정비 접수<br> REPAIRING: 정비 중<br> DONE: 정비 완료<br> 
     * @type {string}
     * @memberof AdminBreakdownListItemResDTO
     */
    'breakdownRepairStatus'?: AdminBreakdownListItemResDTOBreakdownRepairStatusEnum;
    /**
     * TSG아이디
     * @type {number}
     * @memberof AdminBreakdownListItemResDTO
     */
    'tsgId'?: number;
    /**
     * 고장일시
     * @type {string}
     * @memberof AdminBreakdownListItemResDTO
     */
    'occurredAt'?: string;
    /**
     * 접수일시
     * @type {string}
     * @memberof AdminBreakdownListItemResDTO
     */
    'submittedAt'?: string;
    /**
     * 수리일시
     * @type {string}
     * @memberof AdminBreakdownListItemResDTO
     */
    'repairedAt'?: string;
}

export const AdminBreakdownListItemResDTOAlarmTypeEnum = {
    A: 'A',
    B: 'B',
    F: 'F',
    G: 'G',
    I: 'I',
    L: 'L',
    O: 'O',
    S: 'S',
    W: 'W'
} as const;

export type AdminBreakdownListItemResDTOAlarmTypeEnum = typeof AdminBreakdownListItemResDTOAlarmTypeEnum[keyof typeof AdminBreakdownListItemResDTOAlarmTypeEnum];
export const AdminBreakdownListItemResDTOSeverityEnum = {
    High: 'HIGH',
    Medium: 'MEDIUM',
    Low: 'LOW'
} as const;

export type AdminBreakdownListItemResDTOSeverityEnum = typeof AdminBreakdownListItemResDTOSeverityEnum[keyof typeof AdminBreakdownListItemResDTOSeverityEnum];
export const AdminBreakdownListItemResDTOBreakdownRepairStatusEnum = {
    Submitted: 'SUBMITTED',
    Repairing: 'REPAIRING',
    Done: 'DONE'
} as const;

export type AdminBreakdownListItemResDTOBreakdownRepairStatusEnum = typeof AdminBreakdownListItemResDTOBreakdownRepairStatusEnum[keyof typeof AdminBreakdownListItemResDTOBreakdownRepairStatusEnum];


