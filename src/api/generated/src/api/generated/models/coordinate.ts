/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface Coordinate
 */
export interface Coordinate {
    /**
     * 
     * @type {number}
     * @memberof Coordinate
     */
    'x'?: number;
    /**
     * 
     * @type {number}
     * @memberof Coordinate
     */
    'y'?: number;
    /**
     * 
     * @type {number}
     * @memberof Coordinate
     */
    'z'?: number;
    /**
     * 
     * @type {Coordinate}
     * @memberof Coordinate
     */
    'coordinate'?: Coordinate;
    /**
     * 
     * @type {number}
     * @memberof Coordinate
     */
    'm'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof Coordinate
     */
    'valid'?: boolean;
}

