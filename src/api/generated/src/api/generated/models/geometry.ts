/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Coordinate } from './coordinate';
// May contain unused imports in some cases
// @ts-ignore
import type { Envelope } from './envelope';
// May contain unused imports in some cases
// @ts-ignore
import type { GeometryFactory } from './geometry-factory';
// May contain unused imports in some cases
// @ts-ignore
import type { Point } from './point';
// May contain unused imports in some cases
// @ts-ignore
import type { PrecisionModel } from './precision-model';

/**
 * 
 * @export
 * @interface Geometry
 */
export interface Geometry {
    /**
     * 
     * @type {Geometry}
     * @memberof Geometry
     */
    'envelope'?: Geometry;
    /**
     * 
     * @type {GeometryFactory}
     * @memberof Geometry
     */
    'factory'?: GeometryFactory;
    /**
     * 
     * @type {object}
     * @memberof Geometry
     */
    'userData'?: object;
    /**
     * 
     * @type {number}
     * @memberof Geometry
     */
    'boundaryDimension'?: number;
    /**
     * 
     * @type {Envelope}
     * @memberof Geometry
     */
    'envelopeInternal'?: Envelope;
    /**
     * 
     * @type {number}
     * @memberof Geometry
     */
    'srid'?: number;
    /**
     * 
     * @type {PrecisionModel}
     * @memberof Geometry
     */
    'precisionModel'?: PrecisionModel;
    /**
     * 
     * @type {boolean}
     * @memberof Geometry
     */
    'rectangle'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Geometry
     */
    'area'?: number;
    /**
     * 
     * @type {Point}
     * @memberof Geometry
     */
    'centroid'?: Point;
    /**
     * 
     * @type {Point}
     * @memberof Geometry
     */
    'interiorPoint'?: Point;
    /**
     * 
     * @type {Geometry}
     * @memberof Geometry
     */
    'boundary'?: Geometry;
    /**
     * 
     * @type {string}
     * @memberof Geometry
     */
    'geometryType'?: string;
    /**
     * 
     * @type {number}
     * @memberof Geometry
     */
    'dimension'?: number;
    /**
     * 
     * @type {number}
     * @memberof Geometry
     */
    'numGeometries'?: number;
    /**
     * 
     * @type {Coordinate}
     * @memberof Geometry
     */
    'coordinate'?: Coordinate;
    /**
     * 
     * @type {Array<Coordinate>}
     * @memberof Geometry
     */
    'coordinates'?: Array<Coordinate>;
    /**
     * 
     * @type {number}
     * @memberof Geometry
     */
    'numPoints'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof Geometry
     */
    'simple'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Geometry
     */
    'length'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof Geometry
     */
    'empty'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof Geometry
     */
    'valid'?: boolean;
}

