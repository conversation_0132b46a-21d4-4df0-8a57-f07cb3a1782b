/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface PaymentUpdateDTO
 */
export interface PaymentUpdateDTO {
    /**
     * 구독 상태
     * @type {string}
     * @memberof PaymentUpdateDTO
     */
    'status'?: PaymentUpdateDTOStatusEnum;
    /**
     * 구독 유형
     * @type {string}
     * @memberof PaymentUpdateDTO
     */
    'subscriptionType'?: PaymentUpdateDTOSubscriptionTypeEnum;
    /**
     * 자동 갱신 여부
     * @type {boolean}
     * @memberof PaymentUpdateDTO
     */
    'autoRenewal'?: boolean;
    /**
     * 결제 날짜
     * @type {string}
     * @memberof PaymentUpdateDTO
     */
    'paidDate'?: string;
    /**
     * 만료 날짜
     * @type {string}
     * @memberof PaymentUpdateDTO
     */
    'expirationDate'?: string;
    /**
     * 구독 이름
     * @type {string}
     * @memberof PaymentUpdateDTO
     */
    'subscriptionName'?: string;
    /**
     * 가격
     * @type {string}
     * @memberof PaymentUpdateDTO
     */
    'price'?: string;
    /**
     * 취소 날짜
     * @type {string}
     * @memberof PaymentUpdateDTO
     */
    'canceledDate'?: string;
}

export const PaymentUpdateDTOStatusEnum = {
    Pending: 'PENDING',
    Unconfirmed: 'UNCONFIRMED',
    Cancelled: 'CANCELLED',
    Refunded: 'REFUNDED',
    Active: 'ACTIVE',
    Exited: 'EXITED'
} as const;

export type PaymentUpdateDTOStatusEnum = typeof PaymentUpdateDTOStatusEnum[keyof typeof PaymentUpdateDTOStatusEnum];
export const PaymentUpdateDTOSubscriptionTypeEnum = {
    Yearly: 'YEARLY',
    Monthly: 'MONTHLY'
} as const;

export type PaymentUpdateDTOSubscriptionTypeEnum = typeof PaymentUpdateDTOSubscriptionTypeEnum[keyof typeof PaymentUpdateDTOSubscriptionTypeEnum];


