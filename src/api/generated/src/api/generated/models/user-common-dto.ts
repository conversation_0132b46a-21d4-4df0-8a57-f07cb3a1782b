/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 사용자 통합 정보를 위한 데이터 전송 객체
 * @export
 * @interface UserCommonDTO
 */
export interface UserCommonDTO {
    /**
     * 사용자 ID
     * @type {number}
     * @memberof UserCommonDTO
     */
    'userId'?: number;
    /**
     * 로그인 ID
     * @type {string}
     * @memberof UserCommonDTO
     */
    'loginId'?: string;
    /**
     * 사용자 이름
     * @type {string}
     * @memberof UserCommonDTO
     */
    'name'?: string;
    /**
     * 사용자의 이메일
     * @type {string}
     * @memberof UserCommonDTO
     */
    'email'?: string;
    /**
     * 사용자의 휴대폰 번호
     * @type {string}
     * @memberof UserCommonDTO
     */
    'phoneNumber'?: string;
    /**
     * 사용자 유형
     * @type {string}
     * @memberof UserCommonDTO
     */
    'userType'?: string;
    /**
     * 사용자 권한 그룹
     * @type {string}
     * @memberof UserCommonDTO
     */
    'authGroup'?: string;
    /**
     * 사용자의 소속 회사/부서 코드
     * @type {string}
     * @memberof UserCommonDTO
     */
    'office'?: string;
    /**
     * 사용자의 소속 회사/부서명
     * @type {string}
     * @memberof UserCommonDTO
     */
    'officeName'?: string;
    /**
     * 사용자 선호 언어
     * @type {string}
     * @memberof UserCommonDTO
     */
    'language'?: string;
    /**
     * 비밀번호
     * @type {string}
     * @memberof UserCommonDTO
     */
    'password'?: string;
    /**
     * 영문 이름 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'ename'?: string;
    /**
     * 최초 접속일 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'firstDate'?: string;
    /**
     * 앱 이름 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'appname'?: string;
    /**
     * 앱 버전 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'appversion'?: string;
    /**
     * 생성자 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'createBy'?: string;
    /**
     * 생성일 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'createDate'?: string;
    /**
     * 수정자 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'modifyBy'?: string;
    /**
     * 수정일 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'modifyDate'?: string;
    /**
     * 비고 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'remark'?: string;
    /**
     * 일반전화번호 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'telNo'?: string;
    /**
     * 지점 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'branch'?: string;
    /**
     * 직급 코드 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'titleCode'?: string;
    /**
     * 직급명 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'title'?: string;
    /**
     * 부서 코드 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'officePart'?: string;
    /**
     * 부서명 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'officePartName'?: string;
    /**
     * 사업자 코드 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'bsnsCd'?: string;
    /**
     * 고객 코드 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'customerCode'?: string;
    /**
     * 비밀번호 만료일 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'pwExpiredDate'?: string;
    /**
     * RMS 구분 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'rmsGbn'?: string;
    /**
     * 회사 코드 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'compCd'?: string;
    /**
     * IDC 사용자 여부 (PssUser 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'idcUser'?: string;
    /**
     * 주소1 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'addr1'?: string;
    /**
     * 우편번호 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'zipCode'?: string;
    /**
     * 국가 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'country'?: string;
    /**
     * 단위 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'unit'?: string;
    /**
     * 날짜형식 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'dateFormat'?: string;
    /**
     * SMS 수신여부 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'smsSvcYn'?: string;
    /**
     * 이메일 수신여부 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'emailSvcYn'?: string;
    /**
     * 약관동의여부 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'agreeYn'?: string;
    /**
     * 비밀번호 오류횟수 (Tmh04du050 전용)
     * @type {number}
     * @memberof UserCommonDTO
     */
    'pwerrcnt'?: number;
    /**
     * 비밀번호 오류일자 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'pwerrdate'?: string;
    /**
     * 딜러 코드 
     * @type {string}
     * @memberof UserCommonDTO
     */
    'dealerCode'?: string;
    /**
     * 이메일 주소1 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'mailAddr1'?: string;
    /**
     * 이메일 주소2 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'mailAddr2'?: string;
    /**
     * 이메일 주소3 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'mailAddr3'?: string;
    /**
     * 휴대폰 번호1 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'mobileNo1'?: string;
    /**
     * 휴대폰 번호2 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'mobileNo2'?: string;
    /**
     * 휴대폰 번호3 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'mobileNo3'?: string;
    /**
     * 등록자ID (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'regUserId'?: string;
    /**
     * 등록일 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'regDate'?: string;
    /**
     * 수정자ID (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'upUserId'?: string;
    /**
     * 수정일 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'upDate'?: string;
    /**
     * 휴면계정여부 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'dormancyYn'?: string;
    /**
     * PUSH 수신여부 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'pushSvnYn'?: string;
    /**
     * 보유장비수 (Tmh04du050 전용)
     * @type {number}
     * @memberof UserCommonDTO
     */
    'machineCnt'?: number;
    /**
     * 서비스중인장비수 (Tmh04du050 전용)
     * @type {number}
     * @memberof UserCommonDTO
     */
    'serviceCnt'?: number;
    /**
     * 기타속성1 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'attribute1'?: string;
    /**
     * 전화번호 국가코드 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'phoneCode'?: string;
    /**
     * 원격시동앱 사용유무 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'remoteApp'?: string;
    /**
     * 원천시스템 (Tmh04du050 전용)
     * @type {string}
     * @memberof UserCommonDTO
     */
    'srcSys'?: string;
    /**
     * 초기화 구분
     * @type {string}
     * @memberof UserCommonDTO
     */
    'initGb'?: string;
    /**
     * 초기화 날짜
     * @type {string}
     * @memberof UserCommonDTO
     */
    'initDate'?: string;
}

