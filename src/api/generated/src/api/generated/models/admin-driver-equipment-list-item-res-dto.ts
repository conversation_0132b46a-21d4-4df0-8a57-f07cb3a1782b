/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AdminEquipmentStatusResDTO } from './admin-equipment-status-res-dto';

/**
 * 운전자의 장비 정보
 * @export
 * @interface AdminDriverEquipmentListItemResDTO
 */
export interface AdminDriverEquipmentListItemResDTO {
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'driverId'?: number;
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'equipmentId'?: number;
    /**
     * 장비타입:<br> VEHICLE: 차량<br> TRUCK: 트럭<br> HEAVY_EQUIPMENT: 중장비<br> AGRICULTURAL: 농기계<br> DRONE: 드론<br> ROBOT: 로봇<br> 
     * @type {string}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'equipmentType'?: AdminDriverEquipmentListItemResDTOEquipmentTypeEnum;
    /**
     * 차량타입:<br> CAR: 일반 승용차<br> SUV_RV: 다목적 승용차(SUV, RV 등)<br> TRUCK: 트럭<br> BUS: 버스<br> TRAILER: 트레일러<br> LOW_SPEED: 저속 차량<br> BIKE: 이륜차(오토바이 등)<br> 
     * @type {string}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'vehicleType'?: AdminDriverEquipmentListItemResDTOVehicleTypeEnum;
    /**
     * 제조사
     * @type {string}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'manufacturer'?: string;
    /**
     * 모델명
     * @type {string}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'modelName'?: string;
    /**
     * 트림명
     * @type {string}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'trimName'?: string;
    /**
     * 생산년도
     * @type {number}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'productYear'?: number;
    /**
     * 이미지경로
     * @type {string}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'imagePath'?: string;
    /**
     * VIN No
     * @type {string}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'serialNo'?: string;
    /**
     * 차량번호
     * @type {string}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'plateNo'?: string;
    /**
     * 
     * @type {AdminEquipmentStatusResDTO}
     * @memberof AdminDriverEquipmentListItemResDTO
     */
    'status'?: AdminEquipmentStatusResDTO;
}

export const AdminDriverEquipmentListItemResDTOEquipmentTypeEnum = {
    Vehicle: 'VEHICLE',
    Truck: 'TRUCK',
    HeavyEquipment: 'HEAVY_EQUIPMENT',
    Agricultural: 'AGRICULTURAL',
    Drone: 'DRONE',
    Robot: 'ROBOT'
} as const;

export type AdminDriverEquipmentListItemResDTOEquipmentTypeEnum = typeof AdminDriverEquipmentListItemResDTOEquipmentTypeEnum[keyof typeof AdminDriverEquipmentListItemResDTOEquipmentTypeEnum];
export const AdminDriverEquipmentListItemResDTOVehicleTypeEnum = {
    Car: 'CAR',
    SuvRv: 'SUV_RV',
    Truck: 'TRUCK',
    Bus: 'BUS',
    Trailer: 'TRAILER',
    LowSpeed: 'LOW_SPEED',
    Bike: 'BIKE'
} as const;

export type AdminDriverEquipmentListItemResDTOVehicleTypeEnum = typeof AdminDriverEquipmentListItemResDTOVehicleTypeEnum[keyof typeof AdminDriverEquipmentListItemResDTOVehicleTypeEnum];


