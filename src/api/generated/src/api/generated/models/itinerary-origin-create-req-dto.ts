/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { SwaggerPointMixin } from './swagger-point-mixin';

/**
 * 여정출발지 정보
 * @export
 * @interface ItineraryOriginCreateReqDTO
 */
export interface ItineraryOriginCreateReqDTO {
    /**
     * 여정의 출발지 Title
     * @type {string}
     * @memberof ItineraryOriginCreateReqDTO
     */
    'originName'?: string;
    /**
     * 여정의 출발지 주소
     * @type {string}
     * @memberof ItineraryOriginCreateReqDTO
     */
    'originAddress': string;
    /**
     * 
     * @type {SwaggerPointMixin}
     * @memberof ItineraryOriginCreateReqDTO
     */
    'originLocation': SwaggerPointMixin;
}

