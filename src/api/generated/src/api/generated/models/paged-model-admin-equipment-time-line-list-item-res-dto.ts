/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AdminEquipmentTimeLineListItemResDTO } from './admin-equipment-time-line-list-item-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { PageMetadata } from './page-metadata';

/**
 * 
 * @export
 * @interface PagedModelAdminEquipmentTimeLineListItemResDTO
 */
export interface PagedModelAdminEquipmentTimeLineListItemResDTO {
    /**
     * 
     * @type {Array<AdminEquipmentTimeLineListItemResDTO>}
     * @memberof PagedModelAdminEquipmentTimeLineListItemResDTO
     */
    'content'?: Array<AdminEquipmentTimeLineListItemResDTO>;
    /**
     * 
     * @type {PageMetadata}
     * @memberof PagedModelAdminEquipmentTimeLineListItemResDTO
     */
    'page'?: PageMetadata;
}

