/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ItineraryDestinationCreateReqDTO } from './itinerary-destination-create-req-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { ItineraryOriginCreateReqDTO } from './itinerary-origin-create-req-dto';

/**
 * 여정 생성요청
 * @export
 * @interface ItineraryCreateReqDTO
 */
export interface ItineraryCreateReqDTO {
    /**
     * 차량 아이디
     * @type {number}
     * @memberof ItineraryCreateReqDTO
     */
    'vehicleId': number;
    /**
     * 차량 이름
     * @type {string}
     * @memberof ItineraryCreateReqDTO
     */
    'vehicleName'?: string;
    /**
     * 여정계획 아이디
     * @type {number}
     * @memberof ItineraryCreateReqDTO
     */
    'itineraryPlanId'?: number;
    /**
     * 여정 이름
     * @type {string}
     * @memberof ItineraryCreateReqDTO
     */
    'itineraryName'?: string;
    /**
     * 여정의 마지막 목적지까지의 예상 경로 거리 (m)
     * @type {number}
     * @memberof ItineraryCreateReqDTO
     */
    'estimatedDistance'?: number;
    /**
     * 여정의 마지막 목적지까지의 예상 소요 시간 (sec)
     * @type {number}
     * @memberof ItineraryCreateReqDTO
     */
    'estimatedTime'?: number;
    /**
     * 
     * @type {ItineraryOriginCreateReqDTO}
     * @memberof ItineraryCreateReqDTO
     */
    'origin': ItineraryOriginCreateReqDTO;
    /**
     * 여정목적지 정보
     * @type {Array<ItineraryDestinationCreateReqDTO>}
     * @memberof ItineraryCreateReqDTO
     */
    'destinations': Array<ItineraryDestinationCreateReqDTO>;
}

