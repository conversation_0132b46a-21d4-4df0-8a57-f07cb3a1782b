/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AdminFleetBasicResDTO } from './admin-fleet-basic-res-dto';

/**
 * 운전자 정보
 * @export
 * @interface AdminDriverListItemResDTO
 */
export interface AdminDriverListItemResDTO {
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverListItemResDTO
     */
    'driverId'?: number;
    /**
     * 운전자로그인 아이디(이메일)
     * @type {string}
     * @memberof AdminDriverListItemResDTO
     */
    'loginId'?: string;
    /**
     * 운전자명
     * @type {string}
     * @memberof AdminDriverListItemResDTO
     */
    'driverName'?: string;
    /**
     * 운전자전화번호국가코드
     * @type {string}
     * @memberof AdminDriverListItemResDTO
     */
    'driverCountryDialCode'?: string;
    /**
     * 운전자전화번호
     * @type {string}
     * @memberof AdminDriverListItemResDTO
     */
    'driverPhone'?: string;
    /**
     * 면허번호
     * @type {string}
     * @memberof AdminDriverListItemResDTO
     */
    'licenseNo'?: string;
    /**
     * 면허분류
     * @type {string}
     * @memberof AdminDriverListItemResDTO
     */
    'licenseClass'?: AdminDriverListItemResDTOLicenseClassEnum;
    /**
     * 운전자상태
     * @type {string}
     * @memberof AdminDriverListItemResDTO
     */
    'driverStatus'?: AdminDriverListItemResDTODriverStatusEnum;
    /**
     * 장비 수
     * @type {number}
     * @memberof AdminDriverListItemResDTO
     */
    'equipmentCount'?: number;
    /**
     * 플릿정보
     * @type {Array<AdminFleetBasicResDTO>}
     * @memberof AdminDriverListItemResDTO
     */
    'fleets'?: Array<AdminFleetBasicResDTO>;
}

export const AdminDriverListItemResDTOLicenseClassEnum = {
    ClassA: 'CLASS_A',
    ClassB: 'CLASS_B',
    ClassC: 'CLASS_C',
    ClassD: 'CLASS_D',
    ClassM: 'CLASS_M'
} as const;

export type AdminDriverListItemResDTOLicenseClassEnum = typeof AdminDriverListItemResDTOLicenseClassEnum[keyof typeof AdminDriverListItemResDTOLicenseClassEnum];
export const AdminDriverListItemResDTODriverStatusEnum = {
    OnDuty: 'ON_DUTY',
    Idle: 'IDLE'
} as const;

export type AdminDriverListItemResDTODriverStatusEnum = typeof AdminDriverListItemResDTODriverStatusEnum[keyof typeof AdminDriverListItemResDTODriverStatusEnum];


