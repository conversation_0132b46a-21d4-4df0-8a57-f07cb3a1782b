/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { SwaggerPointMixin } from './swagger-point-mixin';

/**
 * 장비 상태 정보
 * @export
 * @interface AdminEquipmentStatusResDTO
 */
export interface AdminEquipmentStatusResDTO {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentStatusResDTO
     */
    'equipmentId'?: number;
    /**
     * 운행운전자아이디
     * @type {number}
     * @memberof AdminEquipmentStatusResDTO
     */
    'currentDriverId'?: number;
    /**
     * 운행여정계획아이디
     * @type {number}
     * @memberof AdminEquipmentStatusResDTO
     */
    'currentItineraryPlanId'?: number;
    /**
     * 운행여정아이디
     * @type {number}
     * @memberof AdminEquipmentStatusResDTO
     */
    'currentItineraryId'?: number;
    /**
     * 마일리지
     * @type {number}
     * @memberof AdminEquipmentStatusResDTO
     */
    'mileage'?: number;
    /**
     * 동작상태:<br> RUNNING: 운행 중<br> IDLE: 유휴<br> 
     * @type {string}
     * @memberof AdminEquipmentStatusResDTO
     */
    'operationStatus'?: AdminEquipmentStatusResDTOOperationStatusEnum;
    /**
     * GPS상태:<br> CONNECTED: 통신 가능<br> DISCONNECTED: 통신 두절<br> 
     * @type {string}
     * @memberof AdminEquipmentStatusResDTO
     */
    'gpsStatus'?: AdminEquipmentStatusResDTOGpsStatusEnum;
    /**
     * 고장상태:<br> BREAKDOWN: 고장<br> REPAIRING: 정비 중<br> NONE: 고장 없음<br> 
     * @type {string}
     * @memberof AdminEquipmentStatusResDTO
     */
    'breakdownStatus'?: AdminEquipmentStatusResDTOBreakdownStatusEnum;
    /**
     * 
     * @type {SwaggerPointMixin}
     * @memberof AdminEquipmentStatusResDTO
     */
    'recentLocation'?: SwaggerPointMixin;
}

export const AdminEquipmentStatusResDTOOperationStatusEnum = {
    Running: 'RUNNING',
    Idle: 'IDLE'
} as const;

export type AdminEquipmentStatusResDTOOperationStatusEnum = typeof AdminEquipmentStatusResDTOOperationStatusEnum[keyof typeof AdminEquipmentStatusResDTOOperationStatusEnum];
export const AdminEquipmentStatusResDTOGpsStatusEnum = {
    Connected: 'CONNECTED',
    Disconnected: 'DISCONNECTED'
} as const;

export type AdminEquipmentStatusResDTOGpsStatusEnum = typeof AdminEquipmentStatusResDTOGpsStatusEnum[keyof typeof AdminEquipmentStatusResDTOGpsStatusEnum];
export const AdminEquipmentStatusResDTOBreakdownStatusEnum = {
    Breakdown: 'BREAKDOWN',
    Repairing: 'REPAIRING',
    None: 'NONE'
} as const;

export type AdminEquipmentStatusResDTOBreakdownStatusEnum = typeof AdminEquipmentStatusResDTOBreakdownStatusEnum[keyof typeof AdminEquipmentStatusResDTOBreakdownStatusEnum];


