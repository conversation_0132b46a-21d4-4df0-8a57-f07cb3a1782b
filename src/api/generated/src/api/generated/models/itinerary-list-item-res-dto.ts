/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 여정간략 조회응답
 * @export
 * @interface ItineraryListItemResDTO
 */
export interface ItineraryListItemResDTO {
    /**
     * 여정 아이디
     * @type {number}
     * @memberof ItineraryListItemResDTO
     */
    'itineraryId'?: number;
    /**
     * 여정 이름
     * @type {string}
     * @memberof ItineraryListItemResDTO
     */
    'itineraryName'?: string;
    /**
     * 차량 이름
     * @type {string}
     * @memberof ItineraryListItemResDTO
     */
    'vehicleName'?: string;
    /**
     * 여정의 총 주행거리 (m)
     * @type {number}
     * @memberof ItineraryListItemResDTO
     */
    'drivedDistance'?: number;
    /**
     * 여정의 총 주행시간 (sec)
     * @type {number}
     * @memberof ItineraryListItemResDTO
     */
    'drivedTime'?: number;
}

