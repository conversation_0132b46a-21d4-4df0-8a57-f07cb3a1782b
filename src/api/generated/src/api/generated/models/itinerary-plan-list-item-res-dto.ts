/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ItineraryPlanDestinationResDTO } from './itinerary-plan-destination-res-dto';

/**
 * 여정계획 조회응답
 * @export
 * @interface ItineraryPlanListItemResDTO
 */
export interface ItineraryPlanListItemResDTO {
    /**
     * 여정계획 아이디
     * @type {number}
     * @memberof ItineraryPlanListItemResDTO
     */
    'itineraryPlanId'?: number;
    /**
     * 여정계획 이름
     * @type {string}
     * @memberof ItineraryPlanListItemResDTO
     */
    'itineraryPlanName'?: string;
    /**
     * 여정계획목적지 목록
     * @type {Array<ItineraryPlanDestinationResDTO>}
     * @memberof ItineraryPlanListItemResDTO
     */
    'destinations'?: Array<ItineraryPlanDestinationResDTO>;
}

