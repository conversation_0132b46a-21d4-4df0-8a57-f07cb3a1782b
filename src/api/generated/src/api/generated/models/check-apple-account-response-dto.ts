/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface CheckAppleAccountResponseDTO
 */
export interface CheckAppleAccountResponseDTO {
    /**
     * 
     * @type {boolean}
     * @memberof CheckAppleAccountResponseDTO
     */
    'exists'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CheckAppleAccountResponseDTO
     */
    'email'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CheckAppleAccountResponseDTO
     */
    'validToken'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof CheckAppleAccountResponseDTO
     */
    'appleAccountExists'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CheckAppleAccountResponseDTO
     */
    'accessToken'?: string;
    /**
     * 
     * @type {string}
     * @memberof CheckAppleAccountResponseDTO
     */
    'refreshToken'?: string;
    /**
     * 
     * @type {number}
     * @memberof CheckAppleAccountResponseDTO
     */
    'userId'?: number;
    /**
     * 
     * @type {string}
     * @memberof CheckAppleAccountResponseDTO
     */
    'appleUserId'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CheckAppleAccountResponseDTO
     */
    'emailVerified'?: boolean;
}

