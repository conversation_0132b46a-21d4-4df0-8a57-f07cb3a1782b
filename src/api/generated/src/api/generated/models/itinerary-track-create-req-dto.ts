/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { SwaggerPointMixin } from './swagger-point-mixin';

/**
 * 여정위치정보 생성요청
 * @export
 * @interface ItineraryTrackCreateReqDTO
 */
export interface ItineraryTrackCreateReqDTO {
    /**
     * 여정 아이디
     * @type {number}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'itineraryId': number;
    /**
     * 여정경로 아이디
     * @type {number}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'itineraryRouteId': number;
    /**
     * 차량 아이디
     * @type {number}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'vehicleId': number;
    /**
     * 
     * @type {SwaggerPointMixin}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'mapMatchingLocation': SwaggerPointMixin;
    /**
     * 
     * @type {SwaggerPointMixin}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'gpsLocation': SwaggerPointMixin;
    /**
     * 헤딩 각도
     * @type {number}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'headingAngle': number;
    /**
     * 속도
     * @type {number}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'speed': number;
    /**
     * 여정경로의 목적지까지 남은 거리 (m)
     * @type {number}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'routeRemainingDistance'?: number;
    /**
     * 여정경로의 목적지까지 남은 시간 (sec)
     * @type {number}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'routeRemainingTime'?: number;
    /**
     * 
     * @type {Array<SwaggerPointMixin>}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'routeRemainingGeometry'?: Array<SwaggerPointMixin>;
    /**
     * 여정의 마지막 목적지까지의 남은 거리 (m)
     * @type {number}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'itineraryRemainingDistance'?: number;
    /**
     * 여정의 마지막 목적지까지의 남은 시간 (sec)
     * @type {number}
     * @memberof ItineraryTrackCreateReqDTO
     */
    'itineraryRemainingTime'?: number;
}

