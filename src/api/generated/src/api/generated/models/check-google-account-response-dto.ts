/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 구글 계정 확인 응답 DTO
 * @export
 * @interface CheckGoogleAccountResponseDTO
 */
export interface CheckGoogleAccountResponseDTO {
    /**
     * 계정 존재 여부
     * @type {boolean}
     * @memberof CheckGoogleAccountResponseDTO
     */
    'exists'?: boolean;
    /**
     * 이메일 주소
     * @type {string}
     * @memberof CheckGoogleAccountResponseDTO
     */
    'email'?: string;
    /**
     * 토큰 유효성
     * @type {boolean}
     * @memberof CheckGoogleAccountResponseDTO
     */
    'validToken'?: boolean;
    /**
     * Google 계정 존재 여부
     * @type {boolean}
     * @memberof CheckGoogleAccountResponseDTO
     */
    'googleAccountExists'?: boolean;
    /**
     * 액세스 토큰 (계정이 있고 토큰이 유효한 경우에만 제공)
     * @type {string}
     * @memberof CheckGoogleAccountResponseDTO
     */
    'accessToken'?: string;
    /**
     * 리프레시 토큰 (계정이 있고 토큰이 유효한 경우에만 제공)
     * @type {string}
     * @memberof CheckGoogleAccountResponseDTO
     */
    'refreshToken'?: string;
    /**
     * 사용자 ID
     * @type {number}
     * @memberof CheckGoogleAccountResponseDTO
     */
    'userId'?: number;
    /**
     * 이메일 인증 여부
     * @type {boolean}
     * @memberof CheckGoogleAccountResponseDTO
     */
    'emailVerified'?: boolean;
}

