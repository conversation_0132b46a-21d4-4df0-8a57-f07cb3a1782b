/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ItineraryPlanDestinationCreateReqDTO } from './itinerary-plan-destination-create-req-dto';

/**
 * 여정계획 생성요청
 * @export
 * @interface ItineraryPlanCreateReqDTO
 */
export interface ItineraryPlanCreateReqDTO {
    /**
     * 여정계획 이름
     * @type {string}
     * @memberof ItineraryPlanCreateReqDTO
     */
    'itineraryPlanName': string;
    /**
     * 여정계획목적지 목록
     * @type {Array<ItineraryPlanDestinationCreateReqDTO>}
     * @memberof ItineraryPlanCreateReqDTO
     */
    'destinations'?: Array<ItineraryPlanDestinationCreateReqDTO>;
}

