/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 운전자 생성요청
 * @export
 * @interface AdminDriverCreateReqDTO
 */
export interface AdminDriverCreateReqDTO {
    /**
     * 플릿아이디목록
     * @type {Array<number>}
     * @memberof AdminDriverCreateReqDTO
     */
    'fleetIdList'?: Array<number>;
    /**
     * 장비아이디목록
     * @type {Array<number>}
     * @memberof AdminDriverCreateReqDTO
     */
    'equipmentIdList'?: Array<number>;
    /**
     * 운전자로그인 아이디(이메일)
     * @type {string}
     * @memberof AdminDriverCreateReqDTO
     */
    'loginId': string;
    /**
     * 운전자로그인 비밀번호
     * @type {string}
     * @memberof AdminDriverCreateReqDTO
     */
    'password': string;
    /**
     * 운전자명
     * @type {string}
     * @memberof AdminDriverCreateReqDTO
     */
    'driverName': string;
    /**
     * 운전자전화번호국가코드
     * @type {string}
     * @memberof AdminDriverCreateReqDTO
     */
    'driverCountryDialCode': string;
    /**
     * 운전자전화번호
     * @type {string}
     * @memberof AdminDriverCreateReqDTO
     */
    'driverPhone': string;
    /**
     * 면허번호
     * @type {string}
     * @memberof AdminDriverCreateReqDTO
     */
    'licenseNo': string;
    /**
     * 면허분류
     * @type {string}
     * @memberof AdminDriverCreateReqDTO
     */
    'licenseClass'?: AdminDriverCreateReqDTOLicenseClassEnum;
    /**
     * 면허발급주
     * @type {string}
     * @memberof AdminDriverCreateReqDTO
     */
    'licenseIssueState'?: AdminDriverCreateReqDTOLicenseIssueStateEnum;
    /**
     * 면허만료일
     * @type {string}
     * @memberof AdminDriverCreateReqDTO
     */
    'licenseExpireDt'?: string;
    /**
     * 운전자성별:<br> MALE: 남성<br> FEMALE: 여성<br> 
     * @type {string}
     * @memberof AdminDriverCreateReqDTO
     */
    'driverGender'?: AdminDriverCreateReqDTODriverGenderEnum;
    /**
     * 운전자관리용아이디
     * @type {string}
     * @memberof AdminDriverCreateReqDTO
     */
    'driverManagementId'?: string;
}

export const AdminDriverCreateReqDTOLicenseClassEnum = {
    ClassA: 'CLASS_A',
    ClassB: 'CLASS_B',
    ClassC: 'CLASS_C',
    ClassD: 'CLASS_D',
    ClassM: 'CLASS_M'
} as const;

export type AdminDriverCreateReqDTOLicenseClassEnum = typeof AdminDriverCreateReqDTOLicenseClassEnum[keyof typeof AdminDriverCreateReqDTOLicenseClassEnum];
export const AdminDriverCreateReqDTOLicenseIssueStateEnum = {
    Alabama: 'ALABAMA',
    Alaska: 'ALASKA',
    Arizona: 'ARIZONA',
    Arkansas: 'ARKANSAS',
    California: 'CALIFORNIA',
    Colorado: 'COLORADO',
    Connecticut: 'CONNECTICUT',
    Delaware: 'DELAWARE',
    Florida: 'FLORIDA',
    Georgia: 'GEORGIA',
    Hawaii: 'HAWAII',
    Idaho: 'IDAHO',
    Illinois: 'ILLINOIS',
    Indiana: 'INDIANA',
    Iowa: 'IOWA',
    Kansas: 'KANSAS',
    Kentucky: 'KENTUCKY',
    Louisiana: 'LOUISIANA',
    Maine: 'MAINE',
    Maryland: 'MARYLAND',
    Massachusetts: 'MASSACHUSETTS',
    Michigan: 'MICHIGAN',
    Minnesota: 'MINNESOTA',
    Mississippi: 'MISSISSIPPI',
    Missouri: 'MISSOURI',
    Montana: 'MONTANA',
    Nebraska: 'NEBRASKA',
    Nevada: 'NEVADA',
    NewHampshire: 'NEW_HAMPSHIRE',
    NewJersey: 'NEW_JERSEY',
    NewMexico: 'NEW_MEXICO',
    NewYork: 'NEW_YORK',
    NorthCarolina: 'NORTH_CAROLINA',
    NorthDakota: 'NORTH_DAKOTA',
    Ohio: 'OHIO',
    Oklahoma: 'OKLAHOMA',
    Oregon: 'OREGON',
    Pennsylvania: 'PENNSYLVANIA',
    RhodeIsland: 'RHODE_ISLAND',
    SouthCarolina: 'SOUTH_CAROLINA',
    SouthDakota: 'SOUTH_DAKOTA',
    Tennessee: 'TENNESSEE',
    Texas: 'TEXAS',
    Utah: 'UTAH',
    Vermont: 'VERMONT',
    Virginia: 'VIRGINIA',
    Washington: 'WASHINGTON',
    WashingtonDc: 'WASHINGTON_DC',
    WestVirginia: 'WEST_VIRGINIA',
    Wisconsin: 'WISCONSIN',
    Wyoming: 'WYOMING'
} as const;

export type AdminDriverCreateReqDTOLicenseIssueStateEnum = typeof AdminDriverCreateReqDTOLicenseIssueStateEnum[keyof typeof AdminDriverCreateReqDTOLicenseIssueStateEnum];
export const AdminDriverCreateReqDTODriverGenderEnum = {
    Male: 'MALE',
    Female: 'FEMALE'
} as const;

export type AdminDriverCreateReqDTODriverGenderEnum = typeof AdminDriverCreateReqDTODriverGenderEnum[keyof typeof AdminDriverCreateReqDTODriverGenderEnum];


