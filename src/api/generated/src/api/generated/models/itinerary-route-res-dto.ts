/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { SwaggerPointMixin } from './swagger-point-mixin';

/**
 * 여정경로 조회응답
 * @export
 * @interface ItineraryRouteResDTO
 */
export interface ItineraryRouteResDTO {
    /**
     * 여정경로 아이디
     * @type {number}
     * @memberof ItineraryRouteResDTO
     */
    'itineraryRouteId'?: number;
    /**
     * 여정경로의 목적지 Title
     * @type {string}
     * @memberof ItineraryRouteResDTO
     */
    'arrivalName'?: string;
    /**
     * 여정경로의 목적지 주소
     * @type {string}
     * @memberof ItineraryRouteResDTO
     */
    'arrivalAddress'?: string;
    /**
     * 
     * @type {SwaggerPointMixin}
     * @memberof ItineraryRouteResDTO
     */
    'arrivalLocation'?: SwaggerPointMixin;
    /**
     * 여정경로의 목적지에 도착한 시각, 완료된 경우 설정됩니다
     * @type {string}
     * @memberof ItineraryRouteResDTO
     */
    'arrivalTime'?: string;
}

