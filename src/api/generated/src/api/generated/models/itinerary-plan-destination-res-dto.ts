/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { LineString } from './line-string';
// May contain unused imports in some cases
// @ts-ignore
import type { SwaggerPointMixin } from './swagger-point-mixin';

/**
 * 여정계획목적지 조회응답
 * @export
 * @interface ItineraryPlanDestinationResDTO
 */
export interface ItineraryPlanDestinationResDTO {
    /**
     * 여정계획목적지 아이디
     * @type {number}
     * @memberof ItineraryPlanDestinationResDTO
     */
    'itineraryPlanDestinationId'?: number;
    /**
     * 여정경로의 목적지 Title
     * @type {string}
     * @memberof ItineraryPlanDestinationResDTO
     */
    'destinationName'?: string;
    /**
     * 여정경로의 목적지 주소
     * @type {string}
     * @memberof ItineraryPlanDestinationResDTO
     */
    'destinationAddress'?: string;
    /**
     * 
     * @type {SwaggerPointMixin}
     * @memberof ItineraryPlanDestinationResDTO
     */
    'destinationLocation'?: SwaggerPointMixin;
    /**
     * 해당 주행 구간의 예상 경로 거리 (m)
     * @type {number}
     * @memberof ItineraryPlanDestinationResDTO
     */
    'estimatedDistance'?: number;
    /**
     * 해당 주행 구간의 예상 소요 시간 (sec)
     * @type {number}
     * @memberof ItineraryPlanDestinationResDTO
     */
    'estimatedTime'?: number;
    /**
     * 
     * @type {LineString}
     * @memberof ItineraryPlanDestinationResDTO
     */
    'estimatedGeometry'?: LineString;
}

