/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 결제 히스토리 응답 DTO
 * @export
 * @interface PaymentHistoryResponseDTO
 */
export interface PaymentHistoryResponseDTO {
    /**
     * 히스토리 ID
     * @type {number}
     * @memberof PaymentHistoryResponseDTO
     */
    'historyId'?: number;
    /**
     * 결제 ID
     * @type {number}
     * @memberof PaymentHistoryResponseDTO
     */
    'paymentId'?: number;
    /**
     * 히스토리 타입
     * @type {string}
     * @memberof PaymentHistoryResponseDTO
     */
    'historyType'?: PaymentHistoryResponseDTOHistoryTypeEnum;
    /**
     * 변경된 필드명
     * @type {string}
     * @memberof PaymentHistoryResponseDTO
     */
    'fieldName'?: string;
    /**
     * 변경 전 값
     * @type {string}
     * @memberof PaymentHistoryResponseDTO
     */
    'previousValue'?: string;
    /**
     * 변경 후 값
     * @type {string}
     * @memberof PaymentHistoryResponseDTO
     */
    'newValue'?: string;
    /**
     * 변경 일시
     * @type {string}
     * @memberof PaymentHistoryResponseDTO
     */
    'changeDate'?: string;
}

export const PaymentHistoryResponseDTOHistoryTypeEnum = {
    Subscription: 'SUBSCRIPTION',
    Cancellation: 'CANCELLATION',
    Update: 'UPDATE'
} as const;

export type PaymentHistoryResponseDTOHistoryTypeEnum = typeof PaymentHistoryResponseDTOHistoryTypeEnum[keyof typeof PaymentHistoryResponseDTOHistoryTypeEnum];


