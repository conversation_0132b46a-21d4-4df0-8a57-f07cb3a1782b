/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ItineraryPlanDestinationCreateReqDTO } from './itinerary-plan-destination-create-req-dto';

/**
 * 배차 생성요청
 * @export
 * @interface AdminItineraryDispatchCreateReqDTO
 */
export interface AdminItineraryDispatchCreateReqDTO {
    /**
     * 일정명(운행명)
     * @type {string}
     * @memberof AdminItineraryDispatchCreateReqDTO
     */
    'itineraryPlanName': string;
    /**
     * 운행예정일시
     * @type {string}
     * @memberof AdminItineraryDispatchCreateReqDTO
     */
    'scheduledDt': string;
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminItineraryDispatchCreateReqDTO
     */
    'driverId': number;
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminItineraryDispatchCreateReqDTO
     */
    'equipmentId': number;
    /**
     * 여정계획목적지 목록
     * @type {Array<ItineraryPlanDestinationCreateReqDTO>}
     * @memberof AdminItineraryDispatchCreateReqDTO
     */
    'destinations': Array<ItineraryPlanDestinationCreateReqDTO>;
}

