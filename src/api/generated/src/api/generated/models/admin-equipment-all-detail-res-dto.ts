/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AdminCountryBasicResDTO } from './admin-country-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminDealerBasicResDTO } from './admin-dealer-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminDriverBasicResDTO } from './admin-driver-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminEquipmentStatusResDTO } from './admin-equipment-status-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminFleetBasicResDTO } from './admin-fleet-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminServiceCenterBasicResDTO } from './admin-service-center-basic-res-dto';

/**
 * 장비 상세 정보
 * @export
 * @interface AdminEquipmentAllDetailResDTO
 */
export interface AdminEquipmentAllDetailResDTO {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'equipmentId'?: number;
    /**
     * 장비타입:<br> VEHICLE: 차량<br> TRUCK: 트럭<br> HEAVY_EQUIPMENT: 중장비<br> AGRICULTURAL: 농기계<br> DRONE: 드론<br> ROBOT: 로봇<br> 
     * @type {string}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'equipmentType'?: AdminEquipmentAllDetailResDTOEquipmentTypeEnum;
    /**
     * 차량타입:<br> CAR: 일반 승용차<br> SUV_RV: 다목적 승용차(SUV, RV 등)<br> TRUCK: 트럭<br> BUS: 버스<br> TRAILER: 트레일러<br> LOW_SPEED: 저속 차량<br> BIKE: 이륜차(오토바이 등)<br> 
     * @type {string}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'vehicleType'?: AdminEquipmentAllDetailResDTOVehicleTypeEnum;
    /**
     * 제조사
     * @type {string}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'manufacturer'?: string;
    /**
     * 모델명
     * @type {string}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'modelName'?: string;
    /**
     * 트림명
     * @type {string}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'trimName'?: string;
    /**
     * 생산년도
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'productYear'?: number;
    /**
     * 이미지경로
     * @type {string}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'imagePath'?: string;
    /**
     * VIN No
     * @type {string}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'serialNo'?: string;
    /**
     * 차량번호
     * @type {string}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'plateNo'?: string;
    /**
     * 차체형식:<br> SEDAN: 세단<br> HATCHBACK: 해치백<br> COUPE: 쿠페<br> CONVERTIBLE: 컨버터블<br> SUV: SUV<br> VAN: 밴<br> MINIVAN: 미니밴<br> WAGON: 웨건<br> PICKUP_TRUCK: 픽업트럭<br> BUS: 버스<br> MINIBUS: 소형버스<br> HEAVY_TRUCK: 대형화물차<br> TRIKE: 삼륜오토바이<br> OFF_ROAD: 오프로드 차량<br> TRAILER: 트레일러<br> 
     * @type {string}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'vehicleBodyClass'?: AdminEquipmentAllDetailResDTOVehicleBodyClassEnum;
    /**
     * 전장: 장비 길이(mm)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'bodyLength'?: number;
    /**
     * 전고: 장비 높이(mm)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'bodyHeight'?: number;
    /**
     * 전폭: 장비 너비(mm)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'bodyWidth'?: number;
    /**
     * 위험물질: Explosives
     * @type {boolean}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'hazmatExplosives'?: boolean;
    /**
     * 위험물질: Gas
     * @type {boolean}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'hazmatGas'?: boolean;
    /**
     * 위험물질: Flammable
     * @type {boolean}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'hazmatFlammable'?: boolean;
    /**
     * 위험물질: Organic
     * @type {boolean}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'hazmatOrganic'?: boolean;
    /**
     * 위험물질: Poison
     * @type {boolean}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'hazmatPoison'?: boolean;
    /**
     * 위험물질: Radioactive
     * @type {boolean}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'hazmatRadioactive'?: boolean;
    /**
     * 위험물질: Corrosive
     * @type {boolean}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'hazmatCorrosive'?: boolean;
    /**
     * 위험물질: Harmful For Water
     * @type {boolean}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'hazmatHarmfulForWater'?: boolean;
    /**
     * 위험물질: Poisonous Inhalation Hazard
     * @type {boolean}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'hazmatPoisonousInhalationHazard'?: boolean;
    /**
     * 위험물질: Other
     * @type {boolean}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'hazmatOther'?: boolean;
    /**
     * 연료타입:<br> DIESEL: 경유<br> GASOLINE: 휘발유<br> HYBRID: 하이브리드<br> ELECTRIC: 전기<br> 
     * @type {string}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'fuelType'?: AdminEquipmentAllDetailResDTOFuelTypeEnum;
    /**
     * 연비(km/L or km/kWh)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'fuelEfficiency'?: number;
    /**
     * 연료탱크용량(L or kWh)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'fuelTankCapacity'?: number;
    /**
     * 타이어 지름(inch)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'tireDiameter'?: number;
    /**
     * 타이어 폭(mm)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'tireWidth'?: number;
    /**
     * 엔진오일 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'engineOilInterval'?: number;
    /**
     * 오일필터 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'oilFilterInterval'?: number;
    /**
     * 연료필터 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'fuelFilterInterval'?: number;
    /**
     * 에어필터 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'airFilterInterval'?: number;
    /**
     * 브레이크패드 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'brakePadInterval'?: number;
    /**
     * 브레이크 라이닝 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'brakeLiningInterval'?: number;
    /**
     * 타이어 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'tireInterval'?: number;
    /**
     * 냉각수 교체주기(년)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'coolantInterval'?: number;
    /**
     * 배터리 교체주기(년)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'batteryInterval'?: number;
    /**
     * 트랜스미션오일 교체주기(km)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'transmissionOilInterval'?: number;
    /**
     * 타이어위치 교환주기(km)
     * @type {number}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'tireRotationInterval'?: number;
    /**
     * 
     * @type {AdminEquipmentStatusResDTO}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'status'?: AdminEquipmentStatusResDTO;
    /**
     * 
     * @type {AdminCountryBasicResDTO}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'country'?: AdminCountryBasicResDTO;
    /**
     * 
     * @type {AdminDealerBasicResDTO}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'dealer'?: AdminDealerBasicResDTO;
    /**
     * 
     * @type {AdminServiceCenterBasicResDTO}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'serviceCenter'?: AdminServiceCenterBasicResDTO;
    /**
     * 플릿 정보
     * @type {Array<AdminFleetBasicResDTO>}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'fleets'?: Array<AdminFleetBasicResDTO>;
    /**
     * 운전자정보
     * @type {Array<AdminDriverBasicResDTO>}
     * @memberof AdminEquipmentAllDetailResDTO
     */
    'drivers'?: Array<AdminDriverBasicResDTO>;
}

export const AdminEquipmentAllDetailResDTOEquipmentTypeEnum = {
    Vehicle: 'VEHICLE',
    Truck: 'TRUCK',
    HeavyEquipment: 'HEAVY_EQUIPMENT',
    Agricultural: 'AGRICULTURAL',
    Drone: 'DRONE',
    Robot: 'ROBOT'
} as const;

export type AdminEquipmentAllDetailResDTOEquipmentTypeEnum = typeof AdminEquipmentAllDetailResDTOEquipmentTypeEnum[keyof typeof AdminEquipmentAllDetailResDTOEquipmentTypeEnum];
export const AdminEquipmentAllDetailResDTOVehicleTypeEnum = {
    Car: 'CAR',
    SuvRv: 'SUV_RV',
    Truck: 'TRUCK',
    Bus: 'BUS',
    Trailer: 'TRAILER',
    LowSpeed: 'LOW_SPEED',
    Bike: 'BIKE'
} as const;

export type AdminEquipmentAllDetailResDTOVehicleTypeEnum = typeof AdminEquipmentAllDetailResDTOVehicleTypeEnum[keyof typeof AdminEquipmentAllDetailResDTOVehicleTypeEnum];
export const AdminEquipmentAllDetailResDTOVehicleBodyClassEnum = {
    Sedan: 'SEDAN',
    Hatchback: 'HATCHBACK',
    Coupe: 'COUPE',
    Convertible: 'CONVERTIBLE',
    Suv: 'SUV',
    Van: 'VAN',
    Minivan: 'MINIVAN',
    Wagon: 'WAGON',
    PickupTruck: 'PICKUP_TRUCK',
    Bus: 'BUS',
    Minibus: 'MINIBUS',
    HeavyTruck: 'HEAVY_TRUCK',
    Trike: 'TRIKE',
    OffRoad: 'OFF_ROAD',
    Trailer: 'TRAILER'
} as const;

export type AdminEquipmentAllDetailResDTOVehicleBodyClassEnum = typeof AdminEquipmentAllDetailResDTOVehicleBodyClassEnum[keyof typeof AdminEquipmentAllDetailResDTOVehicleBodyClassEnum];
export const AdminEquipmentAllDetailResDTOFuelTypeEnum = {
    Diesel: 'DIESEL',
    Gasoline: 'GASOLINE',
    Hybrid: 'HYBRID',
    Electric: 'ELECTRIC'
} as const;

export type AdminEquipmentAllDetailResDTOFuelTypeEnum = typeof AdminEquipmentAllDetailResDTOFuelTypeEnum[keyof typeof AdminEquipmentAllDetailResDTOFuelTypeEnum];


