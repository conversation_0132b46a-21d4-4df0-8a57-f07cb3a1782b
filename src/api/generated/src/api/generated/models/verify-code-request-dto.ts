/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 이메일 인증 코드를 확인하기 위한 DTO
 * @export
 * @interface VerifyCodeRequestDTO
 */
export interface VerifyCodeRequestDTO {
    /**
     * 사용자의 이메일 주소
     * @type {string}
     * @memberof VerifyCodeRequestDTO
     */
    'email': string;
    /**
     * 사용자가 입력한 인증 코드
     * @type {string}
     * @memberof VerifyCodeRequestDTO
     */
    'code': string;
    /**
     * 인증 프로세스 유형
     * @type {string}
     * @memberof VerifyCodeRequestDTO
     */
    'verificationType': VerifyCodeRequestDTOVerificationTypeEnum;
}

export const VerifyCodeRequestDTOVerificationTypeEnum = {
    Registration: 'REGISTRATION',
    ChangePassword: 'CHANGE_PASSWORD'
} as const;

export type VerifyCodeRequestDTOVerificationTypeEnum = typeof VerifyCodeRequestDTOVerificationTypeEnum[keyof typeof VerifyCodeRequestDTOVerificationTypeEnum];


