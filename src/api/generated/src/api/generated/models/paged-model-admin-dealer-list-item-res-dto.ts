/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AdminDealerListItemResDTO } from './admin-dealer-list-item-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { PageMetadata } from './page-metadata';

/**
 * 
 * @export
 * @interface PagedModelAdminDealerListItemResDTO
 */
export interface PagedModelAdminDealerListItemResDTO {
    /**
     * 
     * @type {Array<AdminDealerListItemResDTO>}
     * @memberof PagedModelAdminDealerListItemResDTO
     */
    'content'?: Array<AdminDealerListItemResDTO>;
    /**
     * 
     * @type {PageMetadata}
     * @memberof PagedModelAdminDealerListItemResDTO
     */
    'page'?: PageMetadata;
}

