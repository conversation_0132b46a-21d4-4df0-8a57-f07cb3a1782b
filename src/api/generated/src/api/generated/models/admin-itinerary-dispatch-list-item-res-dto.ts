/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AdminDriverBasicResDTO } from './admin-driver-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminEquipmentBasicResDTO } from './admin-equipment-basic-res-dto';

/**
 * 배차 정보
 * @export
 * @interface AdminItineraryDispatchListItemResDTO
 */
export interface AdminItineraryDispatchListItemResDTO {
    /**
     * 배차아이디
     * @type {number}
     * @memberof AdminItineraryDispatchListItemResDTO
     */
    'itineraryDispatchId'?: number;
    /**
     * 운행예정일시
     * @type {string}
     * @memberof AdminItineraryDispatchListItemResDTO
     */
    'scheduledDt'?: string;
    /**
     * 여정상태:<br> SCHEDULED: 운행대기<br> RUNNING: 운행중<br> COMPLETED: 운행완료<br> 
     * @type {string}
     * @memberof AdminItineraryDispatchListItemResDTO
     */
    'itineraryStatus'?: AdminItineraryDispatchListItemResDTOItineraryStatusEnum;
    /**
     * 일정명(운행명)
     * @type {string}
     * @memberof AdminItineraryDispatchListItemResDTO
     */
    'itineraryPlanName'?: string;
    /**
     * 총 방문지 수
     * @type {number}
     * @memberof AdminItineraryDispatchListItemResDTO
     */
    'totalItineraryRouteCount'?: number;
    /**
     * 운송완료 수
     * @type {number}
     * @memberof AdminItineraryDispatchListItemResDTO
     */
    'completedItineraryRouteCount'?: number;
    /**
     * 
     * @type {AdminDriverBasicResDTO}
     * @memberof AdminItineraryDispatchListItemResDTO
     */
    'driver'?: AdminDriverBasicResDTO;
    /**
     * 
     * @type {AdminEquipmentBasicResDTO}
     * @memberof AdminItineraryDispatchListItemResDTO
     */
    'equipment'?: AdminEquipmentBasicResDTO;
}

export const AdminItineraryDispatchListItemResDTOItineraryStatusEnum = {
    Scheduled: 'SCHEDULED',
    Running: 'RUNNING',
    Completed: 'COMPLETED'
} as const;

export type AdminItineraryDispatchListItemResDTOItineraryStatusEnum = typeof AdminItineraryDispatchListItemResDTOItineraryStatusEnum[keyof typeof AdminItineraryDispatchListItemResDTOItineraryStatusEnum];


