/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Coordinate } from './coordinate';
// May contain unused imports in some cases
// @ts-ignore
import type { CoordinateSequence } from './coordinate-sequence';
// May contain unused imports in some cases
// @ts-ignore
import type { Envelope } from './envelope';
// May contain unused imports in some cases
// @ts-ignore
import type { Geometry } from './geometry';
// May contain unused imports in some cases
// @ts-ignore
import type { GeometryFactory } from './geometry-factory';
// May contain unused imports in some cases
// @ts-ignore
import type { Point } from './point';
// May contain unused imports in some cases
// @ts-ignore
import type { PrecisionModel } from './precision-model';

/**
 * 여정의 총 주행경로 선형
 * @export
 * @interface LineString
 */
export interface LineString {
    /**
     * 
     * @type {Geometry}
     * @memberof LineString
     */
    'envelope'?: Geometry;
    /**
     * 
     * @type {GeometryFactory}
     * @memberof LineString
     */
    'factory'?: GeometryFactory;
    /**
     * 
     * @type {object}
     * @memberof LineString
     */
    'userData'?: object;
    /**
     * 
     * @type {number}
     * @memberof LineString
     */
    'boundaryDimension'?: number;
    /**
     * 
     * @type {Geometry}
     * @memberof LineString
     */
    'boundary'?: Geometry;
    /**
     * 
     * @type {CoordinateSequence}
     * @memberof LineString
     */
    'coordinateSequence'?: CoordinateSequence;
    /**
     * 
     * @type {Point}
     * @memberof LineString
     */
    'endPoint'?: Point;
    /**
     * 
     * @type {Point}
     * @memberof LineString
     */
    'startPoint'?: Point;
    /**
     * 
     * @type {string}
     * @memberof LineString
     */
    'geometryType'?: string;
    /**
     * 
     * @type {number}
     * @memberof LineString
     */
    'dimension'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof LineString
     */
    'ring'?: boolean;
    /**
     * 
     * @type {Coordinate}
     * @memberof LineString
     */
    'coordinate'?: Coordinate;
    /**
     * 
     * @type {Array<Coordinate>}
     * @memberof LineString
     */
    'coordinates'?: Array<Coordinate>;
    /**
     * 
     * @type {number}
     * @memberof LineString
     */
    'numPoints'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof LineString
     */
    'closed'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof LineString
     */
    'length'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof LineString
     */
    'empty'?: boolean;
    /**
     * 
     * @type {Envelope}
     * @memberof LineString
     */
    'envelopeInternal'?: Envelope;
    /**
     * 
     * @type {number}
     * @memberof LineString
     */
    'srid'?: number;
    /**
     * 
     * @type {PrecisionModel}
     * @memberof LineString
     */
    'precisionModel'?: PrecisionModel;
    /**
     * 
     * @type {boolean}
     * @memberof LineString
     */
    'rectangle'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof LineString
     */
    'area'?: number;
    /**
     * 
     * @type {Point}
     * @memberof LineString
     */
    'centroid'?: Point;
    /**
     * 
     * @type {Point}
     * @memberof LineString
     */
    'interiorPoint'?: Point;
    /**
     * 
     * @type {number}
     * @memberof LineString
     */
    'numGeometries'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof LineString
     */
    'simple'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof LineString
     */
    'valid'?: boolean;
}

