/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ItineraryRouteResDTO } from './itinerary-route-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { LineString } from './line-string';

/**
 * 여정상세 조회응답
 * @export
 * @interface ItineraryDetailResDTO
 */
export interface ItineraryDetailResDTO {
    /**
     * 여정 아이디
     * @type {number}
     * @memberof ItineraryDetailResDTO
     */
    'itineraryId'?: number;
    /**
     * 여정 이름
     * @type {string}
     * @memberof ItineraryDetailResDTO
     */
    'itineraryName'?: string;
    /**
     * 차량 이름
     * @type {string}
     * @memberof ItineraryDetailResDTO
     */
    'vehicleName'?: string;
    /**
     * 여정의 총 주행거리 (m)
     * @type {number}
     * @memberof ItineraryDetailResDTO
     */
    'drivedDistance'?: number;
    /**
     * 여정의 총 주행시간 (sec)
     * @type {number}
     * @memberof ItineraryDetailResDTO
     */
    'drivedTime'?: number;
    /**
     * 
     * @type {LineString}
     * @memberof ItineraryDetailResDTO
     */
    'drivedGeometry'?: LineString;
    /**
     * 여정경로 목록
     * @type {Array<ItineraryRouteResDTO>}
     * @memberof ItineraryDetailResDTO
     */
    'routes'?: Array<ItineraryRouteResDTO>;
}

