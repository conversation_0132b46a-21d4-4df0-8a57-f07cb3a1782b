/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { SwaggerPointMixin } from './swagger-point-mixin';

/**
 * 여정계획목적지 생성요청
 * @export
 * @interface ItineraryPlanDestinationCreateReqDTO
 */
export interface ItineraryPlanDestinationCreateReqDTO {
    /**
     * 해당 주행 구간의 목적지 Title
     * @type {string}
     * @memberof ItineraryPlanDestinationCreateReqDTO
     */
    'destinationName'?: string;
    /**
     * 해당 주행 구간의 목적지 주소
     * @type {string}
     * @memberof ItineraryPlanDestinationCreateReqDTO
     */
    'destinationAddress': string;
    /**
     * 
     * @type {SwaggerPointMixin}
     * @memberof ItineraryPlanDestinationCreateReqDTO
     */
    'destinationLocation': SwaggerPointMixin;
    /**
     * 해당 주행 구간의 예상 경로 거리 (m)
     * @type {number}
     * @memberof ItineraryPlanDestinationCreateReqDTO
     */
    'estimatedDistance'?: number;
    /**
     * 해당 주행 구간의 예상 소요 시간 (sec)
     * @type {number}
     * @memberof ItineraryPlanDestinationCreateReqDTO
     */
    'estimatedTime'?: number;
    /**
     * 
     * @type {Array<SwaggerPointMixin>}
     * @memberof ItineraryPlanDestinationCreateReqDTO
     */
    'estimatedGeometry'?: Array<SwaggerPointMixin>;
}

