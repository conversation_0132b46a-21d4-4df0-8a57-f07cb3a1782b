/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 장비 수정요청
 * @export
 * @interface EquipmentUpdateReqDTO
 */
export interface EquipmentUpdateReqDTO {
    /**
     * 장비명
     * @type {string}
     * @memberof EquipmentUpdateReqDTO
     */
    'equipmentName': string;
    /**
     * 장비타입:<br> VEHICLE: 차량<br> TRUCK: 트럭<br> HEAVY_EQUIPMENT: 중장비<br> AGRICULTURAL: 농기계<br> DRONE: 드론<br> ROBOT: 로봇<br> 
     * @type {string}
     * @memberof EquipmentUpdateReqDTO
     */
    'equipmentType'?: EquipmentUpdateReqDTOEquipmentTypeEnum;
    /**
     * 차량타입:<br> CAR: 일반 승용차<br> SUV_RV: 다목적 승용차(SUV, RV 등)<br> TRUCK: 트럭<br> BUS: 버스<br> TRAILER: 트레일러<br> LOW_SPEED: 저속 차량<br> BIKE: 이륜차(오토바이 등)<br> 
     * @type {string}
     * @memberof EquipmentUpdateReqDTO
     */
    'vehicleType': EquipmentUpdateReqDTOVehicleTypeEnum;
    /**
     * 제조사
     * @type {string}
     * @memberof EquipmentUpdateReqDTO
     */
    'manufacturer': string;
    /**
     * 모델명
     * @type {string}
     * @memberof EquipmentUpdateReqDTO
     */
    'modelName': string;
    /**
     * 트림명
     * @type {string}
     * @memberof EquipmentUpdateReqDTO
     */
    'trimName': string;
    /**
     * 생산년도
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'productYear': number;
    /**
     * 이미지경로
     * @type {string}
     * @memberof EquipmentUpdateReqDTO
     */
    'imagePath'?: string;
    /**
     * VIN No
     * @type {string}
     * @memberof EquipmentUpdateReqDTO
     */
    'serialNo': string;
    /**
     * 차량번호
     * @type {string}
     * @memberof EquipmentUpdateReqDTO
     */
    'plateNo': string;
    /**
     * 차체형식:<br> SEDAN: 세단<br> HATCHBACK: 해치백<br> COUPE: 쿠페<br> CONVERTIBLE: 컨버터블<br> SUV: SUV<br> VAN: 밴<br> MINIVAN: 미니밴<br> WAGON: 웨건<br> PICKUP_TRUCK: 픽업트럭<br> BUS: 버스<br> MINIBUS: 소형버스<br> HEAVY_TRUCK: 대형화물차<br> TRIKE: 삼륜오토바이<br> OFF_ROAD: 오프로드 차량<br> TRAILER: 트레일러<br> 
     * @type {string}
     * @memberof EquipmentUpdateReqDTO
     */
    'vehicleBodyClass'?: EquipmentUpdateReqDTOVehicleBodyClassEnum;
    /**
     * 전장: 장비 길이(mm)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'bodyLength': number;
    /**
     * 전고: 장비 높이(mm)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'bodyHeight': number;
    /**
     * 전폭: 장비 너비(mm)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'bodyWidth': number;
    /**
     * 위험물질: Explosives
     * @type {boolean}
     * @memberof EquipmentUpdateReqDTO
     */
    'hazmatExplosives'?: boolean;
    /**
     * 위험물질: Gas
     * @type {boolean}
     * @memberof EquipmentUpdateReqDTO
     */
    'hazmatGas'?: boolean;
    /**
     * 위험물질: Flammable
     * @type {boolean}
     * @memberof EquipmentUpdateReqDTO
     */
    'hazmatFlammable'?: boolean;
    /**
     * 위험물질: Organic
     * @type {boolean}
     * @memberof EquipmentUpdateReqDTO
     */
    'hazmatOrganic'?: boolean;
    /**
     * 위험물질: Poison
     * @type {boolean}
     * @memberof EquipmentUpdateReqDTO
     */
    'hazmatPoison'?: boolean;
    /**
     * 위험물질: Radioactive
     * @type {boolean}
     * @memberof EquipmentUpdateReqDTO
     */
    'hazmatRadioactive'?: boolean;
    /**
     * 위험물질: Corrosive
     * @type {boolean}
     * @memberof EquipmentUpdateReqDTO
     */
    'hazmatCorrosive'?: boolean;
    /**
     * 위험물질: Harmful For Water
     * @type {boolean}
     * @memberof EquipmentUpdateReqDTO
     */
    'hazmatHarmfulForWater'?: boolean;
    /**
     * 위험물질: Poisonous Inhalation Hazard
     * @type {boolean}
     * @memberof EquipmentUpdateReqDTO
     */
    'hazmatPoisonousInhalationHazard'?: boolean;
    /**
     * 위험물질: Other
     * @type {boolean}
     * @memberof EquipmentUpdateReqDTO
     */
    'hazmatOther'?: boolean;
    /**
     * 연료타입:<br> DIESEL: 경유<br> GASOLINE: 휘발유<br> HYBRID: 하이브리드<br> ELECTRIC: 전기<br> 
     * @type {string}
     * @memberof EquipmentUpdateReqDTO
     */
    'fuelType': EquipmentUpdateReqDTOFuelTypeEnum;
    /**
     * 연비(km/L or km/kWh)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'fuelEfficiency'?: number;
    /**
     * 연료탱크용량(L or kWh)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'fuelTankCapacity'?: number;
    /**
     * 타이어 지름(inch)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'tireDiameter'?: number;
    /**
     * 타이어 폭(mm)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'tireWidth'?: number;
    /**
     * 엔진오일 교체주기(km)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'engineOilInterval'?: number;
    /**
     * 오일필터 교체주기(km)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'oilFilterInterval'?: number;
    /**
     * 연료필터 교체주기(km)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'fuelFilterInterval'?: number;
    /**
     * 에어필터 교체주기(km)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'airFilterInterval'?: number;
    /**
     * 브레이크패드 교체주기(km)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'brakePadInterval'?: number;
    /**
     * 브레이크 라이닝 교체주기(km)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'brakeLiningInterval'?: number;
    /**
     * 타이어 교체주기(km)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'tireInterval'?: number;
    /**
     * 냉각수 교체주기(년)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'coolantInterval'?: number;
    /**
     * 배터리 교체주기(년)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'batteryInterval'?: number;
    /**
     * 트랜스미션오일 교체주기(km)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'transmissionOilInterval'?: number;
    /**
     * 타이어위치 교환주기(km)
     * @type {number}
     * @memberof EquipmentUpdateReqDTO
     */
    'tireRotationInterval'?: number;
}

export const EquipmentUpdateReqDTOEquipmentTypeEnum = {
    Vehicle: 'VEHICLE',
    Truck: 'TRUCK',
    HeavyEquipment: 'HEAVY_EQUIPMENT',
    Agricultural: 'AGRICULTURAL',
    Drone: 'DRONE',
    Robot: 'ROBOT'
} as const;

export type EquipmentUpdateReqDTOEquipmentTypeEnum = typeof EquipmentUpdateReqDTOEquipmentTypeEnum[keyof typeof EquipmentUpdateReqDTOEquipmentTypeEnum];
export const EquipmentUpdateReqDTOVehicleTypeEnum = {
    Car: 'CAR',
    SuvRv: 'SUV_RV',
    Truck: 'TRUCK',
    Bus: 'BUS',
    Trailer: 'TRAILER',
    LowSpeed: 'LOW_SPEED',
    Bike: 'BIKE'
} as const;

export type EquipmentUpdateReqDTOVehicleTypeEnum = typeof EquipmentUpdateReqDTOVehicleTypeEnum[keyof typeof EquipmentUpdateReqDTOVehicleTypeEnum];
export const EquipmentUpdateReqDTOVehicleBodyClassEnum = {
    Sedan: 'SEDAN',
    Hatchback: 'HATCHBACK',
    Coupe: 'COUPE',
    Convertible: 'CONVERTIBLE',
    Suv: 'SUV',
    Van: 'VAN',
    Minivan: 'MINIVAN',
    Wagon: 'WAGON',
    PickupTruck: 'PICKUP_TRUCK',
    Bus: 'BUS',
    Minibus: 'MINIBUS',
    HeavyTruck: 'HEAVY_TRUCK',
    Trike: 'TRIKE',
    OffRoad: 'OFF_ROAD',
    Trailer: 'TRAILER'
} as const;

export type EquipmentUpdateReqDTOVehicleBodyClassEnum = typeof EquipmentUpdateReqDTOVehicleBodyClassEnum[keyof typeof EquipmentUpdateReqDTOVehicleBodyClassEnum];
export const EquipmentUpdateReqDTOFuelTypeEnum = {
    Diesel: 'DIESEL',
    Gasoline: 'GASOLINE',
    Hybrid: 'HYBRID',
    Electric: 'ELECTRIC'
} as const;

export type EquipmentUpdateReqDTOFuelTypeEnum = typeof EquipmentUpdateReqDTOFuelTypeEnum[keyof typeof EquipmentUpdateReqDTOFuelTypeEnum];


