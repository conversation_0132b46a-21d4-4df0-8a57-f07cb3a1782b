/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface SubscriptionRequestDTO
 */
export interface SubscriptionRequestDTO {
    /**
     * 구독 이름
     * @type {string}
     * @memberof SubscriptionRequestDTO
     */
    'subscriptionName': string;
    /**
     * 구독 가격
     * @type {string}
     * @memberof SubscriptionRequestDTO
     */
    'price': string;
    /**
     * 자동 갱신 여부
     * @type {boolean}
     * @memberof SubscriptionRequestDTO
     */
    'autoRenewal': boolean;
    /**
     * 구독 유형
     * @type {string}
     * @memberof SubscriptionRequestDTO
     */
    'subscriptionType': SubscriptionRequestDTOSubscriptionTypeEnum;
    /**
     * 구독 생성시 초기 상태 설정. 대기중(PENDING), 활성(ACTIVE), 미확인(UNCONFIRMED) 등 생성과 동시에 초기값을 설정합니다
     * @type {string}
     * @memberof SubscriptionRequestDTO
     */
    'paymentStatus': SubscriptionRequestDTOPaymentStatusEnum;
    /**
     * 구독 생성시 초기 만료일 설정. 구독 생성시 초기 만료일을 설정합니다. 예를 들어 1년 구독이면 1년 후의 날짜를 설정합니다.
     * @type {string}
     * @memberof SubscriptionRequestDTO
     */
    'expirationDate': string;
}

export const SubscriptionRequestDTOSubscriptionTypeEnum = {
    Yearly: 'YEARLY',
    Monthly: 'MONTHLY'
} as const;

export type SubscriptionRequestDTOSubscriptionTypeEnum = typeof SubscriptionRequestDTOSubscriptionTypeEnum[keyof typeof SubscriptionRequestDTOSubscriptionTypeEnum];
export const SubscriptionRequestDTOPaymentStatusEnum = {
    Pending: 'PENDING',
    Unconfirmed: 'UNCONFIRMED',
    Cancelled: 'CANCELLED',
    Refunded: 'REFUNDED',
    Active: 'ACTIVE',
    Exited: 'EXITED'
} as const;

export type SubscriptionRequestDTOPaymentStatusEnum = typeof SubscriptionRequestDTOPaymentStatusEnum[keyof typeof SubscriptionRequestDTOPaymentStatusEnum];


