/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { EquipmentListItemResDTO } from './equipment-list-item-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { PageMetadata } from './page-metadata';

/**
 * 
 * @export
 * @interface PagedModelEquipmentListItemResDTO
 */
export interface PagedModelEquipmentListItemResDTO {
    /**
     * 
     * @type {Array<EquipmentListItemResDTO>}
     * @memberof PagedModelEquipmentListItemResDTO
     */
    'content'?: Array<EquipmentListItemResDTO>;
    /**
     * 
     * @type {PageMetadata}
     * @memberof PagedModelEquipmentListItemResDTO
     */
    'page'?: PageMetadata;
}

