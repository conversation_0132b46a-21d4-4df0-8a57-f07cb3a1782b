/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AdminDriverBasicResDTO } from './admin-driver-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminEquipmentBasicResDTO } from './admin-equipment-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { ItineraryPlanDetailResDTO } from './itinerary-plan-detail-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { ItineraryTrackingResDTO } from './itinerary-tracking-res-dto';

/**
 * 배차 정보
 * @export
 * @interface AdminItineraryDispatchDetailResDTO
 */
export interface AdminItineraryDispatchDetailResDTO {
    /**
     * 배차아이디
     * @type {number}
     * @memberof AdminItineraryDispatchDetailResDTO
     */
    'itineraryDispatchId'?: number;
    /**
     * 운행예정일시
     * @type {string}
     * @memberof AdminItineraryDispatchDetailResDTO
     */
    'scheduledDt'?: string;
    /**
     * 여정상태:<br> SCHEDULED: 운행대기<br> RUNNING: 운행중<br> COMPLETED: 운행완료<br> 
     * @type {string}
     * @memberof AdminItineraryDispatchDetailResDTO
     */
    'itineraryStatus'?: AdminItineraryDispatchDetailResDTOItineraryStatusEnum;
    /**
     * 
     * @type {AdminDriverBasicResDTO}
     * @memberof AdminItineraryDispatchDetailResDTO
     */
    'driver'?: AdminDriverBasicResDTO;
    /**
     * 
     * @type {AdminEquipmentBasicResDTO}
     * @memberof AdminItineraryDispatchDetailResDTO
     */
    'equipment'?: AdminEquipmentBasicResDTO;
    /**
     * 
     * @type {ItineraryPlanDetailResDTO}
     * @memberof AdminItineraryDispatchDetailResDTO
     */
    'itineraryPlan'?: ItineraryPlanDetailResDTO;
    /**
     * 
     * @type {ItineraryTrackingResDTO}
     * @memberof AdminItineraryDispatchDetailResDTO
     */
    'itineraryTracking'?: ItineraryTrackingResDTO;
}

export const AdminItineraryDispatchDetailResDTOItineraryStatusEnum = {
    Scheduled: 'SCHEDULED',
    Running: 'RUNNING',
    Completed: 'COMPLETED'
} as const;

export type AdminItineraryDispatchDetailResDTOItineraryStatusEnum = typeof AdminItineraryDispatchDetailResDTOItineraryStatusEnum[keyof typeof AdminItineraryDispatchDetailResDTOItineraryStatusEnum];


