/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { SwaggerPointMixin } from './swagger-point-mixin';

/**
 * 여정목적지 정보
 * @export
 * @interface ItineraryDestinationCreateReqDTO
 */
export interface ItineraryDestinationCreateReqDTO {
    /**
     * 여정경로의 목적지 Title
     * @type {string}
     * @memberof ItineraryDestinationCreateReqDTO
     */
    'destinationName'?: string;
    /**
     * 여정경로의 목적지 주소
     * @type {string}
     * @memberof ItineraryDestinationCreateReqDTO
     */
    'destinationAddress': string;
    /**
     * 
     * @type {SwaggerPointMixin}
     * @memberof ItineraryDestinationCreateReqDTO
     */
    'destinationLocation': SwaggerPointMixin;
    /**
     * 여정경로의 예상 경로 거리 (m)
     * @type {number}
     * @memberof ItineraryDestinationCreateReqDTO
     */
    'estimatedDistance'?: number;
    /**
     * 여정경로의 예상 소요 시간 (sec)
     * @type {number}
     * @memberof ItineraryDestinationCreateReqDTO
     */
    'estimatedTime'?: number;
    /**
     * 
     * @type {Array<SwaggerPointMixin>}
     * @memberof ItineraryDestinationCreateReqDTO
     */
    'routeGeometry'?: Array<SwaggerPointMixin>;
}

