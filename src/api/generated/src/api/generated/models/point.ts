/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Coordinate } from './coordinate';
// May contain unused imports in some cases
// @ts-ignore
import type { CoordinateSequence } from './coordinate-sequence';
// May contain unused imports in some cases
// @ts-ignore
import type { Envelope } from './envelope';
// May contain unused imports in some cases
// @ts-ignore
import type { Geometry } from './geometry';
// May contain unused imports in some cases
// @ts-ignore
import type { GeometryFactory } from './geometry-factory';
// May contain unused imports in some cases
// @ts-ignore
import type { PrecisionModel } from './precision-model';

/**
 * 
 * @export
 * @interface Point
 */
export interface Point {
    /**
     * 
     * @type {Geometry}
     * @memberof Point
     */
    'envelope'?: Geometry;
    /**
     * 
     * @type {GeometryFactory}
     * @memberof Point
     */
    'factory'?: GeometryFactory;
    /**
     * 
     * @type {object}
     * @memberof Point
     */
    'userData'?: object;
    /**
     * 
     * @type {Array<Coordinate>}
     * @memberof Point
     */
    'coordinates'?: Array<Coordinate>;
    /**
     * 
     * @type {number}
     * @memberof Point
     */
    'boundaryDimension'?: number;
    /**
     * 
     * @type {Geometry}
     * @memberof Point
     */
    'boundary'?: Geometry;
    /**
     * 
     * @type {CoordinateSequence}
     * @memberof Point
     */
    'coordinateSequence'?: CoordinateSequence;
    /**
     * 
     * @type {string}
     * @memberof Point
     */
    'geometryType'?: string;
    /**
     * 
     * @type {number}
     * @memberof Point
     */
    'dimension'?: number;
    /**
     * 
     * @type {Coordinate}
     * @memberof Point
     */
    'coordinate'?: Coordinate;
    /**
     * 
     * @type {number}
     * @memberof Point
     */
    'numPoints'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof Point
     */
    'simple'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof Point
     */
    'empty'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Point
     */
    'y'?: number;
    /**
     * 
     * @type {number}
     * @memberof Point
     */
    'x'?: number;
    /**
     * 
     * @type {Envelope}
     * @memberof Point
     */
    'envelopeInternal'?: Envelope;
    /**
     * 
     * @type {number}
     * @memberof Point
     */
    'srid'?: number;
    /**
     * 
     * @type {PrecisionModel}
     * @memberof Point
     */
    'precisionModel'?: PrecisionModel;
    /**
     * 
     * @type {boolean}
     * @memberof Point
     */
    'rectangle'?: boolean;
    /**
     * 
     * @type {number}
     * @memberof Point
     */
    'area'?: number;
    /**
     * 
     * @type {Point}
     * @memberof Point
     */
    'centroid'?: Point;
    /**
     * 
     * @type {Point}
     * @memberof Point
     */
    'interiorPoint'?: Point;
    /**
     * 
     * @type {number}
     * @memberof Point
     */
    'numGeometries'?: number;
    /**
     * 
     * @type {number}
     * @memberof Point
     */
    'length'?: number;
    /**
     * 
     * @type {boolean}
     * @memberof Point
     */
    'valid'?: boolean;
}

