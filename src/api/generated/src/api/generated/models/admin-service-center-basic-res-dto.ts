/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 서비스센터 정보
 * @export
 * @interface AdminServiceCenterBasicResDTO
 */
export interface AdminServiceCenterBasicResDTO {
    /**
     * 서비스센터아이디
     * @type {number}
     * @memberof AdminServiceCenterBasicResDTO
     */
    'serviceCenterId'?: number;
    /**
     * 국가아이디
     * @type {number}
     * @memberof AdminServiceCenterBasicResDTO
     */
    'countryId'?: number;
    /**
     * 서비스센터명
     * @type {string}
     * @memberof AdminServiceCenterBasicResDTO
     */
    'serviceCenterName'?: string;
    /**
     * 서비스센터전화번호국가코드
     * @type {string}
     * @memberof AdminServiceCenterBasicResDTO
     */
    'serviceCenterCountryDialCode'?: string;
    /**
     * 서비스센터전화번호
     * @type {string}
     * @memberof AdminServiceCenterBasicResDTO
     */
    'serviceCenterPhone'?: string;
    /**
     * 서비스센터주소
     * @type {string}
     * @memberof AdminServiceCenterBasicResDTO
     */
    'serviceCenterAddress'?: string;
}

