/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 인증 요청 DTO
 * @export
 * @interface AuthRequestDTO
 */
export interface AuthRequestDTO {
    /**
     * 계정타입
     * @type {string}
     * @memberof AuthRequestDTO
     */
    'accountType'?: AuthRequestDTOAccountTypeEnum;
    /**
     * 사용자의 로그인아이디.
     * @type {string}
     * @memberof AuthRequestDTO
     */
    'loginId'?: string;
    /**
     * 사용자의 패스워드 .
     * @type {string}
     * @memberof AuthRequestDTO
     */
    'password'?: string;
}

export const AuthRequestDTOAccountTypeEnum = {
    User: 'USER',
    Driver: 'DRIVER'
} as const;

export type AuthRequestDTOAccountTypeEnum = typeof AuthRequestDTOAccountTypeEnum[keyof typeof AuthRequestDTOAccountTypeEnum];


