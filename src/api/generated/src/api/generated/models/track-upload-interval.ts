/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 위치정보 업로드 시간 간격
 * @export
 * @interface TrackUploadInterval
 */
export interface TrackUploadInterval {
    /**
     * 5km/h이하 속도일 때 업로드 시간 간격 (ms)
     * @type {number}
     * @memberof TrackUploadInterval
     */
    'speedBelow5Kph': number;
    /**
     * 5~40km/h 속도일 때 업로드 시간 간격 (ms)
     * @type {number}
     * @memberof TrackUploadInterval
     */
    'speed5To40Kph': number;
    /**
     * 40~80km/h 속도일 때 업로드 시간 간격 (ms)
     * @type {number}
     * @memberof TrackUploadInterval
     */
    'speed40To80Kph': number;
    /**
     * 80km/h이상 속도일 때 업로드 시간 간격 (ms)
     * @type {number}
     * @memberof TrackUploadInterval
     */
    'speedAbove80Kph': number;
}

