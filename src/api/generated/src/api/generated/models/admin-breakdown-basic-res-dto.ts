/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 장비 기본 정보
 * @export
 * @interface AdminBreakdownBasicResDTO
 */
export interface AdminBreakdownBasicResDTO {
    /**
     * 고장아이디
     * @type {number}
     * @memberof AdminBreakdownBasicResDTO
     */
    'breakdownId'?: number;
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminBreakdownBasicResDTO
     */
    'equipmentId'?: number;
    /**
     * 마일리지
     * @type {number}
     * @memberof AdminBreakdownBasicResDTO
     */
    'mileage'?: number;
    /**
     * 고장알람타입:<br> A: 모바일 통신감도가 낮은상태 (기준이하, -100dBm)<br> B: 경계 이탈<br> F: 장비 고장<br> G: GPS 신호 유실<br> I: SOS 활성<br> L: 통신 두절<br> O: SOS 비활성<br> S: 시스템 에러 알람<br> W: 경고<br> 
     * @type {string}
     * @memberof AdminBreakdownBasicResDTO
     */
    'alarmType'?: AdminBreakdownBasicResDTOAlarmTypeEnum;
    /**
     * 고장코드
     * @type {string}
     * @memberof AdminBreakdownBasicResDTO
     */
    'breakdownCode'?: string;
    /**
     * 고장심각도:<br> HIGH: 높음<br> MEDIUM: 보통<br> LOW: 낮음<br> 
     * @type {string}
     * @memberof AdminBreakdownBasicResDTO
     */
    'severity'?: AdminBreakdownBasicResDTOSeverityEnum;
    /**
     * 고장증상
     * @type {string}
     * @memberof AdminBreakdownBasicResDTO
     */
    'symptom'?: string;
    /**
     * 고장사진경로
     * @type {string}
     * @memberof AdminBreakdownBasicResDTO
     */
    'photoPath'?: string;
    /**
     * 고장정비상태:<br> SUBMITTED: 정비 접수<br> REPAIRING: 정비 중<br> DONE: 정비 완료<br> 
     * @type {string}
     * @memberof AdminBreakdownBasicResDTO
     */
    'breakdownRepairStatus'?: AdminBreakdownBasicResDTOBreakdownRepairStatusEnum;
    /**
     * TSG아이디
     * @type {number}
     * @memberof AdminBreakdownBasicResDTO
     */
    'tsgId'?: number;
    /**
     * 고장일시
     * @type {string}
     * @memberof AdminBreakdownBasicResDTO
     */
    'occurredAt'?: string;
    /**
     * 접수일시
     * @type {string}
     * @memberof AdminBreakdownBasicResDTO
     */
    'submittedAt'?: string;
    /**
     * 수리일시
     * @type {string}
     * @memberof AdminBreakdownBasicResDTO
     */
    'repairedAt'?: string;
}

export const AdminBreakdownBasicResDTOAlarmTypeEnum = {
    A: 'A',
    B: 'B',
    F: 'F',
    G: 'G',
    I: 'I',
    L: 'L',
    O: 'O',
    S: 'S',
    W: 'W'
} as const;

export type AdminBreakdownBasicResDTOAlarmTypeEnum = typeof AdminBreakdownBasicResDTOAlarmTypeEnum[keyof typeof AdminBreakdownBasicResDTOAlarmTypeEnum];
export const AdminBreakdownBasicResDTOSeverityEnum = {
    High: 'HIGH',
    Medium: 'MEDIUM',
    Low: 'LOW'
} as const;

export type AdminBreakdownBasicResDTOSeverityEnum = typeof AdminBreakdownBasicResDTOSeverityEnum[keyof typeof AdminBreakdownBasicResDTOSeverityEnum];
export const AdminBreakdownBasicResDTOBreakdownRepairStatusEnum = {
    Submitted: 'SUBMITTED',
    Repairing: 'REPAIRING',
    Done: 'DONE'
} as const;

export type AdminBreakdownBasicResDTOBreakdownRepairStatusEnum = typeof AdminBreakdownBasicResDTOBreakdownRepairStatusEnum[keyof typeof AdminBreakdownBasicResDTOBreakdownRepairStatusEnum];


