/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AdminCountryBasicResDTO } from './admin-country-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminDealerBasicResDTO } from './admin-dealer-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminEquipmentStatusResDTO } from './admin-equipment-status-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminFleetBasicResDTO } from './admin-fleet-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminServiceCenterBasicResDTO } from './admin-service-center-basic-res-dto';

/**
 * 장비 기본 정보
 * @export
 * @interface AdminEquipmentListItemResDTO
 */
export interface AdminEquipmentListItemResDTO {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentListItemResDTO
     */
    'equipmentId'?: number;
    /**
     * 장비타입:<br> VEHICLE: 차량<br> TRUCK: 트럭<br> HEAVY_EQUIPMENT: 중장비<br> AGRICULTURAL: 농기계<br> DRONE: 드론<br> ROBOT: 로봇<br> 
     * @type {string}
     * @memberof AdminEquipmentListItemResDTO
     */
    'equipmentType'?: AdminEquipmentListItemResDTOEquipmentTypeEnum;
    /**
     * 차량타입:<br> CAR: 일반 승용차<br> SUV_RV: 다목적 승용차(SUV, RV 등)<br> TRUCK: 트럭<br> BUS: 버스<br> TRAILER: 트레일러<br> LOW_SPEED: 저속 차량<br> BIKE: 이륜차(오토바이 등)<br> 
     * @type {string}
     * @memberof AdminEquipmentListItemResDTO
     */
    'vehicleType'?: AdminEquipmentListItemResDTOVehicleTypeEnum;
    /**
     * 제조사
     * @type {string}
     * @memberof AdminEquipmentListItemResDTO
     */
    'manufacturer'?: string;
    /**
     * 모델명
     * @type {string}
     * @memberof AdminEquipmentListItemResDTO
     */
    'modelName'?: string;
    /**
     * 트림명
     * @type {string}
     * @memberof AdminEquipmentListItemResDTO
     */
    'trimName'?: string;
    /**
     * 생산년도
     * @type {number}
     * @memberof AdminEquipmentListItemResDTO
     */
    'productYear'?: number;
    /**
     * 이미지경로
     * @type {string}
     * @memberof AdminEquipmentListItemResDTO
     */
    'imagePath'?: string;
    /**
     * VIN No
     * @type {string}
     * @memberof AdminEquipmentListItemResDTO
     */
    'serialNo'?: string;
    /**
     * 차량번호
     * @type {string}
     * @memberof AdminEquipmentListItemResDTO
     */
    'plateNo'?: string;
    /**
     * 운전자수
     * @type {number}
     * @memberof AdminEquipmentListItemResDTO
     */
    'driverCount'?: number;
    /**
     * 
     * @type {AdminEquipmentStatusResDTO}
     * @memberof AdminEquipmentListItemResDTO
     */
    'status'?: AdminEquipmentStatusResDTO;
    /**
     * 
     * @type {AdminCountryBasicResDTO}
     * @memberof AdminEquipmentListItemResDTO
     */
    'country'?: AdminCountryBasicResDTO;
    /**
     * 
     * @type {AdminDealerBasicResDTO}
     * @memberof AdminEquipmentListItemResDTO
     */
    'dealer'?: AdminDealerBasicResDTO;
    /**
     * 
     * @type {AdminServiceCenterBasicResDTO}
     * @memberof AdminEquipmentListItemResDTO
     */
    'serviceCenter'?: AdminServiceCenterBasicResDTO;
    /**
     * 플릿정보
     * @type {Array<AdminFleetBasicResDTO>}
     * @memberof AdminEquipmentListItemResDTO
     */
    'fleets'?: Array<AdminFleetBasicResDTO>;
}

export const AdminEquipmentListItemResDTOEquipmentTypeEnum = {
    Vehicle: 'VEHICLE',
    Truck: 'TRUCK',
    HeavyEquipment: 'HEAVY_EQUIPMENT',
    Agricultural: 'AGRICULTURAL',
    Drone: 'DRONE',
    Robot: 'ROBOT'
} as const;

export type AdminEquipmentListItemResDTOEquipmentTypeEnum = typeof AdminEquipmentListItemResDTOEquipmentTypeEnum[keyof typeof AdminEquipmentListItemResDTOEquipmentTypeEnum];
export const AdminEquipmentListItemResDTOVehicleTypeEnum = {
    Car: 'CAR',
    SuvRv: 'SUV_RV',
    Truck: 'TRUCK',
    Bus: 'BUS',
    Trailer: 'TRAILER',
    LowSpeed: 'LOW_SPEED',
    Bike: 'BIKE'
} as const;

export type AdminEquipmentListItemResDTOVehicleTypeEnum = typeof AdminEquipmentListItemResDTOVehicleTypeEnum[keyof typeof AdminEquipmentListItemResDTOVehicleTypeEnum];


