/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 운전자 정보
 * @export
 * @interface AdminDriverBasicResDTO
 */
export interface AdminDriverBasicResDTO {
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverBasicResDTO
     */
    'driverId'?: number;
    /**
     * 운전자로그인 아이디(이메일)
     * @type {string}
     * @memberof AdminDriverBasicResDTO
     */
    'loginId'?: string;
    /**
     * 운전자명
     * @type {string}
     * @memberof AdminDriverBasicResDTO
     */
    'driverName'?: string;
    /**
     * 운전자전화번호국가코드
     * @type {string}
     * @memberof AdminDriverBasicResDTO
     */
    'driverCountryDialCode'?: string;
    /**
     * 운전자전화번호
     * @type {string}
     * @memberof AdminDriverBasicResDTO
     */
    'driverPhone'?: string;
    /**
     * 운전자상태
     * @type {string}
     * @memberof AdminDriverBasicResDTO
     */
    'driverStatus'?: AdminDriverBasicResDTODriverStatusEnum;
}

export const AdminDriverBasicResDTODriverStatusEnum = {
    OnDuty: 'ON_DUTY',
    Idle: 'IDLE'
} as const;

export type AdminDriverBasicResDTODriverStatusEnum = typeof AdminDriverBasicResDTODriverStatusEnum[keyof typeof AdminDriverBasicResDTODriverStatusEnum];


