/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface PaymentResponseDTO
 */
export interface PaymentResponseDTO {
    /**
     * 결제 ID
     * @type {number}
     * @memberof PaymentResponseDTO
     */
    'paymentId'?: number;
    /**
     * 사용자 ID
     * @type {number}
     * @memberof PaymentResponseDTO
     */
    'userId'?: number;
    /**
     * 구독 이름
     * @type {string}
     * @memberof PaymentResponseDTO
     */
    'subscriptionName'?: string;
    /**
     * 가격
     * @type {string}
     * @memberof PaymentResponseDTO
     */
    'price'?: string;
    /**
     * 결제 상태
     * @type {string}
     * @memberof PaymentResponseDTO
     */
    'paymentStatus'?: PaymentResponseDTOPaymentStatusEnum;
    /**
     * 자동 갱신 여부
     * @type {boolean}
     * @memberof PaymentResponseDTO
     */
    'autoRenewal'?: boolean;
    /**
     * 구독 유형
     * @type {string}
     * @memberof PaymentResponseDTO
     */
    'subscriptionType'?: PaymentResponseDTOSubscriptionTypeEnum;
    /**
     * 결제 날짜
     * @type {string}
     * @memberof PaymentResponseDTO
     */
    'paidDate'?: string;
    /**
     * 만료 날짜
     * @type {string}
     * @memberof PaymentResponseDTO
     */
    'expirationDate'?: string;
    /**
     * 취소 날짜
     * @type {string}
     * @memberof PaymentResponseDTO
     */
    'canceledDate'?: string;
}

export const PaymentResponseDTOPaymentStatusEnum = {
    Pending: 'PENDING',
    Unconfirmed: 'UNCONFIRMED',
    Cancelled: 'CANCELLED',
    Refunded: 'REFUNDED',
    Active: 'ACTIVE',
    Exited: 'EXITED'
} as const;

export type PaymentResponseDTOPaymentStatusEnum = typeof PaymentResponseDTOPaymentStatusEnum[keyof typeof PaymentResponseDTOPaymentStatusEnum];
export const PaymentResponseDTOSubscriptionTypeEnum = {
    Yearly: 'YEARLY',
    Monthly: 'MONTHLY'
} as const;

export type PaymentResponseDTOSubscriptionTypeEnum = typeof PaymentResponseDTOSubscriptionTypeEnum[keyof typeof PaymentResponseDTOSubscriptionTypeEnum];


