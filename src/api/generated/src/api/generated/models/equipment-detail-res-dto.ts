/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 장비 상세 정보
 * @export
 * @interface EquipmentDetailResDTO
 */
export interface EquipmentDetailResDTO {
    /**
     * 장비아이디
     * @type {number}
     * @memberof EquipmentDetailResDTO
     */
    'equipmentId'?: number;
    /**
     * 장비명
     * @type {string}
     * @memberof EquipmentDetailResDTO
     */
    'equipmentName'?: string;
    /**
     * 주 사용 장비 여부
     * @type {boolean}
     * @memberof EquipmentDetailResDTO
     */
    'isPrimary'?: boolean;
    /**
     * 관리자가 등록한 장비 여부
     * @type {boolean}
     * @memberof EquipmentDetailResDTO
     */
    'isRegisteredByAdmin'?: boolean;
    /**
     * 장비타입:<br> VEHICLE: 차량<br> TRUCK: 트럭<br> HEAVY_EQUIPMENT: 중장비<br> AGRICULTURAL: 농기계<br> DRONE: 드론<br> ROBOT: 로봇<br> 
     * @type {string}
     * @memberof EquipmentDetailResDTO
     */
    'equipmentType'?: EquipmentDetailResDTOEquipmentTypeEnum;
    /**
     * 차량타입:<br> CAR: 일반 승용차<br> SUV_RV: 다목적 승용차(SUV, RV 등)<br> TRUCK: 트럭<br> BUS: 버스<br> TRAILER: 트레일러<br> LOW_SPEED: 저속 차량<br> BIKE: 이륜차(오토바이 등)<br> 
     * @type {string}
     * @memberof EquipmentDetailResDTO
     */
    'vehicleType'?: EquipmentDetailResDTOVehicleTypeEnum;
    /**
     * 제조사
     * @type {string}
     * @memberof EquipmentDetailResDTO
     */
    'manufacturer'?: string;
    /**
     * 모델명
     * @type {string}
     * @memberof EquipmentDetailResDTO
     */
    'modelName'?: string;
    /**
     * 트림명
     * @type {string}
     * @memberof EquipmentDetailResDTO
     */
    'trimName'?: string;
    /**
     * 생산년도
     * @type {number}
     * @memberof EquipmentDetailResDTO
     */
    'productYear'?: number;
    /**
     * 이미지경로
     * @type {string}
     * @memberof EquipmentDetailResDTO
     */
    'imagePath'?: string;
    /**
     * VIN No
     * @type {string}
     * @memberof EquipmentDetailResDTO
     */
    'serialNo'?: string;
    /**
     * 차량번호
     * @type {string}
     * @memberof EquipmentDetailResDTO
     */
    'plateNo'?: string;
}

export const EquipmentDetailResDTOEquipmentTypeEnum = {
    Vehicle: 'VEHICLE',
    Truck: 'TRUCK',
    HeavyEquipment: 'HEAVY_EQUIPMENT',
    Agricultural: 'AGRICULTURAL',
    Drone: 'DRONE',
    Robot: 'ROBOT'
} as const;

export type EquipmentDetailResDTOEquipmentTypeEnum = typeof EquipmentDetailResDTOEquipmentTypeEnum[keyof typeof EquipmentDetailResDTOEquipmentTypeEnum];
export const EquipmentDetailResDTOVehicleTypeEnum = {
    Car: 'CAR',
    SuvRv: 'SUV_RV',
    Truck: 'TRUCK',
    Bus: 'BUS',
    Trailer: 'TRAILER',
    LowSpeed: 'LOW_SPEED',
    Bike: 'BIKE'
} as const;

export type EquipmentDetailResDTOVehicleTypeEnum = typeof EquipmentDetailResDTOVehicleTypeEnum[keyof typeof EquipmentDetailResDTOVehicleTypeEnum];


