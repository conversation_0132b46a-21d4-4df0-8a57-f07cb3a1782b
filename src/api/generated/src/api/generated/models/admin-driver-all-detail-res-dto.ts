/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AdminEquipmentBasicResDTO } from './admin-equipment-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminFleetBasicResDTO } from './admin-fleet-basic-res-dto';

/**
 * 운전자 정보
 * @export
 * @interface AdminDriverAllDetailResDTO
 */
export interface AdminDriverAllDetailResDTO {
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverAllDetailResDTO
     */
    'driverId'?: number;
    /**
     * 운전자로그인 아이디(이메일)
     * @type {string}
     * @memberof AdminDriverAllDetailResDTO
     */
    'loginId'?: string;
    /**
     * 운전자명
     * @type {string}
     * @memberof AdminDriverAllDetailResDTO
     */
    'driverName'?: string;
    /**
     * 운전자전화번호국가코드
     * @type {string}
     * @memberof AdminDriverAllDetailResDTO
     */
    'driverCountryDialCode'?: string;
    /**
     * 운전자전화번호
     * @type {string}
     * @memberof AdminDriverAllDetailResDTO
     */
    'driverPhone'?: string;
    /**
     * 면허번호
     * @type {string}
     * @memberof AdminDriverAllDetailResDTO
     */
    'licenseNo'?: string;
    /**
     * 면허분류
     * @type {string}
     * @memberof AdminDriverAllDetailResDTO
     */
    'licenseClass'?: AdminDriverAllDetailResDTOLicenseClassEnum;
    /**
     * 면허발급주:<br> 
     * @type {string}
     * @memberof AdminDriverAllDetailResDTO
     */
    'licenseIssueState'?: AdminDriverAllDetailResDTOLicenseIssueStateEnum;
    /**
     * 면허만료일
     * @type {string}
     * @memberof AdminDriverAllDetailResDTO
     */
    'licenseExpireDt'?: string;
    /**
     * 운전자성별:<br> MALE: 남성<br> FEMALE: 여성<br> 
     * @type {string}
     * @memberof AdminDriverAllDetailResDTO
     */
    'driverGender'?: AdminDriverAllDetailResDTODriverGenderEnum;
    /**
     * 운전자관리용아이디
     * @type {string}
     * @memberof AdminDriverAllDetailResDTO
     */
    'driverManagementId'?: string;
    /**
     * 운전자상태
     * @type {string}
     * @memberof AdminDriverAllDetailResDTO
     */
    'driverStatus'?: AdminDriverAllDetailResDTODriverStatusEnum;
    /**
     * 플릿 정보
     * @type {Array<AdminFleetBasicResDTO>}
     * @memberof AdminDriverAllDetailResDTO
     */
    'fleets'?: Array<AdminFleetBasicResDTO>;
    /**
     * 장비 정보
     * @type {Array<AdminEquipmentBasicResDTO>}
     * @memberof AdminDriverAllDetailResDTO
     */
    'equipments'?: Array<AdminEquipmentBasicResDTO>;
}

export const AdminDriverAllDetailResDTOLicenseClassEnum = {
    ClassA: 'CLASS_A',
    ClassB: 'CLASS_B',
    ClassC: 'CLASS_C',
    ClassD: 'CLASS_D',
    ClassM: 'CLASS_M'
} as const;

export type AdminDriverAllDetailResDTOLicenseClassEnum = typeof AdminDriverAllDetailResDTOLicenseClassEnum[keyof typeof AdminDriverAllDetailResDTOLicenseClassEnum];
export const AdminDriverAllDetailResDTOLicenseIssueStateEnum = {
    Alabama: 'ALABAMA',
    Alaska: 'ALASKA',
    Arizona: 'ARIZONA',
    Arkansas: 'ARKANSAS',
    California: 'CALIFORNIA',
    Colorado: 'COLORADO',
    Connecticut: 'CONNECTICUT',
    Delaware: 'DELAWARE',
    Florida: 'FLORIDA',
    Georgia: 'GEORGIA',
    Hawaii: 'HAWAII',
    Idaho: 'IDAHO',
    Illinois: 'ILLINOIS',
    Indiana: 'INDIANA',
    Iowa: 'IOWA',
    Kansas: 'KANSAS',
    Kentucky: 'KENTUCKY',
    Louisiana: 'LOUISIANA',
    Maine: 'MAINE',
    Maryland: 'MARYLAND',
    Massachusetts: 'MASSACHUSETTS',
    Michigan: 'MICHIGAN',
    Minnesota: 'MINNESOTA',
    Mississippi: 'MISSISSIPPI',
    Missouri: 'MISSOURI',
    Montana: 'MONTANA',
    Nebraska: 'NEBRASKA',
    Nevada: 'NEVADA',
    NewHampshire: 'NEW_HAMPSHIRE',
    NewJersey: 'NEW_JERSEY',
    NewMexico: 'NEW_MEXICO',
    NewYork: 'NEW_YORK',
    NorthCarolina: 'NORTH_CAROLINA',
    NorthDakota: 'NORTH_DAKOTA',
    Ohio: 'OHIO',
    Oklahoma: 'OKLAHOMA',
    Oregon: 'OREGON',
    Pennsylvania: 'PENNSYLVANIA',
    RhodeIsland: 'RHODE_ISLAND',
    SouthCarolina: 'SOUTH_CAROLINA',
    SouthDakota: 'SOUTH_DAKOTA',
    Tennessee: 'TENNESSEE',
    Texas: 'TEXAS',
    Utah: 'UTAH',
    Vermont: 'VERMONT',
    Virginia: 'VIRGINIA',
    Washington: 'WASHINGTON',
    WashingtonDc: 'WASHINGTON_DC',
    WestVirginia: 'WEST_VIRGINIA',
    Wisconsin: 'WISCONSIN',
    Wyoming: 'WYOMING'
} as const;

export type AdminDriverAllDetailResDTOLicenseIssueStateEnum = typeof AdminDriverAllDetailResDTOLicenseIssueStateEnum[keyof typeof AdminDriverAllDetailResDTOLicenseIssueStateEnum];
export const AdminDriverAllDetailResDTODriverGenderEnum = {
    Male: 'MALE',
    Female: 'FEMALE'
} as const;

export type AdminDriverAllDetailResDTODriverGenderEnum = typeof AdminDriverAllDetailResDTODriverGenderEnum[keyof typeof AdminDriverAllDetailResDTODriverGenderEnum];
export const AdminDriverAllDetailResDTODriverStatusEnum = {
    OnDuty: 'ON_DUTY',
    Idle: 'IDLE'
} as const;

export type AdminDriverAllDetailResDTODriverStatusEnum = typeof AdminDriverAllDetailResDTODriverStatusEnum[keyof typeof AdminDriverAllDetailResDTODriverStatusEnum];


