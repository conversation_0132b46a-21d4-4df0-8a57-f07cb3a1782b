/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 사용자 등록 요청 DTO
 * @export
 * @interface RegisterRequestDTO
 */
export interface RegisterRequestDTO {
    /**
     * 사용자의 이름
     * @type {string}
     * @memberof RegisterRequestDTO
     */
    'firstname'?: string;
    /**
     * 사용자의 성
     * @type {string}
     * @memberof RegisterRequestDTO
     */
    'lastname'?: string;
    /**
     * 사용자의 이메일 주소
     * @type {string}
     * @memberof RegisterRequestDTO
     */
    'email'?: string;
    /**
     * 사용자의 비밀번호
     * @type {string}
     * @memberof RegisterRequestDTO
     */
    'password'?: string;
    /**
     * 사용자의 역할. 미입력시 USER로 설정됩니다.
     * @type {string}
     * @memberof RegisterRequestDTO
     */
    'role'?: RegisterRequestDTORoleEnum;
    /**
     * 이메일 코드 인증 완료후 응답으로 받은  completedVerificationId 값 
     * @type {string}
     * @memberof RegisterRequestDTO
     */
    'completedVerificationId'?: string;
}

export const RegisterRequestDTORoleEnum = {
    User: 'USER',
    Admin: 'ADMIN'
} as const;

export type RegisterRequestDTORoleEnum = typeof RegisterRequestDTORoleEnum[keyof typeof RegisterRequestDTORoleEnum];


