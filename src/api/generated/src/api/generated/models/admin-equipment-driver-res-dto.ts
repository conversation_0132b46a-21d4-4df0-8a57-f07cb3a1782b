/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 장비의 운전자 정보
 * @export
 * @interface AdminEquipmentDriverResDTO
 */
export interface AdminEquipmentDriverResDTO {
    /**
     * 장비아이디
     * @type {number}
     * @memberof AdminEquipmentDriverResDTO
     */
    'equipmentId'?: number;
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminEquipmentDriverResDTO
     */
    'driverId'?: number;
    /**
     * 운전자명
     * @type {string}
     * @memberof AdminEquipmentDriverResDTO
     */
    'driverName'?: string;
    /**
     * 운전자전화번호국가코드
     * @type {string}
     * @memberof AdminEquipmentDriverResDTO
     */
    'driverCountryDialCode'?: string;
    /**
     * 운전자전화번호
     * @type {string}
     * @memberof AdminEquipmentDriverResDTO
     */
    'driverPhone'?: string;
    /**
     * 운전자상태
     * @type {string}
     * @memberof AdminEquipmentDriverResDTO
     */
    'driverStatus'?: AdminEquipmentDriverResDTODriverStatusEnum;
    /**
     * 장비 수
     * @type {number}
     * @memberof AdminEquipmentDriverResDTO
     */
    'equipmentCount'?: number;
}

export const AdminEquipmentDriverResDTODriverStatusEnum = {
    OnDuty: 'ON_DUTY',
    Idle: 'IDLE'
} as const;

export type AdminEquipmentDriverResDTODriverStatusEnum = typeof AdminEquipmentDriverResDTODriverStatusEnum[keyof typeof AdminEquipmentDriverResDTODriverStatusEnum];


