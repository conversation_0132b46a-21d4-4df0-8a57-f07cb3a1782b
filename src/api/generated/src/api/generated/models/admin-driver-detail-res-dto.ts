/* tslint:disable */
/* eslint-disable */
/**
 * OpenApi Specification - Carta FMS
 * OpenApi documentation for Carta FMS
 *
 * The version of the OpenAPI document: 1.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AdminEquipmentBasicResDTO } from './admin-equipment-basic-res-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { AdminFleetBasicResDTO } from './admin-fleet-basic-res-dto';

/**
 * 운전자 정보
 * @export
 * @interface AdminDriverDetailResDTO
 */
export interface AdminDriverDetailResDTO {
    /**
     * 운전자아이디
     * @type {number}
     * @memberof AdminDriverDetailResDTO
     */
    'driverId'?: number;
    /**
     * 운전자로그인 아이디(이메일)
     * @type {string}
     * @memberof AdminDriverDetailResDTO
     */
    'loginId'?: string;
    /**
     * 운전자명
     * @type {string}
     * @memberof AdminDriverDetailResDTO
     */
    'driverName'?: string;
    /**
     * 운전자전화번호국가코드
     * @type {string}
     * @memberof AdminDriverDetailResDTO
     */
    'driverCountryDialCode'?: string;
    /**
     * 운전자전화번호
     * @type {string}
     * @memberof AdminDriverDetailResDTO
     */
    'driverPhone'?: string;
    /**
     * 면허번호
     * @type {string}
     * @memberof AdminDriverDetailResDTO
     */
    'licenseNo'?: string;
    /**
     * 면허분류
     * @type {string}
     * @memberof AdminDriverDetailResDTO
     */
    'licenseClass'?: AdminDriverDetailResDTOLicenseClassEnum;
    /**
     * 면허발급주:<br> 
     * @type {string}
     * @memberof AdminDriverDetailResDTO
     */
    'licenseIssueState'?: AdminDriverDetailResDTOLicenseIssueStateEnum;
    /**
     * 면허만료일
     * @type {string}
     * @memberof AdminDriverDetailResDTO
     */
    'licenseExpireDt'?: string;
    /**
     * 운전자상태
     * @type {string}
     * @memberof AdminDriverDetailResDTO
     */
    'driverStatus'?: AdminDriverDetailResDTODriverStatusEnum;
    /**
     * 플릿 정보
     * @type {Array<AdminFleetBasicResDTO>}
     * @memberof AdminDriverDetailResDTO
     */
    'fleets'?: Array<AdminFleetBasicResDTO>;
    /**
     * 장비 정보
     * @type {Array<AdminEquipmentBasicResDTO>}
     * @memberof AdminDriverDetailResDTO
     */
    'equipments'?: Array<AdminEquipmentBasicResDTO>;
}

export const AdminDriverDetailResDTOLicenseClassEnum = {
    ClassA: 'CLASS_A',
    ClassB: 'CLASS_B',
    ClassC: 'CLASS_C',
    ClassD: 'CLASS_D',
    ClassM: 'CLASS_M'
} as const;

export type AdminDriverDetailResDTOLicenseClassEnum = typeof AdminDriverDetailResDTOLicenseClassEnum[keyof typeof AdminDriverDetailResDTOLicenseClassEnum];
export const AdminDriverDetailResDTOLicenseIssueStateEnum = {
    Alabama: 'ALABAMA',
    Alaska: 'ALASKA',
    Arizona: 'ARIZONA',
    Arkansas: 'ARKANSAS',
    California: 'CALIFORNIA',
    Colorado: 'COLORADO',
    Connecticut: 'CONNECTICUT',
    Delaware: 'DELAWARE',
    Florida: 'FLORIDA',
    Georgia: 'GEORGIA',
    Hawaii: 'HAWAII',
    Idaho: 'IDAHO',
    Illinois: 'ILLINOIS',
    Indiana: 'INDIANA',
    Iowa: 'IOWA',
    Kansas: 'KANSAS',
    Kentucky: 'KENTUCKY',
    Louisiana: 'LOUISIANA',
    Maine: 'MAINE',
    Maryland: 'MARYLAND',
    Massachusetts: 'MASSACHUSETTS',
    Michigan: 'MICHIGAN',
    Minnesota: 'MINNESOTA',
    Mississippi: 'MISSISSIPPI',
    Missouri: 'MISSOURI',
    Montana: 'MONTANA',
    Nebraska: 'NEBRASKA',
    Nevada: 'NEVADA',
    NewHampshire: 'NEW_HAMPSHIRE',
    NewJersey: 'NEW_JERSEY',
    NewMexico: 'NEW_MEXICO',
    NewYork: 'NEW_YORK',
    NorthCarolina: 'NORTH_CAROLINA',
    NorthDakota: 'NORTH_DAKOTA',
    Ohio: 'OHIO',
    Oklahoma: 'OKLAHOMA',
    Oregon: 'OREGON',
    Pennsylvania: 'PENNSYLVANIA',
    RhodeIsland: 'RHODE_ISLAND',
    SouthCarolina: 'SOUTH_CAROLINA',
    SouthDakota: 'SOUTH_DAKOTA',
    Tennessee: 'TENNESSEE',
    Texas: 'TEXAS',
    Utah: 'UTAH',
    Vermont: 'VERMONT',
    Virginia: 'VIRGINIA',
    Washington: 'WASHINGTON',
    WashingtonDc: 'WASHINGTON_DC',
    WestVirginia: 'WEST_VIRGINIA',
    Wisconsin: 'WISCONSIN',
    Wyoming: 'WYOMING'
} as const;

export type AdminDriverDetailResDTOLicenseIssueStateEnum = typeof AdminDriverDetailResDTOLicenseIssueStateEnum[keyof typeof AdminDriverDetailResDTOLicenseIssueStateEnum];
export const AdminDriverDetailResDTODriverStatusEnum = {
    OnDuty: 'ON_DUTY',
    Idle: 'IDLE'
} as const;

export type AdminDriverDetailResDTODriverStatusEnum = typeof AdminDriverDetailResDTODriverStatusEnum[keyof typeof AdminDriverDetailResDTODriverStatusEnum];


