import { getLoggedInUser } from '../plugins/api_helper.ts';
import { useEffect, useState } from 'react';

const useProfile = () => {
  const userProfileSession = getLoggedInUser();
  if (!userProfileSession) return false;
  const token = userProfileSession && userProfileSession['access_token'];
  const [loading, setLoading] = useState(userProfileSession ? false : true);
  const [userProfile, setUserProfile] = useState(
    userProfileSession ? userProfileSession : null,
  );

  useEffect(() => {
    const userProfileSession = getLoggedInUser();
    const token = userProfileSession && userProfileSession['access_token'];
    setUserProfile(userProfileSession ? userProfileSession : null);
    setLoading(token ? false : true);
  }, []);

  return { userProfile, loading, token };
};

export { useProfile };
