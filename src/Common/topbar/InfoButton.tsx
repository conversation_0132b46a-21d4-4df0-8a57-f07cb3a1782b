import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Popover } from '@radix-ui/themes';
import { logout } from '@/plugins/api_helper.ts';

const NotificationButton = () => {
  const { t } = useTranslation();

  const navigate = useNavigate();

  type TabType = 'PersonalSetting' | 'NotificationSettings' | 'MyMachine';

  const routeInfo = (tab: TabType) => {
    console.log(tab);
    navigate('/profile', { state: { tab } });
  };

  return (
    <Popover.Root>
      <Popover.Trigger>
        <div className={`ml-[14px] subtitle4 text-gray-15 cursor-pointer`}>
          <PERSON>
        </div>
      </Popover.Trigger>
      <Popover.Content
        size="1"
        align="center"
        maxWidth="300px"
        style={{ padding: '5px' }}
        className="
          space-y-[5px]
          [&>p]:py-[10px]
          [&>p]:px-[15px]
          [&>p]:rounded-md
          [&>p]:body3
          [&>p:hover]:bg-gray-3
          [&>p:hover]:transition-all
          [&>p:hover]:duration-200
        "
      >
        <p onClick={() => routeInfo('PersonalSetting' as TabType)}>
          {t('PersonalSetting')}
        </p>
        <p onClick={() => routeInfo('NotificationSettings' as TabType)}>
          {t('NotificationSettings')}
        </p>
        {/* <p onClick={() => routeInfo('MyMachine' as TabType)}>
          {t('MyMachine')}
        </p> */}
        <div className="divider" />
        <p onClick={logout}>{t('Logout')}</p>
      </Popover.Content>
    </Popover.Root>
  );
};

export default NotificationButton;
