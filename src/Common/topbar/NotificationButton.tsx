import { useTranslation } from 'react-i18next';
import alarm from '@/assets/images/ic/24/alarm_on.svg';
import { Popover } from '@radix-ui/themes';

const NotificationButton = () => {
  const { t } = useTranslation();

  const notifications = [
    {
      title: 'Machine',
      machine: 'BX5-3 | 0001 | USA-0001',
      message: 'A malfunction has occurred in the equipment. Please check.',
      time: '1 min ago',
    },
    {
      title: 'Machine',
      machine: 'BX5-4 | 0002 | USA-0002',
      message: 'A malfunction has occurred in the equipment. Please check.',
      time: '11 mins ago',
    },
    {
      title: 'Work Order',
      machine: 'BX5-3 | 0001 | USA-0001',
      message: 'The work order approval has been processed.',
      time: '27 min ago',
    },
    {
      title: 'Schedule',
      machine: '',
      message: '08:00 Olive task and 6 more A new schedule is available.',
      time: '2024-12-23',
    },
  ];

  return (
    <Popover.Root>
      <Popover.Trigger>
        <img src={alarm} alt="alarm" className="cursor-pointer" />
      </Popover.Trigger>
      <Popover.Content
        size="1"
        align="center"
        maxWidth="250px"
        className="flex flex-col bg-white rounded-lg border border-gray-2 !p-0"
      >
        <div className="mb-[5px] py-3 px-5 subtitle4 border-b border-gray-6">
          {t('Alarm')}
          <span className="ml-[10px] p-1 b-b-r subtitle7 text-secondary-6">
            12
          </span>
        </div>
        {notifications.map((notification, index) => (
          <div key={index}>
            <div className="mb-[5px] mx-[5px] py-3 px-[15px] flex flex-col rounded-md caption3 transition-all duration-200 [&_p]:text-gray-15 hover:bg-gray-3">
              <p className="mb-[11px] flex items-center gap-[7px]">
                <span className="w-[18px] h-[18px] flex items-center justify-center bg-secondary-6 rounded-full subtitle7 text-gray-1">
                  N
                </span>
                {notification.title}
                <span className="text-gray-8 caption4">
                  {notification.time}
                </span>
              </p>
              <p>{notification.machine}</p>
              <p>{notification.message}</p>
            </div>
            {index < notifications.length - 1 && (
              <div className="w-[calc(100%-40px)] mx-5">
                <div className="divider my-[5px]" />
              </div>
            )}
          </div>
        ))}
      </Popover.Content>
    </Popover.Root>
  );
};

export default NotificationButton;
