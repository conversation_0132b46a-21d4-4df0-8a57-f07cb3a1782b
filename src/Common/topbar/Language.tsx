import { useTranslation } from 'react-i18next';
import { Popover } from '@radix-ui/themes';
import Language from '@/assets/images/ic/24/globe.svg';
import american from '@/assets/images/topbar/american.svg';
import korean from '@/assets/images/topbar/korean.svg';

export const LanguageButton = () => {
  const { t } = useTranslation();

  const { i18n } = useTranslation();

  const languageChange = (lang: string) => {
    const langCode = lang === t('English') ? 'en' : 'ko';
    i18n.changeLanguage(langCode);
  };

  return (
    <Popover.Root>
      <Popover.Trigger>
        <img src={Language} alt="Language" className="cursor-pointer" />
      </Popover.Trigger>
      <Popover.Content
        size="1"
        align="center"
        maxWidth="300px"
        style={{ padding: '5px' }}
        className="
          space-y-[5px]
          [&>p]:py-[10px]
          [&>p]:px-[15px]
          [&>p]:flex
          [&>p]:items-center
          [&>p]:gap-2
          [&>p]:rounded-md
          [&>p]:body3
          [&_img]:py-1
          [&_img]:px-[2px]
          [&_img]:border
          [&_img]:rounded-md
          [&_img]:border-gray-6
          [&>p:hover]:bg-gray-3
          [&>p:hover]:transition-all
          [&>p:hover]:duration-200
        "
      >
        <p onClick={() => languageChange(t('English'))}>
          <img src={american} alt="american" /> {t('English')}
        </p>
        <p onClick={() => languageChange(t('Korean'))}>
          <img src={korean} alt="korean" /> {t('Korean')}
        </p>
      </Popover.Content>
    </Popover.Root>
  );
};
