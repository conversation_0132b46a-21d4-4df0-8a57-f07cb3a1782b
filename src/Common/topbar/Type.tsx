// src/Common/topbar/Type.tsx
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Popover } from '@radix-ui/themes';
import { useSelectedType } from '@/context/SelectedTypeContext';
import { cn } from '@/Common/function/utils.ts';
import IcArrowDownB from '@/assets/images/svg/24/IcArrowDownB';
import TruckVehicle from '@/assets/images/svg/24/TruckVehicle.tsx';
import Heavy from '@/assets/images/svg/24/Heavy';
import Agricultural from '@/assets/images/svg/24/Agricultural.tsx';
import Drone from '@/assets/images/svg/24/Drone.tsx';

type Option = { key: string; Icon: React.ElementType };

const options: Option[] = [
  { key: 'Vehicle / Truck', Icon: TruckVehicle },
  { key: 'HeavyEquip', Icon: Heavy },
  { key: 'agricultural', Icon: Agricultural },
  { key: 'drone', Icon: Drone },
];

const Type = () => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const { selectedType, setSelectedType } = useSelectedType();

  const selectedOption =
    options.find((o) => o.key === selectedType) || options[0];

  return (
    <Popover.Root open={open} onOpenChange={setOpen}>
      <Popover.Trigger>
        <div className="flex items-center gap-[10px] subtitle3 text-gray-15 cursor-pointer">
          <selectedOption.Icon className="[&>path]:text-secondary-6 [&>circle]:text-secondary-6" />
          <p>{t(selectedOption.key)}</p>
          <IcArrowDownB
            className={cn(
              'w-6 h-6 flex-shrink-0 transition-transform duration-200',
              { 'rotate-180': open },
            )}
          />
        </div>
      </Popover.Trigger>

      <Popover.Content
        size="1"
        align="center"
        maxWidth="300px"
        style={{ padding: '5px' }}
        className="
          space-y-[5px]
          [&>p]:py-[9px]
          [&>p]:px-3
          [&>p]:flex
          [&>p]:items-center
          [&>p]:rounded-md
          [&>p]:gap-2
          [&>p]:body3
          [&>p:hover]:bg-gray-3
          [&>p:hover]:transition-all
          [&>p:hover]:duration-200
        "
      >
        {options.map(({ key, Icon }) => (
          <p
            key={key}
            onClick={() => {
              setSelectedType(key);
              setOpen(false);
            }}
          >
            <Icon className="w-6" /> {t(key)}
          </p>
        ))}
      </Popover.Content>
    </Popover.Root>
  );
};

export default Type;
