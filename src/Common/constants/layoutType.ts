const LAYOUT_MODE = {
  LIGHT: 'light',
  DARK: 'dark',
} as const;

const SIDEBAR_COLOR = {
  LIGHT: 'light',
  DARK: 'dark',
  BRAND: 'brand',
} as const;

const LAYOUT_DIRECTION = {
  LTR: 'ltr',
  RTL: 'rtl',
} as const;

const LAYOUT_TYPE = {
  LG: 'lg',
  SM: 'sm',
} as const;

const LAYOUT_TYPE_NAME = {
  VERTICAL: 'vertical',
  CREATIVEDETACHED: 'creative-detached',
  SIMPLEDETACHED: 'detached-simple',
} as const;

// TypeScript type definitions
type TLayoutMode = (typeof LAYOUT_MODE)[keyof typeof LAYOUT_MODE];
type TSidebarColor = (typeof SIDEBAR_COLOR)[keyof typeof SIDEBAR_COLOR];
type TLayoutDirection =
  (typeof LAYOUT_DIRECTION)[keyof typeof LAYOUT_DIRECTION];
type TLayoutType = (typeof LAYOUT_TYPE)[keyof typeof LAYOUT_TYPE];
type TLayoutTypeName = (typeof LAYOUT_TYPE_NAME)[keyof typeof LAYOUT_TYPE_NAME];

export {
  LAYOUT_MODE,
  LAYOUT_DIRECTION,
  LAYOUT_TYPE,
  LAYOUT_TYPE_NAME,
  SIDEBAR_COLOR,
  // Type exports
  type TLayoutMode,
  type TSidebarColor,
  type TLayoutDirection,
  type TLayoutType,
  type TLayoutTypeName,
};
