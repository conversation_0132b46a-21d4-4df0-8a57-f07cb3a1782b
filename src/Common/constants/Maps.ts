import { GeneralMapAdapter } from '@/logiMaps/react/general/Map';
import { EqBreakdownStatus, EqOperationStatus } from '@/types';
import { DashboardType } from '@/types/DashboardType';
import { EquipmentType } from '@/types/EquipmentType';
import { StatisticsType } from '@/types/StatisticsType';

type MarkerPlaneData = {
  markerItem: EquipmentType.MarkerMapItem;
  ratioPlane?: {
    level: number;
    x: number;
    y: number;
  };
};

export type EqSingleMarkerItem = {
  id: string;
  latlng: {
    lat: number;
    lng: number;
  };
  operationStatus?: EqOperationStatus;
  breakdownStatus?: EqBreakdownStatus;
};

export type EqSingleMarkerSet = {
  dataLevel: number;
  items: EqSingleMarkerItem[];
};

export type EqGroupMarkerItem = {
  id: string;
  latlng: {
    lat: number;
    lng: number;
  };
  markers: {
    id: string;
    latlng: {
      lat: number;
      lng: number;
    };
  }[];
};

export type EqGroupMarkerSet = {
  dataLevel: number;
  items: EqGroupMarkerItem[];
};

const KeySpots = {
  Coords: {
    SeoulStation: { lat: 37.554816, lng: 126.970583 },
    ForHu: { lat: 37.542737, lng: 127.044839 },
    LACMA: { lat: 34.062952, lng: -118.359952 },
  },
  Addresss: {
    SeoulStation: '대한민국 서울시 용산구 한강대로 405',
    ForHu: '대한민국 서울 성동구 왕십리로 58',
    LACMA: 'Wilshire Blvd, Los Angeles, CA 90036 USA',
  },
};

export const DemoMode = {
  Coords: KeySpots.Coords.LACMA,
  Addresss: KeySpots.Addresss.LACMA,
  //Kor
  //boundary: {min:{lat: 37.2, lng: 126.8}, max:{lat: 37.8, lng: 127.8}}
  //LA
  boundary: {
    min: { lat: 33.820281, lng: -118.380087 },
    max: { lat: 34.252899, lng: -118.198339 },
  },
};

export const MapHelper = {
  makeEqSingleMarkerSet: (
    markers: EquipmentType.MarkerMapItem[] | DashboardType.FilteredMapItem[],
    generalMapAdapter: GeneralMapAdapter,
    dataLevel: number,
    overlapDist: number,
  ): EqSingleMarkerSet => {
    const eqSingleMarkerSet: EqSingleMarkerSet = {
      dataLevel: dataLevel,
      items: [],
    };

    let eqOnPlane: MarkerPlaneData[] = [];
    for (const row of markers) {
      const markerPlaneData: MarkerPlaneData = { markerItem: row };

      const ratioPlane = generalMapAdapter.world2plane(
        {
          lat: markerPlaneData.markerItem.latlng.lat,
          lng: markerPlaneData.markerItem.latlng.lng,
        },
        dataLevel,
      );

      markerPlaneData.ratioPlane = {
        level: dataLevel,
        x: ratioPlane.x,
        y: ratioPlane.y,
      };

      eqOnPlane.push(markerPlaneData);
    }

    while (eqOnPlane.length > 0) {
      const _eqOnPlane = eqOnPlane;
      eqOnPlane = [];

      const baseMark = _eqOnPlane.pop();
      if (!baseMark?.ratioPlane) {
        continue;
      }

      for (const row of _eqOnPlane) {
        if (row?.ratioPlane) {
          const dist = Math.sqrt(
            Math.pow(baseMark.ratioPlane.x - row.ratioPlane.x, 2) +
              Math.pow(baseMark.ratioPlane.y - row.ratioPlane.y, 2),
          );
          if (dist > overlapDist) {
            eqOnPlane.push(row);
          }
        }
      }

      eqSingleMarkerSet.items.push({
        id: baseMark.markerItem.id,
        latlng: { ...baseMark.markerItem.latlng },
        operationStatus: baseMark.markerItem.operationStatus
          ? { ...baseMark.markerItem.operationStatus }
          : undefined,
        breakdownStatus: baseMark.markerItem.breakdownStatus
          ? { ...baseMark.markerItem.breakdownStatus }
          : undefined,
      });
    }

    return eqSingleMarkerSet;
  },

  makeEqGroupMarkerSet: (
    markers: EquipmentType.MarkerMapItem[] | DashboardType.FilteredMapItem[],
    generalMapAdapter: GeneralMapAdapter,
    dataLevel: number,
    overlapDist: number,
  ): EqGroupMarkerSet => {
    const eqGroupMarkerSet: EqGroupMarkerSet = {
      dataLevel: dataLevel,
      items: [],
    };
    let eqOnPlane: MarkerPlaneData[] = [];
    for (const row of markers) {
      const markerPlaneData: MarkerPlaneData = { markerItem: row };

      const ratioPlane = generalMapAdapter.world2plane(
        {
          lat: markerPlaneData.markerItem.latlng.lat,
          lng: markerPlaneData.markerItem.latlng.lng,
        },
        dataLevel,
      );

      markerPlaneData.ratioPlane = {
        level: dataLevel,
        x: ratioPlane.x,
        y: ratioPlane.y,
      };

      eqOnPlane.push(markerPlaneData);
    }

    while (eqOnPlane.length > 0) {
      const _eqOnPlane = eqOnPlane;
      eqOnPlane = [];
      const overlapMarks: {
        id: string;
        latlng: {
          lat: number;
          lng: number;
        };
      }[] = [];
      const baseMark = _eqOnPlane.pop();
      if (!baseMark?.ratioPlane) {
        continue;
      }

      overlapMarks.push({
        id: baseMark.markerItem.id,
        latlng: { ...baseMark.markerItem.latlng },
      });

      for (const row of _eqOnPlane) {
        if (row?.ratioPlane) {
          const dist = Math.sqrt(
            Math.pow(baseMark.ratioPlane.x - row.ratioPlane.x, 2) +
              Math.pow(baseMark.ratioPlane.y - row.ratioPlane.y, 2),
          );
          if (dist <= overlapDist) {
            overlapMarks.push({
              id: row.markerItem.id,
              latlng: { ...row.markerItem.latlng },
            });
          } else {
            eqOnPlane.push(row);
          }
        }
      }

      eqGroupMarkerSet.items.push({
        id: baseMark.markerItem.id,
        latlng: { ...baseMark.markerItem.latlng },
        markers: overlapMarks,
      });
    }

    return eqGroupMarkerSet;
  },
};

export const MapRandomData = {
  getRandomLat: (): number => {
    const min = DemoMode.boundary.min.lat;
    const max = DemoMode.boundary.max.lat;

    return Math.random() * (max - min) + min;
  },

  getRandomLng: (): number => {
    const min = DemoMode.boundary.min.lng;
    const max = DemoMode.boundary.max.lng;

    return Math.random() * (max - min) + min;
  },

  getRandomFloat: (min: number, max: number): number => {
    return Math.random() * (max - min) + min;
  },

  getRandomInt: (min: number, max: number): number => {
    return Math.round(Math.random() * (max - min) + min);
  },

  getRandomBoolean: () => {
    return Math.random() < 0.5;
  },

  getRandomCoord(
    lat: number,
    lng: number,
    angle: number,
    r_min: number,
    r_max: number,
    angle_deviation: number,
  ) {
    const rad = angle * (Math.PI / 180);
    const rad_deviation = angle_deviation * (Math.PI / 180);

    // 1. 랜덤 반경 r을 선택 (r_min <= r <= r_max)
    const r = Math.random() * (r_max - r_min) + r_min;

    // 2. 랜덤 각도를 선택 (rad - rad_deviation <= random_angle <= rad + rad_deviation)
    const random_angle =
      rad + (Math.random() * (2 * rad_deviation) - rad_deviation);

    // 3. 새로운 좌표 계산
    const randLng = lng + r * Math.cos(random_angle);
    const randLat = lat + r * Math.sin(random_angle);

    return { lat: randLat, lng: randLng };
  },

  getRandomPath(
    cnt: number,
    lat: number,
    lng: number,
    angle: number,
    r_min: number,
    r_max: number,
    angle_deviation: number,
  ) {
    const path: { lat: number; lng: number }[] = [];

    let currLatlng = { lat: lat, lng: lng };
    path.push(currLatlng);
    for (let idx = 0; idx < cnt; ++idx) {
      currLatlng = MapRandomData.getRandomCoord(
        currLatlng.lat,
        currLatlng.lng,
        angle,
        r_min,
        r_max,
        angle_deviation,
      );
      path.push(currLatlng);
    }

    return path;
  },

  getHeatmapDatas: (cnt: number): StatisticsType.HeatmapFilteredMapItem[] => {
    const result: StatisticsType.HeatmapFilteredMapItem[] = [];
    for (let idx = 0; idx < cnt; ++idx) {
      //path
      const randomCoord = MapRandomData.getRandomCoord(
        DemoMode.Coords.lat,
        DemoMode.Coords.lng,
        360,
        0.02,
        0.08,
        360,
      );
      const randomPath = MapRandomData.getRandomPath(
        128,
        randomCoord.lat,
        randomCoord.lng,
        30,
        0.0002,
        0.0004,
        360,
      );

      result.push({
        item: {
          id: idx.toString(),
          fleet: idx.toString(),
          latlng: {
            lat: MapRandomData.getRandomLat(),
            lng: MapRandomData.getRandomLng(),
          },
          commType: 'M',
          machineType: ['E', 'L', 'F', 'S'][MapRandomData.getRandomInt(0, 3)],
          modelName: ['HXDEMO', 'HX380LS', 'HL955T3', 'R505LVSS'][
            MapRandomData.getRandomInt(0, 3)
          ],
          plateNo: MapRandomData.getRandomInt(1, 9999)
            .toString()
            .padStart(4, '0'),
          equipmentId: 'HHKHFT23JF0000919',
          serialNo: MapRandomData.getRandomInt(1, 9999).toString(),
          location: DemoMode.Addresss,
          dealer: 'JR SALES & SERVICE',
          driver: {
            id: 'D123456',
            name: 'John Doe',
            phone: '010-1234-5678',
          },
          mileage: MapRandomData.getRandomInt(9, 9999).toString(),
          service: {
            startDate: '09-Apr-2013',
            endDate: '09-Apr-2018',
            status: '운행 중',
          },
          eqStat: {
            operation: MapRandomData.getRandomBoolean(),
            idle: MapRandomData.getRandomBoolean(),
            fault: MapRandomData.getRandomBoolean(),
            required: MapRandomData.getRandomBoolean(),
            maint: MapRandomData.getRandomBoolean(),
          },
          lastUpdate: '',
          path: randomPath,
        },
        checked: false,
      });
    }
    return result;
  },
};

export {};
