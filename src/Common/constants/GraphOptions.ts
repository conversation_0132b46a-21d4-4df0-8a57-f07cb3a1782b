import { t } from 'i18next';
import * as echarts from 'echarts';
import {
  generateDateArray,
  generateMonthArray,
} from '@/Common/function/functions.ts';

export const COLORS = ['#010542', '#FF5900', '#58B4B6', '#D9D5C4', '#F6F7F2'];
export const COLORSBreakdown = [
  '#ed6750',
  '#3fa68a',
  '#4c8af7',
  '#f4bc30',
  '#ff0000',
];

export const IndexChartOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'item',
  },
  legend: {
    bottom: '3%',
    left: 'center',
    icon: 'circle',
    itemGap: 8,
    textStyle: {
      fontSize: 13,
      fontFamily: 'Pretendard',
      padding: [0, 0, 0, -8],
    },
  },

  series: [
    {
      color: COLORS,

      type: 'pie',
      radius: ['40%', '70%'],

      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
      },
      emphasis: {
        label: {
          // show: true,
          fontSize: 40,
          fontWeight: 'bold',
        },
      },
      labelLine: {
        show: false,
      },
      data: [{}],
    },
  ],
};

export const BREAKCOLORS = ['#1C76E0', '#EAB522', '#38AA51'];

export const CircleGraphOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  title: {
    text: '',
    left: 'center',
    top: '37.5%',
    textStyle: {
      fontSize: 38,
      fontWeight: 600,
      color: '#171717',
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['60%', '90%'],
      avoidLabelOverlap: false,
      emphasis: {
        label: {
          show: true,
          fontSize: 18,
          fontWeight: 'bold',
        },
      },
      labelLine: {
        show: false,
      },
      data: [] as { value?: number; name?: string }[],
    },
  ],
};

export const LineGraphOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  grid: {
    left: '3%',
    right: '3%',
    top: '20%',
    bottom: '10%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [] as string[],
    axisTick: { show: false },
    axisLine: { show: true, lineStyle: { color: '#6F6F6F' } },
  },

  yAxis: {
    type: 'value',
    min: 0,
    max: 150,
    interval: 50,
    axisLine: { show: false },
    splitLine: { lineStyle: { color: '#ECECEC', type: 'dashed' } },
    axisLabel: {
      formatter: '{value}',
    },
  },
  series: [
    {
      data: [{}],
      type: 'line',
      showSymbol: false,
      symbol: 'circle',
      symbolSize: 14,
      emphasis: {
        focus: 'none',
        showSymbol: false,
        itemStyle: {
          color: '#fff',
          borderColor: '#FF5900',
          borderWidth: 3,
        },
        lineStyle: {
          color: '#FF5900',
          backgroundColor: '#FF5900',
          width: 2,
        },
      },
      lineStyle: {
        color: '#FF5900',
        width: 2,
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(232,89,0,0.1)' },
          { offset: 1, color: 'rgba(255,255,255,1)' },
        ]),
      },
    },
  ],
  tooltip: {} as {
    trigger: string;
    axisPointer: { type: string };
    formatter: (params: { name: string; value: [string, number] }[]) => string;
  },
};

export const InteractiveInfoOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  grid: {
    left: '3%',
    right: '3%',
    top: '20%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    data: [{}],
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        width: 2,
      },
    },
  },
  yAxis: {
    type: 'value',
    splitLine: { lineStyle: { color: '#ECECEC', type: 'dashed' } },
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  series: [
    {
      itemStyle: {
        borderRadius: [6, 6, 0, 0],
        borderType: 'solid',
      },
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#C3C5DF',
        },
        {
          offset: 1,
          color: '#010542',
        },
      ]),
      barWidth: 36,
      data: [{}],
      type: 'bar',
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#6AD08D',
          },
          {
            offset: 1,
            color: 'rgba(255, 255, 255, 0)',
          },
        ]),
      },
    },
  ],
};

const borderRadius = [0, 40, 40, 0];

export const EngineActiveInfoOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '4%',
    top: '0%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    boundaryGap: [0, 0.01],
    splitLine: { lineStyle: { type: 'dashed' } },
    axisLine: { show: true, lineStyle: { type: 'solid' } },
  },
  yAxis: {
    type: 'category',
    data: [t('IdlingE'), t('TravelingE'), t('WorkingE'), t('EngineRun')],
    axisTick: {
      show: false,
    },
    axisLine: { show: false, lineStyle: { type: 'dashed' } },
  },
  series: [
    {
      type: 'bar',
      label: {
        show: false,
      },
      emphasis: {
        focus: 'series',
      },
      barWidth: 27,
      data: [
        {
          value: 18203,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#82C2FF',
              },
            ]),
          },
        },
        {
          value: 23489,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#C19DFF',
              },
            ]),
          },
        },
        {
          value: 29034,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#5D83E3',
              },
            ]),
          },
        },
        {
          value: 104970,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#5DE3A4',
              },
            ]),
          },
        },
      ],
    },
    {
      type: 'bar',
      barWidth: 27,
      label: {
        show: false,
      },
      emphasis: {
        focus: 'series',
      },
      lineStyle: {
        width: 27,
      },
      data: [
        {
          value: 19325,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#82C2FF',
              },
            ]),
          },
        },
        {
          value: 23438,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#C19DFF',
              },
            ]),
          },
        },
        {
          value: 31000,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#5D83E3',
              },
            ]),
          },
        },
        {
          value: 121594,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#5DE3A4',
              },
            ]),
          },
        },
      ],
    },
  ],
};

export const EnginePowerModeInfoOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '4%',
    top: '0%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    show: true,
    splitLine: { lineStyle: { type: 'dashed' } },
    axisLine: { show: true, lineStyle: { type: 'solid' } },
  },
  yAxis: {
    type: 'category',
    data: ['Standard', 'Power'],
    axisTick: {
      show: false,
    },
    axisLine: { show: false, lineStyle: { type: 'dashed' } },
  },
  series: [
    {
      type: 'bar',
      barWidth: 27,
      label: {
        show: false,
      },
      emphasis: {
        focus: 'series',
      },
      data: [
        {
          value: 19325,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#5DE3DC',
              },
            ]),
          },
        },
        {
          value: 18203,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#FF2D61',
              },
            ]),
          },
        },
      ],
    },
  ],
};

export const MonthlyEngineActiveInfoOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '4%',
    top: '0%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    boundaryGap: [0, 0.01],
    splitLine: { lineStyle: { type: 'dashed' } },
    axisLine: { show: true, lineStyle: { type: 'solid' } },
  },
  yAxis: {
    type: 'category',
    data: [t('IdlingE'), t('TravelingE'), t('WorkingE')],
    axisTick: {
      show: false,
    },
    axisLine: { show: false, lineStyle: { type: 'dashed' } },
  },
  series: [
    {
      type: 'bar',
      barWidth: 27,
      label: {
        show: false,
      },
      emphasis: {
        focus: 'series',
      },
      lineStyle: {
        width: 27,
      },
      data: [
        {
          value: 19325,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#82C2FF',
              },
            ]),
          },
        },
        {
          value: 23438,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#C19DFF',
              },
            ]),
          },
        },
        {
          value: 31000,
          itemStyle: {
            borderRadius,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#5D83E3',
              },
            ]),
          },
        },
      ],
    },
  ],
};

export const MonthlyOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '0%',
    bottom: '3%',
    top: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    axisTick: { show: false },
    splitLine: {
      show: true,
      interval: (index: number) => index !== 0 && index % 5 === 0,
    },
    data: generateDateArray(),
  },
  yAxis: {
    type: 'value',
    // interval: (index: number) => index !== 0 && index % 1 === 0,
    // axisTick: { show: false },
    splitLine: { show: false },
  },
  series: [
    {
      name: t('WorkingE'),
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
      },
      emphasis: {
        focus: 'series',
      },
      barWidth: 20,
      data: [320, 302, 301, 334, 390, 330, 320],
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#5d82e3',
        },
        {
          offset: 1,
          color: '#002554',
        },
      ]),
    },
    {
      name: t('TravelingE'),
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
      },
      barWidth: 20,
      emphasis: {
        focus: 'series',
      },
      data: [120, 132, 101, 134, 90, 230, 210],
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#c19dff',
        },
        {
          offset: 1,
          color: '#002554',
        },
      ]),
    },
    {
      name: t('IdlingE'),
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
      },
      emphasis: {
        focus: 'series',
      },
      data: [220, 182, 191, 234, 290, 330, 310],
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#3ad9ae',
        },
        {
          offset: 1,
          color: '#002554',
        },
      ]),
    },
  ],
};

export const FuelStatusOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '0%',
    bottom: '3%',
    top: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    show: true,
    axisTick: { show: false },
    axisLine: { show: false },
    splitLine: {
      show: false,
      interval: (index: number) => index !== 0 && index % 2 === 0,
    },
    data: generateDateArray(),
  },
  yAxis: {
    type: 'value',
    splitLine: { show: true, lineStyle: { type: 'dashed' } },
  },
  series: [
    {
      barWidth: 4,
      name: t('WorkingE'),
      type: 'bar',
      label: {
        show: true,
      },
      emphasis: {
        focus: 'series',
      },
      itemStyle: {
        borderRadius: [2, 2, 0, 0],
        borderType: 'solid',
      },
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#c2c2c2',
        },
        {
          offset: 1,
          color: '#002554',
        },
      ]),
      data: [320, 302, 301, 334, 390, 330, 320],
    },
  ],
};

export const MonthlyTemperatureOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '0%',
    bottom: '3%',
    top: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    axisTick: { show: false },
    splitLine: {
      show: true,
      interval: (index: number) => index !== 0 && index % 5 === 0,
      lineStyle: { type: 'dashed' },
    },
    data: ['86°F', '140°F', '158°F', '176°F', '194°F', '212°F'],
  },
  yAxis: {
    type: 'value',
    splitLine: { show: false },
  },
  series: [
    {
      name: t('WorkingE'),
      type: 'bar',
      label: {
        show: true,
      },
      emphasis: {
        focus: 'series',
      },
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#C19DFF',
        },
        {
          offset: 1,
          color: '#002554',
        },
      ]),
      data: [320, 302, 301, 334, 390, 330, 320],
      barWidth: 20,
    },
    {
      name: t('TravelingE'),
      type: 'bar',
      label: {
        show: true,
      },
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#4D4D4D',
        },
        {
          offset: 1,
          color: '#B3B3B3',
        },
      ]),
      emphasis: {
        focus: 'series',
      },
      data: [120, 132, 101, 134, 90, 230, 210],
      barWidth: 20,
    },
    {
      name: t('IdlingE'),
      type: 'bar',
      label: {
        show: true,
      },
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#F4BC30',
        },
        {
          offset: 1,
          color: '#F1A076',
        },
      ]),
      emphasis: {
        focus: 'series',
      },
      data: [220, 182, 191, 234, 290, 330, 310],
      barWidth: 20,
    },
  ],
};

export const ExpectedEqChargeOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '4%',
    top: '0%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    show: true,
    splitLine: { lineStyle: { type: 'dashed' } },
    axisLine: { show: true, lineStyle: { type: 'solid' } },
  },
  yAxis: {
    type: 'category',
    data: ['Selected\nMachine', 'Average'],
    axisTick: {
      show: false,
    },
    axisLine: { show: false, lineStyle: { type: 'dashed' } },
  },
  series: [
    {
      // name: 'Direct',
      type: 'bar',
      barWidth: 27,
      label: {
        show: false,
      },
      emphasis: {
        focus: 'series',
      },
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        {
          offset: 0,
          color: '#010542',
        },
        {
          offset: 1,
          color: '#FF5900',
        },
      ]),
      itemStyle: {
        borderRadius,
      },
      data: [
        {
          value: 1,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#002554 ' },
              { offset: 1, color: '#58B4B6' },
            ]),
          },
        },
        {
          value: 2,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              { offset: 0, color: '#010542' },
              { offset: 1, color: '#FF5900' },
            ]),
          },
        },
      ],
      // color: COLORS,
    },
  ],
};

export const FuelConsumptionGraphOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '2%',
    top: '10%',
    bottom: '3%',
  },
  xAxis: {
    type: 'category',
    data: generateDateArray(),
    axisTick: {},
    axisLine: {
      show: true,

      lineStyle: {},
    },
    splitLine: {
      show: true,
      lineStyle: { type: 'dashed' },
      interval: (index: number) => index !== 0 && index % 5 === 0,
    },
  },
  yAxis: {
    type: 'value',
    show: false,

    axisLine: { show: true, lineStyle: { type: 'solid' } },
  },
  series: [
    {
      type: 'bar',
      barWidth: 16,
      label: {
        show: true, // 레이블 표시
        formatter: (params: { value: string }) => `{b|${params.value}}`, // 데이터 값 표시
        rich: {
          a: {
            color: '#fff', // 글자 색상
            backgroundColor: '#5470C6', // 배경 색상 (말풍선 상단)
            padding: [4, 10], // 텍스트 패딩
            borderRadius: 6, // 배경 코너 둥글게
          },
          b: {
            color: '#333', // 추가 텍스트 색
            backgroundColor: '#fff', // 하단 배경
            padding: [2, 8],
            height: 16,
            borderRadius: 3,
            borderWidth: 1, // 테두리 설정
            borderColor: '#aaa',
          },
        },
        position: 'top', // 레이블 위치
      },
      labelLine: {
        show: true, // 말풍선 꼬리 표시
        lineStyle: {
          color: '#5470C6', // 꼬리 색상
          width: 1, // 꼬리 두께
          height: 10,
          type: 'solid', // 실선 모양
        },
      },
      emphasis: {
        focus: 'series',
      },
      color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
        {
          offset: 0,
          color: '#5ADFA6',
        },
        {
          offset: 1,
          color: '#4AD3DD',
        },
      ]),

      data: generateDateArray(),
    },
  ],
};

export const FMEAChartOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'item',
    formatter: '',
  },
  series: [
    {
      top: -80,
      right: -40,
      left: -40,
      color: COLORSBreakdown,
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
      },
      emphasis: {
        label: {
          // show: true,
          fontSize: 40,
          fontWeight: 'bold',
        },
      },
      labelLine: {
        show: false,
      },
      data: [{}],
    },
  ],
};

export const EqOperationRateOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'item',
  },
  legend: {
    bottom: '5%',
    left: 'center',
    icon: 'circle',
    font: '13px',
  },
  series: [
    {
      top: 0,
      right: 0,
      left: 0,
      color: COLORS,

      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center',
      },
      emphasis: {
        label: {
          fontSize: 40,
          fontWeight: 'bold',
        },
      },
      labelLine: {
        show: false,
      },
      data: [{}],
    },
  ],
};

export const WorkTimeRankingOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '4%',
    top: '10%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    show: true,
    splitLine: { lineStyle: { type: 'dashed' } },
    axisLine: { show: true, lineStyle: { type: 'solid' } },
  },
  yAxis: {
    type: 'category',
    data: ['', ''],
    axisTick: {
      show: false,
    },
    axisLine: { show: false, lineStyle: { type: 'dashed' } },
  },
  series: [
    {
      type: 'bar',
      stack: 'total',
      barWidth: 27,
      label: {
        show: false,
      },
      emphasis: {
        focus: 'series',
      },
      data: [
        {
          value: 19325,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#b3b3b3',
              },
              {
                offset: 1,
                color: '#4d4d4d',
              },
            ]),
          },
        },
        {
          value: 18203,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#b3b3b3',
              },
              {
                offset: 1,
                color: '#4d4d4d',
              },
            ]),
          },
        },
      ],
    },
    {
      type: 'bar',
      stack: 'total',
      barWidth: 27,
      label: {
        show: false,
      },
      emphasis: {
        focus: 'series',
      },
      data: [
        {
          value: 19325,
          itemStyle: {
            borderRadius: [0, 10, 10, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#5de3a4',
              },
            ]),
          },
        },
        {
          value: 18203,
          itemStyle: {
            borderRadius: [0, 10, 10, 0],
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#002554',
              },
              {
                offset: 1,
                color: '#5de3a4',
              },
            ]),
          },
        },
      ],
    },
  ],
};

export const OperationTrendOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  title: {},
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    right: '3%',
    data: ['Africa', 'Europe', 'China', 'India', 'Korea'],
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '10%',
    containLabel: true,
  },
  toolbox: {
    feature: {},
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: generateMonthArray(),
    axisTick: { show: false },
  },
  yAxis: {
    type: 'value',
    splitLine: { lineStyle: { type: 'dashed' } },
  },
  series: [
    {
      name: 'Africa',
      type: 'line',
      data: [120, 132, 101, 134, 90, 230, 210],
    },
  ],
};

export const FuelOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  title: {},
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    right: '3%',
    data: ['Fuel', 'AVG'],
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '10%',
    containLabel: true,
  },
  toolbox: {
    feature: {},
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: generateMonthArray(),
    axisTick: { show: false },
  },
  yAxis: {
    type: 'value',
    splitLine: { lineStyle: { type: 'dashed' } },
  },
  series: [
    {
      name: 'Fuel',
      type: 'line',
      data: [120, 132, 101, 134, 90, 230, 210],
    },
    {
      name: 'AVG',
      type: 'line',
      data: [220, 182, 191, 234, 290, 330, 310],
    },
  ],
};

export const FuelConsumptionOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '0%',
    bottom: '3%',
    top: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    axisTick: { show: false },
    splitLine: {
      show: false,
    },
    data: generateMonthArray(),
  },
  yAxis: {
    type: 'value',
    splitLine: { show: true, lineStyle: { type: 'dashed' } },
  },
  series: [
    {
      name: t('WorkingE'),
      type: 'bar',
      stack: 'total',
      label: {
        show: true,
      },
      barWidth: 20,
      emphasis: {
        focus: 'series',
      },
      data: [320, 302, 301, 334, 390, 330, 320],
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#4d4d4d',
        },
        {
          offset: 1,
          color: '#b3b3b3',
        },
      ]),
    },
    {
      name: t('TravelingE'),
      type: 'bar',
      stack: 'total',
      barWidth: 20,
      label: {
        show: true,
      },
      emphasis: {
        focus: 'series',
      },
      data: [120, 132, 101, 134, 90, 230, 210],
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#c19dff',
        },
        {
          offset: 1,
          color: '#002554',
        },
      ]),
    },
  ],
};

export const ModelChargeCountOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '4%',
    containLabel: true,
  },
  xAxis: {
    position: 'top',
    type: 'value',
    show: true,
    splitLine: { lineStyle: { type: 'dashed' } },
    axisLine: { show: true, lineStyle: { type: 'solid' } },
  },
  yAxis: {
    type: 'category',
    data: [
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
      'HXDEMO',
    ],
    axisTick: {
      show: false,
    },
    axisLine: { show: false, lineStyle: { type: 'dashed' } },
  },
  series: [
    {
      type: 'bar',
      barWidth: 27,
      label: {
        show: false,
      },
      emphasis: {
        focus: 'series',
      },
      itemStyle: {
        borderRadius,
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          {
            offset: 0,
            color: '#010542',
          },
          {
            offset: 1,
            color: '#58B4B6',
          },
        ]),
      },
      data: [10, 20, 30, 10, 20, 30, 10, 20, 30, 10, 20, 30, 10, 20, 30],
      // color: COLORS,
    },
  ],
};

export const BatteryConsumptionOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    show: true,
    splitLine: { lineStyle: { type: 'dashed' } },
    axisLine: { show: true, lineStyle: { type: 'solid' } },
  },
  yAxis: {
    type: 'value',
    data: generateMonthArray(),
    axisTick: {
      show: false,
    },
    axisLine: { show: false, lineStyle: { type: 'dashed' } },
  },
  series: [
    {
      type: 'bar',
      barWidth: 27,
      label: {
        show: false,
      },
      emphasis: {
        focus: 'series',
      },
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
          {
            offset: 0,
            color: '#010542',
          },
          {
            offset: 1,
            color: '#58B4B6',
          },
        ]),
      },
      data: [10, 20, 30, 10, 20, 30, 10, 20, 30, 10, 20, 30, 10, 20, 30],
    },
  ],
};

export const ShockOption = {
  textStyle: {
    fontFamily: 'Pretendard',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  grid: {
    left: '70px',
    right: '50px',
    top: '30px',
    bottom: '20px',
  },
  xAxis: {
    data: [{}],
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        width: 0,
      },
    },
    axisLabel: {
      formatter: '{value}일차',
    },
  },
  yAxis: {
    type: 'value',
    splitLine: {
      lineStyle: {
        color: '#cccccc',
        type: 'dashed',
        width: 2,
      },
    },
  },
  series: [
    {
      itemStyle: {
        borderType: 'solid',
      },
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: '#58B4B6',
        },
        {
          offset: 1,
          color: '#FF5900',
        },
      ]),
      barWidth: 16,
      data: [{}],
      type: 'bar',
    },
  ],
};
