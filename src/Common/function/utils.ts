import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Tailwind CSS 클래스를 조건부로 결합하는 유틸리티 함수
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// `도도법 (degree)` -> `도:분:초 표기법 (DMS)`
const toDMS = (coordinate: number) => {
  const absolute = Math.abs(coordinate); // 절대값 (북위/남위, 동경/서경 구분 위해)
  const degrees = Math.floor(absolute); // 도
  const minutes = Math.floor((absolute - degrees) * 60); // 분
  const seconds = ((absolute - degrees - minutes / 60) * 3600).toFixed(2); // 초
  return { degrees, minutes, seconds };
};

// `도도법 (degree)` -> `도:분:초 표기법 (DMS)`
export function convertToDMS({ lat, lng }: { lat: number; lng: number }) {
  const latDMS = toDMS(lat);
  const lngDMS = toDMS(lng);

  // 위도 방향 설정
  const latDirection = lat >= 0 ? 'N' : 'S';
  // 경도 방향 설정
  const lngDirection = lng >= 0 ? 'E' : 'W';
  return {
    latitude: `${latDirection} ${latDMS.degrees}°${latDMS.minutes}'${latDMS.seconds}"`,
    longitude: `${lngDirection} ${lngDMS.degrees}°${lngDMS.minutes}'${lngDMS.seconds}"`,
  };
}

export const valueChangedLiter = (value: number | undefined) => {
  if (value === null || value === undefined || isNaN(value)) {
    return `0 L`;
  }
  const changedValue = (value / 1000).toFixed(1);
  return changedValue + ' L';
};

/**
 * 이메일 유효성 검사
 * @param email
 */
export const isValidEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const calendarBgColor = (colorType?: string) => {
  switch (colorType) {
    case 'BLUE':
      return '#0085FF1A';
    case 'RED':
      return '#FF00001A';
    case 'ORANGE':
      return '#FF8C001A';
    case 'GREEN':
      return '#00FF001A';
    case 'PURPLE':
      return '#8A2BE21A';
    case 'GRAY':
      return '#8080801A';
    default:
      return '#0085FF1A';
  }
};

export const calendarTextColor = (colorType?: string) => {
  switch (colorType) {
    case 'BLUE':
      return '#4c8af7';
    case 'RED':
      return '#E92C2C';
    case 'ORANGE':
      return '#FF9F2D';
    case 'GREEN':
      return '#00BA34';
    case 'PURPLE':
      return '#E278FF';
    case 'GRAY':
      return '#585757';
    default:
      return '#4c8af7';
  }
};
