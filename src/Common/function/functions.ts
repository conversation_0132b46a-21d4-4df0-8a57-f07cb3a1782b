import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import eqtype_forkC from '@/assets/images/fork/forkliftC.svg';
import eqtype_forkR from '@/assets/images/fork/forkliftR.svg';
import eqstat_idle_k from '@/assets/images/badge/idle_k.svg';
import eqstat_idle_e from '@/assets/images/badge/idle_e.svg';
import eqstat_operation_k from '@/assets/images/badge/operation_k.svg';
import eqstat_operation_e from '@/assets/images/badge/operation_e.svg';
import eqstat_fault_k from '@/assets/images/badge/fault_k.svg';
import eqstat_fault_e from '@/assets/images/badge/fault_e.svg';
import eqstat_maint_k from '@/assets/images/badge/maint_k.svg';
import eqstat_maint_e from '@/assets/images/badge/maint_e.svg';
import { EquipmentType } from '@/types/EquipmentType';
import { EqBreakdownStatus, EqOperationStatus } from '@/types';

// 금월의 날짜수 만큼 배열로 작성
const generateDateArray = (yearMonth?: string) => {
  if (yearMonth) {
    return new Array(dayjs(yearMonth).daysInMonth())
      .fill(0)
      .map((_, i) => i + 1);
  } else {
    return new Array(dayjs().daysInMonth()).fill(0).map((_, i) => i + 1);
  }
};

// 금월을 가지고 옴.
const generateMonth = () => {
  return dayjs().month() + 1;
};

// 월들을 가지고 배열로 작성
const generateMonthArray = () => {
  return new Array(12).fill(0).map((_, i) => i + 1);
};

const generateMonthDayArray = (monthDay: number) => {
  return new Array(monthDay).fill(0).map((_, i) => i + 1);
};

// 장비 타입 아이콘 E(굴삭기, excavator), L(휠로더, loader), F(지게차, forklift), S(스키드로더, skidloader)
const getEqTypeIcon = (type?: string) => {
  // E(굴삭기, excavator), L(휠로더, loader), F(지게차, forklift), S(스키드로더, skidloader)
  switch (type) {
    case 'E':
      return eqtype_forkC;
    case 'L':
      return eqtype_forkC;
    case 'F':
      return eqtype_forkC;
    case 'S':
      return eqtype_forkR;
    case 'B':
      return eqtype_forkR;
    default:
      return eqtype_forkR;
  }
};

// 장비 상태 아이콘 운행중, 기타 등등..
const getEqStatIcon = (
  operationStatus?: EqOperationStatus,
  breakdownStatus?: EqBreakdownStatus,
) => {
  const { i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';

  // 고장
  if (breakdownStatus?.breakdown) {
    return isEnglish ? eqstat_fault_e : eqstat_fault_k;
  }
  // 정비 중
  if (breakdownStatus?.repairing) {
    return isEnglish ? eqstat_maint_e : eqstat_maint_k;
  }
  // 유휴
  if (operationStatus?.idle) {
    return isEnglish ? eqstat_idle_e : eqstat_idle_k;
  }
  // 운행 중
  if (operationStatus?.running) {
    return isEnglish ? eqstat_operation_e : eqstat_operation_k;
  }
  // 기본값
  return isEnglish ? eqstat_operation_e : eqstat_operation_k;
};

const getEqStatList = (
  operationStatus?: EqOperationStatus,
  breakdownStatus?: EqBreakdownStatus,
) => {
  const statArray: string[] = [];

  if (operationStatus?.idle) {
    statArray.push('idle');
  }
  if (operationStatus?.running) {
    statArray.push('operation');
  }
  if (breakdownStatus?.breakdown) {
    statArray.push('breakdown');
  }
  if (breakdownStatus?.repairing) {
    statArray.push('repairing');
  }
  if (breakdownStatus?.none) {
    statArray.push('none');
  }
  return statArray;
};

// html 문을 string 문자열로 변경시킨다.
const convertToPlainText = (html?: string) => {
  if (!html) return '';

  // HTML 엔티티를 실제 문자로 변환
  const txt = document.createElement('textarea');
  txt.innerHTML = html;
  const decodedHtml = txt.value;

  // &nbsp; 를 공백으로 치환
  const withSpaces = decodedHtml.replace(/&nbsp;/g, ' ');

  // HTML 태그 제거
  return withSpaces.replace(/<[^>]*>/g, '');
};
// 문자열 날짜를
const formatDate = (dateStr?: string) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return dayjs(date).format('MM-DD').toString();
};

export {
  generateDateArray,
  generateMonth,
  generateMonthArray,
  getEqStatIcon,
  getEqStatList,
  getEqTypeIcon,
  convertToPlainText,
  formatDate,
  generateMonthDayArray,
};
