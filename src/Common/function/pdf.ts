import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export const handleCapture = async (
  captureRef?: React.RefObject<HTMLDivElement>,
  fileName?: string,
) => {
  if (!captureRef) return;
  if (!captureRef.current) return;

  const canvas = await html2canvas(captureRef.current, {
    scale: 2,
    useCORS: true,
    ignoreElements: (element: Element) => {
      if(element.className === 'html2canvas-ignore'){
        return true;
      }
      return false;
    }
  });

  const imgData = canvas.toDataURL('image/png');

  const pdf = new jsPDF('p', 'mm', 'a4');
  const pageWidth = 210; // A4 가로(mm)
  const pageHeight = 297; // A4 세로(mm)
  const imgWidth = pageWidth; // A4 가로 크기에 맞춤
  const imgHeight = (canvas.height * imgWidth) / canvas.width; // 비율 유지

  let y = 0;

  // 📌 이미지 높이가 A4보다 크면 여러 페이지로 나누기
  while (y < imgHeight) {
    pdf.addImage(imgData, 'PNG', 0, -y, imgWidth, imgHeight);
    y += pageHeight; // 다음 페이지로 이동
    if (y < imgHeight) pdf.addPage();
  }

  pdf.save(fileName || 'report.pdf');
};
