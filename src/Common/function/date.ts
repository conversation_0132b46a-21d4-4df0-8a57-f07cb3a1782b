// 날짜 포맷 함수 (YYYY-MM-DD)
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

/**
 * 현재 연도를 YYYY 형식으로 반환합니다.
 * @returns 현재 연도 (예: "2024")
 */
export const getCurrentYear = (): string => {
  return dayjs().format('YYYY');
};

/**
 * 현재 연도와 월을 YYYY-MM 형식으로 반환합니다.
 * @returns 현재 연도와 월 (예: "2024-05")
 */
export const getCurrentYearMonth = (): string => {
  return dayjs().format('YYYY-MM');
};

export const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * YYYYMM 형식의 문자열을 언어에 맞게 변환합니다.
 * 한국어: YY년 M월 (예: 25년 3월)
 * 영어: YY-M (예: 25-3)
 *
 * @param yyyymm YYYYMM 형식의 문자열 (예: 202503)
 * @param locale 언어 코드 ('ko' 또는 'en')
 * @returns 변환된 날짜 문자열
 */
export const formatYearMonth = (
  yyyymm: string | number,
  locale: string = 'ko',
): string => {
  if (!yyyymm) return '';

  // 문자열로 변환
  const yyyymmStr = String(yyyymm);

  // 입력값이 6자리가 아니면 원래 값 반환
  if (yyyymmStr.length !== 6) return String(yyyymm);

  try {
    // YYYYMM에서 연도와 월 추출
    const year = yyyymmStr.substring(0, 4);
    const month = yyyymmStr.substring(4, 6);

    // 날짜 객체 생성 (월은 0부터 시작하므로 1을 빼줌)
    const date = dayjs(`${year}-${month}-01`);

    // 로케일 설정
    date.locale(locale);

    if (locale === 'ko') {
      // 한국어: YY년 M월
      return `${date.format('YY')}년 ${parseInt(month)}월`;
    } else {
      // 영어 및 기타 언어: YY-M
      return `${date.format('YY')}-${parseInt(month)}`;
    }
  } catch (error) {
    console.error('Date formatting error:', error);
    return String(yyyymm);
  }
};
/**
 * 초를 시간 변환하여 포맷팅된 문자열로 반환합니다.
 * @param seconds 변환할 초 단위 시간
 * @returns 시간과 분으로 포맷팅된 문자열 (예: "427")
 */
export const formatSecondsToHours = (seconds: number): string => {
  if (!seconds || seconds < 0) return '0';

  const duration = dayjs.duration(seconds, 'seconds');
  const hours = Math.floor(duration.asHours());

  return `${hours}`;
};

/**
 * 초를 시간과 분으로 변환하여 포맷팅된 문자열로 반환합니다.
 * @param seconds 변환할 초 단위 시간
 * @returns 시간과 분으로 포맷팅된 문자열 (예: "427시간 40분")
 */
export const formatSecondsToHoursAndMinutes = (seconds: number): string => {
  if (!seconds || seconds < 0) return '0시간 0분';

  const duration = dayjs.duration(seconds, 'seconds');
  const hours = Math.floor(duration.asHours());
  const minutes = duration.minutes();

  return `${hours}시간 ${minutes}분`;
};

/**
 * 초를 시간, 분, 초로 변환하여 포맷팅된 문자열로 반환합니다.
 * @param seconds 변환할 초 단위 시간
 * @returns 시간, 분, 초로 포맷팅된 문자열 (예: "427시간 40분 47초")
 */
export const formatSecondsToHoursMinutesSeconds = (seconds: number): string => {
  if (!seconds || seconds < 0) return '0시간 0분 0초';

  const duration = dayjs.duration(seconds, 'seconds');
  const hours = Math.floor(duration.asHours());
  const minutes = duration.minutes();
  const secs = duration.seconds();

  return `${hours}시간 ${minutes}분 ${secs}초`;
};

/**
 * i18n과 함께 사용하기 위한 초를 시간과 분으로 변환 함수
 * @param seconds 변환할 초 단위 시간
 * @param t 번역 함수
 * @returns 시간과 분으로 포맷팅된 문자열
 */
export const formatSecondsToHoursAndMinutesI18n = (
  seconds: number,
  t: (key: string) => string,
): string => {
  if (!seconds || seconds < 0) return `0${t('Hours')} 0${t('Minutes')}`;

  const duration = dayjs.duration(seconds, 'seconds');
  const hours = Math.floor(duration.asHours());
  const minutes = duration.minutes();

  return `${hours}${t('Hours')} ${minutes}${t('Minutes')}`;
};

// 특정 년월의 마지막 날짜 구하기
export const getLastDayOfMonth = (yearMonth: string) => {
  return dayjs(yearMonth).endOf('month').date();
};

//시간을 십진수로 변환 (예: 1:30 -> 1.5)
export const timeToDecimal = (timeStr: string): number => {
  const [hours, minutes] = timeStr.split(' ').map((str) => parseInt(str)); // 시간(h)와 분(m)을 추출
  return Math.round((hours + minutes / 60) * 100) / 100; // 시간 + (분 / 60) 로 계산
};

//십진수를 시간으로 변환 (예: 1.5 -> 1{hUnit} 30{mUnit})
export const decimalToTime = (
  decimal: number,
  hUnit: string,
  mUnit: string,
): string => {
  const hours = Math.floor(decimal); // 시간 부분
  const minutes = Math.round((decimal - hours) * 60); // 분 부분 (소수점 부분을 60으로 곱함)
  return `${hours}${hUnit} ${minutes}${mUnit}`;
};

/**
 * 영어 월 이름을 숫자 문자열로 변환합니다.
 * 전체 월 이름(January)과 축약형(Jan) 모두 지원합니다.
 *
 * @param monthName 변환할 월 이름 (예: "Jan", "January", "Feb" 등)
 * @returns 해당 월의 숫자 문자열 (예: "1", "2" 등), 일치하는 월이 없으면 원래 입력값 반환
 */
export const convertMonthNameToNumber = (monthName: string): string => {
  const monthMap: Record<string, string> = {
    // 축약형
    Jan: '1',
    Feb: '2',
    Mar: '3',
    Apr: '4',
    May: '5',
    Jun: '6',
    Jul: '7',
    Aug: '8',
    Sep: '9',
    Oct: '10',
    Nov: '11',
    Dec: '12',
  };

  // 대소문자 구분 없이 처리하기 위해 입력값을 정규화
  const normalizedInput = monthName.trim();

  // 정확히 일치하는 경우 먼저 확인
  if (monthMap[normalizedInput]) {
    return monthMap[normalizedInput];
  }

  // 대소문자 구분 없이 확인
  const lowerInput = normalizedInput.toLowerCase();
  for (const [key, value] of Object.entries(monthMap)) {
    if (key.toLowerCase() === lowerInput) {
      return value;
    }
  }

  // 부분 일치 확인 (예: "Janu"가 입력되면 "January"로 인식)
  for (const [key, value] of Object.entries(monthMap)) {
    if (
      key.toLowerCase().startsWith(lowerInput) ||
      lowerInput.startsWith(key.toLowerCase())
    ) {
      return value;
    }
  }

  // 일치하는 월이 없으면 원래 입력값 반환
  return monthName;
};

/**
 * 초를 시간 분으로 환산하는 기능
 * @param {number} seconds - The total seconds to convert
 * @returns {string} Formatted string in "X hr Y min" format
 */
export const secondsToHoursMinutes = (
  seconds: number | undefined,
  t: (key: string) => string,
): string => {
  if (seconds === null || seconds === undefined || isNaN(seconds)) {
    return `0 ${t('Hours')} 0 ${t('Minutes')}`;
  }

  // Convert to total hours (including decimal part)
  const totalHours = seconds / 3600;

  // Extract whole hours
  const hours = Math.floor(totalHours);

  // Calculate remaining minutes
  const minutes = Math.round((totalHours - hours) * 60);

  // Handle case where minutes rounds up to 60
  if (minutes === 60) {
    return `${hours + 1} ${t('Hours')} 0 ${t('Minutes')}`;
  }

  return `${hours} ${t('Hours')} ${minutes} ${t('Minutes')}`;
};

/**
 * ISO 8601 형식의 문자열을 생성하는 함수
 * @param date
 * @param time
 */
export const formatISODateTime = (
  date?: string,
  time?: string | number,
): string => {
  if (date && time) return date && time ? `${date}T${String(time)}` : '';
  else return '';
};

// export const parseLocalTime = (timeString: string): LocalTime => {
//   const array: string[] = timeString.split(':');
//
//   return {
//     hour: Number(array[0]),
//     minute: Number(array[1]),
//     second: 0,
//     nano: 0, // dayjs는 밀리초까지만 제공하므로 nano는 0
//   };
// };
