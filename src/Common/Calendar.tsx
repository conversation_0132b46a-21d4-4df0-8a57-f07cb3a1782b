import dayjs from 'dayjs';
import FullCalendar from '@fullcalendar/react';

import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import multiMonthPlugin from '@fullcalendar/multimonth';
import rrulePlugin from '@fullcalendar/rrule';

import {
  DayCellContentArg,
  DayHeaderContentArg,
  EventContentArg,
  EventMountArg,
  EventInput,
  SlotLabelContentArg,
  EventSourceInput,
} from '@fullcalendar/core';
import { cn } from '@/Common/function/utils.ts';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { t } from 'i18next';
import useSchedulePopup from '@/Pages/Calendar/components/useSchedulePopup.tsx';

// CustomCalendarProps
export interface CustomCalendarProps {
  view?: CalendarViewProps;
  date?: Date;
  events?: EventSourceInput;
  clickMonthDay?: (date: string) => void;
  onRefresh?: () => void;
  className?: string;
  visibleRange?: { start: string; end: string };
}

export enum CalendarViewProps {
  DayGridMonth = 'dayGridMonth', // 월간 뷰
  TimeGridWeek = 'timeGridWeek', // 주간 시간 그리드 뷰
  TimeGridDay = 'timeGridDay', // 일간 시간 그리드 뷰
  MultiMonthYear = 'multiMonthYear', // 다중월 연도 뷰
}

// MonthlyCalendar
const MonthlyCalendar = ({
  yearMonth,
  engine,
  className = '',
}: {
  yearMonth: string;
  engine: {
    day: number;
    engRunHour: { hours: number; mins: number };
    workingHour: { hours: number; mins: number };
    travelHour: { hours: number; mins: number };
    idleHour: { hours: number; mins: number };
  }[];
  className?: string;
}) => {
  const { i18n } = useTranslation();
  const format = i18n.language === 'ko' ? 'YYYY년 MM월' : 'MMMM YYYY';
  const [events, setEvents] = useState<EventInput[]>();
  const calendarRef = useRef<FullCalendar>(null);

  useEffect(() => {
    if (!yearMonth) return;
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      setTimeout(() => {
        calendarApi.gotoDate(yearMonth);
      }, 0);
    }
  }, [yearMonth]);

  useEffect(() => {
    if (!yearMonth) return;
    const _events: EventInput[] = [];
    for (let idx = 0; idx < engine.length; ++idx) {
      const item = engine[idx];
      if (item.engRunHour.hours > 0 && item.engRunHour.mins > 0) {
        _events.push({
          title:
            i18n.language === 'ko'
              ? `${item.engRunHour.hours}시간 ${item.engRunHour.mins}분`
              : `${item.engRunHour.hours}hr ${item.engRunHour.mins}min`,
          start: `${yearMonth}-${item.day}`,
          textColor: '#003087',
          backgroundColor: '#0030871a',
          allDay: true,
        });
        if (item.workingHour.hours > 0 && item.workingHour.mins > 0) {
          _events.push({
            title:
              i18n.language === 'ko'
                ? `${item.workingHour.hours}시간 ${item.workingHour.mins}분`
                : `${item.workingHour.hours}hr ${item.workingHour.mins}min`,
            start: `${yearMonth}-${item.day}`,
            textColor: '#60e99f',
            backgroundColor: '#60e99f1a',
            allDay: true,
          });
        }
        if (item.travelHour.hours > 0 && item.travelHour.mins > 0) {
          _events.push({
            title:
              i18n.language === 'ko'
                ? `${item.travelHour.hours}시간 ${item.travelHour.mins}분`
                : `${item.travelHour.hours}hr ${item.travelHour.mins}min`,
            start: `${yearMonth}-${item.day}`,
            textColor: '#002554',
            backgroundColor: '#0025541a',
            allDay: true,
          });
        }
        if (item.idleHour.hours > 0 && item.idleHour.mins > 0) {
          _events.push({
            title:
              i18n.language === 'ko'
                ? `${item.idleHour.hours}시간 ${item.idleHour.mins}분`
                : `${item.idleHour.hours}hr ${item.idleHour.mins}min`,
            start: `${yearMonth}-${item.day}`,
            textColor: '#7300e6',
            backgroundColor: '#7300e61a',
            allDay: true,
          });
        }
      }
    }
    setEvents(_events);
  }, [yearMonth, engine, i18n.language]);

  return (
    <div className={cn('space-y-3', className)}>
      <div className="w-full f-c-c relative">
        <span className="subtitle3">
          {(yearMonth && dayjs(yearMonth).isValid()
            ? dayjs(yearMonth)
            : dayjs()
          ).format(format)}
        </span>
        <div className="f-c gap-3 absolute right-0">
          <div className="f-c gap-2">
            <div className="w-2 h-2 bg-primary-10 rounded-full" />
            <div className="caption3">{t('DrivingTime')}</div>
          </div>
          <div className="f-c gap-2">
            <div className="w-2 h-2 bg-secondary-6 rounded-full" />
            <div className="caption3">{t('IdlingTime')}</div>
          </div>
        </div>
      </div>
      <CustomCalendar
        ref={calendarRef}
        date={yearMonth ? new Date(yearMonth) : new Date()}
        view={CalendarViewProps.DayGridMonth}
        events={events || []}
      />
    </div>
  );
};

// RangeCalendar
const RangeCalendar = ({
  yearMonth,
  engine,
  className = '',
  visibleRange,
}: {
  yearMonth: string;
  engine: {
    day: number;
    engRunHour: { hours: number; mins: number };
    workingHour: { hours: number; mins: number };
    travelHour: { hours: number; mins: number };
    idleHour: { hours: number; mins: number };
  }[];
  className?: string;
  visibleRange?: { start: string; end: string };
}) => {
  const { i18n } = useTranslation();
  const format = i18n.language === 'ko' ? 'YYYY년 MM월' : 'MMMM YYYY';
  const [events, setEvents] = useState<EventInput[]>();
  const calendarRef = useRef<FullCalendar>(null);

  useEffect(() => {
    if (!yearMonth) return;
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      setTimeout(() => {
        calendarApi.gotoDate(yearMonth);
      }, 0);
    }
  }, [yearMonth]);

  useEffect(() => {
    if (!yearMonth) return;
    const _events: EventInput[] = [];
    for (let idx = 0; idx < engine.length; ++idx) {
      const item = engine[idx];
      if (item.engRunHour.hours > 0 && item.engRunHour.mins > 0) {
        _events.push({
          title:
            i18n.language === 'ko'
              ? `${item.engRunHour.hours}시간 ${item.engRunHour.mins}분`
              : `${item.engRunHour.hours}hr ${item.engRunHour.mins}min`,
          start: `${yearMonth}-${item.day}`,
          textColor: '#003087',
          backgroundColor: '#0030871a',
          allDay: true,
        });
        if (item.workingHour.hours > 0 && item.workingHour.mins > 0) {
          _events.push({
            title:
              i18n.language === 'ko'
                ? `${item.workingHour.hours}시간 ${item.workingHour.mins}분`
                : `${item.workingHour.hours}hr ${item.workingHour.mins}min`,
            start: `${yearMonth}-${item.day}`,
            textColor: '#60e99f',
            backgroundColor: '#60e99f1a',
            allDay: true,
          });
        }
        if (item.travelHour.hours > 0 && item.travelHour.mins > 0) {
          _events.push({
            title:
              i18n.language === 'ko'
                ? `${item.travelHour.hours}시간 ${item.travelHour.mins}분`
                : `${item.travelHour.hours}hr ${item.travelHour.mins}min`,
            start: `${yearMonth}-${item.day}`,
            textColor: '#002554',
            backgroundColor: '#0025541a',
            allDay: true,
          });
        }
        if (item.idleHour.hours > 0 && item.idleHour.mins > 0) {
          _events.push({
            title:
              i18n.language === 'ko'
                ? `${item.idleHour.hours}시간 ${item.idleHour.mins}분`
                : `${item.idleHour.hours}hr ${item.idleHour.mins}min`,
            start: `${yearMonth}-${item.day}`,
            textColor: '#7300e6',
            backgroundColor: '#7300e61a',
            allDay: true,
          });
        }
      }
    }
    setEvents(_events);
  }, [yearMonth, engine, i18n.language]);

  return (
    <div className={cn('space-y-3', className)}>
      <div className="w-full f-c-c relative">
        <span className="subtitle3">
          {(yearMonth && dayjs(yearMonth).isValid()
            ? dayjs(yearMonth)
            : dayjs()
          ).format(format)}
        </span>
        <div className="f-c gap-3 absolute right-0">
          <div className="f-c gap-2">
            <div className="w-2 h-2 bg-primary-10 rounded-full" />
            <div className="caption3">{t('DrivingTime')}</div>
          </div>
          <div className="f-c gap-2">
            <div className="w-2 h-2 bg-secondary-6 rounded-full" />
            <div className="caption3">{t('IdlingTime')}</div>
          </div>
        </div>
      </div>
      <CustomCalendar
        ref={calendarRef}
        date={yearMonth ? new Date(yearMonth) : new Date()}
        view={CalendarViewProps.DayGridMonth}
        events={events || []}
        visibleRange={visibleRange}
      />
    </div>
  );
};

// CustomCalendar
const CustomCalendar = forwardRef<FullCalendar, CustomCalendarProps>(
  (
    {
      view = CalendarViewProps.TimeGridDay,
      date = new Date(),
      events = [],
      clickMonthDay,
      onRefresh,
      className = '',
      visibleRange,
    },
    ref,
  ) => {
    const { t } = useTranslation();

    const dayHeaderClassNames = '';
    const dayHeaderContent = (headerArg: DayHeaderContentArg) => {
      let className = 'w-full h-full p-3 subtitle4';
      if (view === CalendarViewProps.MultiMonthYear) {
        className = '';
      }
      const text =
        view === CalendarViewProps.DayGridMonth ||
        view === CalendarViewProps.MultiMonthYear ? (
          dayjs(headerArg.date).format('ddd').toLocaleUpperCase()
        ) : (
          <>
            {dayjs(headerArg.date).format('ddd').toUpperCase()}
            <br />
            {headerArg.date.getDate()}
          </>
        );
      return <div className={className}>{text}</div>;
    };

    const dayCellContent = (cellArg: DayCellContentArg) => {
      let className = 'body4';
      if (view === CalendarViewProps.MultiMonthYear) {
        className = '';
      }
      return <div className={className}>{cellArg.dayNumberText}</div>;
    };

    const eventClassNames = (eventArg: EventContentArg) => {
      const bg = '!bg-[' + eventArg.event.backgroundColor + ']';
      const p = view === CalendarViewProps.DayGridMonth ? '' : '';
      return cn('', p, bg, '');
    };

    const eventDidMount = (eventArg: EventMountArg) => {
      const el = eventArg.el as HTMLElement;
      if (el) {
        el.style.backgroundColor = eventArg.event.backgroundColor || '';
        el.style.borderRadius = '4px';
        el.style.border = 'none';
      }
    };

    const { openScheduleInfoPopup } = useSchedulePopup();
    const eventContent = (eventArg: EventContentArg) => {
      const classes = cn('');
      const barClasses = cn('');
      const Bar =
        view !== CalendarViewProps.DayGridMonth ? (
          <div
            className={barClasses}
            style={{ background: eventArg.event.textColor }}
          />
        ) : null;
      return (
        <div
          className={''}
          onClick={(e) => {
            e.stopPropagation();
            openScheduleInfoPopup(eventArg.event._def.publicId, () => {
              if (onRefresh) {
                onRefresh();
              }
            });
          }}
        >
          {Bar}
          <div className={classes}>
            {!eventArg.event.allDay &&
              view !== CalendarViewProps.DayGridMonth && (
                <div
                  style={{
                    color: eventArg.event.textColor,
                  }}
                >
                  {dayjs(eventArg.event.start).format('HH:mm')}
                </div>
              )}
            <div
              style={{
                color: eventArg.event.textColor,
              }}
            >
              {eventArg.event.title}
            </div>
          </div>
        </div>
      );
    };

    const slotLabelContent = (slotArg: SlotLabelContentArg) => {
      const allDay = slotArg.text === t('AllDay');
      const height = allDay ? '' : 'h-28';
      const classes = cn('', height);
      return (
        <div className={classes}>
          {allDay ? t('AllDay') : dayjs(slotArg.date).format('HH:mm')}
        </div>
      );
    };

    const slotLaneClassNames = () => {
      return 'p-2 ';
    };

    return (
      <div className={className}>
        <FullCalendar
          key={date.toDateString()}
          ref={ref}
          height="auto"
          locale={'en'}
          plugins={[
            dayGridPlugin,
            timeGridPlugin,
            multiMonthPlugin,
            rrulePlugin,
          ]}
          initialDate={date}
          initialView={view}
          visibleRange={visibleRange}
          headerToolbar={false}
          footerToolbar={false}
          slotLaneClassNames={slotLaneClassNames}
          slotLabelContent={slotLabelContent}
          allDayContent={slotLabelContent}
          dayHeaderClassNames={dayHeaderClassNames}
          dayHeaderContent={dayHeaderContent}
          dayCellContent={dayCellContent}
          dayMaxEvents={2}
          eventMaxStack={2}
          slotMinTime={'00:00:00'}
          slotMaxTime={'24:00:00'}
          slotDuration={'01:00:00'}
          timeZone={'local'}
          allDayText={t('AllDay')}
          events={
            view === CalendarViewProps.MultiMonthYear
              ? events || []
              : events || []
          }
          eventClassNames={eventClassNames}
          eventContent={eventContent}
          eventDidMount={eventDidMount}
          dayCellDidMount={(args) => {
            args.el.style.cursor = 'pointer';
            args.el.addEventListener('click', () => {
              if (clickMonthDay) {
                clickMonthDay(dayjs(args.date).format('YYYY-MM-DD'));
              }
            });
          }}
          moreLinkClick={(e) => {
            e.jsEvent.stopPropagation();
            e.jsEvent.preventDefault();
          }}
        />
      </div>
    );
  },
);
CustomCalendar.displayName = 'CustomCalendar';

export { MonthlyCalendar, RangeCalendar, CustomCalendar };
