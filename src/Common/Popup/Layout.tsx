import React from 'react';
import { ReactNode } from 'react';
import { cn } from '@/Common/function/utils.ts';
type Props = {
  children: ReactNode;
  isOpen: boolean;
  onClick?: () => void;
  className?: string;
  zIndex?: number;
};
const Layout = ({
  className,
  children,
  isOpen,
  onClick,
  zIndex = 30,
}: Props) => {
  return (
    isOpen && (
      <React.Fragment>
        <div
          style={{ zIndex: zIndex }}
          onClick={onClick}
          className={cn(
            'absolute z-30 top-0 left-0 w-full h-full flex justify-center items-center tracking-normal',
            className,
          )}
        >
          {children}
        </div>
        <div
          style={{ zIndex: zIndex - 1 }}
          className={'absolute bg-black/80 w-full h-full z-20 top-0 left-0'}
        ></div>
      </React.Fragment>
    )
  );
};

export default Layout;
