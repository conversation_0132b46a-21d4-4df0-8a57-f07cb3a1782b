import Layout from './Layout';
import { AlertPopupProps } from '@/types';

const NoButtonPopup = ({ isOpen, title, text, onClose }: AlertPopupProps) => {
  return (
    <Layout isOpen={isOpen} onClick={onClose}>
      <div className="max-h-[356px] bg-white rounded flex-col justify-start items-start gap-[30px] inline-flex">
        <div className="pl-[20.50px] pt-[46px] flex-col justify-start items-start gap-5 inline-flex">
          {title && (
            <div className="w-[296px] text-center text-[22px] font-bold leading-[30.80px]">
              {title}
            </div>
          )}
          <div className="w-[296px] max-h-[72px] overflow-auto text-center text-[#646363] text-base font-semibold leading-normal">
            {text}
          </div>
        </div>
        <div className="w-[336px] px-5 pb-5 justify-center items-center inline-flex"></div>
      </div>
    </Layout>
  );
};

export default NoButtonPopup;
