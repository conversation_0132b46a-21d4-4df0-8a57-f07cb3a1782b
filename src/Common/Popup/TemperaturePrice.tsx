import { useTranslation } from 'react-i18next';
import { Cross1Icon } from '@radix-ui/react-icons';
import Layout from './Layout';
import { Button } from '@/Common/Components/common/Button';
import { AlertPopupProps } from '@/types';
import Input from '@/Common/Components/common/Input';
import Dropdown from '@/Common/Components/common/DropDown';

const TemperaturePrice = ({
  isOpen,
  title,
  onClose,
  onConfirm,
}: AlertPopupProps) => {
  const { t, i18n } = useTranslation();
  const isEnglish = i18n.language === 'en';

  const options = [
    { key: t('°F'), value: 'f' },
    { key: t('°C'), value: 'c' },
  ];

  return (
    <Layout isOpen={isOpen}>
      <section className="w-[600px] popup-wrap">
        <article>
          <h2> {t('TemperatureThresholdSettings')}</h2>
          <Cross1Icon
            onClick={onClose}
            width={24}
            height={24}
            className="cursor-pointer"
          />
        </article>

        <article>
          <div className="mb-7 space-y-4 [&>div]:f-c [&>div]:gap-5 [&_input]:w-[200px]">
            <div>
              <p>{t('TemperatureUnit')}</p>
              <Dropdown
                onChange={() => undefined}
                options={options}
                placeholder={'°F'}
              />
            </div>
            <div>
              <p className="w-[230px]">{t('HighTemperatureThreshold')}</p>
              <Input
                onChange={() => undefined}
                placeholder={t('HighTemperature')}
              />
            </div>
            <div>
              <p className="w-[230px]">{t('LowTemperatureThreshold')}</p>
              <Input
                onChange={() => undefined}
                placeholder={t('LowTemperature')}
              />
            </div>
          </div>

          <div className="f-je gap-[10px]">
            <Button
              variant={'bt_secondary'}
              label={'Cancel'}
              onClick={onClose}
            />
            <Button variant={'bt_primary'} label={'Save'} onClick={onConfirm} />
          </div>
        </article>
      </section>
    </Layout>
  );
};

export default TemperaturePrice;
