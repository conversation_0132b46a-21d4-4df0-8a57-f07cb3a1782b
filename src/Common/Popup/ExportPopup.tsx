import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import Layout from '@/Common/Popup/Layout.tsx';
import close_popup from '@/assets/images/etc/close_popup.png';
import { Button } from '@/Common/Components/common/Button';
import Radio, { RadioOption } from '@/Common/Components/common/Radio';
import { PopupProps } from '@/types';

const ExportPopup = ({ onClose, onConfirm, isOpen }: PopupProps) => {
  const { t } = useTranslation();

  const [selected, setSelected] = useState<string>('0');

  const options: RadioOption[] = [
    { value: '0', label: 'ExcelFileFormatIfYouHaveMSExcel' },
    { value: '1', label: 'ExcelFileFormatIfYouDontHaveMSExcel' },
  ];

  return (
    <Layout isOpen={isOpen}>
      <div className="w-[480px] h-[328px] flex-col justify-start items-start gap-10 inline-flex bg-white">
        <div className="self-stretch h-[204px] flex-col justify-start items-center flex">
          <div className="self-stretch h-[104px] px-10 pt-10 pb-[34px] rounded-lg justify-between items-center flex">
            <div className="self-stretch justify-start items-center gap-2 inline-flex">
              <div className="text-2xl font-bold leading-normal">
                {t('SelectOutputFileType')}
              </div>
            </div>
            <img
              src={close_popup}
              className="w-6 h-6 cursor-pointer"
              onClick={onClose}
            />
          </div>
          <div className="flex-col justify-start items-start flex">
            <Radio
              className="flex-col gap-7"
              options={options}
              value={selected}
              onValueChange={setSelected}
            />
          </div>
        </div>
        <div className="self-stretch h-[84px] px-10 pb-10 flex-col justify-start items-end gap-2.5 flex">
          <div className="self-stretch justify-end items-center gap-3 inline-flex">
            <Button variant={'bt_primary'} label={'Close'} onClick={onClose} />
            <Button variant={'bt_primary'} label={'OKS'} onClick={onConfirm} />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ExportPopup;
