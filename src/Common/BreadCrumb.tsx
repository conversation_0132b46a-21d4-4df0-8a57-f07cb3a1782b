import React from 'react';

const BreadCrumb = ({ title }: { title: string }) => {
  document.title = `${title}, CARTA MOBILITY`;

  return (
    <React.Fragment>
      <div>
        <nav className="w-full">
          <ul className="space-y-2 detached-breadcrumb">
            <li className="text-2xl font-bold text-black dark:text-white">
              {title}
            </li>
          </ul>
        </nav>
      </div>
    </React.Fragment>
  );
};

export default BreadCrumb;
