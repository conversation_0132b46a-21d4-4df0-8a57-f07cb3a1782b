import { GeneralMarker } from '@/logiMaps/react/general/Marker';

export interface EqGroupMarkerProps {
  id: string;
  latlng: {
    lat: number;
    lng: number;
  };
  markers: {
    id: string;
    latlng: {
      lat: number;
      lng: number;
    };
  }[];
  onClick?: (
    id: string,
    latlng: { lat: number; lng: number },
    markers: {
      id: string;
      latlng: {
        lat: number;
        lng: number;
      };
    }[],
  ) => void;
}

const EqGroupMarker = (props: EqGroupMarkerProps) => {
  //버블 단위: 1~9(40) / 10~49(50) / 50~100(60) / 100~499(80) / 500+(100)
  const getCircleSize = (overlapCnt: number) => {
    let circleSize: number; //32 44 52 64 78
    if (overlapCnt <= 9) {
      circleSize = 30;
    } else if (overlapCnt <= 49) {
      circleSize = 40;
    } else if (overlapCnt <= 100) {
      circleSize = 44;
    } else if (overlapCnt <= 499) {
      circleSize = 60;
    } else {
      circleSize = 74;
    }
    return circleSize;
  };

  const handleMarkerClick = () => {
    props.onClick?.(props.id, props.latlng, props.markers);
  };

  const getTextSizeClass = (overlapCnt: number) => {
    if (overlapCnt <= 9) return 'subHead3';
    if (overlapCnt <= 49) return 'subHead3';
    if (overlapCnt <= 100) return 'subHead2';
    if (overlapCnt <= 499) return 'display5';
    return 'display5';
  };

  return (
    <GeneralMarker
      id={props.id}
      position={{ lat: props.latlng.lat, lng: props.latlng.lng }}
      anchorPoint={['50%', '50%']}
      //zIndex={8}
      onClick={() => {
        handleMarkerClick();
      }}
    >
      <div
        style={{
          position: 'relative',
          width: `${getCircleSize(props.markers.length)}px`,
          height: `${getCircleSize(props.markers.length)}px`,
        }}
      >
        <div className="w-full h-full bg-primary-10/70 rounded-full absolute top-0 left-0" />
        <div
          className={`${getTextSizeClass(props.markers.length)} text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 `}
        >
          {props.markers.length}
        </div>
      </div>
    </GeneralMarker>
  );
};

export default EqGroupMarker;
