import { useCallback } from 'react';
import { GeneralMarker } from '@/logiMaps/react/general/Marker';
import delivery_before from '@/assets/images/markers/delivery_before.svg';
import delivery_after from '@/assets/images/markers/delivery_after.svg';

export interface TrackingMarkerProps {
  id: string;
  latlng: {
    lat: number;
    lng: number;
  };
  deliveryStatus?: 'before' | 'after';
  onClick?: (id: string, latlng: { lat: number; lng: number }) => void;
}

const TrackingMarker = (props: TrackingMarkerProps) => {
  const getImage = (deliveryStatus: 'before' | 'after' = 'before') => {
    return deliveryStatus === 'before' ? delivery_before : delivery_after;
  };

  const getAnchorPoint = (): [string, string] | undefined => {
    return ['50%', '38px'];
  };

  const anchorPoint = getAnchorPoint();

  const onMarkerClick = useCallback(() => {
    props.onClick?.(props.id, props.latlng);
  }, []);

  return (
    <GeneralMarker
      id={props.id}
      position={{ lat: props.latlng.lat, lng: props.latlng.lng }}
      anchorPoint={anchorPoint}
      //zIndex={8}
      onClick={() => {
        onMarkerClick();
      }}
    >
      <img src={getImage(props.deliveryStatus)} />
    </GeneralMarker>
  );
};

export default TrackingMarker;
