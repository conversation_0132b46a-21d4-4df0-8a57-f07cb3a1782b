import { useCallback } from 'react';
import { GeneralMarker } from '@/logiMaps/react/general/Marker';
import dispatch_default_pin from '@/assets/images/markers/dispatch_default_pin.svg';
import dispatch_dest_pin from '@/assets/images/markers/dispatch_dest_pin.svg';

export interface DispatchMarkerProps {
  id: string;
  latlng: {
    lat: number;
    lng: number;
  };
  destination?: boolean;
  onClick?: (id: string, latlng: { lat: number; lng: number }) => void;
}

const DispatchMarker = (props: DispatchMarkerProps) => {
  const getImage = (destination: boolean = false) => {
    return destination ? dispatch_dest_pin : dispatch_default_pin;
  };

  const getAnchorPoint = (): [string, string] | undefined => {
    return ['50%', '38px'];
  };

  const anchorPoint = getAnchorPoint();

  const onMarkerClick = useCallback(() => {
    props.onClick?.(props.id, props.latlng);
  }, []);

  return (
    <GeneralMarker
      id={props.id}
      position={{ lat: props.latlng.lat, lng: props.latlng.lng }}
      anchorPoint={anchorPoint}
      //zIndex={8}
      onClick={() => {
        onMarkerClick();
      }}
    >
      <img src={getImage(props.destination)} />
    </GeneralMarker>
  );
};

export default DispatchMarker;
