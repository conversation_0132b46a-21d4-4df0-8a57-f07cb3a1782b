import { useCallback } from 'react';
import { GeneralMarker } from '@/logiMaps/react/general/Marker';
import marker_operation from '@/assets/images/markers/operation_marker.svg';
import marker_idle from '@/assets/images/markers/idle_marker.svg';
import marker_fault from '@/assets/images/markers/fault_martker.svg';
import marker_maint from '@/assets/images/markers/maint_marker.svg';

import marker_operation_info from '@/assets/images/markers/operation_marker_info.svg';
import marker_idle_info from '@/assets/images/markers/idle_marker_info.svg';
import marker_fault_info from '@/assets/images/markers/fault_marker_info.svg';
import marker_maint_info from '@/assets/images/markers/maint_marker_info.svg';
import { EqOperationStatus, EqBreakdownStatus } from '@/types';

export interface EqSingleMarkerProps {
  id: string;
  latlng: {
    lat: number;
    lng: number;
  };
  operationStatus?: EqOperationStatus;
  breakdownStatus?: EqBreakdownStatus;
  markerType?: number;
  onClick?: (id: string, latlng: { lat: number; lng: number }) => void;
}

const EqSingleMarker = (props: EqSingleMarkerProps) => {
  const getImage = (
    markerType?: number,
    operationStatus?: EqOperationStatus,
    breakdownStatus?: EqBreakdownStatus,
  ) => {
    const isInfo = markerType === 2;
    // 고장
    if (breakdownStatus?.breakdown)
      return isInfo ? marker_fault_info : marker_fault;
    // 소모품 교체 필요
    // if (breakdownStatus?.required)
    //   return isInfo ? marker_required_info : marker_required;
    // 정비 중
    if (breakdownStatus?.repairing)
      return isInfo ? marker_maint_info : marker_maint;
    // 유휴
    if (operationStatus?.idle) return isInfo ? marker_idle_info : marker_idle;
    // 운행 중
    if (operationStatus?.running)
      return isInfo ? marker_operation_info : marker_operation;

    return isInfo ? marker_operation_info : marker_operation;
  };

  const getAnchorPoint = (
    markerType?: number,
  ): [string, string] | undefined => {
    if (markerType == 2) {
      return ['50%', '36px'];
    } else {
      return ['50%', '10px'];
    }
  };

  const anchorPoint = getAnchorPoint(props.markerType);

  const onMarkerClick = useCallback(() => {
    props.onClick?.(props.id, props.latlng);
  }, []);

  return (
    <GeneralMarker
      id={props.id}
      position={{ lat: props.latlng.lat, lng: props.latlng.lng }}
      anchorPoint={anchorPoint}
      //zIndex={8}
      onClick={() => {
        onMarkerClick();
      }}
    >
      <img
        src={getImage(
          props.markerType,
          props.operationStatus,
          props.breakdownStatus,
        )}
      />
    </GeneralMarker>
  );
};

export default EqSingleMarker;
