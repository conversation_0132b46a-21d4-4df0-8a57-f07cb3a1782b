import React, { ReactNode } from 'react';
import clsx from 'clsx';

export interface FileCardProps {
  title: string;
  onClick?: () => void;
  // 새로 추가
  icon?: ReactNode;
  src?: string;
  alt?: string;
  className?: string;
}

const FileCard: React.FC<FileCardProps> = ({
  title,
  onClick,
  icon,
  src,
  alt = 'icon',
  className,
}) => {
  return (
    <div
      onClick={onClick}
      className={`
        py-[30px] px-[26px] f-c-c flex-col gap-[10px] border border-gray-6 rounded-10 cursor-pointer transition-colors duration-150
        hover:bg-primary-0
        [&_p]:text-gray-10
        [&_p]:hover:text-gray-15
        [&_path[stroke]]:stroke-gray-10
        [&_path[stroke]]:hover:stroke-gray-15
        [&_path[fill]]:fill-gray-10
        [&_path[fill]]:hover:fill-gray-15
        ${className}
      `}
    >
      <div className="w-7 h-7 f-c-c ">
        {icon ? icon : src ? <img src={src} alt={alt} /> : null}
      </div>
      <p>{title}</p>
    </div>
  );
};

export default FileCard;
