// PasswordRegex.tsx
import { useTranslation } from 'react-i18next';
import Check from '@/assets/images/svg/16/Check';

type PasswordRuleStatus = {
  lengthOk: boolean;
  atLeast3Ok: boolean;
  tripleSeqOk: boolean;
};

const PasswordRegex = ({ status }: { status: PasswordRuleStatus }) => {
  const { t } = useTranslation();

  const color = (ok: boolean) =>
    ok ? '[&_path]:stroke-semantic-1' : '[&_path]:stroke-gray-6';

  return (
    <article className="py-4 px-5 bg-gray-1 border border-gray-6 rounded-md [&_li]:caption3 [&>ul>li]:mb-1">
      <p className="mb-2 subtitle6">{t('PasswordRequirements')}</p>
      <ul>
        {/* 8~12자 */}
        <li className="f-c gap-[6px]">
          <Check className={color(status.lengthOk)} />
          {t('812Characters')}
        </li>

        {/* 3가지 이상 포함 */}
        <ol>
          <li className="f-c gap-[6px]">
            <Check className={color(status.atLeast3Ok)} />
            {t('MustIncludeAtLeast3OfTheFollowing')}
          </li>
          <ul className="ml-[34px]">
            <li>{t('UppercaseLettersAZ')}</li>
            <li>{t('LowercaseLettersaz')}</li>
            <li>{t('Numbers09')}</li>
            <li>{t('SpecialCharacters!@#$%^*()-_=+~')}</li>
          </ul>
        </ol>

        {/* 동일/연속 3자 금지 */}
        <li className="f-c gap-[6px]">
          <Check className={color(status.tripleSeqOk)} />
          {t('CannotContain3IdenticalOrSequentialCharacters')}
        </li>
      </ul>
    </article>
  );
};

export default PasswordRegex;
