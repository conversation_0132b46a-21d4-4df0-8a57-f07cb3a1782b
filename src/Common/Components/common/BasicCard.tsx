import { useTranslation } from 'react-i18next';

interface BasicCardProps {
  className?: string;
  title?: string;
  value: string | number;
  unit?: string;
  subValue?: string | number;
  subUnit?: string;
}

export default function BasicCard({
  className,
  title,
  value,
  unit,
  subValue,
  subUnit,
}: BasicCardProps) {
  const { t } = useTranslation();

  return (
    <div
      className={`${className} w-full py-5 px-6 f-c-b gap-5 bg-white border border-gray-6 rounded-md `}
    >
      <div className="subtitle4">{title ? t(title) : null}</div>
      <div className="f-c gap-1">
        <span className="subtitle1">{value}</span>
        {unit && <span className="body1">{t(unit)}</span>}
        {/* 시간으로 설정 시 "분" */}
        {subValue !== undefined && (
          <>
            <span className="subtitle1">{subValue}</span>
            {subUnit && <span className="body1">{t(subUnit)}</span>}
          </>
        )}
      </div>
    </div>
  );
}
