import { useTranslation } from 'react-i18next';
import { useRef, useState, useEffect } from 'react';
import * as Popover from '@radix-ui/react-popover';
import Input from '@/Common/Components/common/Input';

export type SearchPopoverProps<T> = {
  placeholder: string;
  displayProperty: keyof T; // 추가된 속성
  showCancel?: boolean;
  onSearch: (query: string) => Promise<T[]>;
  onSelect: (value: T | null) => void;
  renderResult: (result: T) => React.ReactNode;
  classNames?: {
    content?: string;
  };
};

function SearchPopover<T>({
  placeholder,
  displayProperty,
  showCancel,
  onSearch,
  onSelect,
  renderResult,
  classNames,
}: SearchPopoverProps<T>) {
  const { t } = useTranslation();

  const [open, setOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  const [value, setValue] = useState('');
  const [results, setResults] = useState<T[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (value.trim()) {
        setIsSearching(true);
        setSearchError(null);
        onSearch(value)
          .then((results) => setResults(results))
          .catch(() => setResults([]))
          .finally(() => setIsSearching(false));
      } else {
        setResults([]);
      }
    }, 500); // 500ms 디바운싱

    return () => clearTimeout(timeoutId);
  }, [value, onSearch]);

  const handleSearchBlur = () => {
    requestAnimationFrame(() => {
      if (
        contentRef.current &&
        contentRef.current.contains(document.activeElement)
      ) {
        return; // Popover 내부 클릭이면 닫지 않음
      }
      setOpen(false);
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setValue(inputValue);
    // 수동 입력 시 선택 상태 초기화
    onSelect(null);
    if (inputValue && !open) {
      setOpen(true);
    }
  };

  return (
    <Popover.Root open={open}>
      <Popover.Trigger asChild>
        <Input
          type="search"
          placeholder={placeholder}
          value={value}
          onChange={handleInputChange}
          showCancel={showCancel}
          onMouseDown={() => setOpen(true)}
          onBlur={handleSearchBlur}
        />
      </Popover.Trigger>
      <Popover.Content
        ref={contentRef}
        align="center"
        className={classNames?.content || ''}
        sideOffset={6}
      >
        {isSearching ? (
          <div className="d-s-t">{t('Searching')}</div>
        ) : searchError ? (
          <div className="d-s-t">{searchError}</div>
        ) : results.length > 0 ? (
          results.map((result, idx) => (
            <div
              key={`${idx}`}
              onClick={() => {
                const displayValue = result[displayProperty];
                setValue(displayValue as string);
                onSelect(result);
                setOpen(false);
              }}
            >
              {renderResult(result)}
            </div>
          ))
        ) : (
          <div className="d-s-t">{t('SearchResultsNotFound')}</div>
        )}
      </Popover.Content>
    </Popover.Root>
  );
}

export default SearchPopover;
