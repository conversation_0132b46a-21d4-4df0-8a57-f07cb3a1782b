import { forwardRef, useState, useEffect } from 'react';
import dropdown from '@/assets/images/etc/search_dropdown.svg';
import { cn } from '@/Common/function/utils.ts';
import { DropdownOption } from '@/types';

const DropDownMulti = forwardRef<
  HTMLButtonElement,
  React.HTMLAttributes<HTMLDivElement> & {
    options: { key: string; value: string }[];
    onChange?: (values: string[]) => void;
    onSelKeys?: (keys: string[]) => void;
    placeholder: string;
    selectedKeys?: string[];
  }
>(({ className, onSelKeys, selectedKeys = [], ...props }, ref) => {
  const [selectedItems, setSelectedItems] = useState<DropdownOption[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (selectedKeys.length > 0) {
      const selectedOptions = props.options.filter((option) =>
        selectedKeys.includes(option.key),
      );
      setSelectedItems(selectedOptions);
    } else {
      setSelectedItems([]);
    }
  }, [selectedKeys, props.options]);

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  const handleSelect = (option: DropdownOption) => {
    let updatedSelectedItems;

    if (selectedItems.some((item) => item.key === option.key)) {
      updatedSelectedItems = selectedItems.filter(
        (item) => item.key !== option.key,
      );
    } else {
      updatedSelectedItems = [...selectedItems, option];
    }

    setSelectedItems(updatedSelectedItems);

    const selectedKeys = updatedSelectedItems.map((item) => item.key);
    if (onSelKeys) onSelKeys(selectedKeys);
    if (props?.onChange) props?.onChange(selectedKeys);
  };

  return (
    <button
      ref={ref}
      type={'button'}
      aria-checked={isOpen ? 'true' : 'false'}
      className={cn(
        'rounded border border-gray-1 group relative',
        'min-w-[160px] h-11 pl-4 pr-3 py-1.5 bg-transparent',
        className,
      )}
      onClick={handleToggle}
    >
      <div className={'justify-between items-center gap-2 flex'}>
        {/* 선택된 항목 리스트 */}
        <div className="flex flex-wrap gap-1 w-full max-h-7 overflow-y-auto">
          {selectedItems.length > 0 ? (
            selectedItems.map((item) => (
              <span
                key={item.key}
                className="bg-gray-200 text-sm px-2 py-1 rounded-md flex items-center gap-1"
              >
                {item.key}
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSelect(item);
                  }}
                  className="ml-1 text-red-500 text-xs"
                >
                  ✕
                </button>
              </span>
            ))
          ) : (
            <span className="text-gray-500 text-sm">{props.placeholder}</span>
          )}
        </div>

        <div className="w-6 h-6 relative overflow-hidden">
          <img
            src={dropdown}
            alt={'dropdown'}
            className={'group-aria-checked:rotate-180 transition duration-300'}
          />
        </div>
      </div>

      {/* 드롭다운 리스트 */}
      {props.options?.length > 0 && isOpen && (
        <div
          className={
            'absolute top-[calc(100%+8px)] left-[90px] translate-x-[-50%] z-10 bg-white rounded-lg shadow-lg border border-gray-300 overflow-hidden max-h-60 overflow-y-auto'
          }
        >
          {props.options.map((option) => {
            const isSelected = selectedItems.some(
              (item) => item.key === option.key,
            );
            return (
              <div
                key={option.key}
                className={`w-full px-3 py-2.5 flex items-center gap-2 cursor-pointer ${
                  isSelected ? 'bg-blue-100' : 'hover:bg-gray-5'
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleSelect(option);
                }}
              >
                <div className="text-gray-700 text-sm">{option.key}</div>
                {isSelected && (
                  <span className="ml-auto text-blue-500">✔</span>
                )}
              </div>
            );
          })}
        </div>
      )}
    </button>
  );
});

DropDownMulti.displayName = 'DropDownMulti';

export default DropDownMulti;
