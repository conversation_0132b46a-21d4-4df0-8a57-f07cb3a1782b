import { ColumnDef } from '@tanstack/react-table';
import { cn } from '@/Common/function/utils.ts';
import React from 'react';

/*
 * 데이터 테이블 파라미터
 * */
export interface CustomTableProps<T> {
  /*
   * 테이블에 보여 줄 데이터
   * */
  data?: T[];
  /*
   * 테이블에 보여 줄 컬럼 데이터
   * */
  columns?: ColumnDef<T>[];
  /*
   * 테이블 로우 클릭 이벤트
   * */
  onClickRow?: (p?: unknown) => void;
  /*
   * 링크 허용 true 시 detail page이동
   * */
  linking?: boolean;
  /*
   * detail page이동 시 페이지 이동 파라미터
   * */
  id?: keyof T;
  // 페이지당, 로우 표현 숫자
  rowsPerPage?: number;
  // 총 수.
  totRows?: number;
}

const CustomTable = React.forwardRef<
  HTMLTableElement,
  React.HTMLAttributes<HTMLTableElement> & { size?: string }
>(({ className, ...props }, ref) => (
  <div style={{ height: '100%', overflowY: 'auto' }}>
    <table
      ref={ref}
      className={cn('w-full border-b border-gray-6 relative', className)}
      {...props}
    />
  </div>
));
CustomTable.displayName = 'Table';

const CustomTableHeader = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <thead
    ref={ref}
    className={cn('[&_tr]:border-b', className)}
    {...props}
    style={{ position: 'sticky', top: 0, zIndex: 10 }}
  />
));
CustomTableHeader.displayName = 'CustomTableHeader';

const CustomTableBody = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tbody
    ref={ref}
    className={cn('[&_tr:last-child]:border-0', className)}
    {...props}
  />
));
CustomTableBody.displayName = 'CustomTableBody';

const CustomTableFooter = React.forwardRef<
  HTMLTableSectionElement,
  React.HTMLAttributes<HTMLTableSectionElement>
>(({ className, ...props }, ref) => (
  <tfoot
    ref={ref}
    className={cn('border-t [&>tr]:last:border-b-0', className)}
    {...props}
  />
));
CustomTableFooter.displayName = 'CustomTableFooter';

const CustomTableRow = React.forwardRef<
  HTMLTableRowElement,
  React.HTMLAttributes<HTMLTableRowElement>
>(({ className, ...props }, ref) => (
  <tr
    ref={ref}
    className={cn(
      'border-b transition-colors data-[state=selected]:bg-gray-10',
      className,
    )}
    {...props}
  />
));
CustomTableRow.displayName = 'CustomTableRow';

const CustomTableHead = React.forwardRef<
  HTMLTableCellElement,
  React.ThHTMLAttributes<HTMLTableCellElement>
  // eslint-disable-next-line react/prop-types
>(({ className, ...props }, ref) => (
  <th ref={ref} className={cn('body4 text-left', className)} {...props} />
));
CustomTableHead.displayName = 'CustomTableHead';

const CustomTableCell = React.forwardRef<
  HTMLTableCellElement,
  React.TdHTMLAttributes<HTMLTableCellElement>
  // eslint-disable-next-line react/prop-types
>(({ className, ...props }, ref) => (
  <td ref={ref} className={cn('caption2 text-left', className)} {...props} />
));
CustomTableCell.displayName = 'TableCell';

const CustomTableCaption = React.forwardRef<
  HTMLTableCaptionElement,
  React.HTMLAttributes<HTMLTableCaptionElement>
>(({ className, ...props }, ref) => (
  <caption ref={ref} className={cn('', className)} {...props} />
));
CustomTableCaption.displayName = 'TableCaption';

export {
  CustomTableCaption,
  CustomTableCell,
  CustomTableBody,
  CustomTableFooter,
  CustomTableHead,
  CustomTableHeader,
  CustomTable,
  CustomTableRow,
};
