import { useTranslation } from 'react-i18next';
import React, { Fragment, useEffect, useState } from 'react';
import { TableOptions } from '@tanstack/react-table';
import {
  ChevronLeftIcon,
  DoubleArrowLeftIcon,
  ChevronRightIcon,
  DoubleArrowRightIcon,
} from '@radix-ui/react-icons';
import {
  ColumnDef,
  ColumnFiltersState,
  FilterFn,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  SortingState,
  getSortedRowModel,
  useReactTable,
  OnChangeFn,
  RowSelectionState,
} from '@tanstack/react-table';
import Checkbox from '@/Common/Components/common/CheckBox';
import { rankItem } from '@tanstack/match-sorter-utils';

interface ManagementTableProps<TData> {
  columns: ColumnDef<TData>[];
  data: TData[];
  tableclassName?: string;
  divclassName?: string;
  thclassName?: string;
  trclassName?: string;
  tableClass?: string;
  tdclassName?: string;
  checkclassName?: string;
  theadclassName?: string;
  tbodyclassName?: string;
  isBordered?: boolean;
  customPageSize?: number;
  isPagination: boolean;
  PaginationClassName?: string;
  SearchPlaceholder?: string;
  isCheckbox?: boolean;
  sortKey?: string;
  sortOrder?: 'asc' | 'desc';
  onSelectionChange?: (selectedRows: TData[]) => void; // 추가: 선택 변경 시 호출될 콜백
  onRowClick?: (clickedRow: TData) => void;
  totalCount?: number;
  currentPage?: number;
  onPageChange?: (page: number) => void;
  maxHeight?: string;
  rowSelection?: Record<string, boolean>; // 추가: 외부에서 rowSelection 상태를 제어할 수 있게 함
  onRowSelectionChange?: (rowSelection: Record<string, boolean>) => void; // 추가: rowSelection 변경 콜백
  showDashForEmpty?: boolean; // 추가: 빈 값에 대시 표시 여부
  expandedRow?: string[];
  expandRow?: (id: string) => void;
}

const CommonTable = <T = unknown,>({
  columns,
  data,
  tableclassName = 'min-w-full border-b border-gray-6',
  divclassName = 'overflow-visible',
  thclassName = 'py-3 px-3 subtitle4 whitespace-pre-line',
  trclassName = '',
  tdclassName = 'max-w-[865px] px-3 body2 text-left',
  checkclassName = 'px-3 w-[45px]',
  theadclassName = 'bg-primary-0',
  tbodyclassName = 'bg-transparent',
  isPagination,
  customPageSize,
  PaginationClassName = 'mt-7 f-c-c',
  isCheckbox,
  sortKey,
  sortOrder,
  onSelectionChange,
  onRowClick,
  totalCount = 0,
  currentPage = 1,
  onPageChange,
  maxHeight,
  rowSelection: externalRowSelection,
  onRowSelectionChange,
  showDashForEmpty = true,
  expandedRow: expandedRowProp,
  expandRow: expandRowProp,
}: ManagementTableProps<T>) => {
  const { t } = useTranslation();

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [internalRowSelection, setInternalRowSelection] = useState<
    Record<string, boolean>
  >({});

  const [sorting, setSorting] = useState<SortingState>([]);

  const totalPages = Math.ceil(totalCount / (customPageSize || 10));

  const rowSelection =
    externalRowSelection !== undefined
      ? externalRowSelection
      : internalRowSelection;

  const handleRowSelectionChange: OnChangeFn<RowSelectionState> = (
    updaterOrValue,
  ) => {
    // updaterOrValue는 함수 또는 값일 수 있음
    const newRowSelection =
      typeof updaterOrValue === 'function'
        ? updaterOrValue(rowSelection)
        : updaterOrValue;

    if (externalRowSelection === undefined) {
      // 내부 상태만 업데이트
      setInternalRowSelection(newRowSelection);
    }

    // 외부 콜백 호출
    onRowSelectionChange?.(newRowSelection);
  };

  const fuzzyFilter: FilterFn<T> = (row, columnId, value, addMeta) => {
    const itemRank = rankItem(row.getValue(columnId), value);
    addMeta({
      itemRank,
    });
    return itemRank.passed;
  };

  const tableOptions: TableOptions<T> = {
    columns,
    data,
    filterFns: {
      fuzzy: fuzzyFilter,
    },
    state: {
      columnFilters,
      globalFilter,
      sorting,
      pagination: {
        pageIndex: currentPage - 1,
        pageSize: customPageSize || 10,
      },
    },
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange: setSorting,
    globalFilterFn: fuzzyFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
    pageCount: totalPages,
  };

  if (isCheckbox) {
    tableOptions.state = { ...tableOptions.state, rowSelection };
    tableOptions.onRowSelectionChange = handleRowSelectionChange;
    tableOptions.enableRowSelection = true;
  }

  const table = useReactTable(tableOptions);

  const handlePageChange = (newPageIndex: number) => {
    if (onPageChange) {
      onPageChange(newPageIndex + 1);
    } else {
      table.setPageIndex(newPageIndex);
    }
  };

  useEffect(() => {
    if (Number(customPageSize)) table.setPageSize(Number(customPageSize));
  }, [customPageSize, table]);

  const {
    getHeaderGroups,
    getRowModel,
    getPageOptions,
    setPageSize,
    getState,
    getCanPreviousPage,
    getCanNextPage,
  } = table;

  useEffect(() => {
    if (Number(customPageSize)) setPageSize(Number(customPageSize));
  }, [customPageSize, setPageSize]);

  // 전달받은 sortKey, sortOrder 값이 변경되면 정렬 상태 업데이트
  useEffect(() => {
    if (sortKey) {
      setSorting([{ id: sortKey, desc: sortOrder === 'desc' }]);
    }
  }, [sortKey, sortOrder]);

  useEffect(() => {
    if (!onSelectionChange) return; // onSelectionChange가 없는 경우 실행하지 않음

    const selectedRows = Object.keys(rowSelection)
      .filter((key) => rowSelection[key])
      .map((key) => {
        const row = table.getRow(key);
        return row?.original;
      })
      .filter(Boolean) as T[];

    onSelectionChange(selectedRows);
  }, [rowSelection]);

  const getVisiblePageNumbers = () => {
    const currentPageIndex = getState().pagination.pageIndex;
    const pageCount = getPageOptions().length;

    // Maximum number of page buttons to show
    const maxVisiblePages = 5;

    let startPage = Math.max(
      0,
      currentPageIndex - Math.floor(maxVisiblePages / 2),
    );
    const endPage = Math.min(pageCount - 1, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(0, endPage - maxVisiblePages + 1);
    }

    const pages = [];

    if (startPage > 0) {
      pages.push({ page: 0, label: '1' });
      if (startPage > 1) {
        pages.push({ page: -1, label: '...' });
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push({ page: i, label: (i + 1).toString() });
    }

    if (endPage < pageCount - 1) {
      if (endPage < pageCount - 2) {
        pages.push({ page: -2, label: '...' });
      }
      pages.push({ page: pageCount - 1, label: pageCount.toString() });
    }

    return pages;
  };

  return (
    <Fragment>
      <div
        className={divclassName}
        style={maxHeight ? { maxHeight, overflowY: 'auto' } : undefined}
      >
        <table className={tableclassName}>
          <thead className={theadclassName}>
            {getHeaderGroups().map((headerGroup, headerGroupIndex) => (
              <tr key={headerGroup.id} className={trclassName}>
                {/* 첫 번째 헤더 행에 체크박스 열 추가 */}
                {headerGroupIndex === 0 && isCheckbox && (
                  <th className={checkclassName}>
                    <div className="flex items-center justify-center">
                      <Checkbox
                        checked={table.getIsAllRowsSelected()}
                        onCheckedChange={(checked) => {
                          const event = {
                            target: { checked: checked as boolean },
                          } as React.ChangeEvent<HTMLInputElement>;
                          table.getToggleAllRowsSelectedHandler()(event);
                        }}
                      />
                    </div>
                  </th>
                )}
                {headerGroup.headers.map((header) => {
                  return (
                    <th
                      key={header.id}
                      colSpan={header.colSpan}
                      {...{
                        className: `${header.column.getCanSort()} ${thclassName}`,
                      }}
                    >
                      <div className="text-left gap-2">
                        {header.isPlaceholder ? null : (
                          <Fragment>
                            {/* Warning (<div> cannot appear as a descendant of <p>) 발생해서 <p> 제거 */}
                            {/* <p> */}
                            {flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                            {/* </p> */}
                          </Fragment>
                        )}
                      </div>
                    </th>
                  );
                })}
              </tr>
            ))}
          </thead>

          <tbody className={tbodyclassName}>
            {getRowModel().rows.length > 0 ? (
              getRowModel().rows.map((row) => (
                <React.Fragment key={row.id}>
                  <tr
                    key={row.id}
                    className={trclassName}
                    onClick={() => {
                      expandRowProp?.((row.original as any).no);
                      onRowClick?.(row.original);
                    }}
                  >
                    {/* 각 행 앞에 체크박스 셀 추가 */}
                    {isCheckbox && (
                      <td className={checkclassName}>
                        <div
                          className="flex items-center justify-center"
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          <Checkbox
                            checked={row.getIsSelected()}
                            onCheckedChange={(checked) => {
                              const event = {
                                target: { checked: checked as boolean },
                              } as React.ChangeEvent<HTMLInputElement>;
                              row.getToggleSelectedHandler()(event);
                            }}
                          />
                        </div>
                      </td>
                    )}
                    {row.getVisibleCells().map((cell) => {
                      // showDashForEmpty 옵션 추가
                      const value = cell.getValue();
                      const rendered = flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      );
                      return (
                        <td
                          key={cell.id}
                          className={`max-w-[865px] px-3 body2 text-left ${tdclassName}`}
                        >
                          {showDashForEmpty
                            ? value === undefined ||
                              value === null ||
                              value === ''
                              ? '-'
                              : rendered
                            : rendered}
                        </td>
                      );
                    })}
                  </tr>
                  {/* FAQ 답변 영역 */}
                  {expandedRowProp?.includes((row.original as any).no) && (
                    <tr className="bg-gray-1">
                      <td colSpan={columns.length + (isCheckbox ? 1 : 0)}>
                        <div className="w-full max-w-[900px] ml-[440px] f-c gap-3">
                          <h3 className="subtitle3">A</h3>
                          <p
                            dangerouslySetInnerHTML={{
                              __html: (row.original as any).answer,
                            }}
                            className="body2"
                          />
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))
            ) : (
              <tr>
                <td
                  colSpan={columns.length + (isCheckbox ? 1 : 0)}
                  className={`${tdclassName} text-center`}
                >
                  {t('NoDataAvailable')}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {isPagination && (
        <div className={PaginationClassName}>
          <ul className="f-c gap-[6px] [&:li]:mx-[3px]">
            <li>
              <button
                className="h-[35px] px-2 f-c-c b-b-r transition"
                onClick={() => handlePageChange(0)}
              >
                <DoubleArrowLeftIcon />
              </button>
            </li>
            <li>
              <button
                className={`h-[35px] px-2 f-c-c b-b-r transition ${
                  !getCanPreviousPage() && 'disabled'
                }`}
                onClick={() => {
                  if (getState().pagination.pageIndex > 0) {
                    handlePageChange(getState().pagination.pageIndex - 1);
                  }
                }}
              >
                <ChevronLeftIcon />
              </button>
            </li>
            {getVisiblePageNumbers().map((pageInfo, key) => (
              <React.Fragment key={key}>
                <li>
                  {pageInfo.page >= 0 ? (
                    <button
                      className={`py-[6px] px-3 f-c-c b-b-r body3 transition hover:bg-primary-8 hover:text-white ${
                        getState().pagination.pageIndex === pageInfo.page &&
                        'bg-primary-10 text-gray-1'
                      }`}
                      onClick={() => handlePageChange(pageInfo.page)}
                    >
                      {pageInfo.label}
                    </button>
                  ) : (
                    <span className="body3 w-[30px] h-8 f-c-c">
                      {pageInfo.label}
                    </span>
                  )}
                </li>
              </React.Fragment>
            ))}
            <li>
              <button
                className={`h-[35px] px-2 f-c-c b-b-r transition ${
                  !getCanNextPage() && ''
                }`}
                onClick={() => {
                  if (
                    getPageOptions().length - 1 >
                    getState().pagination.pageIndex
                  ) {
                    handlePageChange(getState().pagination.pageIndex + 1);
                  }
                }}
              >
                <ChevronRightIcon />
              </button>
            </li>
            <li>
              <button
                className="h-[35px] px-2 f-c-c b-b-r transition"
                onClick={() => handlePageChange(getPageOptions().length - 1)}
              >
                <DoubleArrowRightIcon />
              </button>
            </li>
          </ul>
        </div>
      )}
    </Fragment>
  );
};

export default CommonTable;
