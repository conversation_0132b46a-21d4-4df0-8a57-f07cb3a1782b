import { Dispatch, LegacyRef, SetStateAction, useRef } from 'react';
import icons_graph from '@/assets/images/etc/icons_graph.svg';
import icons_graph_un from '@/assets/images/etc/icons_graph_un.svg';
import bi_table from '@/assets/images/etc/bi_table.svg';
import bi_table_un from '@/assets/images/etc/bi_table_un.svg';

const InfoTypeRadio = ({
  setType,
}: {
  setType: Dispatch<SetStateAction<string>>;
}) => {
  const radioGraphRef = useRef<HTMLInputElement>(null);
  const radioTableRef = useRef<HTMLInputElement>(null);
  return (
    <div role={'radiogroup'} className="py-[3px] px-3 f-c-c w-b-b-r">
      <button
        role={'radio'}
        aria-checked={true}
        className="w-4 h-4 group"
        ref={radioGraphRef as LegacyRef<HTMLButtonElement> | undefined}
        onClick={() => {
          if (radioGraphRef.current && radioTableRef.current) {
            radioGraphRef.current.ariaChecked = 'true';
            radioTableRef.current.ariaChecked = 'false';
            setType('graph');
          }
        }}
      >
        <img src={icons_graph} className={'group-aria-checked:block hidden'} />
        <img
          src={icons_graph_un}
          className={'group-aria-checked:hidden block'}
        />
      </button>

      <div className="divider-v h-6 mx-3"></div>

      <button
        role={'radio'}
        aria-checked={false}
        className="w-4 h-4 group"
        ref={radioTableRef as LegacyRef<HTMLButtonElement> | undefined}
        onClick={() => {
          if (radioGraphRef.current && radioTableRef.current) {
            radioGraphRef.current.ariaChecked = 'false';
            radioTableRef.current.ariaChecked = 'true';
            setType('table');
          }
        }}
      >
        <img src={bi_table} className={'group-aria-checked:block hidden'} />
        <img src={bi_table_un} className={'group-aria-checked:hidden block'} />
      </button>
    </div>
  );
};

export default InfoTypeRadio;
