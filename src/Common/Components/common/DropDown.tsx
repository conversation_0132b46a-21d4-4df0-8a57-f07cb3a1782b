import { forwardRef, useState, useEffect } from 'react';
import { cn } from '@/Common/function/utils.ts';
import { DropdownOption } from '@/types';
import arrow from '@/assets/images/ic/24/arrow_down.svg';

let globalDropdownCloser: (() => void) | null = null;

const DropDown = forwardRef<
  HTMLButtonElement,
  React.HTMLAttributes<HTMLDivElement> & {
    options: {
      key: string;
      value: string;
      icon?: JSX.Element;
    }[];
    onChange?: (value: string | number) => void;
    onSelKey?: (key: string) => void;
    onSelPair?: (key: string, value: string) => void;
    placeholder: string;
    selectedKey?: string;
    size?: 'sm' | 'md' | 'lg' | 'full' | 'no' | 'fit';
    renderOptions?: (args: {
      options: {
        key: string;
        value: string;
        icon?: JSX.Element;
      }[];
      selectedKey?: string;
      onSelect: (option: DropdownOption) => void;
    }) => React.ReactNode;
  }
>(
  (
    {
      className,
      onSelKey,
      onSelPair,
      selectedKey,
      size = 'fit',
      renderOptions,
      ...props
    },
    ref,
  ) => {
    const [displayText, setDisplayText] = useState(props.placeholder);
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
      if (selectedKey) {
        const selectedOption =
          props.options.find((option) => option.key === selectedKey) ??
          props.options.find((option) => option.value === selectedKey);

        if (selectedOption) {
          setDisplayText(selectedOption.key);
        } else {
          setDisplayText(props.placeholder);
        }
      } else {
        setDisplayText(props.placeholder);
      }
    }, [selectedKey, props.options, props.placeholder]);

    const handleChangeToggle = (e: React.MouseEvent) => {
      e.stopPropagation();

      if (!isOpen) {
        setIsOpen(true);
        globalDropdownCloser?.();
        globalDropdownCloser = () => setIsOpen(false);
      } else {
        setIsOpen(false);
        globalDropdownCloser = null;
      }
    };

    const handleChangeSelect = (option: DropdownOption) => {
      setDisplayText(option.key);
      setIsOpen(false);
      globalDropdownCloser = null;
      if (onSelKey) onSelKey(option.key);
      if (onSelPair) onSelPair(option.key, option.value);
      if (props?.onChange) props?.onChange(option.value);
    };

    useEffect(() => {
      if (!isOpen) return;
      const handleOutsideClick = () => {
        setIsOpen(false);
        globalDropdownCloser = null;
      };
      window.addEventListener('click', handleOutsideClick);
      return () => window.removeEventListener('click', handleOutsideClick);
    }, [isOpen]);

    useEffect(() => {
      return () => {
        if (globalDropdownCloser) globalDropdownCloser = null;
      };
    }, []);

    const sizeClass =
      size === 'sm'
        ? 'w-full max-w-[140px] py-[5px] pl-4 b-b-r'
        : size === 'md'
          ? 'w-[200px] py-[9px] pl-4 b-b-r'
          : size === 'lg'
            ? 'w-[300px] py-[9px] pl-4 b-b-r'
            : size === 'full'
              ? 'w-full py-[9px] pl-4 b-b-r'
              : size === 'no'
                ? 'w-fit max-w-none w-auto py-[9px] border-0'
                : 'w-fit min-w-[100px] py-[9px] pl-4 b-b-r';

    return (
      <button
        ref={ref}
        type={'button'}
        aria-checked={isOpen ? 'true' : 'false'}
        className={cn(
          sizeClass,
          'pr-[10px] bg-white/20 group relative',
          className,
        )}
        onClick={handleChangeToggle}
      >
        <div className={'f-c-b gap-2'}>
          <div
            className={cn(
              'w-full text-left truncate',
              size === 'sm' ? 'body4' : 'body2',
            )}
          >
            {displayText}
          </div>
          <img
            src={arrow}
            alt={'dropdown'}
            className={'group-aria-checked:rotate-180 transition duration-300'}
          />
        </div>
        {props.options?.length > 0 && (
          <div
            className={
              'w-auto min-w-full' +
              (isOpen
                ? ' block absolute top-[calc(100%+8px)] left-1/2 translate-x-[-50%] z-[30]'
                : ' hidden')
            }
          >
            {renderOptions ? (
              renderOptions({
                options: props.options,
                selectedKey,
                onSelect: handleChangeSelect,
              })
            ) : (
              <div className="min-w-full max-h-[400px] w-max p-[5px] w-b-b-r overflow-y-auto shadow-custom overflow-hidden">
                {props.options.map((option) => (
                  <div
                    key={option.key}
                    className="w-full py-[10px] px-3 f-s-b bg-white rounded-md text-left hover:bg-primary-0 transition-colors duration-150 cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleChangeSelect(option);
                    }}
                  >
                    <div className="body4">{option.key}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </button>
    );
  },
);

DropDown.displayName = 'DropDown';

export default DropDown;
