import { StatisticsType } from '@/types/StatisticsType';
import { useTranslation } from 'react-i18next';

type RowMode = 'vehicle' | 'driver';

interface RowComponentProps extends StatisticsType.RowProps {
  rowNum: number;
  type?: RowMode;
}

const Row = ({
  rowNum,
  model,
  vehicleNumber,
  manageNumber,
  hitNumber,
  date,
  name,
  type = 'driver',
}: RowComponentProps) => {
  const { t } = useTranslation();

  return (
    <div className="border-b border-gray-6">
      <div className="py-[10px] px-6 f-c-b">
        {type === 'vehicle' ? (
          // 차량용 Row
          <div className="text-divider3 body3">
            <div>{rowNum}</div>
            <div>{model}</div>
            <div>{vehicleNumber}</div>
            <div>{manageNumber}</div>
            <div>
              {hitNumber}
              <em>{t('Times')}</em>
            </div>
          </div>
        ) : (
          // 기사용 Row
          <div className="text-divider3 body3">
            <div>{rowNum}</div>
            <div>{name}</div>
            <div>
              {hitNumber}
              <em>{t('Times')}</em>
            </div>
          </div>
        )}
        {/* 날짜는 양쪽에 공통 */}
        <div className="caption2 text-gray-8">{date}</div>
      </div>
    </div>
  );
};

export default Row;
