import React, { useRef, useState, forwardRef } from 'react';
import * as Checkbox from '@radix-ui/react-checkbox';
import { v4 as uuidv4 } from 'uuid';
import { cn } from '@/Common/function/utils.ts';

interface CheckBoxProps {
  id?: string;
  label?: string;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  className?: string;
  disabled?: boolean;
}

const CheckBox = forwardRef<
  React.ElementRef<typeof Checkbox.Root>,
  CheckBoxProps
>(
  (
    {
      id: propId,
      label,
      checked: propChecked,
      onCheckedChange,
      className,
      disabled = false,
    },
    ref,
  ) => {
    const idRef = useRef(propId ?? uuidv4());
    const id = idRef.current;
    const isControlled = propChecked !== undefined;
    const [innerChecked, setInnerChecked] = useState<boolean>(false);
    const checkedValue = isControlled ? propChecked! : innerChecked;
    const handleChange = (state: boolean | 'indeterminate') => {
      const next = state === true;
      if (!isControlled) {
        setInnerChecked(next);
      }
      onCheckedChange?.(next);
    };

    return (
      <div className={cn('flex items-center', className)}>
        <Checkbox.Root
          ref={ref}
          id={id}
          checked={checkedValue}
          disabled={disabled}
          onCheckedChange={handleChange}
          className={cn(
            'flex-shrink-0 w-5 h-5 flex items-center justify-center bg-white border border-gray-6 rounded transition-colors hover:border-secondary-6',
            'disabled:cursor-not-allowed disabled:opacity-50',
            'data-[state=checked]:bg-secondary-6 data-[state=checked]:border-secondary-6',
          )}
        >
          <Checkbox.Indicator className="flex items-center justify-center text-current">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path
                d="M4 8.5L7 11.5L12 5.5"
                stroke="white"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </Checkbox.Indicator>
        </Checkbox.Root>

        {label && (
          <label
            htmlFor={id}
            className="ml-2 body4 cursor-pointer peer-disabled:opacity-50 peer-disabled:cursor-not-allowed"
          >
            {label}
          </label>
        )}
      </div>
    );
  },
);

CheckBox.displayName = Checkbox.Root.displayName;

export default CheckBox;
