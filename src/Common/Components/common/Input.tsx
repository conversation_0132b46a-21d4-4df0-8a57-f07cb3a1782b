import {
  forwardRef,
  InputHTMLAttributes,
  useEffect,
  useRef,
  useState,
} from 'react';
import { v4 } from 'uuid';
import { DatePickerProps } from 'react-datepicker';
import dayjs, { Dayjs } from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { cn } from '@/Common/function/utils.ts';
import cancel from '@/assets/images/ic/24/input_cancel.svg';
import Delete from '@/assets/images/svg/20/Delete';
import search from '@/assets/images/ic/24/search.svg';
import eye from '@/assets/images/ic/24/eye.svg';
import eye_disable from '@/assets/images/ic/24/eye_hide.svg';

dayjs.extend(duration);

const Input = forwardRef<
  HTMLInputElement,
  InputHTMLAttributes<HTMLInputElement> & {
    reset?: () => void;
    value?: string;
    datePickerProps?: DatePickerProps;
    widthSize?: 'sm' | 'md' | 'lg';
    error?: string;
    success?: string;
    variant?: string;
    showCancel?: boolean;
    onCancel?: () => void;
    suffix?: React.ReactNode;
  }
>(
  (
    {
      reset,
      className,
      error,
      success,
      //onFocus, //onFocus와 onBlur간 무한 루프 발생하여 제거
      onBlur,
      value: propsValue,
      showCancel,
      onCancel,
      widthSize,
      suffix,
      ...props
    },
    ref,
  ) => {
    const id = useRef(v4());
    const [value, setValue] = useState(propsValue || '');
    const [focused, setFocused] = useState(false);
    const [type, setType] = useState(
      props.type === 'date'
        ? 'text'
        : props.type === 'search'
          ? 'text'
          : props.type || '',
    );
    const isHidden = value.length > 0 ? 'block' : 'hidden';
    const eyeStatus = type == 'password' ? eye : eye_disable;
    const padding =
      type === 'password'
        ? isHidden === 'block'
          ? 'pr-16'
          : 'pr-10'
        : type === 'timer'
          ? 'pr-[90px]'
          : isHidden === 'block'
            ? 'pr-10'
            : 'pr-4';

    let sizeStyle = '';
    switch (widthSize) {
      case 'sm':
        sizeStyle = 'w-[150px]';
        break;
      case 'md':
        sizeStyle = 'w-[200px]';
        break;
      case 'lg':
        sizeStyle = 'w-[300px]';
        break;
      default:
        sizeStyle = 'w-full';
    }

    // 타이머 관련
    const intervalRef = useRef<NodeJS.Timeout>();
    const [intervalCount] = useState(dayjs().add(10, 'minutes').unix());
    const [remainingTime, setRemainingTime] = useState('10:00');
    const updateCountdown = (endTime: Dayjs) => {
      const now = dayjs();
      const duration = dayjs.duration(endTime.diff(now));
      if (duration.asMilliseconds() <= 0) {
        clearInterval(intervalRef.current);
        setRemainingTime('00 : 00');
      } else {
        setRemainingTime(
          `${String(duration.minutes()).length === 1 ? `0${duration.minutes()}` : duration.minutes()} : ${String(duration.seconds()).length === 1 ? `0${duration.seconds()}` : duration.seconds()}`,
        );
      }
    };
    const makeTimeStamp = () => {
      const endTime = dayjs.unix(intervalCount);
      intervalRef.current = setInterval(() => updateCountdown(endTime), 1000);
    };
    useEffect(() => {
      if (type === 'timer') {
        makeTimeStamp();
      }
      return () => {
        clearInterval(intervalRef.current);
      };
    }, []);

    // value prop이 바뀔 때 내부 상태도 동기화 (Controlled 지원)
    useEffect(() => {
      if (typeof propsValue === 'string' && propsValue !== value) {
        setValue(propsValue);
      }
    }, [propsValue]);

    // 상태별 input class 계산
    const inputClass = cn(
      'w-full py-[10px] px-4 rounded-md outline-0 body2 relative placeholder:text-gray-7 focus:outline-0 focus:ring-0 focus:border-primary-7',
      padding,
      props.type === 'search' && 'pl-12',
      // --- 상태별 border style ---
      error && value
        ? 'border border-semantic-4'
        : success && value
          ? 'border border-semantic-1'
          : focused
            ? 'border border-primary-7'
            : value
              ? 'border border-gray-6'
              : 'border border-gray-6',
      props.type === 'date' && 'hidden',
    );

    return (
      <div className={cn(sizeStyle, 'relative', className)}>
        {/* search 타입이면 input 앞에 돋보기 표시 */}
        {props.type === 'search' && (
          <img
            src={search}
            className=" w-6 h-6 absolute left-4 top-[23px] -translate-y-1/2 pointer-events-none z-[3]"
            alt="search"
          />
        )}
        <input
          id={id.current}
          ref={ref}
          {...props}
          type={props.type === 'button' ? 'text' : type}
          value={value}
          onChange={(e) => {
            setValue(e.target.value);
            if (props.onChange) props.onChange(e);
          }}
          onMouseDown={(e) => {
            setFocused(true);
            if (props.onMouseDown) props.onMouseDown(e);
          }}
          onBlur={(e) => {
            setFocused(false);
            if (onBlur) onBlur(e);
          }}
          className={inputClass}
        />

        {value && focused ? (
          // 리셋 버튼
          <div
            className="absolute top-[23px] right-[10px] -translate-y-1/2 block"
            onMouseDown={(e) => {
              e.preventDefault();
              if (props.onChange) {
                const event = {
                  ...new Event('input', { bubbles: true }),
                  target: { value: '' },
                } as unknown as React.ChangeEvent<HTMLInputElement>;
                props.onChange(event);
              }
              reset?.();
            }}
          >
            <img src={cancel} className="z-10 cursor-pointer" alt="reset" />
          </div>
        ) : props?.type === 'password' ? (
          // 1. 비밀번호 토글 (reset이 안 보일 때만)
          <div className="absolute top-[23px] right-3 -translate-y-1/2">
            <img
              src={eyeStatus}
              className="cursor-pointer z-10"
              onClick={() => setType(type === 'password' ? 'text' : 'password')}
              alt="toggle password"
            />
          </div>
        ) : showCancel && onCancel ? (
          // 2. 배차 경유지 삭제
          <div
            onClick={() => onCancel()}
            className="absolute top-[23px] right-3 -translate-y-1/2 [&_path]:hover:stroke-gray-7 [&_circle]:hover:stroke-gray-7"
          >
            <Delete className="cursor-pointer" />
          </div>
        ) : props.type === 'timer' ? (
          // 3. 타이머
          <div className="w-max f-c-c caption4 text-secondary-6 absolute top-[23px] right-4 -translate-y-1/2">
            {remainingTime}
          </div>
        ) : suffix ? (
          // 4. suffix
          <div className="body2 absolute top-[23px] right-4 -translate-y-1/2 pointer-events-none select-none">
            {suffix}
          </div>
        ) : null}

        {/* 에러 메시지 */}
        {error && value && (
          <div
            className="mt-1 caption4 text-semantic-4"
            style={{ letterSpacing: 0 }}
          >
            {error}
          </div>
        )}

        {/* 성공 메시지 */}
        {success && (
          <div
            className="mt-1 caption4 text-semantic-1"
            style={{ letterSpacing: 0 }}
          >
            {success}
          </div>
        )}
      </div>
    );
  },
);
Input.displayName = 'Input';

export default Input;
