import * as React from 'react';
import * as SwitchPrimitives from '@radix-ui/react-switch';
import { v4 as uuidv4 } from 'uuid';
import { cn } from '@/Common/function/utils.ts';

interface SwitchProps
  extends React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root> {
  label: string;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
}

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  SwitchProps
>(({ className, label, checked, onCheckedChange, ...props }, ref) => {
  const id = uuidv4();
  return (
    <div className={'flex items-center gap-2'}>
      <SwitchPrimitives.Root
        id={id}
        className={cn(
          'f-c shrink-0 rounded-full bg-gray-8 cursor-pointer transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-secondary-6',
          className,
          'h-8 w-[58px]',
        )}
        checked={checked}
        onCheckedChange={onCheckedChange}
        {...props}
        ref={ref}
      >
        <SwitchPrimitives.Thumb
          className={cn(
            'bg-white ring-0 rounded-full shadow-lg pointer-events-none transition-transform',
            'w-[26px] h-[26px] data-[state=checked]:translate-x-[29px] data-[state=unchecked]:translate-x-[3px]',
          )}
        />
      </SwitchPrimitives.Root>
      {label ? (
        <label
          htmlFor={id}
          className={cn(
            'cursor-pointer peer-disabled:opacity-50 peer-disabled:cursor-not-allowed text-base',
          )}
        >
          {label}
        </label>
      ) : null}
    </div>
  );
});

Switch.displayName = SwitchPrimitives.Root.displayName;

export { Switch };
