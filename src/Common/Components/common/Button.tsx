import { useTranslation } from 'react-i18next';
import * as React from 'react';
import { cva, VariantProps } from 'class-variance-authority';

interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  label: React.ReactNode;
}

const buttonVariants = cva('f-c-c rounded-md transition duration-100', {
  variants: {
    variant: {
      bt_primary:
        'py-[11px] px-4 bg-primary-10 subtitle4 text-white hover:bg-primary-10 hover:text-primary-3',
      bt_primary_sm:
        'py-1 px-5 bg-secondary-6 subtitle5 text-white hover:bg-secondary-7',
      bt_secondary:
        'py-[10px] px-4 bg-gray-5 subtitle4 text-primary-10 hover:bg-gray-7',
      bt_secondary_sm:
        'py-[10px] px-4 bg-gray-5 subtitle4 text-primary-10 hover:bg-secondary-1 data-[selected=true]:bg-secondary-6 data-[selected=true]:text-white',
      bt_tertiary:
        'py-[10px] px-4 bg-secondary-0 border border-secondary-6 subtitle4 text-secondary-6 hover:bg-secondary-2',
      bt_tertiary_sm:
        'py-1 px-5 bg-secondary-0 border border-secondary-6 subtitle5 text-secondary-6 hover:bg-secondary-2',
      bt_tertiary_sm2:
        'py-1 px-5 bg-gray-5 subtitle5 text-primary-10 hover:bg-gray-7',
      disabled: 'opacity-20 pointer-events-none',
    },
  },
  defaultVariants: {
    variant: 'bt_primary',
  },
});

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, disabled, label, ...props }, ref) => {
    const { t } = useTranslation();

    const baseClass = buttonVariants({ className, variant });
    const disabledClass = disabled
      ? ' ' + buttonVariants({ variant: 'disabled' })
      : '';

    return (
      <button
        ref={ref}
        className={baseClass + disabledClass}
        disabled={disabled}
        {...props}
      >
        {typeof label === 'string' ? t(label) : label}
      </button>
    );
  },
);

Button.displayName = 'Button';
