import React from 'react';
import operation from '@/assets/images/badge/operation_e.svg';
import idle from '@/assets/images/badge/idle_e.svg';
import { DriverStatusType } from '@/types';

const iconMap: Record<DriverStatusType, string> = {
  [DriverStatusType.OnDuty]: operation,
  [DriverStatusType.Idle]: idle,
};

interface DriverStatusBadgeProps {
  status: DriverStatusType;
  size?: number;
}

const DriverStatusBadge: React.FC<DriverStatusBadgeProps> = ({
  status,
  size,
}) => {
  const src = iconMap[status];
  return src ? (
    <img
      src={src}
      alt={status}
      width={size}
      height={size}
      className="inline-block"
    />
  ) : null;
};

export default DriverStatusBadge;
