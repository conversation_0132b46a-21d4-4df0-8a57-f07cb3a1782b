import { ButtonHTMLAttributes, useRef } from 'react';
import useAria from '@/Common/Components/hooks/useAria.tsx';

interface CheckBadgeProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  onCheckedChange?: (checked: boolean) => void;
  isChecked?: boolean;
}

const CheckBadge = ({
  isChecked,
  onCheckedChange,
  ...props
}: CheckBadgeProps) => {
  const refButton = useRef<HTMLButtonElement>(null);
  const { useAriaCheckbox } = useAria();

  return (
    <button
      type="button"
      aria-checked={isChecked}
      {...props}
      ref={refButton}
      className="py-[5px] px-3 f-c-c border border-secondary-1 rounded-full transition-colors duration-200 hover:bg-secondary-1 aria-checked:bg-secondary-1"
      onClick={(e) => {
        useAriaCheckbox(e, refButton, props, onCheckedChange);
      }}
    >
      <div className="body5 text-secondary-6">{props.children}</div>
    </button>
  );
};

export default CheckBadge;
