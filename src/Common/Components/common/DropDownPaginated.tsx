import { forwardRef, useState, useEffect, useRef, useCallback } from 'react';
import { cn } from '@/Common/function/utils.ts';
import { DropdownOption } from '@/types';
import arrow from '@/assets/images/ic/24/arrow_down.svg';

let globalDropdownCloser: (() => void) | null = null;

const DropDownPaginated = forwardRef<
  HTMLButtonElement,
  React.HTMLAttributes<HTMLDivElement> & {
    options?: DropdownOption[]; // fallback
    loadOptions?: (page: number) => Promise<DropdownOption[]>;
    onChange?: (value: string | number) => void;
    onSelKey?: (key: string) => void;
    onSelPair?: (key: string, value: string) => void;
    placeholder: string;
    selectedKey?: string;
    size?: 'sm' | 'md' | 'lg' | 'full' | 'no' | 'fit';
    renderOptions?: (args: {
      options: {
        key: string;
        value: string;
        icon?: JSX.Element;
      }[];
      selectedKey?: string;
      onSelect: (option: DropdownOption) => void;
    }) => React.ReactNode;
  }
>(
  (
    {
      className,
      onSelKey,
      onSelPair,
      selectedKey,
      size = 'md',
      options: staticOptions = [],
      loadOptions,
      renderOptions,
      ...props
    },
    ref,
  ) => {
    const [options, setOptions] = useState<DropdownOption[]>([]);
    const [displayText, setDisplayText] = useState(props.placeholder);
    const [isOpen, setIsOpen] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const listRef = useRef<HTMLDivElement>(null);

    // 초기 및 selectedKey 반영
    useEffect(() => {
      if (selectedKey) {
        const selectedOption =
          options.find((option) => option.key === selectedKey) ??
          staticOptions.find((option) => option.value === selectedKey);

        if (selectedOption) {
          setDisplayText(selectedOption.key);
        } else {
          setDisplayText(props.placeholder);
        }
      } else {
        setDisplayText(props.placeholder);
      }
    }, [selectedKey, options, staticOptions, props.placeholder]);

    // 옵션 로딩 (초기)
    useEffect(() => {
      let ignore = false;

      if (loadOptions) {
        loadOptions(pageRef.current).then((fetched) => {
          if (ignore) return;

          if (fetched.length > 0) {
            setOptions((prev) => [...prev, ...fetched]);
            pageRef.current += 1;
            setHasMore(true);
          } else {
            setHasMore(false);
          }
        });
      } else {
        setOptions(staticOptions);
      }

      return () => {
        ignore = true;
      };
    }, []);

    useEffect(() => {
      if (!isOpen) return;
      const handleOutsideClick = () => {
        setIsOpen(false);
        globalDropdownCloser = null;
      };
      window.addEventListener('click', handleOutsideClick);
      return () => window.removeEventListener('click', handleOutsideClick);
    }, [isOpen]);

    // 컴포넌트 언마운트 시 global closer 제거
    useEffect(() => {
      return () => {
        if (globalDropdownCloser) globalDropdownCloser = null;
      };
    }, []);

    const handleChangeToggle = (e: React.MouseEvent) => {
      e.stopPropagation();

      if (!isOpen) {
        setIsOpen(true);
        globalDropdownCloser?.();
        globalDropdownCloser = () => setIsOpen(false);
      } else {
        setIsOpen(false);
        globalDropdownCloser = null;
      }
    };

    const handleChangeSelect = (option: DropdownOption) => {
      if (onSelKey) onSelKey(option.key);
      if (onSelPair) onSelPair(option.key, option.value);
      if (props?.onChange) props?.onChange(option.value);
      setDisplayText(option.key);
      setIsOpen(false);
      globalDropdownCloser = null;
    };

    const pageRef = useRef(0); // 현재 페이지 추적용

    const isLoadingMoreRef = useRef(false);

    const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
      const el = e.currentTarget;
      if (
        isOpen &&
        el.scrollTop + el.clientHeight >= el.scrollHeight - 10 &&
        hasMore &&
        loadOptions &&
        !isLoadingMoreRef.current
      ) {
        isLoadingMoreRef.current = true;
        loadOptions(pageRef.current).then((fetched) => {
          if (fetched.length > 0) {
            setOptions((prev) => [...prev, ...fetched]);
            pageRef.current += 1;
          } else {
            setHasMore(false);
          }

          isLoadingMoreRef.current = false;
        });
      }
    };

    const sizeClass =
      size === 'sm'
        ? 'w-full max-w-[140px] py-[5px] pl-4 b-b-r'
        : size === 'md'
          ? 'w-[200px] py-[9px] pl-4 b-b-r'
          : size === 'lg'
            ? 'w-[300px] py-[9px] pl-4 b-b-r'
            : size === 'full'
              ? 'w-full py-[9px] pl-4 b-b-r'
              : size === 'no'
                ? 'w-fit max-w-none w-auto py-[9px] border-0'
                : 'w-fit min-w-[100px] py-[9px] pl-4 b-b-r';

    return (
      <button
        ref={ref}
        type="button"
        aria-checked={isOpen ? 'true' : 'false'}
        className={cn(
          sizeClass,
          'pr-[10px] bg-white/20 group relative',
          className,
        )}
        onClick={handleChangeToggle}
      >
        <div className="f-c-b gap-2">
          <div
            className={cn(
              'w-full text-left truncate',
              size === 'sm' ? 'body4' : 'body2',
            )}
          >
            {displayText}
          </div>
          <img
            src={arrow}
            alt="dropdown"
            className="group-aria-checked:rotate-180 transition duration-300"
          />
        </div>
        {options.length > 0 && (
          <div
            className={cn(
              'w-auto min-w-full',
              isOpen
                ? 'block absolute top-[calc(100%+8px)] left-1/2 translate-x-[-50%] z-[30]'
                : 'hidden',
            )}
          >
            {renderOptions ? (
              renderOptions({
                options: options,
                selectedKey,
                onSelect: handleChangeSelect,
              })
            ) : (
              <div
                className="min-w-full max-h-[200px] w-max p-[5px] w-b-b-r overflow-y-auto shadow-custom overflow-hidden"
                onScroll={handleScroll}
                ref={listRef}
              >
                {options.map((option) => (
                  <div
                    key={`${option.key}_${option.value}`}
                    className="w-full py-[10px] px-3 bg-white rounded-md text-left hover:bg-primary-0 transition-colors duration-150 cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleChangeSelect(option);
                    }}
                  >
                    {option.icon && (
                      <div className="w-4 h-4">{option.icon}</div>
                    )}
                    <div>
                      <div className="body4">{option.key}</div>
                      {option.subLabel && (
                        <div className="mt-[3px] body6">{option.subLabel}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </button>
    );
  },
);

DropDownPaginated.displayName = 'DropDownPaginated';

export default DropDownPaginated;
