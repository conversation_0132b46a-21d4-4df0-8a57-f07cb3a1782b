import { useTranslation } from 'react-i18next';
import { ChangeEvent, DragEvent, Fragment, useRef, useState } from 'react';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import FileBadge from '@/Common/Components/common/FileBadge.tsx';

interface FileDropDownProps {
  onFilesChange?: (files: File[]) => void;
  existingFileNames?: string[];
  onRemoveExistingFileName?: (fileName: string) => void;
}

const FileDropDown = ({
  onFilesChange,
  existingFileNames = [],
  onRemoveExistingFileName,
}: FileDropDownProps) => {
  const { t } = useTranslation();
  const [files, setFiles] = useState<File[]>([]);
  const ref = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => e.preventDefault();

  const MAX_FILES = 5;

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const droppedFiles = Array.from(e.dataTransfer.files || []);
    const updatedFiles = [...files, ...droppedFiles].slice(0, MAX_FILES);
    setFiles(updatedFiles);
    onFilesChange?.(updatedFiles);
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    const updatedFiles = [...files, ...selectedFiles].slice(0, MAX_FILES);
    setFiles(updatedFiles);
    onFilesChange?.(updatedFiles);
  };

  const handleClick = () => ref.current?.click();

  const handleRemove = (file: File) => {
    const filtered = files.filter((f) => f.name !== file.name);
    setFiles(filtered);
    onFilesChange?.(filtered);
  };

  return (
    <div className="w-full space-y-2 cursor-pointer">
      <div
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        onClick={handleClick}
        className="f-c border border-gray-6 rounded-md"
      >
        <h3 className="w-[110px] px-4 py-[10px] bg-gray-4 body3 text-center">
          {t('AttatchFile')}
        </h3>
        {existingFileNames.length > 0 || files.length > 0 ? (
          <SearchItemContainer className="pl-5">
            {existingFileNames.map((fileName) => (
              <FileBadge
                key={`existing-${fileName}`}
                message={fileName}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onRemoveExistingFileName?.(fileName);
                }}
              />
            ))}
            {files.map((file) => (
              <FileBadge
                key={`new-${file.name}`}
                message={file.name}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleRemove(file);
                }}
              />
            ))}
          </SearchItemContainer>
        ) : (
          <Fragment>
            <div className="ml-5 caption2 text-gray-7">
              {t('UpTo5FilesCanBeUploadedJpgJpegBmpPngGif')}
            </div>
          </Fragment>
        )}
        <input
          ref={ref}
          type="file"
          multiple
          style={{ display: 'none' }}
          onChange={handleChange}
        />
      </div>
    </div>
  );
};

export default FileDropDown;
