import React from 'react';
import { useTranslation } from 'react-i18next';
import * as RadioGroup from '@radix-ui/react-radio-group';
import { cn } from '@/Common/function/utils.ts';

export interface RadioOption {
  value: string;
  label: string;
}

interface RadioProps {
  options: RadioOption[];
  value: string;
  onValueChange: (value: string) => void;
  className?: string;
  disabled?: boolean;
}

const Radio: React.FC<RadioProps> = ({
  options,
  value,
  onValueChange,
  className = '',
  disabled = false,
}) => {
  const { t } = useTranslation();

  return (
    <RadioGroup.Root
      className={cn('w-full flex flex-wrap gap-x-[30px]', className)}
      value={value}
      onValueChange={onValueChange}
      disabled={disabled}
    >
      {options.map((opt) => {
        const id = `radio-${opt.value}`;
        return (
          <div key={opt.value} className="f-c gap-2">
            <RadioGroup.Item
              value={opt.value}
              id={id}
              className={cn(
                'w-5 h-5 flex-shrink-0 f-c-c bg-transparent border-[1px] border-gray-6 rounded-full transition-all hover:border-secondary-6',
                'data-[state=checked]:bg-transparent',
                'data-[state=checked]:border-[5px] data-[state=checked]:border-secondary-6',
                'peer-disabled:cursor-not-allowed peer-disabled:opacity-50',
              )}
            >
              <RadioGroup.Indicator className="f-c-c"></RadioGroup.Indicator>
            </RadioGroup.Item>
            <label
              htmlFor={id}
              className={cn(
                'body2 cursor-pointer peer-disabled:opacity-50 peer-disabled:cursor-not-allowed',
              )}
            >
              {t(opt.label)}
            </label>
          </div>
        );
      })}
    </RadioGroup.Root>
  );
};

export default Radio;
