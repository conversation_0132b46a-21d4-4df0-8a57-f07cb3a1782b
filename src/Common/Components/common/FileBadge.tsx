import { forwardRef, HTMLAttributes } from 'react';
import { cn } from '@/Common/function/utils.ts';
import close from '@/assets/images/ic/24/close.svg';

interface BadgeProps {
  message: React.ReactNode;
}

const FileBadge = forwardRef<
  HTMLDivElement,
  HTMLAttributes<HTMLDivElement> & BadgeProps
>(({ message, ...props }, ref) => {
  return (
    <div
      ref={ref}
      {...props}
      className={cn(
        'w-fit mr-3 py-[2px] pl-4 pr-2 f-c gap-1 bg-secondary-0 border border-secondary-3 rounded-full',
        props.className,
      )}
    >
      <div className="body4 text-gray-12">{message}</div>
      <img src={close} alt={'close'} className="cursor-pointer" />
    </div>
  );
});

FileBadge.displayName = 'FileBadge';

export default FileBadge;
