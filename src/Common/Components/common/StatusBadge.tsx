import React from 'react';
import operation from '@/assets/images/badge/operation_e.svg';
import idle from '@/assets/images/badge/idle_e.svg';
import fault from '@/assets/images/badge/fault_e.svg';
import maint from '@/assets/images/badge/maint_e.svg';
import { BreakdownStatusType, OperationStatusType } from '@/types';

const iconMap1: Record<OperationStatusType, string> = {
  [OperationStatusType.InOperation]: operation,
  [OperationStatusType.Idle]: idle,
};

const iconMap2: Record<BreakdownStatusType, string> = {
  [BreakdownStatusType.Breakdown]: fault,
  [BreakdownStatusType.Repairing]: maint,
  [BreakdownStatusType.None]: '',
};

interface StatusBadgeProps {
  operationStatus: OperationStatusType;
  breakdownStatus: BreakdownStatusType;
  size?: number;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({
  operationStatus,
  breakdownStatus,
  size,
}) => {
  const src =
    breakdownStatus === BreakdownStatusType.None
      ? iconMap1[operationStatus]
      : iconMap2[breakdownStatus];
  return src ? (
    <img
      src={src}
      alt={breakdownStatus}
      width={size}
      height={size}
      className="inline-block"
    />
  ) : null;
};

export default StatusBadge;
