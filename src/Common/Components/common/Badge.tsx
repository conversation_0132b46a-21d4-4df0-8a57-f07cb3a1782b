import { forwardRef, HTMLAttributes } from 'react';
import { cn } from '@/Common/function/utils.ts';

interface BadgeProps {
  message: string;
  messageColor?: string;
}

const Badge = forwardRef<
  HTMLDivElement,
  HTMLAttributes<HTMLDivElement> & BadgeProps
>(({ message, messageColor, ...props }, ref) => {
  return (
    <div
      ref={ref}
      {...props}
      className={cn(
        'w-fit py-[3px] px-4 f-c-c bg-primary-0 rounded-full',
        props.className,
      )}
    >
      <div className={`body3 ${messageColor ?? ''}`}>{message}</div>
    </div>
  );
});

Badge.displayName = 'Badge';

export default Badge;
