import { useTranslation } from 'react-i18next';
import {
  ChangeEvent,
  DragEvent,
  Fragment,
  useRef,
  useState,
  useEffect,
} from 'react';
import SearchItemContainer from '@/Common/Components/layout/SearchItemContainer';
import { Button } from '@/Common/Components/common/Button';
import FileBadge from '@/Common/Components/common/FileBadge.tsx';

interface FileExcelProps {
  onFilesChange?: (files: File[]) => void;
  existingFileNames?: string[];
  onRemoveExistingFileName?: (fileName: string) => void;
  onShowCancelConfirmChange?: (show: boolean) => void;
  onConfirm?: () => void;
}

const FileExcel = ({
  onFilesChange,
  existingFileNames = [],
  onRemoveExistingFileName,
  onShowCancelConfirmChange,
  onConfirm,
}: FileExcelProps) => {
  const { t } = useTranslation();
  const [files, setFiles] = useState<File[]>([]);
  const ref = useRef<HTMLInputElement>(null);

  const [progress, setProgress] = useState<number | null>(null);
  const [uploadingFile, setUploadingFile] = useState<File | null>(null);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  useEffect(() => {
    if (progress === null && uploadingFile === null && files.length > 0) {
      onConfirm?.();
    }
  }, [progress, uploadingFile]);

  useEffect(() => {
    onShowCancelConfirmChange?.(showCancelConfirm);
  }, [showCancelConfirm, onShowCancelConfirmChange]);

  const dragOver = (e: DragEvent<HTMLDivElement>) => e.preventDefault();
  const MAX_FILES = 5;

  const isExcelFile = (file: File) => {
    const name = file.name.toLowerCase();
    return (
      name.endsWith('.xls') ||
      name.endsWith('.xlsx') ||
      file.type === 'application/vnd.ms-excel' ||
      file.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
  };

  const uploadTimer = useRef<NodeJS.Timeout | null>(null);

  const simulateUpload = (file: File) => {
    setUploadingFile(file);
    setProgress(0);

    let p = 0;
    const interval = setInterval(() => {
      p += 10;
      setProgress(p);
      if (p >= 100) {
        clearInterval(interval);
        uploadTimer.current = null;
        setTimeout(() => {
          setProgress(null);
          setUploadingFile(null);
        }, 500);
      }
    }, 250);

    uploadTimer.current = interval;
  };

  const dragDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const droppedFiles = Array.from(e.dataTransfer.files || []).filter(
      isExcelFile,
    );
    const updatedFiles = [...files, ...droppedFiles].slice(0, MAX_FILES);
    setFiles(updatedFiles);
    onFilesChange?.(updatedFiles);

    if (droppedFiles.length > 0) {
      simulateUpload(droppedFiles[0]);
    }
  };

  const fileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []).filter(isExcelFile);
    const updatedFiles = [...files, ...selectedFiles].slice(0, MAX_FILES);
    setFiles(updatedFiles);
    onFilesChange?.(updatedFiles);
    if (ref.current) ref.current.value = '';

    if (selectedFiles.length > 0) {
      simulateUpload(selectedFiles[0]);
    }
  };

  const click = () => ref.current?.click();

  const fileRemove = (file: File) => {
    const filtered = files.filter((f) => f.name !== file.name);
    setFiles(filtered);
    onFilesChange?.(filtered);
  };

  // 업로드 취소
  const cancelUpload = () => {
    if (uploadTimer.current) {
      clearInterval(uploadTimer.current);
      uploadTimer.current = null;
    }
    setProgress(null);
    setUploadingFile(null);
    setFiles([]);
    onFilesChange?.([]);
  };

  // === 업로드 취소 확인 UI ===
  if (showCancelConfirm) {
    return (
      <div>
        <h2 className="mb-10 body1 text-center whitespace-pre">
          {t(
            'TheDriverListUploadHasNotBeenCompletedDoYouWantToCancelTheUpload',
          )}
        </h2>
        <div className="f-c-e gap-[10px]">
          <Button
            variant={'bt_secondary'}
            label={t('Cancel')}
            onClick={() => {
              cancelUpload();
              setShowCancelConfirm(false);
            }}
          />
          <Button
            variant={'bt_primary'}
            label={t('Upload')}
            onClick={() => setShowCancelConfirm(false)}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="w-full ">
      {/* Input */}
      <div
        onClick={!progress ? click : undefined}
        onDrop={!progress ? dragDrop : undefined}
        onDragOver={!progress ? dragOver : undefined}
        style={{ cursor: progress ? 'not-allowed' : 'pointer' }}
        className={
          progress !== null && uploadingFile
            ? 'h-fit f-c-c rounded'
            : 'h-[90px] f-c-c border border-dashed border-gray-8 rounded'
        }
      >
        {progress !== null && uploadingFile ? (
          <h2 className="body1 text-center whitespace-pre">
            {t('UploadingDriverListPleaseWaitAMoment')}
          </h2>
        ) : (
          <>
            {existingFileNames.length > 0 || files.length > 0 ? (
              <SearchItemContainer className="pl-5">
                {existingFileNames.map((fileName) => (
                  <FileBadge
                    key={`existing-${fileName}`}
                    message={fileName}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      onRemoveExistingFileName?.(fileName);
                    }}
                  />
                ))}
                {files.map((file) => (
                  <FileBadge
                    key={`new-${file.name}`}
                    message={file.name}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      fileRemove(file);
                    }}
                  />
                ))}
              </SearchItemContainer>
            ) : (
              <Fragment>
                <div className="ml-5 body3 text-gray-7">
                  {t('DragAndDropAfileOrClick')}
                </div>
              </Fragment>
            )}
            <input
              ref={ref}
              accept=".xls,.xlsx,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              type="file"
              multiple
              onChange={fileChange}
              className="hidden"
              disabled={!!progress}
            />
          </>
        )}
      </div>

      {/* Download Form + 게이지바 영역 */}
      <div className="mt-5 mb-[10px] p-5 f-c-b bg-primary-0-1 rounded">
        {progress !== null && uploadingFile ? (
          <div className="w-full space-y-2">
            <div className="f-c-b caption3 text-gray-12">
              <span>{uploadingFile.name}</span>
              <span>{progress}%</span>
            </div>
            {/* Progress Bar */}
            <div className="w-full h-[5px] bg-secondary-6/15 rounded-full">
              <div
                className="h-[5px] bg-progress rounded-full transition-all"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        ) : (
          <>
            <h3 className="caption3 text-gray-10 whitespace-pre">
              {t(
                'DontHaveTheDriverRegistrationFormClickTheButtonBelowToDownloadIt',
              )}
            </h3>
            <p className="body4 text-secondary-6">{t('DownloadForms')}</p>
          </>
        )}
      </div>

      {/* 버튼 */}
      <div className="f-je">
        <Button
          variant={'bt_tertiary_sm2'}
          label={t('CancelUpload')}
          onClick={
            progress !== null && uploadingFile
              ? () => setShowCancelConfirm(true)
              : undefined
          }
        />
      </div>
    </div>
  );
};

export default FileExcel;
