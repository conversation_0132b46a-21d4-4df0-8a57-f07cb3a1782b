import filter from '@/assets/images/etc/filter.svg';
import { HTMLAttributes } from 'react';
import { cn } from '@/Common/function/utils.ts';

const CustomColumnHeader = (
  p: HTMLAttributes<HTMLSpanElement> & { enableSort?: boolean },
) => {
  return (
    <div
      className={cn(
        'justify-center flex items-center gap-1 text-base font-bold',
        p.className,
      )}
    >
      {p.children}
      {p.enableSort && <img src={filter} alt="Filter Icon" />}
    </div>
  );
};

export default CustomColumnHeader;
