import { useEffect, useState } from 'react';

const Loading = () => {
  const [dotCount, setDotCount] = useState(1);

  useEffect(() => {
    const interval = setInterval(() => {
      setDotCount((prev) => (prev % 3) + 1);
    }, 500);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="w-full h-full f-c-c bg-primary-8/20 absolute z-[60]">
      <div className="subtitle2 text-white">{`loading${'.'.repeat(dotCount)}`}</div>
    </div>
  );
};

export default Loading;
