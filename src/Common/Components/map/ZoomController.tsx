import { forwardRef } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/Common/function/utils.ts';
import plus from '@/assets/images/ic/28/plus.svg';
import minus from '@/assets/images/ic/28/minus.svg';

type ZoomControllerProps = {
  plus: () => void;
  minus: () => void;
  right?: string;
  bottom?: string;
  style?: React.CSSProperties;
  className?: string;
};

const ZoomController = forwardRef<HTMLDivElement, ZoomControllerProps>(
  ({ right = 'left-6', bottom = 'bottom-4', ...props }, ref) => {
    const className = cn(
      `f-c-c flex-col bg-white rounded-lg absolute z-5 [&>button]:p-1 [&>button]:border-b [&>button:last-child]:border-0 [&>button]:border-gray-6`,
      right,
      bottom,
      props?.className,
    );

    return (
      <motion.div
        ref={ref}
        style={props.style}
        className={className}
        initial={{ y: 40, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 40, opacity: 0 }}
        transition={{ duration: 0.4, ease: 'easeInOut' }}
      >
        <button
          onClick={() => {
            props.plus();
          }}
        >
          <img src={plus} alt="plus" />
        </button>
        <button onClick={props.minus}>
          <img src={minus} alt="minus" />
        </button>
      </motion.div>
    );
  },
);
ZoomController.displayName = 'ZoomController';

export default ZoomController;
