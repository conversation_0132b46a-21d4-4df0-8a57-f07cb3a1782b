import { useEffect, useRef } from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { useLayoutStore } from '@/store/layout.ts';
import { cn } from '@/Common/function/utils.ts';
import useAria from '@/Common/Components/hooks/useAria.tsx';
import arrow from '@/assets/images/arrow/arrow_left_m.svg';

type MonitoringViewListButtonProps = {
  className?: string;
  status?: boolean;
};

const MonitoringViewListButton = ({
  className,
  status,
  ...props
}: MonitoringViewListButtonProps & HTMLMotionProps<'button'>) => {
  const ref = useRef<HTMLButtonElement>(null);
  const { useAriaCheckbox } = useAria();
  const { move, moved } = useLayoutStore((state) => state);

  useEffect(() => {
    if (status != null && ref.current != null) {
      moved(status);
      ref.current.ariaChecked = status ? 'false' : 'true';
    }
  }, [status, ref]);

  return (
    <motion.button
      {...props}
      ref={ref}
      aria-checked={'false'}
      className={cn(
        'py-2 bg-secondary-6 rounded-r absolute top-[82px] left-0 group',
        className,
      )}
      onClick={(e) => {
        moved(!move);
        useAriaCheckbox(e, ref, {
          className,
        });
      }}
      initial={{ y: 0, opacity: 1 }}
      animate={{
        x: move ? 360 : 0,
        y: 0,
        opacity: 1,
      }}
      transition={{ duration: 0.4, ease: 'easeInOut' }}
      style={{ left: 0 }}
    >
      <img
        src={arrow}
        alt={'arrow'}
        className={'group-aria-checked:rotate-[-180deg]'}
      />
    </motion.button>
  );
};
export default MonitoringViewListButton;
