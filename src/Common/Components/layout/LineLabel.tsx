import { useTranslation } from 'react-i18next';
import { HTMLAttributes, ReactNode } from 'react';
import { cn } from '@/Common/function/utils.ts';

interface LineLabelProps extends HTMLAttributes<HTMLSpanElement> {
  label?: ReactNode;
  required?: boolean;
}

const LineLabel = ({
  label,
  children,
  className,
  required,
  ...rest
}: LineLabelProps) => {
  const { t } = useTranslation();

  const labelTrans = (value?: ReactNode) => {
    if (typeof value === 'string') {
      return t(value);
    }
    return value;
  };

  return (
    <span className={cn(className, 'flex-shrink-0')} {...rest}>
      {labelTrans(label) ?? labelTrans(children)}
      {required && <em className="ml-1 text-semantic-4">*</em>}
    </span>
  );
};

export default LineLabel;
