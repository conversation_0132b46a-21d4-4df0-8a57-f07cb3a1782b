import { HTMLAttributes } from 'react';
import { cn } from '@/Common/function/utils.ts';

const SearchHtmlLabel = ({
  className,
  children,
  ...rest
}: HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span className={cn('text-xl font-bold', className)} {...rest}>
      {String(children)
        .split('\n')
        .map((line, index) => (
          <span key={index}>
            {line}
            <br />
          </span>
        ))}
    </span>
  );
};

export default SearchHtmlLabel;
