import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { CalendarProps } from 'react-calendar';
import Calendar from 'react-calendar';
import { Popover } from '@radix-ui/themes';
import dayjs from 'dayjs';
import calender from '@/assets/images/ic/24/calender.svg';

interface YearSelectorProps extends CalendarProps {
  onValueChange?: (value: Date | [Date, Date] | null) => void;
  onInit?: (value: Date | [Date, Date] | null) => void;
  initialValue?: Date | [Date, Date] | null;
}

const YearSelector = (props: YearSelectorProps) => {
  const { onValueChange, onInit, initialValue, ...calendarProps } = props;
  type YearSelectorValue = Date | [Date, Date] | null;
  const [value, setValue] = useState<YearSelectorValue>(
    (initialValue as YearSelectorValue) || new Date(),
  );
  const { i18n } = useTranslation();
  const format = i18n.language === 'ko' ? 'YYYY년' : 'YYYY';
  const languageString = i18n.language === 'ko' ? 'ko' : 'en';

  useEffect(() => {
    if (value) {
      onInit?.(value);
    }
  }, []);

  // 초기값이 변경되면 내부 상태도 업데이트
  useEffect(() => {
    if (initialValue) {
      setValue(initialValue);
    }
  }, [initialValue]);

  // 값이 변경될 때 부모 컴포넌트에 알림
  const handleValueChange = (newValue: Date | Date[] | null) => {
    let valueToSend: Date | [Date, Date] | null = null;
    if (Array.isArray(newValue)) {
      if (newValue.length === 2) valueToSend = [newValue[0], newValue[1]];
      else if (newValue.length === 1) valueToSend = newValue[0];
      else valueToSend = null;
    } else {
      valueToSend = newValue;
    }
    setValue(valueToSend);
    if (onValueChange) {
      onValueChange(valueToSend);
    }
  };

  return (
    <Popover.Root>
      <Popover.Trigger>
        <div className="py-[10px] px-3 f-c-b gap-2 bg-white/20 border border-gray-6 rounded-md cursor-pointer">
          <img src={calender} alt={'calendar'} />
          <div className="body2">{dayjs(value?.toString()).format(format)}</div>
        </div>
      </Popover.Trigger>
      <Popover.Content align="center">
        <Calendar
          locale={languageString}
          {...calendarProps}
          onClickYear={(p) => {
            handleValueChange(p);
          }}
          value={value}
          view={'decade'}
        />
      </Popover.Content>
    </Popover.Root>
  );
};

export default YearSelector;
