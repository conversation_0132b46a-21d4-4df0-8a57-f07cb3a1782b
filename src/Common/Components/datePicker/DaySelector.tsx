import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { Popover } from '@radix-ui/themes';
import dayjs from 'dayjs';
import Calendar from 'react-calendar';
import calender from '@/assets/images/ic/24/calender.svg';

type ValuePiece = Date | null;

type Value = ValuePiece | [ValuePiece, ValuePiece];

type DaySelectorSize = 'default' | 'lg';

const DaySelector = ({
  className,
  error,
  initValue,
  onInit,
  onChange,
  size = 'default',
}: {
  className?: string;
  error?: string;
  initValue?: string;
  onInit?: (day: string) => void;
  onChange?: (day: string) => void;
  size?: DaySelectorSize; // 추가
}) => {
  const [value, setValue] = useState<Value>(
    initValue ? new Date(initValue) : new Date(),
  );

  useEffect(() => {
    const newValue = initValue ? new Date(initValue) : new Date();
    setValue(newValue || '10:00');
  }, [initValue]);

  const { i18n } = useTranslation();
  const format = i18n.language === 'ko' ? 'YYYY년 MM월 DD일' : 'YYYY-MM-DD';
  const languageString = i18n.language === 'ko' ? 'ko' : 'en';

  useEffect(() => {
    if (value) {
      onInit?.(value.toString());
    }
  }, []);

  useEffect(() => {
    if (value) {
      onChange?.(value.toString());
    }
  }, [value]);

  // 사이즈별 스타일
  const base =
    'px-3 py-[10px] f-c gap-2 bg-white/20 border border-gray-6 rounded-md cursor-pointer';
  const sizeClass = size === 'lg' ? 'max-w-full w-full' : 'max-w-[256px] w-max';
  const boxClass = `${base} ${sizeClass}`;

  return (
    <Popover.Root>
      <Popover.Trigger>
        <div className={`${className} ${boxClass}`}>
          <img src={calender} alt={'calendar'} />
          <div className="body2">{dayjs(value?.toString()).format(format)}</div>
          {error && <div className="caption3 text-semantic-4">{error}</div>}
        </div>
      </Popover.Trigger>
      <Popover.Content align="center" className="f-c-c">
        <Calendar
          locale={languageString}
          formatDay={(locale, date) => dayjs(date).format('DD')}
          defaultView={'month'}
          onChange={setValue}
          value={value}
          calendarType={'gregory'}
          className={'h-full'}
        />
      </Popover.Content>
    </Popover.Root>
  );
};

export default DaySelector;
