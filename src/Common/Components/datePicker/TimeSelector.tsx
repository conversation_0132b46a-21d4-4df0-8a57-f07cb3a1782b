import { useState, useEffect } from 'react';
import TimePicker from 'react-time-picker';
import '@/assets/scss/plugins/timeSelector.css';

const TimeSelector = ({
  initValue,
  onChange,
}: {
  initValue?: string;
  onInit?: (time: string) => void;
  onChange?: (time: string) => void;
}) => {
  const [value, setValue] = useState<string>(initValue || '10:00');

  // 부모에서 initValue가 변경될 때 state 업데이트
  useEffect(() => {
    setValue(initValue || '10:00');
  }, [initValue]);

  const handleChange = (value: string | null) => {
    if (value === null) {
      console.log('시간 값이 초기화되었습니다.');
    } else {
      console.log('선택된 시간:', value);
      setValue(value);
      onChange?.(value);
    }
  };

  return (
    <div>
      <TimePicker
        onChange={handleChange}
        value={value}
        disableClock={true}
        format="HH:mm"
        className="time-picker"
      />
    </div>
  );
};

export default TimeSelector;
