import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { Popover } from '@radix-ui/themes';
import Calendar from 'react-calendar';
import dayjs from 'dayjs';
import { Value } from 'react-calendar/src/shared/types.ts';
import { CalendarProps } from 'react-calendar/src/Calendar.tsx';
import calender from '@/assets/images/ic/24/calender.svg';

// 부모 컴포넌트로 값을 전달하기 위한 props 추가
interface MonthSelectorProps extends CalendarProps {
  value?: Value;
  onValueChange?: (value: Value) => void;
  onInit?: (value: Value) => void;
}

const MonthSelector = (props: MonthSelectorProps) => {
  const {
    value: controlledValue,
    onValueChange,
    onInit,
    ...calendarProps
  } = props;
  const [internalValue, setInternalValue] = useState<Value>(
    controlledValue ?? new Date(),
  );
  const value = controlledValue ?? internalValue;

  const { i18n } = useTranslation();
  const format = i18n.language === 'ko' ? 'YYYY년 MM월' : 'YYYY-MM';
  const languageString = i18n.language === 'ko' ? 'ko' : 'en';

  useEffect(() => {
    if (value) {
      onInit?.(value);
    }
  }, []);

  const handleValueChange = (newValue: Value) => {
    if (!controlledValue) {
      setInternalValue(newValue); // 내부 상태만 관리할 때
    }
    onValueChange?.(newValue);
  };

  return (
    <Popover.Root>
      <Popover.Trigger>
        <div className="max-w-[256px] w-max px-3 py-[10px] f-c gap-2 bg-white/20 border border-gray-6 rounded-md cursor-pointer">
          <img src={calender} alt={'calendar'} />
          <div className="body2">{dayjs(value?.toString()).format(format)}</div>
        </div>
      </Popover.Trigger>
      <Popover.Content
        align="center"
        width={languageString == 'ko' ? '240px' : '260px'}
      >
        <Calendar
          locale={languageString}
          {...calendarProps}
          view={'year'}
          className={'h-full'}
          onClickMonth={handleValueChange}
          value={value}
          calendarType={'gregory'}
        />
      </Popover.Content>
    </Popover.Root>
  );
};

export default MonthSelector;
