import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import { Popover } from '@radix-ui/themes';
import Calendar from 'react-calendar';
import dayjs from 'dayjs';
import type { LooseValue, Value } from 'react-calendar/src/shared/types.ts';
import calender from '@/assets/images/ic/24/calender.svg';

interface FromToSelectorProps {
  fontSize?: string;
  initValue?: { start: string; end: string };
  onInit?: (start: string, end: string) => void;
  onChange?: (start: string, end: string) => void;
}

const FromToSelector = (props: FromToSelectorProps) => {
  const [value, onChange] = useState<Value[] | Value | LooseValue>([
    props.initValue?.start ? new Date(props.initValue?.start) : new Date(),
    props.initValue?.end ? new Date(props.initValue?.end) : new Date(),
  ]);
  const [start, setStart] = useState<string>(props.initValue?.start ?? '');
  const [end, setEnd] = useState<string>(props.initValue?.end ?? '');
  const { i18n } = useTranslation();
  const format = i18n.language === 'ko' ? 'YYYY년 MM월 DD일' : 'YYYY-MM-DD';
  const languageString = i18n.language === 'ko' ? 'ko' : 'en';

  useEffect(() => {
    if (value && Array.isArray(value)) {
      if (value[0] && value[1]) {
        props.onInit?.(value[0].toString(), value[1].toString());
      }
    }
  }, []);

  useEffect(() => {
    if (value && Array.isArray(value)) {
      if (value[0] && value[1]) {
        setStart(value[0].toString());
        setEnd(value[1].toString());
      }
    }
  }, [value]);

  useEffect(() => {
    props.onChange?.(start, end);
  }, [start, end]);

  return (
    <Popover.Root>
      <Popover.Trigger>
        <div className="max-w-[256px] min-w-[256px] w-max px-3 py-[10px] f-c gap-2 bg-white/20 border border-gray-6 rounded-md cursor-pointer">
          <img src={calender} alt={'calendar'} />
          <div className="body2">
            {`${dayjs(start).format(format)} ~ ${dayjs(end).format(format)}`}
          </div>
        </div>
      </Popover.Trigger>
      <Popover.Content align="center">
        <Calendar
          locale={languageString}
          onChange={(v) => onChange(v)}
          value={value as LooseValue}
          selectRange
          className={'h-fit'}
        />
      </Popover.Content>
    </Popover.Root>
  );
};

export default FromToSelector;
