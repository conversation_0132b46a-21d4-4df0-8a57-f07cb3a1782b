import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { GeneralInfoWindow } from '@/logiMaps/react/general/InfoWindow';
import { Cross1Icon } from '@radix-ui/react-icons';
import eqstat_idle_e from '@/assets/images/badge/idle_e.svg';
import eqstat_operation_e from '@/assets/images/badge/operation_e.svg';
import eqstat_fault_e from '@/assets/images/badge/fault_e.svg';
import eqstat_maint_e from '@/assets/images/badge/maint_e.svg';
import eqstat_idle_k from '@/assets/images/badge/idle_k.svg';
import eqstat_operation_k from '@/assets/images/badge/operation_k.svg';
import eqstat_fault_k from '@/assets/images/badge/fault_k.svg';
import eqstat_maint_k from '@/assets/images/badge/maint_k.svg';

export interface EqGroupInfoWindowProps {
  id: string;
  position: { lat: number; lng: number };
  items: {
    id: string;
    equipmentId: string;
    modelName: string;
    vehicleNum: string;
    mileage: string;
    operationStatus: {
      running: boolean; // 운행 중
      idle: boolean; // 유휴
    };
    breakdownStatus: {
      breakdown: boolean; // 고장
      repairing: boolean; // 정비 중
      none: boolean; // 고장 없음
    };
  }[];
  isClosed?: boolean;
  onClose?: () => void;
}

const { i18n } = useTranslation();
const isEnglish = i18n.language === 'en';

const EqGroupInfoWindow = (props: EqGroupInfoWindowProps) => {
  const { t } = useTranslation();

  const getStatusIcon = (operationStatus, breakdownStatus) => {
    // 고장
    if (breakdownStatus?.breakdown) {
      return eqstat_fault_e : eqstat_fault_k;
    }
    // 정비 중
    if (breakdownStatus?.repairing) {
      return isEnglish ? eqstat_maint_e : eqstat_maint_k;
    }
    // 유휴
    if (operationStatus?.idle) {
      return isEnglish ? eqstat_idle_e : eqstat_idle_k;
    }
    // 운행 중
    if (operationStatus?.running) {
      return isEnglish ? eqstat_operation_e : eqstat_operation_k;
    }
    // 기본값
    return isEnglish ? eqstat_operation_e : eqstat_operation_k;
  };

  return (
    <GeneralInfoWindow
      id={props.id}
      position={{ lat: props.position.lat, lng: props.position.lng }}
      pixelOffset={[0, -20]}
      borderRadius="8px"
      zIndex={10}
    >
      <div className="w-[510px] max-h-[290px] px-1 overflow-y-scroll ">
        <div className="f-c-e">
          <Cross1Icon
            onClick={props.onClose}
            width={20}
            height={20}
            className="mb-[10px] cursor-pointer"
          />
        </div>
        <table>
          <thead>
            <tr className="text-left [&_th]:subtitle6">
              <th>{t('ModelName')}</th>
              <th>{t('VehicleNumber')}</th>
              <th>{t('Mileage')}</th>
              <th>{t('Status')}</th>
            </tr>
          </thead>
          <tbody>
            {props.items.map((row, index) => (
              <tr key={index} className="[&_td]:body5">
                <td>
                  <Link
                    to={'/eq_list'}
                    state={{ equipmentId: row.equipmentId }}
                    className="underline"
                  >
                    {row.modelName}
                  </Link>
                </td>
                <td>{row.vehicleNum}</td>
                <td>{row.mileage}</td>
                <td>
                  {getStatusIcon(row.operationStatus, row.breakdownStatus) && (
                    <img
                      src={getStatusIcon(
                        row.operationStatus,
                        row.breakdownStatus,
                      )}
                      alt={row.equipmentId}
                    />
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </GeneralInfoWindow>
  );
};

export default EqGroupInfoWindow;
