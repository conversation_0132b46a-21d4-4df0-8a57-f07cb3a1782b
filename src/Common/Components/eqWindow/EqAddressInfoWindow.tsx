import { GeneralInfoWindow } from '@/logiMaps/react/general/InfoWindow';
import { useQuery } from '@tanstack/react-query';

export interface EqAddressInfoWindowProps {
  id: string;
  position: { lat: number; lng: number };
  pixelOffset: [number, number];
  address?: string;
}

const EqAddressInfoWindow = (props: EqAddressInfoWindowProps) => {
  // const { t, i18n } = useTranslation();
  // const mapSource = useMapSource();

  /** Query */
  //주소 찾기
  const { data: equipmentAddress } = useQuery({
    queryKey: ['addressinfowindow', props.position],
    queryFn: async () => {
      try {
        if (props.address) {
          return props.address;
        } else if (props.position) {
          // const response = await commonApi.getArea1({
          //   latitude: props.position.lat.toFixed(6),
          //   longitude: props.position.lng.toFixed(6),
          //   language: i18n.language === 'en' ? 'en' : 'ko',
          // });
          // if (response.data) {
          //   if (
          //     response.data.status === 'OK' &&
          //     response.data.results &&
          //     response.data.results.length > 0
          //   ) {
          //     return response.data.results[0].formatted_address;
          //   }
          // }
        }
        return '';
      } catch (error) {
        console.error('API 호출 에러:', error);
        throw error;
      }
    },
    initialData: '',
    enabled: true,
  });

  return (
    <GeneralInfoWindow
      id={props.id}
      position={props.position}
      pixelOffset={props.pixelOffset}
      zIndex={10}
      backgroundColor={'rgba(0, 0, 0, 0.5)'}
      padding={'5px'}
      border={'0px solid'}
      borderRadius={'20px'}
    >
      {/* bodyContent */}
      <div className="text-xs font-normal text-white">{equipmentAddress}</div>
    </GeneralInfoWindow>
  );
};

export default EqAddressInfoWindow;
