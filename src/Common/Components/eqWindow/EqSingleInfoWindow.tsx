import { useEffect, useState } from 'react';
import { GeneralInfoWindow } from '@/logiMaps/react/general/InfoWindow';
import { getEqStatList } from '@/Common/function/functions.ts';
import { EquipmentType } from '@/types/EquipmentType';

export interface EqSingleInfoWindowProps {
  position: { lat: number; lng: number };
  item: EquipmentType.FilteredMapItem;
  onClose?: () => void;
}

const EqSingleInfoWindow = (props: EqSingleInfoWindowProps) => {
  const [, setEqStats] = useState<{ key: string; value: string }[]>([]);

  /** useEffect */
  useEffect(() => {
    if (!props.item) {
      return;
    }

    const eqStatList = getEqStatList(
      props.item.operationStatus,
      props.item.breakdownStatus,
    );
    setEqStats(
      eqStatList.map((item) => ({
        key: item,
        value: item,
      })),
    );
  }, [props.item, props.position]);

  // props.item이 없으면 렌더링하지 않음
  if (!props.item) {
    return null;
  }

  console.log('EqSingleInfoWindow: Rendering with item:', {
    id: props.item.id,
    modelName: props.item.modelName,
    equipmentId: props.item.equipmentId,
    mileage: props.item.mileage,
    position: props.position,
  });

  return (
    <GeneralInfoWindow
      id={props.item.id}
      position={{ lat: props.position.lat, lng: props.position.lng }}
      zIndex={10}
      pixelOffset={[0, -10]}
      borderRadius="8px"
    >
      <div
        className="
          bg-white rounded px-2 py-1
          [&>span]:px-2
          [&>span]:caption2
          [&>span]:text-gray-15
          [&>span]:border-r
          [&>span:last-child]:border-0
          [&>span]:border-gray-6
        "
      >
        <span>{props.item.modelName || 'N/A'}</span>
        <span>{props.item.plateNo || 'N/A'}</span>
        <span>{props.item.driver.name || 0}</span>
      </div>
    </GeneralInfoWindow>
  );
};

export default EqSingleInfoWindow;
