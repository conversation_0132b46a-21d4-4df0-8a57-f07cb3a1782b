import { GeneralInfoWindow } from '@/logiMaps/react/general/InfoWindow';
import { useCallback } from 'react';
import close from '@/assets/images/ic/24/close.svg';
import { FleetType } from '@/types/FleetType';

export interface EqBrokenInfoWindowProps {
  id: string;
  position: { lat: number; lng: number };
  item: FleetType.BrokenMapItem;
  onClose?: () => void;
}

const EqBrokenInfoWindow = (props: EqBrokenInfoWindowProps) => {
  const handleInfowindowCloseClick = useCallback(() => {
    props.onClose?.();
  }, []);

  return (
    <GeneralInfoWindow
      id={props.id}
      position={props.position}
      pixelOffset={[0, -4]}
      zIndex={10}
      //minWidth={140}
    >
      <div className="relative">
        <img
          className="absolute w-6 h-6 top-0 right-0"
          src={close}
          onClick={handleInfowindowCloseClick}
        />
        {/* bodyContent */}
        <div>
          <span className="text-xs font-normal pr-2">고장 발생일</span>
          <span className="text-xs font-normal">
            {props.item.issueDateTime}
          </span>
        </div>
        <div>
          <span className="text-xs font-normal pr-2">모델</span>
          <span className="text-xs font-normal">{props.item.modelName}</span>
        </div>
        <div>
          <span className="text-xs font-normal pr-2">호기</span>
          <span className="text-xs font-normal">{props.item.hogiNo}</span>
        </div>
        <div>
          <span className="text-xs font-normal pr-2">위치</span>
          <span className="text-xs font-normal">{props.item.address}</span>
        </div>
      </div>
    </GeneralInfoWindow>
  );
};

export default EqBrokenInfoWindow;
