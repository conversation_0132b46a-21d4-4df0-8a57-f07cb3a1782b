import { useTranslation } from 'react-i18next';
import { GeneralInfoWindow } from '@/logiMaps/react/general/InfoWindow';

export interface EqPositionInfoWindowProps {
  id: string;
  position: { lat: number; lng: number };
  pixelOffset: [number, number];
  routeInfo?: {
    date: string;
  };
  locInfo?: {
    vehNum: string;
    driver: string;
    latitude: string;
    longitude: string;
    location: string;
  };
}

const EqPositionInfoWindow = (props: EqPositionInfoWindowProps) => {
  const { t } = useTranslation();

  return (
    <GeneralInfoWindow
      id={props.id}
      position={props.position}
      pixelOffset={props.pixelOffset}
      zIndex={10}
    >
      {props.locInfo && (
        <div
          className="
            w-[228px] py-[10px] px-3 bg-white border border-gray-6 rounded-md relative
            [&>div]:f-c [&>div]:gap-2
            [&_h3]:w-[70px] [&_h3]:caption3 [&_h3]:text-gray-8
            [&_p]:caption3 [&_p]:text-right
          "
        >
          <div>
            <h3>{t('VehicleNo')}</h3>
            <span>{props.locInfo.vehNum}</span>
          </div>
          <div>
            <h3>{t('Driver')}</h3>
            <span>{props.locInfo.driver}</span>
          </div>
          <div>
            <h3>{t('Latitude')}</h3>
            <span>{props.locInfo.latitude}</span>
          </div>
          <div>
            <h3>{t('Longitude')}</h3>
            <span>{props.locInfo.longitude}</span>
          </div>
          <div>
            <h3>{t('Location')}</h3>
            <span>{props.locInfo.location}</span>
          </div>
        </div>
      )}
    </GeneralInfoWindow>
  );
};

export default EqPositionInfoWindow;
