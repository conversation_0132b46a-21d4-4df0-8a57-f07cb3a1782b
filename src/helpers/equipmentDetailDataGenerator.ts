import DrivingPattern from '@/Pages/MonitoringEq/components/EqList/statistics/DrivingPattern';
import {
  getRandomDateTime,
  getRandomFloat,
  getRandomInt,
  getRandomTimeFormatted,
} from './randomDataGenerator';

export const generateEquipmentDistanceData = () => {
  return {
    todaysDistance: `${getRandomInt(2, 99)}km`,
    totalDistance: `${getRandomFloat(2, 99)}km`,
  };
};

export const generateEquipmentGaugeInfo = () => {
  return {
    isEV: false,
    // 엔진식 (디젤/경유)
    fuelD: {
      //연료,  변속오일, 냉각수, 요소수
      fuel: { value: 65, gauge: 75 },
      tmoTemp: { value: 82, gauge: 70 },
      coolTemp: { value: 90, gauge: 80 },
      defLevel: { value: 88, gauge: 88 },
    },
    // 엔진식 (가솔린/휘발유)
    fuelG: {
      //연료, 변속오일, 냉각수
      fuel: { value: 65, gauge: 75 },
      tmoTemp: { value: 82, gauge: 70 },
      coolTemp: { value: 90, gauge: 80 },
    },
    ev: {
      //배터리, 모터(오른쪽), 모터(왼쪽), 펌프, EPS
      battery: { value: 85, gauge: 85 },
      rightMotor: { value: 45, gauge: 60 },
      leftMotor: { value: 42, gauge: 58 },
      pumpMotor: { value: 38, gauge: 55 },
      epsMotor: { value: 92, gauge: 92 },
    },
  };
};

export const generateEquipmentDailyInfo = (isElectric: boolean) => {
  return {
    isElectric: isElectric,
    faultAlert: {
      count: getRandomInt(2, 10),
      recent: getRandomInt(2, 10),
    },
    consumableAlert: {
      count: getRandomInt(2, 10),
      recent: getRandomInt(2, 10),
    },
    drivingCommon: {
      drivingTime: getRandomTimeFormatted('{hr}h {min}m'),
      avgDrivingTime: getRandomTimeFormatted('{hr}h {min}m'),
      idleTime: getRandomTimeFormatted('{hr}h {min}m'),
      avgIdleTime: getRandomTimeFormatted('{hr}h {min}m'),
      todayDis: getRandomTimeFormatted('{hr}h {min}m'),
      avgDis: getRandomTimeFormatted('{hr}h {min}m'),
    },
    drivingPattern: {
      overspeedCount: getRandomInt(2, 10),
      averageSpeed: getRandomInt(2, 10),
      harshBrakingCount: getRandomInt(2, 10),
      harshAccelerationCount: getRandomInt(2, 10),
    },
    location: {
      datetime: getRandomDateTime(),
      latlng: {
        lat: 40.712776,
        lng: -74.005974,
      }, //뉴욕 시청
      address: 'City Hall Park, New York, NY 10007, USA',
    },
  };
};

export const generateEquipmentMonthlyInfo = (isElectric: boolean) => {
  return {
    isElectric: isElectric,
    yearMonth: '2023-10',
    common: {
      workingDays: getRandomInt(20, 30),
      totalOperatingTime: {
        hours: getRandomInt(0, 24),
        mins: getRandomInt(0, 59),
      },
      avgDailyOperatingTime: {
        hours: getRandomInt(0, 24),
        mins: getRandomInt(0, 59),
      },
      engRunHour: getRandomInt(0, 9).toString(), //가동 시간
      drivingHour: getRandomInt(0, 9).toString(), //작업 시간
      travelHour: getRandomInt(0, 9).toString(), //이동 시간
      idleHour: getRandomInt(0, 9).toString(), //유휴 시간
      stdModeHour: getRandomInt(0, 9).toString(), //엔진파워.스탠다드
      pwrModeHour: getRandomInt(0, 9).toString(), //엔진파워.파워
    },
    drivingPattern: {
      overspeedCount: getRandomInt(2, 10),
      averageSpeed: getRandomInt(2, 10),
      harshBrakingCount: getRandomInt(2, 10),
      harshAccelerationCount: getRandomInt(2, 10),
    },
    fuel: {
      fuelUsed: getRandomFloat(100, 500, 1), // 총연료사용량 (예: 100~500L)
      fuelUsedAvg: getRandomFloat(3, 20, 1), // 일평균사용량 (예: 3~20L)
      fuelRate: getRandomFloat(2, 8, 2), // 연비(평균, 예: 2~8km/L)
      logs: Array.from({ length: 28 }, (_, i) => ({
        date: i + 1,
        usage: getRandomFloat(2, 25, 2), // 일별 연료사용량 (예: 2~25L)
      })),
    },
    battery: {
      batteryUsed: getRandomFloat(100, 500, 1), // 총 배터리 사용량 (예: 100~500kWh)
      batteryUsedAvg: getRandomFloat(3, 20, 1), // 일평균 배터리 사용량 (예: 3~20kWh)
      batteryRate: getRandomFloat(10, 100, 1), // 배터리 사용 비율 (예: 10~100%)
      logs: Array.from({ length: 28 }, (_, i) => ({
        date: i + 1,
        usage: getRandomFloat(2, 25, 2), // 일별 배터리 사용량 (예: 2~25kWh)
      })),
    },
    engine: Array.from({ length: 28 }, (_, i) => ({
      day: i + 1,
      engRunHour: {
        hours: getRandomInt(0, 4),
        mins: getRandomInt(0, 59),
      },
      workingHour: {
        hours: getRandomInt(0, 4),
        mins: getRandomInt(0, 59),
      },
      travelHour: {
        hours: getRandomInt(0, 4),
        mins: getRandomInt(0, 59),
      },
      idleHour: {
        hours: getRandomInt(0, 4),
        mins: getRandomInt(0, 59),
      },
    })),
    temperature: Array.from({ length: 28 * 3 }, (_, i) => ({
      itemCode: i % 3 < 1 ? `L` : i % 3 < 2 ? `C` : `T`,
      temp30: getRandomInt(0, 100),
      temp30_60: getRandomInt(0, 100),
      temp60_70: getRandomInt(0, 100),
      temp70_80: getRandomInt(0, 100),
      temp80_90: getRandomInt(0, 100),
      temp90_100: getRandomInt(0, 100),
      temp100: getRandomInt(0, 100),
    })),
  };
};

export const generateEquipmentCustomRange = (isElectric: boolean) => {
  // 임의의 날짜 범위
  const fromDate = '2023-10-01';
  const toDate = '2023-10-15';

  // 엔진 가동시간 통계 샘플
  const engRunHourStatistics = {
    h4: getRandomInt(0, 5),
    h5: getRandomInt(0, 5),
    h6: getRandomInt(0, 5),
    h7: getRandomInt(0, 5),
    h8: getRandomInt(0, 5),
    h9: getRandomInt(0, 5),
    h10: getRandomInt(0, 5),
    h11: getRandomInt(0, 5),
    h12: getRandomInt(0, 5),
    h13: getRandomInt(0, 5),
    h14: getRandomInt(0, 5),
  };

  return {
    isElectric,
    fromDate,
    toDate,
    common: {
      workingDays: getRandomInt(10, 15),
      totalOperatingTime: {
        hours: getRandomInt(50, 100),
        mins: getRandomInt(0, 59),
      },
      avgDailyOperatingTime: {
        hours: getRandomInt(2, 8),
        mins: getRandomInt(0, 59),
      },
      engRunHourStatistics,
      engRunHour: getRandomInt(10, 50).toString(),
      drivingHour: getRandomInt(10, 50).toString(),
      travelHour: getRandomInt(10, 50).toString(),
      idleHour: getRandomInt(10, 50).toString(),
      stdModeHour: getRandomInt(5, 20).toString(),
      pwrModeHour: getRandomInt(5, 20).toString(),
    },
    drivingPattern: {
      overspeedCount: getRandomInt(2, 10),
      averageSpeed: getRandomInt(2, 10),
      harshBrakingCount: getRandomInt(2, 10),
      harshAccelerationCount: getRandomInt(2, 10),
    },
    fuel: isElectric
      ? undefined
      : {
          fuelUsed: getRandomFloat(100, 500, 1),
          fuelUsedAvg: getRandomFloat(3, 20, 1),
          fuelRate: getRandomFloat(2, 8, 2),
          logs: Array.from({ length: 15 }, (_, i) => ({
            date: i + 1,
            dateType: 'D',
            usage: getRandomFloat(2, 25, 2),
          })),
        },
    battery: isElectric
      ? {
          batteryUsed: getRandomFloat(100, 500, 1),
          batteryUsedAvg: getRandomFloat(3, 20, 1),
          batteryRate: getRandomFloat(10, 100, 1),
          logs: Array.from({ length: 15 }, (_, i) => ({
            date: i + 1,
            dateType: 'D',
            usage: getRandomFloat(2, 25, 2),
          })),
        }
      : undefined,
    engine: Array.from({ length: 28 }, (_, i) => ({
      day: i + 1,
      engRunHour: {
        hours: getRandomInt(0, 4),
        mins: getRandomInt(0, 59),
      },
      workingHour: {
        hours: getRandomInt(0, 4),
        mins: getRandomInt(0, 59),
      },
      travelHour: {
        hours: getRandomInt(0, 4),
        mins: getRandomInt(0, 59),
      },
      idleHour: {
        hours: getRandomInt(0, 4),
        mins: getRandomInt(0, 59),
      },
    })),
    temperature: Array.from({ length: 28 * 3 }, (_, i) => ({
      itemCode: i % 3 < 1 ? `L` : i % 3 < 2 ? `C` : `T`,
      temp30: getRandomInt(0, 100),
      temp30_60: getRandomInt(0, 100),
      temp60_70: getRandomInt(0, 100),
      temp70_80: getRandomInt(0, 100),
      temp80_90: getRandomInt(0, 100),
      temp90_100: getRandomInt(0, 100),
      temp100: getRandomInt(0, 100),
    })),
  };
};

export const generateWorkHistoryData = () => {
  return {
    rows: [
      {
        dispatchStatus: 'before',
        viewMode: 1,
        title: { name: 'Location' },
        driver: null,
        drivingStats: [
          {
            label: 'Average Speed',
            value: '60km/h',
          },
          {
            label: 'Overspeed Count',
            value: '5',
          },
          {
            label: 'Harsh Braking Count',
            value: '5',
          },
          {
            label: 'Harsh Acceleration Count',
            value: '5',
          },
          {
            label: 'Sudden Start Count',
            value: '5',
          },
        ],
        timelineList: null,
      },
      {
        dispatchStatus: 'inOperation',
        viewMode: 2,
        title: {
          name: 'Current Route',
          score: '85',
        },
        driver: null,
        drivingStats: [
          {
            label: 'Average Speed',
            value: '60km/h',
          },
          {
            label: 'Overspeed Count',
            value: '5',
          },
          {
            label: 'Harsh Braking Count',
            value: '5',
          },
          {
            label: 'Harsh Acceleration Count',
            value: '5',
          },
          {
            label: 'Sudden Start Count',
            value: '5',
          },
        ],
        timelineList: [
          {
            vehNum: 'TRK-1001',
            driver: 'Alice',
            address: '123 Main St',
            segments: [
              {
                estimatedTime: '30 min',
                arrivalTime: '09:15',
                destination: 'Warehouse A',
              },
              {
                estimatedTime: '45 min',
                arrivalTime: '10:00',
                destination: 'Warehouse B',
              },
            ],
          },
        ],
      },
      {
        dispatchStatus: 'completed',
        viewMode: 2,
        title: {
          name: 'Completed Route',
          timeRange: '19:12 ~ 20:43',
          totalTime: 'Total 1 hr 21 min',
          totalDistance: '43.7km',
          score: '45',
        },
        driver: {
          name: 'John Smith',
          phone: '+****************',
          email: '<EMAIL>',
          type: 'driver type',
        },
        drivingStats: [
          {
            label: 'Driving Distance',
            value: '321km',
          },
          {
            label: 'Driving Time',
            value: '8 hr',
          },
          {
            label: 'Average Speed',
            value: '60km/h',
          },
          {
            label: 'Visited Locations',
            value: '6',
          },
          {
            label: 'Overspeed Count',
            value: '5',
          },
          {
            label: 'Harsh Braking Count',
            value: '5',
          },
          {
            label: 'Harsh Acceleration Count',
            value: '5',
          },
          {
            label: 'Sudden Start Count',
            value: '5',
          },
        ],
        timelineList: [
          {
            vehNum: 'TRK-1001',
            driver: 'Alice',
            address: '123 Main St',
            segments: [
              {
                tagName: 'start',
                address: '123 Main St',
                arrivalTime: '2025-04-08 09:15',
                estimatedTime: '30 min',
                //destination: 'Warehouse A',
              },
              {
                estimatedTime: '45 min',
                arrivalTime: '10:00',
                destination: 'Warehouse B',
              },
            ],
          },
        ],
      },
    ],
    page: {
      pageSize: 1,
      totalCnt: 10,
      pageNum: 0,
    },
  };
};

export const generatePositionTabData = () => {
  return {
    rows: [
      {
        dispatchStatus: 'before',
        viewMode: 1,
        title: { name: 'Location' },
        driver: null,
        drivingStats: [
          {
            label: 'Average Speed',
            value: '60km/h',
          },
          {
            label: 'Overspeed Count',
            value: '5',
          },
          {
            label: 'Harsh Braking Count',
            value: '5',
          },
          {
            label: 'Harsh Acceleration Count',
            value: '5',
          },
          {
            label: 'Sudden Start Count',
            value: '5',
          },
        ],
        currentLocation: {
          lat: 40.7308,
          lng: -73.987054,
        },
        timelineList: null,
      },
      {
        dispatchStatus: 'inOperation',
        viewMode: 2,
        title: {
          name: 'Current Route',
          score: '85',
        },
        driver: null,
        drivingStats: [
          {
            label: 'Average Speed',
            value: '60km/h',
          },
          {
            label: 'Overspeed Count',
            value: '5',
          },
          {
            label: 'Harsh Braking Count',
            value: '5',
          },
          {
            label: 'Harsh Acceleration Count',
            value: '5',
          },
          {
            label: 'Sudden Start Count',
            value: '5',
          },
        ],
        currentLocation: {
          lat: 40.7308,
          lng: -73.987054,
        },
        timelineList: [
          {
            segments: [
              {
                tagName: 'start',
                address: 'Warehouse A',
                arrivalTime: '2025-04-08 09:15',
                estimatedTime: '30 min',
                completed: true,
                path: [
                  {
                    lat: 40.730804,
                    lng: -73.993148,
                  },
                  {
                    lat: 40.730461,
                    lng: -73.992278,
                  },
                  {
                    lat: 40.729433,
                    lng: -73.989782,
                  },
                  {
                    lat: 40.728545,
                    lng: -73.987638,
                  },
                  {
                    lat: 40.73418,
                    lng: -73.983452,
                  },
                ],
              },
              {
                tagName: 'end',
                address: 'Warehouse B',
                arrivalTime: '2025-04-08 11:15',
                estimatedTime: '45 min',
                completed: false,
                path: [
                  { lat: 40.5308, lng: -73.987054 },
                  { lat: 40.5308, lng: -73.987054 },
                ],
              },
            ],
          },
        ],
      },
      {
        dispatchStatus: 'completed',
        viewMode: 2,
        title: {
          name: 'Completed Route',
          timeRange: '19:12 ~ 20:43',
          totalTime: 'Total 1 hr 21 min',
          totalDistance: '43.7km',
          score: '45',
        },
        driver: {
          name: 'John Smith',
          phone: '+****************',
          email: '<EMAIL>',
          type: 'driver type',
        },
        drivingStats: [
          {
            label: 'Driving Distance',
            value: '321km',
          },
          {
            label: 'Driving Time',
            value: '8 hr',
          },
          {
            label: 'Average Speed',
            value: '60km/h',
          },
          {
            label: 'Visited Locations',
            value: '6',
          },
          {
            label: 'Overspeed Count',
            value: '5',
          },
          {
            label: 'Harsh Braking Count',
            value: '5',
          },
          {
            label: 'Harsh Acceleration Count',
            value: '5',
          },
          {
            label: 'Sudden Start Count',
            value: '5',
          },
        ],
        currentLocation: {
          lat: 40.7308,
          lng: -73.987054,
        },
        timelineList: [
          {
            segments: [
              {
                tagName: 'start',
                address: 'Warehouse A',
                arrivalTime: '2025-04-08 09:15',
                estimatedTime: '30 min',
                completed: true,
                path: [
                  {
                    lat: 40.730804,
                    lng: -73.993148,
                  },
                  {
                    lat: 40.730461,
                    lng: -73.992278,
                  },
                  {
                    lat: 40.729433,
                    lng: -73.989782,
                  },
                  {
                    lat: 40.728545,
                    lng: -73.987638,
                  },
                  {
                    lat: 40.73418,
                    lng: -73.983452,
                  },
                ],
              },
              {
                tagName: 'end',
                address: 'Warehouse B',
                arrivalTime: '2025-04-08 11:15',
                estimatedTime: '45 min',
                completed: false,
                path: [
                  {
                    lat: 40.73418,
                    lng: -73.983452,
                  },
                  {
                    lat: 40.735119,
                    lng: -73.985579,
                  },
                  {
                    lat: 40.732543,
                    lng: -73.987521,
                  },
                  {
                    lat: 40.735246,
                    lng: -73.994036,
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
    page: {
      pageSize: 1,
      totalCnt: 10,
      pageNum: 0,
    },
  };
};
