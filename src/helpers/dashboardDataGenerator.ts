/**
 * Dashboard component random data generator utility functions
 */
import { t } from 'i18next';
import {
  getRandomDateString,
  getRandomDriverName,
  getRandomElement,
  getRandomEquipmentId,
  getRandomEquipmentModel,
  getRandomFloat,
  getRandomInt,
  getRandomSerialNumber,
  getRandomWorkStatus,
} from './randomDataGenerator';

export const generateFleetList = () => {
  return ['Fleet1', 'Fleet2', 'Fleet3', 'Fleet4'];
};

export const generateDashboardData = () => {
  const workingTimeData = generateStatCardData();
  const totalOperationTimeData = generateTotalOperationTimeData();
  const drivingTimeData = generateStatCardData();
  const idleTimeData = generateStatCardData();
  const errorAlertChartData = generateErrorAlertChartData();
  const maintenanceReminderChartData =
    generateMaintenanceReminderAlertChartData();
  const chargingData = generateEnergyEfficiencyData();
  const fuelData = generateEnergyEfficiencyData();
  const operatingMachinesData = generateInfoCardData();
  const workersData = generateInfoCardData();
  const lineChartDataArr = Array.from({ length: 7 }, () =>
    generateLineGraphData(),
  );
  const lineChartData: Record<string, { time: string; value: number }[]> = {};
  lineChartDataArr.forEach((data, idx) => {
    lineChartData[String(idx)] = data;
  });
  const operationEfficiencyData = generateOperationEfficiencyData();
  const impactData = generateImpactData();
  const NumberOfCollisions = generateNumberOfCollisionsData();

  return {
    statCard: {
      workingTimeData: {
        value: Number(workingTimeData.value),
        comparison: workingTimeData.comparison,
      },
      totalOperationTimeData: {
        value: Number(totalOperationTimeData.value),
        comparison: totalOperationTimeData.comparison,
      },
      drivingTimeData: {
        value: Number(drivingTimeData.value),
        comparison: drivingTimeData.comparison,
      },
      idleTimeData: {
        value: Number(idleTimeData.value),
        comparison: idleTimeData.comparison,
      },
    },
    errorAlertChartData: errorAlertChartData,
    maintenanceReminderChartData: maintenanceReminderChartData,
    chargingData: {
      mainSection: {
        value: Number(chargingData.mainSection.value) ?? 0,
        comparison: chargingData.mainSection.comparison,
      },
      subSection: {
        value: Number(chargingData.subSection.value) ?? 0,
        comparison: chargingData.subSection.comparison,
      },
      mainUnit: t('Times'),
      subUnit: 'kWh',
    },
    fuelData: {
      mainSection: {
        value: Number(fuelData.mainSection.value) ?? 0,
        comparison: fuelData.mainSection.comparison,
      },
      subSection: {
        value: Number(fuelData.subSection.value) ?? 0,
        comparison: fuelData.subSection.comparison,
      },
      mainUnit: t('L'),
      subUnit: t('L'),
    },
    statCard2: {
      operatingMachines: {
        totalWorkingTimeData: {
          value: Number(operatingMachinesData.mainSection.value),
          comparison: operatingMachinesData.mainSection.comparison,
        },
        totalIdlingData: {
          value: Number(operatingMachinesData.subSection.value),
          comparison: operatingMachinesData.subSection.comparison,
        },
      },
      workers: {
        totalDrivingTimeData: {
          value: Number(workersData.mainSection.value),
          comparison: workersData.mainSection.comparison,
        },
        totalWorkingTimeData: {
          value: Number(workersData.subSection.value),
          comparison: workersData.subSection.comparison,
        },
      },
    },
    lineChartData: lineChartData,
    operationEfficiencyData: operationEfficiencyData,
    impactData: impactData,
    NumberOfCollisions: NumberOfCollisions,
  };
};

/**
 * Generates random data for the Notice component
 * @returns Random data for the Notice component
 */
export const generateNoticeData = (count = 5) => {
  const titles = [
    'FMS Service Extension Policy Update',
    'Terms of Service and Privacy Policy Account Information',
    'Account Creation Permission Change Notice',
    'FMS API',
    'Privacy Policy Full Revision Notice',
    'Service Maintenance Notice',
    'New Feature Release Notice',
    'Security Update Notice',
  ];

  const contexts = [
    'Important changes to the service extension policy for FMS users.',
    'Important information about account terms and privacy policy.',
    'Notice regarding changes to account creation permissions.',
    'Information about FMS API and usage guidelines.',
    'Important updates regarding the full revision of privacy policy.',
    'Notice for scheduled service maintenance.',
    'New features have been released. Check them out now.',
    'Security enhancement updates have been applied.',
  ];

  return Array(count)
    .fill(0)
    .map(() => ({
      title: getRandomElement(titles),
      context: getRandomElement(contexts),
      startDate: getRandomDateString(2023, 2024),
    }));
};

const generateErrorAlertChartData = () => {
  const pendingCount = getRandomInt(5, 100);
  const inProgressCount = getRandomInt(5, 100);
  const resolvedCount = getRandomInt(5, 100);

  return [
    { name: 'pending', value: pendingCount },
    { name: 'inprogress', value: inProgressCount },
    { name: 'resolved', value: resolvedCount },
  ];
};

const generateMaintenanceReminderAlertChartData = () => {
  const duesoonCount = getRandomInt(5, 100);
  const overdueCount = getRandomInt(5, 100);
  const replacedCount = getRandomInt(5, 100);

  return [
    { name: 'pending', value: duesoonCount },
    { name: 'inprogress', value: overdueCount },
    { name: 'resolved', value: replacedCount },
  ];
};

/**
 * Generates random data for the EnergyEfficiencyCard component
 * @returns Random data for the EnergyEfficiencyCard component
 */
const generateEnergyEfficiencyData = () => {
  const fuelEfficiency = getRandomFloat(5, 15, 1);
  const fuelEfficiencyChange = getRandomFloat(0.1, 2, 1);
  const fuelConsumption = getRandomFloat(10, 50, 1);
  const fuelConsumptionChange = getRandomFloat(0.5, 5, 1);

  return {
    mainSection: {
      value: fuelEfficiency.toString(),
      comparison: {
        value: fuelEfficiencyChange.toString(),
        isIncrease: getRandomInt(0, 1) === 1,
      },
    },
    subSection: {
      value: fuelConsumption.toString(),
      comparison: {
        value: fuelConsumptionChange.toString(),
        isIncrease: getRandomInt(0, 1) === 1,
      },
    },
    mainUnit: 'km/L',
    subUnit: 'L',
  };
};

/**
 * Generates random data for the FuelCost component
 * @returns Random data for the FuelCost component
 */
const generateFuelCostData = () => {
  const cost = getRandomInt(100000, 5000000);
  const changeValue = getRandomInt(10000, 500000);

  return {
    cost,
    comparison: {
      text: t('VsPerviousDay'),
      value: changeValue,
      isIncrease: getRandomInt(0, 1) === 1,
    },
  };
};

/**
 * Generates random data for the Impact component
 * @returns Random data for the Impact component
 */
const generateImpactData = () => {
  const generateRows = (count: number, isEquipment = true) => {
    return Array(count)
      .fill(0)
      .map(() => ({
        model: isEquipment ? getRandomEquipmentModel() : undefined,
        vehicleNumber: isEquipment ? getRandomEquipmentId() : undefined,
        manageNumber: isEquipment ? getRandomSerialNumber() : undefined,
        hitNumber: getRandomInt(1, 50),
        date: getRandomDateString(2023, 2024),
        name: isEquipment ? getRandomEquipmentModel() : getRandomDriverName(),
        number: isEquipment ? getRandomSerialNumber() : undefined,
        where: isEquipment
          ? undefined
          : getRandomElement(['Zone A', 'Zone B', 'Zone C', 'Zone D']),
      }));
  };

  return {
    eq: generateRows(5, true),
    user: generateRows(5, false),
  };
};

/**
 * Generates random data for the NumberOfCollisions component
 * @returns Random data for the NumberOfCollisions component
 */
const generateNumberOfCollisionsData = () => {
  return {
    vertical: getRandomInt(3, 15),
    side: getRandomInt(5, 20),
    fr: getRandomInt(1, 10),
  };
};

/**
 * Generates random data for the IndexChart component
 * @returns Random data for the IndexChart component
 */
const generateIndexChartData = () => {
  const total = 100;
  const ARank = getRandomInt(30, 70);
  const BRank = getRandomInt(10, 30);
  const CRank = getRandomInt(5, 20);
  const DRank = getRandomInt(5, 20);
  const NoReport = total - ARank - BRank - CRank - DRank;

  return [
    { value: ARank, name: t('ARank') },
    { value: BRank, name: t('BRank') },
    { value: CRank, name: t('CRank') },
    { value: DRank, name: t('DRank') },
    { value: NoReport, name: t('NoReport') },
  ];
};

/**
 * Generates random data for the InfoCard component
 * @returns Random data for the InfoCard component
 */
const generateInfoCardData = () => {
  const mainValue = getRandomInt(10, 100);
  const mainChangeValue = getRandomInt(1, 10);
  const subValue = getRandomInt(10, 100);
  const subChangeValue = getRandomInt(1, 10);

  return {
    mainSection: {
      value: mainValue.toString(),
      comparison: {
        value: mainChangeValue.toString(),
        isIncrease: getRandomInt(0, 1) === 1,
      },
    },
    subSection: {
      value: subValue.toString(),
      comparison: {
        value: subChangeValue.toString(),
        isIncrease: getRandomInt(0, 1) === 1,
      },
    },
  };
};

/**
 * Generates random data for the LineGraph component
 * @returns Random data for the LineGraph component
 */
const generateLineGraphData = () => {
  const days = ['12:00', '13:00', '14:00', '15:00', '16:00', '17:00', '18:00'];

  return days.map((day) => ({
    time: day,
    value: getRandomInt(30, 100),
  }));
};

/**
 * Generates random data for the DashboardMap component
 * @returns Random data for the DashboardMap component
 */
export const generateMapData = (count = 10) => {
  // Generate random locations based on Los Angeles center coordinates
  const centerLat = 34.0522; // LA latitude
  const centerLng = -118.2437; // LA longitude

  return Array(count)
    .fill(0)
    .map(() => {
      const latOffset = (Math.random() - 0.5) * 0.5; // Increased range for US coverage
      const lngOffset = (Math.random() - 0.5) * 0.5; // Increased range for US coverage

      const workStatus = getRandomWorkStatus();

      return {
        id: getRandomEquipmentId(),
        latlng: {
          lat: centerLat + latOffset,
          lng: centerLng + lngOffset,
        },
        eqStat: {
          operation: workStatus.isWorking,
          idle: !workStatus.isWorking,
          fault: false,
          required: false,
          maint: false,
        },
      };
    });
};

/**
 * Generates random data for the DrivingEfficiency component
 * @returns Random data for the DrivingEfficiency component
 */
const generateOperationEfficiencyData = () => {
  const days = ['12', '13', '14', '15', '16', '17', '18'];

  return days.map((day) => ({
    day,
    value: getRandomInt(50, 100),
  }));
};

/**
 * Generates random data for the StatCard component
 * @returns Random data for the StatCard component
 */
const generateStatCardData = () => {
  const value = getRandomInt(50, 500);
  const changeValue = getRandomInt(5, 50);

  return {
    value: value,
    comparison: {
      value: changeValue.toString(),
      isIncrease: getRandomInt(0, 1) === 1,
    },
  };
};

/**
 * Generates random data for the TotalOperationTimeCard component
 * @returns Random data for the TotalOperationTimeCard component
 */
const generateTotalOperationTimeData = () => {
  const hours = getRandomInt(100, 999);
  const changeHours = getRandomInt(5, 30);

  return {
    value: hours.toString(),
    comparison: {
      value: changeHours.toString(),
      isIncrease: getRandomInt(0, 1) === 1,
    },
  };
};

/**
 * Generates random data for the Weather component
 * @returns Random data for the Weather component
 */
export const generateWeatherData = (count = 5) => {
  const fleets = ['Fleet A', 'Fleet B', 'Fleet C', 'Fleet D', 'Fleet E'];
  const locations = [
    'Los Angeles',
    'San Francisco',
    'San Diego',
    'Las Vegas',
    'Phoenix',
    'Seattle',
    'Portland',
    'Denver',
  ];
  const centerLat = 34.0522; // LA latitude
  const centerLng = -118.2437; // LA longitude

  return Array(count)
    .fill(0)
    .map(() => ({
      fleet: getRandomElement(fleets),
      latlng: { lat: centerLat, lng: centerLng },
      location: getRandomElement(locations),
      tempC: getRandomInt(0, 10),
      tempF: getRandomInt(0, 10),
      maxtempC: getRandomInt(0, 10),
      maxtempF: getRandomInt(0, 10),
      mintempC: getRandomInt(0, 10),
      mintempF: getRandomInt(0, 10),
      weatherDesc: '',
      warn: getRandomInt(0, 10),
      //weather: getRandomInt(0, 4) as 0 | 1 | 2 | 3 | 4, // 0: Hail, 1: Cloudy, 2: Heavy Rain, 3: Clear, 4: Thunder
      //temperature: getRandomInt(-10, 35),
      //warn: getRandomInt(0, 2), // 0: Low Temp Warning, 1: High Temp Warning, 2: No Warning
    }));
};
