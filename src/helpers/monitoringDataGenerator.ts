import { getRandomInt } from './randomDataGenerator';

export const generateMapData = (count = 10) => {
  // Generate random locations based on Los Angeles center coordinates
  const centerLat = 34.0522; // LA latitude
  const centerLng = -118.2437; // LA longitude

  return Array(count)
    .fill(0)
    .map((_, idx) => {
      const latOffset = (Math.random() - 0.5) * 0.01; // 약 1km 이내
      const lngOffset = (Math.random() - 0.5) * 0.01;

      return {
        id: `EQ${1000 + idx}`,
        equipmentId: `EQ${1000 + idx}`,
        equipmentType: 'Excavator',
        vehicleType: 'Heavy',
        manufacturer: `Manufacturer${idx + 1}`,
        modelName: `Model${idx + 1}`,
        trimName: `Trim${idx + 1}`,
        productYear: 2020 + (idx % 5),
        plateNo: `LA-${1000 + idx}`,
        vinNumber: `VIN${100000 + idx}`,
        latlng: {
          lat: Math.random() * 100,
          lng: Math.random() * 100,
        },
        mileage: Math.floor(Math.random() * 1000),
        operationStatus: {
          running: true,
          idle: false,
        },
        gpsStatus: {
          connected: true,
          disconnected: false,
        },
        breakdownStatus: {
          breakdown: false,
          repairing: false,
          none: true,
        },
        driver: {
          id: `D${idx + 1}`,
          name: `Driver${idx + 1}`,
          countryDialCode: `US`,
          phone: `555-010${idx}`,
        },
        dealer: {
          id: `DE${idx + 1}`,
          name: `Dealer${idx + 1}`,
          countryDialCode: `US`,
          phone: `555-020${idx}`,
        },
        serviceCenter: {
          id: `SC${idx + 1}`,
          name: `Service Center${idx + 1}`,
          countryDialCode: `US`,
          phone: `555-030${idx}`,
          address: `123 Service St, LA${idx + 1}`,
        },
        location: 'Los Angeles, CA',
        lastUpdate: '2025-07-18 12:00',
      };
    });
};

export const generateTransportStatusData = () => {
  return {
    awaitingCount: getRandomInt(50, 200),
    deliveredCount: getRandomInt(50, 200),
    failedCount: getRandomInt(50, 200),
    inTransitCount: getRandomInt(50, 200),
    deliveryInfo: [
      {
        id: '123432351243',
        branchName: 'Fremont Center',
        fullAddress: '1234 Main St, Fremont, CA',
        arrivalTime: '2025-06-24 13:45',
        deliveryOrder: '9/15',
        status: 'Delivered',
      },
      {
        id: '123432351243',
        branchName: 'Fremont Center',
        fullAddress: '1234 Main St, Fremont, CA',
        arrivalTime: '2025-06-24 13:45',
        deliveryOrder: '9/15',
        status: 'Delivered',
      },
      {
        id: '123432351243',
        branchName: 'Fremont Center',
        fullAddress: '1234 Main St, Fremont, CA',
        arrivalTime: '2025-06-24 13:45',
        deliveryOrder: '9/15',
        status: 'Delivered',
      },
    ],
  };
};

export const generateDetailInfoData = () => {
  return {
    id: 'EQ1001',
    operationStatus: {
      running: true,
      idle: false,
    },
    breakdownStatus: {
      breakdown: false,
      repairing: false,
      none: true,
    },
    equipmentId: 'EQ1001',
    modelName: 'Model 1',
    plateNo: 'LA-1001',
    driver: 'John Doe',
    phoneNum: '555-0100',
    mileage: '100',
    faultInfo: undefined,
    maintInfo: undefined,
    dealerInfo: {
      name: 'Dealer 1',
      phone: '555-0101',
    },
    serviceInfo: {
      center: 'Service Center 1',
      phoneNum: '555-0101',
      address: '123 Service St, LA',
    },
    lastUpdate: '2025-07-18 12:00',
  };
};

export const generateTrackingDetailData = () => {
  return {
    id: 'EQ1001',
    operationStatus: {
      running: true,
      idle: false,
    },
    breakdownStatus: {
      breakdown: false,
      repairing: false,
      none: true,
    },
    equipmentId: 'EQ1001',
    modelName: 'Model 1',
    plateNo: 'LA-1001',
    driver: 'John Doe',
    phoneNum: '555-0100',
    mileage: '100',
    dispatchInfo: [
      {
        name: 'Destination 1',
        address: '123 Destination St, LA',
        time: '2025-07-18 12:00',
        completed: true,
        path: [
          {
            lat: 40.730588,
            lng: -73.99546,
          },
          {
            lat: 40.730296,
            lng: -73.994756,
          },
          {
            lat: 40.731147,
            lng: -73.993952,
          },
          {
            lat: 40.730804,
            lng: -73.993148,
          },
        ],
      },
      {
        name: 'Destination 2',
        address: '456 Destination Ave, LA',
        time: '2025-07-18 13:00',
        completed: false,
        path: [
          {
            lat: 40.730804,
            lng: -73.993148,
          },
          {
            lat: 40.730461,
            lng: -73.992278,
          },
          {
            lat: 40.729433,
            lng: -73.989782,
          },
          {
            lat: 40.728545,
            lng: -73.987638,
          },
          {
            lat: 40.73418,
            lng: -73.983452,
          },
        ],
      },
      {
        name: 'Destination 3',
        address: '789 Destination Blvd, LA',
        time: '2025-07-18 14:00',
        completed: false,
        path: [
          {
            lat: 40.73418,
            lng: -73.983452,
          },
          {
            lat: 40.735119,
            lng: -73.985579,
          },
          {
            lat: 40.732543,
            lng: -73.987521,
          },
          {
            lat: 40.735246,
            lng: -73.994036,
          },
        ],
      },
    ],
  };
};

export const generateTimelineData = () => {
  return [
    {
      vehNum: 'TRK-1001',
      driver: 'Alice',
      address: '123 Main St',
      segments: [
        {
          estimatedTime: '30 min',
          arrivalTime: '09:15',
          destination: 'Warehouse A',
        },
        {
          estimatedTime: '45 min',
          arrivalTime: '10:00',
          destination: 'Warehouse B',
        },
      ],
    },
    {
      vehNum: 'TRK-1002',
      driver: 'Bob',
      address: '456 Elm St',
      segments: [
        {
          estimatedTime: '15 min',
          arrivalTime: '09:30',
          destination: 'Depot X',
        },
        {
          estimatedTime: '1h 10min',
          arrivalTime: '10:40',
          destination: 'Depot Y',
        },
      ],
    },
  ];
};
