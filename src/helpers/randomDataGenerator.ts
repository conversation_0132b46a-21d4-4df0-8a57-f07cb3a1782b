import { t } from 'i18next';
/**
 * Equipment data random generator utility functions
 */

/**
 * 주어진 범위 내에서 랜덤 정수를 생성합니다.
 * @param min 최소값
 * @param max 최대값
 * @returns 랜덤 정수
 */
export const getRandomInt = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * 랜덤 불리언 값을 생성합니다.
 * @returns true 또는 false
 */
export const getRandomBoolean = (): boolean => {
  return Math.random() < 0.5;
};

/**
 * 주어진 범위 내에서 랜덤 소수를 생성합니다.
 * @param min 최소값
 * @param max 최대값
 * @param decimals 소수점 자릿수
 * @returns 랜덤 소수
 */
export const getRandomFloat = (
  min: number,
  max: number,
  decimals = 2,
): number => {
  const value = Math.random() * (max - min) + min;
  return Number(value.toFixed(decimals));
};

/**
 * 주어진 배열에서 랜덤 요소를 선택합니다.
 * @param array 선택할 배열
 * @returns 랜덤으로 선택된 요소
 */
export const getRandomElement = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)];
};

/**
 * 랜덤 날짜를 생성합니다.
 * @param start 시작 날짜
 * @param end 종료 날짜
 * @returns 랜덤 날짜
 */
export const getRandomDate = (start: Date, end: Date): Date => {
  return new Date(
    start.getTime() + Math.random() * (end.getTime() - start.getTime()),
  );
};

/**
 * 랜덤 시간을 HH:MM 형식으로 생성합니다.
 * @returns HH:MM 형식의 랜덤 시간
 */
export const getRandomTime = (): string => {
  const hours = getRandomInt(0, 23).toString().padStart(2, '0');
  const minutes = getRandomInt(0, 59).toString().padStart(2, '0');
  return `${hours} : ${minutes}`;
};

/**
 * 지정한 format에 맞게 랜덤 시간 문자열을 반환합니다.
 * format 예시: '{hr}h {min}m', '{hr}:{min}', '{hr}시간 {min}분'
 */
export const getRandomTimeFormatted = (
  format: string = '{hr}h {min}m',
): string => {
  const hr = getRandomInt(0, 23).toString().padStart(2, '0');
  const min = getRandomInt(0, 59).toString().padStart(2, '0');
  return format.replace('{hr}', hr).replace('{min}', min);
};

/**
 * 랜덤 장비 모델을 생성합니다.
 * @returns 랜덤 장비 모델
 */
export const getRandomEquipmentModel = (): string => {
  const models = ['100D-9V', '120D-7E', '150D-9', '180D-9', '200D-7E'];
  return getRandomElement(models);
};

/**
 * 랜덤 장비 일련번호를 생성합니다.
 * @returns 랜덤 장비 일련번호
 */
export const getRandomSerialNumber = (): string => {
  return getRandomInt(10000, 99999).toString().padStart(5, '0');
};

/**
 * 랜덤 장비 ID를 생성합니다.
 * @returns 랜덤 장비 ID
 */
export const getRandomEquipmentId = (): string => {
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const prefix = 'HHKHE';
  const middle = Array(3)
    .fill(0)
    .map(() => letters.charAt(Math.floor(Math.random() * letters.length)))
    .join('');
  const suffix = getRandomInt(10000, 99999).toString();
  return `${prefix}${middle}${suffix}`;
};

/**
 * 랜덤 운전자 이름을 생성합니다.
 * @returns 랜덤 운전자 이름
 */
export const getRandomDriverName = (): string => {
  const names = [
    'John Smith',
    'Emma Wilson',
    'Michael Brown',
    'Sarah Davis',
    'Daniel Lee',
    'James Johnson',
    'Emily White',
    'William Turner',
  ];
  return getRandomElement(names);
};

/**
 * 랜덤 배터리 레벨을 생성합니다.
 * @returns 랜덤 배터리 레벨 (0-500)
 */
export const getRandomBatteryLevel = (): number => {
  return getRandomInt(50, 500);
};

/**
 * 랜덤 배터리 용량을 생성합니다.
 * @returns 랜덤 배터리 용량 (L)
 */
export const getRandomBatteryCapacity = (): string => {
  return `${getRandomInt(100, 300)}L`;
};

/**
 * 랜덤 작업 시간을 생성합니다.
 * @returns 랜덤 작업 시간 (시간 분)
 */
export const getRandomWorkingHours = (): string => {
  const hours = getRandomInt(1, 12);
  const minutes = getRandomInt(0, 59);
  return `${hours} ${t('HoursS')} ${minutes}${t('MinuteM')}`;
};

/**
 * 랜덤 작업 시간 변화를 생성합니다.
 * @returns 랜덤 작업 시간 변화 (증가 또는 감소)
 */
export const getRandomWorkingHoursChange = (): {
  text1: string;
  text2: string;
  isIncrease: boolean;
} => {
  const hours = getRandomInt(0, 3);
  const isIncrease = Math.random() > 0.5;
  return {
    text1: `${t('VsPerviousDay')}`,
    text2: `${hours}${t('HoursS')} ${isIncrease ? t('Up') : t('Down')}`,
    isIncrease,
  };
};

/**
 * 랜덤 통신 상태를 생성합니다.
 * @returns 랜덤 통신 상태 (Online 또는 Offline)
 */
export const getRandomCommunicationStatus = (): {
  status: string;
  isOnline: boolean;
} => {
  const isOnline = Math.random() > 0.2; // 80% 확률로 온라인
  return {
    status: isOnline ? 'Online' : 'Offline',
    isOnline,
  };
};

/**
 * 랜덤 작업 상태를 생성합니다.
 * @returns 랜덤 작업 상태
 */
export const getRandomWorkStatus = (): {
  status: string;
  isWorking: boolean;
} => {
  const statuses = [
    { status: t('ARank'), isWorking: true },
    { status: t('BRank'), isWorking: false },
    { status: t('CRank'), isWorking: false },
    { status: t('DRank'), isWorking: false },
    { status: t('NoReport'), isWorking: false },
  ];
  return getRandomElement(statuses);
};

/**
 * 랜덤 날짜 문자열을 YYYY-MM-DD 형식으로 생성합니다.
 * @param startYear 시작 연도
 * @param endYear 종료 연도
 * @returns YYYY-MM-DD 형식의 랜덤 날짜 문자열
 */
export const getRandomDateString = (
  startYear = 2018,
  endYear = 2024,
): string => {
  const year = getRandomInt(startYear, endYear);
  const month = getRandomInt(1, 12).toString().padStart(2, '0');
  const day = getRandomInt(1, 28).toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

/**
 * 랜덤 서비스 기간을 생성합니다.
 * @returns 랜덤 서비스 기간 (시작일 ~ 종료일)
 */
export const getRandomServicePeriod = (): string => {
  const startDate = getRandomDateString(2018, 2022);
  const endDate = getRandomDateString(2023, 2026);
  return `${startDate} ~ ${endDate}`;
};

/**
 * 랜덤 SPN 코드를 생성합니다.
 * @returns 랜덤 SPN 코드
 */
export const getRandomSPN = (): string => {
  return getRandomInt(100, 9999).toString();
};

/**
 * 랜덤 FMI 코드를 생성합니다.
 * @returns 랜덤 FMI 코드
 */
export const getRandomFMI = (): string => {
  return getRandomInt(1, 31).toString();
};

/**
 * 랜덤 OC 코드를 생성합니다.
 * @returns 랜덤 OC 코드
 */
export const getRandomOC = (): string => {
  return getRandomInt(1, 99).toString();
};

/**
 * 랜덤 알람 타입을 생성합니다.
 * @returns 랜덤 알람 타입
 */
export const getRandomAlarmType = (): string => {
  const types = ['Key OFF', 'Active', 'Inactive', 'Warning', 'Critical'];
  return getRandomElement(types);
};

/**
 * 랜덤 딜러 이름을 생성합니다.
 * @returns 랜덤 딜러 이름
 */
export const getRandomDealerName = (): string => {
  const dealers = [
    'WEST COAST HEAVY EQUIPMENT INC.',
    'CALIFORNIA MACHINERY SOLUTIONS',
    'PACIFIC INDUSTRIAL EQUIPMENT',
    'AMERICAN HEAVY MACHINERY CO.',
    'US CONSTRUCTION EQUIPMENT',
    'SOUTHWEST EQUIPMENT DEALERS',
  ];
  return getRandomElement(dealers);
};

/**
 * 랜덤 엔진 상태 데이터를 생성합니다.
 * @returns 엔진 상태 데이터 객체
 */
export const getRandomEngineStatus = () => {
  return {
    ElapsedTime: getRandomInt(1, 100).toString(),
    EngineOilPressureKPa: getRandomInt(200, 400).toString(),
    TransmissionOilTemperature: getRandomInt(0, 100).toString(),
    EngineFuelRateLh: getRandomFloat(1, 5, 2).toString(),
    BarometricPressureKPa: getRandomFloat(98, 102, 1).toString(),
    EngineSpeedRpm: getRandomInt(700, 2000).toString(),
    EngineLoadatCurrentSpeed: getRandomInt(10, 100).toString(),
    AirIntakePressureKPa:
      Math.random() > 0.2 ? getRandomInt(10, 100).toString() : 'N/A',
    BatteryVoltageV: getRandomFloat(24, 29, 1).toString(),
    ActualTorque: getRandomInt(0, 100).toString(),
    DEFTank: getRandomFloat(0, 100, 1).toString(),
    Operationcondition: getRandomElement([
      'Engine Run',
      'Engine Stop',
      'Engine Idle',
    ]),
    FuelLevel: getRandomInt(0, 100).toString(),
    IntakeManifold1Term: getRandomInt(30, 60).toString(),
    CoolantTemperature: getRandomInt(40, 90).toString(),
    FuelTemperature:
      Math.random() > 0.2 ? getRandomInt(20, 80).toString() : 'N/A',
    HydraulicOilTemperature: getRandomInt(20, 60).toString(),
  };
};

/**
 * 랜덤 고장 코드 설명을 생성합니다.
 * @returns 랜덤 고장 코드 설명
 */
export const getRandomFaultDescription = (): string => {
  const descriptions = [
    'Aftertreatment 1 Diesel Exhaust Fluid Quality - Unknown Cause',
    'Engine Coolant Temperature Sensor Error',
    'Engine Oil Pressure Sensor Malfunction',
    'Fuel System Pressure Error',
    'Battery Voltage Abnormal',
    'Engine Speed Sensor Error',
  ];
  return getRandomElement(descriptions);
};

/**
 * 랜덤 예상 원인 데이터를 생성합니다.
 * @returns 예상 원인 데이터 배열
 */
export const getRandomCauseData = () => {
  return [
    { label: 'Probability', value: 'Part or System' },
    {
      label: getRandomInt(30, 40) + '%',
      value: 'Aftertreatment Diesel Exhaust Fluid Quality Sensor',
    },
    { label: getRandomInt(10, 20) + '%', value: 'Fuel System Pressure Sensor' },
    { label: getRandomInt(5, 10) + '%', value: 'Engine Control Module' },
  ];
};

/**
 * 랜덤 통계 데이터를 생성합니다.
 * @returns 통계 데이터 객체
 */
export const getRandomStatistics = () => {
  return {
    targetMachines: getRandomInt(10000, 15000),
    ecdOnMachines: getRandomInt(9000, 12000),
    ecdOffMachines: getRandomInt(1, 10),
    changingMachines: getRandomInt(10, 30),
    monthlyMessages: getRandomInt(50000, 80000),
    monthlyReports: getRandomInt(70000, 90000),
  };
};

/**
 * 랜덤 DateTime 문자열을 생성합니다 (YYYY-MM-DD HH:mm:ss)
 * @returns DateTime 문자열
 */
export const getRandomDateTime = (): string => {
  const date = getRandomDateString();
  const hours = getRandomInt(0, 23).toString().padStart(2, '0');
  const minutes = getRandomInt(0, 59).toString().padStart(2, '0');
  const seconds = getRandomInt(0, 59).toString().padStart(2, '0');
  return `${date} ${hours}:${minutes}:${seconds}`;
};
