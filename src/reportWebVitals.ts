// import { <PERSON><PERSON><PERSON><PERSON> } from 'web-vitals';

import { CLSMetric, FCPMetric, LCPMetric, TTFBMetric } from 'web-vitals';

const reportWebVitals = (
  onPerfEntry?:
    | {
        (metric: CLSMetric): void;
        (metric: FCPMetric): void;
        (metric: LCPMetric): void;
        (metric: TTFBMetric): void;
      }
    | undefined,
) => {
  if (onPerfEntry && onPerfEntry instanceof Function) {
    import('web-vitals').then(({ onCLS, onFCP, onLCP, onTTFB }) => {
      onCLS(onPerfEntry);
      onFCP(onPerfEntry);
      onLCP(onPerfEntry);
      onTTFB(onPerfEntry);
    });
  }
};

export default reportWebVitals;
