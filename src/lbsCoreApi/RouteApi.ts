/**
 * 경로 검색 API
 */

const BASE_URL = 'https://lbs-proxy-global.cartamobility.com';

/**
 * 경로 검색 API 타입 정의
 */

// 시작점 좌표
export interface StartPosition {
  dx: number; // longitude
  dy: number; // latitude
}

// 목적지 좌표
export interface DestinationPosition {
  dx: number; // longitude
  dy: number; // latitude
  nid: number; // destination id
}

// 경로 요청 데이터
export interface RouteRequest {
  command: string; // "RequestRoute"
  start: StartPosition;
  reqid: string; // request id (timestamp)
  routeoption: number; // 512
  destpos: DestinationPosition[];
  height: number;
  weight: number;
}

// 방문 순서 정보
export interface VisitOrderItem {
  nid: string;
  visit: number;
  estimate: number; // 예상 시간 (초)
  dist: number; // 거리 (미터)
}

// 경로 포인트
export interface RoutePoint {
  dx: number; // longitude
  dy: number; // latitude
}

// 경로 응답 데이터
export interface RouteResponse {
  command: string; // "ResponseRoute"
  resid: number; // response id
  order: VisitOrderItem[];
  pnts: RoutePoint[][];
}

/**
 * 경로 검색 API 호출
 * @param request 경로 검색 요청 데이터
 * @returns Promise<RouteResponse | null>
 */
export const requestRoute = async (
  request: RouteRequest,
): Promise<RouteResponse | null> => {
  try {
    const response = await fetch(`${BASE_URL}/tmsroute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (response.status == 200) {
      return await response.json();
    } else {
      return null;
    }
  } catch (error) {
    console.error('경로 검색 API 호출 실패:', error);
    return null;
  }
};

/**
 * 경로 검색을 위한 편의 함수
 * @param start 시작점 좌표 [longitude, latitude]
 * @param destinations 목적지들 [longitude, latitude][]
 * @param options 추가 옵션
 * @returns Promise<RouteResponse | null>
 */
export const searchRoute = async (
  start: [number, number], // [lng, lat]
  destinations: [number, number][], // [[lng, lat], ...]
  options: {
    height?: number;
    weight?: number;
    routeOption?: number;
  } = {},
): Promise<RouteResponse | null> => {
  const { height = 0, weight = 0, routeOption = 512 } = options;

  const reqid = Date.now().toString();

  const request: RouteRequest = {
    command: 'RequestRoute',
    start: {
      dx: start[0], // longitude
      dy: start[1], // latitude
    },
    reqid,
    routeoption: routeOption,
    destpos: destinations.map((dest, index) => ({
      dx: dest[0], // longitude
      dy: dest[1], // latitude
      nid: index + 1,
    })),
    height,
    weight,
  };

  return await requestRoute(request);
};
