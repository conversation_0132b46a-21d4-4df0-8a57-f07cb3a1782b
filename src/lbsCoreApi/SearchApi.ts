/**
 * 주소 검색 API
 */

const BASE_URL = 'https://lbs-proxy-global.cartamobility.com';

export interface SearchAddressItem {
  coordinates: [number, number]; // [lng, lat]
  country: string;
  adminarea: string;
  subadminarea: string;
  locality: string;
  sublocality: string;
  thoroughfare: string;
  subthoroughfare: string;
  postalcode: string;
  intersectioncoordinates: [number, number];
  intersectingstreetadminarea: string;
  intersectingstreetlocality: string;
  intersectingstreet: string;
}

export interface SearchAddressResponse {
  count: number;
  searchaddress: SearchAddressItem[];
}

/**
 * 주소 검색 API 호출
 * @param addr 검색할 주소 문자열
 * @returns Promise<SearchAddressResponse | null>
 */
export const searchAddress = async (
  addr: string,
): Promise<SearchAddressResponse | null> => {
  try {
    const response = await fetch(
      `${BASE_URL}/searchaddress?addr=${encodeURIComponent(addr)}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      },
    );

    if (response.status == 200) {
      return await response.json();
    } else {
      return null;
    }
  } catch (error) {
    console.error('주소 검색 API 호출 실패:', error);
    return null;
  }
};

/**
 * 주소 검색 (간단한 버전 - 배열만 반환)
 * @param addr 검색할 주소 문자열
 * @returns Promise<Array<{address: string, lat: number, lng: number}>>
 */
export const searchAddressSimple = async (
  addr: string,
): Promise<Array<{ address: string; lat: number; lng: number }>> => {
  try {
    const result = await searchAddress(addr);
    if (result && result.searchaddress) {
      return result.searchaddress.map((item: SearchAddressItem) => {
        // 주소 문자열 생성 (번지수 + 도로명 + 지역)
        const addressParts = [
          item.subthoroughfare,
          item.thoroughfare,
          item.locality,
          item.adminarea,
        ].filter(Boolean);

        return {
          address: addressParts.join(' '),
          lng: item.coordinates[0], // coordinates는 [lng, lat] 순서
          lat: item.coordinates[1],
        };
      });
    }
    return [];
  } catch (error) {
    console.error('주소 검색 실패:', error);
    return [];
  }
};
