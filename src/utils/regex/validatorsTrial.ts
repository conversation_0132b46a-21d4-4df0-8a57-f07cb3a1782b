import {
  validateFirstName,
  validateLastName,
  validateEmail,
  validatePassword,
  validatePasswordConfirm,
  PASSWORD_LENGTH,
} from '@/utils/regex/regex';

export type TrialFormValues = {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirm: string;
};

export type TrialFormErrors = Partial<Record<keyof TrialFormValues, string>>;

// 단일 필드 검증
export function validateField<K extends keyof TrialFormValues>(
  field: K,
  values: TrialFormValues,
  t: (k: string, o?: Record<string, any>) => string,
): string {
  const { firstName, lastName, email, password, confirm } = values;

  switch (field) {
    // case 'firstName':
    //   return validateFirstName(firstName) ? '' : t('FirstNameError');

    // case 'lastName':
    //   return validateLastName(lastName) ? '' : t('LastNameError');

    case 'email':
      return validateEmail(email) ? '' : t('ThisEmailIsAlreadyInUse');

    case 'password': {
      const r = validatePassword(password);
      if (!r.lengthOk)
        return t('PleaseEnterBetween8And12Characters', {
          min: PASSWORD_LENGTH.min,
          max: PASSWORD_LENGTH.max,
        });
      if (!r.allowedOk) return t('PleaseEnterBetween8And12Characters');
      if (!r.rules.lowercase) return t('PleaseEnterBetween8And12Characters');
      if (!r.rules.digit) return t('PleaseEnterBetween8And12Characters');
      if (!r.rules.special) return t('PleaseEnterBetween8And12Characters');
      return '';
    }

    case 'confirm':
      return validatePasswordConfirm(password, confirm)
        ? ''
        : t('PasswordsDoNotMatch');

    default:
      return '';
  }
}

// 비밀번호 확인 성공 여부
export function isConfirmSuccess(values: TrialFormValues): boolean {
  return validatePasswordConfirm(values.password, values.confirm);
}

// 전체 폼 검증
export function validateAll(
  values: TrialFormValues,
  t: (k: string, o?: Record<string, any>) => string,
): { errors: TrialFormErrors; isValid: boolean } {
  const fields: (keyof TrialFormValues)[] = [
    'firstName',
    'lastName',
    'email',
    'password',
    'confirm',
  ];
  const errors: TrialFormErrors = {};
  for (const f of fields) {
    const msg = validateField(f, values, t);
    if (msg) errors[f] = msg;
  }
  const isValid =
    fields.every((f) => values[f]) && Object.keys(errors).length === 0;
  return { errors, isValid };
}
