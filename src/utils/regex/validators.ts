import {
  validateEmail,
  validatePassword,
  PASSWORD_LENGTH,
} from '@/utils/regex/regex';

// 번역 함수 타입(선택). t를 안 쓰고 싶으면 기본 메시지로 처리
type TFunc = (k: string, o?: Record<string, any>) => string;

// ===== Email =====
export function isEmailValid(email: string): boolean {
  return validateEmail(email);
}

export function getEmailError(email: string, t?: TFunc): string {
  if (isEmailValid(email)) return '';
  return t ? t('EmailError') : '';
}

// ===== Password =====
export function isPasswordValid(password: string): boolean {
  const r = validatePassword(password);
  return (
    r.lengthOk &&
    r.allowedOk &&
    r.rules.uppercase &&
    r.rules.lowercase &&
    r.rules.digit &&
    r.rules.special
  );
}

export function getPasswordError(password: string, t?: TFunc): string {
  const r = validatePassword(password);

  if (!r.lengthOk) {
    return t
      ? t('PleaseEnterBetween8And12Characters', {
          min: PASSWORD_LENGTH.min,
          max: PASSWORD_LENGTH.max,
        })
      : ``;
  }
  if (!r.allowedOk) return t ? t('PleaseEnterBetween8And12Characters') : '';
  if (!r.rules.uppercase)
    return t ? t('PleaseEnterBetween8And12Characters') : '';
  if (!r.rules.lowercase)
    return t ? t('PleaseEnterBetween8And12Characters') : '';
  if (!r.rules.digit) return t ? t('PleaseEnterBetween8And12Characters') : '';
  if (!r.rules.special) return t ? t('PleaseEnterBetween8And12Characters') : '';
  return '';
}
