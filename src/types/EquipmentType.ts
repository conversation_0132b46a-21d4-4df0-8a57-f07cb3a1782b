import { EqBreakdownStatus, EqOperationStatus, PopupProps } from '.';

export namespace EquipmentType {
  export enum UnitType {
    L = 'L',
    P = '%',
  }

  // 필터링된 장비 데이터
  export type FilteredMapItem = {
    id: string; // ID
    equipmentId: string; //장비 번호
    equipmentType: string; //장비 타입
    vehicleType: string; //차량 타입
    manufacturer: string;
    modelName: string;
    trimName: string;
    productYear: number;
    plateNo: string; //차량 번호
    vinNumber: string; //관리 번호
    latlng: {
      lat: number;
      lng: number;
    };
    mileage: number;
    operationStatus: {
      running: boolean; // 운행 중
      idle: boolean; // 유휴
    };
    gpsStatus: {
      connected: boolean; // 통신 가능
      disconnected: boolean; // 통신 두절
    };
    breakdownStatus: {
      breakdown: boolean; // 고장
      repairing: boolean; // 정비 중
      none: boolean; // 고장 없음
    };
    driver: Driver; //운전자 정보
    dealer: Dealer; //대리점
    serviceCenter: ServiceCenter; //서비스 센터
    location: string; //위치
    lastUpdate: string; //최종 업데이트 일시
  };

  // 장비 서비스 상태
  export type EqService = {
    startDate: string;
    endDate: string;
    status: string;
  };

  // 운전자 정보
  export type Driver = {
    id: string; //운전자 ID
    name: string; //운전자 이름
    countryDialCode: string; //국가 전화 코드
    phone: string; //운전자 전화번호
  };

  // 대리점 정보
  export type Dealer = {
    id: string; //대리점 ID
    name: string; //대리점 이름
    countryDialCode: string; //국가 전화 코드
    phone: string; //대리점 전화번호
  };

  export type ServiceCenter = {
    id: string; //서비스 센터 ID
    name: string; //서비스 센터 이름
    countryDialCode: string; //국가 전화 코드
    phone: string; //서비스 센터 전화번호
    address: string; //서비스 센터 주소
  };

  export type DispatchInfo = {
    name: string;
    address: string;
    time: string;
    completed: boolean;
    path: { lat: number; lng: number }[];
  };

  //알람 팝업
  export interface AlarmPopupProps extends PopupProps {
    equipmentId: string;
    serialNo: string;
    row: any;
  }

  // 고장 가이드 북 팝업
  export interface TSCasebookColumnProps {
    no: string;
    file: string;
    registDate: string;
  }

  // 지오펜싱 설정 팝업
  export interface GeofencingSettingProps extends PopupProps {
    title?: string;
    text?: string;
    buttonText?: string;
    secondButtonText?: string;
    currentPosition: { lat: number; lng: number };
    currentAddress: string;
    latestReceivedPosition: { lat: number; lng: number };
    latestReceivedAddress: string;
  }

  // 알람 수신 팝업 테이블
  export interface AlarmReceiverColumnProps {
    no: string;
    permission: string;
    userId: string;
    country: string;
    mobile: string;
    email: string;
    name: string;
    delete: string;
  }

  export interface TSGListPopupProps extends PopupProps {
    spn: string;
    fmi: string;
  }

  //ExchangeTerm 팝업
  export interface ExchangeTermPopupProps extends PopupProps {
    equipmentId: string;
    row: any;
  }

  //ExpendableAlarm 팝업
  export interface ExpendableAlarmPopupProps extends PopupProps {
    equipmentId: string;
    modelName: string;
    plateNo: string;
    row: any;
  }

  // 기본 정보
  export type BasicInfo = {
    powerType: string; //E: EV
    modelName: string; //모델명
    plateNo: string; //차량 번호 (호기, hogiNo)
    serialNo: string; //관리 번호 (custNo)
    deliveryDate: string; //고객인도일자
    svcStartDate: string; //서비스개시일자
    svcEndDate: string; //서비스종료일자
    commAvail: string; //통신상태
    commAvailCd: string; //통신상태 (AR: 통신가능)
  };

  // 일별 -> 알림
  export type DailyAlertInfo = {
    count: number; //알림
    recent: number; //새로운 알림
  };

  // 일별 -> 운행 기본 정보
  export type DailyDrivingCommonInfo = {
    drivingTime: string;
    avgDrivingTime: string;
    idleTime: string;
    avgIdleTime: string;
    todayDis: string;
    avgDis: string;
  };

  // 일별 -> 운행 패턴
  export type DailyDrivingPatternInfo = {
    overspeedCount: number; //과속 횟수
    averageSpeed: number; //평균 속도
    harshBrakingCount: number; //급제동 횟수
    harshAccelerationCount: number; //급가속 횟수
  };

  // 일별 -> 일별 정보
  export type DailyInfo = {
    isElectric: boolean;
    faultAlert: DailyAlertInfo; //고장 알림
    consumableAlert: DailyAlertInfo; //소모품 알림
    drivingCommon: DailyDrivingCommonInfo;
    drivingPattern: DailyDrivingPatternInfo;
    location: {
      datetime: string;
      latlng: {
        lat: number;
        lng: number;
      };
      address: string;
    };
  };

  export type MonthlyInfo = {
    isElectric: boolean;
    yearMonth: string;
    common: {
      workingDays: number; //작동 일수
      totalOperatingTime: {
        //총가동시간
        hours: number;
        mins: number;
      };
      avgDailyOperatingTime: {
        //일평균가동시간
        hours: number;
        mins: number;
      };
      engRunHour: string; //가동 시간
      drivingHour: string; //작업 시간
      travelHour: string; //이동 시간
      idleHour: string; //유휴 시간
      stdModeHour: string; //엔진파워.스탠다드
      pwrModeHour: string; //엔진파워.파워
    };
    drivingPattern: {
      overspeedCount: number; //과속 횟수
      averageSpeed: number; //평균 속도
      harshBrakingCount: number; //급제동 횟수
      harshAccelerationCount: number; //급가속 횟수
    };
    fuel?: {
      fuelUsed: number; //연료사용량.총연료사용량
      fuelUsedAvg: number; //연료사용량.일평균사용량
      fuelRate: number; //연료사용량.연비(평균)
      logs: {
        date: number;
        usage: number; //연료사용량
      }[];
    };
    battery?: {
      batteryUsed: number; //배터리 사용량
      batteryUsedAvg: number; //배터리 평균 사용량
      batteryRate: number; //배터리 사용 비율
      logs: {
        date: number;
        usage: number; //배터리 사용량
      }[];
    };
    engine: {
      day: number;
      engRunHour: {
        //엔진가동
        hours: number;
        mins: number;
      };
      workingHour: {
        //작업시간
        hours: number;
        mins: number;
      };
      travelHour: {
        //주행시간
        hours: number;
        mins: number;
      };
      idleHour: {
        //공회전시간
        hours: number;
        mins: number;
      };
    }[];
    temperature: {
      //L:작동유, C:냉각수, T:변속오일
      //MR:모터(오른쪽), ML:모터(왼쪽), MP:펌프, ME:EPS
      itemCode: string;
      temp30: number;
      temp30_60: number;
      temp60_70: number;
      temp70_80: number;
      temp80_90: number;
      temp90_100: number;
      temp100: number;
    }[];
  };

  export type PeriodicInfo = {
    isElectric: boolean;
    fromDate: string;
    toDate: string;
    common: {
      workingDays: number; //작동 일수
      totalOperatingTime: {
        //총가동시간
        hours: number;
        mins: number;
      };
      avgDailyOperatingTime: {
        //일평균가동시간
        hours: number;
        mins: number;
      };
      engRunHourStatistics: {
        h4: number;
        h5: number;
        h6: number;
        h7: number;
        h8: number;
        h9: number;
        h10: number;
        h11: number;
        h12: number;
        h13: number;
        h14: number;
      };
      engRunHour: string; //가동 시간
      drivingHour: string; //작업 시간
      travelHour: string; //이동 시간
      idleHour: string; //유휴 시간
      stdModeHour: string; //엔진파워.스탠다드
      pwrModeHour: string; //엔진파워.파워
    };
    drivingPattern: {
      overspeedCount: number; //과속 횟수
      averageSpeed: number; //평균 속도
      harshBrakingCount: number; //급제동 횟수
      harshAccelerationCount: number; //급가속 횟수
    };
    fuel?: {
      fuelUsed: number; //연료사용량.총연료사용량
      fuelUsedAvg: number; //연료사용량.일평균사용량
      fuelRate: number; //연료사용량.연비(평균)
      logs: {
        date: number;
        dateType: string;
        usage: number;
      }[];
    };
    battery?: {
      batteryUsed: number; //배터리 사용량
      batteryUsedAvg: number; //배터리 평균 사용량
      batteryRate: number; //배터리 사용 비율
      logs: {
        date: number;
        dateType: string;
        usage: number;
      }[];
    };
    engine: {
      day: number;
      engRunHour: {
        //엔진가동
        hours: number;
        mins: number;
      };
      workingHour: {
        //작업시간
        hours: number;
        mins: number;
      };
      travelHour: {
        //주행시간
        hours: number;
        mins: number;
      };
      idleHour: {
        //공회전시간
        hours: number;
        mins: number;
      };
    }[];
    temperature: {
      //L:작동유, C:냉각수, T:변속오일
      //MR:모터(오른쪽), ML:모터(왼쪽), MP:펌프, ME:EPS
      itemCode: string;
      temp30: number;
      temp30_60: number;
      temp60_70: number;
      temp70_80: number;
      temp80_90: number;
      temp90_100: number;
      temp100: number;
    }[];
  };

  // 장비 상세 고장 알람 테이블들
  export interface ActiveDTC {
    date: string;
    source: string;
    dtc: string;
  }

  export interface DTCHistory {
    seqKey: number;
    date: string;
    source: string;
    status: string;
    mileage: number;
    dtc: string;
  }

  export interface DTCDetail {
    spn: string;
    fmi: string;
    level: string;
    description: string;
  }

  export interface DTCDetailAddtional {
    keyOn: string;
    powerOnTime: string;
    engine: string;
    oilTemperature: string;
    transmissionOliTemperature: string;
    engineSpeed: string;
    batteryVoltage: string;
    version: string;
  }

  // 소모품 이력 탭 테이블
  export interface ExpendableHistoryTabProps {
    date: string;
    item: string;
    type: string;
    setting: string;
    mileage: number;
    exchange: string;
    alarm: boolean;
  }

  // 위치 탭 테이블
  export interface PositionTableColumnProps {
    datetime: string;
    lat: number;
    lng: number;
  }

  // TSG 리스트 팝업 테이블
  export interface TSGListTableColumnProps {
    hcespn: string;
    fmi: string;
    model: string;
    tsg: string;
  }

  // 알람 코드 팝업 테이블
  export interface AlarmCodeTableColumnProps {
    spn: string;
    type: string;
    fmi: string;
    maker: string;
    level: string;
    enExplain: string;
    koExplain: string;
    reviewCode: string;
  }

  export interface ElectricAlarmCodeTableColumnProps {
    model: string;
    alarm: string;
    level: string;
    enExplain: string;
    koExplain: string;
  }

  // 일별 -> 엔진 파워 모드 테이블
  export interface DailyDrivingDistanceTableColumnProps {
    division: string;
    work: string;
  }

  // 온도 분포 탭 -> 테이블 타입
  export interface TemperatureProps {
    receivedDate?: string;
    oilTemp?: string;
    iceTemp?: string;
    transOil?: string;
    monitorRM?: string;
    monitorLM?: string;
    monitorPump?: string;
    monitorEPS?: string;
  }

  // 엔진 가동 시간 탭 테이블 타입
  export interface EngineTimeTableProps {
    date?: string;
    engineTimeHistory?: string;
    mileage?: string;
    engineTime?: string;
    time?: string;
    jobTime?: string;
    idling?: string;
    fuel?: string;
    def?: string;
    battery?: string;
    wait?: string;
  }

  // 베터리 테이블 컴포넌트 타입
  export interface BatteryTableProps {
    date: string;
    mileage: string;
    keyOn: string;
    jobTime: string;
    time: string;
    wait: string;
    level: string;
    tank: string;
  }

  // 버전 정보 -> RMCU 테이블 컴포넌트 타입
  export interface RmcuInfoTable {
    author?: string;
    version?: string;
    serialNumber?: string;
    changedDate?: string;
    prevSerialNumber?: string;
    newSerialNumber?: string;
    note?: string;
  }

  // 충격 정보 탭 테이블 컴포넌트 타입
  export interface ImpactInformationTable {
    impactDate: string;
    rearFrontImact: string;
    siteImpact: string;
    upDownImpact: string;
  }

  // 일별 탭 -> 엔진 가동 테이블 컴포넌트 타입
  export interface DailyDrivingTimeTableColumnProps {
    division: string;
    daily: string;
    average?: string;
  }

  // 장비 상세 -> 버전 정보 탭 -> MCU 탭 테이블 컴포넌트 타입
  export interface MCUInfoTable {
    createdDate?: string;
    version?: string;
    serialNumber?: string;
    model?: string;
    receiveDate?: string;
    no?: string;
    changeDate?: string;
    diffMileageHour?: string;
    diffMileageSec?: string;
    author?: string;
    inputDate?: string;
  }

  // 장비 상세 -> 연료 소모량 테이블 컴포넌트 타입
  export interface FuelTableProps {
    date: string;
    mileage: string;
    engineTime: string;
    jobTime: string;
    time: string;
    level: string;
    fuelConsumption: string;
    tank: string;
  }

  // 장비상세 -> 제어기 탭 테이블 컴포넌트 타입
  export interface RMCUControllerInfo {
    typeNm?: string;
    version: string;
    serialNumber: string;
    maker: string;
    receivedDate: string;
  }

  // 장비상세 -> 제어기 탭 -> 이력 테이블 컴포넌트 타입
  export interface ControllerHistory {
    type: string;
    receivedDate: string;
    version: string;
    serialNumber: string;
    maker: string;
  }

  // 장비 상세 -> 서비스 이력 -> 서비스 정보 테이블 컴포넌트 타입
  export interface ServiceInfoTable {
    claim: string;
    activeTime: string;
    breakDownTime: string;
    maintainTime: string;
    claimType: string;
    note: string;
  }

  // 장비 상세 -> 서비스 이력 -> 생산 정보 테이블 컴포넌트 타입
  export interface ProductInfoTable {
    car: number;
    designModel: string;
    createdDate: string;
    outDate: string;
    customerDate: string;
    eqNumber: number;
    engineNumber: number;
    customerName: string;
  }

  // 장비 상세 -> 서비스 이력 -> 영업 정보 테이블 컴포넌트 타입
  export interface SalesInfoTable {
    contractNo: number;
    customerName: string;
    dealer: string;
    getDate: string;
    money: number;
    condition: string;
    requestDate: string;
  }

  // 장비 상세 -> 서비스 이력 -> 선택 사양 정보 테이블 컴포넌트 타입
  export interface SpecInfoTable {
    code: string;
    codeName: string;
    type: string;
    explain: string;
    frame: string;
    weight: string;
  }

  // 장비 상세 설정 탭 설정 시 드롭다운 타입
  export interface ValueDropdownProps {
    value: string;
    change: boolean;
    option: { key: string; value: string }[];
  }

  // 장비 상세 세팅 탭 -> 변경 인풋 타입
  export interface ValueInputProps {
    value: string;
    change: boolean;
    unit?: string;
  }

  // 장비 추가 팝업 테이블 컬럼 타입
  export interface EqAddTable {
    check?: boolean;
    model: string;
    code: string;
  }

  // MMC 작동 테이블 컬럼 타입
  export interface MMCApplyTable {
    breakdownDate: string;
    breakdownApplyDate: string;
    model: string;
    code: string;
    dealer: string;
    level: string;
    detail: string;
    symptoms: string;
    way: string;
    guide: string;
    message: string;
    done: string;
  }

  export interface MMCMaintenanceTable {
    breakdownDate: string;
    breakdownApplyDate: string;
    model: string;
    code: string;
    dealer: string;
    item: string;
    remaining: string;
    detail: string;
    symptoms: string;
    way: string;
    message: string;
    done: string;
  }

  // MMC 통계 테이블 컬럼 타입
  export interface MMCResultTable {
    charger: string;
    apply: string;
    done: string;
    id: string;
  }

  // MMC 화면 검색 기능 파라미터 타입
  export interface MMCSearchParams {
    country: string;
    faultPart: string;
    faultSeverity: {
      warning: boolean;
      service_soon: boolean;
      service_now: boolean;
      stop_soon: boolean;
      stop_now: boolean;
    };
    consumableStatus: {
      deadlineDue: boolean;
      deadlineExceeded: boolean;
    };
  }

  // MMC 화면 검색 기능 컴포넌트 타입
  export interface MMCSearchFilterProps {
    onSearch?: (params: MMCSearchParams) => void;
  }

  // 타임라인 정보
  export interface TimelineSegment {
    estimatedTime: string;
    arrivalTime: string;
    destination: string;
  }

  export interface TimelineItem {
    vehNum: string;
    driver: string;
    address: string;
    segments: TimelineSegment[];
  }

  // 장비 상세 필터 아이템 타입
  export type StatusFilterItem = {
    key: string;
    name: string;
    checked: boolean;
  };

  // 모니터링 차량 상세정보
  export type DetailInfoItem = {
    id: string;
    operationStatus: EqOperationStatus;
    breakdownStatus: EqBreakdownStatus;
    modelName: string; // 모델 명
    vehNum: string; // 차량 번호
    driver: string; // 운전자
    phoneNum: string; // 전화번호
    mileage: string; // mileage
    // 고장 정보
    faultInfo: {
      date: string; // 고장 일시
      status: string; // 진행 상태
      mileage: string; // mileage
      type: string; // 알림 타입
      code: string; // 고장 코드
      level: string; // 고장 심각도
      symptoms: string; // 고장 증상
    };
    // 소모품 교체 정보
    maintInfo: {
      name: string; // 소모품 항목
      interval: string; // 교체 주기
      status: string; // 상태
      time: string; // 사용 시간
      date: string; // 최근 교체 일자
      detail: string; // 교체 정보
    };
    // 딜러 정보
    dealerInfo: {
      name: string; // 딜러사명
      phoneNum: string; // 전화번호
    };
    // A/S 대리점 정보
    serviceInfo: {
      center: string; // 정비 센터
      phoneNum: string; // 전화번호
      address: string; // 주소
    };
  };

  export type TrackingDetailItem = {
    id: string;
    operationStatus: EqOperationStatus;
    breakdownStatus: EqBreakdownStatus;
    modelName: string; // 모델 명
    vehNum: string; // 차량 번호
    driver: string; // 운전자
    phoneNum: string; // 전화번호
    mileage: string; // mileage
    // 배차 정보
    dispatchInfo: {
      total: number; // 총 배차 수
      name: string; // 운송지명
      address: string; // 운송지 주소
      time: string; // 운송 완료 시간
    };
  };

  export interface TimelineProps {
    items: FilteredMapItem[];
    left?: string;
    bottom?: string;
    onHeightChange?: (height: number) => void;
    isChange?: boolean;
  }

  // 맵 마커 기본 정보
  export type MarkerMapItem = {
    id: string;
    latlng: {
      lat: number;
      lng: number;
    };
    operationStatus: EqOperationStatus;
    breakdownStatus: EqBreakdownStatus;
  };

  // MMC 장비 데이터
  export type MMCEqItem = {
    id: string;
    fleet: string; //플릿
    latlng: {
      lat: number;
      lng: number;
    };
    commType: string; //통신 방식
    machineType: string; //장비 타입
    modelName: string; //모델 명
    hogiNo: string; //호기
    equipmentId: string; //장비 번호
    custNo: string; //관리 번호
    location: string; //위치
    dealer: string; //대리점
    mileage: string; //작동 시간
    service: EqService; //서비스 상태
    //eqStat: EqStat; //장비 상태
    lastUpdate: string; //최종 업데이트 일시
  };

  // 장비상세 맵 검색 조회 타입
  export interface VehicleViewListProps {
    items: FilteredMapItem[];
  }

  // 장비 상세 맵 검색 리스트 타입
  export interface FilterItemProps {
    item?: FilteredMapItem;
    onItemClick?: (item: FilteredMapItem) => void;
  }

  // 연료 일일 평균 컴포넌트 타입
  export interface FuelProps {
    unit?: UnitType;
  }
}
