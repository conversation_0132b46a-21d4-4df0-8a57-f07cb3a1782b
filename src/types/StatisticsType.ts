export namespace StatisticsType {
  // 장비 상세 데이터
  export type FilteredMapItem = {
    id: string;
    fleet: string; //플릿
    latlng: {
      lat: number;
      lng: number;
    };
    commType: string; //통신 방식
    machineType: string; //장비 타입
    equipmentId: string; //장비 번호
    modelName: string; //모델 명
    plateNo: string; //차량 번호 (호기, hogiNo)
    serialNo: string; //관리 번호 (관리 번호, custNo)
    location: string; //위치
    dealer: string; //대리점
    driver: Driver; //운전자 정보
    mileage: string; //작동 시간
    service: EqService; //서비스 상태
    eqStat: EqStat; //장비 상태
    lastUpdate: string; //최종 업데이트 일시
    currAlarm?: string;
  };

  // 장비 상태
  export type EqStat = {
    operation: boolean; // 운행 중
    idle: boolean; // 유휴
    fault: boolean; // 고장
    required: boolean; // 소모품 교체 필요
    maint: boolean; // 정비 중
  };

  // 장비 서비스 상태
  export type EqService = {
    startDate: string;
    endDate: string;
    status: string;
  };

  // 운전자 정보
  export type Driver = {
    id: string; //운전자 ID
    name: string; //운전자 이름
    phone: string; //운전자 전화번호
  };

  // 연비 통계 페이지 테이블
  export interface FuelTable {
    month: string;
    mileage: string;
    engineActiveTime: string;
    workTime: string;
    driveTime: string;
    idleTime: string;
    fuel: string;
    fuelConsumption: string;
    fuelRate: string;
    fuelRate2: string;
  }

  // 장비별 충격횟수 테이블 로우 타입
  export interface RowProps {
    rowNum?: number;
    model?: string;
    vehicleNumber?: string;
    manageNumber?: string;
    hitNumber?: number;
    date?: string;
    name?: string;
    where?: string;
    hogi?: string;
    upDown?: boolean;
  }

  // 장비별 충격횟수 컴포넌트 타입
  export interface ImpactProps {
    eq?: RowProps[];
    user?: RowProps[];
  }

  // 통계 -> 장비 가동 시간 컴포넌트 타입
  export interface StatisticsImpactProps {
    title?: string;
    eq: (RowProps & {
      number?: string;
      where?: string;
      time?: string;
      hogi?: string;
      increase?: number; //증가분 값
    })[];
  }

  // 통계 -> 장비 가동추이 분석 -> 테이블 컴포넌트 타입.
  export interface EquipmentOperationTrendTable {
    ename: string;
    average?: string;
    january: string;
    february: string;
    march: string;
    april: string;
    may: string;
    june: string;
    july: string;
    august: string;
    september: string;
    october: string;
    november: string;
    december: string;
  }

  // 통계 -> 장비 가동 시간 분석 테이블 컴포넌트 타입.
  export interface EquipmentUptimeTableProps {
    time: string;
    engineActiveTime: React.ReactNode;
    region: string;
    activeDate: React.ReactNode;
    dayCount: string;
  }

  // 통계 -> 연료 통계 -> 연료 소비량 테이블 컴포넌트 타입.
  export interface FuelConsumptionTable {
    date: string;
    totalEngineActiveTime: string;
    totalWorkTime: string;
    totalDriveTime: string;
    totalFuelConsumption: string;
    workingHour: string;
    travelHour: string;
    fuelRate: string;
    fuelRate2: string;
  }

  // 통계 -> 배터리 소모율 테이블 컴포넌트 타입
  export interface BatteryConsumptionTable {
    date: string;
    totalEngineActiveTime: string;
    totalWorkTime: string;
    totalDriveTime: string;
    totalBatteryConsumption: string;
  }

  // 통계 -> 생산성/효율성 통계 -> 출력 팝업 테이블 컴포넌트 타입.
  export interface WorkerEqStatisticsTable {
    name: string;
    allEqTime: string;
    workTime: string;
    driveTime: string;
    restTime: string;
  }

  // 통계 -> 충격 -> 운전자 충격 이력 테이블 컬럼 컴포넌트 타입
  export interface PilotShockHistoryProps {
    date: string;
    pilot: string;
    frontRear: string;
    side: string;
    upDown: string;
    address: string;
    point: string;
  }

  // 통계 모데별 고장 알림 순위
  export interface RowStatProps {
    rowNum?: number;
    name: string;
    hitNumber: number;
    hogi?: string;
    upDown?: boolean;
    increase?: number;
  }

  // 가동율 통계 컴포넌트 타입
  export interface ChartSeriesData {
    name: string | undefined;
    type: string;
    data: number[];
  }

  //연료 통꼐 tab 타임
  export interface MenuTabPros {
    title: string;
  }

  // 연료 통계에 필터 사용하는 타입
  export interface FuelAndBatterySearchFilters {
    fleetSeqNo: string;
    region: string;
    country: string;
    sDealer: string;
    model: string;
    hogi: string;
    year: string;
    sDate: string;
    eDate: string;
    periodType: string;
  }

  export interface DataLineChat {
    date: string;
    value: number;
  }

  // 장비 가동 시간 분석 필터 사용하는 타입
  export interface EquipmentUpTimeSearchFilters {
    fleetSeqNo?: number | undefined;
    kubun1: string;
    region: string;
    country: string;
    sDealer: string | undefined;
    smodel: string | undefined;
  }

  // 접속 통계 -> 접속 로그 테이블 컴포넌트 타입
  export interface AccessLogTable {
    date: string;
    id: string;
    type: string;
    name: string;
    dealer: string;
    region: string;
    nation: string;
    authority: string;
    ip: string;
  }

  // 히트맵 데이터
  export type HeatmapFilteredMapItem = {
    item: FilteredMapItem & { path: { lat: number; lng: number }[] };
    checked: boolean;
  };

  // 대시보드에서 사용되는 라인 그래프 컴포넌트
  export interface GraphData {
    time: string;
    value: number;
  }

  export interface HeatmapVehicleViewListProps {
    items: HeatmapFilteredMapItem[];
    onRefreshClick?(items: HeatmapFilteredMapItem[]): void;
  }

  // 대시보드에서 사용되는 라인 그래프 컴포넌트
  export interface LineGraphProps {
    title: string;
    data: GraphData[];
    className?: string;
  }

  // 운영 효율 그래프 타입
  export interface DataPoint {
    day: string;
    value: number;
  }

  // 운영 효율 그래프 타입
  export interface DrivingEfficiencyProps {
    data: DataPoint[];
    title?: string;
    className?: string;
  }

  // 대시보드에서 사용되는 도넛 차트 컴포넌트 타입
  export interface ChartData {
    name: string;
    value: number;
  }

  // 대시보드에서 사용되는 도넛 차트 컴포넌트 타입
  export interface IndexChartProps {
    title: string;
    data: ChartData[];
  }

  export interface HeatmapFilterItemProps {
    item?:
      | {
          id: string; //ID
          machineType: string; //장비 타입
          eqStat: EqStat; //장비 상태
          modelName: string; //모델 명
          plateNo: string; //차량 번호 (호기, hogiNo)
          serialNo: string; //관리 번호
          location: string; //위치
          dealer: string; //대리점
          mileage: string; //작동 시간
        }
      | FilteredMapItem;
    checked?: boolean;
    onChecked?(id: string, checked: boolean): void;
  }

  // 통계 -> 리튬 베터리 테이블 컬럼 타입
  export interface LithiumBatteryTable {
    model: string;
    eq: string;
    no: string;
    mileage: string;
    sendDate: string;
    warranty: string;
    alarm: string[];
    errorType: string;
    errorLevel: string;
    errorDate: string;
  }
}
