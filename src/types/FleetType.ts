// Fleet 관련 타입들을 네임스페이스로 그룹화

export namespace FleetType {
  export interface BasicInformation {
    manufacture: string;
    modelName: string;
    trimName: string;
    yearOfManufacture: string;
    vehicleNumber: string;
    vinNumber: string;
  }

  export interface DealershipInformation {
    dealershipName: string;
    dealerName: string;
    contactNumber: string;
    country: { key: string; value: string };
    address: string;
    streetAddress: string;
    aptSuitUnit: string;
    city: string;
    state: { key: string; value: string };
    zipCode: string;
  }

  export interface VehicleInformation {
    truckType: { key: string; value: string };
    length: string;
    lengthUnit: { key: string; value: string };
    height: string;
    heightUnit: { key: string; value: string };
    width: string;
    widthUnit: { key: string; value: string };
    weight: string;
    weightUnit: { key: string; value: string };
    hazardousMaterials: string[];
  }

  export interface ListData {
    fleetData: ListStructure;
  }

  export interface ListStructure {
    name?: string;
    equipmentCnt?: number;
    driverCnt?: number;
    seqNo?: number;
  }

  export interface EqListData {
    fleetData: DataStructure;
  }

  export interface DataStructure {
    nation: string;
    model: string;
    unit: string;
    num: string;
    name: string;
    location: string;
    equipmentId: string;
    driver: string;
  }

  // 플릿 관리 관련 타입들

  //플릿에서 사용하는 데이터 구조
  export interface EqListProps {
    fleetData: FleetListStructure;
  }

  export interface FleetListStructure {
    name?: string;
    equipmentCnt?: number;
    driverCnt?: number;
    seqNo?: number;
  }

  //플릿에서 사용하는 데이터 구조
  export interface FleetEqListProps {
    fleetData: FleetDataStructure;
  }

  export interface FleetDataStructure {
    nation: string;
    model: string;
    unit: string;
    num: string;
    name: string;
    location: string;
    equipmentId: string;
    driver: string;
  }

  //플릿 사용 가능한 장비 목록
  export interface FleetAvailableListStructure {
    model: string;
    unit: string;
    equipmentId: string;
  }

  export interface DriverListStructure {
    driver?: string;
    fleet?: string;
    classification?: string;
    startD?: string;
    expiration?: string;
    start?: string;
    end?: string;
    eq?: string | number;
    send?: string;
  }

  export interface EqListStructure {
    no?: string | number;
    fleet?: string;
    model?: string;
    driver?: string;
    send?: string;
  }

  // 플릿 관리 / 운전자 관리 /충돌 관리
  export interface FleetCollisionListData {
    check?: boolean;
    no?: string;
    fleet?: string;
    unit?: string;
    impact?: string;
    accident?: string;
    notsent?: string;
    edit?: string;
  }

  // 플릿을 통계 자식 페이지게에 전달하는 데이터 타입
  export interface ExpendableStatisticsProps {
    fleetValue?: string;
    equipmentId?: string;
  }

  export interface FleetCheckListReportData {
    fleet?: string;
    date?: string;
    model?: string;
    unit?: string;
    driver?: string;
    info?: string;
  }

  export interface FleetWorkHistoryData {
    model: string;
    start: string;
    end: string;
    log: string;
  }

  export interface FleetAssignedEquipmentData {
    manufacturer: string;
    model: string;
    trim: string;
    year: string;
    vin: string;
    num: string;
  }

  export interface FleetDetailsEqtData {
    driver: string;
    id: string;
    sDate: string;
    eDate: string;
    sTime: string;
    eTime: string;
    count: string;
  }

  //fleet check list 타입
  export interface ChecklistItem {
    rn: number;
    questText: string;
    answerStr: string;
    criticalStr: string;
    lockStr: string;
    upDate: string;
    equipmentCount: number;
  }

  // 플릿 고장 지도 데이터
  export type BrokenMapItem = {
    id: string;
    latlng: {
      lat: number;
      lng: number;
    };
    issueDateTime: string;
    modelName: string;
    hogiNo: string;
    address: string;
  };
}
