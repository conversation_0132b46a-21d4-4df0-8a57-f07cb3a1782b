import { PopupProps } from '.';
import { CalendarViewProps } from '@/Common/Calendar.tsx';
import { EventSourceInput } from '@fullcalendar/core/index.js';

export namespace CalendarType {
  export interface CalendarInfo extends PopupProps {
    calendarType?: string;
    publicStatus?: string;
    countryList?: string[];
    colorType?: string;
    title?: string;
    context?: string;
    repeatType?: string;
    startDate?: string;
    startTime?: string;
    endDate?: string;
    endTime?: string;
    calendarId?: string;
  }

  export interface SelectItem {
    key: string;
    value: string;
  }

  export interface DayCalendarData {
    calendarId?: number;
    date?: string;
    title?: string;
    context?: string;
    colorType?: string;
    time?: string;
  }

  // 캘린더 타입
  export interface CustomCalendarProps {
    view?: CalendarViewProps;
    date?: Date;
    events?: EventSourceInput;
    clickMonthDay?: (day: string) => void;
    onRefresh?: () => void; // 부모로 전달할 refresh 콜백
  }
}
