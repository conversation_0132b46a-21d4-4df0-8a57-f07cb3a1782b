export namespace DispatchType {
  // 장비 상세 데이터
  export type FilteredMapItem = {
    id: string;
    fleet: string; //플릿
    latlng: {
      lat: number;
      lng: number;
    };
    commType: string; //통신 방식
    machineType: string; //장비 타입
    equipmentId: string; //장비 번호
    modelName: string; //모델 명
    plateNo: string; //차량 번호 (호기, hogiNo)
    serialNo: string; //관리 번호 (관리 번호, custNo)
    location: string; //위치
    dealer: string; //대리점
    driver: Driver; //운전자 정보
    mileage: string; //작동 시간
    service: EqService; //서비스 상태
    eqStat: EqStat; //장비 상태
    lastUpdate: string; //최종 업데이트 일시
    currAlarm?: string;
  };

  // 장비 상태
  export type EqStat = {
    operation: boolean; // 운행 중
    idle: boolean; // 유휴
    fault: boolean; // 고장
    required: boolean; // 소모품 교체 필요
    maint: boolean; // 정비 중
  };

  // 장비 서비스 상태
  export type EqService = {
    startDate: string;
    endDate: string;
    status: string;
  };

  // 운전자 정보
  export type Driver = {
    id: string; //운전자 ID
    name: string; //운전자 이름
    phone: string; //운전자 전화번호
  };
}
