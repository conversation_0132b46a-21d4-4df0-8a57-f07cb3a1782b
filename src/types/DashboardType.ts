import { EqBreakdownStatus, EqOperationStatus } from '.';
import { EquipmentType } from './EquipmentType';

export namespace DashboardType {
  // 맵 데이터
  export type FilteredMapItem = {
    id: string;
    fleet: string; //플릿
    latlng: {
      lat: number;
      lng: number;
    };
    operationStatus: EqOperationStatus;
    breakdownStatus: EqBreakdownStatus;
    lastUpdate: string; //최종 업데이트 일시
  };

  // 통계 카드
  export type StatCardData = {
    value: number;
    comparison: {
      value: string;
      isIncrease: boolean;
    };
  };

  // 통계 카드 컴포넌트
  export interface StatCardProps {
    title: string;
    value: number;
    unit: string;
    icon?: string;
    comparison: {
      text: string;
      value: string;
      isIncrease: boolean;
    };
    variant?: 'first' | 'second';
  }

  // 날씨 컴포넌트
  export type WeatherPageRow = {
    fleet: string;
    latlng: {
      lat: number;
      lng: number;
    };
    location: string;
    tempC: number;
    tempF: number;
    maxtempC: number;
    maxtempF: number;
    mintempC: number;
    mintempF: number;
    weatherDesc: string;
    warn: number;
  };

  // 날씨 컴포넌트
  export interface WeatherProps {
    weather?: WeatherPageRow[];
    className?: string;
  }

  // 공지사항 컴포넌트 타입
  export interface NoticeProps {
    auth: string;
    userId?: string;
    className?: string;
  }

  // 정보 카드 컴포넌트 타입
  // interface ComparisonData {
  //   text: string;
  //   value: string;
  //   isIncrease: boolean;
  // }

  // 정보 카드 컴포넌트 타입
  // export interface InfoSection {
  //   title: string;
  //   iconSrc?: string;
  //   iconAlt?: string;
  //   value: string;
  //   comparison: ComparisonData;
  // }

  // 정보 카드 컴포넌트 타입
  // export interface InfoCardProps {
  //   mainSection: InfoSection | undefined;
  //   subSection: InfoSection | undefined;
  // }

  // 연료비용을 표시하는 컴포넌트
  // export interface FuelCostProps {
  //   cost: number;
  //   comparison?: {
  //     text: string;
  //     value: number;
  //     isIncrease: boolean;
  //   };
  // }
}
