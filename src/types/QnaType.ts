export namespace QnaType {
  // 답변 상세 화면 컴포넌트 타입
  export interface AnswerDetailsProps {
    onRegisterClick: () => void;
  }

  // FAQ -> 테이블 컴포넌트 타입
  export interface FAQData {
    no: string;
    type: string;
    readPermission: string;
    langType: string;
    question: string;
    answer: string;
    date: string;
    topFixed?: boolean;
    fileName?: string[];
  }

  export interface AnswerDetailsProps {
    onRegisterClick: () => void;
  }
}
