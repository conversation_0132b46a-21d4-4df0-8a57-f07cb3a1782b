<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="source_sans_proregular" horiz-adv-x="497" >
<font-face units-per-em="1000" ascent="750" descent="-250" />
<missing-glyph horiz-adv-x="200" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="333" />
<glyph unicode=" "  horiz-adv-x="200" />
<glyph unicode="&#x09;" horiz-adv-x="200" />
<glyph unicode="&#xa0;" horiz-adv-x="200" />
<glyph unicode="!" horiz-adv-x="289" d="M116 198l-11 378l-2 94h83l-2 -94l-11 -378h-57zM145 -12q-25 0 -42.5 17.5t-17.5 44.5q0 28 17.5 46t42.5 18t42 -18t17 -46q0 -27 -17.5 -44.5t-41.5 -17.5z" />
<glyph unicode="&#x22;" horiz-adv-x="426" d="M99 431l-16 167l-3 92h88l-3 -92l-16 -167h-50zM276 431l-16 167l-3 92h88l-3 -92l-16 -167h-50z" />
<glyph unicode="#" d="M90 0l25 204h-80v57h87l18 148h-85v58h92l23 183h53l-23 -183h133l24 183h53l-24 -183h81v-58h-87l-18 -148h85v-57h-92l-25 -204h-53l24 204h-132l-25 -204h-54zM176 261h132l18 148h-132z" />
<glyph unicode="$" d="M222 -110v99q-97 9 -170 77l38 57q78 -67 155 -67q54 0 82.5 27.5t28.5 75.5q0 44 -28.5 76t-69.5 52t-82.5 42t-70 60t-28.5 92q0 66 39.5 111t105.5 55v101h60v-99q78 -9 142 -76l-44 -49q-31 30 -57.5 43.5t-64.5 13.5q-46 0 -73 -26t-27 -70q0 -29 15.5 -51.5t40 -38 t54 -29.5t59.5 -31t54.5 -38t40 -54.5t15.5 -77.5q0 -71 -42 -117.5t-113 -56.5v-101h-60z" />
<glyph unicode="%" horiz-adv-x="824" d="M184 254q-68 0 -108.5 55t-40.5 153q0 97 40.5 151.5t108.5 54.5t108.5 -54.5t40.5 -151.5q0 -98 -40.5 -153t-108.5 -55zM184 305q39 0 62.5 40.5t23.5 116.5t-23 115.5t-63 39.5t-63.5 -39.5t-23.5 -115.5t23.5 -116.5t63.5 -40.5zM203 -12l362 680h56l-362 -680h-56z M641 -12q-68 0 -108.5 55t-40.5 153q0 97 40.5 151.5t108.5 54.5t108.5 -54.5t40.5 -151.5q0 -98 -40.5 -153t-108.5 -55zM641 39q39 0 62.5 40.5t23.5 116.5t-23 115.5t-63 39.5t-63.5 -39.5t-23.5 -115.5t23.5 -116.5t63.5 -40.5z" />
<glyph unicode="&#x26;" horiz-adv-x="609" d="M232 -12q-89 0 -144.5 51t-55.5 131q0 60 34.5 103.5t94.5 85.5q-43 86 -43 154q0 66 42.5 110.5t109.5 44.5q60 0 94.5 -35.5t34.5 -94.5q0 -31 -11.5 -58t-36.5 -52.5t-44.5 -41.5t-55.5 -42q62 -97 166 -184q63 85 92 194h77q-42 -138 -115 -235q70 -48 123 -63 l-22 -68q-73 21 -151 75q-83 -75 -189 -75zM189 514q0 -50 31 -115q55 37 83.5 68t28.5 70q0 31 -15.5 51.5t-46.5 20.5q-36 0 -58.5 -27t-22.5 -68zM241 54q64 0 125 53q-105 92 -172 195q-82 -66 -82 -127q0 -54 37 -87.5t92 -33.5z" />
<glyph unicode="'" horiz-adv-x="249" d="M99 431l-16 167l-3 92h88l-3 -92l-16 -167h-50z" />
<glyph unicode="(" horiz-adv-x="303" d="M214 -176q-132 213 -132 454t132 454l51 -24q-115 -190 -115 -430t115 -430z" />
<glyph unicode=")" horiz-adv-x="303" d="M89 -176l-51 24q115 190 115 430t-115 430l51 24q132 -213 132 -454t-132 -454z" />
<glyph unicode="*" horiz-adv-x="418" d="M138 420l-40 29l57 94l-97 40l15 46l102 -25l9 108h49l9 -107l103 24l15 -46l-97 -40l56 -94l-39 -29l-71 86z" />
<glyph unicode="+" d="M216 104v195h-182v62h182v195h65v-195h182v-62h-182v-195h-65z" />
<glyph unicode="," horiz-adv-x="249" d="M67 -170l-20 48q43 19 67 51t23 71q-3 -1 -10 -1q-25 0 -42 15t-17 42q0 26 17.5 42t43.5 16q32 0 50.5 -26t18.5 -71q0 -64 -34.5 -112.5t-96.5 -74.5z" />
<glyph unicode="-" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="." horiz-adv-x="249" d="M125 -12q-25 0 -42.5 17.5t-17.5 44.5q0 28 17.5 46t42.5 18t42 -18t17 -46q0 -27 -17.5 -44.5t-41.5 -17.5z" />
<glyph unicode="/" horiz-adv-x="350" d="M10 -160l267 870h60l-267 -870h-60z" />
<glyph unicode="0" d="M249 -12q-97 0 -151 87t-54 246t54 244t151 85q96 0 150 -85t54 -244t-54 -246t-150 -87zM249 54q58 0 91 66t33 201q0 263 -124 263q-125 0 -125 -263q0 -135 33 -201t92 -66z" />
<glyph unicode="1" d="M79 0v68h146v470h-116v53q80 14 135 47h63v-570h132v-68h-360z" />
<glyph unicode="2" d="M40 0v49q83 83 129.5 132t90.5 103.5t62 95t18 78.5q0 57 -31 91t-90 34q-70 0 -136 -77l-47 47q89 97 193 97q88 0 139.5 -51.5t51.5 -136.5q0 -82 -61.5 -168t-198.5 -229q70 6 107 6h185v-71h-412z" />
<glyph unicode="3" d="M236 -12q-128 0 -210 90l42 54q72 -76 163 -76q58 0 95 32t37 85q0 127 -204 127v63q181 0 181 119q0 47 -29.5 74t-80.5 27q-71 0 -136 -65l-44 52q85 80 183 80q85 0 138 -43t53 -119q0 -108 -116 -152v-4q61 -14 99.5 -56.5t38.5 -105.5q0 -83 -60 -132.5t-150 -49.5z " />
<glyph unicode="4" d="M304 0v176h-287v54l273 408h92v-396h87v-66h-87v-176h-78zM104 242h200v185q0 38 5 123h-4q-2 -4 -52 -90z" />
<glyph unicode="5" d="M234 -12q-122 0 -209 87l40 54q73 -73 161 -73q60 0 100 40.5t40 105.5t-37 102.5t-99 37.5q-50 0 -109 -39l-44 28l21 307h319v-71h-247l-17 -189q49 26 98 26q87 0 142.5 -50.5t55.5 -149.5t-63.5 -157.5t-151.5 -58.5z" />
<glyph unicode="6" d="M268 -12q-98 0 -159 79.5t-61 227.5q0 180 69 267.5t174 87.5q91 0 154 -68l-46 -51q-42 50 -105 50q-73 0 -119 -64.5t-49 -206.5q30 37 70 58.5t79 21.5q85 0 133.5 -50.5t48.5 -146.5q0 -90 -55 -147.5t-134 -57.5zM268 53q48 0 79.5 39t31.5 101q0 64 -30 99.5 t-88 35.5q-33 0 -69 -21t-64 -63q8 -93 43.5 -142t96.5 -49z" />
<glyph unicode="7" d="M177 0q8 181 49.5 309t135.5 258h-318v71h411v-51q-108 -138 -146 -262.5t-47 -324.5h-85z" />
<glyph unicode="8" d="M250 -12q-90 0 -149.5 49.5t-59.5 125.5q0 108 120 172v4q-89 62 -89 147q0 73 51.5 118.5t129.5 45.5q82 0 131 -47.5t49 -122.5q0 -46 -26.5 -86.5t-61.5 -67.5v-4q51 -30 81 -67.5t30 -96.5q0 -73 -57.5 -121.5t-148.5 -48.5zM295 348q67 59 67 128q0 49 -30 81 t-81 32q-45 0 -74 -28.5t-29 -74.5q0 -51 37.5 -80.5t109.5 -57.5zM252 49q55 0 90 31t35 81q0 24 -10.5 44t-22 32.5t-41 28t-44.5 22t-55 22.5q-89 -59 -89 -139q0 -53 39 -87.5t98 -34.5z" />
<glyph unicode="9" d="M235 310q76 0 134 85q-17 190 -141 190q-47 0 -79 -39t-32 -101q0 -64 30 -99.5t88 -35.5zM205 -12q-92 0 -154 67l46 52q43 -51 105 -51q74 0 120.5 65t48.5 209q-30 -38 -70 -60t-80 -22q-84 0 -132.5 50.5t-48.5 146.5q0 90 55 147.5t133 57.5q98 0 159 -79t61 -228 q0 -180 -69 -267.5t-174 -87.5z" />
<glyph unicode=":" horiz-adv-x="249" d="M125 349q-25 0 -42.5 17.5t-17.5 44.5q0 28 17.5 46t42.5 18t42 -18t17 -46q0 -27 -17.5 -44.5t-41.5 -17.5zM125 -12q-25 0 -42.5 17.5t-17.5 44.5q0 28 17.5 46t42.5 18t42 -18t17 -46q0 -27 -17.5 -44.5t-41.5 -17.5z" />
<glyph unicode=";" horiz-adv-x="249" d="M125 349q-25 0 -42.5 17.5t-17.5 44.5q0 28 17.5 46t42.5 18t42 -18t17 -46q0 -27 -17.5 -44.5t-41.5 -17.5zM67 -170l-20 48q43 19 67 51t23 71q-3 -1 -10 -1q-25 0 -42 15t-17 42q0 26 17.5 42t43.5 16q32 0 50.5 -26t18.5 -71q0 -64 -34.5 -112.5t-96.5 -74.5z" />
<glyph unicode="&#x3c;" d="M463 131l-429 168v66l429 168v-71l-211 -78l-134 -50v-4l134 -50l211 -78v-71z" />
<glyph unicode="=" d="M34 406v62h429v-62h-429zM34 192v62h429v-62h-429z" />
<glyph unicode="&#x3e;" d="M34 131v71l211 78l134 50v4l-134 50l-211 78v71l429 -168v-66z" />
<glyph unicode="?" horiz-adv-x="425" d="M160 198q-6 43 5.5 82t33 68t43.5 55t38.5 55.5t16.5 58.5q0 42 -25.5 69.5t-71.5 27.5q-65 0 -115 -58l-47 43q73 83 172 83q76 0 121.5 -43t45.5 -117q0 -36 -16.5 -70t-39.5 -61t-45 -55t-35 -63.5t-9 -74.5h-72zM198 -12q-25 0 -42 17.5t-17 44.5q0 28 17 46t42 18 t42.5 -18t17.5 -46q0 -27 -17.5 -44.5t-42.5 -17.5z" />
<glyph unicode="@" horiz-adv-x="847" d="M403 -155q-153 0 -252.5 94.5t-99.5 263.5q0 196 122 319.5t295 123.5q150 0 239 -91t89 -240q0 -123 -62 -194.5t-138 -71.5q-42 0 -70 19.5t-33 56.5h-2q-58 -68 -122 -68q-53 0 -86.5 37.5t-33.5 103.5q0 86 54.5 159t137.5 73q54 0 80 -48h2l11 40h55l-39 -200 q-30 -117 54 -117q51 0 92.5 57.5t41.5 149.5q0 125 -72 202t-205 77q-140 0 -245 -108t-105 -277q0 -143 83.5 -224.5t214.5 -81.5q84 0 162 45l22 -49q-85 -51 -190 -51zM385 113q43 0 92 59l29 159q-26 42 -64 42q-56 0 -91.5 -54t-35.5 -116q0 -46 19 -68t51 -22z" />
<glyph unicode="A" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-31 94 -66 221h-4q-31 -117 -65 -221zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85z" />
<glyph unicode="B" horiz-adv-x="588" d="M90 0v656h195q225 0 225 -160q0 -50 -25.5 -89t-70.5 -54v-4q62 -12 98 -52t36 -105q0 -94 -67.5 -143t-181.5 -49h-209zM173 377h97q158 0 158 108q0 56 -38 80.5t-116 24.5h-101v-213zM173 66h114q179 0 179 129q0 118 -179 118h-114v-247z" />
<glyph unicode="C" horiz-adv-x="571" d="M338 -12q-126 0 -206 91.5t-80 248.5q0 155 81.5 247.5t209.5 92.5q108 0 180 -81l-45 -54q-59 62 -134 62q-93 0 -149.5 -71.5t-56.5 -193.5q0 -124 55 -196.5t148 -72.5q84 0 152 74l46 -52q-82 -95 -201 -95z" />
<glyph unicode="D" horiz-adv-x="615" d="M90 0v656h164q151 0 230.5 -84t79.5 -241q0 -158 -79 -244.5t-227 -86.5h-168zM173 68h75q113 0 171.5 68.5t58.5 194.5q0 125 -58 191t-172 66h-75v-520z" />
<glyph unicode="E" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388z" />
<glyph unicode="F" horiz-adv-x="494" d="M90 0v656h378v-70h-295v-222h250v-70h-250v-294h-83z" />
<glyph unicode="G" horiz-adv-x="617" d="M348 -12q-132 0 -214 91t-82 249q0 156 84 248t217 92q112 0 189 -81l-46 -54q-60 62 -141 62q-99 0 -158 -71.5t-59 -193.5q0 -125 57 -197t161 -72q76 0 118 39v171h-139v69h215v-276q-73 -76 -202 -76z" />
<glyph unicode="H" horiz-adv-x="652" d="M90 0v656h83v-275h305v275h84v-656h-84v309h-305v-309h-83z" />
<glyph unicode="I" horiz-adv-x="263" d="M90 0v656h83v-656h-83z" />
<glyph unicode="J" horiz-adv-x="480" d="M212 -12q-123 0 -181 105l60 42q41 -74 113 -74q53 0 79 33t26 107v455h84v-463q0 -91 -45 -148t-136 -57z" />
<glyph unicode="K" horiz-adv-x="579" d="M90 0v656h83v-329h3l273 329h94l-205 -250l237 -406h-93l-196 341l-113 -133v-208h-83z" />
<glyph unicode="L" horiz-adv-x="486" d="M90 0v656h83v-585h287v-71h-370z" />
<glyph unicode="M" horiz-adv-x="727" d="M90 0v656h100l126 -350q8 -22 23.5 -68t23.5 -69h4q8 23 22.5 69t22.5 68l124 350h101v-656h-78v361q0 35 11 194h-4l-52 -149l-124 -340h-55l-124 340l-52 149h-4q10 -145 10 -194v-361h-75z" />
<glyph unicode="N" horiz-adv-x="647" d="M90 0v656h86l237 -412l71 -136h4q-10 165 -10 209v339h79v-656h-86l-238 413l-71 135h-4q11 -145 11 -205v-343h-79z" />
<glyph unicode="O" horiz-adv-x="664" d="M332 -12q-125 0 -202.5 93t-77.5 250q0 155 77.5 246t202.5 91t203 -91t78 -246q0 -157 -78 -250t-203 -93zM332 61q88 0 141 73.5t53 196.5q0 122 -53 193t-141 71t-141 -71t-53 -193q0 -123 53 -196.5t141 -73.5z" />
<glyph unicode="P" horiz-adv-x="566" d="M90 0v656h187q246 0 246 -193q0 -100 -65 -151.5t-177 -51.5h-108v-260h-83zM173 328h98q86 0 127.5 33t41.5 102t-42 97.5t-131 28.5h-94v-261z" />
<glyph unicode="Q" horiz-adv-x="664" d="M332 57q88 0 141 74t53 200q0 122 -53 193t-141 71t-141 -71t-53 -193q0 -126 53 -200t141 -74zM533 -165q-87 0 -149.5 42.5t-92.5 113.5q-109 15 -174 106t-65 234q0 155 77.5 246t202.5 91t203 -91t78 -246q0 -140 -63 -230.5t-169 -108.5q45 -87 161 -87q37 0 69 9 l16 -64q-37 -15 -94 -15z" />
<glyph unicode="R" horiz-adv-x="569" d="M90 0v656h205q230 0 230 -184q0 -75 -39 -122t-109 -64l167 -286h-94l-158 277h-119v-277h-83zM173 345h110q159 0 159 127q0 62 -40 89.5t-119 27.5h-110v-244z" />
<glyph unicode="S" horiz-adv-x="534" d="M272 -12q-136 0 -230 97l50 58q79 -82 181 -82q64 0 100.5 29t36.5 78q0 24 -7 41.5t-25.5 31.5t-32.5 21.5t-44 21.5l-94 41q-133 56 -133 169q0 75 58.5 124.5t147.5 49.5q115 0 194 -81l-45 -54q-65 62 -149 62q-55 0 -88.5 -26t-33.5 -70q0 -22 8.5 -39.5t27.5 -31 t33 -20.5t40 -18l93 -40q65 -28 100 -68t35 -107q0 -80 -61 -133.5t-162 -53.5z" />
<glyph unicode="T" horiz-adv-x="536" d="M226 0v586h-198v70h480v-70h-198v-586h-84z" />
<glyph unicode="U" horiz-adv-x="645" d="M323 -12q-109 0 -172.5 67t-63.5 216v385h83v-387q0 -208 153 -208q155 0 155 208v387h80v-385q0 -283 -235 -283z" />
<glyph unicode="V" horiz-adv-x="515" d="M210 0l-210 656h89l105 -354q10 -32 30 -103.5t34 -113.5h4q14 44 35 118t28 99l105 354h85l-208 -656h-97z" />
<glyph unicode="W" horiz-adv-x="786" d="M162 0l-139 656h86l69 -357q6 -35 19 -105.5t19 -106.5h4q8 39 24 111t22 101l91 357h76l91 -357q8 -34 48 -212h4q24 142 37 212l69 357h80l-136 -656h-100l-99 395q-17 71 -32 149h-4q-6 -29 -17.5 -80.5t-15.5 -68.5l-97 -395h-99z" />
<glyph unicode="X" horiz-adv-x="513" d="M15 0l191 339l-178 317h92l89 -168q14 -25 51 -95h4q29 61 47 95l87 168h88l-179 -321l191 -335h-92l-96 177q-5 9 -24.5 45.5t-31.5 59.5h-4q-10 -20 -52 -105l-95 -177h-88z" />
<glyph unicode="Y" horiz-adv-x="476" d="M196 0v254l-197 402h89l85 -185q46 -105 63 -143h4q57 122 66 143l84 185h87l-197 -402v-254h-84z" />
<glyph unicode="Z" horiz-adv-x="539" d="M45 0v50l345 536h-314v70h418v-49l-346 -536h349v-71h-452z" />
<glyph unicode="[" horiz-adv-x="303" d="M94 -152v860h179v-47h-117v-766h117v-47h-179z" />
<glyph unicode="\" horiz-adv-x="350" d="M281 -160l-267 870h59l267 -870h-59z" />
<glyph unicode="]" horiz-adv-x="303" d="M31 -152v47h116v766h-116v47h178v-860h-178z" />
<glyph unicode="^" d="M60 284l152 386h73l152 -386h-72l-65 176l-49 133h-4l-50 -133l-65 -176h-72z" />
<glyph unicode="_" horiz-adv-x="500" d="M12 -126v55h476v-55h-476z" />
<glyph unicode="`" horiz-adv-x="542" d="M285 573l-157 153l58 55l141 -167z" />
<glyph unicode="a" horiz-adv-x="504" d="M194 -12q-62 0 -102 37t-40 101q0 79 71 122t227 60q0 122 -100 122q-69 0 -145 -52l-32 57q98 63 191 63q86 0 127.5 -52.5t41.5 -147.5v-298h-68l-7 58h-3q-85 -70 -161 -70zM218 54q59 0 132 65v135q-118 -15 -168 -44.5t-50 -77.5q0 -39 24 -58.5t62 -19.5z" />
<glyph unicode="b" horiz-adv-x="553" d="M297 -12q-71 0 -139 62h-3l-7 -50h-66v712h82v-194l-2 -88q77 68 152 68q93 0 143 -66t50 -181q0 -120 -61.5 -191.5t-148.5 -71.5zM283 57q61 0 100 52.5t39 140.5q0 179 -128 179q-59 0 -130 -66v-255q59 -51 119 -51z" />
<glyph unicode="c" horiz-adv-x="456" d="M274 -12q-100 0 -164 68t-64 186t68 187t166 69q79 0 142 -57l-42 -54q-48 43 -97 43q-66 0 -109 -52.5t-43 -135.5t41.5 -134.5t108.5 -51.5q60 0 114 49l36 -55q-70 -62 -157 -62z" />
<glyph unicode="d" horiz-adv-x="555" d="M248 -12q-93 0 -147 67t-54 187q0 115 62 185.5t149 70.5q67 0 136 -56l-4 83v187h83v-712h-68l-7 57h-3q-71 -69 -147 -69zM266 57q64 0 124 67v254q-57 51 -118 51q-59 0 -99.5 -52t-40.5 -134q0 -87 35.5 -136.5t98.5 -49.5z" />
<glyph unicode="e" horiz-adv-x="496" d="M279 -12q-100 0 -166.5 69t-66.5 185q0 114 65 185t153 71q92 0 143 -61.5t51 -166.5q0 -29 -3 -47h-328q5 -77 48.5 -123t113.5 -46q62 0 121 38l29 -54q-78 -50 -160 -50zM126 282h260q0 73 -31.5 112t-88.5 39q-53 0 -93 -40.5t-47 -110.5z" />
<glyph unicode="f" horiz-adv-x="292" d="M96 0v419h-66v62l66 5v77q0 76 35.5 118.5t106.5 42.5q42 0 81 -16l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82z" />
<glyph unicode="g" horiz-adv-x="504" d="M246 -224q-91 0 -146 34.5t-55 96.5t71 110v4q-43 27 -43 79q0 54 53 90v4q-63 51 -63 131q0 77 53 125t130 48q38 0 69 -12h169v-63h-100q40 -38 40 -100q0 -76 -51 -122.5t-127 -46.5q-39 0 -73 17q-31 -27 -31 -58q0 -53 86 -53h94q86 0 128 -28t42 -88 q0 -70 -68 -119t-178 -49zM246 209q43 0 73 32t30 84t-29.5 82.5t-73.5 30.5t-73.5 -30.5t-29.5 -82.5t30 -84t73 -32zM258 -167q67 0 110.5 29.5t43.5 69.5q0 33 -24.5 46.5t-73.5 13.5h-84q-35 0 -63 8q-50 -36 -50 -82q0 -39 37.5 -62t103.5 -23z" />
<glyph unicode="h" horiz-adv-x="544" d="M82 0v712h82v-194l-3 -100q84 80 162 80q148 0 148 -190v-308h-82v297q0 68 -22 99t-70 31q-35 0 -64 -17.5t-69 -57.5v-352h-82z" />
<glyph unicode="i" horiz-adv-x="246" d="M82 0v486h82v-486h-82zM124 586q-24 0 -40.5 15t-16.5 38q0 24 16.5 38.5t40.5 14.5t40.5 -14.5t16.5 -38.5q0 -23 -16.5 -38t-40.5 -15z" />
<glyph unicode="j" horiz-adv-x="247" d="M32 -217q-40 0 -72 13l17 62q24 -8 46 -8q34 0 47 23.5t13 71.5v541h82v-541q0 -162 -133 -162zM125 586q-24 0 -40.5 15t-16.5 38q0 24 16.5 38.5t40.5 14.5t40 -14.5t16 -38.5q0 -23 -16 -38t-40 -15z" />
<glyph unicode="k" horiz-adv-x="495" d="M82 0v712h81v-482h3l207 256h91l-163 -195l185 -291h-90l-142 234l-91 -106v-128h-81z" />
<glyph unicode="l" horiz-adv-x="255" d="M169 -12q-87 0 -87 110v614h82v-620q0 -36 23 -36q9 0 18 2l11 -62q-18 -8 -47 -8z" />
<glyph unicode="m" horiz-adv-x="829" d="M82 0v486h68l7 -70h3q75 82 151 82q102 0 134 -93q86 93 160 93q148 0 148 -190v-308h-82v297q0 68 -22 99t-68 31q-55 0 -122 -75v-352h-82v297q0 68 -22 99t-69 31q-54 0 -122 -75v-352h-82z" />
<glyph unicode="n" horiz-adv-x="547" d="M82 0v486h68l7 -70h3q82 82 163 82q148 0 148 -190v-308h-82v297q0 68 -22 99t-70 31q-35 0 -64 -17.5t-69 -57.5v-352h-82z" />
<glyph unicode="o" horiz-adv-x="542" d="M271 -12q-94 0 -159.5 69t-65.5 185q0 118 65.5 187t159.5 69t159.5 -69t65.5 -187q0 -116 -65.5 -185t-159.5 -69zM271 56q63 0 101.5 51.5t38.5 134.5q0 84 -38.5 136t-101.5 52q-62 0 -101 -52t-39 -136q0 -83 39 -134.5t101 -51.5z" />
<glyph unicode="p" horiz-adv-x="555" d="M82 -205v691h68l7 -56h3q81 68 155 68q92 0 142 -66.5t50 -181.5q0 -119 -61.5 -190.5t-148.5 -71.5q-63 0 -135 56l2 -85v-164h-82zM283 57q61 0 100 52.5t39 140.5q0 179 -128 179q-56 0 -130 -66v-255q61 -51 119 -51z" />
<glyph unicode="q" horiz-adv-x="555" d="M390 -205v173l4 88q-70 -68 -146 -68q-93 0 -147 67t-54 187q0 115 62 185.5t149 70.5q72 0 139 -58h2l8 46h66v-691h-83zM266 57q64 0 124 67v254q-57 51 -118 51q-59 0 -99.5 -52t-40.5 -134q0 -87 35.5 -136.5t98.5 -49.5z" />
<glyph unicode="r" horiz-adv-x="347" d="M82 0v486h68l7 -88h3q25 47 61 73.5t77 26.5q29 0 52 -10l-16 -72q-26 8 -47 8q-34 0 -67 -27t-56 -85v-312h-82z" />
<glyph unicode="s" horiz-adv-x="419" d="M209 -12q-100 0 -181 67l41 55q72 -58 143 -58q46 0 71 21.5t25 54.5q0 18 -9 32t-28 25.5t-33 18t-41 16.5q-32 11 -53 21t-44.5 27t-35.5 40t-12 52q0 60 45 99t122 39q83 0 151 -55l-39 -52q-57 43 -111 43q-43 0 -66 -20t-23 -50q0 -20 16.5 -36.5t31 -23.5t50.5 -20 q7 -3 10 -4q28 -11 43 -17t38.5 -19.5t35.5 -27t21.5 -35t9.5 -48.5q0 -62 -47.5 -103.5t-130.5 -41.5z" />
<glyph unicode="t" horiz-adv-x="338" d="M235 -12q-139 0 -139 162v269h-72v62l76 5l10 136h69v-136h131v-67h-131v-270q0 -47 17 -70.5t58 -23.5q20 0 55 13l16 -62q-54 -18 -90 -18z" />
<glyph unicode="u" horiz-adv-x="544" d="M224 -12q-149 0 -149 190v308h83v-297q0 -68 21.5 -99t69.5 -31q37 0 66 19t65 63v345h82v-486h-68l-7 76h-3q-75 -88 -160 -88z" />
<glyph unicode="v" horiz-adv-x="467" d="M187 0l-175 486h85l92 -276q14 -47 45 -143h4q4 13 20 64t24 79l92 276h81l-172 -486h-96z" />
<glyph unicode="w" horiz-adv-x="718" d="M159 0l-135 486h84l72 -281q12 -51 29 -135h4l33 135l75 281h80l76 -281q16 -61 34 -135h4q6 24 30 135l71 281h78l-130 -486h-100l-70 261q-7 26 -17.5 73t-15.5 67h-4q-23 -101 -34 -141l-68 -260h-96z" />
<glyph unicode="x" horiz-adv-x="446" d="M14 0l159 254l-147 232h89l65 -107q33 -57 48 -81h4q24 47 44 81l59 107h86l-147 -241l158 -245h-89l-71 113q-30 52 -53 87h-4q-21 -35 -49 -87l-66 -113h-86z" />
<glyph unicode="y" horiz-adv-x="467" d="M90 -209q-30 0 -57 10l16 65q23 -7 37 -7q75 0 110 104l11 36l-195 487h85l99 -269q20 -56 47 -137h4q8 26 23 76t18 61l87 269h80l-183 -526q-28 -80 -70.5 -124.5t-111.5 -44.5z" />
<glyph unicode="z" horiz-adv-x="425" d="M31 0v44l256 375h-228v67h332v-44l-256 -375h264v-67h-368z" />
<glyph unicode="{" horiz-adv-x="303" d="M228 -152q-62 0 -89.5 29t-27.5 104q0 37 4.5 98.5t4.5 94.5q0 77 -86 78v52q86 1 86 77q0 33 -4.5 95t-4.5 99q0 75 27.5 104t89.5 29h45v-47h-27q-39 0 -53.5 -20.5t-14.5 -70.5q0 -30 3 -88t3 -93q0 -49 -14 -74.5t-46 -34.5v-4q32 -9 46 -35t14 -74q0 -35 -3 -93 t-3 -88q0 -50 14.5 -70.5t53.5 -20.5h27v-47h-45z" />
<glyph unicode="|" horiz-adv-x="241" d="M92 -250v1000h58v-1000h-58z" />
<glyph unicode="}" horiz-adv-x="303" d="M31 -152v47h26q39 0 53.5 20.5t14.5 70.5q0 30 -2.5 88t-2.5 93q0 49 13.5 74.5t45.5 34.5v4q-32 9 -45.5 34.5t-13.5 74.5q0 35 2.5 93t2.5 88q0 50 -14.5 70.5t-53.5 20.5h-26v47h44q62 0 89.5 -29t27.5 -104q0 -37 -4.5 -99t-4.5 -95t20 -54.5t66 -22.5v-52 q-86 -1 -86 -78q0 -33 4.5 -94.5t4.5 -98.5q0 -75 -27.5 -104t-89.5 -29h-44z" />
<glyph unicode="~" d="M336 257q-34 0 -66 21t-60 42t-51 21q-44 0 -77 -61l-46 33q51 90 125 90q34 0 66 -21t60 -42t51 -21q44 0 77 61l46 -34q-51 -89 -125 -89z" />
<glyph unicode="&#xa1;" horiz-adv-x="289" d="M103 -184l2 94l11 378h57l11 -378l2 -94h-83zM145 372q-25 0 -42.5 18t-17.5 46q0 27 17.5 44.5t42.5 17.5q24 0 41.5 -17.5t17.5 -44.5q0 -28 -17 -46t-42 -18z" />
<glyph unicode="&#xa2;" d="M143 310q0 -67 31.5 -111t87.5 -57v335q-55 -13 -87 -57t-32 -110zM262 -33v104q-92 11 -146.5 73.5t-54.5 165.5q0 100 56.5 162t144.5 75v106h52v-103q73 -4 131 -58l-40 -52q-44 39 -91 42v-344q56 4 104 47l36 -52q-64 -57 -140 -63v-103h-52z" />
<glyph unicode="&#xa3;" d="M54 0v50q50 27 78 75t28 105q0 24 -7 55h-100v52l67 4h18q-27 87 -27 125q0 84 51 134t135 50q94 0 155 -75l-48 -47q-43 53 -101 53q-53 0 -82 -32t-29 -85q0 -39 24 -123h159v-56h-146q5 -30 5 -56q0 -90 -62 -154v-4h281v-71h-399z" />
<glyph unicode="&#xa4;" d="M70 103l-44 45l64 65q-36 49 -36 116q0 68 36 117l-64 66l44 45l68 -70q48 37 111 37q62 0 110 -37l68 70l44 -45l-65 -66q37 -50 37 -117q0 -66 -37 -116l65 -65l-44 -45l-68 69q-47 -38 -110 -38q-64 0 -111 38zM249 195q49 0 83.5 37.5t34.5 96.5t-34.5 96.5 t-83.5 37.5q-50 0 -84.5 -37.5t-34.5 -96.5t34.5 -96.5t84.5 -37.5z" />
<glyph unicode="&#xa5;" d="M207 0v158h-162v48h162v65h-162v47h141l-163 320h86l78 -171q11 -25 32 -72t28 -63h4q13 27 34.5 75.5t26.5 59.5l78 171h84l-164 -320h142v-47h-163v-65h163v-48h-163v-158h-82z" />
<glyph unicode="&#xa6;" horiz-adv-x="241" d="M92 291v459h58v-459h-58zM92 -250v464h58v-464h-58z" />
<glyph unicode="&#xa7;" d="M117 348q0 -31 21.5 -55t47.5 -36.5t68.5 -31.5t65.5 -32q60 28 60 85q0 26 -12.5 46t-35.5 35t-46.5 25.5t-55.5 24.5t-52 26q-61 -34 -61 -87zM236 -64q-107 0 -173 71l50 45q54 -52 123 -52q39 0 62 20t23 49q0 31 -28.5 54t-69 39.5t-81 36.5t-69 56.5t-28.5 87.5 q0 81 86 129q-32 34 -32 82q0 55 40 92.5t112 37.5q84 0 154 -57l-40 -53q-57 46 -111 46q-40 0 -59.5 -17.5t-19.5 -45.5q0 -25 20.5 -45t51.5 -33t66.5 -30.5t66.5 -36t51.5 -52t20.5 -76.5q0 -81 -85 -129q30 -35 30 -82q0 -59 -45.5 -98t-115.5 -39z" />
<glyph unicode="&#xa8;" horiz-adv-x="542" d="M175 587q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM367 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="744" d="M372 -11q-135 0 -229 92.5t-94 241.5q0 148 94 239t229 91t229 -91t94 -239q0 -149 -94 -241.5t-229 -92.5zM372 31q116 0 196 81.5t80 210.5q0 128 -80 208.5t-196 80.5t-196 -80.5t-80 -208.5q0 -129 80 -210.5t196 -81.5zM380 125q-78 0 -130 53.5t-52 144.5 q0 84 54.5 136.5t130.5 52.5q68 0 125 -57l-35 -39q-43 41 -87 41q-54 0 -88 -37.5t-34 -96.5q0 -66 32.5 -104.5t86.5 -38.5q51 0 102 44l30 -42q-66 -57 -135 -57z" />
<glyph unicode="&#xaa;" horiz-adv-x="345" d="M136 387q-44 0 -71.5 25.5t-27.5 68.5q0 52 47.5 80t151.5 39q-2 73 -62 73q-47 0 -101 -34l-23 43q69 42 133 42q115 0 115 -133v-196h-50l-7 37h-4q-51 -45 -101 -45zM154 436q39 0 82 40v85q-138 -15 -138 -75q0 -50 56 -50z" />
<glyph unicode="&#xab;" horiz-adv-x="429" d="M181 66l-136 155v62l136 155l36 -30l-118 -156l118 -158zM339 66l-136 155v62l136 155l36 -30l-118 -156l118 -158z" />
<glyph unicode="&#xac;" d="M397 104v195h-363v62h429v-257h-66z" />
<glyph unicode="&#xad;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#xae;" horiz-adv-x="423" d="M211 319q-79 0 -133.5 55.5t-54.5 141.5t54.5 141.5t133.5 55.5t134 -55.5t55 -141.5t-55 -141.5t-134 -55.5zM211 356q64 0 106.5 45t42.5 115t-42.5 115.5t-106.5 45.5q-63 0 -105.5 -45.5t-42.5 -115.5q0 -71 42 -115.5t106 -44.5zM139 417v203h76q78 0 78 -65 q0 -18 -10.5 -33.5t-26.5 -21.5l46 -83h-46l-35 70h-41v-70h-41zM180 519h26q43 0 43 34q0 33 -41 33h-28v-67z" />
<glyph unicode="&#xaf;" horiz-adv-x="542" d="M138 601v57h266v-57h-266z" />
<glyph unicode="&#xb0;" horiz-adv-x="331" d="M166 429q-52 0 -88.5 35.5t-36.5 91.5q0 58 36.5 93.5t88.5 35.5t88.5 -35.5t36.5 -93.5q0 -56 -36.5 -91.5t-88.5 -35.5zM166 475q33 0 54 23t21 58q0 37 -21 60t-54 23t-54 -23t-21 -60q0 -35 21 -58t54 -23z" />
<glyph unicode="&#xb1;" d="M216 127v177h-182v62h182v190h65v-190h182v-62h-182v-177h-65zM34 0v62h429v-62h-429z" />
<glyph unicode="&#xb2;" horiz-adv-x="367" d="M52 395v37q104 93 144.5 140.5t40.5 88.5q0 38 -20 60t-55 22q-45 0 -84 -57l-38 35q51 76 130 76q60 0 95 -33t35 -93q0 -27 -9 -52t-34.5 -57t-42 -50t-59.5 -62h165v-55h-268z" />
<glyph unicode="&#xb3;" horiz-adv-x="367" d="M180 383q-93 0 -145 76l43 33q39 -58 99 -58q33 0 55.5 19t22.5 51q0 33 -31.5 51t-89.5 18v41q49 0 77.5 19.5t28.5 50.5q0 28 -19.5 45t-51.5 17q-41 0 -79 -47l-39 34q56 64 128 64q52 0 88 -28.5t36 -76.5q0 -64 -68 -96q36 -9 60 -34t24 -62q0 -53 -40 -85t-99 -32z " />
<glyph unicode="&#xb4;" horiz-adv-x="542" d="M257 573l-42 41l141 167l58 -55z" />
<glyph unicode="&#xb5;" horiz-adv-x="562" d="M82 -179v665h82v-297q0 -130 89 -130q37 0 67.5 19.5t61.5 75.5v332h83q-1 -44 -2.5 -126.5t-2.5 -147.5t-1 -117q0 -39 36 -39q13 0 29 6l11 -62q-24 -12 -58 -12q-41 0 -62 22.5t-26 71.5h-2q-52 -92 -135 -92q-66 0 -94 47q1 -129 7 -216h-83z" />
<glyph unicode="&#xb6;" horiz-adv-x="560" d="M380 -80v736h84v-736h-84zM293 226q-115 0 -183.5 54t-68.5 163q0 112 65 162.5t176 50.5h44v-430h-33z" />
<glyph unicode="&#xb7;" horiz-adv-x="249" d="M125 259q-25 0 -42.5 17.5t-17.5 44.5q0 28 17.5 46t42.5 18t42 -18t17 -46q0 -27 -17.5 -44.5t-41.5 -17.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="542" d="M190 -226l-8 40q61 4 85 16t24 34q0 20 -17.5 31t-59.5 17l44 91h53l-29 -67q73 -17 73 -71q0 -83 -165 -91z" />
<glyph unicode="&#xb9;" horiz-adv-x="367" d="M172 395v308h-85v42q57 10 97 40h52v-390h-64z" />
<glyph unicode="&#xba;" horiz-adv-x="365" d="M182 387q-65 0 -108.5 45.5t-43.5 122.5q0 78 43.5 123.5t108.5 45.5q64 0 108 -45.5t44 -123.5q0 -77 -44 -122.5t-108 -45.5zM182 438q41 0 64.5 32.5t23.5 84.5q0 54 -23.5 86t-64.5 32t-65 -32t-24 -86q0 -52 24 -84.5t65 -32.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="429" d="M89 66l-35 28l118 158l-118 156l35 30l137 -155v-62zM247 66l-35 28l118 158l-118 156l35 30l137 -155v-62z" />
<glyph unicode="&#xbc;" horiz-adv-x="781" d="M148 266v308h-85v42q57 10 97 40h52v-390h-64zM180 -12l362 680h56l-362 -680h-56zM633 0v104h-178v33l164 253h72v-240h58v-46h-58v-104h-58zM522 150h111v70l4 109h-4l-50 -81z" />
<glyph unicode="&#xbd;" horiz-adv-x="808" d="M148 266v308h-85v42q57 10 97 40h52v-390h-64zM160 -12l362 680h56l-362 -680h-56zM493 0v37q104 93 144.5 140.5t40.5 88.5q0 38 -20 60t-55 22q-45 0 -84 -57l-38 35q51 76 130 76q60 0 95 -33t35 -93q0 -27 -9 -52t-34.5 -57t-42 -50t-59.5 -62h165v-55h-268z" />
<glyph unicode="&#xbe;" horiz-adv-x="796" d="M179 254q-93 0 -145 76l43 33q39 -58 99 -58q33 0 55.5 18.5t22.5 51.5t-31.5 51t-89.5 18v41q49 0 77.5 19.5t28.5 50.5q0 28 -19.5 45t-51.5 17q-41 0 -79 -47l-39 34q56 64 128 64q52 0 88 -28.5t36 -76.5q0 -64 -68 -96q36 -9 60 -34t24 -62q0 -53 -40 -85t-99 -32z M218 -12l362 680h56l-362 -680h-56zM648 0v104h-178v33l164 253h72v-240h58v-46h-58v-104h-58zM537 150h111v70l4 109h-4l-50 -81z" />
<glyph unicode="&#xbf;" horiz-adv-x="425" d="M215 -196q-76 0 -121.5 43t-45.5 117q0 36 16.5 70t39 61t45 55t35.5 63.5t8 74.5h73q6 -43 -5.5 -82t-33 -68t-43.5 -55t-38.5 -55.5t-16.5 -58.5q0 -42 25 -69t72 -27q65 0 114 57l48 -43q-76 -83 -172 -83zM227 372q-25 0 -42.5 18t-17.5 46q0 27 17.5 44.5t42.5 17.5 q24 0 41.5 -17.5t17.5 -44.5q0 -28 -17 -46t-42 -18z" />
<glyph unicode="&#xc0;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-31 94 -66 221h-4q-31 -117 -65 -221zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM287 697l-152 115l47 55l142 -128z" />
<glyph unicode="&#xc1;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-31 94 -66 221h-4q-31 -117 -65 -221zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM253 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xc2;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-31 94 -66 221h-4q-31 -117 -65 -221zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM122 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph unicode="&#xc3;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-31 94 -66 221h-4q-31 -117 -65 -221zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM345 709q-32 0 -59 17.5t-48.5 35.5t-40.5 18t-32 -17t-16 -49l-56 4q3 57 30 90t72 33q32 0 59 -17.5t48.5 -35.5t40.5 -18q40 0 48 66 l56 -4q-3 -58 -30 -90.5t-72 -32.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-31 94 -66 221h-4q-31 -117 -65 -221zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM167 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM373 715q-22 0 -35.5 14 t-13.5 35q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#xc5;" horiz-adv-x="544" d="M203 367l-31 -100h197l-31 100q-31 94 -66 221h-4q-31 -117 -65 -221zM3 0l222 656h94l222 -656h-89l-62 200h-239l-63 -200h-85zM270 699q-42 0 -69.5 25t-27.5 65q0 41 27.5 66t69.5 25t69.5 -25t27.5 -66q0 -40 -27.5 -65t-69.5 -25zM270 735q21 0 36 14.5t15 39.5 t-15 39.5t-36 14.5q-22 0 -37 -14.5t-15 -39.5t14.5 -39.5t37.5 -14.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="822" d="M290 376l-61 -118h172v332h-4q-53 -107 -107 -214zM8 0l344 656h411v-70h-278v-206h232v-71h-232v-238h288v-71h-372v191h-206l-99 -191h-88z" />
<glyph unicode="&#xc7;" horiz-adv-x="571" d="M256 -226l-8 40q61 4 85 16t24 34q0 20 -17.5 31t-59.5 17l37 77q-118 8 -191.5 98.5t-73.5 240.5q0 155 81.5 247.5t209.5 92.5q108 0 180 -81l-45 -54q-59 62 -134 62q-93 0 -149.5 -71.5t-56.5 -193.5q0 -124 55 -196.5t148 -72.5q84 0 152 74l46 -52 q-71 -82 -168 -93l-23 -54q73 -17 73 -71q0 -83 -165 -91z" />
<glyph unicode="&#xc8;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM301 697l-152 115l47 55l142 -128z" />
<glyph unicode="&#xc9;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM267 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xca;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM136 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph unicode="&#xcb;" horiz-adv-x="527" d="M90 0v656h378v-70h-295v-206h249v-71h-249v-238h305v-71h-388zM181 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM387 715q-22 0 -35.5 14t-13.5 35q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5 q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#xcc;" horiz-adv-x="263" d="M90 0v656h83v-656h-83zM148 697l-152 115l47 55l142 -128z" />
<glyph unicode="&#xcd;" horiz-adv-x="263" d="M90 0v656h83v-656h-83zM114 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xce;" horiz-adv-x="263" d="M90 0v656h83v-656h-83zM-17 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph unicode="&#xcf;" horiz-adv-x="263" d="M90 0v656h83v-656h-83zM28 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM234 715q-22 0 -35.5 14t-13.5 35q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#xd0;" horiz-adv-x="638" d="M33 321v43l79 4v288h164q151 0 230.5 -84t79.5 -241q0 -158 -79 -244.5t-227 -86.5h-168v321h-79zM195 68h75q113 0 171.5 68.5t58.5 194.5q0 125 -58 191t-172 66h-75v-220h149v-47h-149v-253z" />
<glyph unicode="&#xd1;" horiz-adv-x="647" d="M90 0v656h86l237 -412l71 -136h4q-10 165 -10 209v339h79v-656h-86l-238 413l-71 135h-4q11 -145 11 -205v-343h-79zM402 709q-32 0 -59 17.5t-48.5 35.5t-40.5 18t-32 -17t-16 -49l-56 4q3 57 30 90t72 33q32 0 59 -17.5t48.5 -35.5t40.5 -18q40 0 48 66l56 -4 q-3 -58 -30 -90.5t-72 -32.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="664" d="M332 -12q-125 0 -202.5 93t-77.5 250q0 155 77.5 246t202.5 91t203 -91t78 -246q0 -157 -78 -250t-203 -93zM332 61q88 0 141 73.5t53 196.5q0 122 -53 193t-141 71t-141 -71t-53 -193q0 -123 53 -196.5t141 -73.5zM349 697l-152 115l47 55l142 -128z" />
<glyph unicode="&#xd3;" horiz-adv-x="664" d="M332 -12q-125 0 -202.5 93t-77.5 250q0 155 77.5 246t202.5 91t203 -91t78 -246q0 -157 -78 -250t-203 -93zM332 61q88 0 141 73.5t53 196.5q0 122 -53 193t-141 71t-141 -71t-53 -193q0 -123 53 -196.5t141 -73.5zM315 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xd4;" horiz-adv-x="664" d="M332 -12q-125 0 -202.5 93t-77.5 250q0 155 77.5 246t202.5 91t203 -91t78 -246q0 -157 -78 -250t-203 -93zM332 61q88 0 141 73.5t53 196.5q0 122 -53 193t-141 71t-141 -71t-53 -193q0 -123 53 -196.5t141 -73.5zM184 725l105 113h86l105 -113l-36 -26l-110 93h-4 l-110 -93z" />
<glyph unicode="&#xd5;" horiz-adv-x="664" d="M332 -12q-125 0 -202.5 93t-77.5 250q0 155 77.5 246t202.5 91t203 -91t78 -246q0 -157 -78 -250t-203 -93zM332 61q88 0 141 73.5t53 196.5q0 122 -53 193t-141 71t-141 -71t-53 -193q0 -123 53 -196.5t141 -73.5zM407 709q-32 0 -59 17.5t-48.5 35.5t-40.5 18t-32 -17 t-16 -49l-56 4q3 57 30 90t72 33q32 0 59 -17.5t48.5 -35.5t40.5 -18q40 0 48 66l56 -4q-3 -58 -30 -90.5t-72 -32.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="664" d="M332 -12q-125 0 -202.5 93t-77.5 250q0 155 77.5 246t202.5 91t203 -91t78 -246q0 -157 -78 -250t-203 -93zM332 61q88 0 141 73.5t53 196.5q0 122 -53 193t-141 71t-141 -71t-53 -193q0 -123 53 -196.5t141 -73.5zM229 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5 q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM435 715q-22 0 -35.5 14t-13.5 35q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#xd7;" d="M94 126l-44 45l155 159l-155 158l44 45l155 -159l154 159l44 -45l-155 -158l155 -159l-44 -45l-154 160z" />
<glyph unicode="&#xd8;" horiz-adv-x="664" d="M335 -12q-104 0 -175 65l-64 -83l-46 36l70 91q-65 94 -65 234q0 155 77.5 246t202.5 91q104 0 176 -63l62 81l46 -35l-69 -89q66 -91 66 -231q0 -157 -78 -250t-203 -93zM141 331q0 -95 33 -163l288 373q-51 54 -127 54q-88 0 -141 -71t-53 -193zM335 61q88 0 141 73.5 t53 196.5q0 94 -33 160l-287 -374q50 -56 126 -56z" />
<glyph unicode="&#xd9;" horiz-adv-x="645" d="M323 -12q-109 0 -172.5 67t-63.5 216v385h83v-387q0 -208 153 -208q155 0 155 208v387h80v-385q0 -283 -235 -283zM340 697l-152 115l47 55l142 -128z" />
<glyph unicode="&#xda;" horiz-adv-x="645" d="M323 -12q-109 0 -172.5 67t-63.5 216v385h83v-387q0 -208 153 -208q155 0 155 208v387h80v-385q0 -283 -235 -283zM306 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xdb;" horiz-adv-x="645" d="M323 -12q-109 0 -172.5 67t-63.5 216v385h83v-387q0 -208 153 -208q155 0 155 208v387h80v-385q0 -283 -235 -283zM175 725l105 113h86l105 -113l-36 -26l-110 93h-4l-110 -93z" />
<glyph unicode="&#xdc;" horiz-adv-x="645" d="M323 -12q-109 0 -172.5 67t-63.5 216v385h83v-387q0 -208 153 -208q155 0 155 208v387h80v-385q0 -283 -235 -283zM220 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM426 715q-22 0 -35.5 14t-13.5 35 q0 22 13.5 35.5t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#xdd;" horiz-adv-x="476" d="M196 0v254l-197 402h89l85 -185q46 -105 63 -143h4q57 122 66 143l84 185h87l-197 -402v-254h-84zM221 697l-37 42l142 128l47 -55z" />
<glyph unicode="&#xde;" horiz-adv-x="583" d="M90 0v656h83v-110h118q242 0 242 -193q0 -101 -65 -152t-177 -51h-118v-150h-83zM173 218h108q86 0 127.5 32.5t41.5 102.5q0 69 -40.5 97t-128.5 28h-108v-260z" />
<glyph unicode="&#xdf;" horiz-adv-x="576" d="M387 -12q-74 0 -141 49l33 58q52 -43 106 -43q39 0 61.5 23.5t22.5 56.5q0 32 -20.5 55t-49.5 37.5t-57.5 30.5t-49 43.5t-20.5 65.5q0 30 14 58t30 46.5t30 45t14 54.5q0 39 -21.5 63t-61.5 24q-54 0 -83.5 -39.5t-29.5 -115.5v-500h-82v515q0 95 51.5 151t144.5 56 q76 0 119.5 -42t43.5 -104q0 -34 -14 -64t-31.5 -48.5t-31.5 -42.5t-14 -47q0 -26 20.5 -45.5t49.5 -34t57.5 -31.5t49 -49t20.5 -77q0 -64 -43.5 -106.5t-116.5 -42.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="504" d="M194 -12q-62 0 -102 37t-40 101q0 79 71 122t227 60q0 122 -100 122q-69 0 -145 -52l-32 57q98 63 191 63q86 0 127.5 -52.5t41.5 -147.5v-298h-68l-7 58h-3q-85 -70 -161 -70zM218 54q59 0 132 65v135q-118 -15 -168 -44.5t-50 -77.5q0 -39 24 -58.5t62 -19.5zM277 573 l-157 153l58 55l141 -167z" />
<glyph unicode="&#xe1;" horiz-adv-x="504" d="M194 -12q-62 0 -102 37t-40 101q0 79 71 122t227 60q0 122 -100 122q-69 0 -145 -52l-32 57q98 63 191 63q86 0 127.5 -52.5t41.5 -147.5v-298h-68l-7 58h-3q-85 -70 -161 -70zM218 54q59 0 132 65v135q-118 -15 -168 -44.5t-50 -77.5q0 -39 24 -58.5t62 -19.5zM249 573 l-42 41l141 167l58 -55z" />
<glyph unicode="&#xe2;" horiz-adv-x="504" d="M194 -12q-62 0 -102 37t-40 101q0 79 71 122t227 60q0 122 -100 122q-69 0 -145 -52l-32 57q98 63 191 63q86 0 127.5 -52.5t41.5 -147.5v-298h-68l-7 58h-3q-85 -70 -161 -70zM218 54q59 0 132 65v135q-118 -15 -168 -44.5t-50 -77.5q0 -39 24 -58.5t62 -19.5zM108 600 l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#xe3;" horiz-adv-x="504" d="M194 -12q-62 0 -102 37t-40 101q0 79 71 122t227 60q0 122 -100 122q-69 0 -145 -52l-32 57q98 63 191 63q86 0 127.5 -52.5t41.5 -147.5v-298h-68l-7 58h-3q-85 -70 -161 -70zM218 54q59 0 132 65v135q-118 -15 -168 -44.5t-50 -77.5q0 -39 24 -58.5t62 -19.5zM335 577 q-30 0 -56 21t-47.5 42t-39.5 21q-42 0 -47 -79l-55 3q2 62 27 99t74 37q30 0 56 -21t47.5 -42t40.5 -21q41 0 46 79l55 -4q-2 -62 -27 -98.5t-74 -36.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="504" d="M194 -12q-62 0 -102 37t-40 101q0 79 71 122t227 60q0 122 -100 122q-69 0 -145 -52l-32 57q98 63 191 63q86 0 127.5 -52.5t41.5 -147.5v-298h-68l-7 58h-3q-85 -70 -161 -70zM218 54q59 0 132 65v135q-118 -15 -168 -44.5t-50 -77.5q0 -39 24 -58.5t62 -19.5zM167 587 q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM359 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="504" d="M194 -12q-62 0 -102 37t-40 101q0 79 71 122t227 60q0 122 -100 122q-69 0 -145 -52l-32 57q98 63 191 63q86 0 127.5 -52.5t41.5 -147.5v-298h-68l-7 58h-3q-85 -70 -161 -70zM218 54q59 0 132 65v135q-118 -15 -168 -44.5t-50 -77.5q0 -39 24 -58.5t62 -19.5zM263 554 q-50 0 -80 28t-30 71t30 70.5t80 27.5t80 -27.5t30 -70.5t-30 -71t-80 -28zM263 591q25 0 41 17t16 45q0 27 -16.5 44t-40.5 17t-40.5 -17t-16.5 -44q0 -28 16 -45t41 -17z" />
<glyph unicode="&#xe6;" horiz-adv-x="785" d="M201 -12q-63 0 -103 37t-40 101q0 79 70.5 122t223.5 60q0 122 -99 122q-65 0 -141 -52l-33 57q98 63 184 63q106 0 139 -103q62 103 160 103q88 0 136.5 -61.5t48.5 -167.5q0 -28 -3 -46h-315q3 -75 45.5 -121.5t105.5 -46.5q55 0 118 40l30 -57q-78 -50 -158 -50 q-101 0 -171 91q-102 -91 -198 -91zM224 54q35 0 75 20t72 55q-17 39 -19 100v25q-113 -15 -163 -44.5t-50 -77.5q0 -39 23.5 -58.5t61.5 -19.5zM429 278h246q0 75 -29.5 115t-85.5 40q-51 0 -87.5 -42.5t-43.5 -112.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="456" d="M185 -226l-8 40q61 4 85 16t24 34q0 20 -17.5 31t-59.5 17l37 77q-89 10 -144.5 77t-55.5 176q0 118 68 187t166 69q79 0 142 -57l-42 -54q-48 43 -97 43q-66 0 -109 -52.5t-43 -135.5t41.5 -134.5t108.5 -51.5q60 0 114 49l36 -55q-59 -52 -131 -60l-23 -54 q73 -17 73 -71q0 -83 -165 -91z" />
<glyph unicode="&#xe8;" horiz-adv-x="496" d="M279 -12q-100 0 -166.5 69t-66.5 185q0 114 65 185t153 71q92 0 143 -61.5t51 -166.5q0 -29 -3 -47h-328q5 -77 48.5 -123t113.5 -46q62 0 121 38l29 -54q-78 -50 -160 -50zM126 282h260q0 73 -31.5 112t-88.5 39q-53 0 -93 -40.5t-47 -110.5zM278 573l-157 153l58 55 l141 -167z" />
<glyph unicode="&#xe9;" horiz-adv-x="496" d="M279 -12q-100 0 -166.5 69t-66.5 185q0 114 65 185t153 71q92 0 143 -61.5t51 -166.5q0 -29 -3 -47h-328q5 -77 48.5 -123t113.5 -46q62 0 121 38l29 -54q-78 -50 -160 -50zM126 282h260q0 73 -31.5 112t-88.5 39q-53 0 -93 -40.5t-47 -110.5zM250 573l-42 41l141 167 l58 -55z" />
<glyph unicode="&#xea;" horiz-adv-x="496" d="M279 -12q-100 0 -166.5 69t-66.5 185q0 114 65 185t153 71q92 0 143 -61.5t51 -166.5q0 -29 -3 -47h-328q5 -77 48.5 -123t113.5 -46q62 0 121 38l29 -54q-78 -50 -160 -50zM126 282h260q0 73 -31.5 112t-88.5 39q-53 0 -93 -40.5t-47 -110.5zM109 600l114 140h82 l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#xeb;" horiz-adv-x="496" d="M279 -12q-100 0 -166.5 69t-66.5 185q0 114 65 185t153 71q92 0 143 -61.5t51 -166.5q0 -29 -3 -47h-328q5 -77 48.5 -123t113.5 -46q62 0 121 38l29 -54q-78 -50 -160 -50zM126 282h260q0 73 -31.5 112t-88.5 39q-53 0 -93 -40.5t-47 -110.5zM168 587q-22 0 -36 14.5 t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM360 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#xec;" horiz-adv-x="246" d="M82 0v486h82v-486h-82zM137 573l-157 153l58 55l141 -167z" />
<glyph unicode="&#xed;" horiz-adv-x="246" d="M82 0v486h82v-486h-82zM109 573l-42 41l141 167l58 -55z" />
<glyph unicode="&#xee;" horiz-adv-x="246" d="M82 0v486h82v-486h-82zM-32 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#xef;" horiz-adv-x="246" d="M82 0v486h82v-486h-82zM27 587q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM219 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="545" d="M269 -12q-89 0 -152.5 64.5t-63.5 169.5q0 100 58.5 162.5t147.5 62.5q84 0 134 -66q-24 111 -107 192l-141 -73l-24 41l127 65q-52 40 -112 72l38 52q83 -45 138 -91l142 73l24 -41l-129 -66q136 -138 136 -348q0 -120 -59.5 -194.5t-156.5 -74.5zM271 56 q66 0 100.5 53.5t34.5 144.5q0 21 -2 55q-55 74 -133 74q-65 0 -102.5 -45t-37.5 -116q0 -73 41 -119.5t99 -46.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="547" d="M82 0v486h68l7 -70h3q82 82 163 82q148 0 148 -190v-308h-82v297q0 68 -22 99t-70 31q-35 0 -64 -17.5t-69 -57.5v-352h-82zM364 577q-30 0 -56 21t-47.5 42t-39.5 21q-42 0 -47 -79l-55 3q2 62 27 99t74 37q30 0 56 -21t47.5 -42t40.5 -21q41 0 46 79l55 -4 q-2 -62 -27 -98.5t-74 -36.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="542" d="M271 -12q-94 0 -159.5 69t-65.5 185q0 118 65.5 187t159.5 69t159.5 -69t65.5 -187q0 -116 -65.5 -185t-159.5 -69zM271 56q63 0 101.5 51.5t38.5 134.5q0 84 -38.5 136t-101.5 52q-62 0 -101 -52t-39 -136q0 -83 39 -134.5t101 -51.5zM285 573l-157 153l58 55l141 -167z " />
<glyph unicode="&#xf3;" horiz-adv-x="542" d="M271 -12q-94 0 -159.5 69t-65.5 185q0 118 65.5 187t159.5 69t159.5 -69t65.5 -187q0 -116 -65.5 -185t-159.5 -69zM271 56q63 0 101.5 51.5t38.5 134.5q0 84 -38.5 136t-101.5 52q-62 0 -101 -52t-39 -136q0 -83 39 -134.5t101 -51.5zM257 573l-42 41l141 167l58 -55z " />
<glyph unicode="&#xf4;" horiz-adv-x="542" d="M271 -12q-94 0 -159.5 69t-65.5 185q0 118 65.5 187t159.5 69t159.5 -69t65.5 -187q0 -116 -65.5 -185t-159.5 -69zM271 56q63 0 101.5 51.5t38.5 134.5q0 84 -38.5 136t-101.5 52q-62 0 -101 -52t-39 -136q0 -83 39 -134.5t101 -51.5zM116 600l114 140h82l114 -140 l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#xf5;" horiz-adv-x="542" d="M271 -12q-94 0 -159.5 69t-65.5 185q0 118 65.5 187t159.5 69t159.5 -69t65.5 -187q0 -116 -65.5 -185t-159.5 -69zM271 56q63 0 101.5 51.5t38.5 134.5q0 84 -38.5 136t-101.5 52q-62 0 -101 -52t-39 -136q0 -83 39 -134.5t101 -51.5zM343 577q-30 0 -56 21t-47.5 42 t-39.5 21q-42 0 -47 -79l-55 3q2 62 27 99t74 37q30 0 56 -21t47.5 -42t40.5 -21q41 0 46 79l55 -4q-2 -62 -27 -98.5t-74 -36.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="542" d="M271 -12q-94 0 -159.5 69t-65.5 185q0 118 65.5 187t159.5 69t159.5 -69t65.5 -187q0 -116 -65.5 -185t-159.5 -69zM271 56q63 0 101.5 51.5t38.5 134.5q0 84 -38.5 136t-101.5 52q-62 0 -101 -52t-39 -136q0 -83 39 -134.5t101 -51.5zM175 587q-22 0 -36 14.5t-14 35.5 t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM367 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#xf7;" d="M34 299v62h429v-62h-429zM249 96q-24 0 -39.5 15t-15.5 39q0 23 16 38t39 15t38.5 -15t15.5 -38q0 -24 -15.5 -39t-38.5 -15zM249 456q-24 0 -39.5 15t-15.5 39q0 23 16 38t39 15t38.5 -15t15.5 -38q0 -24 -15.5 -39t-38.5 -15z" />
<glyph unicode="&#xf8;" horiz-adv-x="542" d="M271 -12q-79 0 -139 49l-49 -60l-37 29l54 65q-54 70 -54 171q0 118 65.5 187t159.5 69q80 0 139 -50l50 61l36 -29l-54 -65q54 -70 54 -173q0 -116 -65.5 -185t-159.5 -69zM127 246q0 -67 23 -113l214 259q-37 40 -93 40q-63 0 -103.5 -52t-40.5 -134zM271 54 q63 0 103.5 51.5t40.5 133.5q0 67 -24 114l-213 -260q39 -39 93 -39z" />
<glyph unicode="&#xf9;" horiz-adv-x="544" d="M224 -12q-149 0 -149 190v308h83v-297q0 -68 21.5 -99t69.5 -31q37 0 66 19t65 63v345h82v-486h-68l-7 76h-3q-75 -88 -160 -88zM286 573l-157 153l58 55l141 -167z" />
<glyph unicode="&#xfa;" horiz-adv-x="544" d="M224 -12q-149 0 -149 190v308h83v-297q0 -68 21.5 -99t69.5 -31q37 0 66 19t65 63v345h82v-486h-68l-7 76h-3q-75 -88 -160 -88zM258 573l-42 41l141 167l58 -55z" />
<glyph unicode="&#xfb;" horiz-adv-x="544" d="M224 -12q-149 0 -149 190v308h83v-297q0 -68 21.5 -99t69.5 -31q37 0 66 19t65 63v345h82v-486h-68l-7 76h-3q-75 -88 -160 -88zM117 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#xfc;" horiz-adv-x="544" d="M224 -12q-149 0 -149 190v308h83v-297q0 -68 21.5 -99t69.5 -31q37 0 66 19t65 63v345h82v-486h-68l-7 76h-3q-75 -88 -160 -88zM176 587q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5t-35 -14.5zM368 587q-21 0 -35 14.5t-14 35.5 t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="467" d="M90 -209q-30 0 -57 10l16 65q23 -7 37 -7q75 0 110 104l11 36l-195 487h85l99 -269q20 -56 47 -137h4q8 26 23 76t18 61l87 269h80l-183 -526q-28 -80 -70.5 -124.5t-111.5 -44.5zM229 573l-42 41l141 167l58 -55z" />
<glyph unicode="&#xfe;" horiz-adv-x="555" d="M82 -205v917h82v-194l-1 -83q80 63 148 63q94 0 145 -66.5t51 -181.5q0 -119 -61.5 -190.5t-148.5 -71.5q-64 0 -134 54l1 -83v-164h-82zM283 57q61 0 100 52.5t39 140.5q0 179 -128 179q-56 0 -130 -66v-255q61 -51 119 -51z" />
<glyph unicode="&#xff;" horiz-adv-x="467" d="M90 -209q-30 0 -57 10l16 65q23 -7 37 -7q75 0 110 104l11 36l-195 487h85l99 -269q20 -56 47 -137h4q8 26 23 76t18 61l87 269h80l-183 -526q-28 -80 -70.5 -124.5t-111.5 -44.5zM147 587q-22 0 -36 14.5t-14 35.5t14 35.5t36 14.5q21 0 35 -14.5t14 -35.5t-14 -35.5 t-35 -14.5zM339 587q-21 0 -35 14.5t-14 35.5t14 35.5t35 14.5q22 0 36 -14.5t14 -35.5t-14 -35.5t-36 -14.5z" />
<glyph unicode="&#x152;" horiz-adv-x="847" d="M369 0q-149 0 -233 87t-84 244q0 156 84 240.5t237 84.5h415v-70h-278v-206h232v-71h-232v-238h288v-71h-429zM379 68h48v520h-48q-118 0 -179.5 -66t-61.5 -191q0 -126 61.5 -194.5t179.5 -68.5z" />
<glyph unicode="&#x153;" horiz-adv-x="839" d="M264 -12q-92 0 -155 68.5t-63 185.5q0 118 63.5 187t156.5 69q56 0 103 -32t73 -90q28 57 73 89.5t98 32.5q89 0 138.5 -61.5t49.5 -167.5q0 -28 -3 -46h-319q3 -75 46.5 -121.5t106.5 -46.5q57 0 120 40l30 -57q-78 -50 -160 -50q-57 0 -104 32t-75 89 q-57 -121 -179 -121zM266 56q60 0 98 51.5t38 134.5q0 84 -38 136t-98 52t-98 -52t-38 -136q0 -83 38 -134.5t98 -51.5zM479 278h250q0 74 -31 114.5t-87 40.5q-51 0 -88 -42.5t-44 -112.5z" />
<glyph unicode="&#x178;" horiz-adv-x="476" d="M196 0v254l-197 402h89l85 -185q46 -105 63 -143h4q57 122 66 143l84 185h87l-197 -402v-254h-84zM135 715q-21 0 -35 14t-14 35q0 22 14 35.5t35 13.5q22 0 35.5 -13.5t13.5 -35.5q0 -21 -13.5 -35t-35.5 -14zM341 715q-22 0 -35.5 14t-13.5 35q0 22 13.5 35.5 t35.5 13.5q21 0 35 -13.5t14 -35.5q0 -21 -14 -35t-35 -14z" />
<glyph unicode="&#x2c6;" horiz-adv-x="542" d="M116 600l114 140h82l114 -140l-35 -32l-118 113h-4l-118 -113z" />
<glyph unicode="&#x2dc;" horiz-adv-x="542" d="M343 577q-30 0 -56 21t-47.5 42t-39.5 21q-42 0 -47 -79l-55 3q2 62 27 99t74 37q30 0 56 -21t47.5 -42t40.5 -21q41 0 46 79l55 -4q-2 -62 -27 -98.5t-74 -36.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="440" />
<glyph unicode="&#x2001;" horiz-adv-x="880" />
<glyph unicode="&#x2002;" horiz-adv-x="440" />
<glyph unicode="&#x2003;" horiz-adv-x="880" />
<glyph unicode="&#x2004;" horiz-adv-x="293" />
<glyph unicode="&#x2005;" horiz-adv-x="220" />
<glyph unicode="&#x2006;" horiz-adv-x="146" />
<glyph unicode="&#x2007;" horiz-adv-x="146" />
<glyph unicode="&#x2008;" horiz-adv-x="110" />
<glyph unicode="&#x2009;" horiz-adv-x="176" />
<glyph unicode="&#x200a;" horiz-adv-x="48" />
<glyph unicode="&#x2010;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#x2011;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#x2012;" horiz-adv-x="311" d="M41 219v63h230v-63h-230z" />
<glyph unicode="&#x2013;" horiz-adv-x="480" d="M41 223v57h398v-57h-398z" />
<glyph unicode="&#x2014;" horiz-adv-x="800" d="M41 223v57h718v-57h-718z" />
<glyph unicode="&#x2018;" horiz-adv-x="249" d="M120 428q-30 0 -46.5 23.5t-16.5 66.5q0 117 106 178l24 -39q-76 -49 -76 -124q3 1 9 1q22 0 37.5 -13.5t15.5 -37.5q0 -25 -14.5 -40t-38.5 -15z" />
<glyph unicode="&#x2019;" horiz-adv-x="249" d="M86 431l-23 38q75 49 75 125q-3 -1 -8 -1q-22 0 -37.5 13.5t-15.5 36.5q0 26 14.5 41t38.5 15q29 0 46 -24t17 -67q0 -115 -107 -177z" />
<glyph unicode="&#x201a;" horiz-adv-x="249" d="M86 -145l-23 38q75 49 75 125q-3 -1 -8 -1q-22 0 -37.5 13.5t-15.5 36.5q0 26 14.5 41t38.5 15q29 0 46 -24t17 -67q0 -115 -107 -177z" />
<glyph unicode="&#x201c;" horiz-adv-x="426" d="M120 428q-30 0 -46.5 23.5t-16.5 66.5q0 117 106 178l24 -39q-76 -49 -76 -124q3 1 9 1q22 0 37.5 -13.5t15.5 -37.5q0 -25 -14.5 -40t-38.5 -15zM297 428q-30 0 -46.5 23.5t-16.5 66.5q0 117 106 178l24 -39q-76 -49 -76 -124q3 1 9 1q22 0 37.5 -13.5t15.5 -37.5 q0 -25 -14.5 -40t-38.5 -15z" />
<glyph unicode="&#x201d;" horiz-adv-x="426" d="M86 431l-23 38q75 49 75 125q-3 -1 -8 -1q-22 0 -37.5 13.5t-15.5 36.5q0 26 14.5 41t38.5 15q29 0 46 -24t17 -67q0 -115 -107 -177zM263 431l-23 38q75 49 75 125q-3 -1 -8 -1q-22 0 -37.5 13.5t-15.5 36.5q0 26 14.5 41t38.5 15q29 0 46 -24t17 -67q0 -115 -107 -177z " />
<glyph unicode="&#x201e;" horiz-adv-x="426" d="M86 -145l-23 38q75 49 75 125q-3 -1 -8 -1q-22 0 -37.5 13.5t-15.5 36.5q0 26 14.5 41t38.5 15q29 0 46 -24t17 -67q0 -115 -107 -177zM263 -145l-23 38q75 49 75 125q-3 -1 -8 -1q-22 0 -37.5 13.5t-15.5 36.5q0 26 14.5 41t38.5 15q29 0 46 -24t17 -67 q0 -115 -107 -177z" />
<glyph unicode="&#x2022;" horiz-adv-x="304" d="M152 143q-46 0 -79 33.5t-33 86.5t33 87t79 34t79 -34t33 -87t-33 -86.5t-79 -33.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="948" d="M154 -12q-25 0 -42.5 17.5t-17.5 44.5q0 28 17.5 46t42.5 18t42 -18t17 -46q0 -27 -17.5 -44.5t-41.5 -17.5zM489 -12q-25 0 -42.5 17.5t-17.5 44.5q0 28 17.5 46t42.5 18t42 -18t17 -46q0 -27 -17.5 -44.5t-41.5 -17.5zM824 -12q-25 0 -42.5 17.5t-17.5 44.5 q0 28 17.5 46t42.5 18t42 -18t17 -46q0 -27 -17.5 -44.5t-41.5 -17.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="176" />
<glyph unicode="&#x2039;" horiz-adv-x="271" d="M181 66l-136 155v62l136 155l36 -30l-118 -156l118 -158z" />
<glyph unicode="&#x203a;" horiz-adv-x="271" d="M89 66l-35 28l118 158l-118 156l35 30l137 -155v-62z" />
<glyph unicode="&#x205f;" horiz-adv-x="220" />
<glyph unicode="&#x20ac;" d="M319 -12q-91 0 -153 65.5t-79 181.5h-64v43l59 4q-1 12 -1 36q0 22 1 32h-59v44l64 5q17 118 82.5 184.5t163.5 66.5q87 0 149 -78l-49 -47q-47 59 -103 59q-65 0 -105.5 -48.5t-53.5 -136.5h254v-49h-258q-1 -9 -1 -29q0 -26 1 -38h218v-48h-213q13 -86 52 -133.5 t100 -47.5q66 0 118 71l49 -44q-71 -93 -172 -93z" />
<glyph unicode="&#x2122;" horiz-adv-x="637" d="M102 366v256h-99v54h259v-54h-100v-256h-60zM310 366v310h73l47 -116l28 -78h4l28 78l46 116h72v-310h-55v137l7 105h-4l-73 -194h-47l-73 194h-4l7 -105v-137h-56z" />
<glyph unicode="&#x25fc;" horiz-adv-x="485" d="M0 485h485v-485h-485v485z" />
<glyph unicode="&#xfb01;" horiz-adv-x="556" d="M96 0v419h-66v62l66 5v77q0 76 35.5 118.5t106.5 42.5q42 0 81 -16l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82zM392 0v486h82v-486h-82zM434 586q-24 0 -40.5 15t-16.5 38q0 24 16.5 38.5t40.5 14.5t40.5 -14.5t16.5 -38.5q0 -23 -16.5 -38 t-40.5 -15z" />
<glyph unicode="&#xfb02;" horiz-adv-x="547" d="M96 0v419h-66v62l66 5v77q0 76 35.5 118.5t106.5 42.5q42 0 81 -16l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82zM461 -12q-87 0 -87 110v614h82v-620q0 -36 23 -36q9 0 18 2l11 -62q-18 -8 -47 -8z" />
<glyph unicode="&#xfb03;" horiz-adv-x="823" d="M381 0v419h-203v-419h-82v419h-66v62l66 5v64q0 77 38.5 120.5t112.5 43.5q50 0 89 -18l-17 -62q-30 13 -65 13q-36 0 -56 -25.5t-20 -73.5v-62h203v77q0 76 35.5 118.5t106.5 42.5q42 0 81 -16l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82zM659 0 v486h82v-486h-82zM701 586q-24 0 -40.5 15t-16.5 38q0 24 16.5 38.5t40.5 14.5t40.5 -14.5t16.5 -38.5q0 -23 -16.5 -38t-40.5 -15z" />
<glyph unicode="&#xfb04;" horiz-adv-x="832" d="M381 0v419h-203v-419h-82v419h-66v62l66 5v64q0 77 38.5 120.5t112.5 43.5q50 0 89 -18l-17 -62q-30 13 -65 13q-36 0 -56 -25.5t-20 -73.5v-62h203v77q0 76 35.5 118.5t106.5 42.5q42 0 81 -16l-18 -63q-27 12 -55 12q-68 0 -68 -94v-77h103v-67h-103v-419h-82zM746 -12 q-87 0 -87 110v614h82v-620q0 -36 23 -36q9 0 18 2l11 -62q-18 -8 -47 -8z" />
<hkern u1="&#x2f;" u2="&#xef;" k="-65" />
<hkern u1="&#x2f;" u2="&#xee;" k="-65" />
<hkern u1="&#x2f;" u2="&#xec;" k="-15" />
<hkern u1="F" u2="&#xef;" k="-36" />
<hkern u1="F" u2="&#xee;" k="-29" />
<hkern u1="V" u2="&#xef;" k="-64" />
<hkern u1="V" u2="&#xee;" k="-53" />
<hkern u1="V" u2="&#xed;" k="-13" />
<hkern u1="V" u2="&#xec;" k="-20" />
<hkern u1="x" u2="&#x3b;" k="-7" />
<hkern u1="x" u2="&#x2c;" k="-7" />
<hkern g1="backslash" 	g2="Eth" 	k="29" />
<hkern g1="backslash" 	g2="g" 	k="-33" />
<hkern g1="backslash" 	g2="j" 	k="-73" />
<hkern g1="backslash" 	g2="T" 	k="85" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="29" />
<hkern g1="backslash" 	g2="v" 	k="20" />
<hkern g1="backslash" 	g2="V" 	k="53" />
<hkern g1="backslash" 	g2="w" 	k="10" />
<hkern g1="backslash" 	g2="W" 	k="29" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="-13" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="73" />
<hkern g1="exclamdown" 	g2="j" 	k="-33" />
<hkern g1="exclamdown" 	g2="V" 	k="32" />
<hkern g1="exclamdown" 	g2="W" 	k="16" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="periodcentered" 	g2="T" 	k="64" />
<hkern g1="periodcentered" 	g2="V" 	k="26" />
<hkern g1="periodcentered" 	g2="Y,Yacute,Ydieresis" 	k="58" />
<hkern g1="periodcentered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="24" />
<hkern g1="periodcentered" 	g2="S" 	k="24" />
<hkern g1="periodcentered" 	g2="x" 	k="20" />
<hkern g1="periodcentered" 	g2="X" 	k="26" />
<hkern g1="periodcentered" 	g2="Z" 	k="38" />
<hkern g1="questiondown" 	g2="j" 	k="-59" />
<hkern g1="questiondown" 	g2="T" 	k="87" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="40" />
<hkern g1="questiondown" 	g2="V" 	k="58" />
<hkern g1="questiondown" 	g2="W" 	k="40" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="89" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="62" />
<hkern g1="questiondown" 	g2="S" 	k="35" />
<hkern g1="questiondown" 	g2="X" 	k="42" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="35" />
<hkern g1="questiondown" 	g2="f,uniFB01,uniFB02" 	k="62" />
<hkern g1="questiondown" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="21" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="44" />
<hkern g1="slash" 	g2="g" 	k="10" />
<hkern g1="slash" 	g2="j" 	k="-25" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="24" />
<hkern g1="slash" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="slash" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-25" />
<hkern g1="slash" 	g2="J" 	k="80" />
<hkern g1="slash" 	g2="t" 	k="-9" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="t" 	k="14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="T" 	k="24" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="V" 	k="16" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="W" 	k="4" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="Y,Yacute,Ydieresis" 	k="24" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="question" 	k="12" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="16" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="54" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="26" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="56" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="94" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="periodcentered" 	k="24" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="-23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="15" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v" 	k="7" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="x" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="7" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Z" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="67" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="40" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="registered" 	k="80" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="52" />
<hkern g1="B" 	g2="t" 	k="10" />
<hkern g1="B" 	g2="T" 	k="24" />
<hkern g1="B" 	g2="V" 	k="6" />
<hkern g1="B" 	g2="W" 	k="4" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="B" 	g2="asterisk" 	k="30" />
<hkern g1="B" 	g2="periodcentered" 	k="20" />
<hkern g1="B" 	g2="v" 	k="14" />
<hkern g1="B" 	g2="w" 	k="10" />
<hkern g1="B" 	g2="x" 	k="10" />
<hkern g1="B" 	g2="X" 	k="4" />
<hkern g1="B" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="B" 	g2="Z" 	k="4" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="B" 	g2="trademark" 	k="20" />
<hkern g1="B" 	g2="J" 	k="16" />
<hkern g1="B" 	g2="S" 	k="14" />
<hkern g1="c,ccedilla" 	g2="t" 	k="10" />
<hkern g1="c,ccedilla" 	g2="T" 	k="20" />
<hkern g1="c,ccedilla" 	g2="V" 	k="14" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="24" />
<hkern g1="c,ccedilla" 	g2="periodcentered" 	k="14" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="c,ccedilla" 	g2="v" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-8" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-6" />
<hkern g1="c,ccedilla" 	g2="registered" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="g" 	k="10" />
<hkern g1="c,ccedilla" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="21" />
<hkern g1="c,ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="20" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="27" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="V" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="7" />
<hkern g1="C,Ccedilla" 	g2="periodcentered" 	k="52" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="X" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="registered" 	k="-10" />
<hkern g1="C,Ccedilla" 	g2="trademark" 	k="-24" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="S" 	k="24" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="26" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="24" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="26" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="question" 	k="7" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk" 	k="34" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="periodcentered" 	k="4" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="-5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="-5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="x" 	k="7" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="-5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="17" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="g" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="hyphen,uni00AD,endash,emdash" 	k="-10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="24" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="x" 	k="31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f" 	g2="T" 	k="-46" />
<hkern g1="f" 	g2="V" 	k="-66" />
<hkern g1="f" 	g2="W" 	k="-46" />
<hkern g1="f" 	g2="Y,Yacute,Ydieresis" 	k="-59" />
<hkern g1="f" 	g2="question" 	k="-26" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-34" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="50" />
<hkern g1="f" 	g2="periodcentered" 	k="20" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="f" 	g2="s" 	k="4" />
<hkern g1="f" 	g2="v" 	k="-13" />
<hkern g1="f" 	g2="x" 	k="4" />
<hkern g1="f" 	g2="X" 	k="-33" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-34" />
<hkern g1="f" 	g2="quotedbl,quotesingle" 	k="-40" />
<hkern g1="f" 	g2="backslash" 	k="-62" />
<hkern g1="f" 	g2="registered" 	k="-75" />
<hkern g1="f" 	g2="trademark" 	k="-76" />
<hkern g1="f" 	g2="g" 	k="14" />
<hkern g1="f" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f" 	g2="hyphen,uni00AD,endash,emdash" 	k="14" />
<hkern g1="f" 	g2="j" 	k="10" />
<hkern g1="f" 	g2="z" 	k="14" />
<hkern g1="f" 	g2="parenright,bracketright,braceright" 	k="-49" />
<hkern g1="f" 	g2="exclam" 	k="-14" />
<hkern g1="f" 	g2="slash" 	k="14" />
<hkern g1="F" 	g2="t" 	k="10" />
<hkern g1="F" 	g2="V" 	k="-4" />
<hkern g1="F" 	g2="W" 	k="-4" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="72" />
<hkern g1="F" 	g2="periodcentered" 	k="14" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="34" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="F" 	g2="s" 	k="20" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="F" 	g2="v" 	k="20" />
<hkern g1="F" 	g2="w" 	k="16" />
<hkern g1="F" 	g2="x" 	k="26" />
<hkern g1="F" 	g2="X" 	k="24" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="F" 	g2="Z" 	k="30" />
<hkern g1="F" 	g2="registered" 	k="-20" />
<hkern g1="F" 	g2="trademark" 	k="-38" />
<hkern g1="F" 	g2="J" 	k="138" />
<hkern g1="F" 	g2="S" 	k="20" />
<hkern g1="F" 	g2="g" 	k="24" />
<hkern g1="F" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="F" 	g2="z" 	k="30" />
<hkern g1="F" 	g2="slash" 	k="69" />
<hkern g1="F" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="germandbls" 	g2="t" 	k="16" />
<hkern g1="germandbls" 	g2="question" 	k="16" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="56" />
<hkern g1="germandbls" 	g2="f,uniFB01,uniFB02" 	k="4" />
<hkern g1="germandbls" 	g2="v" 	k="16" />
<hkern g1="germandbls" 	g2="w" 	k="14" />
<hkern g1="germandbls" 	g2="x" 	k="-6" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="48" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="67" />
<hkern g1="germandbls" 	g2="backslash" 	k="26" />
<hkern g1="germandbls" 	g2="registered" 	k="35" />
<hkern g1="g" 	g2="T" 	k="26" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="g" 	g2="question" 	k="36" />
<hkern g1="g" 	g2="asterisk" 	k="33" />
<hkern g1="g" 	g2="periodcentered" 	k="8" />
<hkern g1="g" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="g" 	g2="v" 	k="4" />
<hkern g1="g" 	g2="w" 	k="4" />
<hkern g1="g" 	g2="y,yacute,ydieresis" 	k="-15" />
<hkern g1="g" 	g2="registered" 	k="-14" />
<hkern g1="g" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="g" 	g2="j" 	k="-37" />
<hkern g1="g" 	g2="z" 	k="14" />
<hkern g1="g" 	g2="parenright,bracketright,braceright" 	k="-14" />
<hkern g1="g" 	g2="slash" 	k="-48" />
<hkern g1="G" 	g2="T" 	k="20" />
<hkern g1="G" 	g2="V" 	k="14" />
<hkern g1="G" 	g2="W" 	k="4" />
<hkern g1="G" 	g2="asterisk" 	k="28" />
<hkern g1="G" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="G" 	g2="registered" 	k="-8" />
<hkern g1="G" 	g2="trademark" 	k="-12" />
<hkern g1="J" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="24" />
<hkern g1="J" 	g2="J" 	k="40" />
<hkern g1="k" 	g2="t" 	k="21" />
<hkern g1="k" 	g2="T" 	k="35" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="k" 	g2="question" 	k="12" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="26" />
<hkern g1="k" 	g2="asterisk" 	k="21" />
<hkern g1="k" 	g2="colon,semicolon" 	k="-13" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-13" />
<hkern g1="k" 	g2="periodcentered" 	k="26" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="k" 	g2="x" 	k="7" />
<hkern g1="k" 	g2="trademark" 	k="20" />
<hkern g1="k" 	g2="g" 	k="10" />
<hkern g1="k" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="18" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="44" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="26" />
<hkern g1="k" 	g2="guillemotright,guilsinglright" 	k="11" />
<hkern g1="k" 	g2="j" 	k="10" />
<hkern g1="k" 	g2="z" 	k="7" />
<hkern g1="K" 	g2="t" 	k="33" />
<hkern g1="K" 	g2="T" 	k="17" />
<hkern g1="K" 	g2="V" 	k="12" />
<hkern g1="K" 	g2="W" 	k="10" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="K" 	g2="question" 	k="7" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="18" />
<hkern g1="K" 	g2="asterisk" 	k="40" />
<hkern g1="K" 	g2="periodcentered" 	k="46" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="K" 	g2="v" 	k="20" />
<hkern g1="K" 	g2="w" 	k="16" />
<hkern g1="K" 	g2="x" 	k="16" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="K" 	g2="quoteleft,quotedblleft" 	k="18" />
<hkern g1="K" 	g2="quotedbl,quotesingle" 	k="21" />
<hkern g1="K" 	g2="trademark" 	k="-9" />
<hkern g1="K" 	g2="S" 	k="9" />
<hkern g1="K" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="7" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="30" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="K" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="K" 	g2="j" 	k="10" />
<hkern g1="K" 	g2="z" 	k="14" />
<hkern g1="l,uniFB02" 	g2="j" 	k="-8" />
<hkern g1="L" 	g2="t" 	k="20" />
<hkern g1="L" 	g2="T" 	k="120" />
<hkern g1="L" 	g2="V" 	k="76" />
<hkern g1="L" 	g2="W" 	k="56" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="76" />
<hkern g1="L" 	g2="question" 	k="32" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="78" />
<hkern g1="L" 	g2="asterisk" 	k="152" />
<hkern g1="L" 	g2="periodcentered" 	k="92" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-3" />
<hkern g1="L" 	g2="f,uniFB01,uniFB02" 	k="14" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="26" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="29" />
<hkern g1="L" 	g2="v" 	k="36" />
<hkern g1="L" 	g2="w" 	k="34" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="36" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="78" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="89" />
<hkern g1="L" 	g2="backslash" 	k="80" />
<hkern g1="L" 	g2="registered" 	k="92" />
<hkern g1="L" 	g2="trademark" 	k="98" />
<hkern g1="L" 	g2="S" 	k="22" />
<hkern g1="L" 	g2="g" 	k="4" />
<hkern g1="L" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="L" 	g2="hyphen,uni00AD,endash,emdash" 	k="54" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="34" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="24" />
<hkern g1="h,m,n,ntilde" 	g2="V" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="16" />
<hkern g1="h,m,n,ntilde" 	g2="question" 	k="7" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk" 	k="34" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="20" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="t" 	k="17" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="57" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V" 	k="19" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="46" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="question" 	k="12" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="26" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk" 	k="25" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="14" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="v" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="w" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="17" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="X" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="y,yacute,ydieresis" 	k="4" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="29" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="26" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="registered" 	k="7" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="hyphen,uni00AD,endash,emdash" 	k="-6" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="z" 	k="6" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="24" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="6" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="34" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="14" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="17" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="registered" 	k="-6" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="26" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="40" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="z" 	k="7" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="18" />
<hkern g1="P" 	g2="T" 	k="24" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="112" />
<hkern g1="P" 	g2="periodcentered" 	k="8" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="50" />
<hkern g1="P" 	g2="s" 	k="10" />
<hkern g1="P" 	g2="x" 	k="14" />
<hkern g1="P" 	g2="X" 	k="24" />
<hkern g1="P" 	g2="Z" 	k="78" />
<hkern g1="P" 	g2="registered" 	k="-30" />
<hkern g1="P" 	g2="J" 	k="146" />
<hkern g1="P" 	g2="S" 	k="10" />
<hkern g1="P" 	g2="g" 	k="30" />
<hkern g1="P" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="24" />
<hkern g1="P" 	g2="hyphen,uni00AD,endash,emdash" 	k="33" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="P" 	g2="z" 	k="20" />
<hkern g1="P" 	g2="slash" 	k="75" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-18" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-25" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="57" />
<hkern g1="r" 	g2="periodcentered" 	k="16" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="24" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="r" 	g2="s" 	k="4" />
<hkern g1="r" 	g2="v" 	k="-25" />
<hkern g1="r" 	g2="w" 	k="-19" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-25" />
<hkern g1="r" 	g2="Z" 	k="10" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-33" />
<hkern g1="r" 	g2="backslash" 	k="-13" />
<hkern g1="r" 	g2="registered" 	k="-62" />
<hkern g1="r" 	g2="J" 	k="55" />
<hkern g1="r" 	g2="g" 	k="10" />
<hkern g1="r" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="24" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="r" 	g2="z" 	k="4" />
<hkern g1="r" 	g2="slash" 	k="34" />
<hkern g1="R" 	g2="T" 	k="14" />
<hkern g1="R" 	g2="V" 	k="-5" />
<hkern g1="R" 	g2="W" 	k="-6" />
<hkern g1="R" 	g2="asterisk" 	k="10" />
<hkern g1="R" 	g2="periodcentered" 	k="10" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="4" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="R" 	g2="x" 	k="7" />
<hkern g1="R" 	g2="X" 	k="4" />
<hkern g1="R" 	g2="Z" 	k="10" />
<hkern g1="R" 	g2="registered" 	k="-25" />
<hkern g1="R" 	g2="J" 	k="18" />
<hkern g1="R" 	g2="S" 	k="14" />
<hkern g1="R" 	g2="g" 	k="4" />
<hkern g1="R" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="R" 	g2="hyphen,uni00AD,endash,emdash" 	k="31" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="30" />
<hkern g1="R" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="R" 	g2="z" 	k="7" />
<hkern g1="s" 	g2="t" 	k="20" />
<hkern g1="s" 	g2="T" 	k="24" />
<hkern g1="s" 	g2="V" 	k="10" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="s" 	g2="question" 	k="11" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="11" />
<hkern g1="s" 	g2="asterisk" 	k="49" />
<hkern g1="s" 	g2="hyphen,uni00AD,endash,emdash" 	k="-10" />
<hkern g1="S" 	g2="t" 	k="24" />
<hkern g1="S" 	g2="T" 	k="20" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="11" />
<hkern g1="S" 	g2="asterisk" 	k="16" />
<hkern g1="S" 	g2="periodcentered" 	k="11" />
<hkern g1="S" 	g2="registered" 	k="-3" />
<hkern g1="S" 	g2="J" 	k="14" />
<hkern g1="S" 	g2="S" 	k="14" />
<hkern g1="S" 	g2="hyphen,uni00AD,endash,emdash" 	k="-14" />
<hkern g1="Thorn" 	g2="asterisk" 	k="62" />
<hkern g1="Thorn" 	g2="backslash" 	k="40" />
<hkern g1="Thorn" 	g2="trademark" 	k="24" />
<hkern g1="Thorn" 	g2="slash" 	k="58" />
<hkern g1="t" 	g2="t" 	k="20" />
<hkern g1="t" 	g2="T" 	k="14" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="4" />
<hkern g1="t" 	g2="question" 	k="26" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-14" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-7" />
<hkern g1="t" 	g2="periodcentered" 	k="20" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="21" />
<hkern g1="t" 	g2="s" 	k="10" />
<hkern g1="t" 	g2="x" 	k="16" />
<hkern g1="t" 	g2="registered" 	k="-34" />
<hkern g1="t" 	g2="g" 	k="10" />
<hkern g1="t" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="14" />
<hkern g1="t" 	g2="hyphen,uni00AD,endash,emdash" 	k="26" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="18" />
<hkern g1="t" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="t" 	g2="slash" 	k="-10" />
<hkern g1="T" 	g2="t" 	k="18" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="106" />
<hkern g1="T" 	g2="periodcentered" 	k="64" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="73" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02" 	k="18" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="24" />
<hkern g1="T" 	g2="s" 	k="59" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="46" />
<hkern g1="T" 	g2="v" 	k="33" />
<hkern g1="T" 	g2="w" 	k="34" />
<hkern g1="T" 	g2="x" 	k="39" />
<hkern g1="T" 	g2="X" 	k="20" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="T" 	g2="Z" 	k="54" />
<hkern g1="T" 	g2="registered" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-34" />
<hkern g1="T" 	g2="J" 	k="126" />
<hkern g1="T" 	g2="S" 	k="39" />
<hkern g1="T" 	g2="g" 	k="73" />
<hkern g1="T" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="66" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="73" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="47" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="40" />
<hkern g1="T" 	g2="z" 	k="75" />
<hkern g1="T" 	g2="slash" 	k="89" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="46" />
<hkern g1="T" 	g2="AE" 	k="85" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="20" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="V" 	k="14" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="asterisk" 	k="24" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="V" 	k="4" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="4" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="4" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="x" 	k="7" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="47" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="S" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="31" />
<hkern g1="v" 	g2="T" 	k="20" />
<hkern g1="v" 	g2="V" 	k="9" />
<hkern g1="v" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="v" 	g2="asterisk" 	k="11" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="11" />
<hkern g1="v" 	g2="Z" 	k="10" />
<hkern g1="v" 	g2="registered" 	k="-45" />
<hkern g1="v" 	g2="trademark" 	k="-9" />
<hkern g1="v" 	g2="J" 	k="40" />
<hkern g1="v" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="v" 	g2="hyphen,uni00AD,endash,emdash" 	k="7" />
<hkern g1="v" 	g2="j" 	k="10" />
<hkern g1="v" 	g2="z" 	k="24" />
<hkern g1="v" 	g2="slash" 	k="20" />
<hkern g1="V" 	g2="V" 	k="-10" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-13" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="65" />
<hkern g1="V" 	g2="periodcentered" 	k="11" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="22" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="V" 	g2="s" 	k="12" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="30" />
<hkern g1="V" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="V" 	g2="v" 	k="9" />
<hkern g1="V" 	g2="w" 	k="9" />
<hkern g1="V" 	g2="x" 	k="15" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="9" />
<hkern g1="V" 	g2="Z" 	k="19" />
<hkern g1="V" 	g2="registered" 	k="-53" />
<hkern g1="V" 	g2="trademark" 	k="-54" />
<hkern g1="V" 	g2="J" 	k="73" />
<hkern g1="V" 	g2="S" 	k="10" />
<hkern g1="V" 	g2="g" 	k="21" />
<hkern g1="V" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="15" />
<hkern g1="V" 	g2="hyphen,uni00AD,endash,emdash" 	k="19" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="21" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="19" />
<hkern g1="V" 	g2="z" 	k="25" />
<hkern g1="V" 	g2="slash" 	k="47" />
<hkern g1="V" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="w" 	g2="T" 	k="24" />
<hkern g1="w" 	g2="V" 	k="9" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="24" />
<hkern g1="w" 	g2="asterisk" 	k="11" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="4" />
<hkern g1="w" 	g2="X" 	k="14" />
<hkern g1="w" 	g2="registered" 	k="-38" />
<hkern g1="w" 	g2="trademark" 	k="-10" />
<hkern g1="w" 	g2="J" 	k="30" />
<hkern g1="w" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="w" 	g2="j" 	k="10" />
<hkern g1="w" 	g2="z" 	k="17" />
<hkern g1="w" 	g2="slash" 	k="8" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-13" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="34" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="W" 	g2="Z" 	k="6" />
<hkern g1="W" 	g2="registered" 	k="-39" />
<hkern g1="W" 	g2="trademark" 	k="-34" />
<hkern g1="W" 	g2="J" 	k="65" />
<hkern g1="W" 	g2="S" 	k="6" />
<hkern g1="W" 	g2="g" 	k="13" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="10" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="W" 	g2="z" 	k="10" />
<hkern g1="W" 	g2="slash" 	k="29" />
<hkern g1="x" 	g2="t" 	k="24" />
<hkern g1="x" 	g2="T" 	k="33" />
<hkern g1="x" 	g2="V" 	k="15" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="34" />
<hkern g1="x" 	g2="asterisk" 	k="23" />
<hkern g1="x" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="7" />
<hkern g1="x" 	g2="periodcentered" 	k="20" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="x" 	g2="X" 	k="10" />
<hkern g1="x" 	g2="registered" 	k="-38" />
<hkern g1="x" 	g2="trademark" 	k="-9" />
<hkern g1="x" 	g2="S" 	k="4" />
<hkern g1="x" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="17" />
<hkern g1="x" 	g2="hyphen,uni00AD,endash,emdash" 	k="14" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="24" />
<hkern g1="x" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="x" 	g2="exclam" 	k="14" />
<hkern g1="X" 	g2="t" 	k="16" />
<hkern g1="X" 	g2="T" 	k="20" />
<hkern g1="X" 	g2="quoteright,quotedblright" 	k="7" />
<hkern g1="X" 	g2="asterisk" 	k="10" />
<hkern g1="X" 	g2="periodcentered" 	k="40" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-3" />
<hkern g1="X" 	g2="f,uniFB01,uniFB02" 	k="16" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="17" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="X" 	g2="v" 	k="16" />
<hkern g1="X" 	g2="w" 	k="14" />
<hkern g1="X" 	g2="x" 	k="10" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="X" 	g2="quoteleft,quotedblleft" 	k="7" />
<hkern g1="X" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="X" 	g2="registered" 	k="-8" />
<hkern g1="X" 	g2="trademark" 	k="-21" />
<hkern g1="X" 	g2="S" 	k="14" />
<hkern g1="X" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="7" />
<hkern g1="X" 	g2="hyphen,uni00AD,endash,emdash" 	k="27" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="X" 	g2="z" 	k="14" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="V" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="7" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="35" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="x" 	k="14" />
<hkern g1="y,yacute,ydieresis" 	g2="X" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="registered" 	k="-45" />
<hkern g1="y,yacute,ydieresis" 	g2="trademark" 	k="-13" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="40" />
<hkern g1="y,yacute,ydieresis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotright,guilsinglright" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="j" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="z" 	k="24" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="28" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="question" 	k="7" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-9" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="25" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="91" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="periodcentered" 	k="46" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="67" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="34" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="24" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="34" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="26" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="registered" 	k="-33" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-46" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="60" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="55" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="47" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="67" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="40" />
<hkern g1="z" 	g2="T" 	k="26" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="29" />
<hkern g1="z" 	g2="periodcentered" 	k="11" />
<hkern g1="z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="23" />
<hkern g1="z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="z" 	g2="v" 	k="4" />
<hkern g1="z" 	g2="y,yacute,ydieresis" 	k="4" />
<hkern g1="z" 	g2="registered" 	k="-25" />
<hkern g1="z" 	g2="trademark" 	k="-13" />
<hkern g1="z" 	g2="g" 	k="6" />
<hkern g1="z" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="z" 	g2="hyphen,uni00AD,endash,emdash" 	k="16" />
<hkern g1="Z" 	g2="t" 	k="14" />
<hkern g1="Z" 	g2="V" 	k="6" />
<hkern g1="Z" 	g2="W" 	k="6" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="14" />
<hkern g1="Z" 	g2="periodcentered" 	k="60" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="Z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="Z" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="Z" 	g2="v" 	k="16" />
<hkern g1="Z" 	g2="w" 	k="16" />
<hkern g1="Z" 	g2="x" 	k="24" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="Z" 	g2="Z" 	k="16" />
<hkern g1="Z" 	g2="registered" 	k="-20" />
<hkern g1="Z" 	g2="trademark" 	k="-20" />
<hkern g1="Z" 	g2="J" 	k="33" />
<hkern g1="Z" 	g2="S" 	k="30" />
<hkern g1="Z" 	g2="g" 	k="14" />
<hkern g1="Z" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="21" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="34" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="34" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="j" 	k="-80" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="J" 	k="20" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="26" />
<hkern g1="colon,semicolon" 	g2="asterisk" 	k="49" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-26" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="92" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk" 	k="138" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="11" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t" 	k="46" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="99" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="35" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="65" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="22" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="34" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="84" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="104" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="96" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="J" 	k="20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="66" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="t" 	k="26" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="47" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="v" 	k="7" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="V" 	k="19" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="W" 	k="10" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="17" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="x" 	k="14" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="X" 	k="27" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Z" 	k="20" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="32" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t" 	k="14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="40" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="v" 	k="4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="17" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="17" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="y,yacute,ydieresis" 	k="4" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="x" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="30" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="55" />
<hkern g1="guillemotright,guilsinglright" 	g2="t" 	k="33" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="46" />
<hkern g1="guillemotright,guilsinglright" 	g2="v" 	k="7" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="21" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="7" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="30" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="24" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="18" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="21" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="75" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-9" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-13" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-13" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="X" 	k="7" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="85" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="43" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="56" />
<hkern g1="quoteleft,quotedblleft" 	g2="f,uniFB01,uniFB02" 	k="14" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="83" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="53" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="133" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="58" />
<hkern g1="quoteright,quotedblright" 	g2="X" 	k="7" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="85" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="65" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="58" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB01,uniFB02" 	k="16" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="45" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="132" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="13" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="26" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="95" />
<hkern g1="quotedbl,quotesingle" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="quotedbl,quotesingle" 	g2="S" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="X" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="55" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="115" />
</font>
</defs></svg> 