<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="source_sans_prolight" horiz-adv-x="479" >
<font-face units-per-em="1000" ascent="750" descent="-250" />
<missing-glyph horiz-adv-x="200" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="333" />
<glyph unicode=" "  horiz-adv-x="200" />
<glyph unicode="&#x09;" horiz-adv-x="200" />
<glyph unicode="&#xa0;" horiz-adv-x="200" />
<glyph unicode="!" horiz-adv-x="259" d="M113 179l-6 420v71h46l-1 -71l-6 -420h-33zM130 -12q-17 0 -30 12.5t-13 32.5q0 21 13 33.5t30 12.5t29.5 -12.5t12.5 -33.5q0 -20 -12.5 -32.5t-29.5 -12.5z" />
<glyph unicode="&#x22;" horiz-adv-x="360" d="M92 477l-8 147l-1 69h51v-69l-9 -147h-33zM233 477l-8 147l-1 69h51v-69l-9 -147h-33z" />
<glyph unicode="#" d="M93 0l26 214h-83v36h87l21 170h-88v36h92l25 194h35l-25 -194h149l26 194h34l-25 -194h82v-36h-85l-21 -170h86v-36h-91l-25 -214h-35l25 214h-149l-26 -214h-35zM158 250h150l21 170h-150z" />
<glyph unicode="$" d="M226 -110v99q-93 7 -167 77l24 31q78 -70 161 -70q62 0 96 33.5t34 89.5q0 54 -30 91t-72 58t-84.5 42.5t-72.5 59.5t-30 94q0 62 40 105t101 50v99h39v-99q45 -3 76 -20.5t61 -49.5l-26 -28q-33 32 -60 46t-70 14q-51 0 -83.5 -32.5t-32.5 -82.5q0 -48 30 -80t72 -52 t84.5 -42t72.5 -64.5t30 -105.5q0 -70 -43 -114t-111 -50v-99h-39z" />
<glyph unicode="%" horiz-adv-x="805" d="M181 257q-66 0 -103.5 54.5t-37.5 153.5q0 98 37.5 152t103.5 54t103.5 -54t37.5 -152q0 -99 -37.5 -153.5t-103.5 -54.5zM181 290q47 0 74.5 46.5t27.5 128.5t-27.5 127.5t-74.5 45.5t-74.5 -45.5t-27.5 -127.5t27.5 -128.5t74.5 -46.5zM198 -12l368 683h38l-367 -683 h-39zM624 -12q-66 0 -103.5 54.5t-37.5 153.5q0 98 37.5 152t103.5 54t103.5 -54t37.5 -152q0 -99 -37.5 -153.5t-103.5 -54.5zM624 21q47 0 74.5 46.5t27.5 128.5t-27.5 127.5t-74.5 45.5t-74.5 -45.5t-27.5 -127.5t27.5 -128.5t74.5 -46.5z" />
<glyph unicode="&#x26;" horiz-adv-x="575" d="M226 -12q-81 0 -135.5 49.5t-54.5 129.5q0 59 35 102.5t100 91.5q-44 91 -44 163q0 64 38 105.5t98 41.5q52 0 81 -33.5t29 -86.5q0 -30 -11.5 -57t-38.5 -54t-45 -42t-57 -44q68 -125 183 -226q68 85 104 217h43q-43 -148 -117 -242q66 -53 128 -76l-15 -39 q-65 23 -141 83q-81 -83 -180 -83zM168 526q0 -63 37 -140q63 45 95.5 81t32.5 82q0 36 -17.5 61t-54.5 25q-42 0 -67.5 -31.5t-25.5 -77.5zM230 26q77 0 146 71q-119 109 -187 232q-52 -41 -80 -77.5t-28 -81.5q0 -64 43.5 -104t105.5 -40z" />
<glyph unicode="'" horiz-adv-x="219" d="M92 477l-8 147l-1 69h51v-69l-9 -147h-33z" />
<glyph unicode="(" horiz-adv-x="279" d="M220 -175q-132 214 -132 453t132 453l27 -16q-120 -191 -120 -437t120 -437z" />
<glyph unicode=")" horiz-adv-x="279" d="M59 -175l-27 16q120 191 120 437t-120 437l27 16q132 -214 132 -453t-132 -453z" />
<glyph unicode="*" horiz-adv-x="395" d="M132 462l-27 20l55 88l-90 36l10 30l94 -26l7 102h32l7 -100l95 24l10 -30l-90 -36l54 -88l-27 -20l-64 83z" />
<glyph unicode="+" d="M219 110v201h-185v38h185v201h41v-201h185v-38h-185v-201h-41z" />
<glyph unicode="," horiz-adv-x="219" d="M62 -155l-14 30q77 40 77 121q-4 -1 -12 -1q-18 0 -30 11t-12 31q0 19 12.5 30.5t30.5 11.5q23 0 37 -19t14 -52q0 -53 -28 -95.5t-75 -67.5z" />
<glyph unicode="-" horiz-adv-x="299" d="M40 230v39h220v-39h-220z" />
<glyph unicode="." horiz-adv-x="219" d="M110 -12q-17 0 -30 12.5t-13 32.5q0 21 13 33.5t30 12.5t29.5 -12.5t12.5 -33.5q0 -20 -12.5 -32.5t-29.5 -12.5z" />
<glyph unicode="/" horiz-adv-x="357" d="M9 -160l305 870h38l-305 -870h-38z" />
<glyph unicode="0" d="M239 -12q-91 0 -141 87t-50 247q0 159 49.5 244t141.5 85t141.5 -85t49.5 -244q0 -160 -50 -247t-141 -87zM239 26q68 0 107 76t39 220q0 143 -39 217t-107 74t-107 -74t-39 -217t39 -219.5t107 -76.5z" />
<glyph unicode="1" d="M84 0v39h150v530h-114v30q75 13 123 40h36v-600h139v-39h-334z" />
<glyph unicode="2" d="M41 0v28q174 177 244.5 271t70.5 172q0 63 -35 102.5t-104 39.5q-81 0 -149 -84l-29 28q85 94 182 94q85 0 132.5 -48.5t47.5 -130.5q0 -31 -8 -61.5t-30 -68t-40.5 -64.5t-61.5 -76t-68.5 -76.5t-84.5 -90.5q88 5 107 5h218v-40h-392z" />
<glyph unicode="3" d="M232 -12q-117 0 -203 90l26 31q74 -82 177 -82q65 0 107 39t42 102q0 148 -223 148v39q105 0 152.5 38t47.5 101q0 54 -35 86.5t-94 32.5q-81 0 -147 -70l-26 30q80 78 173 78q77 0 126 -41t49 -113q0 -116 -122 -158v-4q63 -13 104 -57t41 -109q0 -82 -55.5 -131.5 t-139.5 -49.5z" />
<glyph unicode="4" d="M310 0v191h-294v26l297 422h40v-410h95v-38h-95v-191h-43zM70 229h240v234q0 52 4 110h-4q-9 -15 -26.5 -41t-25.5 -39z" />
<glyph unicode="5" d="M227 -12q-115 0 -201 86l25 31q76 -78 175 -78q66 0 112.5 48.5t46.5 123.5q0 77 -42 121t-114 44q-56 0 -119 -43l-31 19l23 299h295v-39h-255l-20 -231q56 34 114 34q86 0 140.5 -51t54.5 -151q0 -97 -61 -155t-143 -58z" />
<glyph unicode="6" d="M261 -12q-97 0 -153 79t-56 223q0 188 66.5 274.5t168.5 86.5q80 0 135 -62l-27 -30q-42 53 -108 53q-84 0 -137 -75.5t-53 -239.5q73 89 161 89q85 0 131.5 -51t46.5 -147q0 -86 -51.5 -143t-123.5 -57zM262 26q55 0 92 46.5t37 115.5q0 73 -34 116.5t-104 43.5 q-84 0 -155 -96q6 -105 47.5 -165.5t116.5 -60.5z" />
<glyph unicode="7" d="M188 0q7 192 51.5 328t141.5 272h-337v39h393v-26q-110 -146 -151.5 -280.5t-49.5 -332.5h-48z" />
<glyph unicode="8" d="M243 -12q-88 0 -145.5 50t-57.5 126q0 59 37.5 105.5t90.5 74.5v4q-92 61 -92 147q0 68 48 112t119 44q80 0 126.5 -46.5t46.5 -120.5q0 -47 -28 -90.5t-63 -71.5v-4q52 -32 82 -68.5t30 -94.5q0 -71 -54 -119t-140 -48zM290 335q84 68 84 148q0 55 -35.5 93t-95.5 38 q-53 0 -88 -33.5t-35 -85.5q0 -32 13.5 -57.5t41.5 -45t51.5 -30.5t63.5 -27zM243 25q66 0 107.5 37.5t41.5 93.5q0 34 -14.5 60t-43.5 45.5t-56.5 32t-69.5 29.5q-3 1 -4 1.5t-3 1t-4 1.5q-113 -66 -113 -161q0 -60 45.5 -100.5t113.5 -40.5z" />
<glyph unicode="9" d="M226 291q37 0 79 24.5t76 72.5q-6 105 -47.5 165t-117.5 60q-55 0 -92 -46.5t-37 -114.5q0 -74 34.5 -117.5t104.5 -43.5zM191 -12q-81 0 -135 62l27 30q42 -53 109 -53q84 0 137 75.5t53 239.5q-73 -88 -162 -88q-84 0 -130.5 51t-46.5 147q0 86 51.5 142.5t123.5 56.5 q97 0 152.5 -78.5t55.5 -222.5q0 -188 -66 -275t-169 -87z" />
<glyph unicode=":" horiz-adv-x="219" d="M110 370q-17 0 -30 12.5t-13 32.5q0 21 13 33.5t30 12.5t29.5 -12.5t12.5 -33.5q0 -20 -12.5 -32.5t-29.5 -12.5zM110 -12q-17 0 -30 12.5t-13 32.5q0 21 13 33.5t30 12.5t29.5 -12.5t12.5 -33.5q0 -20 -12.5 -32.5t-29.5 -12.5z" />
<glyph unicode=";" horiz-adv-x="219" d="M110 370q-17 0 -30 12.5t-13 32.5q0 21 13 33.5t30 12.5t29.5 -12.5t12.5 -33.5q0 -20 -12.5 -32.5t-29.5 -12.5zM62 -155l-14 30q77 40 77 121q-4 -1 -12 -1q-18 0 -30 11t-12 31q0 19 12.5 30.5t30.5 11.5q23 0 37 -19t14 -52q0 -53 -28 -95.5t-75 -67.5z" />
<glyph unicode="&#x3c;" d="M445 146l-411 164v44l411 164v-43l-232 -90l-133 -51v-4l133 -51l232 -90v-43z" />
<glyph unicode="=" d="M34 413v38h411v-38h-411zM34 209v38h411v-38h-411z" />
<glyph unicode="&#x3e;" d="M34 146v43l232 90l133 51v4l-133 51l-232 90v43l411 -164v-44z" />
<glyph unicode="?" horiz-adv-x="402" d="M163 179q-6 46 6.5 87t34.5 72t45 59.5t40 62t17 66.5q0 50 -29 83.5t-84 33.5q-77 0 -129 -64l-27 25q69 78 160 78q71 0 113 -43t42 -110q0 -37 -17 -73t-41 -65.5t-47 -60.5t-36 -70t-8 -81h-40zM186 -12q-17 0 -29.5 12.5t-12.5 32.5q0 21 12.5 33.5t29.5 12.5 t30 -12.5t13 -33.5q0 -20 -13 -32.5t-30 -12.5z" />
<glyph unicode="@" horiz-adv-x="814" d="M388 -146q-148 0 -242 93.5t-94 254.5q0 190 116.5 310.5t280.5 120.5q144 0 229 -87t85 -230q0 -119 -57.5 -189.5t-126.5 -70.5q-41 0 -67 19t-30 57h-2q-63 -70 -122 -70q-49 0 -81.5 35.5t-32.5 100.5q0 81 52 153t134 72q53 0 83 -48h2l9 40h34l-36 -189 q-34 -135 60 -135q53 0 98 61.5t45 160.5q0 130 -75 207.5t-204 77.5q-143 0 -249.5 -110.5t-106.5 -282.5q0 -145 82.5 -230t218.5 -85q89 0 168 48l16 -30q-85 -54 -187 -54zM364 98q50 0 108 67l31 173q-32 49 -74 49q-64 0 -104.5 -58.5t-40.5 -127.5q0 -103 80 -103z " />
<glyph unicode="A" horiz-adv-x="526" d="M188 388l-42 -123h234l-42 123q-46 133 -73 228h-4q-27 -95 -73 -228zM8 0l232 659h46l232 -659h-48l-77 226h-261l-78 -226h-46z" />
<glyph unicode="B" horiz-adv-x="578" d="M97 0v659h178q103 0 160.5 -40t57.5 -120q0 -53 -26.5 -91t-75.5 -53v-4q66 -11 104 -52t38 -107q0 -93 -65.5 -142.5t-179.5 -49.5h-191zM143 369h114q190 0 190 125q0 68 -45.5 97.5t-139.5 29.5h-119v-252zM143 38h133q101 0 156.5 38t55.5 115q0 140 -212 140h-133 v-293z" />
<glyph unicode="C" horiz-adv-x="564" d="M331 -12q-123 0 -199.5 93.5t-76.5 249.5t77.5 248t203.5 92q105 0 172 -79l-27 -31q-60 68 -144 68q-109 0 -171.5 -80t-62.5 -218t62 -219.5t168 -81.5q94 0 167 81l27 -29q-83 -94 -196 -94z" />
<glyph unicode="D" horiz-adv-x="604" d="M97 0v659h153q148 0 223.5 -86.5t75.5 -240.5q0 -155 -75.5 -243.5t-222.5 -88.5h-154zM143 39h102q129 0 192.5 79t63.5 214q0 134 -63.5 211t-192.5 77h-102v-581z" />
<glyph unicode="E" horiz-adv-x="515" d="M97 0v659h361v-40h-315v-249h264v-40h-264v-290h325v-40h-371z" />
<glyph unicode="F" horiz-adv-x="477" d="M97 0v659h359v-40h-313v-262h265v-40h-265v-317h-46z" />
<glyph unicode="G" horiz-adv-x="605" d="M338 -12q-128 0 -205.5 93t-77.5 250q0 156 80 248t211 92q105 0 180 -79l-27 -31q-62 68 -152 68q-113 0 -178.5 -80t-65.5 -218t63 -219.5t175 -81.5q98 0 148 50v205h-158v39h202v-261q-70 -75 -195 -75z" />
<glyph unicode="H" horiz-adv-x="639" d="M97 0v659h46v-289h353v289h46v-659h-46v330h-353v-330h-46z" />
<glyph unicode="I" horiz-adv-x="240" d="M97 0v659h46v-659h-46z" />
<glyph unicode="J" horiz-adv-x="462" d="M203 -12q-110 0 -162 97l35 23q45 -78 127 -78q60 0 89 37t29 117v475h46v-480q0 -86 -39.5 -138.5t-124.5 -52.5z" />
<glyph unicode="K" horiz-adv-x="558" d="M97 0v659h46v-366h2l319 366h55l-210 -244l239 -415h-53l-218 380l-134 -153v-227h-46z" />
<glyph unicode="L" horiz-adv-x="467" d="M97 0v659h46v-619h304v-40h-350z" />
<glyph unicode="M" horiz-adv-x="706" d="M97 0v659h65l138 -387q5 -15 23.5 -66.5t28.5 -79.5h4q7 20 24 69.5t26 76.5l137 387h66v-659h-45v433q0 68 6 178h-4l-51 -147l-142 -392h-40l-143 392l-52 147h-4q6 -165 6 -178v-433h-43z" />
<glyph unicode="N" horiz-adv-x="636" d="M97 0v659h48l278 -465l74 -131h4l-6 192v404h44v-659h-48l-278 465l-74 131h-4q1 -21 2.5 -58.5t2.5 -69t1 -59.5v-409h-44z" />
<glyph unicode="O" horiz-adv-x="652" d="M326 -12q-120 0 -195.5 94.5t-75.5 249.5q0 154 75.5 246.5t195.5 92.5q121 0 196.5 -92.5t75.5 -246.5q0 -155 -76 -249.5t-196 -94.5zM326 30q101 0 162 82.5t61 219.5q0 136 -61 216.5t-162 80.5t-162 -80.5t-61 -216.5q0 -137 61 -219.5t162 -82.5z" />
<glyph unicode="P" horiz-adv-x="549" d="M97 0v659h166q117 0 178 -43t61 -141q0 -95 -61.5 -143.5t-173.5 -48.5h-124v-283h-46zM143 322h113q102 0 150.5 36.5t48.5 116.5q0 81 -48.5 113t-154.5 32h-109v-298z" />
<glyph unicode="Q" horiz-adv-x="652" d="M326 27q101 0 162 83.5t61 221.5q0 136 -61 216.5t-162 80.5t-162 -80.5t-61 -216.5q0 -138 61 -221.5t162 -83.5zM519 -156q-80 0 -136.5 40t-82.5 105q-109 11 -177 104t-68 239q0 154 75.5 246.5t195.5 92.5q121 0 196.5 -92.5t75.5 -246.5q0 -147 -68 -239.5 t-179 -103.5q46 -102 172 -102q33 0 67 8l10 -39q-41 -12 -81 -12z" />
<glyph unicode="R" horiz-adv-x="544" d="M97 0v659h189q216 0 216 -174q0 -77 -44.5 -123t-124.5 -56l182 -306h-53l-179 303h-140v-303h-46zM143 341h131q181 0 181 144q0 72 -45.5 103.5t-135.5 31.5h-131v-279z" />
<glyph unicode="S" horiz-adv-x="521" d="M269 -12q-134 0 -223 99l30 32q79 -89 193 -89q75 0 119.5 36.5t44.5 95.5q0 30 -8.5 52t-27.5 38t-35.5 25t-46.5 23l-100 45q-27 12 -46 23.5t-41.5 30.5t-34.5 47t-12 63q0 71 54.5 116.5t137.5 45.5q110 0 183 -79l-26 -30q-65 67 -157 67q-66 0 -106 -32t-40 -86 q0 -27 9.5 -48t30.5 -36.5t34.5 -23t39.5 -19.5l100 -44q67 -30 103 -68t36 -107q0 -77 -58 -127t-153 -50z" />
<glyph unicode="T" horiz-adv-x="524" d="M239 0v619h-210v40h466v-40h-210v-619h-46z" />
<glyph unicode="U" horiz-adv-x="634" d="M316 -12q-45 0 -82.5 13t-70 42t-50.5 82.5t-18 127.5v406h46v-401q0 -122 48.5 -175t126.5 -53q80 0 130 53t50 175v401h43v-406q0 -74 -18.5 -127.5t-51 -82.5t-70 -42t-83.5 -13z" />
<glyph unicode="V" horiz-adv-x="491" d="M222 0l-218 659h49l122 -385q57 -183 70 -221h4q12 36 23 71.5t24 78.5t22 71l122 385h47l-216 -659h-49z" />
<glyph unicode="W" horiz-adv-x="770" d="M178 0l-150 659h48l83 -388q30 -144 45 -215h4q25 112 51 215l103 388h47l103 -388q9 -36 26.5 -107.5t26.5 -107.5h4q8 36 21.5 107.5t21.5 107.5l83 388h45l-147 -659h-51l-120 452q-3 15 -16 67t-19 82h-4q-6 -28 -19 -77.5t-18 -71.5l-118 -452h-50z" />
<glyph unicode="X" horiz-adv-x="482" d="M17 0l198 341l-184 318h50l108 -194l54 -90h4q37 69 49 90l108 194h47l-184 -320l198 -339h-50l-115 202q-20 37 -61 105h-4q-33 -62 -56 -105l-115 -202h-47z" />
<glyph unicode="Y" horiz-adv-x="447" d="M200 0v267l-197 392h49l100 -206q53 -111 70 -142h4q55 107 71 142l100 206h47l-198 -392v-267h-46z" />
<glyph unicode="Z" horiz-adv-x="538" d="M50 0v27l379 592h-346v40h404v-27l-380 -592h383v-40h-440z" />
<glyph unicode="[" horiz-adv-x="279" d="M98 -152v860h160v-29h-125v-802h125v-29h-160z" />
<glyph unicode="\" horiz-adv-x="357" d="M311 -160l-306 870h38l306 -870h-38z" />
<glyph unicode="]" horiz-adv-x="279" d="M21 -152v29h125v802h-125v29h160v-860h-160z" />
<glyph unicode="^" d="M66 290l150 380h46l151 -380h-43l-77 203l-52 133h-4l-51 -133l-77 -203h-43z" />
<glyph unicode="_" horiz-adv-x="500" d="M12 -117v38h476v-38h-476z" />
<glyph unicode="`" horiz-adv-x="535" d="M296 574l-144 151l34 29l134 -157z" />
<glyph unicode="a" horiz-adv-x="491" d="M197 -12q-61 0 -100 33.5t-39 98.5q0 79 73.5 121t236.5 60q2 152 -114 152q-76 0 -153 -56l-20 33q88 62 179 62q80 0 116 -50.5t36 -134.5v-307h-38l-4 62h-3q-93 -74 -170 -74zM205 26q71 0 163 78v163q-144 -17 -204.5 -51.5t-60.5 -93.5q0 -49 28.5 -72.5 t73.5 -23.5z" />
<glyph unicode="b" horiz-adv-x="542" d="M280 -12q-70 0 -144 61h-2l-5 -49h-37v719h44v-208l-2 -94q90 75 166 75q94 0 143 -65.5t49 -179.5q0 -118 -62 -188.5t-150 -70.5zM278 27q73 0 120 61.5t47 158.5q0 95 -37 150.5t-115 55.5q-69 0 -157 -79v-285q73 -62 142 -62z" />
<glyph unicode="c" horiz-adv-x="449" d="M270 -12q-96 0 -157 67.5t-61 183.5t63.5 184.5t155.5 68.5q76 0 141 -59l-26 -31q-55 51 -114 51q-74 0 -124 -60t-50 -154q0 -95 48 -153.5t126 -58.5q68 0 128 55l23 -31q-72 -63 -153 -63z" />
<glyph unicode="d" horiz-adv-x="544" d="M251 -12q-92 0 -145.5 65.5t-53.5 185.5q0 113 62.5 183t150.5 70q41 0 73 -14t72 -45l-2 88v198h44v-719h-38l-4 62h-3q-74 -74 -156 -74zM257 27q72 0 151 79v285q-70 62 -140 62q-72 0 -120.5 -61.5t-48.5 -152.5q0 -97 41.5 -154.5t116.5 -57.5z" />
<glyph unicode="e" horiz-adv-x="483" d="M274 -12q-95 0 -158.5 68t-63.5 183q0 113 62.5 183t146.5 70q86 0 135 -58.5t49 -163.5q0 -24 -2 -36h-346q2 -92 51.5 -150t129.5 -58q69 0 129 43l18 -34q-78 -47 -151 -47zM97 270h306q0 90 -37.5 137t-103.5 47q-64 0 -111 -50t-54 -134z" />
<glyph unicode="f" horiz-adv-x="263" d="M99 0v442h-66v34l66 4v107q0 71 31 107.5t88 36.5q35 0 70 -16l-12 -36q-28 14 -58 14q-75 0 -75 -109v-104h111v-38h-111v-442h-44z" />
<glyph unicode="g" horiz-adv-x="487" d="M249 -231q-91 0 -144 36t-53 97q0 64 71 116v4q-42 25 -42 77q0 51 52 88v4q-60 52 -60 132q0 73 49 121t120 48q33 0 63 -12h163v-37h-112q53 -46 53 -121q0 -73 -48 -121t-119 -48q-46 0 -80 20q-39 -32 -39 -69q0 -63 92 -63h103q81 0 119.5 -26.5t38.5 -83.5 q0 -65 -63.5 -113.5t-163.5 -48.5zM242 188q51 0 87.5 38t36.5 97t-36 96t-88 37q-53 0 -89 -36.5t-36 -96.5q0 -59 36.5 -97t88.5 -38zM254 -195q78 0 127.5 37t49.5 84q0 73 -111 73h-103q-27 0 -60 8q-63 -43 -63 -101q0 -46 42.5 -73.5t117.5 -27.5z" />
<glyph unicode="h" horiz-adv-x="528" d="M92 0v719h44v-314q87 87 166 87q74 0 109.5 -45.5t35.5 -138.5v-308h-44v302q0 77 -26 113.5t-84 36.5q-41 0 -75.5 -21t-81.5 -69v-362h-44z" />
<glyph unicode="i" horiz-adv-x="229" d="M92 0v480h44v-480h-44zM115 596q-17 0 -28.5 11t-11.5 28q0 18 11.5 29t28.5 11t28.5 -11t11.5 -29q0 -17 -11.5 -28t-28.5 -11z" />
<glyph unicode="j" horiz-adv-x="229" d="M29 -229q-32 0 -62 12l11 36q30 -10 50 -10q38 0 51 26.5t13 78.5v566h45v-569q0 -140 -108 -140zM115 596q-16 0 -28 11t-12 28q0 18 12 29t28 11q17 0 29 -11t12 -29q0 -17 -12 -28t-29 -11z" />
<glyph unicode="k" horiz-adv-x="464" d="M92 0v719h44v-530h2l237 291h51l-156 -190l182 -290h-49l-159 257l-108 -126v-131h-44z" />
<glyph unicode="l" horiz-adv-x="237" d="M151 -12q-59 0 -59 76v655h44v-661q0 -31 21 -31q8 0 18 2l8 -36q-15 -5 -32 -5z" />
<glyph unicode="m" horiz-adv-x="812" d="M92 0v480h38l4 -74h3q75 86 151 86q105 0 133 -98q90 98 163 98q144 0 144 -184v-308h-45v302q0 150 -107 150q-65 0 -144 -90v-362h-44v302q0 77 -26.5 113.5t-81.5 36.5q-64 0 -144 -90v-362h-44z" />
<glyph unicode="n" horiz-adv-x="532" d="M92 0v480h38l4 -74h3q86 86 165 86q74 0 109.5 -45.5t35.5 -138.5v-308h-44v302q0 77 -26 113.5t-84 36.5q-41 0 -75.5 -21t-81.5 -69v-362h-44z" />
<glyph unicode="o" horiz-adv-x="535" d="M267 -12q-90 0 -152.5 68t-62.5 183q0 116 62.5 184.5t152.5 68.5q91 0 153.5 -68.5t62.5 -184.5q0 -115 -62.5 -183t-153.5 -68zM267 27q73 0 121.5 59t48.5 153t-48.5 154t-121.5 60t-121 -59.5t-48 -154.5q0 -94 48 -153t121 -59z" />
<glyph unicode="p" horiz-adv-x="544" d="M92 -217v697h38l4 -60h3q91 72 163 72q94 0 143 -65.5t49 -179.5q0 -118 -62 -188.5t-150 -70.5q-64 0 -144 59v-264h-44zM278 27q73 0 120 61.5t47 158.5q0 95 -37 150.5t-115 55.5q-67 0 -157 -79v-285q76 -62 142 -62z" />
<glyph unicode="q" horiz-adv-x="544" d="M408 -217v185l2 95q-79 -75 -159 -75q-92 0 -145.5 65.5t-53.5 185.5q0 113 62.5 183t150.5 70q71 0 143 -56h2l5 44h37v-697h-44zM257 27q72 0 151 79v285q-70 62 -140 62q-72 0 -120.5 -61.5t-48.5 -152.5q0 -97 41.5 -154.5t116.5 -57.5z" />
<glyph unicode="r" horiz-adv-x="317" d="M92 0v480h38l4 -89h3q55 101 136 101q28 0 48 -10l-10 -40q-21 8 -44 8q-80 0 -131 -123v-327h-44z" />
<glyph unicode="s" horiz-adv-x="405" d="M208 -12q-97 0 -176 66l26 33q74 -61 153 -61q54 0 84 27.5t30 67.5q0 28 -19.5 50t-41 32.5t-56.5 23.5q-29 10 -45 16.5t-40 19t-36 25.5t-22 32.5t-10 43.5q0 55 41.5 91.5t113.5 36.5q77 0 137 -50l-24 -31q-57 43 -115 43q-52 0 -80 -25.5t-28 -61.5q0 -20 8.5 -35 t27.5 -27t34 -18.5t43 -16.5q83 -31 109 -51q47 -37 48 -96q0 -57 -43.5 -96t-118.5 -39z" />
<glyph unicode="t" horiz-adv-x="312" d="M223 -12q-66 0 -93 37.5t-27 107.5v309h-75v34l76 4l6 139h38v-139h139v-38h-139v-313q0 -50 17.5 -76t63.5 -26q28 0 60 15l12 -36q-50 -18 -78 -18z" />
<glyph unicode="u" horiz-adv-x="529" d="M230 -12q-74 0 -109.5 45.5t-35.5 138.5v308h44v-302q0 -77 26 -113.5t83 -36.5q42 0 77 23t78 75v354h44v-480h-37l-5 80h-2q-79 -92 -163 -92z" />
<glyph unicode="v" horiz-adv-x="434" d="M192 0l-180 480h48l108 -301q9 -26 26 -74t22 -64h4q23 63 49 138l108 301h45l-178 -480h-52z" />
<glyph unicode="w" horiz-adv-x="685" d="M166 0l-142 480h48l86 -311q22 -80 33 -128h4q5 19 10.5 40t13 47t11.5 41l87 311h53l87 -311q7 -25 19.5 -69.5t16.5 -58.5h4q5 19 16.5 61.5t17.5 66.5l85 311h45l-138 -480h-58l-84 298q-7 24 -18.5 69t-18.5 68h-4q-19 -80 -38 -139l-83 -296h-53z" />
<glyph unicode="x" horiz-adv-x="407" d="M14 0l163 251l-150 229h49l78 -122q30 -48 51 -79h4q8 13 24 39.5t25 39.5l75 122h46l-149 -233l163 -247h-49l-85 131q-31 51 -57 88h-4q-11 -16 -30.5 -48t-24.5 -40l-82 -131h-47z" />
<glyph unicode="y" horiz-adv-x="436" d="M73 -219q-27 0 -49 10l10 39q20 -8 39 -8q81 0 123 127l13 42l-197 489h48l116 -302q38 -101 52 -134h4q5 14 44 134l103 302h45l-190 -540q-53 -159 -161 -159z" />
<glyph unicode="z" horiz-adv-x="404" d="M27 0v24l284 418h-253v38h310v-23l-283 -418h293v-39h-351z" />
<glyph unicode="{" horiz-adv-x="279" d="M224 -152q-58 0 -84.5 27.5t-26.5 100.5q0 38 4 104.5t4 100.5q0 81 -86 81v32q86 0 86 79q0 35 -4 102.5t-4 104.5q0 73 26.5 100.5t84.5 27.5h34v-29h-31q-44 0 -60.5 -24t-16.5 -79q0 -32 3 -93.5t3 -98.5q0 -46 -11 -70.5t-40 -33.5v-4q29 -9 40 -34t11 -70 q0 -37 -3 -98.5t-3 -93.5q0 -55 16.5 -79t60.5 -24h31v-29h-34z" />
<glyph unicode="|" horiz-adv-x="226" d="M95 -250v1000h36v-1000h-36z" />
<glyph unicode="}" horiz-adv-x="279" d="M21 -152v29h31q44 0 60.5 24t16.5 79q0 32 -3 93.5t-3 98.5q0 88 51 104v4q-28 9 -39.5 33.5t-11.5 70.5q0 37 3 98.5t3 93.5q0 55 -16.5 79t-60.5 24h-31v29h34q58 0 84.5 -27.5t26.5 -100.5q0 -37 -4 -104.5t-4 -102.5q0 -79 86 -79v-32q-86 0 -86 -81q0 -34 4 -100.5 t4 -104.5q0 -73 -26.5 -100.5t-84.5 -27.5h-34z" />
<glyph unicode="~" d="M329 266q-33 0 -66 22.5t-62 45t-53 22.5q-47 0 -79 -63l-29 18q43 83 110 83q33 0 66 -22.5t62 -45t53 -22.5q47 0 79 63l28 -20q-20 -39 -49.5 -60t-59.5 -21z" />
<glyph unicode="&#xa1;" horiz-adv-x="259" d="M107 -190v71l6 420h33l6 -420l1 -71h-46zM130 401q-18 0 -30.5 12.5t-12.5 34.5q0 19 13 31.5t30 12.5t29.5 -12t12.5 -32q0 -22 -12.5 -34.5t-29.5 -12.5z" />
<glyph unicode="&#xa2;" d="M107 311q0 -78 40 -129t108 -61v381q-66 -11 -107 -62.5t-41 -128.5zM255 -28v109q-87 8 -140 69t-53 161t54.5 160.5t138.5 69.5v111h34v-109q75 -2 135 -60l-24 -28q-54 49 -111 49v-385q66 3 123 54l23 -29q-66 -61 -146 -64v-108h-34z" />
<glyph unicode="&#xa3;" d="M57 0v27q53 30 79.5 81.5t26.5 112.5q0 31 -9 72h-100v33l66 3h24q-2 9 -8.5 29.5t-9.5 31.5t-7 28.5t-5.5 32.5t-1.5 30q0 79 45.5 124.5t124.5 45.5q88 0 144 -71l-29 -27q-50 59 -115 59q-59 0 -92 -37t-33 -94q0 -19 3.5 -40t7 -35t11 -40t10.5 -37h165v-36h-157 q8 -37 8 -72q0 -108 -72 -177v-4h295v-40h-371z" />
<glyph unicode="&#xa4;" d="M57 114l-26 28l65 67q-38 51 -38 119q0 71 38 122l-65 68l26 28l66 -69q49 44 116 44t116 -44l67 69l26 -28l-66 -68q38 -51 38 -122q0 -68 -37 -119l65 -67l-26 -28l-67 68q-48 -45 -116 -45q-66 0 -116 45zM239 174q57 0 97 43.5t40 110.5q0 68 -40 112t-97 44t-97 -44 t-40 -112q0 -67 40 -110.5t97 -43.5z" />
<glyph unicode="&#xa5;" d="M216 0v165h-166v34h166v71h-166v33h152l-172 336h48l94 -193q42 -88 66 -137h4q12 23 33.5 68.5t33.5 68.5l94 193h46l-174 -336h154v-33h-168v-71h168v-34h-168v-165h-45z" />
<glyph unicode="&#xa6;" horiz-adv-x="226" d="M95 279v471h36v-471h-36zM95 -250v470h36v-470h-36z" />
<glyph unicode="&#xa7;" d="M92 355q0 -37 24 -64.5t53.5 -41t74 -33t68.5 -35.5q39 18 57 40t18 60t-24 66.5t-53 42.5t-74 34t-68 35q-38 -22 -57 -45t-19 -59zM233 -52q-95 0 -161 63l30 28q57 -54 131 -54q45 0 72.5 24.5t27.5 60.5q0 37 -29 63t-70.5 43t-83.5 36.5t-71 54.5t-29 85 q0 83 89 131q-33 33 -33 81q0 47 35 82t100 35q75 0 140 -53l-24 -31q-57 46 -115 46q-47 0 -70.5 -22t-23.5 -54q0 -36 29 -61.5t70 -42.5t82.5 -36.5t70.5 -55.5t29 -87q0 -46 -22 -75.5t-65 -52.5q34 -34 34 -84q0 -53 -42 -88.5t-101 -35.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="535" d="M178 598q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5zM356 598q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="741" d="M371 -12q-133 0 -226.5 93t-93.5 241q0 147 93 238.5t227 91.5q133 0 226 -91.5t93 -238.5q0 -148 -93 -241t-226 -93zM371 19q118 0 201 85t83 218q0 132 -83 216t-201 84q-119 0 -202 -84t-83 -216q0 -133 83.5 -218t201.5 -85zM376 124q-74 0 -124.5 53.5t-50.5 144.5 q0 84 53 135.5t127 51.5q65 0 120 -55l-23 -26q-47 44 -98 44q-60 0 -98 -41.5t-38 -108.5q0 -74 36.5 -117.5t97.5 -43.5q60 0 115 50l20 -28q-33 -29 -63 -44t-74 -15z" />
<glyph unicode="&#xaa;" horiz-adv-x="337" d="M139 398q-44 0 -70.5 24t-26.5 67q0 52 49 79.5t157 38.5q-2 92 -70 92q-54 0 -107 -37l-15 27q64 42 126 42q103 0 103 -125v-200h-30l-6 39h-4q-55 -47 -106 -47zM148 430q48 0 100 49v102q-90 -10 -128.5 -32t-38.5 -57q0 -62 67 -62z" />
<glyph unicode="&#xab;" horiz-adv-x="399" d="M180 72l-137 161v38l137 161l25 -21l-123 -159l123 -161zM321 72l-137 161v38l137 161l25 -21l-123 -159l123 -161z" />
<glyph unicode="&#xac;" d="M404 110v201h-370v38h411v-239h-41z" />
<glyph unicode="&#xad;" horiz-adv-x="299" d="M40 230v39h220v-39h-220z" />
<glyph unicode="&#xae;" horiz-adv-x="401" d="M200 324q-76 0 -129 54t-53 139q0 86 53 140.5t129 54.5q77 0 129.5 -54.5t52.5 -140.5q0 -85 -52.5 -139t-129.5 -54zM200 353q64 0 107.5 46.5t43.5 117.5t-44 119t-107 48t-107 -48t-44 -119t43.5 -117.5t107.5 -46.5zM132 418v206h69q77 0 77 -64q0 -19 -11.5 -35 t-28.5 -21l49 -86h-37l-39 76h-47v-76h-32zM164 521h29q51 0 51 37q0 38 -48 38h-32v-75z" />
<glyph unicode="&#xaf;" horiz-adv-x="535" d="M144 606v36h246v-36h-246z" />
<glyph unicode="&#xb0;" horiz-adv-x="311" d="M156 447q-47 0 -80.5 32.5t-33.5 85.5q0 54 33.5 86.5t80.5 32.5t80.5 -32.5t33.5 -86.5q0 -53 -33.5 -85.5t-80.5 -32.5zM156 479q35 0 57.5 24.5t22.5 61.5q0 38 -22.5 63t-57.5 25t-57.5 -25t-22.5 -63q0 -37 22.5 -61.5t57.5 -24.5z" />
<glyph unicode="&#xb1;" d="M219 109v201h-185v37h185v203h41v-203h185v-37h-185v-201h-41zM34 0v37h411v-37h-411z" />
<glyph unicode="&#xb2;" horiz-adv-x="362" d="M56 406v25q111 103 152 153.5t41 96.5q0 43 -23.5 68t-62.5 25q-51 0 -91 -62l-25 23q19 31 51.5 52t68.5 21q56 0 88.5 -31.5t32.5 -91.5q0 -23 -8.5 -47t-17.5 -40.5t-35.5 -46.5t-40 -43.5l-53.5 -53.5l-14 -14h192v-34h-255z" />
<glyph unicode="&#xb3;" horiz-adv-x="362" d="M177 394q-86 0 -137 75l29 22q40 -64 109 -64q38 0 64.5 22.5t26.5 60.5t-37 59t-101 21v28q56 0 89 24.5t33 59.5q0 33 -22.5 53t-60.5 20q-47 0 -89 -52l-25 22q49 63 118 63q50 0 84 -28t34 -75q0 -69 -75 -99q38 -7 64.5 -32.5t26.5 -64.5q0 -53 -37.5 -84t-93.5 -31 z" />
<glyph unicode="&#xb4;" horiz-adv-x="535" d="M238 574l-24 23l134 157l34 -29z" />
<glyph unicode="&#xb5;" horiz-adv-x="543" d="M92 -180v660h44v-302q0 -150 106 -150q44 0 80 22t75 89v341h44q0 -47 -1 -132t-1.5 -154.5t-0.5 -129.5q0 -37 30 -37q12 0 28 6l8 -36q-20 -9 -42 -9q-35 0 -49 23.5t-14 77.5h-3q-59 -100 -151 -100q-40 0 -67.5 13t-45.5 47q0 -148 5 -229h-45z" />
<glyph unicode="&#xb6;" horiz-adv-x="515" d="M373 -80v739h46v-739h-46zM291 244q-117 0 -183 50t-66 157q0 108 62 158t171 50h44v-415h-28z" />
<glyph unicode="&#xb7;" horiz-adv-x="219" d="M110 277q-17 0 -30 12.5t-13 32.5q0 21 13 33.5t30 12.5t29.5 -12.5t12.5 -33.5q0 -20 -12.5 -32.5t-29.5 -12.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="535" d="M199 -219l-7 30q63 4 88 17t25 36q0 22 -18 33.5t-60 17.5l41 87h35l-30 -67q71 -18 71 -69q0 -76 -145 -85z" />
<glyph unicode="&#xb9;" horiz-adv-x="362" d="M176 406v324h-82v27q53 11 87 39h33v-390h-38z" />
<glyph unicode="&#xba;" horiz-adv-x="360" d="M179 398q-63 0 -104.5 45t-41.5 122t41.5 121.5t104.5 44.5t104.5 -44.5t41.5 -121.5t-41.5 -122t-104.5 -45zM179 431q48 0 77.5 37t29.5 97t-29 97t-78 37q-48 0 -77.5 -37t-29.5 -97q0 -59 29.5 -96.5t77.5 -37.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="399" d="M78 72l-24 19l122 161l-122 159l24 21l138 -161v-38zM219 72l-24 19l122 161l-122 159l24 21l138 -161v-38z" />
<glyph unicode="&#xbc;" horiz-adv-x="764" d="M155 269v324h-82v27q53 11 87 39h33v-390h-38zM177 -12l368 683h38l-367 -683h-39zM625 0v114h-177v21l177 255h36v-244h58v-32h-58v-114h-36zM496 146h129v87l4 104h-4l-56 -83z" />
<glyph unicode="&#xbd;" horiz-adv-x="786" d="M155 269v324h-82v27q53 11 87 39h33v-390h-38zM154 -12l368 683h38l-367 -683h-39zM480 0v25q111 103 152 153.5t41 96.5q0 43 -23.5 68t-62.5 25q-51 0 -91 -62l-25 23q19 31 51.5 52t68.5 21q56 0 88.5 -31.5t32.5 -91.5q0 -23 -8.5 -47t-17.5 -40.5t-35.5 -46.5 t-40 -43.5l-53.5 -53.5l-14 -14h192v-34h-255z" />
<glyph unicode="&#xbe;" horiz-adv-x="783" d="M177 257q-86 0 -137 75l29 22q40 -64 109 -64q38 0 64.5 22.5t26.5 60.5t-37 59t-101 21v28q56 0 89 24.5t33 59.5q0 33 -22.5 53t-60.5 20q-47 0 -89 -52l-25 22q49 63 118 63q50 0 84 -28t34 -75q0 -69 -75 -99q38 -7 64.5 -32.5t26.5 -64.5q0 -53 -37.5 -84t-93.5 -31 zM224 -12l368 683h38l-367 -683h-39zM644 0v114h-177v21l177 255h36v-244h58v-32h-58v-114h-36zM515 146h129v87l4 104h-4l-56 -83z" />
<glyph unicode="&#xbf;" horiz-adv-x="402" d="M206 -202q-72 0 -113.5 43t-41.5 110q0 37 17 73t40.5 66t46.5 60.5t36 69.5t8 81h41q6 -46 -6.5 -87t-35 -72t-45.5 -59.5t-40 -62t-17 -66.5q0 -50 29 -83.5t85 -33.5q75 0 128 64l28 -25q-71 -78 -160 -78zM216 401q-17 0 -29.5 12.5t-12.5 34.5q0 20 12.5 32t29.5 12 t29.5 -12t12.5 -32q0 -22 -12.5 -34.5t-29.5 -12.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="526" d="M188 388l-42 -123h234l-42 123q-46 133 -73 228h-4q-27 -95 -73 -228zM8 0l232 659h46l232 -659h-48l-77 226h-261l-78 -226h-46zM284 706l-133 113l28 33l125 -123z" />
<glyph unicode="&#xc1;" horiz-adv-x="526" d="M188 388l-42 -123h234l-42 123q-46 133 -73 228h-4q-27 -95 -73 -228zM8 0l232 659h46l232 -659h-48l-77 226h-261l-78 -226h-46zM242 706l-20 23l125 123l28 -33z" />
<glyph unicode="&#xc2;" horiz-adv-x="526" d="M188 388l-42 -123h234l-42 123q-46 133 -73 228h-4q-27 -95 -73 -228zM8 0l232 659h46l232 -659h-48l-77 226h-261l-78 -226h-46zM128 726l113 113h44l113 -113l-20 -19l-113 98h-4l-113 -98z" />
<glyph unicode="&#xc3;" horiz-adv-x="526" d="M188 388l-42 -123h234l-42 123q-46 133 -73 228h-4q-27 -95 -73 -228zM8 0l232 659h46l232 -659h-48l-77 226h-261l-78 -226h-46zM340 718q-26 0 -49 12.5t-36.5 27t-32 27t-36.5 12.5q-22 0 -37 -20.5t-18 -55.5l-33 3q2 48 25.5 79t62.5 31q26 0 49 -12.5t36.5 -27 t32 -27t36.5 -12.5q21 0 35.5 20.5t19.5 56.5l33 -3q-2 -48 -25.5 -79.5t-62.5 -31.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="526" d="M188 388l-42 -123h234l-42 123q-46 133 -73 228h-4q-27 -95 -73 -228zM8 0l232 659h46l232 -659h-48l-77 226h-261l-78 -226h-46zM165 725q-16 0 -26 10t-10 26q0 17 10 27t26 10t26.5 -10t10.5 -27q0 -16 -10.5 -26t-26.5 -10zM361 725q-16 0 -26.5 10t-10.5 26 q0 17 10.5 27t26.5 10t26 -10t10 -27q0 -16 -10 -26t-26 -10z" />
<glyph unicode="&#xc5;" horiz-adv-x="526" d="M188 388l-42 -123h234l-42 123q-46 133 -73 228h-4q-27 -95 -73 -228zM8 0l232 659h46l232 -659h-48l-77 226h-261l-78 -226h-46zM263 700q-38 0 -63 23.5t-25 61.5q0 39 25 62.5t63 23.5q37 0 62.5 -23.5t25.5 -62.5q0 -37 -25.5 -61t-62.5 -24zM263 726q24 0 40 15.5 t16 43.5q0 27 -16.5 43.5t-39.5 16.5q-25 0 -41.5 -16t-16.5 -44t16.5 -43.5t41.5 -15.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="809" d="M283 388l-73 -133h201v364h-4q-29 -53 -124 -231zM21 0l365 659h366v-40h-295v-249h245v-40h-245v-290h305v-40h-351v217h-222l-119 -217h-49z" />
<glyph unicode="&#xc7;" horiz-adv-x="564" d="M262 -219l-7 30q63 4 88 17t25 36q0 22 -18 33.5t-60 17.5l34 73q-120 3 -194.5 96t-74.5 247q0 156 77.5 248t203.5 92q105 0 172 -79l-27 -31q-60 68 -144 68q-109 0 -171.5 -80t-62.5 -218t62 -219.5t168 -81.5q94 0 167 81l27 -29q-73 -83 -167 -92l-24 -55 q71 -18 71 -69q0 -76 -145 -85z" />
<glyph unicode="&#xc8;" horiz-adv-x="515" d="M97 0v659h361v-40h-315v-249h264v-40h-264v-290h325v-40h-371zM303 706l-133 113l28 33l125 -123z" />
<glyph unicode="&#xc9;" horiz-adv-x="515" d="M97 0v659h361v-40h-315v-249h264v-40h-264v-290h325v-40h-371zM261 706l-20 23l125 123l28 -33z" />
<glyph unicode="&#xca;" horiz-adv-x="515" d="M97 0v659h361v-40h-315v-249h264v-40h-264v-290h325v-40h-371zM147 726l113 113h44l113 -113l-20 -19l-113 98h-4l-113 -98z" />
<glyph unicode="&#xcb;" horiz-adv-x="515" d="M97 0v659h361v-40h-315v-249h264v-40h-264v-290h325v-40h-371zM184 725q-16 0 -26 10t-10 26q0 17 10 27t26 10t26.5 -10t10.5 -27q0 -16 -10.5 -26t-26.5 -10zM380 725q-16 0 -26.5 10t-10.5 26q0 17 10.5 27t26.5 10t26 -10t10 -27q0 -16 -10 -26t-26 -10z" />
<glyph unicode="&#xcc;" horiz-adv-x="240" d="M97 0v659h46v-659h-46zM141 706l-133 113l28 33l125 -123z" />
<glyph unicode="&#xcd;" horiz-adv-x="240" d="M97 0v659h46v-659h-46zM99 706l-20 23l125 123l28 -33z" />
<glyph unicode="&#xce;" horiz-adv-x="240" d="M97 0v659h46v-659h-46zM-15 726l113 113h44l113 -113l-20 -19l-113 98h-4l-113 -98z" />
<glyph unicode="&#xcf;" horiz-adv-x="240" d="M97 0v659h46v-659h-46zM22 725q-16 0 -26 10t-10 26q0 17 10 27t26 10t26.5 -10t10.5 -27q0 -16 -10.5 -26t-26.5 -10zM218 725q-16 0 -26.5 10t-10.5 26q0 17 10.5 27t26.5 10t26 -10t10 -27q0 -16 -10 -26t-26 -10z" />
<glyph unicode="&#xd0;" horiz-adv-x="625" d="M37 332v31l81 2v294h153q148 0 223.5 -86.5t75.5 -240.5q0 -155 -75.5 -243.5t-222.5 -88.5h-154v332h-81zM164 39h102q129 0 192.5 79t63.5 214q0 134 -63.5 211t-192.5 77h-102v-255h163v-33h-163v-293z" />
<glyph unicode="&#xd1;" horiz-adv-x="636" d="M97 0v659h48l278 -465l74 -131h4l-6 192v404h44v-659h-48l-278 465l-74 131h-4q1 -21 2.5 -58.5t2.5 -69t1 -59.5v-409h-44zM400 718q-26 0 -49 12.5t-36.5 27t-32 27t-36.5 12.5q-22 0 -37 -20.5t-18 -55.5l-33 3q2 48 25.5 79t62.5 31q26 0 49 -12.5t36.5 -27t32 -27 t36.5 -12.5q21 0 35.5 20.5t19.5 56.5l33 -3q-2 -48 -25.5 -79.5t-62.5 -31.5z" />
<glyph unicode="&#xd2;" horiz-adv-x="652" d="M326 -12q-120 0 -195.5 94.5t-75.5 249.5q0 154 75.5 246.5t195.5 92.5q121 0 196.5 -92.5t75.5 -246.5q0 -155 -76 -249.5t-196 -94.5zM326 30q101 0 162 82.5t61 219.5q0 136 -61 216.5t-162 80.5t-162 -80.5t-61 -216.5q0 -137 61 -219.5t162 -82.5zM347 706l-133 113 l28 33l125 -123z" />
<glyph unicode="&#xd3;" horiz-adv-x="652" d="M326 -12q-120 0 -195.5 94.5t-75.5 249.5q0 154 75.5 246.5t195.5 92.5q121 0 196.5 -92.5t75.5 -246.5q0 -155 -76 -249.5t-196 -94.5zM326 30q101 0 162 82.5t61 219.5q0 136 -61 216.5t-162 80.5t-162 -80.5t-61 -216.5q0 -137 61 -219.5t162 -82.5zM305 706l-20 23 l125 123l28 -33z" />
<glyph unicode="&#xd4;" horiz-adv-x="652" d="M326 -12q-120 0 -195.5 94.5t-75.5 249.5q0 154 75.5 246.5t195.5 92.5q121 0 196.5 -92.5t75.5 -246.5q0 -155 -76 -249.5t-196 -94.5zM326 30q101 0 162 82.5t61 219.5q0 136 -61 216.5t-162 80.5t-162 -80.5t-61 -216.5q0 -137 61 -219.5t162 -82.5zM191 726l113 113 h44l113 -113l-20 -19l-113 98h-4l-113 -98z" />
<glyph unicode="&#xd5;" horiz-adv-x="652" d="M326 -12q-120 0 -195.5 94.5t-75.5 249.5q0 154 75.5 246.5t195.5 92.5q121 0 196.5 -92.5t75.5 -246.5q0 -155 -76 -249.5t-196 -94.5zM326 30q101 0 162 82.5t61 219.5q0 136 -61 216.5t-162 80.5t-162 -80.5t-61 -216.5q0 -137 61 -219.5t162 -82.5zM403 718 q-26 0 -49 12.5t-36.5 27t-32 27t-36.5 12.5q-22 0 -37 -20.5t-18 -55.5l-33 3q2 48 25.5 79t62.5 31q26 0 49 -12.5t36.5 -27t32 -27t36.5 -12.5q21 0 35.5 20.5t19.5 56.5l33 -3q-2 -48 -25.5 -79.5t-62.5 -31.5z" />
<glyph unicode="&#xd6;" horiz-adv-x="652" d="M326 -12q-120 0 -195.5 94.5t-75.5 249.5q0 154 75.5 246.5t195.5 92.5q121 0 196.5 -92.5t75.5 -246.5q0 -155 -76 -249.5t-196 -94.5zM326 30q101 0 162 82.5t61 219.5q0 136 -61 216.5t-162 80.5t-162 -80.5t-61 -216.5q0 -137 61 -219.5t162 -82.5zM228 725 q-16 0 -26 10t-10 26q0 17 10 27t26 10t26.5 -10t10.5 -27q0 -16 -10.5 -26t-26.5 -10zM424 725q-16 0 -26.5 10t-10.5 26q0 17 10.5 27t26.5 10t26 -10t10 -27q0 -16 -10 -26t-26 -10z" />
<glyph unicode="&#xd7;" d="M78 135l-26 28l161 167l-161 167l26 28l161 -169l162 169l26 -28l-161 -167l161 -167l-26 -28l-162 168z" />
<glyph unicode="&#xd8;" horiz-adv-x="652" d="M330 -12q-106 0 -180 77l-66 -88l-28 22l70 94q-67 94 -67 239q0 154 75.5 246.5t195.5 92.5q103 0 176 -70l60 81l29 -22l-65 -86q72 -92 72 -242q0 -155 -76 -249.5t-196 -94.5zM107 332q0 -121 48 -200l323 433q-59 64 -148 64q-101 0 -162 -80.5t-61 -216.5zM330 30 q101 0 162 82.5t61 219.5q0 123 -52 203l-324 -434q60 -71 153 -71z" />
<glyph unicode="&#xd9;" horiz-adv-x="634" d="M316 -12q-45 0 -82.5 13t-70 42t-50.5 82.5t-18 127.5v406h46v-401q0 -122 48.5 -175t126.5 -53q80 0 130 53t50 175v401h43v-406q0 -74 -18.5 -127.5t-51 -82.5t-70 -42t-83.5 -13zM338 706l-133 113l28 33l125 -123z" />
<glyph unicode="&#xda;" horiz-adv-x="634" d="M316 -12q-45 0 -82.5 13t-70 42t-50.5 82.5t-18 127.5v406h46v-401q0 -122 48.5 -175t126.5 -53q80 0 130 53t50 175v401h43v-406q0 -74 -18.5 -127.5t-51 -82.5t-70 -42t-83.5 -13zM296 706l-20 23l125 123l28 -33z" />
<glyph unicode="&#xdb;" horiz-adv-x="634" d="M316 -12q-45 0 -82.5 13t-70 42t-50.5 82.5t-18 127.5v406h46v-401q0 -122 48.5 -175t126.5 -53q80 0 130 53t50 175v401h43v-406q0 -74 -18.5 -127.5t-51 -82.5t-70 -42t-83.5 -13zM182 726l113 113h44l113 -113l-20 -19l-113 98h-4l-113 -98z" />
<glyph unicode="&#xdc;" horiz-adv-x="634" d="M316 -12q-45 0 -82.5 13t-70 42t-50.5 82.5t-18 127.5v406h46v-401q0 -122 48.5 -175t126.5 -53q80 0 130 53t50 175v401h43v-406q0 -74 -18.5 -127.5t-51 -82.5t-70 -42t-83.5 -13zM219 725q-16 0 -26 10t-10 26q0 17 10 27t26 10t26.5 -10t10.5 -27q0 -16 -10.5 -26 t-26.5 -10zM415 725q-16 0 -26.5 10t-10.5 26q0 17 10.5 27t26.5 10t26 -10t10 -27q0 -16 -10 -26t-26 -10z" />
<glyph unicode="&#xdd;" horiz-adv-x="447" d="M200 0v267l-197 392h49l100 -206q53 -111 70 -142h4q55 107 71 142l100 206h47l-198 -392v-267h-46zM202 706l-20 23l125 123l28 -33z" />
<glyph unicode="&#xde;" horiz-adv-x="563" d="M97 0v659h46v-117h134q115 0 175 -43t60 -141q0 -95 -61.5 -143.5t-173.5 -48.5h-134v-166h-46zM143 205h123q102 0 150.5 36.5t48.5 116.5t-47.5 112.5t-151.5 32.5h-123v-298z" />
<glyph unicode="&#xdf;" horiz-adv-x="542" d="M367 -12q-73 0 -136 51l22 33q56 -46 114 -46q48 0 74.5 29t26.5 70q0 38 -21.5 64.5t-52 42t-61.5 31.5t-52.5 43t-21.5 67q0 32 15.5 62t33.5 49.5t33.5 48t15.5 58.5q0 45 -24.5 72.5t-70.5 27.5q-59 0 -92.5 -43.5t-33.5 -130.5v-517h-44v532q0 91 46.5 144t124.5 53 q64 0 101.5 -38t37.5 -96q0 -35 -15.5 -66.5t-34 -51.5t-34 -47t-15.5 -53q0 -31 21.5 -53t52.5 -37t62 -32t52.5 -49.5t21.5 -78.5q0 -60 -41.5 -99.5t-104.5 -39.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="491" d="M197 -12q-61 0 -100 33.5t-39 98.5q0 79 73.5 121t236.5 60q2 152 -114 152q-76 0 -153 -56l-20 33q88 62 179 62q80 0 116 -50.5t36 -134.5v-307h-38l-4 62h-3q-93 -74 -170 -74zM205 26q71 0 163 78v163q-144 -17 -204.5 -51.5t-60.5 -93.5q0 -49 28.5 -72.5 t73.5 -23.5zM284 574l-144 151l34 29l134 -157z" />
<glyph unicode="&#xe1;" horiz-adv-x="491" d="M197 -12q-61 0 -100 33.5t-39 98.5q0 79 73.5 121t236.5 60q2 152 -114 152q-76 0 -153 -56l-20 33q88 62 179 62q80 0 116 -50.5t36 -134.5v-307h-38l-4 62h-3q-93 -74 -170 -74zM205 26q71 0 163 78v163q-144 -17 -204.5 -51.5t-60.5 -93.5q0 -49 28.5 -72.5 t73.5 -23.5zM226 574l-24 23l134 157l34 -29z" />
<glyph unicode="&#xe2;" horiz-adv-x="491" d="M197 -12q-61 0 -100 33.5t-39 98.5q0 79 73.5 121t236.5 60q2 152 -114 152q-76 0 -153 -56l-20 33q88 62 179 62q80 0 116 -50.5t36 -134.5v-307h-38l-4 62h-3q-93 -74 -170 -74zM205 26q71 0 163 78v163q-144 -17 -204.5 -51.5t-60.5 -93.5q0 -49 28.5 -72.5 t73.5 -23.5zM117 593l117 144h42l117 -144l-22 -21l-114 126h-4l-114 -126z" />
<glyph unicode="&#xe3;" horiz-adv-x="491" d="M197 -12q-61 0 -100 33.5t-39 98.5q0 79 73.5 121t236.5 60q2 152 -114 152q-76 0 -153 -56l-20 33q88 62 179 62q80 0 116 -50.5t36 -134.5v-307h-38l-4 62h-3q-93 -74 -170 -74zM205 26q71 0 163 78v163q-144 -17 -204.5 -51.5t-60.5 -93.5q0 -49 28.5 -72.5 t73.5 -23.5zM331 579q-26 0 -48 15t-35.5 33t-32 33t-36.5 15q-49 0 -54 -93l-34 2q8 126 89 126q32 0 58 -24t48.5 -48t45.5 -24q47 0 53 93l34 -2q-8 -126 -88 -126z" />
<glyph unicode="&#xe4;" horiz-adv-x="491" d="M197 -12q-61 0 -100 33.5t-39 98.5q0 79 73.5 121t236.5 60q2 152 -114 152q-76 0 -153 -56l-20 33q88 62 179 62q80 0 116 -50.5t36 -134.5v-307h-38l-4 62h-3q-93 -74 -170 -74zM205 26q71 0 163 78v163q-144 -17 -204.5 -51.5t-60.5 -93.5q0 -49 28.5 -72.5 t73.5 -23.5zM166 598q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5zM344 598q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="491" d="M197 -12q-61 0 -100 33.5t-39 98.5q0 79 73.5 121t236.5 60q2 152 -114 152q-76 0 -153 -56l-20 33q88 62 179 62q80 0 116 -50.5t36 -134.5v-307h-38l-4 62h-3q-93 -74 -170 -74zM205 26q71 0 163 78v163q-144 -17 -204.5 -51.5t-60.5 -93.5q0 -49 28.5 -72.5 t73.5 -23.5zM255 548q-42 0 -69.5 26.5t-27.5 68.5t27.5 69t69.5 27t69.5 -27t27.5 -69t-27.5 -68.5t-69.5 -26.5zM255 574q28 0 46.5 19t18.5 50q0 32 -18.5 51t-46.5 19t-46.5 -19t-18.5 -51q0 -31 18.5 -50t46.5 -19z" />
<glyph unicode="&#xe6;" horiz-adv-x="784" d="M205 -12q-61 0 -100.5 33.5t-39.5 97.5q0 79 73.5 121.5t231.5 60.5q2 152 -113 152q-73 0 -148 -56l-20 33q88 62 172 62q57 0 92.5 -31.5t47.5 -88.5q27 56 71 88t96 32q84 0 131 -58t47 -164q0 -24 -2 -36h-332q0 -90 49 -148.5t122 -58.5q65 0 125 43l19 -35 q-25 -15 -40.5 -23t-45 -16t-62.5 -8q-101 0 -172 99q-47 -45 -102 -72t-100 -27zM212 26q40 0 89.5 25t89.5 68q-21 43 -21 121l-1 27q-138 -17 -198 -51.5t-60 -93.5q0 -49 28 -72.5t73 -23.5zM412 268h293q0 91 -36.5 138.5t-101.5 47.5q-60 0 -104 -52t-51 -134z" />
<glyph unicode="&#xe7;" horiz-adv-x="449" d="M188 -219l-7 30q63 4 88 17t25 36q0 22 -18 33.5t-60 17.5l35 74q-89 7 -144 73t-55 177q0 116 63.5 184.5t155.5 68.5q76 0 141 -59l-26 -31q-55 51 -114 51q-74 0 -124 -60t-50 -154q0 -95 48 -153.5t126 -58.5q68 0 128 55l23 -31q-63 -57 -137 -62l-24 -54 q71 -18 71 -69q0 -76 -145 -85z" />
<glyph unicode="&#xe8;" horiz-adv-x="483" d="M274 -12q-95 0 -158.5 68t-63.5 183q0 113 62.5 183t146.5 70q86 0 135 -58.5t49 -163.5q0 -24 -2 -36h-346q2 -92 51.5 -150t129.5 -58q69 0 129 43l18 -34q-78 -47 -151 -47zM97 270h306q0 90 -37.5 137t-103.5 47q-64 0 -111 -50t-54 -134zM290 574l-144 151l34 29 l134 -157z" />
<glyph unicode="&#xe9;" horiz-adv-x="483" d="M274 -12q-95 0 -158.5 68t-63.5 183q0 113 62.5 183t146.5 70q86 0 135 -58.5t49 -163.5q0 -24 -2 -36h-346q2 -92 51.5 -150t129.5 -58q69 0 129 43l18 -34q-78 -47 -151 -47zM97 270h306q0 90 -37.5 137t-103.5 47q-64 0 -111 -50t-54 -134zM232 574l-24 23l134 157 l34 -29z" />
<glyph unicode="&#xea;" horiz-adv-x="483" d="M274 -12q-95 0 -158.5 68t-63.5 183q0 113 62.5 183t146.5 70q86 0 135 -58.5t49 -163.5q0 -24 -2 -36h-346q2 -92 51.5 -150t129.5 -58q69 0 129 43l18 -34q-78 -47 -151 -47zM97 270h306q0 90 -37.5 137t-103.5 47q-64 0 -111 -50t-54 -134zM123 593l117 144h42 l117 -144l-22 -21l-114 126h-4l-114 -126z" />
<glyph unicode="&#xeb;" horiz-adv-x="483" d="M274 -12q-95 0 -158.5 68t-63.5 183q0 113 62.5 183t146.5 70q86 0 135 -58.5t49 -163.5q0 -24 -2 -36h-346q2 -92 51.5 -150t129.5 -58q69 0 129 43l18 -34q-78 -47 -151 -47zM97 270h306q0 90 -37.5 137t-103.5 47q-64 0 -111 -50t-54 -134zM172 598q-16 0 -26.5 10.5 t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5zM350 598q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5z" />
<glyph unicode="&#xec;" horiz-adv-x="229" d="M92 0v480h44v-480h-44zM143 574l-144 151l34 29l134 -157z" />
<glyph unicode="&#xed;" horiz-adv-x="229" d="M92 0v480h44v-480h-44zM85 574l-24 23l134 157l34 -29z" />
<glyph unicode="&#xee;" horiz-adv-x="229" d="M92 0v480h44v-480h-44zM-24 593l117 144h42l117 -144l-22 -21l-114 126h-4l-114 -126z" />
<glyph unicode="&#xef;" horiz-adv-x="229" d="M92 0v480h44v-480h-44zM25 598q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5zM203 598q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="535" d="M267 -12q-84 0 -145.5 63.5t-61.5 167.5q0 99 57.5 161t149.5 62q90 0 148 -76q-24 132 -116 222l-142 -73l-15 27l132 68q-48 41 -123 84l22 30q80 -42 138 -95l142 74l15 -27l-134 -69q136 -138 136 -355q0 -119 -56 -191.5t-147 -72.5zM267 27q78 0 118 62.5t40 161.5 q0 32 -3 62q-62 91 -153 91q-78 0 -121.5 -53t-43.5 -132q0 -83 47.5 -137.5t115.5 -54.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="532" d="M92 0v480h38l4 -74h3q86 86 165 86q74 0 109.5 -45.5t35.5 -138.5v-308h-44v302q0 77 -26 113.5t-84 36.5q-41 0 -75.5 -21t-81.5 -69v-362h-44zM359 579q-26 0 -48 15t-35.5 33t-32 33t-36.5 15q-49 0 -54 -93l-34 2q8 126 89 126q32 0 58 -24t48.5 -48t45.5 -24 q47 0 53 93l34 -2q-8 -126 -88 -126z" />
<glyph unicode="&#xf2;" horiz-adv-x="535" d="M267 -12q-90 0 -152.5 68t-62.5 183q0 116 62.5 184.5t152.5 68.5q91 0 153.5 -68.5t62.5 -184.5q0 -115 -62.5 -183t-153.5 -68zM267 27q73 0 121.5 59t48.5 153t-48.5 154t-121.5 60t-121 -59.5t-48 -154.5q0 -94 48 -153t121 -59zM296 574l-144 151l34 29l134 -157z " />
<glyph unicode="&#xf3;" horiz-adv-x="535" d="M267 -12q-90 0 -152.5 68t-62.5 183q0 116 62.5 184.5t152.5 68.5q91 0 153.5 -68.5t62.5 -184.5q0 -115 -62.5 -183t-153.5 -68zM267 27q73 0 121.5 59t48.5 153t-48.5 154t-121.5 60t-121 -59.5t-48 -154.5q0 -94 48 -153t121 -59zM238 574l-24 23l134 157l34 -29z" />
<glyph unicode="&#xf4;" horiz-adv-x="535" d="M267 -12q-90 0 -152.5 68t-62.5 183q0 116 62.5 184.5t152.5 68.5q91 0 153.5 -68.5t62.5 -184.5q0 -115 -62.5 -183t-153.5 -68zM267 27q73 0 121.5 59t48.5 153t-48.5 154t-121.5 60t-121 -59.5t-48 -154.5q0 -94 48 -153t121 -59zM129 593l117 144h42l117 -144 l-22 -21l-114 126h-4l-114 -126z" />
<glyph unicode="&#xf5;" horiz-adv-x="535" d="M267 -12q-90 0 -152.5 68t-62.5 183q0 116 62.5 184.5t152.5 68.5q91 0 153.5 -68.5t62.5 -184.5q0 -115 -62.5 -183t-153.5 -68zM267 27q73 0 121.5 59t48.5 153t-48.5 154t-121.5 60t-121 -59.5t-48 -154.5q0 -94 48 -153t121 -59zM343 579q-26 0 -48 15t-35.5 33 t-32 33t-36.5 15q-49 0 -54 -93l-34 2q8 126 89 126q32 0 58 -24t48.5 -48t45.5 -24q47 0 53 93l34 -2q-8 -126 -88 -126z" />
<glyph unicode="&#xf6;" horiz-adv-x="535" d="M267 -12q-90 0 -152.5 68t-62.5 183q0 116 62.5 184.5t152.5 68.5q91 0 153.5 -68.5t62.5 -184.5q0 -115 -62.5 -183t-153.5 -68zM267 27q73 0 121.5 59t48.5 153t-48.5 154t-121.5 60t-121 -59.5t-48 -154.5q0 -94 48 -153t121 -59zM178 598q-16 0 -26.5 10.5 t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5zM356 598q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5z" />
<glyph unicode="&#xf7;" d="M34 311v38h411v-38h-411zM239 109q-16 0 -27 11.5t-11 29.5q0 17 11 28t27 11q17 0 27.5 -11t10.5 -28q0 -19 -10.5 -30t-27.5 -11zM239 469q-16 0 -27 11.5t-11 29.5q0 17 11 28t27 11q17 0 27.5 -11t10.5 -28q0 -19 -10.5 -30t-27.5 -11z" />
<glyph unicode="&#xf8;" horiz-adv-x="535" d="M267 -12q-82 0 -141 56l-54 -66l-26 21l58 70q-52 68 -52 170q0 116 62.5 184.5t152.5 68.5q83 0 142 -56l54 65l26 -21l-58 -69q52 -70 52 -172q0 -115 -62.5 -183t-153.5 -68zM97 240q0 -81 36 -137l249 300q-46 51 -115 51q-73 0 -121.5 -60t-48.5 -154zM267 26 q74 0 122.5 59.5t48.5 152.5q0 82 -36 138l-249 -300q47 -50 114 -50z" />
<glyph unicode="&#xf9;" horiz-adv-x="529" d="M230 -12q-74 0 -109.5 45.5t-35.5 138.5v308h44v-302q0 -77 26 -113.5t83 -36.5q42 0 77 23t78 75v354h44v-480h-37l-5 80h-2q-79 -92 -163 -92zM294 574l-144 151l34 29l134 -157z" />
<glyph unicode="&#xfa;" horiz-adv-x="529" d="M230 -12q-74 0 -109.5 45.5t-35.5 138.5v308h44v-302q0 -77 26 -113.5t83 -36.5q42 0 77 23t78 75v354h44v-480h-37l-5 80h-2q-79 -92 -163 -92zM236 574l-24 23l134 157l34 -29z" />
<glyph unicode="&#xfb;" horiz-adv-x="529" d="M230 -12q-74 0 -109.5 45.5t-35.5 138.5v308h44v-302q0 -77 26 -113.5t83 -36.5q42 0 77 23t78 75v354h44v-480h-37l-5 80h-2q-79 -92 -163 -92zM127 593l117 144h42l117 -144l-22 -21l-114 126h-4l-114 -126z" />
<glyph unicode="&#xfc;" horiz-adv-x="529" d="M230 -12q-74 0 -109.5 45.5t-35.5 138.5v308h44v-302q0 -77 26 -113.5t83 -36.5q42 0 77 23t78 75v354h44v-480h-37l-5 80h-2q-79 -92 -163 -92zM176 598q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5zM354 598 q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="436" d="M73 -219q-27 0 -49 10l10 39q20 -8 39 -8q81 0 123 127l13 42l-197 489h48l116 -302q38 -101 52 -134h4q5 14 44 134l103 302h45l-190 -540q-53 -159 -161 -159zM200 574l-24 23l134 157l34 -29z" />
<glyph unicode="&#xfe;" horiz-adv-x="544" d="M92 -217v936h44v-298q92 71 163 71q95 0 144 -65t49 -180q0 -118 -62 -188.5t-150 -70.5q-66 0 -144 58v-263h-44zM278 27q73 0 120 61.5t47 158.5q0 95 -37 150.5t-115 55.5q-67 0 -157 -79v-285q76 -62 142 -62z" />
<glyph unicode="&#xff;" horiz-adv-x="436" d="M73 -219q-27 0 -49 10l10 39q20 -8 39 -8q81 0 123 127l13 42l-197 489h48l116 -302q38 -101 52 -134h4q5 14 44 134l103 302h45l-190 -540q-53 -159 -161 -159zM140 598q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5 t-26.5 -10.5zM318 598q-16 0 -26.5 10.5t-10.5 26.5t10.5 26.5t26.5 10.5t26.5 -10.5t10.5 -26.5t-10.5 -26.5t-26.5 -10.5z" />
<glyph unicode="&#x152;" horiz-adv-x="833" d="M367 0q-152 0 -232 89t-80 243q0 153 80 240t233 87h408v-40h-295v-249h245v-40h-245v-290h305v-40h-419zM372 39h63v581h-63q-134 0 -201.5 -77t-67.5 -211t68 -213.5t201 -79.5z" />
<glyph unicode="&#x153;" horiz-adv-x="848" d="M263 -12q-88 0 -149.5 68t-61.5 183q0 116 61.5 184.5t149.5 68.5q60 0 110.5 -37t75.5 -105q26 66 74 104t105 38q85 0 134 -58.5t49 -163.5q0 -18 -3 -36h-337q0 -90 50 -148.5t123 -58.5q68 0 128 43l19 -35q-78 -47 -152 -47q-63 0 -113 38t-76 103 q-56 -141 -187 -141zM263 27q72 0 118.5 59t46.5 153q0 95 -46.5 154.5t-118.5 59.5q-71 0 -118 -60t-47 -154t47 -153t118 -59zM471 268h298q0 90 -38 138t-103 48q-61 0 -105.5 -52t-51.5 -134z" />
<glyph unicode="&#x178;" horiz-adv-x="447" d="M200 0v267l-197 392h49l100 -206q53 -111 70 -142h4q55 107 71 142l100 206h47l-198 -392v-267h-46zM125 725q-16 0 -26 10t-10 26q0 17 10 27t26 10t26.5 -10t10.5 -27q0 -16 -10.5 -26t-26.5 -10zM321 725q-16 0 -26.5 10t-10.5 26q0 17 10.5 27t26.5 10t26 -10t10 -27 q0 -16 -10 -26t-26 -10z" />
<glyph unicode="&#x2c6;" horiz-adv-x="535" d="M129 593l117 144h42l117 -144l-22 -21l-114 126h-4l-114 -126z" />
<glyph unicode="&#x2dc;" horiz-adv-x="535" d="M343 579q-26 0 -48 15t-35.5 33t-32 33t-36.5 15q-49 0 -54 -93l-34 2q8 126 89 126q32 0 58 -24t48.5 -48t45.5 -24q47 0 53 93l34 -2q-8 -126 -88 -126z" />
<glyph unicode="&#x2000;" horiz-adv-x="435" />
<glyph unicode="&#x2001;" horiz-adv-x="871" />
<glyph unicode="&#x2002;" horiz-adv-x="435" />
<glyph unicode="&#x2003;" horiz-adv-x="871" />
<glyph unicode="&#x2004;" horiz-adv-x="290" />
<glyph unicode="&#x2005;" horiz-adv-x="217" />
<glyph unicode="&#x2006;" horiz-adv-x="145" />
<glyph unicode="&#x2007;" horiz-adv-x="145" />
<glyph unicode="&#x2008;" horiz-adv-x="108" />
<glyph unicode="&#x2009;" horiz-adv-x="174" />
<glyph unicode="&#x200a;" horiz-adv-x="48" />
<glyph unicode="&#x2010;" horiz-adv-x="299" d="M40 230v39h220v-39h-220z" />
<glyph unicode="&#x2011;" horiz-adv-x="299" d="M40 230v39h220v-39h-220z" />
<glyph unicode="&#x2012;" horiz-adv-x="299" d="M40 230v39h220v-39h-220z" />
<glyph unicode="&#x2013;" horiz-adv-x="480" d="M40 232v36h400v-36h-400z" />
<glyph unicode="&#x2014;" horiz-adv-x="800" d="M40 232v36h720v-36h-720z" />
<glyph unicode="&#x2018;" horiz-adv-x="219" d="M102 480q-20 0 -32 17.5t-12 49.5q0 100 85 156l18 -23q-35 -29 -51 -56.5t-16 -67.5q2 1 8 1q16 0 28 -9.5t12 -27.5t-11 -29t-29 -11z" />
<glyph unicode="&#x2019;" horiz-adv-x="219" d="M76 477l-18 23q35 29 51 56.5t16 67.5q-2 -1 -8 -1q-16 0 -27.5 9.5t-11.5 27.5t11 29t28 11q21 0 33 -17.5t12 -49.5q0 -100 -86 -156z" />
<glyph unicode="&#x201a;" horiz-adv-x="219" d="M76 -131l-18 23q35 29 51 56.5t16 67.5q-2 -1 -8 -1q-16 0 -27.5 9.5t-11.5 27.5t11 29t28 11q21 0 33 -17.5t12 -49.5q0 -100 -86 -156z" />
<glyph unicode="&#x201c;" horiz-adv-x="360" d="M102 480q-20 0 -32 17.5t-12 49.5q0 100 85 156l18 -23q-35 -29 -51 -56.5t-16 -67.5q2 1 8 1q16 0 28 -9.5t12 -27.5t-11 -29t-29 -11zM243 480q-20 0 -32 17.5t-12 49.5q0 100 85 156l18 -23q-35 -29 -51 -56.5t-16 -67.5q2 1 8 1q16 0 28 -9.5t12 -27.5t-11 -29 t-29 -11z" />
<glyph unicode="&#x201d;" horiz-adv-x="360" d="M76 477l-18 23q35 29 51 56.5t16 67.5q-2 -1 -8 -1q-16 0 -27.5 9.5t-11.5 27.5t11 29t28 11q21 0 33 -17.5t12 -49.5q0 -100 -86 -156zM217 477l-18 23q35 29 51 56.5t16 67.5q-2 -1 -8 -1q-16 0 -27.5 9.5t-11.5 27.5t10.5 29t28.5 11q21 0 33 -17.5t12 -49.5 q0 -100 -86 -156z" />
<glyph unicode="&#x201e;" horiz-adv-x="360" d="M76 -131l-18 23q35 29 51 56.5t16 67.5q-2 -1 -8 -1q-16 0 -27.5 9.5t-11.5 27.5t11 29t28 11q21 0 33 -17.5t12 -49.5q0 -100 -86 -156zM217 -131l-18 23q35 29 51 56.5t16 67.5q-2 -1 -8 -1q-16 0 -27.5 9.5t-11.5 27.5t10.5 29t28.5 11q21 0 33 -17.5t12 -49.5 q0 -100 -86 -156z" />
<glyph unicode="&#x2022;" horiz-adv-x="281" d="M140 154q-40 0 -70 30.5t-30 79.5q0 48 30 79t70 31q41 0 71 -30.5t30 -79.5t-30 -79.5t-71 -30.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="931" d="M147 -12q-17 0 -30 12.5t-13 32.5q0 21 13 33.5t30 12.5t29.5 -12.5t12.5 -33.5q0 -20 -12.5 -32.5t-29.5 -12.5zM484 -12q-17 0 -30 12.5t-13 32.5q0 21 13 33.5t30 12.5t29.5 -12.5t12.5 -33.5q0 -20 -12.5 -32.5t-29.5 -12.5zM821 -12q-17 0 -30 12.5t-13 32.5 q0 21 13 33.5t30 12.5t29.5 -12.5t12.5 -33.5q0 -20 -12.5 -32.5t-29.5 -12.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="174" />
<glyph unicode="&#x2039;" horiz-adv-x="259" d="M180 72l-137 161v38l137 161l25 -21l-123 -159l123 -161z" />
<glyph unicode="&#x203a;" horiz-adv-x="259" d="M78 72l-24 19l122 161l-122 159l24 21l138 -161v-38z" />
<glyph unicode="&#x205f;" horiz-adv-x="217" />
<glyph unicode="&#x20ac;" d="M308 -12q-87 0 -145.5 69t-73.5 189h-65v29l62 4q-1 14 -1 42q0 24 1 35h-62v29l65 4q14 123 76 192.5t158 69.5q43 0 80.5 -22t60.5 -56l-29 -27q-49 67 -112 67q-79 0 -127.5 -59t-59.5 -165h278v-33h-281q-1 -11 -1 -34q0 -29 1 -43h241v-33h-238q13 -103 58.5 -161.5 t116.5 -58.5q76 0 134 81l29 -25q-68 -94 -166 -94z" />
<glyph unicode="&#x2122;" horiz-adv-x="610" d="M109 369v272h-107v35h253v-35h-108v-272h-38zM307 369v307h52l52 -126l31 -86h4l31 86l50 126h52v-307h-37v161l5 98h-4l-83 -211h-34l-83 211h-4l5 -98v-161h-37z" />
<glyph unicode="&#x25fc;" horiz-adv-x="480" d="M0 480h480v-480h-480v480z" />
<glyph unicode="&#xfb01;" horiz-adv-x="509" d="M99 0v442h-66v34l66 4v107q0 71 31 107.5t88 36.5q35 0 70 -16l-12 -36q-28 14 -58 14q-75 0 -75 -109v-104h111v-38h-111v-442h-44zM373 0v480h44v-480h-44zM396 596q-17 0 -28.5 11t-11.5 28q0 18 11.5 29t28.5 11t28.5 -11t11.5 -29q0 -17 -11.5 -28t-28.5 -11z" />
<glyph unicode="&#xfb02;" horiz-adv-x="500" d="M99 0v442h-66v34l66 4v107q0 71 31 107.5t88 36.5q35 0 70 -16l-12 -36q-28 14 -58 14q-75 0 -75 -109v-104h111v-38h-111v-442h-44zM414 -12q-59 0 -59 76v655h44v-661q0 -31 21 -31q8 0 18 2l8 -36q-15 -5 -32 -5z" />
<glyph unicode="&#xfb03;" horiz-adv-x="758" d="M366 0v442h-223v-442h-44v442h-66v34l66 4v89q0 74 34 113t96 39q42 0 81 -18l-12 -36q-32 16 -69 16q-86 0 -86 -117v-86h223v107q0 71 30.5 107.5t87.5 36.5q35 0 70 -16l-12 -36q-27 14 -57 14q-75 0 -75 -109v-104h111v-38h-111v-442h-44zM622 0v480h44v-480h-44z M645 596q-17 0 -28.5 11t-11.5 28q0 18 11.5 29t28.5 11t28.5 -11t11.5 -29q0 -17 -11.5 -28t-28.5 -11z" />
<glyph unicode="&#xfb04;" horiz-adv-x="766" d="M366 0v442h-223v-442h-44v442h-66v34l66 4v89q0 74 34 113t96 39q42 0 81 -18l-12 -36q-32 16 -69 16q-86 0 -86 -117v-86h223v107q0 71 30.5 107.5t87.5 36.5q35 0 70 -16l-12 -36q-27 14 -57 14q-75 0 -75 -109v-104h111v-38h-111v-442h-44zM681 -12q-59 0 -59 76v655 h44v-661q0 -31 21 -31q8 0 18 2l8 -36q-15 -5 -32 -5z" />
<hkern u1="&#x2f;" u2="&#xef;" k="-76" />
<hkern u1="&#x2f;" u2="&#xee;" k="-76" />
<hkern u1="&#x2f;" u2="&#xec;" k="-4" />
<hkern u1="F" u2="&#xef;" k="-39" />
<hkern u1="F" u2="&#xee;" k="-37" />
<hkern u1="V" u2="&#xef;" k="-61" />
<hkern u1="V" u2="&#xee;" k="-58" />
<hkern u1="V" u2="&#xed;" k="-18" />
<hkern u1="V" u2="&#xec;" k="-20" />
<hkern u1="x" u2="&#x3b;" k="-2" />
<hkern u1="x" u2="&#x2c;" k="-2" />
<hkern g1="backslash" 	g2="Eth" 	k="37" />
<hkern g1="backslash" 	g2="g" 	k="-38" />
<hkern g1="backslash" 	g2="j" 	k="-78" />
<hkern g1="backslash" 	g2="T" 	k="96" />
<hkern g1="backslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="37" />
<hkern g1="backslash" 	g2="v" 	k="20" />
<hkern g1="backslash" 	g2="V" 	k="58" />
<hkern g1="backslash" 	g2="w" 	k="10" />
<hkern g1="backslash" 	g2="W" 	k="37" />
<hkern g1="backslash" 	g2="y,yacute,ydieresis" 	k="-18" />
<hkern g1="backslash" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="exclamdown" 	g2="j" 	k="-38" />
<hkern g1="exclamdown" 	g2="V" 	k="20" />
<hkern g1="exclamdown" 	g2="W" 	k="10" />
<hkern g1="exclamdown" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="periodcentered" 	g2="T" 	k="61" />
<hkern g1="periodcentered" 	g2="V" 	k="20" />
<hkern g1="periodcentered" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="periodcentered" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="21" />
<hkern g1="periodcentered" 	g2="S" 	k="21" />
<hkern g1="periodcentered" 	g2="x" 	k="20" />
<hkern g1="periodcentered" 	g2="X" 	k="20" />
<hkern g1="periodcentered" 	g2="Z" 	k="41" />
<hkern g1="questiondown" 	g2="j" 	k="-67" />
<hkern g1="questiondown" 	g2="T" 	k="82" />
<hkern g1="questiondown" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="questiondown" 	g2="V" 	k="41" />
<hkern g1="questiondown" 	g2="W" 	k="31" />
<hkern g1="questiondown" 	g2="Y,Yacute,Ydieresis" 	k="68" />
<hkern g1="questiondown" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="46" />
<hkern g1="questiondown" 	g2="S" 	k="20" />
<hkern g1="questiondown" 	g2="X" 	k="26" />
<hkern g1="questiondown" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="24" />
<hkern g1="questiondown" 	g2="f,uniFB01,uniFB02" 	k="46" />
<hkern g1="questiondown" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="13" />
<hkern g1="questiondown" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="21" />
<hkern g1="slash" 	g2="g" 	k="10" />
<hkern g1="slash" 	g2="j" 	k="-36" />
<hkern g1="slash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="slash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="21" />
<hkern g1="slash" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="slash" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="slash" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-36" />
<hkern g1="slash" 	g2="J" 	k="80" />
<hkern g1="slash" 	g2="t" 	k="-17" />
<hkern g1="slash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="t" 	k="11" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="T" 	k="21" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="V" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="Y,Yacute,Ydieresis" 	k="21" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="asterisk" 	k="60" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="11" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="44" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="V" 	k="11" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="55" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="asterisk" 	k="100" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="periodcentered" 	k="21" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-28" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="9" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="-28" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="9" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="17" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="X" 	k="-10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Z" 	k="11" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="question" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="58" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="44" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="backslash" 	k="40" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="registered" 	k="71" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="trademark" 	k="40" />
<hkern g1="B" 	g2="t" 	k="10" />
<hkern g1="B" 	g2="T" 	k="21" />
<hkern g1="B" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="B" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="B" 	g2="asterisk" 	k="30" />
<hkern g1="B" 	g2="periodcentered" 	k="20" />
<hkern g1="B" 	g2="quoteleft,quotedblleft" 	k="11" />
<hkern g1="B" 	g2="trademark" 	k="20" />
<hkern g1="B" 	g2="J" 	k="15" />
<hkern g1="B" 	g2="S" 	k="11" />
<hkern g1="B" 	g2="v" 	k="11" />
<hkern g1="B" 	g2="w" 	k="10" />
<hkern g1="B" 	g2="x" 	k="10" />
<hkern g1="B" 	g2="y,yacute,ydieresis" 	k="11" />
<hkern g1="c,ccedilla" 	g2="t" 	k="10" />
<hkern g1="c,ccedilla" 	g2="T" 	k="20" />
<hkern g1="c,ccedilla" 	g2="V" 	k="11" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="21" />
<hkern g1="c,ccedilla" 	g2="periodcentered" 	k="11" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="c,ccedilla" 	g2="registered" 	k="-29" />
<hkern g1="c,ccedilla" 	g2="v" 	k="-9" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-9" />
<hkern g1="c,ccedilla" 	g2="x" 	k="-15" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-9" />
<hkern g1="c,ccedilla" 	g2="g" 	k="10" />
<hkern g1="c,ccedilla" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="17" />
<hkern g1="c,ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="11" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="11" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="22" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="21" />
<hkern g1="C,Ccedilla" 	g2="periodcentered" 	k="40" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="24" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="11" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="registered" 	k="-19" />
<hkern g1="C,Ccedilla" 	g2="trademark" 	k="-30" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="S" 	k="21" />
<hkern g1="C,Ccedilla" 	g2="v" 	k="11" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="11" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="hyphen,uni00AD,endash,emdash" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="11" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="21" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="V" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="asterisk" 	k="40" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="15" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="backslash" 	k="11" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="registered" 	k="-7" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="trademark" 	k="12" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="20" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v" 	k="-12" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="-12" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="-12" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="g" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="hyphen,uni00AD,endash,emdash" 	k="-10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="guillemotleft,guilsinglleft" 	k="-7" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="z" 	k="-4" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="21" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="v" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="x" 	k="23" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f" 	g2="T" 	k="-49" />
<hkern g1="f" 	g2="V" 	k="-69" />
<hkern g1="f" 	g2="Y,Yacute,Ydieresis" 	k="-67" />
<hkern g1="f" 	g2="quoteright,quotedblright" 	k="-40" />
<hkern g1="f" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="f" 	g2="periodcentered" 	k="20" />
<hkern g1="f" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="f" 	g2="X" 	k="-38" />
<hkern g1="f" 	g2="question" 	k="-29" />
<hkern g1="f" 	g2="quoteleft,quotedblleft" 	k="-40" />
<hkern g1="f" 	g2="quotedbl,quotesingle" 	k="-40" />
<hkern g1="f" 	g2="backslash" 	k="-79" />
<hkern g1="f" 	g2="registered" 	k="-90" />
<hkern g1="f" 	g2="trademark" 	k="-79" />
<hkern g1="f" 	g2="v" 	k="-18" />
<hkern g1="f" 	g2="g" 	k="15" />
<hkern g1="f" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="f" 	g2="hyphen,uni00AD,endash,emdash" 	k="11" />
<hkern g1="f" 	g2="W" 	k="-49" />
<hkern g1="f" 	g2="z" 	k="11" />
<hkern g1="f" 	g2="j" 	k="10" />
<hkern g1="f" 	g2="parenright,bracketright,braceright" 	k="-57" />
<hkern g1="f" 	g2="exclam" 	k="-20" />
<hkern g1="f" 	g2="slash" 	k="20" />
<hkern g1="F" 	g2="t" 	k="10" />
<hkern g1="F" 	g2="V" 	k="-10" />
<hkern g1="F" 	g2="Y,Yacute,Ydieresis" 	k="-11" />
<hkern g1="F" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="60" />
<hkern g1="F" 	g2="periodcentered" 	k="11" />
<hkern g1="F" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="31" />
<hkern g1="F" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="36" />
<hkern g1="F" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="F" 	g2="s" 	k="16" />
<hkern g1="F" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="15" />
<hkern g1="F" 	g2="X" 	k="21" />
<hkern g1="F" 	g2="Z" 	k="30" />
<hkern g1="F" 	g2="registered" 	k="-29" />
<hkern g1="F" 	g2="trademark" 	k="-54" />
<hkern g1="F" 	g2="J" 	k="141" />
<hkern g1="F" 	g2="S" 	k="16" />
<hkern g1="F" 	g2="v" 	k="11" />
<hkern g1="F" 	g2="w" 	k="10" />
<hkern g1="F" 	g2="x" 	k="20" />
<hkern g1="F" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="F" 	g2="g" 	k="21" />
<hkern g1="F" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="11" />
<hkern g1="F" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="F" 	g2="W" 	k="-10" />
<hkern g1="F" 	g2="z" 	k="17" />
<hkern g1="F" 	g2="slash" 	k="81" />
<hkern g1="F" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="germandbls" 	g2="t" 	k="15" />
<hkern g1="germandbls" 	g2="quoteright,quotedblright" 	k="37" />
<hkern g1="germandbls" 	g2="question" 	k="10" />
<hkern g1="germandbls" 	g2="quoteleft,quotedblleft" 	k="35" />
<hkern g1="germandbls" 	g2="quotedbl,quotesingle" 	k="62" />
<hkern g1="germandbls" 	g2="backslash" 	k="20" />
<hkern g1="germandbls" 	g2="registered" 	k="24" />
<hkern g1="germandbls" 	g2="v" 	k="10" />
<hkern g1="germandbls" 	g2="w" 	k="11" />
<hkern g1="germandbls" 	g2="x" 	k="-9" />
<hkern g1="germandbls" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="g" 	g2="T" 	k="20" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="g" 	g2="asterisk" 	k="38" />
<hkern g1="g" 	g2="periodcentered" 	k="11" />
<hkern g1="g" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="15" />
<hkern g1="g" 	g2="question" 	k="30" />
<hkern g1="g" 	g2="registered" 	k="-20" />
<hkern g1="g" 	g2="y,yacute,ydieresis" 	k="-22" />
<hkern g1="g" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="11" />
<hkern g1="g" 	g2="z" 	k="11" />
<hkern g1="g" 	g2="j" 	k="-32" />
<hkern g1="g" 	g2="parenright,bracketright,braceright" 	k="-20" />
<hkern g1="g" 	g2="slash" 	k="-60" />
<hkern g1="G" 	g2="T" 	k="20" />
<hkern g1="G" 	g2="V" 	k="11" />
<hkern g1="G" 	g2="asterisk" 	k="31" />
<hkern g1="G" 	g2="registered" 	k="-20" />
<hkern g1="G" 	g2="trademark" 	k="-25" />
<hkern g1="J" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="21" />
<hkern g1="J" 	g2="J" 	k="40" />
<hkern g1="k" 	g2="t" 	k="13" />
<hkern g1="k" 	g2="T" 	k="24" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="7" />
<hkern g1="k" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="k" 	g2="asterisk" 	k="22" />
<hkern g1="k" 	g2="colon,semicolon" 	k="-18" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-18" />
<hkern g1="k" 	g2="periodcentered" 	k="20" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="11" />
<hkern g1="k" 	g2="registered" 	k="-7" />
<hkern g1="k" 	g2="trademark" 	k="20" />
<hkern g1="k" 	g2="g" 	k="10" />
<hkern g1="k" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="k" 	g2="hyphen,uni00AD,endash,emdash" 	k="21" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="k" 	g2="guillemotright,guilsinglright" 	k="3" />
<hkern g1="k" 	g2="j" 	k="10" />
<hkern g1="K" 	g2="t" 	k="22" />
<hkern g1="K" 	g2="T" 	k="12" />
<hkern g1="K" 	g2="V" 	k="9" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="K" 	g2="asterisk" 	k="40" />
<hkern g1="K" 	g2="periodcentered" 	k="40" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="K" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="15" />
<hkern g1="K" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="11" />
<hkern g1="K" 	g2="quotedbl,quotesingle" 	k="13" />
<hkern g1="K" 	g2="registered" 	k="-19" />
<hkern g1="K" 	g2="trademark" 	k="-17" />
<hkern g1="K" 	g2="v" 	k="11" />
<hkern g1="K" 	g2="w" 	k="10" />
<hkern g1="K" 	g2="x" 	k="10" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="11" />
<hkern g1="K" 	g2="hyphen,uni00AD,endash,emdash" 	k="17" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="K" 	g2="W" 	k="10" />
<hkern g1="K" 	g2="z" 	k="11" />
<hkern g1="K" 	g2="j" 	k="10" />
<hkern g1="l,uniFB02" 	g2="j" 	k="-7" />
<hkern g1="L" 	g2="t" 	k="16" />
<hkern g1="L" 	g2="T" 	k="120" />
<hkern g1="L" 	g2="V" 	k="75" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="L" 	g2="asterisk" 	k="140" />
<hkern g1="L" 	g2="periodcentered" 	k="80" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-8" />
<hkern g1="L" 	g2="f,uniFB01,uniFB02" 	k="11" />
<hkern g1="L" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="24" />
<hkern g1="L" 	g2="question" 	k="20" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="59" />
<hkern g1="L" 	g2="backslash" 	k="80" />
<hkern g1="L" 	g2="registered" 	k="80" />
<hkern g1="L" 	g2="trademark" 	k="81" />
<hkern g1="L" 	g2="S" 	k="19" />
<hkern g1="L" 	g2="v" 	k="26" />
<hkern g1="L" 	g2="w" 	k="31" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="26" />
<hkern g1="L" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="11" />
<hkern g1="L" 	g2="hyphen,uni00AD,endash,emdash" 	k="60" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="40" />
<hkern g1="L" 	g2="W" 	k="55" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="21" />
<hkern g1="h,m,n,ntilde" 	g2="V" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="h,m,n,ntilde" 	g2="asterisk" 	k="40" />
<hkern g1="h,m,n,ntilde" 	g2="trademark" 	k="20" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="t" 	k="12" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="T" 	k="56" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="V" 	k="18" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="Y,Yacute,Ydieresis" 	k="40" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="asterisk" 	k="36" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="15" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quoteleft,quotedblleft" 	k="20" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="quotedbl,quotesingle" 	k="37" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="backslash" 	k="20" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="x" 	k="12" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn" 	g2="hyphen,uni00AD,endash,emdash" 	k="-9" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="21" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="V" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="asterisk" 	k="40" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="X" 	k="12" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="registered" 	k="-9" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="trademark" 	k="20" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="31" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="x" 	k="11" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="9" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="slash" 	k="21" />
<hkern g1="P" 	g2="T" 	k="30" />
<hkern g1="P" 	g2="Y,Yacute,Ydieresis" 	k="10" />
<hkern g1="P" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="100" />
<hkern g1="P" 	g2="periodcentered" 	k="11" />
<hkern g1="P" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="48" />
<hkern g1="P" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="50" />
<hkern g1="P" 	g2="s" 	k="10" />
<hkern g1="P" 	g2="X" 	k="21" />
<hkern g1="P" 	g2="Z" 	k="94" />
<hkern g1="P" 	g2="registered" 	k="-39" />
<hkern g1="P" 	g2="J" 	k="149" />
<hkern g1="P" 	g2="S" 	k="10" />
<hkern g1="P" 	g2="x" 	k="11" />
<hkern g1="P" 	g2="g" 	k="39" />
<hkern g1="P" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="P" 	g2="hyphen,uni00AD,endash,emdash" 	k="38" />
<hkern g1="P" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="P" 	g2="z" 	k="20" />
<hkern g1="P" 	g2="slash" 	k="90" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-34" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-36" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="39" />
<hkern g1="r" 	g2="periodcentered" 	k="19" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="21" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="11" />
<hkern g1="r" 	g2="Z" 	k="10" />
<hkern g1="r" 	g2="quoteleft,quotedblleft" 	k="-38" />
<hkern g1="r" 	g2="backslash" 	k="-18" />
<hkern g1="r" 	g2="registered" 	k="-79" />
<hkern g1="r" 	g2="J" 	k="44" />
<hkern g1="r" 	g2="v" 	k="-36" />
<hkern g1="r" 	g2="w" 	k="-27" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-36" />
<hkern g1="r" 	g2="g" 	k="10" />
<hkern g1="r" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="r" 	g2="hyphen,uni00AD,endash,emdash" 	k="21" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="20" />
<hkern g1="r" 	g2="slash" 	k="40" />
<hkern g1="R" 	g2="T" 	k="11" />
<hkern g1="R" 	g2="V" 	k="-16" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="-7" />
<hkern g1="R" 	g2="asterisk" 	k="10" />
<hkern g1="R" 	g2="periodcentered" 	k="10" />
<hkern g1="R" 	g2="Z" 	k="10" />
<hkern g1="R" 	g2="registered" 	k="-36" />
<hkern g1="R" 	g2="J" 	k="21" />
<hkern g1="R" 	g2="S" 	k="14" />
<hkern g1="R" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="R" 	g2="hyphen,uni00AD,endash,emdash" 	k="32" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="30" />
<hkern g1="R" 	g2="W" 	k="-9" />
<hkern g1="R" 	g2="guillemotright,guilsinglright" 	k="11" />
<hkern g1="s" 	g2="t" 	k="20" />
<hkern g1="s" 	g2="T" 	k="21" />
<hkern g1="s" 	g2="V" 	k="10" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="s" 	g2="quoteright,quotedblright" 	k="3" />
<hkern g1="s" 	g2="asterisk" 	k="61" />
<hkern g1="s" 	g2="question" 	k="3" />
<hkern g1="s" 	g2="hyphen,uni00AD,endash,emdash" 	k="-10" />
<hkern g1="S" 	g2="t" 	k="21" />
<hkern g1="S" 	g2="T" 	k="20" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="S" 	g2="quoteright,quotedblright" 	k="3" />
<hkern g1="S" 	g2="asterisk" 	k="19" />
<hkern g1="S" 	g2="periodcentered" 	k="12" />
<hkern g1="S" 	g2="registered" 	k="-8" />
<hkern g1="S" 	g2="J" 	k="11" />
<hkern g1="S" 	g2="S" 	k="11" />
<hkern g1="S" 	g2="hyphen,uni00AD,endash,emdash" 	k="-11" />
<hkern g1="S" 	g2="z" 	k="-9" />
<hkern g1="Thorn" 	g2="asterisk" 	k="75" />
<hkern g1="Thorn" 	g2="backslash" 	k="40" />
<hkern g1="Thorn" 	g2="trademark" 	k="21" />
<hkern g1="Thorn" 	g2="slash" 	k="74" />
<hkern g1="t" 	g2="t" 	k="20" />
<hkern g1="t" 	g2="T" 	k="11" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-18" />
<hkern g1="t" 	g2="periodcentered" 	k="20" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="22" />
<hkern g1="t" 	g2="s" 	k="10" />
<hkern g1="t" 	g2="question" 	k="20" />
<hkern g1="t" 	g2="registered" 	k="-40" />
<hkern g1="t" 	g2="x" 	k="19" />
<hkern g1="t" 	g2="g" 	k="10" />
<hkern g1="t" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="11" />
<hkern g1="t" 	g2="hyphen,uni00AD,endash,emdash" 	k="20" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="21" />
<hkern g1="t" 	g2="guillemotright,guilsinglright" 	k="11" />
<hkern g1="t" 	g2="slash" 	k="-19" />
<hkern g1="T" 	g2="t" 	k="21" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="T" 	g2="colon,semicolon" 	k="20" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="100" />
<hkern g1="T" 	g2="periodcentered" 	k="61" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="78" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="40" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02" 	k="21" />
<hkern g1="T" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="21" />
<hkern g1="T" 	g2="s" 	k="58" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="49" />
<hkern g1="T" 	g2="X" 	k="20" />
<hkern g1="T" 	g2="Z" 	k="60" />
<hkern g1="T" 	g2="registered" 	k="-20" />
<hkern g1="T" 	g2="trademark" 	k="-40" />
<hkern g1="T" 	g2="J" 	k="129" />
<hkern g1="T" 	g2="S" 	k="47" />
<hkern g1="T" 	g2="v" 	k="38" />
<hkern g1="T" 	g2="w" 	k="40" />
<hkern g1="T" 	g2="x" 	k="47" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="38" />
<hkern g1="T" 	g2="g" 	k="78" />
<hkern g1="T" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="60" />
<hkern g1="T" 	g2="hyphen,uni00AD,endash,emdash" 	k="78" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="42" />
<hkern g1="T" 	g2="z" 	k="82" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="40" />
<hkern g1="T" 	g2="slash" 	k="101" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="49" />
<hkern g1="T" 	g2="AE" 	k="96" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="20" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="V" 	k="11" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="30" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="asterisk" 	k="30" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="19" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="X" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="42" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="S" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="slash" 	k="41" />
<hkern g1="v" 	g2="T" 	k="20" />
<hkern g1="v" 	g2="V" 	k="8" />
<hkern g1="v" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="v" 	g2="asterisk" 	k="12" />
<hkern g1="v" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="v" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="v" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="v" 	g2="Z" 	k="10" />
<hkern g1="v" 	g2="registered" 	k="-56" />
<hkern g1="v" 	g2="trademark" 	k="-17" />
<hkern g1="v" 	g2="J" 	k="40" />
<hkern g1="v" 	g2="z" 	k="21" />
<hkern g1="v" 	g2="j" 	k="10" />
<hkern g1="v" 	g2="slash" 	k="20" />
<hkern g1="V" 	g2="V" 	k="-10" />
<hkern g1="V" 	g2="quoteright,quotedblright" 	k="-18" />
<hkern g1="V" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="58" />
<hkern g1="V" 	g2="periodcentered" 	k="16" />
<hkern g1="V" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="15" />
<hkern g1="V" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="11" />
<hkern g1="V" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="V" 	g2="s" 	k="9" />
<hkern g1="V" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="30" />
<hkern g1="V" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="V" 	g2="Z" 	k="18" />
<hkern g1="V" 	g2="registered" 	k="-54" />
<hkern g1="V" 	g2="trademark" 	k="-51" />
<hkern g1="V" 	g2="J" 	k="78" />
<hkern g1="V" 	g2="S" 	k="10" />
<hkern g1="V" 	g2="v" 	k="8" />
<hkern g1="V" 	g2="w" 	k="8" />
<hkern g1="V" 	g2="x" 	k="13" />
<hkern g1="V" 	g2="y,yacute,ydieresis" 	k="8" />
<hkern g1="V" 	g2="g" 	k="22" />
<hkern g1="V" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="13" />
<hkern g1="V" 	g2="hyphen,uni00AD,endash,emdash" 	k="18" />
<hkern g1="V" 	g2="guillemotleft,guilsinglleft" 	k="22" />
<hkern g1="V" 	g2="z" 	k="27" />
<hkern g1="V" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="V" 	g2="slash" 	k="58" />
<hkern g1="V" 	g2="m,n,p,r,ntilde" 	k="20" />
<hkern g1="w" 	g2="T" 	k="21" />
<hkern g1="w" 	g2="V" 	k="8" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="21" />
<hkern g1="w" 	g2="asterisk" 	k="12" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="15" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="w" 	g2="X" 	k="11" />
<hkern g1="w" 	g2="registered" 	k="-54" />
<hkern g1="w" 	g2="trademark" 	k="-19" />
<hkern g1="w" 	g2="J" 	k="30" />
<hkern g1="w" 	g2="z" 	k="12" />
<hkern g1="w" 	g2="j" 	k="10" />
<hkern g1="w" 	g2="slash" 	k="11" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-18" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="19" />
<hkern g1="W" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="9" />
<hkern g1="W" 	g2="Z" 	k="9" />
<hkern g1="W" 	g2="registered" 	k="-47" />
<hkern g1="W" 	g2="trademark" 	k="-31" />
<hkern g1="W" 	g2="J" 	k="76" />
<hkern g1="W" 	g2="S" 	k="9" />
<hkern g1="W" 	g2="g" 	k="18" />
<hkern g1="W" 	g2="hyphen,uni00AD,endash,emdash" 	k="10" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="W" 	g2="z" 	k="10" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="10" />
<hkern g1="W" 	g2="slash" 	k="41" />
<hkern g1="x" 	g2="t" 	k="21" />
<hkern g1="x" 	g2="T" 	k="38" />
<hkern g1="x" 	g2="V" 	k="13" />
<hkern g1="x" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="x" 	g2="asterisk" 	k="24" />
<hkern g1="x" 	g2="periodcentered" 	k="20" />
<hkern g1="x" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="x" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="x" 	g2="X" 	k="10" />
<hkern g1="x" 	g2="registered" 	k="-54" />
<hkern g1="x" 	g2="trademark" 	k="-17" />
<hkern g1="x" 	g2="y,yacute,ydieresis" 	k="-7" />
<hkern g1="x" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="x" 	g2="hyphen,uni00AD,endash,emdash" 	k="11" />
<hkern g1="x" 	g2="guillemotleft,guilsinglleft" 	k="21" />
<hkern g1="x" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="x" 	g2="exclam" 	k="11" />
<hkern g1="X" 	g2="t" 	k="10" />
<hkern g1="X" 	g2="T" 	k="20" />
<hkern g1="X" 	g2="asterisk" 	k="10" />
<hkern g1="X" 	g2="periodcentered" 	k="40" />
<hkern g1="X" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="10" />
<hkern g1="X" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-8" />
<hkern g1="X" 	g2="f,uniFB01,uniFB02" 	k="10" />
<hkern g1="X" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="X" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="X" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="X" 	g2="quotedbl,quotesingle" 	k="10" />
<hkern g1="X" 	g2="registered" 	k="-20" />
<hkern g1="X" 	g2="trademark" 	k="-22" />
<hkern g1="X" 	g2="J" 	k="-7" />
<hkern g1="X" 	g2="S" 	k="11" />
<hkern g1="X" 	g2="v" 	k="10" />
<hkern g1="X" 	g2="w" 	k="11" />
<hkern g1="X" 	g2="x" 	k="10" />
<hkern g1="X" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="X" 	g2="hyphen,uni00AD,endash,emdash" 	k="22" />
<hkern g1="X" 	g2="guillemotleft,guilsinglleft" 	k="11" />
<hkern g1="X" 	g2="z" 	k="11" />
<hkern g1="X" 	g2="guillemotright,guilsinglright" 	k="11" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="y,yacute,ydieresis" 	g2="registered" 	k="-56" />
<hkern g1="y,yacute,ydieresis" 	g2="trademark" 	k="-18" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="40" />
<hkern g1="y,yacute,ydieresis" 	g2="x" 	k="11" />
<hkern g1="y,yacute,ydieresis" 	g2="z" 	k="21" />
<hkern g1="y,yacute,ydieresis" 	g2="j" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="slash" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="15" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="11" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-17" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="79" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="periodcentered" 	k="40" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="62" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="11" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="11" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="33" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="29" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="registered" 	k="-38" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="trademark" 	k="-49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="100" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="15" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="v" 	k="11" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="21" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="x" 	k="31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="11" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="60" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,uni00AD,endash,emdash" 	k="60" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="44" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="42" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="28" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="slash" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="40" />
<hkern g1="z" 	g2="T" 	k="29" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="z" 	g2="periodcentered" 	k="12" />
<hkern g1="z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="28" />
<hkern g1="z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="z" 	g2="registered" 	k="-36" />
<hkern g1="z" 	g2="trademark" 	k="-18" />
<hkern g1="z" 	g2="g" 	k="9" />
<hkern g1="z" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="z" 	g2="hyphen,uni00AD,endash,emdash" 	k="10" />
<hkern g1="Z" 	g2="t" 	k="11" />
<hkern g1="Z" 	g2="V" 	k="9" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="11" />
<hkern g1="Z" 	g2="periodcentered" 	k="60" />
<hkern g1="Z" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="19" />
<hkern g1="Z" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="9" />
<hkern g1="Z" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="Z" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="28" />
<hkern g1="Z" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="Z" 	g2="Z" 	k="19" />
<hkern g1="Z" 	g2="registered" 	k="-20" />
<hkern g1="Z" 	g2="trademark" 	k="-20" />
<hkern g1="Z" 	g2="J" 	k="38" />
<hkern g1="Z" 	g2="S" 	k="30" />
<hkern g1="Z" 	g2="v" 	k="19" />
<hkern g1="Z" 	g2="w" 	k="19" />
<hkern g1="Z" 	g2="x" 	k="21" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="19" />
<hkern g1="Z" 	g2="g" 	k="15" />
<hkern g1="Z" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="26" />
<hkern g1="Z" 	g2="hyphen,uni00AD,endash,emdash" 	k="40" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="40" />
<hkern g1="Z" 	g2="W" 	k="9" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="j" 	k="-80" />
<hkern g1="parenleft,bracketleft,braceleft" 	g2="J" 	k="20" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-4" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="colon,semicolon" 	g2="asterisk" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="80" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="asterisk" 	k="121" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="t" 	k="36" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="98" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="16" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="v" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="V" 	k="58" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="15" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteleft,quotedblleft" 	k="61" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quoteright,quotedblright" 	k="81" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="quotedbl,quotesingle" 	k="77" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="J" 	k="20" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="60" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="t" 	k="29" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="T" 	k="42" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="V" 	k="18" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="W" 	k="10" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="S" 	k="12" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="x" 	k="11" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="X" 	k="22" />
<hkern g1="hyphen,uni00AD,endash,emdash" 	g2="Z" 	k="20" />
<hkern g1="exclam" 	g2="quoteright,quotedblright" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="28" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t" 	k="11" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="40" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="V" 	k="12" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="12" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="x" 	k="20" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="X" 	k="11" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="30" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="44" />
<hkern g1="guillemotright,guilsinglright" 	g2="t" 	k="38" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="40" />
<hkern g1="guillemotright,guilsinglright" 	g2="V" 	k="22" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="30" />
<hkern g1="guillemotright,guilsinglright" 	g2="x" 	k="21" />
<hkern g1="guillemotright,guilsinglright" 	g2="X" 	k="11" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="21" />
<hkern g1="question" 	g2="quoteright,quotedblright" 	k="13" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="64" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-17" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="38" />
<hkern g1="quoteleft,quotedblleft" 	g2="V" 	k="-18" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-18" />
<hkern g1="quoteleft,quotedblleft" 	g2="S" 	k="11" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="96" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="48" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="55" />
<hkern g1="quoteleft,quotedblleft" 	g2="f,uniFB01,uniFB02" 	k="11" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="38" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="10" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="59" />
<hkern g1="quoteleft,quotedblleft" 	g2="exclamdown" 	k="58" />
<hkern g1="quoteleft,quotedblleft" 	g2="questiondown" 	k="138" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="66" />
<hkern g1="quoteright,quotedblright" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="74" />
<hkern g1="quoteright,quotedblright" 	g2="AE" 	k="96" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="76" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="45" />
<hkern g1="quoteright,quotedblright" 	g2="f,uniFB01,uniFB02" 	k="19" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="56" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="57" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="120" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="z" 	k="29" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="84" />
<hkern g1="quotedbl,quotesingle" 	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="quotedbl,quotesingle" 	g2="S" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="X" 	k="10" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="44" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="104" />
</font>
</defs></svg> 