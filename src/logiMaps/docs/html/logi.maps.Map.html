<!DOCTYPE html>
<html lang="en">
<head>
    
    <meta charset="utf-8">
    <title>Map - Documentation</title>
    
    
    <script src="scripts/prettify/prettify.js"></script>
    <script src="scripts/prettify/lang-css.js"></script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>

<input type="checkbox" id="nav-trigger" class="nav-trigger" />
<label for="nav-trigger" class="navicon-button x">
  <div class="navicon"></div>
</label>

<label for="nav-trigger" class="overlay"></label>

<nav >
    
    <input type="text" id="nav-search" placeholder="Search" />
    
    
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="logi.maps.Map.html">logi.maps.Map</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addCircle">addCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addCustom">addCustom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addFont">addFont</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addGps">addGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addImage">addImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addLabel">addLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addLine">addLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addObject">addObject</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addPolygon">addPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addRoute">addRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#disableWheelEvent">disableWheelEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#enableWheelEvent">enableWheelEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findCircle">findCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findGps">findGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findImage">findImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findLabel">findLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findLine">findLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findPolygon">findPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findRoute">findRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getBounds">getBounds</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getCenter">getCenter</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getConvaxHull">getConvaxHull</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getDistrictHoverStyle">getDistrictHoverStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getDistrictStyle">getDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getDragAreaRect">getDragAreaRect</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getHoveredDistrict">getHoveredDistrict</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getLevel">getLevel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getNearestInfoOnPolyline">getNearestInfoOnPolyline</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getRealDistance">getRealDistance</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getSearchDistrictStyle">getSearchDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getSplitInfoOnPolyline">getSplitInfoOnPolyline</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getTheme">getTheme</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getZoom">getZoom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hideLayer">hideLayer</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitCircle">hitCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitCircleKey">hitCircleKey</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImage">hitImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImageKey">hitImageKey</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImageKeys">hitImageKeys</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImages">hitImages</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitPolygon">hitPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitPolygonKey">hitPolygonKey</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistCircle">isExistCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistGps">isExistGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistImage">isExistImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistLabel">isExistLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistLine">isExistLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistPolygon">isExistPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistRoute">isExistRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#move">move</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeAll">removeAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCircle">removeCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCircleAll">removeCircleAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCustom">removeCustom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCustomAll">removeCustomAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeGps">removeGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeGpsAll">removeGpsAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeImage">removeImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeImageAll">removeImageAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLabel">removeLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLabelAll">removeLabelAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLine">removeLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLineAll">removeLineAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeObject">removeObject</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removePolygon">removePolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removePolygonAll">removePolygonAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeRoute">removeRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeRouteAll">removeRouteAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#screen2world">screen2world</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#screenshot">screenshot</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setBounds">setBounds</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setCenter">setCenter</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setCenterMark">setCenterMark</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictHoverRange">setDistrictHoverRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictHoverStyle">setDistrictHoverStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictRange">setDistrictRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictStyle">setDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictVisible">setDistrictVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDragAreaMode">setDragAreaMode</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDragAreaStyle">setDragAreaStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingCircleOnMove">setDrawingCircleOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingGpsOnMove">setDrawingGpsOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingImageOnMove">setDrawingImageOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingLabelOnMove">setDrawingLabelOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingLineOnMove">setDrawingLineOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingPolygonOnMove">setDrawingPolygonOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingRouteOnMove">setDrawingRouteOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setFreezeModeOnMoving">setFreezeModeOnMoving</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setHeatmap">setHeatmap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setLevel">setLevel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setLevelRange">setLevelRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setMotionEventLock">setMotionEventLock</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setOrderType">setOrderType</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setOverlapCheck">setOverlapCheck</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setOverlapInfoVisibility">setOverlapInfoVisibility</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setSearchDistrict">setSearchDistrict</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setSearchDistrictStyle">setSearchDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setTheme">setTheme</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setZoom">setZoom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#showLayer">showLayer</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#updateMap">updateMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#world2screen">world2screen</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#zoomIn">zoomIn</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#zoomOut">zoomOut</a></li></ul></li><li><a href="logi.maps.Object.html">logi.maps.Object</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Image.html">logi.maps.Image</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#addTextInfo">addTextInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#changeImage">changeImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#changeTextBgImage">changeTextBgImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#createImage">createImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getAngle">getAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getImageSrc">getImageSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getOffsetX">getOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getOffsetY">getOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getPosition">getPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getTextInfo">getTextInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#move">move</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#removeTextInfo">removeTextInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#removeTextInfos">removeTextInfos</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setAngle">setAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setBoundaryPadding">setBoundaryPadding</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setImageSrc">setImageSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setOffsetX">setOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setOffsetY">setOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setPosition">setPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setText">setText</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setVisible">setVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#textInfo">textInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#textInfo">textInfo</a></li></ul></li><li><a href="logi.maps.Label.html">logi.maps.Label</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getAlign">getAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getAngle">getAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getBgImgOffsetX">getBgImgOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getBgImgOffsetY">getBgImgOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getBgImgSrc">getBgImgSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getFontSize">getFontSize</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getOffsetX">getOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getOffsetY">getOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getPosition">getPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getText">getText</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getTextColor">getTextColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setAlign">setAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setAngle">setAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgBox">setBgBox</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgImgOffsetX">setBgImgOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgImgOffsetY">setBgImgOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgImgSrc">setBgImgSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBoundaryPadding">setBoundaryPadding</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setFontSize">setFontSize</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setOffsetX">setOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setOffsetY">setOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setPosition">setPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setText">setText</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setTextColor">setTextColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Line.html">logi.maps.Line</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#getLineType">getLineType</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setLatLngs">setLatLngs</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setLineProperty">setLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Polygon.html">logi.maps.Polygon</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setFillColor">setFillColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setLineProperty">setLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Cirlce.html">logi.maps.Cirlce</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setCenter">setCenter</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setFillColor">setFillColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setLineProperty">setLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setRadius">setRadius</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Route.html">logi.maps.Route</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#addPassedPoint">addPassedPoint</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPassedLine">setPassedLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPassedLineProperty">setPassedLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPastRecordLine">setPastRecordLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPastRecordLineProperty">setPastRecordLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setRouteLine">setRouteLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setRouteLineProperty">setRouteLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Gps.html">logi.maps.Gps</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#addGps">addGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setGps">setGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setMatchedProperty">setMatchedProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setRawProperty">setRawProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setRelProperty">setRelProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Custom.html">logi.maps.Custom</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.TextInfo.html">logi.maps.TextInfo</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImg">bgImg</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImg">bgImg</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgAlign">bgImgAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgOffsetX">bgImgOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgOffsetY">bgImgOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgSrc">bgImgSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#fontFamily">fontFamily</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#fontSize">fontSize</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#offsetX">offsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#offsetY">offsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#text">text</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#textAlign">textAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#textBold">textBold</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#textColor">textColor</a></li></ul></li><li><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></li><li><a href="logi.maps.LatLngBound.html">logi.maps.LatLngBound</a></li><li><a href="logi.maps.Meta.html">logi.maps.Meta</a></li><li><a href="logi.maps.Point.html">logi.maps.Point</a></li></ul><h3>Global</h3><ul><li><a href="global.html#ALIGN">ALIGN</a></li><li><a href="global.html#BRIDGE_MAPEVENT">BRIDGE_MAPEVENT</a></li><li><a href="global.html#DISTRICT_DATATYPE">DISTRICT_DATATYPE</a></li><li><a href="global.html#DISTRICT_STYLE">DISTRICT_STYLE</a></li><li><a href="global.html#DISTRICT_VISIBLETYPE">DISTRICT_VISIBLETYPE</a></li><li><a href="global.html#EVENT">EVENT</a></li><li><a href="global.html#LINETYPE">LINETYPE</a></li><li><a href="global.html#OBJEVENT">OBJEVENT</a></li></ul>
    
</nav>

<div id="main">
    
    <h1 class="page-title">Map</h1>
    

    




<section>

<header>
    
        <h2>
        
            Map
        
        </h2>
        
    
</header>

<article>
    
        <div class="container-overview">
        
            

    

    <h4 class="name" id="Map"><span class="type-signature"></span>new Map<span class="signature">(mapDivId, options)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>Map을 초기화 및 생성한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let logiMap = new logi.maps.Map('div_canvas', {center: {lat:37.542760, lng: 127.044996}, level: 16});
 //지도가 서울숲역으로 이동하여 level 16 축적으로 div_canvas 영역에 그린다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>mapDivId</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>root Div ID</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>option</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>OnDoubleClick</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>더블클릭 이벤트 콜백 함수</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>OnClick</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>클릭 이벤트 콜백 함수</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>OnMouseDown</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>마우스 다운 이벤트 콜백 함수</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>OnMouseUp</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>마우스 업 이벤트 콜백 함수</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>OnMouseMove</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>마우스 이동 이벤트 콜백 함수</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>OnMapWheel</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>마우스 휠 이벤트 콜백 함수</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>OnDraw</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>맵 그리기 이벤트 콜백 함수</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>levelRange</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>맵 레벨 범위 (default: {8, 18})</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>min</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>월드 레벨</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>max</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>디테일 레벨</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>center</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



            
            </td>

            

            

            <td class="description last"><p>맵 위치</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>level</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>맵 레벨</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>extn</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>맵 타입 (png, xvg ...)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>region</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>국가 (kor, nam ...)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>theme</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>리소스 테마명</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>serverUrl</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>맵 서버 주소</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>vectorUrl</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>벡터 서버 주소</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>customDivId</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>사용자 Div ID 지정</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
        </div>
    

    

    

    
    
    

     

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    <h4 class="name" id="addCircle"><span class="type-signature"></span>addCircle<span class="signature">(circle)</span><span class="type-signature"> &rarr; {logi.maps.Circle}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>Circle을 Map에 추가한다</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let circle = new logi.maps.Circle(
 {lat: 37.5115557, lng: 127.0595261},
 64.0, 'gray');
 logiMap.addCircle(circle);
 //생성된 circle이 Map에 추가된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>circle</code></td>
            

            <td class="type">
            
                
<span class="param-type">logi.maps.Circle</span>



            
            </td>

            

            

            <td class="description last"><p>추가할 원</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>추가된 원</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">logi.maps.Circle</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="addCustom"><span class="type-signature"></span>addCustom<span class="signature">(custom)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Custom.html">logi.maps.Custom</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>커스텀 객체를 Map에 추가한다.
overlay에 그려지는 요소라서 기존 요소(이미지, 라벨 등) 보다 가장 상위에 표시 됨</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let custom = new logi.maps.Custom({lat: 37.5115557, lng: 127.0595261}, {content: '&lt;div>custom&lt;/div>'});
 logiMap.addCustom(custom);
 //생성된 커스텀 객체가 Map에 추가된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>custom</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Custom.html">logi.maps.Custom</a></span>



            
            </td>

            

            

            <td class="description last"><p>추가할 커스텀 객체</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>추가된 커스텀 객체</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Custom.html">logi.maps.Custom</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="addEventListener"><span class="type-signature"></span>addEventListener<span class="signature">(eventName, func)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>지도에서 발생되는 이벤트를 등록된 리스너로 콜백한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.addEventListener(logi.maps.EVENT.dblclick, function(event){ console.log("double click"); });
 //지도에서 더블클릭하면 console 창에 “double click”이 출력된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>eventName</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#EVENT">EVENT</a></span>



            
            </td>

            

            

            <td class="description last"><p>이벤트명</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>콜백함수</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="addFont"><span class="type-signature"></span>addFont<span class="signature">(family, source)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>사용할 폰트들을 등록한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.addFont(“LogiFont”, “url(/font/XXXKR-Medium.ttf)”);
 //XXXKR-Medium.ttf가 ‘LogiFont’ 명으로 추가된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>family</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>폰트패밀리</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>source</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>경로</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="addGps"><span class="type-signature"></span>addGps<span class="signature">(gps)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Gps.html">logi.maps.Gps</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>GPS를 Map에 추가한다</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>...</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>gps</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Gps.html">logi.maps.Gps</a></span>



            
            </td>

            

            

            <td class="description last"><p>추가할 GPS</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>추가된 GPS</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Gps.html">logi.maps.Gps</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="addImage"><span class="type-signature"></span>addImage<span class="signature">(image)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Image.html">logi.maps.Image</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>Image를 Map에 추가한다</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let image = new logi.maps.Image('./sample.png', {lat:37.50346, lng:127.01868});
 logiMap.addImage(image);
 //생성된 image가 Map에 추가된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>image</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Image.html">logi.maps.Image</a></span>



            
            </td>

            

            

            <td class="description last"><p>추가할 이미지</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>추가된 이미지</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Image.html">logi.maps.Image</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="addLabel"><span class="type-signature"></span>addLabel<span class="signature">(label)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Label.html">logi.maps.Label</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>Label을 Map에 추가한다</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let label = new logi.maps.Label('label-text', {lat:37.50346, lng:127.01868});
 logiMap.addLabel(label);
 //생성된 label이 Map에 추가된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>label</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Label.html">logi.maps.Label</a></span>



            
            </td>

            

            

            <td class="description last"><p>추가할 라벨</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>추가된 라벨</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Label.html">logi.maps.Label</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="addLine"><span class="type-signature"></span>addLine<span class="signature">(line)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Line.html">logi.maps.Line</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>Line을 Map에 추가한다</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let line = new logi.maps.Line(logi.maps.LINETYPE.STRAIGHT, {fromLatLng:{lat: 37.50346, lng: 127.01868}, toLatLng:{lat: 37.50346, lng: 127.02468}, width: 4});
 logiMap.addLine(line);
 //생성된 line이 Map에 추가된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>line</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Line.html">logi.maps.Line</a></span>



            
            </td>

            

            

            <td class="description last"><p>추가할 라인</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>추가된 라인</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Line.html">logi.maps.Line</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="addObject"><span class="type-signature"></span>addObject<span class="signature">(obj)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>생성된 Image, Label, Line, Polygon, Circle, Route, Gps, Custom을 Map에 추가한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let image = new logi.maps.Image('./sample.png', {lat:37.50346, lng:127.01868});
 logiMap.addObject(image);
 //생성된 image가 Map에 추가된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>obj</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Object.html">logi.maps.Object</a></span>



            
            </td>

            

            

            <td class="description last"><p>추가할 Object</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="addPolygon"><span class="type-signature"></span>addPolygon<span class="signature">(polygon)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Polygon.html">logi.maps.Polygon</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>Polygon을 Map에 추가한다</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let polygon = new logi.maps.Polygon([
 {lat: 37.5115557, lng: 127.0595261},
 {lat: 37.5062379, lng: 127.0050378},
 {lat: 37.5665960, lng: 127.0077020},
 {lat: 37.5115557, lng: 127.0595261}
 ], 'gray');
 logiMap.addPolygon(polygon);
 //생성된 polygon이 Map에 추가된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>polygon</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Polygon.html">logi.maps.Polygon</a></span>



            
            </td>

            

            

            <td class="description last"><p>추가할 폴리곤</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>추가된 폴리곤</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Polygon.html">logi.maps.Polygon</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="addRoute"><span class="type-signature"></span>addRoute<span class="signature">(route)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Route.html">logi.maps.Route</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>Route를 Map에 추가한다</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let route = new logi.maps.Route([
 {lat: 37.5115557, lng: 127.0595261},
 {lat: 37.5062379, lng: 127.0050378},
 {lat: 37.5665960, lng: 127.0077020}], {
 routeLine: {width: 4, color: ‘#0088FF’}});
 logiMap.addRoute(route);
 //생성된 route를 Map에 추가된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>route</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Route.html">logi.maps.Route</a></span>



            
            </td>

            

            

            <td class="description last"><p>추가할 경로</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>추가된 경로</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Route.html">logi.maps.Route</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="disableWheelEvent"><span class="type-signature"></span>disableWheelEvent<span class="signature">()</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>마우스 휠 이벤트를 비활성화한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.disableWheelEvent();
 //마우스 휠에 따라 지도 레벨이 변경되지 않는다.</code></pre>




















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="enableWheelEvent"><span class="type-signature"></span>enableWheelEvent<span class="signature">()</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>마우스 휠 이벤트를 활성화한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.enableWheelEvent();
 //마우스 휠에 따라 지도 레벨이 변경된다.</code></pre>




















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="findCircle"><span class="type-signature"></span>findCircle<span class="signature">(keyword)</span><span class="type-signature"> &rarr; {logi.maps.Circle|Array.&lt;logi.maps.Circle>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.</li>
<li>key, rect 또는 class 값으로 등록된 Circle을 찾는다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>let circle = logiMap.findCircle(‘key-01’);
 //‘key-01’로 등록된 circle이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let circle = logiMap.findCircle({key: ‘key-01’});
 //key 값으로 등록된 circle이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let circles = logiMap.findCircle({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
 //영역에 포함되는 circle 배열이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let circles = logiMap.findCircle({class: ‘cir’});
 //class 값으로 등록된 circle 배열이 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>원 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rect</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>영역 rect</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>원 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <ul>
<li>logi.maps.Circle: key 검색</li>
<li>logi.maps.Circle[]: rect 또는 class 검색</li>
</ul>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">logi.maps.Circle</span>
|

<span class="param-type">Array.&lt;logi.maps.Circle></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="findGps"><span class="type-signature"></span>findGps<span class="signature">(keyword)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Gps.html">logi.maps.Gps</a>|Array.&lt;<a href="logi.maps.Gps.html">logi.maps.Gps</a>>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.</li>
<li>key 또는 class 값으로 등록된 GPS를 찾는다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>let gps = logiMap.findGps(‘key-01’);
 //‘key-01’로 등록된 gps가 전달된다.</code></pre>

    <pre class="prettyprint"><code>let gps = logiMap.findGps({key: ‘key-01’});
 //key 값으로 등록된 gps가 전달된다.</code></pre>

    <pre class="prettyprint"><code>let gpss = logiMap.findGps({class: ‘gps’});
 //class 값으로 등록된 gps 배열이 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>GPS key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>GPS class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <ul>
<li>logi.maps.Gps: key 검색</li>
<li>logi.maps.Gps[]: class 검색</li>
</ul>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Gps.html">logi.maps.Gps</a></span>
|

<span class="param-type">Array.&lt;<a href="logi.maps.Gps.html">logi.maps.Gps</a>></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="findImage"><span class="type-signature"></span>findImage<span class="signature">(keyword)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Image.html">logi.maps.Image</a>|Array.&lt;<a href="logi.maps.Image.html">logi.maps.Image</a>>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.</li>
<li>key, rect 또는 class 값으로 등록된 Image를 찾는다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>let image = logiMap.findImage(‘key-01’);
 //‘key-01’로 등록된 image가 전달된다.</code></pre>

    <pre class="prettyprint"><code>let image = logiMap.findImage({key: ‘key-01’});
 //key 값으로 등록된 image 전달된다.</code></pre>

    <pre class="prettyprint"><code>let images = logiMap.findImage({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
 //영역에 포함되는 image 배열이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let images = logiMap.findImage({class: ‘pin’});
 //class 값으로 등록된 image 배열이 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>이미지 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rect</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>영역 rect</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>이미지 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <ul>
<li>logi.maps.Image: key 검색</li>
<li>logi.maps.Image[]: rect 또는 class 검색</li>
</ul>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Image.html">logi.maps.Image</a></span>
|

<span class="param-type">Array.&lt;<a href="logi.maps.Image.html">logi.maps.Image</a>></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="findLabel"><span class="type-signature"></span>findLabel<span class="signature">(keyword)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Label.html">logi.maps.Label</a>|Array.&lt;<a href="logi.maps.Label.html">logi.maps.Label</a>>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.</li>
<li>key, rect 또는 class 값으로 등록된 Label을 찾는다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>let label = logiMap.findLabel(‘key-01’);
 //‘key-01’로 등록된 label이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let label = logiMap.findLabel({key: ‘key-01’});
 //key 값으로 등록된 label이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let labels = logiMap.findLabel({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
 //영역에 포함되는 label 배열이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let labels = logiMap.findLabel({class: ‘lbl’});
 //class 값으로 등록된 label 배열이 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>라벨 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rect</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>영역 rect</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>라벨 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <ul>
<li>logi.maps.Label: key 검색</li>
<li>logi.maps.Label[]: rect 또는 class 검색</li>
</ul>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Label.html">logi.maps.Label</a></span>
|

<span class="param-type">Array.&lt;<a href="logi.maps.Label.html">logi.maps.Label</a>></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="findLine"><span class="type-signature"></span>findLine<span class="signature">(keyword)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Line.html">logi.maps.Line</a>|Array.&lt;<a href="logi.maps.Line.html">logi.maps.Line</a>>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.</li>
<li>key, rect 또는 class 값으로 등록된 Line을 찾는다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>let line = logiMap.findLine(‘key-01’);
 //‘key-01’로 등록된 line이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let line = logiMap.findLine({key: ‘key-01’});
 //key 값으로 등록된 line이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let lines = logiMap.findLine({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
 //영역에 포함되는 line 배열이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let lines = logiMap.findLine({class: ‘ln’});
 //class 값으로 등록된 line 배열이 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>라인 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rect</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>영역 rect</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>라인 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <ul>
<li>logi.maps.Line: key 검색</li>
<li>logi.maps.Line[]: rect 또는 class 검색</li>
</ul>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Line.html">logi.maps.Line</a></span>
|

<span class="param-type">Array.&lt;<a href="logi.maps.Line.html">logi.maps.Line</a>></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="findPolygon"><span class="type-signature"></span>findPolygon<span class="signature">(keyword)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Polygon.html">logi.maps.Polygon</a>|Array.&lt;<a href="logi.maps.Polygon.html">logi.maps.Polygon</a>>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.</li>
<li>key, rect 또는 class 값으로 등록된 Polygon을 찾는다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>let polygon = logiMap.findPolygon(‘key-01’);
 //‘key-01’로 등록된 polygon이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let polygon = logiMap.findPolygon({key: ‘key-01’});
 //key 값으로 등록된 polygon이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let polygons = logiMap.findPolygon({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
 //영역에 포함되는 polygon 배열이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let polygons = logiMap.findPolygon({class: ‘ply’});
 //class 값으로 등록된 polygon 배열이 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>폴리곤 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rect</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>영역 rect</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>폴리곤 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <ul>
<li>logi.maps.Polygon: key 검색</li>
<li>logi.maps.Polygon[]: rect 또는 class 검색</li>
</ul>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Polygon.html">logi.maps.Polygon</a></span>
|

<span class="param-type">Array.&lt;<a href="logi.maps.Polygon.html">logi.maps.Polygon</a>></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="findRoute"><span class="type-signature"></span>findRoute<span class="signature">(keyword)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Route.html">logi.maps.Route</a>|Array.&lt;<a href="logi.maps.Route.html">logi.maps.Route</a>>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.</li>
<li>key, rect 또는 class 값으로 등록된 Route를 찾는다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>let route = logiMap.findRoute(‘key-01’);
 //‘key-01’로 등록된 route가 전달된다.</code></pre>

    <pre class="prettyprint"><code>let route = logiMap.findRoute({key: ‘key-01’});
 //key 값으로 등록된 route가 전달된다.</code></pre>

    <pre class="prettyprint"><code>let routes = logiMap.findRoute({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
 //영역에 포함되는 route 배열이 전달된다.</code></pre>

    <pre class="prettyprint"><code>let routes = logiMap.findRoute({class: ‘rt’});
 //class 값으로 등록된 route 배열이 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>경로 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>rect</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>></span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>영역 rect</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>경로 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <ul>
<li>logi.maps.Route: key 검색</li>
<li>logi.maps.Route[]: rect 또는 class 검색</li>
</ul>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Route.html">logi.maps.Route</a></span>
|

<span class="param-type">Array.&lt;<a href="logi.maps.Route.html">logi.maps.Route</a>></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getBounds"><span class="type-signature"></span>getBounds<span class="signature">()</span><span class="type-signature"> &rarr; {Object}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>지도화면의 바운더리 경계값을 리턴한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let worldBoundary = logiMap.getBounds();
 //지도화면의 바운더리 경계값이 리턴된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>바운더리 좌표</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Object</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getCenter"><span class="type-signature"></span>getCenter<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>현재 지도 위치를 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let center = logiMap.getCenter();
 //{lat: 37.5436, lng: 127.0447} 지도 위치가 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>현재 지도 좌표</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getConvaxHull"><span class="type-signature"></span>getConvaxHull<span class="signature">(latlngs, expandMeter)</span><span class="type-signature"> &rarr; {Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>좌표배열과 확장 값을 입력하면 모든 좌표를 포함하는 폴리곤 좌표 리스트를 리턴한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let convaxHull = logiMap. getConvaxHull([
 {lat: 37.5115557, lng: 127.0595261},
 {lat: 37.5062379, lng: 127.0050378},
 {lat: 37.566596, lng: 127.007702}
 ], 10);
 //ConvaxHull 계산을 하여 폴리곤 좌표가 리턴된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>latlngs</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>></span>



            
            </td>

            

            

            <td class="description last"><p>좌표 배열</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>expandMeter</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>확장값</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>폴리곤 좌표 배열</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getDistrictHoverStyle"><span class="type-signature"></span>getDistrictHoverStyle<span class="signature">(dataType)</span><span class="type-signature"> &rarr; {<a href="global.html#DISTRICT_STYLE">DISTRICT_STYLE</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>행정 구역(우편번호, 시도, 시군구, 읍면동, ...) Hover 스타일을 리턴한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let style = logiMap.getDistrictHoverStyle(logi.maps.DISTRICT_DATATYPE.SGG);
 //시군구 Hover에 설정된 스타일이 리턴된다</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dataType</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#DISTRICT_DATATYPE">DISTRICT_DATATYPE</a></span>



            
            </td>

            

            

            <td class="description last"><p>행정 구역 타입</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>스타일</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="global.html#DISTRICT_STYLE">DISTRICT_STYLE</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getDistrictStyle"><span class="type-signature"></span>getDistrictStyle<span class="signature">(dataType)</span><span class="type-signature"> &rarr; {<a href="global.html#DISTRICT_STYLE">DISTRICT_STYLE</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>행정 구역(우편번호, 시도, 시군구, 읍면동, ...) 스타일을 리턴한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let style = logiMap.getDistrictStyle(logi.maps.DISTRICT_DATATYPE.SGG);
 //시군구에 설정된 스타일이 리턴된다</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dataType</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#DISTRICT_DATATYPE">DISTRICT_DATATYPE</a></span>



            
            </td>

            

            

            <td class="description last"><p>행정 구역 타입</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>스타일</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="global.html#DISTRICT_STYLE">DISTRICT_STYLE</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getDragAreaRect"><span class="type-signature"></span>getDragAreaRect<span class="signature">()</span><span class="type-signature"> &rarr; {Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>드래그 사각 영역의 좌표 두개를 리턴한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let rect = logiMap.getDragAreaRect();
 //드래그 사각 영역의 좌표 두개가 배열로 리턴된다</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>드래그 사각 영역의 월드 좌표 2개</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getHoveredDistrict"><span class="type-signature"></span>getHoveredDistrict<span class="signature">()</span><span class="type-signature"> &rarr; {Object}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>Hover 모드에서 활성화된 영역의 정보를 리턴한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.getHoveredDistrict();
 //활성화된 영역의 정보가 리턴된다.(없으면 null)</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        


<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Object</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getLevel"><span class="type-signature"></span>getLevel<span class="signature">()</span><span class="type-signature"> &rarr; {Number}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>현재 지도 타일 Level을 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    
        <dt class="important tag-deprecated">Deprecated:</dt><dd><ul class="dummy"><li>This method is deprecated. Use getZoom() instead.</li></ul></dd>
    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let level = logiMap.getLevel();
 //현재 지도 타일 Level이 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>지도 타일 레벨</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getNearestInfoOnPolyline"><span class="type-signature"></span>getNearestInfoOnPolyline<span class="signature">(latlngs, latlng)</span><span class="type-signature"> &rarr; {Object}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>입력된 지점과 가장 가까운 라인의 점을 계산한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let result = logiMap.getNearestInfoOnPolyline([{lat: 37.54248, lng:127.04511}, {lat: 37.5427, lng: 127.04447}, {lat: 37.54304, lng:127.04457}], {lat:37.542816, lng:127.045103});
 //{lat: 37.542517, lng: 127.045000, distance: 0.000315}의 결과 값을 얻는다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>latlngs</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>></span>



            
            </td>

            

            

            <td class="description last"><p>대상 라인</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>latlng</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



            
            </td>

            

            

            <td class="description last"><p>기준점</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>가장 가까운 지점 정보 (경도, 위도, 거리)</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Object</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getRealDistance"><span class="type-signature"></span>getRealDistance<span class="signature">(fromLatLng, toLatLng)</span><span class="type-signature"> &rarr; {Number}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>두 월드 좌표의 거리를 meter 단위로 계산한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let distance = logiMap.getRealDistance({lat:37.679709, lng:126.842542}, {lat: 37.501278, lng: 127.233375});
 //39742.926 (meter) 거리가 계산된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fromLatLng</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



            
            </td>

            

            

            <td class="description last"><p>시작 월드 좌표</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>toLatLng</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



            
            </td>

            

            

            <td class="description last"><p>끝 월드 좌표</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>meter 거리</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getSearchDistrictStyle"><span class="type-signature"></span>getSearchDistrictStyle<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="global.html#DISTRICT_STYLE">DISTRICT_STYLE</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>검색된 행정 구역(시도, 시군구, 읍면동, ...) 스타일을 리턴한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let style = logiMap.getSearchDistrictStyle();
 //설정된 스타일이 리턴된다</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>스타일</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="global.html#DISTRICT_STYLE">DISTRICT_STYLE</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getSplitInfoOnPolyline"><span class="type-signature"></span>getSplitInfoOnPolyline<span class="signature">(latlngs, ratio)</span><span class="type-signature"> &rarr; {Object}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라인의 길이를 1.0으로 했을 경우 ratio(0.0~1.0) 위치의 좌표와 각도를 계산한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let distance = logiMap.getRealDistance({lat:37.679709, lng:126.842542}, {lat: 37.501278, lng: 127.233375});
 //39742.926 (meter) 거리가 계산된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>latlngs</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>></span>



            
            </td>

            

            

            <td class="description last"><p>시작 월드 좌표</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>ratio</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>끝 월드 좌표</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>위치 정보 (위도, 경도, 각도)</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Object</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getTheme"><span class="type-signature"></span>getTheme<span class="signature">()</span><span class="type-signature"> &rarr; {String}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>SVG(XVG) 맵 타일을 사용할 경우 맵 테마를 사용 할 수 있다.</li>
<li>resource/region/theme 이름 순으로 폴더가 구성 된다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let themeName = logiMap.getTheme();
 //themeName에 핸재 적용된 테마명이 리턴된다</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>테마명</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getZoom"><span class="type-signature"></span>getZoom<span class="signature">()</span><span class="type-signature"> &rarr; {Number}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>현재 지도 스케일이 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let zoom = logiMap.getZoom();
 //현재 지도 스케일이 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>지도 스케일</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="hideLayer"><span class="type-signature"></span>hideLayer<span class="signature">(레이어명, options)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>SVG(XVG) 맵 타일을 사용할 경우 맵 레이어 On/Off를 사용 할 수 있다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.hideLayer('rwy');
 //철도 라인이 안 그려진다.
 logiMap.hideLayer('poi', {codeType: 'category', codeList: ['11']});
 //POI 심볼 중에서 카테고리 11(건물, 일반 기업)이 안 그려진다.
 logiMap.hideLayer('poitext', {codeType: 'poi', codeList: ['92', '95']});
 //POI 글자 중에서 POI 코드 92XXXXXXX(판매업), 95XXXXXXX(생산업) 관련이 안 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>레이어명</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>옵션</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>codeType</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>코드 타입 ('category' | 'poi')</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>codeList</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>코드 리스트 (카테고리 ID 또는 POI 코드의 배열)</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="hitCircle"><span class="type-signature"></span>hitCircle<span class="signature">(x, y)</span><span class="type-signature"> &rarr; {logi.maps.Circle}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>입력된 화면 좌표와 겹치는 Circle을 찾는다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let circle = logiMap.hitCircle(250, 250);
 //스크린 좌표 (250, 250)에 있는 circle이 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 X</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 Y</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>좌표와 겹치는 원</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">logi.maps.Circle</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="hitCircleKey"><span class="type-signature"></span>hitCircleKey<span class="signature">(x, y)</span><span class="type-signature"> &rarr; {String}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>입력된 화면 좌표와 겹치는 Circle의 key를 찾는다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let circleKey = logiMap.hitCircleKey(250, 250);
 //스크린 좌표 (250, 250)에 있는 circle의 key가 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 X</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 Y</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>좌표와 겹치는 원 Key</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="hitImage"><span class="type-signature"></span>hitImage<span class="signature">(x, y)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Image.html">logi.maps.Image</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>입력된 화면 좌표와 겹치는 Image를 찾는다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let image = logiMap.hitImage(250, 250);
 //스크린 좌표 (250, 250)에 있는 image가 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 X</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 Y</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>좌표와 겹치는 이미지</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Image.html">logi.maps.Image</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="hitImageKey"><span class="type-signature"></span>hitImageKey<span class="signature">(x, y)</span><span class="type-signature"> &rarr; {String}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>입력된 화면 좌표와 겹치는 Image의 key를 찾는다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let imageKey = logiMap.hitImageKey(250, 250);
 //스크린 좌표 (250, 250)에 있는 image의 key가 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 X</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 Y</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>좌표와 겹치는 이미지 Key</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="hitImageKeys"><span class="type-signature"></span>hitImageKeys<span class="signature">(x, y)</span><span class="type-signature"> &rarr; {Array.&lt;String>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>입력된 화면 좌표와 겹치는 Image들의 key들을 찾는다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let imageKeys = logiMap.hitImageKeys(250, 250);
 //스크린 좌표 (250, 250)에 있는 image들의 key 배열이 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 X</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 Y</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>좌표와 겹치는 이미지 Keys</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;String></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="hitImages"><span class="type-signature"></span>hitImages<span class="signature">(x, y)</span><span class="type-signature"> &rarr; {Array.&lt;<a href="logi.maps.Image.html">logi.maps.Image</a>>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>입력된 화면 좌표와 겹치는 Image들을 찾는다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let images = logiMap.hitImages(250, 250);
 //스크린 좌표 (250, 250)에 있는 image 배열이 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 X</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 Y</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>좌표와 겹치는 이미지들</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array.&lt;<a href="logi.maps.Image.html">logi.maps.Image</a>></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="hitPolygon"><span class="type-signature"></span>hitPolygon<span class="signature">(x, y)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Polygon.html">logi.maps.Polygon</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>입력된 화면 좌표와 겹치는 Polygon을 찾는다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let polygon = logiMap.hitPolygon(250, 250);
 //스크린 좌표 (250, 250)에 있는 polygon이 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 X</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 Y</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>좌표와 겹치는 폴리곤</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Polygon.html">logi.maps.Polygon</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="hitPolygonKey"><span class="type-signature"></span>hitPolygonKey<span class="signature">(x, y)</span><span class="type-signature"> &rarr; {String}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>입력된 화면 좌표와 겹치는 Polygon의 key를 찾는다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let polygonKey = logiMap.hitPolygonKey(250, 250);
 //스크린 좌표 (250, 250)에 있는 polygon의 key가 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>x</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 X</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>y</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표 Y</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>좌표와 겹치는 폴리곤 Key</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="isExistCircle"><span class="type-signature"></span>isExistCircle<span class="signature">(objKey)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>key 값으로 등록된 Circle이 있는지 확인한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let exist = logiMap.isExistCircle(‘key-01’);
 //‘key-01’로 등록된 cirlce이 있으면 true 없으면 false</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>objKey</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>원 Key</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>등록 여부</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="isExistGps"><span class="type-signature"></span>isExistGps<span class="signature">(objKey)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>key 값으로 등록된 GPS가 있는지 확인한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let exist = logiMap.isExistGps(‘key-01’);
 //‘key-01’로 등록된 gps가 있으면 true 없으면 false</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>objKey</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>GPS Key</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>등록 여부</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="isExistImage"><span class="type-signature"></span>isExistImage<span class="signature">(objKey)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>key 값으로 등록된 Image가 있는지 확인한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let exist = logiMap.isExistImage(‘key-01’);
 //‘key-01’로 등록된 image가 있으면 true 없으면 false</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>objKey</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>이미지 Key</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>등록 여부</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="isExistLabel"><span class="type-signature"></span>isExistLabel<span class="signature">(objKey)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>key 값으로 등록된 Label이 있는지 확인한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let exist = logiMap.isExistLabel(‘key-01’);
 //‘key-01’로 등록된 label이 있으면 true 없으면 false</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>objKey</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>라벨 Key</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>등록 여부</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="isExistLine"><span class="type-signature"></span>isExistLine<span class="signature">(objKey)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>key 값으로 등록된 Line이 있는지 확인한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let exist = logiMap.isExistLine(‘key-01’);
 //‘key-01’로 등록된 line이 있으면 true 없으면 false</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>objKey</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>라인 Key</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>등록 여부</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="isExistPolygon"><span class="type-signature"></span>isExistPolygon<span class="signature">(objKey)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>key 값으로 등록된 Polygon이 있는지 확인한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let exist = logiMap.isExistPolygon(‘key-01’);
 //‘key-01’로 등록된 polygon이 있으면 true 없으면 false</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>objKey</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>폴리곤 Key</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>등록 여부</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="isExistRoute"><span class="type-signature"></span>isExistRoute<span class="signature">(objKey)</span><span class="type-signature"> &rarr; {Boolean}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>key 값으로 등록된 Route가 있는지 확인한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let exist = logiMap.isExistRoute(‘key-01’);
 //‘key-01’로 등록된 route가 있으면 true 없으면 false</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>objKey</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>라우트 Key</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>등록 여부</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="move"><span class="type-signature"></span>move<span class="signature">(latlng, level, behavior)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>지도를 이동 또는 축소/확대를 한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.move({lat: 37.5436, lng: 127.0447}, 16);
 //서울숲역으로 지도가 이동되고 축척 레벨은 16으로 설정된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>latlng</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



            
            </td>

            

            

            <td class="description last"><p>이동할 좌표</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>level</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>이동할 레벨</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>behavior</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>이동 방식 (default: undefined, ‘smooth’: 지도 변화(이동, 축척)를 애니메이션처럼 스텝을 나누어 적용)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeAll"><span class="type-signature"></span>removeAll<span class="signature">(excludedKeys)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 모든 Image, Label, Line, Polygon, Circle, Route, Gps, Custom을 Map에서 삭제한다.
excludedKeys에 값이 있다면 해당 key를 가진 Object는 삭제 대상에서 제외된다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeAll();
 //등록된 모든 Image, Label, Line, Polygon, Circle, Route, Gps, Custom을 Map에서 삭제된다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeAll([‘key-01’]);
 //key 값으로 등록된 Object만 제외되고 모두 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>excludedKeys</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>제외할 Keys (default: undefined)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeCircle"><span class="type-signature"></span>removeCircle<span class="signature">(keyword)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.</li>
<li>key 또는 class 값으로 등록된 Circle을 Map에서 삭제한다.</li>
<li>keyword 타입이 String이면 key로 처리된다.</li>
<li>key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeCircle(‘key-01’);</code></pre>

    <pre class="prettyprint"><code>logiMap.removeCircle({key: ‘key-01’});
 //key 값으로 등록된 Circle이 Map에서 삭제된다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeCircle({class: ‘cir’});
 //class 값으로 등록된 Circle들이 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>원 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>원 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeCircleAll"><span class="type-signature"></span>removeCircleAll<span class="signature">(excludedKeys)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 모든 Circe을 삭제한다.
excludedKeys에 값이 있다면 해당 key를 가진 Circle은 삭제 대상에서 제외된다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeCircleAll();
 //등록된 모든 Circle을 삭제한다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeCircleAll([‘key-01’]);
 //key 값으로 등록된 Circle만 제외되고 모두 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>excludedKeys</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>제외할 폴리곤 Keys (default: undefined)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeCustom"><span class="type-signature"></span>removeCustom<span class="signature">(keyword)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.</li>
<li>key 또는 class 값으로 등록된 커스텀 객체를 Map에서 삭제한다.</li>
<li>keyword 타입이 String이면 key로 처리된다.</li>
<li>key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeCustom(‘key-01’);</code></pre>

    <pre class="prettyprint"><code>logiMap.removeCustom({key: ‘key-01’});
 //key 값으로 등록된 커스텀 객체가 Map에서 삭제된다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeCustom({class: ‘pin’});
 //class 값으로 등록된 커스텀 객체들이 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>커스텀 객체 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>커스텀 객체 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeCustomAll"><span class="type-signature"></span>removeCustomAll<span class="signature">(excludedKeys)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 모든 커스텀 객체를 삭제한다.
excludedKeys에 값이 있다면 해당 key를 가진 커스텀 객체는 삭제 대상에서 제외된다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeCustomAll();
 //등록된 모든 커스텀 객체를 삭제한다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeCustomAll([‘key-01’]);
 //key 값으로 등록된 커스텀 객체만 제외되고 모두 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>excludedKeys</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>제외할 커스텀 객체 Keys (default: undefined)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeEventListener"><span class="type-signature"></span>removeEventListener<span class="signature">(eventName, func)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 이벤트 리스너를 지운다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.removeEventListener(logi.maps.EVENT.dblclick, function(event) { console.log("double click"); });
 //지도에서 더블클릭해도 console 창에 “double click”이 출력되지 않는다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>eventName</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#EVENT">EVENT</a></span>



            
            </td>

            

            

            <td class="description last"><p>이벤트명</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>콜백함수</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeGps"><span class="type-signature"></span>removeGps<span class="signature">(keyword)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.</li>
<li>key 또는 class 값으로 등록된 GPS를 Map에서 삭제한다.</li>
<li>keyword 타입이 String이면 key로 처리된다.</li>
<li>key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeGps(‘key-01’);</code></pre>

    <pre class="prettyprint"><code>logiMap.removeGps({key: ‘key-01’});
 //key 값으로 등록된 GPS가 Map에서 삭제된다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeGps({class: ‘gps’});
 //class 값으로 등록된 GPS들이 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>GPS key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>GPS class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeGpsAll"><span class="type-signature"></span>removeGpsAll<span class="signature">(excludedKeys)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 모든 GPS를 삭제한다.
excludedKeys에 값이 있다면 해당 key를 가진 GPS는 삭제 대상에서 제외된다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeGpsAll();
 //등록된 모든 GPS를 삭제한다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeGpsAll([‘key-01’]);
 //key 값으로 등록된 GPS만 제외되고 모두 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>excludedKeys</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>제외할 경로 Keys (default: undefined)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeImage"><span class="type-signature"></span>removeImage<span class="signature">(keyword)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.</li>
<li>key 또는 class 값으로 등록된 Image를 Map에서 삭제한다.</li>
<li>keyword 타입이 String이면 key로 처리된다.</li>
<li>key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeImage(‘key-01’);</code></pre>

    <pre class="prettyprint"><code>logiMap.removeImage({key: ‘key-01’});
 //key 값으로 등록된 Image가 Map에서 삭제된다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeImage({class: ‘pin’});
 //class 값으로 등록된 Image들이 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>이미지 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>이미지 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeImageAll"><span class="type-signature"></span>removeImageAll<span class="signature">(excludedKeys)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 모든 Image를 삭제한다.
excludedKeys에 값이 있다면 해당 key를 가진 Image는 삭제 대상에서 제외된다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeImageAll();
 //등록된 모든 Image를 삭제한다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeImageAll([‘key-01’]);
 //key 값으로 등록된 Image만 제외되고 모두 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>excludedKeys</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>제외할 이미지 Keys (default: undefined)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeLabel"><span class="type-signature"></span>removeLabel<span class="signature">(keyword)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.</li>
<li>key 또는 class 값으로 등록된 Label을 Map에서 삭제한다.</li>
<li>keyword 타입이 String이면 key로 처리된다.</li>
<li>key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeLabel(‘key-01’);</code></pre>

    <pre class="prettyprint"><code>logiMap.removeLabel({key: ‘key-01’});
 //key 값으로 등록된 Label이 Map에서 삭제된다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeLabel({class: ‘lbl’});
 //class 값으로 등록된 Label들이 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>라벨 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>라벨 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeLabelAll"><span class="type-signature"></span>removeLabelAll<span class="signature">(excludedKeys)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 모든 Label을 삭제한다.
excludedKeys에 값이 있다면 해당 key를 가진 Label은 삭제 대상에서 제외된다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeLabelAll();
 //등록된 모든 Label을 삭제한다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeLabelAll([‘key-01’]);
 //key 값으로 등록된 Label만 제외되고 모두 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>excludedKeys</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>제외할 라벨 Keys (default: undefined)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeLine"><span class="type-signature"></span>removeLine<span class="signature">(keyword)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.</li>
<li>key 또는 class 값으로 등록된 Line을 Map에서 삭제한다.</li>
<li>keyword 타입이 String이면 key로 처리된다.</li>
<li>key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeLine(‘key-01’);</code></pre>

    <pre class="prettyprint"><code>logiMap.removeLine({key: ‘key-01’});
 //key 값으로 등록된 Line이 Map에서 삭제된다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeLine({class: ‘ln’});
 //class 값으로 등록된 Line들이 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>라인 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>라인 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeLineAll"><span class="type-signature"></span>removeLineAll<span class="signature">(excludedKeys)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 모든 Line을 삭제한다.
excludedKeys에 값이 있다면 해당 key를 가진 Line은 삭제 대상에서 제외된다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeLineAll();
 //등록된 모든 Line을 삭제한다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeLineAll([‘key-01’]);
 //key 값으로 등록된 Line만 제외되고 모두 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>excludedKeys</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>제외할 라인 Keys (default: undefined)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeObject"><span class="type-signature"></span>removeObject<span class="signature">(obj)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 Image, Label, Line, Polygon, Circle, Route, Gps, Custom을 Map에서 삭제한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.removeObject(image);
 //등록된 image가 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>obj</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Object.html">logi.maps.Object</a></span>



            
            </td>

            

            

            <td class="description last"><p>제거할 Object</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removePolygon"><span class="type-signature"></span>removePolygon<span class="signature">(keyword)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.</li>
<li>key 또는 class 값으로 등록된 Polygon을 Map에서 삭제한다.</li>
<li>keyword 타입이 String이면 key로 처리된다.</li>
<li>key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removePolygon(‘key-01’);</code></pre>

    <pre class="prettyprint"><code>logiMap.removePolygon({key: ‘key-01’});
 //key 값으로 등록된 Polygon이 Map에서 삭제된다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removePolygon({class: ‘ply’});
 //class 값으로 등록된 Polygon들이 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>폴리곤 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>폴리곤 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removePolygonAll"><span class="type-signature"></span>removePolygonAll<span class="signature">(excludedKeys)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 모든 Polygon을 삭제한다.
excludedKeys에 값이 있다면 해당 key를 가진 Polygon은 삭제 대상에서 제외된다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removePolygonAll();
 //등록된 모든 Polygon을 삭제한다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removePolygonAll([‘key-01’]);
 //key 값으로 등록된 Polygon만 제외되고 모두 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>excludedKeys</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>제외할 폴리곤 Keys (default: undefined)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeRoute"><span class="type-signature"></span>removeRoute<span class="signature">(keyword)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.</li>
<li>key 또는 class 값으로 등록된 Route를 Map에서 삭제한다.</li>
<li>keyword 타입이 String이면 key로 처리된다.</li>
<li>key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeRoute(‘key-01’);</code></pre>

    <pre class="prettyprint"><code>logiMap.removeRoute({key: ‘key-01’});
 //key 값으로 등록된 Route가 Map에서 삭제된다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeRoute({class: ‘rt’});
 //class 값으로 등록된 Route들이 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>keyword</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>keyword</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>경로 key</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                

                
                </td>
            

            

            <td class="description last"><p>경로 class</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeRouteAll"><span class="type-signature"></span>removeRouteAll<span class="signature">(excludedKeys)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 모든 Route를 삭제한다.
excludedKeys에 값이 있다면 해당 key를 가진 Route는 삭제 대상에서 제외된다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.removeRouteAll();
 //등록된 모든 Route를 삭제한다.</code></pre>

    <pre class="prettyprint"><code>logiMap.removeRouteAll([‘key-01’]);
 //key 값으로 등록된 Route만 제외되고 모두 Map에서 삭제된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>excludedKeys</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>제외할 경로 Keys (default: undefined)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="screen2world"><span class="type-signature"></span>screen2world<span class="signature">(point, zoom)</span><span class="type-signature"> &rarr; {<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>스크린 좌표를 월드 좌표로 변환한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let world = logiMap.screen2world({x: 200, y: 150});
 //{lat: 37.544773, lng: 127.045735} 월드 좌표로 변환된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>point</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Point.html">logi.maps.Point</a></span>



            
            </td>

            

            

            <td class="description last"><p>스크린 좌표</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>zoom</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>지도 스케일 (default: -1)</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>월드 좌표</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="screenshot"><span class="type-signature type-signature-async">(async) </span>screenshot<span class="signature">(imageSize)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>화면을 스크린샷한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.screenshot().then(blob => { … } );
 //현재 화면을 스크린샷해서 png blob 전달한다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>imageSize</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>이미지사이즈 (default: 화면 사이즈)</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>width</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>이미지 넓이</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>height</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>이미지 높이</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setBounds"><span class="type-signature"></span>setBounds<span class="signature">(latlngBound, padding, behavior)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>바운더리 경계가 포함되게 지도화면을 구성한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>logiMap.setBounds({min: {lat: 37.50346, lng: 127.01868}, max: {lat: 37.52752, lng: 127.04079}});
 //입력된 사각형 영역으로 지도를 이동시킨다.</code></pre>

    <pre class="prettyprint"><code>logiMap.setBounds({min: {lat: 37.50346, lng: 127.01868}, max: {lat: 37.52752, lng: 127.04079}}, {left: 100, top: 100, right: 100, bottom: 100});
 //{100, 100, 100, 100} 패딩값이 적용된 사각형 영역으로 지도를 이동시킨다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>latlngBound</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.LatLngBound.html">logi.maps.LatLngBound</a></span>



            
            </td>

            

            

            <td class="description last"><p>바운더리 좌표</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>padding</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>옵셋값 (default: undefined)</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>left</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>왼쪽</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>top</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>위쪽</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>right</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>오른쪽</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bottom</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>아래쪽</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>behavior</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>이동 방식 (default: undefined, ‘smooth’: 지도 변화(이동, 축척)를 애니메이션처럼 스텝을 나누어 적용)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setBridgeEvent"><span class="type-signature"></span>setBridgeEvent<span class="signature">(eventName, activity)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>모바일 웹앱에서 Bridge로 전달 받을 이벤트를 등록한다.
[message format]</p>
<ul>
<li>‘message’: ‘onMapEvent’, ‘type’:{eventName}, ‘pointX’:{screenX}, ‘pointY’:{screenY}</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setBridgeEvent(logi.maps.BRIDGE_MAPEVENT.touch, true);
 //맵을 터치하면 Bridge를 통해 onMapEvent 함수 또는 메시지로 정보가 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>eventName</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#BRIDGE_MAPEVENT">BRIDGE_MAPEVENT</a></span>



            
            </td>

            

            

            <td class="description last"><p>이벤트명</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>activity</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>활성화 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setCenter"><span class="type-signature"></span>setCenter<span class="signature">(latlng)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>입력 받은 위치로 지도를 이동한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setCenter({lat: 37.5436, lng: 127.0447});
 //서울숲역으로 지도가 이동된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>latlng</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



            
            </td>

            

            

            <td class="description last"><p>이동할 좌표</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setCenterMark"><span class="type-signature"></span>setCenterMark<span class="signature">(length, thickness, color)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>화면 중앙에 마크를 그린다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setCenterMark(80, 1, ‘red’);
 //화면 중앙에 가로 세로 80 길이의 빨간색 십자 마크가 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>length</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>길이</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>thickness</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>두께</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>color</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>색상</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDistrictHoverRange"><span class="type-signature"></span>setDistrictHoverRange<span class="signature">(dataType, fromLevel, toLevel)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>행정 구역(우편번호, 시도, 시군구, 읍면동, ...) Hover 활성화 범위를 지정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDistrictHoverRange(logi.maps.DISTRICT_DATATYPE.SGG, 10, 18);
 //시군구 Hover 영역 표시는 레벨 10~18에서 활성화 된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dataType</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#DISTRICT_DATATYPE">DISTRICT_DATATYPE</a></span>



            
            </td>

            

            

            <td class="description last"><p>행정 구역 타입</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fromLevel</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>시작 레벨</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>toLevel</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>끝 레벨</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDistrictHoverStyle"><span class="type-signature"></span>setDistrictHoverStyle<span class="signature">(dataType, polygon, text, textBox)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>행정 구역(우편번호, 시도, 시군구, 읍면동, ...) Hover 스타일을 지정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDistrictHoverStyle(logi.maps.DISTRICT_DATATYPE.SGG, {fillColor: "#FF0000", lineWidth: 1, lineColor: "#FF00FF"}, {fontColor: "#FFFF00", fontSize: 14});
 //시군구 Hover 영역 색상은 빨간색, 외곽 실선은 자홍색, 글자 크기는 14 사이즈로 작성된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dataType</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#DISTRICT_DATATYPE">DISTRICT_DATATYPE</a></span>



            
            </td>

            

            

            <td class="description last"><p>행정 구역 타입</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>polygon</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">null</span>



            
            </td>

            

            

            <td class="description last"><p>폴리곤 스타일</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fillColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>영역 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineWidth</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>외곽 라인 두께</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>외곽 라인 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dashLength</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>점선 길이,</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dashSpace</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>점선 간격,</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>text</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">null</span>



            
            </td>

            

            

            <td class="description last"><p>글자 스타일</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fontFamily</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>폰트 명</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fontBold</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>글자 볼드</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fontColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fontSize</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>글자 크기</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>textBox</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">null</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 스타일 (default: null)</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fillColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineWidth</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 외곽 라인 두께</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 외곽 라인 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>radius</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 라운드</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDistrictRange"><span class="type-signature"></span>setDistrictRange<span class="signature">(dataType, fromLevel, toLevel)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>행정 구역(우편번호, 시도, 시군구, 읍면동, ...) 표시 범위를 지정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDistrictRange(logi.maps.DISTRICT_DATATYPE.SGG, 10, 18);
 //시군구 영역 표시는 레벨 10~18에서 활성화 된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dataType</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#DISTRICT_DATATYPE">DISTRICT_DATATYPE</a></span>



            
            </td>

            

            

            <td class="description last"><p>행정 구역 타입</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fromLevel</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>시작 레벨</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>toLevel</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>끝 레벨</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDistrictStyle"><span class="type-signature"></span>setDistrictStyle<span class="signature">(dataType, polygon, text, textBox)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>행정 구역(우편번호, 시도, 시군구, 읍면동, ...) 스타일을 지정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDistrictStyle(logi.maps.DISTRICT_DATATYPE.SGG, {fillColor: "#FF0000", lineWidth: 1, lineColor: "#FF00FF"}, {fontColor: "#FFFF00", fontSize:18});
 //시군구 영역 색상은 빨간색, 외곽 실선은 자홍색, 글자 크기는 18 사이즈로 작성된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>dataType</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#DISTRICT_DATATYPE">DISTRICT_DATATYPE</a></span>



            
            </td>

            

            

            <td class="description last"><p>행정 구역 타입</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>polygon</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">null</span>



            
            </td>

            

            

            <td class="description last"><p>폴리곤 스타일</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fillColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>영역 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineWidth</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>외곽 라인 두께</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>외곽 라인 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dashLength</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>점선 길이,</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dashSpace</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>점선 간격,</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>text</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">null</span>



            
            </td>

            

            

            <td class="description last"><p>글자 스타일</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fontFamily</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>폰트 명</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fontBold</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>글자 볼드</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fontColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fontSize</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;Number></span>
|

<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>글자 크기 (fromLevel부터 fontSize가 순착적으로 적용)</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>textBox</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">null</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 스타일 (default: null)</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fillColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineWidth</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 외곽 라인 두께</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 외곽 라인 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>radius</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 라운드</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDistrictVisible"><span class="type-signature"></span>setDistrictVisible<span class="signature">(visibleType)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>행정 구역(우편번호, 시도, 시군구, 읍면동, ...) 표시 유형을 선택한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDistrictVisible(logi.maps.DISTRICT_VISIBLETYPE.SGG_ON);
 //시군구 영역을 표시한다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>visibleType</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#DISTRICT_VISIBLETYPE">DISTRICT_VISIBLETYPE</a></span>



            
            </td>

            

            

            <td class="description last"><p>행정 구역 표시 유형</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDragAreaMode"><span class="type-signature"></span>setDragAreaMode<span class="signature">(mode)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>마우스를 드래그하면 화면에 사각 영역을 표시한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDragAreaMode(true);
 //마우스를 드래그하면 화면에 사각 영역이 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>mode</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>사각 영역 그리기 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDragAreaStyle"><span class="type-signature"></span>setDragAreaStyle<span class="signature">(style)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>드래그 사각 영역의 스타일을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDragAreaStyle(style);
 //설정된 스타일로 드래그 사각 영역이 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>style</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>style</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>width</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>라인 넓이</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dashLength</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>점선 길이</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dashSpace</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>간격 길이</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>color</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>라인 색</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDrawingCircleOnMove"><span class="type-signature"></span>setDrawingCircleOnMove<span class="signature">(drawing)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>지도 이동할 때 원 그리기 여부를 결정한다.
logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDrawingCircleOnMove(false);
 //지도 이동할 때는 원을 그리지 않는다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>drawing</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>그리기 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDrawingGpsOnMove"><span class="type-signature"></span>setDrawingGpsOnMove<span class="signature">(drawing)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>지도 이동할 때 경로 그리기 여부를 결정한다.
logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDrawingGpsOnMove(false);
 //지도 이동할 때는 경로를 그리지 않는다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>drawing</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>그리기 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDrawingImageOnMove"><span class="type-signature"></span>setDrawingImageOnMove<span class="signature">(drawing)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>지도 이동할 때 이미지 그리기 여부를 결정한다.
logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDrawingImageOnMove(false);
 //지도 이동할 때는 이미지를 그리지 않는다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>drawing</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>그리기 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDrawingLabelOnMove"><span class="type-signature"></span>setDrawingLabelOnMove<span class="signature">(drawing)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>지도 이동할 때 라벨 그리기 여부를 결정한다.
logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDrawingLabelOnMove(false);
 //지도 이동할 때는 라벨을 그리지 않는다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>drawing</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>그리기 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDrawingLineOnMove"><span class="type-signature"></span>setDrawingLineOnMove<span class="signature">(drawing)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>지도 이동할 때 라인 그리기 여부를 결정한다.
logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDrawingLineOnMove(false);
 //지도 이동할 때는 라인을 그리지 않는다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>drawing</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>그리기 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDrawingPolygonOnMove"><span class="type-signature"></span>setDrawingPolygonOnMove<span class="signature">(drawing)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>지도 이동할 때 폴리곤 그리기 여부를 결정한다.
logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDrawingPolygonOnMove(false);
 //지도 이동할 때는 폴리곤을 그리지 않는다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>drawing</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>그리기 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setDrawingRouteOnMove"><span class="type-signature"></span>setDrawingRouteOnMove<span class="signature">(drawing)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>지도 이동할 때 경로 그리기 여부를 결정한다.
logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setDrawingRouteOnMove(false);
 //지도 이동할 때는 경로를 그리지 않는다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>drawing</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>그리기 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setFreezeModeOnMoving"><span class="type-signature"></span>setFreezeModeOnMoving<span class="signature">(modeOn)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>패닝 할 때 맵 타일을 제외한 UI 프리징 여/부 설정</li>
<li>기본 모드는 freeze mode on</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setFreezeModeOnMoving(true);
 //패닝 시작할 때 맵 타일을 제외한 UI를 캡쳐한다. 패닝에 따라서 캡쳐된 이미지를 이동 시킨다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>modeOn</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>모드 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setHeatmap"><span class="type-signature"></span>setHeatmap<span class="signature">(data)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>히트맵 데이터를 렌더링합니다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setHeatmap([
 {latlng: {lat: 37.5115557, lng: 127.0595261}, intensity: 0.5},
 {latlng: {lat: 37.5062379, lng: 127.0050378}, intensity: 1.0},
 {latlng: {lat: 37.566596, lng: 127.007702}, intensity: 0.4}
 ]);
 //히트맵 데이터가 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>data</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;{latlng: {lat: Number, lng: Number}, intensity: Number}></span>



            
            </td>

            

            

            <td class="description last"><p>좌표 및 강도 정보 배열</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setLevel"><span class="type-signature"></span>setLevel<span class="signature">(level)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>현재 지도 타일 Level을 변경한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    
        <dt class="important tag-deprecated">Deprecated:</dt><dd><ul class="dummy"><li>This method is deprecated. Use setZoom(zoom) instead.</li></ul></dd>
    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setLevel(16);
 //지도 타일 레벨이 16 으로 변경된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>level</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>지도 타일 레벨</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setLevelRange"><span class="type-signature"></span>setLevelRange<span class="signature">(min, max)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>지도 스케일 범위를 변경 할 수 있다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setLevelRange(8, 18);
 //지도 스케일 범위가 8 ~ 18로 설정된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>min</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>월드 레벨</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>max</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>디테일 레벨</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setMotionEventLock"><span class="type-signature"></span>setMotionEventLock<span class="signature">(eventLock)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>마우스(터치) 이벤트를 활성화 또는 비활성화한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setMotionEventLock(true);
 //마우스(터치) 이벤트가 비활성화된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>eventLock</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>활성화 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setOrderType"><span class="type-signature"></span>setOrderType<span class="signature">(orderType)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>OrderType은 두가지 타입이 있다.</p>
<ul>
<li>‘object’(default): route, gps, line, polygon, circle, image, label 순으로 그려진다. 같은 object들은 zindex 값에 의해서 순서가 결정된다.</li>
<li>‘zindex’: zindex 값으로만 그리는 순서가 결정된다. zindex 값이 같으면 route, gps, line, polygon, circle, image, label 순으로 그려진다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setOrderType(‘zindex’);
 //그리는 순서가 zindex 타입으로 변경된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>orderType</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>우선순위타입</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setOverlapCheck"><span class="type-signature"></span>setOverlapCheck<span class="signature">(check)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>그룹 아이디가 같은 image와 label은 영역이 겹쳐지면 하나로 표시된다. setOverlapCheck를 이용하면 전체를 끄거나 켤 수 있다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setOverlapCheck(false);
 //영역 체크를 모두 해제한다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>check</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>바운더리체크 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setOverlapInfoVisibility"><span class="type-signature"></span>setOverlapInfoVisibility<span class="signature">(visibility)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>image 또는 label의 겹쳐진 수를 표시 할 지 여부를 지정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setOverlapInfoVisibility(true);
 //겹쳐진 수를 표시한다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>visibility</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>겹친정보표시 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setSearchDistrict"><span class="type-signature type-signature-async">(async) </span>setSearchDistrict<span class="signature">(code, padding, behavior)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>행정 구역(시도, 시군구, 읍면동, ...) 코드에 맞는 폴리곤을 검색해서 화면에 그린다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setSearchDistrict('**********');
 //서울특별시 영역 폴리곤이 그려지고 해당 폴리곤으로 이동한다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>code</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>행정 구역 코드 (10자리 PNU 코드: 시도 2자리, 시군구 3자리, 읍면동 3자리, 리 2자리)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>padding</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>옵셋값 (default: undefined)</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>left</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>왼쪽</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>top</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>위쪽</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>right</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>오른쪽</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bottom</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>아래쪽</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>behavior</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>이동 방식 (default: undefined, ‘smooth’: 지도 변화(이동, 축척)를 애니메이션처럼 스텝을 나누어 적용)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setSearchDistrictStyle"><span class="type-signature"></span>setSearchDistrictStyle<span class="signature">(polygon, text, textBox)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>검색된 행정 구역(시도, 시군구, 읍면동, ...) 스타일을 지정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setSearchDistrictStyle({fillColor: "#FF0000", lineWidth: 1, lineColor: "#FF00FF"}, {fontColor: "#FFFF00", fontSize: 14});
 //영역 색상은 빨간색, 외곽 실선은 자홍색, 글자 크기는 14 사이즈로 작성된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>polygon</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">null</span>



            
            </td>

            

            

            <td class="description last"><p>폴리곤 스타일</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fillColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>영역 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineWidth</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>외곽 라인 두께</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>외곽 라인 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dashLength</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>점선 길이,</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>dashSpace</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>점선 간격,</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>text</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">null</span>



            
            </td>

            

            

            <td class="description last"><p>글자 스타일</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fontFamily</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>폰트 명</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fontBold</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>글자 볼드</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fontColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fontSize</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>글자 크기</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>textBox</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>
|

<span class="param-type">null</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 스타일 (default: null)</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fillColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineWidth</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 외곽 라인 두께</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 외곽 라인 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>radius</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>글자 박스 라운드</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setTheme"><span class="type-signature type-signature-async">(async) </span>setTheme<span class="signature">(name)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>SVG(XVG) 맵 타일을 사용할 경우 맵 테마를 사용 할 수 있다.</li>
<li>resource/region/theme 이름 순으로 폴더가 구성 된다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setTheme(“aloa”);
 //‘aloa’ 폴더 아래에 작성된 layout으로 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>name</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>테마명</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setZoom"><span class="type-signature"></span>setZoom<span class="signature">(zoom)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>현재 지도 스케일이 변경한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.setZoom(16);
 //지도 스케일이 16 으로 변경된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>zoom</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>지도 스케일</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="showLayer"><span class="type-signature"></span>showLayer<span class="signature">(레이어명, options)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><ul>
<li>SVG(XVG) 맵 타일을 사용할 경우 맵 레이어 On/Off를 사용 할 수 있다.</li>
</ul></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.showLayer('rwy');
 //철도 라인이 그려진다.
 logiMap.showLayer('poi', {codeType: 'category', codeList: ['11']});
 //POI 심볼 중에서 카테고리 11(건물, 일반 기업)이 그려진다.
 logiMap.showLayer('poitext', {codeType: 'poi', codeList: ['92', '95']});
 //POI 글자 중에서 POI 코드 92XXXXXXX(판매업), 95XXXXXXX(생산업) 관련이 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>레이어명</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>옵션</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>codeType</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>코드 타입 ('category' | 'poi')</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>codeList</code></td>
            

            <td class="type">
            
                
<span class="param-type">Array.&lt;String></span>



            
            </td>

            

            

            <td class="description last"><p>코드 리스트 (카테고리 ID 또는 POI 코드의 배열)</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="updateMap"><span class="type-signature"></span>updateMap<span class="signature">()</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>지도를 강제로 다시 그린다.
지도 갱신이 필요한 시점에 자동으로 호출 됨</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.updateMap();
 //화면을 강제로 갱신한다.</code></pre>




















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="world2screen"><span class="type-signature"></span>world2screen<span class="signature">(latlng, zoom)</span><span class="type-signature"> &rarr; {<a href="logi.maps.Point.html">logi.maps.Point</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>월드 좌표를 스크린 좌표로 변환한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let screen = logiMap.world2screen({lat: 37.544773, lng: 127.045735});
 //{x: 200, y: 150} 스크린 좌표로 변환된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>latlng</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



            
            </td>

            

            

            <td class="description last"><p>월드 좌표</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>zoom</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>지도 스케일 (default: -1)</p></td>
        </tr>

    
    </tbody>
</table>
















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>스크린 좌표</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Point.html">logi.maps.Point</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="zoomIn"><span class="type-signature"></span>zoomIn<span class="signature">()</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>현재 지도의 Level을 한 단계 확대한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.zoomIn();
 //지도를 한 단계 확대한다.</code></pre>




















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="zoomOut"><span class="type-signature"></span>zoomOut<span class="signature">()</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>현재 지도의 Level을 한 단계 축소한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>logiMap.zoomOut();
 //지도를 한 단계 축소한다.</code></pre>




















<h4>&nbsp;</h4>

        
    

    

    
</article>

</section>




    
    
</div>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 4.0.4</a> using the <a href="https://github.com/clenemt/docdash">docdash</a> theme.
</footer>

<script>prettyPrint();</script>
<script src="scripts/polyfill.js"></script>
<script src="scripts/linenumber.js"></script>

<script src="scripts/search.js" defer></script>


<script src="scripts/collapse.js" defer></script>


</body>
</html>