* {
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  width: 100%;
}

body {
  color: #4d4e53;
  background-color: white;
  margin: 0 auto;
  padding: 0 20px;
  font-family: 'Helvetica Neue', Helvetica, sans-serif;
  font-size: 16px;
}

img {
  max-width: 100%;
}

a,
a:active {
  color: #606;
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

article a {
  border-bottom: 1px solid #ddd;
}

article a:hover,
article a:active {
  border-bottom-color: #222;
}

article .description a {
  word-break: break-word;
}

p,
ul,
ol,
blockquote {
  margin-bottom: 1em;
  line-height: 160%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Montserrat', sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #000;
  font-weight: 400;
  margin: 0;
}

h1 {
  font-weight: 300;
  font-size: 48px;
  margin: 1em 0 0.5em;
}

h1.page-title {
  font-size: 48px;
  margin: 1em 30px;
  line-height: 100%;
  word-wrap: break-word;
}

h2 {
  font-size: 24px;
  margin: 1.5em 0 0.3em;
}

h3 {
  font-size: 24px;
  margin: 1.2em 0 0.3em;
}

h4 {
  font-size: 18px;
  margin: 1em 0 0.2em;
  color: #4d4e53;
}

h4.name {
  color: #fff;
  background: #6d426d;
  box-shadow: 0 0.25em 0.5em #d3d3d3;
  border-top: 1px solid #d3d3d3;
  border-bottom: 1px solid #d3d3d3;
  margin: 1.5em 0 0.5em;
  padding: 0.75em 0 0.75em 10px;
}

h4.name a {
  color: #fc83ff;
}

h4.name a:hover {
  border-bottom-color: #fc83ff;
}

h5,
.container-overview .subsection-title {
  font-size: 120%;
  letter-spacing: -0.01em;
  margin: 8px 0 3px 0;
}

h6 {
  font-size: 100%;
  letter-spacing: -0.01em;
  margin: 6px 0 3px 0;
  font-style: italic;
}

.usertext h1 {
  font-family: 'Source Sans Pro';
  font-size: 24px;
  margin: 2.5em 0 1em;
  font-weight: 400;
}

.usertext h2 {
  font-family: 'Source Sans Pro';
  font-size: 18px;
  margin: 2em 0 0.5em;
  font-weight: 400;
}

.usertext h3 {
  font-family: 'Source Sans Pro';
  font-size: 15px;
  margin: 1.5em 0 0;
  font-weight: 400;
}

.usertext h4 {
  font-family: 'Source Sans Pro';
  font-size: 14px;
  margin: 0 0 0;
  font-weight: 400;
}

.usertext h5 {
  font-size: 12px;
  margin: 1em 0 0;
  font-weight: normal;
  color: #666;
}

.usertext h6 {
  font-size: 11px;
  margin: 1em 0 0;
  font-weight: normal;
  font-style: normal;
  color: #666;
}

tt,
code,
kbd,
samp,
pre {
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  background: #f4f4f4;
}

tt,
code,
kbd,
samp {
  padding: 1px 5px;
}

pre {
  padding-bottom: 1em;
}

.class-description {
  font-size: 130%;
  line-height: 140%;
  margin-bottom: 1em;
  margin-top: 1em;
}

.class-description:empty {
  margin: 0;
}

#main {
  float: right;
  width: calc(100% - 240px);
}

header {
  display: block;
}

section {
  display: block;
  background-color: #fff;
  padding: 0 0 0 30px;
}

.variation {
  display: none;
}

.signature-attributes {
  font-size: 60%;
  color: #eee;
  font-style: italic;
  font-weight: lighter;
}

nav {
  float: left;
  display: block;
  width: 250px;
  background: #fff;
  overflow: auto;
  position: fixed;
  height: 100%;
}

nav #nav-search {
  width: 210px;
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
  margin-right: 20px;
  margin-top: 20px;
}

nav.wrap a {
  word-wrap: break-word;
}

nav h3 {
  margin-top: 12px;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 700;
  line-height: 24px;
  margin: 15px 0 10px;
  padding: 0;
  color: #000;
}

nav h3.collapsed_header {
  cursor: pointer;
}

nav ul {
  font-family: 'Lucida Grande', 'Lucida Sans Unicode', arial, sans-serif;
  font-size: 100%;
  line-height: 17px;
  padding: 0;
  margin: 0;
  list-style-type: none;
}

nav ul a,
nav ul a:active {
  font-family: 'Montserrat', sans-serif;
  line-height: 18px;
  padding: 0;
  display: block;
  font-size: 12px;
}

nav a:hover,
nav a:active {
  color: #606;
}

nav > ul {
  padding: 0 10px;
}

nav > ul > li > a {
  color: #606;
  margin-top: 10px;
}

nav ul ul a {
  color: hsl(207, 1%, 60%);
  border-left: 1px solid hsl(207, 10%, 86%);
}

nav ul ul a,
nav ul ul a:active {
  padding-left: 20px;
}

nav h2 {
  font-size: 13px;
  margin: 10px 0 0 0;
  padding: 0;
}

nav > h2 > a {
  margin: 10px 0 -10px;
  color: #606 !important;
}

footer {
  color: hsl(0, 0%, 28%);
  margin-left: 250px;
  display: block;
  padding: 15px;
  font-style: italic;
  font-size: 90%;
}

.ancestors {
  color: #999;
}

.ancestors a {
  color: #999 !important;
}

.clear {
  clear: both;
}

.important {
  font-weight: bold;
  color: #950b02;
}

.yes-def {
  text-indent: -1000px;
}

.type-signature {
  color: #ca79ca;
}

.type-signature:last-child {
  color: #eee;
}

.name,
.signature {
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
}

.signature {
  color: #fc83ff;
}

.details {
  margin-top: 6px;
  border-left: 2px solid #ddd;
  line-height: 20px;
  font-size: 14px;
}

.details dt {
  width: auto;
  float: left;
  padding-left: 10px;
}

.details dd {
  margin-left: 70px;
  margin-top: 6px;
  margin-bottom: 6px;
}

.details ul {
  margin: 0;
}

.details ul {
  list-style-type: none;
}

.details pre.prettyprint {
  margin: 0;
}

.details .object-value {
  padding-top: 0;
}

.description {
  margin-bottom: 1em;
  margin-top: 1em;
}

.code-caption {
  font-style: italic;
  font-size: 107%;
  margin: 0;
}

.prettyprint {
  font-size: 14px;
  overflow: auto;
}

.prettyprint.source {
  width: inherit;
  line-height: 18px;
  display: block;
  background-color: #0d152a;
  color: #aeaeae;
}

.prettyprint code {
  line-height: 18px;
  display: block;
  background-color: #0d152a;
  color: #4d4e53;
}

.prettyprint > code {
  padding: 15px;
}

.prettyprint .linenums code {
  padding: 0 15px;
}

.prettyprint .linenums li:first-of-type code {
  padding-top: 15px;
}

.prettyprint code span.line {
  display: inline-block;
}

.prettyprint.linenums {
  padding-left: 70px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.prettyprint.linenums ol {
  padding-left: 0;
}

.prettyprint.linenums li {
  border-left: 3px #34446b solid;
}

.prettyprint.linenums li.selected,
.prettyprint.linenums li.selected * {
  background-color: #34446b;
}

.prettyprint.linenums li * {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.prettyprint.linenums li code:empty:after {
  content: '';
  display: inline-block;
  width: 0px;
}

table {
  border-spacing: 0;
  border: 1px solid #ddd;
  border-collapse: collapse;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 100%;
  font-size: 14px;
  margin: 1em 0;
}

td,
th {
  margin: 0px;
  text-align: left;
  vertical-align: top;
  padding: 10px;
  display: table-cell;
}

thead tr,
thead tr {
  background-color: #fff;
  font-weight: bold;
  border-bottom: 1px solid #ddd;
}

.params .type {
  white-space: nowrap;
}

.params code {
  white-space: pre;
}

.params td,
.params .name,
.props .name,
.name code {
  color: #4d4e53;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
  font-size: 100%;
}

.params td {
  border-top: 1px solid #eee;
}

.params td.description > p:first-child,
.props td.description > p:first-child {
  margin-top: 0;
  padding-top: 0;
}

.params td.description > p:last-child,
.props td.description > p:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
}

span.param-type,
.params td .param-type,
.param-type dd {
  color: #606;
  font-family: Consolas, Monaco, 'Andale Mono', monospace;
}

.param-type dt,
.param-type dd {
  display: inline-block;
}

.param-type {
  margin: 14px 0;
}

.disabled {
  color: #454545;
}

/* navicon button */
.navicon-button {
  display: none;
  position: relative;
  padding: 2.0625rem 1.5rem;
  transition: 0.25s;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  opacity: 0.8;
}
.navicon-button .navicon:before,
.navicon-button .navicon:after {
  transition: 0.25s;
}
.navicon-button:hover {
  transition: 0.5s;
  opacity: 1;
}
.navicon-button:hover .navicon:before,
.navicon-button:hover .navicon:after {
  transition: 0.25s;
}
.navicon-button:hover .navicon:before {
  top: 0.825rem;
}
.navicon-button:hover .navicon:after {
  top: -0.825rem;
}

/* navicon */
.navicon {
  position: relative;
  width: 2.5em;
  height: 0.3125rem;
  background: #000;
  transition: 0.3s;
  border-radius: 2.5rem;
}
.navicon:before,
.navicon:after {
  display: block;
  content: '';
  height: 0.3125rem;
  width: 2.5rem;
  background: #000;
  position: absolute;
  z-index: -1;
  transition: 0.3s 0.25s;
  border-radius: 1rem;
}
.navicon:before {
  top: 0.625rem;
}
.navicon:after {
  top: -0.625rem;
}

/* open */
.nav-trigger:checked + label:not(.steps) .navicon:before,
.nav-trigger:checked + label:not(.steps) .navicon:after {
  top: 0 !important;
}

.nav-trigger:checked + label .navicon:before,
.nav-trigger:checked + label .navicon:after {
  transition: 0.5s;
}

/* Minus */
.nav-trigger:checked + label {
  -webkit-transform: scale(0.75);
  transform: scale(0.75);
}

/* × and + */
.nav-trigger:checked + label.plus .navicon,
.nav-trigger:checked + label.x .navicon {
  background: transparent;
}

.nav-trigger:checked + label.plus .navicon:before,
.nav-trigger:checked + label.x .navicon:before {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  background: #fff;
}

.nav-trigger:checked + label.plus .navicon:after,
.nav-trigger:checked + label.x .navicon:after {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  background: #fff;
}

.nav-trigger:checked + label.plus {
  -webkit-transform: scale(0.75) rotate(45deg);
  transform: scale(0.75) rotate(45deg);
}

.nav-trigger:checked ~ nav {
  left: 0 !important;
}

.nav-trigger:checked ~ .overlay {
  display: block;
}

.nav-trigger {
  position: fixed;
  top: 0;
  clip: rect(0, 0, 0, 0);
}

.overlay {
  display: none;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: hsla(0, 0%, 0%, 0.5);
  z-index: 1;
}

/* nav level */
.level-hide {
  display: none;
}
html[data-search-mode] .level-hide {
  display: block;
}

@media only screen and (max-width: 680px) {
  body {
    overflow-x: hidden;
  }

  nav {
    background: #fff;
    width: 250px;
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: -250px;
    z-index: 3;
    padding: 0 10px;
    transition: left 0.2s;
  }

  .navicon-button {
    display: inline-block;
    position: fixed;
    top: 1.5em;
    right: 0;
    z-index: 2;
  }

  #main {
    width: 100%;
  }

  #main h1.page-title {
    margin: 1em 0;
  }

  #main section {
    padding: 0;
  }

  footer {
    margin-left: 0;
  }
}

/** Add a '#' to static members */
[data-type='member'] a::before {
  content: '#';
  display: inline-block;
  margin-left: -14px;
  margin-right: 5px;
}

#disqus_thread {
  margin-left: 30px;
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 400;
  src: url('../fonts/Montserrat/Montserrat-Regular.eot'); /* IE9 Compat Modes */
  src:
    url('../fonts/Montserrat/Montserrat-Regular.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */ url('../fonts/Montserrat/Montserrat-Regular.woff2')
      format('woff2'),
    /* Super Modern Browsers */
      url('../fonts/Montserrat/Montserrat-Regular.woff') format('woff'),
    /* Pretty Modern Browsers */
      url('../fonts/Montserrat/Montserrat-Regular.ttf') format('truetype'); /* Safari, Android, iOS */
}

@font-face {
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 700;
  src: url('../fonts/Montserrat/Montserrat-Bold.eot'); /* IE9 Compat Modes */
  src:
    url('../fonts/Montserrat/Montserrat-Bold.eot?#iefix')
      format('embedded-opentype'),
    /* IE6-IE8 */ url('../fonts/Montserrat/Montserrat-Bold.woff2')
      format('woff2'),
    /* Super Modern Browsers */ url('../fonts/Montserrat/Montserrat-Bold.woff')
      format('woff'),
    /* Pretty Modern Browsers */ url('../fonts/Montserrat/Montserrat-Bold.ttf')
      format('truetype'); /* Safari, Android, iOS */
}

@font-face {
  font-family: 'Source Sans Pro';
  src: url('../fonts/Source-Sans-Pro/sourcesanspro-regular-webfont.eot');
  src:
    url('../fonts/Source-Sans-Pro/sourcesanspro-regular-webfont.eot?#iefix')
      format('embedded-opentype'),
    url('../fonts/Source-Sans-Pro/sourcesanspro-regular-webfont.woff2')
      format('woff2'),
    url('../fonts/Source-Sans-Pro/sourcesanspro-regular-webfont.woff')
      format('woff'),
    url('../fonts/Source-Sans-Pro/sourcesanspro-regular-webfont.ttf')
      format('truetype'),
    url('../fonts/Source-Sans-Pro/sourcesanspro-regular-webfont.svg#source_sans_proregular')
      format('svg');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Source Sans Pro';
  src: url('../fonts/Source-Sans-Pro/sourcesanspro-light-webfont.eot');
  src:
    url('../fonts/Source-Sans-Pro/sourcesanspro-light-webfont.eot?#iefix')
      format('embedded-opentype'),
    url('../fonts/Source-Sans-Pro/sourcesanspro-light-webfont.woff2')
      format('woff2'),
    url('../fonts/Source-Sans-Pro/sourcesanspro-light-webfont.woff')
      format('woff'),
    url('../fonts/Source-Sans-Pro/sourcesanspro-light-webfont.ttf')
      format('truetype'),
    url('../fonts/Source-Sans-Pro/sourcesanspro-light-webfont.svg#source_sans_prolight')
      format('svg');
  font-weight: 300;
  font-style: normal;
}
