<!DOCTYPE html>
<html lang="en">
<head>
    
    <meta charset="utf-8">
    <title>TextInfo - Documentation</title>
    
    
    <script src="scripts/prettify/prettify.js"></script>
    <script src="scripts/prettify/lang-css.js"></script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>

<input type="checkbox" id="nav-trigger" class="nav-trigger" />
<label for="nav-trigger" class="navicon-button x">
  <div class="navicon"></div>
</label>

<label for="nav-trigger" class="overlay"></label>

<nav >
    
    <input type="text" id="nav-search" placeholder="Search" />
    
    
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="logi.maps.Map.html">logi.maps.Map</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addCircle">addCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addCustom">addCustom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addFont">addFont</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addGps">addGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addImage">addImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addLabel">addLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addLine">addLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addObject">addObject</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addPolygon">addPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addRoute">addRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#disableWheelEvent">disableWheelEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#enableWheelEvent">enableWheelEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findCircle">findCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findGps">findGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findImage">findImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findLabel">findLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findLine">findLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findPolygon">findPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findRoute">findRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getBounds">getBounds</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getCenter">getCenter</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getConvaxHull">getConvaxHull</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getDistrictHoverStyle">getDistrictHoverStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getDistrictStyle">getDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getDragAreaRect">getDragAreaRect</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getHoveredDistrict">getHoveredDistrict</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getLevel">getLevel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getNearestInfoOnPolyline">getNearestInfoOnPolyline</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getRealDistance">getRealDistance</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getSearchDistrictStyle">getSearchDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getSplitInfoOnPolyline">getSplitInfoOnPolyline</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getTheme">getTheme</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getZoom">getZoom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hideLayer">hideLayer</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitCircle">hitCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitCircleKey">hitCircleKey</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImage">hitImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImageKey">hitImageKey</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImageKeys">hitImageKeys</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImages">hitImages</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitPolygon">hitPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitPolygonKey">hitPolygonKey</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistCircle">isExistCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistGps">isExistGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistImage">isExistImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistLabel">isExistLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistLine">isExistLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistPolygon">isExistPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistRoute">isExistRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#move">move</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeAll">removeAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCircle">removeCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCircleAll">removeCircleAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCustom">removeCustom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCustomAll">removeCustomAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeGps">removeGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeGpsAll">removeGpsAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeImage">removeImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeImageAll">removeImageAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLabel">removeLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLabelAll">removeLabelAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLine">removeLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLineAll">removeLineAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeObject">removeObject</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removePolygon">removePolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removePolygonAll">removePolygonAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeRoute">removeRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeRouteAll">removeRouteAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#screen2world">screen2world</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#screenshot">screenshot</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setBounds">setBounds</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setCenter">setCenter</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setCenterMark">setCenterMark</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictHoverRange">setDistrictHoverRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictHoverStyle">setDistrictHoverStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictRange">setDistrictRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictStyle">setDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictVisible">setDistrictVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDragAreaMode">setDragAreaMode</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDragAreaStyle">setDragAreaStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingCircleOnMove">setDrawingCircleOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingGpsOnMove">setDrawingGpsOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingImageOnMove">setDrawingImageOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingLabelOnMove">setDrawingLabelOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingLineOnMove">setDrawingLineOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingPolygonOnMove">setDrawingPolygonOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingRouteOnMove">setDrawingRouteOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setFreezeModeOnMoving">setFreezeModeOnMoving</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setHeatmap">setHeatmap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setLevel">setLevel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setLevelRange">setLevelRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setMotionEventLock">setMotionEventLock</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setOrderType">setOrderType</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setOverlapCheck">setOverlapCheck</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setOverlapInfoVisibility">setOverlapInfoVisibility</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setSearchDistrict">setSearchDistrict</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setSearchDistrictStyle">setSearchDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setTheme">setTheme</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setZoom">setZoom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#showLayer">showLayer</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#updateMap">updateMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#world2screen">world2screen</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#zoomIn">zoomIn</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#zoomOut">zoomOut</a></li></ul></li><li><a href="logi.maps.Object.html">logi.maps.Object</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Image.html">logi.maps.Image</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#addTextInfo">addTextInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#changeImage">changeImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#changeTextBgImage">changeTextBgImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#createImage">createImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getAngle">getAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getImageSrc">getImageSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getOffsetX">getOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getOffsetY">getOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getPosition">getPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getTextInfo">getTextInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#move">move</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#removeTextInfo">removeTextInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#removeTextInfos">removeTextInfos</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setAngle">setAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setBoundaryPadding">setBoundaryPadding</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setImageSrc">setImageSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setOffsetX">setOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setOffsetY">setOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setPosition">setPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setText">setText</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setVisible">setVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#textInfo">textInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#textInfo">textInfo</a></li></ul></li><li><a href="logi.maps.Label.html">logi.maps.Label</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getAlign">getAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getAngle">getAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getBgImgOffsetX">getBgImgOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getBgImgOffsetY">getBgImgOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getBgImgSrc">getBgImgSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getFontSize">getFontSize</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getOffsetX">getOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getOffsetY">getOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getPosition">getPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getText">getText</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getTextColor">getTextColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setAlign">setAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setAngle">setAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgBox">setBgBox</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgImgOffsetX">setBgImgOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgImgOffsetY">setBgImgOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgImgSrc">setBgImgSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBoundaryPadding">setBoundaryPadding</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setFontSize">setFontSize</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setOffsetX">setOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setOffsetY">setOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setPosition">setPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setText">setText</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setTextColor">setTextColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Line.html">logi.maps.Line</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#getLineType">getLineType</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setLatLngs">setLatLngs</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setLineProperty">setLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Polygon.html">logi.maps.Polygon</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setFillColor">setFillColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setLineProperty">setLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Cirlce.html">logi.maps.Cirlce</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setCenter">setCenter</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setFillColor">setFillColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setLineProperty">setLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setRadius">setRadius</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Route.html">logi.maps.Route</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#addPassedPoint">addPassedPoint</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPassedLine">setPassedLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPassedLineProperty">setPassedLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPastRecordLine">setPastRecordLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPastRecordLineProperty">setPastRecordLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setRouteLine">setRouteLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setRouteLineProperty">setRouteLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Gps.html">logi.maps.Gps</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#addGps">addGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setGps">setGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setMatchedProperty">setMatchedProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setRawProperty">setRawProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setRelProperty">setRelProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Custom.html">logi.maps.Custom</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.TextInfo.html">logi.maps.TextInfo</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImg">bgImg</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImg">bgImg</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgAlign">bgImgAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgOffsetX">bgImgOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgOffsetY">bgImgOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgSrc">bgImgSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#fontFamily">fontFamily</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#fontSize">fontSize</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#offsetX">offsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#offsetY">offsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#text">text</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#textAlign">textAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#textBold">textBold</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#textColor">textColor</a></li></ul></li><li><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></li><li><a href="logi.maps.LatLngBound.html">logi.maps.LatLngBound</a></li><li><a href="logi.maps.Meta.html">logi.maps.Meta</a></li><li><a href="logi.maps.Point.html">logi.maps.Point</a></li></ul><h3>Global</h3><ul><li><a href="global.html#ALIGN">ALIGN</a></li><li><a href="global.html#BRIDGE_MAPEVENT">BRIDGE_MAPEVENT</a></li><li><a href="global.html#DISTRICT_DATATYPE">DISTRICT_DATATYPE</a></li><li><a href="global.html#DISTRICT_STYLE">DISTRICT_STYLE</a></li><li><a href="global.html#DISTRICT_VISIBLETYPE">DISTRICT_VISIBLETYPE</a></li><li><a href="global.html#EVENT">EVENT</a></li><li><a href="global.html#LINETYPE">LINETYPE</a></li><li><a href="global.html#OBJEVENT">OBJEVENT</a></li></ul>
    
</nav>

<div id="main">
    
    <h1 class="page-title">TextInfo</h1>
    

    




<section>

<header>
    
        <h2>
        
            TextInfo
        
        </h2>
        
    
</header>

<article>
    
        <div class="container-overview">
        
            

    

    <h4 class="name" id="TextInfo"><span class="type-signature"></span>new TextInfo<span class="signature">()</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>Block member variable access using setter/getter</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>





























<h4>&nbsp;</h4>

        
        </div>
    

    

    

    
    
    

     

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    <h4 class="name" id="bgImg"><span class="type-signature"></span>bgImg<span class="signature">()</span><span class="type-signature"></span></h4>

    




<dl class="details">
    

    

    

    

    

    

    

    

    

    
        <dt class="important tag-deprecated">Deprecated:</dt><dd><ul class="dummy"><li>'bgImg' was declared deprecated. (>> bgImgSrc)</li></ul></dd>
    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>





























<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="bgImg"><span class="type-signature"></span>bgImg<span class="signature">()</span><span class="type-signature"></span></h4>

    




<dl class="details">
    

    

    

    

    

    

    

    

    

    
        <dt class="important tag-deprecated">Deprecated:</dt><dd><ul class="dummy"><li>'bgImg' was declared deprecated. (>> bgImgSrc)</li></ul></dd>
    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>





























<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="bgImgAlign"><span class="type-signature"></span>bgImgAlign<span class="signature">(bgImgAlign)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>배경 이미지 정렬을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.bgImgAlign = logi.maps.ALIGN.CM;
 //배경 이미지가 중앙 정렬로 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>bgImgAlign</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#ALIGN">ALIGN</a></span>



            
            </td>

            

            

            <td class="description last"><p>배경 이미지 정렬</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="bgImgOffsetX"><span class="type-signature"></span>bgImgOffsetX<span class="signature">(bgImgOffsetX)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>배경 이미지 위치의 offset을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.bgImgOffsetX = 10;
 //배경 이미지의 위치가 offsetX(10) 만큼 이동된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>bgImgOffsetX</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 이미지 offsetX</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="bgImgOffsetY"><span class="type-signature"></span>bgImgOffsetY<span class="signature">(bgImgOffsetY)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>배경 이미지 위치의 offset을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.bgImgOffsetY = 10;
 //배경 이미지의 위치가 offsetY(10) 만큼 이동된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>bgImgOffsetY</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 이미지 offsetY</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="bgImgSrc"><span class="type-signature"></span>bgImgSrc<span class="signature">(bgImgSrc)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>배경 이미지를 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.bgImgSrc = '/img/bg.png';
 //지정된 배경 이미지가 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>bgImgSrc</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>배경 이미지 경로</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="fontFamily"><span class="type-signature"></span>fontFamily<span class="signature">(fontFamily)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>fontFamily를 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.fontFamily = 'NotoSansKR-Bold-Hestia';
 //fontFamily 값이 'NotoSansKR-Bold-Hestia'로 설정된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fontFamily</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>fontFamily</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="fontSize"><span class="type-signature"></span>fontSize<span class="signature">(textColor)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>글자 색상을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.textColor = ‘blue’;
 //글자가 blue 색상으로 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>textColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자색</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="offsetX"><span class="type-signature"></span>offsetX<span class="signature">(offsetX)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>offsetX을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.offsetX = 0;
 //offsetX 값이 0으로 설정된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>offsetX</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>offsetX</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="offsetY"><span class="type-signature"></span>offsetY<span class="signature">(offsetY)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>offsetY를 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.offsetY = 0;
 //offsetY 값이 0으로 설정된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>offsetY</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>offsetY</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="text"><span class="type-signature"></span>text<span class="signature">(text)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>text를 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.text = 'test';
 //text 값이 'test'로 설정된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>text</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>text</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="textAlign"><span class="type-signature"></span>textAlign<span class="signature">(textAlign)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>글자 정렬을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.textAlign = logi.maps.ALIGN.CM;
 //글자가 중앙 정렬로 작성된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>textAlign</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#ALIGN">ALIGN</a></span>



            
            </td>

            

            

            <td class="description last"><p>글자정렬</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="textBold"><span class="type-signature"></span>textBold<span class="signature">(textBold)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>글자 bold를 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.textBold = true;
 //글자 bold가 활성화된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>textBold</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>글자 bold</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="textColor"><span class="type-signature"></span>textColor<span class="signature">(textColor)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>글자 색상을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>textInfo.textColor = ‘blue’;
 //글자가 blue 색상으로 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>textColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자색</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
    

    

    
</article>

</section>




    
    
</div>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 4.0.4</a> using the <a href="https://github.com/clenemt/docdash">docdash</a> theme.
</footer>

<script>prettyPrint();</script>
<script src="scripts/polyfill.js"></script>
<script src="scripts/linenumber.js"></script>

<script src="scripts/search.js" defer></script>


<script src="scripts/collapse.js" defer></script>


</body>
</html>