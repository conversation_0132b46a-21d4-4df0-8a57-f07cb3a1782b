<!DOCTYPE html>
<html lang="en">
<head>
    
    <meta charset="utf-8">
    <title>Label - Documentation</title>
    
    
    <script src="scripts/prettify/prettify.js"></script>
    <script src="scripts/prettify/lang-css.js"></script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc.css">
    <script src="scripts/nav.js" defer></script>
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>

<input type="checkbox" id="nav-trigger" class="nav-trigger" />
<label for="nav-trigger" class="navicon-button x">
  <div class="navicon"></div>
</label>

<label for="nav-trigger" class="overlay"></label>

<nav >
    
    <input type="text" id="nav-search" placeholder="Search" />
    
    
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="logi.maps.Map.html">logi.maps.Map</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addCircle">addCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addCustom">addCustom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addFont">addFont</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addGps">addGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addImage">addImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addLabel">addLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addLine">addLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addObject">addObject</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addPolygon">addPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#addRoute">addRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#disableWheelEvent">disableWheelEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#enableWheelEvent">enableWheelEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findCircle">findCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findGps">findGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findImage">findImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findLabel">findLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findLine">findLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findPolygon">findPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#findRoute">findRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getBounds">getBounds</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getCenter">getCenter</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getConvaxHull">getConvaxHull</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getDistrictHoverStyle">getDistrictHoverStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getDistrictStyle">getDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getDragAreaRect">getDragAreaRect</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getHoveredDistrict">getHoveredDistrict</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getLevel">getLevel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getNearestInfoOnPolyline">getNearestInfoOnPolyline</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getRealDistance">getRealDistance</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getSearchDistrictStyle">getSearchDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getSplitInfoOnPolyline">getSplitInfoOnPolyline</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getTheme">getTheme</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#getZoom">getZoom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hideLayer">hideLayer</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitCircle">hitCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitCircleKey">hitCircleKey</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImage">hitImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImageKey">hitImageKey</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImageKeys">hitImageKeys</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitImages">hitImages</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitPolygon">hitPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#hitPolygonKey">hitPolygonKey</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistCircle">isExistCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistGps">isExistGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistImage">isExistImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistLabel">isExistLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistLine">isExistLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistPolygon">isExistPolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#isExistRoute">isExistRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#move">move</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeAll">removeAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCircle">removeCircle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCircleAll">removeCircleAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCustom">removeCustom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeCustomAll">removeCustomAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeGps">removeGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeGpsAll">removeGpsAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeImage">removeImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeImageAll">removeImageAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLabel">removeLabel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLabelAll">removeLabelAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLine">removeLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeLineAll">removeLineAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeObject">removeObject</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removePolygon">removePolygon</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removePolygonAll">removePolygonAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeRoute">removeRoute</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#removeRouteAll">removeRouteAll</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#screen2world">screen2world</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#screenshot">screenshot</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setBounds">setBounds</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setCenter">setCenter</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setCenterMark">setCenterMark</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictHoverRange">setDistrictHoverRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictHoverStyle">setDistrictHoverStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictRange">setDistrictRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictStyle">setDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDistrictVisible">setDistrictVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDragAreaMode">setDragAreaMode</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDragAreaStyle">setDragAreaStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingCircleOnMove">setDrawingCircleOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingGpsOnMove">setDrawingGpsOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingImageOnMove">setDrawingImageOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingLabelOnMove">setDrawingLabelOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingLineOnMove">setDrawingLineOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingPolygonOnMove">setDrawingPolygonOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setDrawingRouteOnMove">setDrawingRouteOnMove</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setFreezeModeOnMoving">setFreezeModeOnMoving</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setHeatmap">setHeatmap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setLevel">setLevel</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setLevelRange">setLevelRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setMotionEventLock">setMotionEventLock</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setOrderType">setOrderType</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setOverlapCheck">setOverlapCheck</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setOverlapInfoVisibility">setOverlapInfoVisibility</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setSearchDistrict">setSearchDistrict</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setSearchDistrictStyle">setSearchDistrictStyle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setTheme">setTheme</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#setZoom">setZoom</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#showLayer">showLayer</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#updateMap">updateMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#world2screen">world2screen</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#zoomIn">zoomIn</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Map.html#zoomOut">zoomOut</a></li></ul></li><li><a href="logi.maps.Object.html">logi.maps.Object</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Object.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Image.html">logi.maps.Image</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#addTextInfo">addTextInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#changeImage">changeImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#changeTextBgImage">changeTextBgImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#createImage">createImage</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getAngle">getAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getImageSrc">getImageSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getOffsetX">getOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getOffsetY">getOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getPosition">getPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getTextInfo">getTextInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#move">move</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#removeTextInfo">removeTextInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#removeTextInfos">removeTextInfos</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setAngle">setAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setBoundaryPadding">setBoundaryPadding</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setImageSrc">setImageSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setOffsetX">setOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setOffsetY">setOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setPosition">setPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setText">setText</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#setVisible">setVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#textInfo">textInfo</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Image.html#textInfo">textInfo</a></li></ul></li><li><a href="logi.maps.Label.html">logi.maps.Label</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getAlign">getAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getAngle">getAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getBgImgOffsetX">getBgImgOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getBgImgOffsetY">getBgImgOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getBgImgSrc">getBgImgSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getFontSize">getFontSize</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getOffsetX">getOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getOffsetY">getOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getPosition">getPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getText">getText</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getTextColor">getTextColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setAlign">setAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setAngle">setAngle</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgBox">setBgBox</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgImgOffsetX">setBgImgOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgImgOffsetY">setBgImgOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBgImgSrc">setBgImgSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBoundaryPadding">setBoundaryPadding</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setFontSize">setFontSize</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setOffsetX">setOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setOffsetY">setOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setPosition">setPosition</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setText">setText</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setTextColor">setTextColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Label.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Line.html">logi.maps.Line</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#getLineType">getLineType</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setLatLngs">setLatLngs</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setLineProperty">setLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Line.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Polygon.html">logi.maps.Polygon</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setFillColor">setFillColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setLineProperty">setLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Polygon.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Cirlce.html">logi.maps.Cirlce</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setCenter">setCenter</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setFillColor">setFillColor</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setLineProperty">setLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setRadius">setRadius</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Cirlce.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Route.html">logi.maps.Route</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#addPassedPoint">addPassedPoint</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPassedLine">setPassedLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPassedLineProperty">setPassedLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPastRecordLine">setPastRecordLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setPastRecordLineProperty">setPastRecordLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setRouteLine">setRouteLine</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setRouteLineProperty">setRouteLineProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Route.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Gps.html">logi.maps.Gps</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#addGps">addGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setGps">setGps</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setMatchedProperty">setMatchedProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setRawProperty">setRawProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setRelProperty">setRelProperty</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Gps.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.Custom.html">logi.maps.Custom</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#addEventListener">addEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#getMap">getMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#getVisible">getVisible</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#key">key</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#on">on</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#removeEventListener">removeEventListener</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setBridgeEvent">setBridgeEvent</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setMap">setMap</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setRenderRange">setRenderRange</a></li><li data-type='method' style='display: none;'><a href="logi.maps.Custom.html#setVisible">setVisible</a></li></ul></li><li><a href="logi.maps.TextInfo.html">logi.maps.TextInfo</a><ul class='methods'><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImg">bgImg</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImg">bgImg</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgAlign">bgImgAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgOffsetX">bgImgOffsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgOffsetY">bgImgOffsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#bgImgSrc">bgImgSrc</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#fontFamily">fontFamily</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#fontSize">fontSize</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#offsetX">offsetX</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#offsetY">offsetY</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#text">text</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#textAlign">textAlign</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#textBold">textBold</a></li><li data-type='method' style='display: none;'><a href="logi.maps.TextInfo.html#textColor">textColor</a></li></ul></li><li><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></li><li><a href="logi.maps.LatLngBound.html">logi.maps.LatLngBound</a></li><li><a href="logi.maps.Meta.html">logi.maps.Meta</a></li><li><a href="logi.maps.Point.html">logi.maps.Point</a></li></ul><h3>Global</h3><ul><li><a href="global.html#ALIGN">ALIGN</a></li><li><a href="global.html#BRIDGE_MAPEVENT">BRIDGE_MAPEVENT</a></li><li><a href="global.html#DISTRICT_DATATYPE">DISTRICT_DATATYPE</a></li><li><a href="global.html#DISTRICT_STYLE">DISTRICT_STYLE</a></li><li><a href="global.html#DISTRICT_VISIBLETYPE">DISTRICT_VISIBLETYPE</a></li><li><a href="global.html#EVENT">EVENT</a></li><li><a href="global.html#LINETYPE">LINETYPE</a></li><li><a href="global.html#OBJEVENT">OBJEVENT</a></li></ul>
    
</nav>

<div id="main">
    
    <h1 class="page-title">Label</h1>
    

    




<section>

<header>
    
        <h2>
        
            Label
        
        </h2>
        
    
</header>

<article>
    
        <div class="container-overview">
        
            

    

    <h4 class="name" id="Label"><span class="type-signature"></span>new Label<span class="signature">(text, position, options)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨을 생성한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let label = new logi.maps.Label('test-text', { lat: 37.566596, lng: 127.007702 }, {map: logiMap});
 //동대문시장 위에 ‘test-text’ 글자가 표시된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>text</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>position</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



            
            </td>

            

            

            <td class="description last"><p>라벨 위치</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>options</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>option</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>key</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>label key (default: random 생성)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>class</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>label class (CSS의 class와 비슷함)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>zIndex</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>그리기 순서 (default: 0)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fontFamily</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>폰트 명</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>textBold</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>볼드</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>color</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fontSize</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>크기</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>groupId</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>같은 그룹끼리 바운더리가 겹치면 하나가 제거된다. (default: 0은 제외)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>offsetX</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>offset x</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>offsetY</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>offset y</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>angle</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>라벨 각도</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>align</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#ALIGN">ALIGN</a></span>



            
            </td>

            

            

            <td class="description last"><p>라벨 정렬</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bgBox</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스 설정</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>width</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 너비 (0 이면 라벨 사이즈에 맞춤)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>height</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 높이 (0 이면 라벨 사이즈에 맞춤)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>radius</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 라운드</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fillColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 배경 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 테두리 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineWidth</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 테두리 두께</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>offsetX</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 X 오프셋</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>offsetY</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 Y 오프셋</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>padding</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 패딩 정보</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>left</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 패딩 left</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>top</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 패딩 top</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>right</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 패딩 right</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bottom</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 패딩 bottom</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bgImgSrc</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>배경 이미지 주소</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bgImgOffsetX</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 이미지 offset x</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bgImgOffsetY</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 이미지 offset y</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>map</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Map.html">logi.maps.Map</a></span>



            
            </td>

            

            

            <td class="description last"><p>표시될 Map</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
        </div>
    

    
        <h3 class="subsection-title">Extends</h3>

        


    <ul>
        <li><a href="logi.maps.Object.html">logi.maps.Object</a></li>
    </ul>


    

    

    
    
    

     

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    <h4 class="name" id="addEventListener"><span class="type-signature"></span>addEventListener<span class="signature">(eventName, func)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>발생되는 이벤트를 등록된 리스너로 콜백한다.</p></li></ul></dd>
    

    

    

    

    

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="logi.maps.Object.html#addEventListener">logi.maps.Object#addEventListener</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>object.addEventListener(logi.maps.OBJEVENT.dblclick, function(event) { console.log("double click"); });
 //더블클릭하면 console 창에 “double click”이 출력된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>eventName</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#OBJEVENT">OBJEVENT</a></span>



            
            </td>

            

            

            <td class="description last"><p>이벤트명</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>콜백함수</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getAlign"><span class="type-signature"></span>getAlign<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="global.html#ALIGN">ALIGN</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 정렬을 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let align = label.getAlign();
 //라벨의 정렬 값이 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>정렬</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="global.html#ALIGN">ALIGN</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getAngle"><span class="type-signature"></span>getAngle<span class="signature">()</span><span class="type-signature"> &rarr; {Number}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 각도를 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let angle = label.getAngle();
 //라벨의 각도가 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>각도</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getBgImgOffsetX"><span class="type-signature"></span>getBgImgOffsetX<span class="signature">()</span><span class="type-signature"> &rarr; {Number}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 배경 이미지 위치 옵셋을 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let bgImgOffsetX = label.getBgImgOffsetX();
 //라벨의 배경 이미지 위치 옵셋이 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>배경 이미지 offset X</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getBgImgOffsetY"><span class="type-signature"></span>getBgImgOffsetY<span class="signature">()</span><span class="type-signature"> &rarr; {Number}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 배경 이미지 위치 옵셋을 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let bgImgOffsetY = label.getBgImgOffsetY();
 //라벨의 배경 이미지 위치 옵셋이 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>배경 이미지 offset Y</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getBgImgSrc"><span class="type-signature"></span>getBgImgSrc<span class="signature">()</span><span class="type-signature"> &rarr; {String}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 배경 이미지 주소를 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let bgImgSrc = label.getBgImgSrc();
 //라벨의 배경 이미지 주소가 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>이미지 주소</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getFontSize"><span class="type-signature"></span>getFontSize<span class="signature">()</span><span class="type-signature"> &rarr; {Number}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 글자 크기를 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let fontSize = label.getFontSize();
 //라벨의 글자 크기가 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>폰트 사이즈</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getMap"><span class="type-signature"></span>getMap<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="logi.maps.Map.html">logi.maps.Map</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 Map을 전달한다.</p></li></ul></dd>
    

    

    

    

    

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="logi.maps.Object.html#getMap">logi.maps.Object#getMap</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let map = object.getMap();
 //등록된 Map이 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>등록된 Map</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.Map.html">logi.maps.Map</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getOffsetX"><span class="type-signature"></span>getOffsetX<span class="signature">()</span><span class="type-signature"> &rarr; {Number}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 offset을 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let offset = label.getOffsetX();
 //라벨의 offset 값이 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>offset X</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Number</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getOffsetY"><span class="type-signature"></span>getOffsetY<span class="signature">(offsetY)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨 위치의 offset을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setOffsetY(100);
 //라벨이 실제 위치에서 화면 y좌표 100만큼 떨어진 곳에 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>offsetY</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>offset Y</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getPosition"><span class="type-signature"></span>getPosition<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="logi.maps.LatLng.html">logi.maps.LatLng</a>}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 위치를 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let position = label.getPosition();
 //라벨의 위치를 전달한다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>위치 좌표</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getText"><span class="type-signature"></span>getText<span class="signature">()</span><span class="type-signature"> &rarr; {String}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 글자를 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let text = label.getText();
 //라벨의 글자가 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>글자</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getTextColor"><span class="type-signature"></span>getTextColor<span class="signature">()</span><span class="type-signature"> &rarr; {String}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 글자 색상을 전달한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let textColor = label.getTextColor();
 //라벨의 글자 색상이 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>글자색</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="getVisible"><span class="type-signature"></span>getVisible<span class="signature">()</span><span class="type-signature"> &rarr; {Boolean}</span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>object의 보임/숨김 상태를 전달한다.</p></li></ul></dd>
    

    

    

    

    

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="logi.maps.Object.html#getVisible">logi.maps.Object#getVisible</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>let visible = object.getVisible();
 //object의 보임/숨김 상태가 전달된다.</code></pre>


















<h5 class="h5-returns">Returns:</h5>

        
<div class="param-desc">
    <p>보이기(true), 숨기기(false)</p>
</div>



<dl class="param-type">
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Boolean</span>



    </dd>
</dl>

    


<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="key"><span class="type-signature"></span>key<span class="signature">()</span><span class="type-signature"></span></h4>

    




<dl class="details">
    

    

    

    

    

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="logi.maps.Object.html#key">logi.maps.Object#key</a>
    </li></ul></dd>
    

    

    

    

    
        <dt class="important tag-deprecated">Deprecated:</dt><dd><ul class="dummy"><li>'key' was declared deprecated. (>> getKey())</li></ul></dd>
    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>





























<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="on"><span class="type-signature"></span>on<span class="signature">()</span><span class="type-signature"></span></h4>

    




<dl class="details">
    

    

    

    

    

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="logi.maps.Object.html#on">logi.maps.Object#on</a>
    </li></ul></dd>
    

    

    

    

    
        <dt class="important tag-deprecated">Deprecated:</dt><dd><ul class="dummy"><li>'on' was declared deprecated. (>> addEventListener(eventName, onEventHandle))</li></ul></dd>
    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>





























<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="removeEventListener"><span class="type-signature"></span>removeEventListener<span class="signature">(eventName, func)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>등록된 이벤트 리스너를 지운다.</p></li></ul></dd>
    

    

    

    

    

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="logi.maps.Object.html#removeEventListener">logi.maps.Object#removeEventListener</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>object.removeEventListener(logi.maps.OBJEVENT.dblclick, onEvent);
 //onEvent 함수가 이미지 이벤트 리스너에서 제거 된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>eventName</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#OBJEVENT">OBJEVENT</a></span>



            
            </td>

            

            

            <td class="description last"><p>이벤트명</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>func</code></td>
            

            <td class="type">
            
                
<span class="param-type">function</span>



            
            </td>

            

            

            <td class="description last"><p>콜백함수</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setAlign"><span class="type-signature"></span>setAlign<span class="signature">(align)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 정렬 값을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setAlign(logi.maps.LT);
 //라벨이 좌상단을 기준으로 정렬된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>align</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#ALIGN">ALIGN</a></span>



            
            </td>

            

            

            <td class="description last"><p>정렬</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setAngle"><span class="type-signature"></span>setAngle<span class="signature">(angle)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 각도를 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setAngle(45);
 //라벨이 45도 회전된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>angle</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>각도</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setBgBox"><span class="type-signature"></span>setBgBox<span class="signature">(bgBox)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 배경 박스를 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Examples</h5>
    
    <pre class="prettyprint"><code>label.setBgBox({
 width: 200,
 height: 100,
 radius: 10,
 fillColor: '#FFFFFF',
 lineColor: '#000000',
 lineWidth: 1,
 offsetX: 0,
 offsetY: 0,
 padding: {
  left: 10,
  top: 10,
  right: 10,
  bottom: 10,
}});
//라벨의 배경 박스가 설정된다.</code></pre>

    <pre class="prettyprint"><code>label.setBgBox({
 width: 0,
 height: 0,
});
//라벨 사이즈에 맞게 배경 박스가 설정된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>bgBox</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스 설정</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>width</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 너비 (0 이면 라벨 사이즈에 맞춤)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>height</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 높이 (0 이면 라벨 사이즈에 맞춤)</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>radius</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 라운드</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>fillColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 배경 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 테두리 색상</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>lineWidth</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 테두리 두께</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>offsetX</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 X 오프셋</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>offsetY</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 Y 오프셋</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>padding</code></td>
            

            <td class="type">
            
                
<span class="param-type">Object</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 패딩 정보</p>
                <h6>Properties</h6>
                

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>left</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 패딩 left</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>top</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 패딩 top</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>right</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 패딩 right</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bottom</code></td>
            

            <td class="type">
            
                
<span class="param-type">number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 박스의 패딩 bottom</p></td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>

            </td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setBgImgOffsetX"><span class="type-signature"></span>setBgImgOffsetX<span class="signature">(bgImgOffsetX)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 배경 이미지 위치 옵셋을 지정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setBgImgOffsetX(50);
 //배경 이미지의 실제 위치에서 화면 x좌표 50만큼 떨어진 곳에 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>bgImgOffsetX</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 이미지 offset X</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setBgImgOffsetY"><span class="type-signature"></span>setBgImgOffsetY<span class="signature">(bgImgOffsetY)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 배경 이미지 위치 옵셋을 지정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setBgImgOffsetY(50);
 //배경 이미지의 실제 위치에서 화면 y좌표 50만큼 떨어진 곳에 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>bgImgOffsetY</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>배경 이미지 offset Y</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setBgImgSrc"><span class="type-signature type-signature-async">(async) </span>setBgImgSrc<span class="signature">(src)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 배경 이미지를 지정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setBgImgSrc(‘./bg.png’);
 //‘./bg.png’ 이미지가 라벨의 배경이미지로 사용된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>src</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>배경 이미지 주소</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setBoundaryPadding"><span class="type-signature"></span>setBoundaryPadding<span class="signature">(left, top, right, bottom)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 바운더리를 조정할 수 있다. padding 정보를 고려하여 overlap을 체크한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>image.setBoundaryPadding(10, 10, 10, 10);
 //실제 바운더리보다 상하좌우 10정도 작게 체크한다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>left</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>padding left</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>top</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>padding top</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>right</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>padding right</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>bottom</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>padding bottom</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setBridgeEvent"><span class="type-signature"></span>setBridgeEvent<span class="signature">(eventName, activity)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>모바일 웹앱에서 Bridge로 전달 받을 이벤트를 등록한다.
[message format]</p>
<ul>
<li>‘message’: ‘onMapEvent’, ‘type’:{eventName}, ‘pointX’:{screenX}, ‘pointY’:{screenY}</li>
</ul></li></ul></dd>
    

    

    

    

    

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="logi.maps.Object.html#setBridgeEvent">logi.maps.Object#setBridgeEvent</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>object.setBridgeEvent(logi.maps.OBJEVENT.touch, true);
 //터치하면 Bridge를 통해 onObjEvent 함수 또는 메시지로 정보가 전달된다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>eventName</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="global.html#BRIDGE_MAPEVENT">BRIDGE_MAPEVENT</a></span>



            
            </td>

            

            

            <td class="description last"><p>이벤트명</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>activity</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>활성화 여부</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setFontSize"><span class="type-signature"></span>setFontSize<span class="signature">(fontSize)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 글자 크기를 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setFontSize(21);
 //라벨의 글자가 21 사이즈로 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fontSize</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>폰트 사이즈</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setMap"><span class="type-signature"></span>setMap<span class="signature">(map)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>표시될 Map을 등록한다.
setMap(null)을 입력하면 지도에서 이미지가 제거된다.</p></li></ul></dd>
    

    

    

    

    

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="logi.maps.Object.html#setMap">logi.maps.Object#setMap</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>object.setMap(map);
 //map 지도 위에 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>map</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.Map.html">logi.maps.Map</a></span>



            
            </td>

            

            

            <td class="description last"><p>표시될 Map</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setOffsetX"><span class="type-signature"></span>setOffsetX<span class="signature">(offsetX)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨 위치의 offset을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setOffsetX(100);
 //라벨이 실제 위치에서 화면 x좌표 100만큼 떨어진 곳에 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>offsetX</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>offset X</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setOffsetY"><span class="type-signature"></span>setOffsetY<span class="signature">(offsetY)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨 위치의 offset을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setOffsetY(100);
 //라벨이 실제 위치에서 화면 y좌표 100만큼 떨어진 곳에 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>offsetY</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>offset Y</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setPosition"><span class="type-signature"></span>setPosition<span class="signature">(latlng)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 위치를 지정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setPosition({lat: 37.566596, lng: 127.007702});
 //동대문시장 위에 라벨이 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>latlng</code></td>
            

            <td class="type">
            
                
<span class="param-type"><a href="logi.maps.LatLng.html">logi.maps.LatLng</a></span>



            
            </td>

            

            

            <td class="description last"><p>위치 좌표</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setRenderRange"><span class="type-signature"></span>setRenderRange<span class="signature">(fromLevel, toLevel)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>object가 그려져야 할 레벨 범위를 지정한다.
설정하지 않으면 모든 레벨에서 그려진다.</p></li></ul></dd>
    

    

    

    

    

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="logi.maps.Object.html#setRenderRange">logi.maps.Object#setRenderRange</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>object.setRenderRange(16, 17);
 //맵 레벨이 16~18일 때 object가 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>fromLevel</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>시작 레벨</p></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>toLevel</code></td>
            

            <td class="type">
            
                
<span class="param-type">Number</span>



            
            </td>

            

            

            <td class="description last"><p>끝 레벨</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setText"><span class="type-signature"></span>setText<span class="signature">(text)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 글자를 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setText(‘label-text’);
 //라벨의 글자가 ‘label-text’로 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>text</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setTextColor"><span class="type-signature"></span>setTextColor<span class="signature">(textColor)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>라벨의 글자 색상을 설정한다.</p></li></ul></dd>
    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>label.setTextColor(‘blue’);
 //라벨의 글자가 blue 색상으로 그려진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>textColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>



            
            </td>

            

            

            <td class="description last"><p>글자색</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
            

    

    <h4 class="name" id="setVisible"><span class="type-signature"></span>setVisible<span class="signature">(visible)</span><span class="type-signature"></span></h4>

    




<dl class="details">
    
    <dt class="tag-description">Description:</dt>
    <dd class="tag-description"><ul class="dummy"><li><p>object를 보이게 하거나 숨긴다.</p></li></ul></dd>
    

    

    

    

    

    
    <dt class="tag-overrides">Overrides:</dt>
    <dd class="tag-overrides"><ul class="dummy"><li>
        <a href="logi.maps.Object.html#setVisible">logi.maps.Object#setVisible</a>
    </li></ul></dd>
    

    

    

    

    

    

    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>.</li></ul></dd>
    

    

    

    

    
</dl>











    <h5 class="h5-examples">Example</h5>
    
    <pre class="prettyprint"><code>object.setVisible(false);
 //object가 숨겨진다.</code></pre>




    <h5 class="h5-parameters">Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>visible</code></td>
            

            <td class="type">
            
                
<span class="param-type">Boolean</span>



            
            </td>

            

            

            <td class="description last"><p>보이기(true), 숨기기(false)</p></td>
        </tr>

    
    </tbody>
</table>


















<h4>&nbsp;</h4>

        
    

    

    
</article>

</section>




    
    
</div>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 4.0.4</a> using the <a href="https://github.com/clenemt/docdash">docdash</a> theme.
</footer>

<script>prettyPrint();</script>
<script src="scripts/polyfill.js"></script>
<script src="scripts/linenumber.js"></script>

<script src="scripts/search.js" defer></script>


<script src="scripts/collapse.js" defer></script>


</body>
</html>