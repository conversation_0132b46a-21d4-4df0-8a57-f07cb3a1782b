[2.0.1.9]
 - logi.maps.label boundary 계산 오류 수정, logi.maps.image에 offset 옵션 추가

[2.0.1.10]
 - 터치 줌 할 때 중간 스케일 적용, 맵 타일 로드 속도 향상, fontfamily 추가

[2.0.1.11]
 - label 바운더리 계산을 기본은 text 바운더리로 하고 배경 이미지가 있으면 배경 이미지 바운더리로 함

[2.0.1.12]
 - device pixel ratio 적용
 - textinfo align -> bgImgAlign 명칭 변경

[2.0.1.13]
 -removeImageAll, removeLabelAll, removeLineAll, removePolygonAll, removeRouteAll 함수에 excludedKeys 파라미터 추가

[2.0.1.14]
 - logi.maps.Map 생성할 때 options.serverUrl 추가

[2.0.1.15]
 - setBounds 기능 보완

[2.0.1.16]
 - hit 오류 수정 (화면 밖 object가 선택되는 경우 발생)

[2.0.1.17]
- 화면 밖 넓은 범위를 setBounds 할 때 최대 스케일 적용되게 수정

[2.0.1.18]
- polygon, polyline, route에서 입력되는 latlng정보를 간결하게 가공하여 사용 (필요없는 latlng 정보를 제외 시킴)

[2.0.1.19]
- 현 위치로 이동시 아이콘 안 보이는 현상 수정
- dev-server 워터 마크 제거

[********]
- 이미지 캐쉬 추가
- 맵 전체 바운더리 설정
- IndexedDB에 예외처리 추가

[********]
- object에 class 멤버 변수 추가 (object를 삭제할 때 class 값으로 삭제 가능)
- object에 zIndex 추가 (우선 순위 지정 가능)

[********]
- svg render 방식 보완 (속도 향상)

[********]
- svg text(roadname) 관련 format 변경

[********]
- preload 로직 제거

[********]
- javascript, resource 파일들 버전 추가
- map tile 요청에 대한 304 status 결과 처리

[********]
- API 추가: checkRenderRange, setProperty, setBoundaryPadding 함수 추가
- hit 오차 수정 (polygon, line, route)
- on 함수 오류 수정 (EventHandler)

[*******]
- API 추가: move와 setBounds 함수에 behavior 파라미터 추가 (move animation)
- API 추가: getBounds 함수 추가
- API 추가: getConvaxHull 함수 추가
- API 추가: hitImages, hitImageKeys 함수 추가
- API 추가: setDrawingImageOnMove, setDrawingLabelOnMove 함수 추가
- API 추가: setDrawingLineOnMove, setDrawingPolygonOnMove, setDrawingRouteOnMove 함수 추가

[*******]
- API 추가: getSplitInfoOnPolyline 함수 추가
- API 추가: getNearestInfoOnPolyline 함수 추가
- API 추가: setMotionEventLock 함수 추가
- API 추가: hitPolygon, hitPolygonKey 함수 추가
- API 추가: setRegionDataVisibility, setPostDataVisibility 함수 추가
- API 추가: setRegionDataStyle, setPostDataStyle 함수 추가

[2.0.3.1]
- addFont 오류 수정: 폰트 추가 안되는 이슈

[2.0.4.0]
- API 추가: setOverlapCheck 함수 추가
- API 추가: setOverlapInfoVisibility 함수 추가

[2.0.5.0]
- API 제거: setRegionDataVisibility, setPostDataVisibility 함수 제거
- API 제거: setRegionDataStyle, setPostDataStyle 함수 제거
- API 추가: getHoveredDistrict 함수 추가
- API 추가: setDistrictVisible 함수 추가
- API 추가: setDistrictRange, setDistrictStyle 함수 추가
- API 추가: setDistrictHoverRange, setDistrictHoverStyle 함수 추가

[2.0.6.0]
- logi.maps.TextInfo에 textBold 추가

[2.0.6.1]
- roadname 간격, 빈도 조절에 따른 수정 (svg, xvg)

[2.0.6.2]
- logi.maps.mtlayer 추가 (logi.maps.map에서 유닛 분리)

[2.0.6.3]
- 멀티 터치 스케일 감도 조절 0.10 -> 0.18

[2.0.6.4]
- 멀티 터치 스케일 감도 조절 0.18 -> 0.16
- 맵 타일 캐쉬 사이즈 조정

[2.0.6.5]
- 리소스 버전 없으면 앱 버전 사용하게 변경

[2.0.7.0]
- 리소스 파일들 서버에서 관리

[2.0.7.1]
- 맵 폴리곤 외곽라인 옵션 추가
- User Sytle 추가

[2.0.7.2]
- fillOrder 추가 (Polygon fill과 stroke 그리는 순서)

[2.0.7.3]
- indexedDB 사이즈 관련 정책 변경
- map style 관련 기능 추가 (import/export)

[2.0.7.4]
- import 관련 eslint(react) 에러 수정
- logi.maps.gps 추가

[2.0.8.0]
- logi.maps.Line에 setLatLngs 함수 추가

[2.0.8.1]
- setBounds 기능 보완 (미세 스케일 적용)

[2.0.8.2]
- gps object 추가
- image에 getSize 함수 추가
- line에 getLineType 함수 추가

[2.0.9.0]
- webgl 사용 (map tile에서 polygon, polyline)

[2.0.9.1]
- earcut 로드 오류 수정

[2.1.0.0]
- webgl 활성화 (map tile에서 polygon, polyline)
- map tile cache 사이즈 화면 크기에 맞게 계산해서 적용
- map tile 메모리 로드 파트와 그리는 파트 분리
- findImage, findLabel, findLine, findPolygon, findRoute 함수 파라미터 변경 (String -> {key, class})

[2.1.0.1]
- 배경 색상 오류 수정

[2.1.1.0]
- 간헐적으로 외곽 타일(right,bottom)이 안그려지는 이슈 수정
- webgl 모드에서 screenshot 오류 수정
- DragArea 추가: 그리기 기능 (setDragAreaMode, setDragAreaStyle, getDragAreaRect)
- DragArea 추가: 찾기 기능 (findXXX 함수 파라미터에 keyword.rect 추가)

[2.1.2.0]
- react 호환되게 업데이트
- 마우스 휠 또는 터치 드래그에 대해서만 e.preventDefault 적용
- API 가이드 pdf -> html 변경
- Heatmap 기능 추가
- logi.maps.Custom 객체 추가
- zoom 용어 추가 (level은 타일 레벨(integer), zoom은 지도 스케일(double))

[2.1.2.1]
- logi.maps.Custom 보완
- showLayer / hideLayer 함수 추가 (map_style.xml에 LAYER 속성 값, exportStyle 함수를 이용하면 다운로드 할 수 있음)

[2.1.2.2]
- logi.maps.Map 생성자 파라미터에 options.OnClick 추가 

[2.1.3.0]
- 행정 구역 코드로 행정 구역(시도, 시군구, 읍면동) 그리기 (setSearchDistrict, setSearchDistrictStyle)
- 행정 구역 스타일이 제대로 변경되지 않는 문제 수정

[2.1.3.1]
- logi.maps.Map 생성자 파라미터 중 options.theme 버그 수정

[2.1.4.0]
- logi.maps.EVENT.click2 이벤트 추가 (드래그 없이 클릭했을 때만 발생하는 커스텀 이벤트)
- logi.maps.Map.addEventListener 및 removeEventListener에서 사용 가능

[2.1.5.0]
 - logi.maps.Map.setLevelRange 함수 추가

[2.1.6.0]
 - logi.maps.Map.setDistrictStyle 함수에 textBox 파라미터 추가 
 - logi.maps.Map.setDistrictHoverStyle 함수에 textBox 파라미터 추가 
 - logi.maps.Map.setSearchDistrictStyle 함수에 textBox 파라미터 추가 
 - logi.maps.Map.getDistrictStyle 함수 추가
 - logi.maps.Map.getDistrictHoverStyle 함수 추가
 - logi.maps.Map.getSearchDistrictStyle 함수 추가

[2.1.7.0]
 - logi.maps.Map.setDistrictStyle 함수에 fontfamily, fontBold 파라미터 추가
 - logi.maps.Map.setDistrictHoverStyle 함수에 fontfamily, fontBold 파라미터 추가
 - logi.maps.Map.setSearchDistrictStyle 함수에 fontfamily, fontBold 파라미터 추가
 - logi.maps.Map.setDistrictStyle 함수 파라미터 구조 변경
 - logi.maps.Map.setDistrictHoverStyle 함수 파라미터 구조 변경
 - logi.maps.Map.setSearchDistrictStyle 함수 파라미터 구조 변경
 - 행정구역 표시에서 사각 박스 안에 글자가 세로 중앙 정렬 되게 수정

 [2.1.7.1]
 - 버그 수정: drawObjects 함수에 사용되는 변수 const -> let 변경

 [2.1.8.0]
 - showLayer / hideLayer 함수 보완 options.drawType, options.categoryId 파라미터 추가

 [2.1.9.0]
 - showLayer / hideLayer 함수 보완, 기존 options.drawType, options.categoryId 제거하고 options.codeType, options.codeList 파라미터 추가

 [2.1.10.0]
 - logi.maps.label에 배경 박스(bgBox) 추가, constructor 또는 setBgBox 함수 참고

 [2.1.10.1]
 - fitBounds 함수에서 setCenter -> setLevel 순서에서 setLevel -> setCenter로 변경
 