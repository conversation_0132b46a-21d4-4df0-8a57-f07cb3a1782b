import React, {
  forwardRef,
  Ref,
  useEffect,
  useRef,
  useImperativeHandle,
} from 'react';
import logi from '../../../jmodules/map/logi-maps-api.js';
import { useLogiMap } from '../Map/LogiMapContext';

interface LogiDashedLineProps {
  id?: string;
  className?: string;
  path: logi.maps.LatLng[];
  width?: number;
  dashLength?: number;
  dashSpace?: number;
  color: string;
  strokeWidth?: number;
  strokeColor?: string;
  onClick?: () => void;
}

export type LogiLineRef = Ref<logi.maps.Line | null>;

function useLogiDashedLine(props: LogiDashedLineProps) {
  const { logiMap } = useLogiMap();

  const line = useRef(new logi.maps.Line()).current;

  useEffect(() => {
    if (!logiMap) {
      if (logiMap === undefined)
        console.error('<LogiDashedLine> has to be inside a Map component.');

      return;
    }

    line.setMap(logiMap);

    return () => {
      line.setMap(null);
    };
  }, [logiMap]);

  useEffect(() => {
    line.setLineProperty('DASH', {
      width: props.width,
      dashLength: props.dashLength,
      dashSpace: props.dashSpace,
      color: props.color,
      strokeWidth: props.strokeWidth,
      strokeColor: props.strokeColor,
    });
  }, [
    props.width,
    props.dashLength,
    props.dashSpace,
    props.color,
    props.strokeWidth,
    props.strokeColor,
  ]);

  useEffect(() => {
    if (!props.path) return;
    line.setLatLngs(props.path);
  }, [props.path]);

  return line;
}

export const LogiDashedLine = forwardRef(
  (props: LogiDashedLineProps, ref: LogiLineRef) => {
    const dashedLine = useLogiDashedLine(props);
    useImperativeHandle(ref, () => dashedLine);
    return null;
  },
);

LogiDashedLine.displayName = 'LogiDashedLine';
