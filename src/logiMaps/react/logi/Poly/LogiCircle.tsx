import React, {
  forwardRef,
  Ref,
  useEffect,
  useRef,
  useImperativeHandle,
} from 'react';
import logi from '../../../jmodules/map/logi-maps-api.js';
import { useLogiMap } from '../Map/LogiMapContext';

interface LogiCircleProps {
  id?: string;
  className?: string;
  center: { lat: number; lng: number };
  radius: number;
  fillColor: string;
  fillOpacity: number;
  strokeColor?: string;
  strokeOpacity?: number;
  strokeWeight?: number;
  onClick?: () => void;
}

export type LogiCircleRef = Ref<logi.maps.Circle | null>;

function useLogiCircle(props: LogiCircleProps) {
  const { logiMap } = useLogiMap();

  const toHexColorWithAlpha = (
    fillColor: string,
    fillOpacity: number,
  ): string => {
    // 이미 8자리인 경우 (알파값 있음)
    if (fillColor.length === 9) {
      return fillColor.toUpperCase();
    }

    // 안전하게: 6자리 hex + opacity
    const hexOpacity = Math.round(Math.min(Math.max(fillOpacity, 0), 1) * 255)
      .toString(16)
      .padStart(2, '0')
      .toUpperCase();

    return fillColor.toUpperCase() + hexOpacity;
  };

  const circle = useRef(
    new logi.maps.Circle(
      props.center,
      props.radius,
      toHexColorWithAlpha(props.fillColor, props.fillOpacity),
      {
        lineWidth: props.strokeWeight ?? 0,
        lineColor: toHexColorWithAlpha(
          props.strokeColor ?? '#FFFFFF',
          props.strokeOpacity ?? 0.0,
        ),
        radiusUnit: 'meter',
      },
    ),
  ).current;

  useEffect(() => {
    if (!logiMap) {
      if (logiMap === undefined)
        console.error('<LogiCircle> has to be inside a Map component.');

      return;
    }

    circle.setMap(logiMap);

    return () => {
      circle.setMap(null);
    };
  }, [logiMap]);

  useEffect(() => {
    if (!props.center || !logiMap) return;
    circle.setCenter(props.center);
  }, [logiMap, props.center]);

  useEffect(() => {
    if (!props.radius) return;
    circle.setRadius(props.radius, 'meter');
  }, [props.radius]);

  useEffect(() => {
    if (!props.fillColor) return;
    circle.setFillColor(
      toHexColorWithAlpha(props.fillColor, props.fillOpacity),
    );
  }, [props.fillColor, props.fillOpacity]);

  useEffect(() => {
    if (!props.strokeColor || !props.strokeOpacity || !props.strokeWeight)
      return;
    circle.setLineProperty(
      props.strokeWeight,
      toHexColorWithAlpha(props.strokeColor, props.strokeOpacity),
    );
  }, [props.strokeColor, props.strokeOpacity, props.strokeWeight]);

  return circle;
}

export const LogiCircle = forwardRef(
  (props: LogiCircleProps, ref: LogiCircleRef) => {
    const circle = useLogiCircle(props);
    useImperativeHandle(ref, () => circle);
    return null;
  },
);

LogiCircle.displayName = 'LogiCircle';
