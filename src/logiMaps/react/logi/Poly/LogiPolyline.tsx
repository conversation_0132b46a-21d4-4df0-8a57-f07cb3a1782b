import React, {
  forwardRef,
  Ref,
  useEffect,
  useRef,
  useImperativeHandle,
} from 'react';
import logi from '../../../jmodules/map/logi-maps-api.js';
import { useLogiMap } from '../Map/LogiMapContext';

interface LogiPolylineProps {
  id?: string;
  className?: string;
  path: logi.maps.LatLng[];
  width?: number;
  color: string;
  strokeWidth?: number;
  strokeColor?: string;
  onClick?: () => void;
}

export type LogiLineRef = Ref<logi.maps.Line | null>;

function useLogiPolyline(props: LogiPolylineProps) {
  const { logiMap } = useLogiMap();

  const line = useRef(new logi.maps.Line()).current;

  useEffect(() => {
    if (!logiMap) {
      if (logiMap === undefined)
        console.error('<LogiPolyline> has to be inside a Map component.');

      return;
    }

    line.setMap(logiMap);

    return () => {
      line.setMap(null);
    };
  }, [logiMap]);

  useEffect(() => {
    line.setLineProperty('POLY', {
      width: props.width,
      color: props.color,
      strokeWidth: props.strokeWidth,
      strokeColor: props.strokeColor,
    });
  }, [props.width, props.color, props.strokeWidth, props.strokeColor]);

  useEffect(() => {
    if (!props.path) return;
    line.setLatLngs(props.path);
  }, [props.path]);

  return line;
}

export const LogiPolyline = forwardRef(
  (props: LogiPolylineProps, ref: LogiLineRef) => {
    const polyline = useLogiPolyline(props);
    useImperativeHandle(ref, () => polyline);
    return null;
  },
);

LogiPolyline.displayName = 'LogiPolyline';
