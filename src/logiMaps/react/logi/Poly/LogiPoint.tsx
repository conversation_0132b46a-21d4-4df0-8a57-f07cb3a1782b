import React, {
  forwardRef,
  Ref,
  useEffect,
  useRef,
  useImperativeHandle,
} from 'react';
import logi from '../../../jmodules/map/logi-maps-api.js';
import { useLogiMap } from '../Map/LogiMapContext';

interface LogiPointProps {
  id?: string;
  className?: string;
  center: { lat: number; lng: number };
  pixelRadius: number;
  fillColor: string;
  fillOpacity: number;
  strokeColor?: string;
  strokeOpacity?: number;
  strokeWeight?: number;
  onClick?: () => void;
}

export type LogiPointRef = Ref<logi.maps.Circle | null>;

function useLogiPoint(props: LogiPointProps) {
  const { logiMap } = useLogiMap();

  const toHexColorWithAlpha = (
    fillColor: string,
    fillOpacity: number,
  ): string => {
    // 이미 8자리인 경우 (알파값 있음)
    if (fillColor.length === 9) {
      return fillColor.toUpperCase();
    }

    // 안전하게: 6자리 hex + opacity
    const hexOpacity = Math.round(Math.min(Math.max(fillOpacity, 0), 1) * 255)
      .toString(16)
      .padStart(2, '0')
      .toUpperCase();

    return fillColor.toUpperCase() + hexOpacity;
  };

  const circle = useRef(
    new logi.maps.Circle(
      props.center,
      props.pixelRadius,
      toHexColorWithAlpha(props.fillColor, props.fillOpacity),
      {
        lineWidth: props.strokeWeight ?? 0,
        lineColor: toHexColorWithAlpha(
          props.strokeColor ?? '#FFFFFF',
          props.strokeOpacity ?? 0.0,
        ),
        radiusUnit: 'pixel',
      },
    ),
  ).current;

  useEffect(() => {
    if (!logiMap) {
      if (logiMap === undefined)
        console.error('<LogiPoint> has to be inside a Map component.');

      return;
    }

    circle.setMap(logiMap);

    return () => {
      circle.setMap(null);
    };
  }, [logiMap]);

  useEffect(() => {
    if (!props.center || !logiMap) return;
    circle.setCenter(props.center);
  }, [logiMap, props.center]);

  useEffect(() => {
    if (!props.pixelRadius) return;
    circle.setRadius(props.pixelRadius, 'pixel');
  }, [props.pixelRadius]);

  useEffect(() => {
    if (!props.fillColor) return;
    circle.setFillColor(
      toHexColorWithAlpha(props.fillColor, props.fillOpacity),
    );
  }, [props.fillColor, props.fillOpacity]);

  useEffect(() => {
    if (!props.strokeColor || !props.strokeOpacity || !props.strokeWeight)
      return;
    circle.setLineProperty(
      props.strokeWeight,
      toHexColorWithAlpha(props.strokeColor, props.strokeOpacity),
    );
  }, [props.strokeColor, props.strokeOpacity, props.strokeWeight]);

  return circle;
}

export const LogiPoint = forwardRef(
  (props: LogiPointProps, ref: LogiPointRef) => {
    const circle = useLogiPoint(props);
    useImperativeHandle(ref, () => circle);
    return null;
  },
);

LogiPoint.displayName = 'LogiPoint';
