import React, {
  forwardRef,
  Ref,
  useEffect,
  useRef,
  useImperativeHandle,
} from 'react';
import logi from '../../../jmodules/map/logi-maps-api.js';
import { useLogiMap } from '../Map/LogiMapContext';

interface LogiDottedLineProps {
  id?: string;
  className?: string;
  path: logi.maps.LatLng[];
  dotRadius?: number;
  dotGap?: number;
  color: string;
  strokeWidth?: number;
  strokeColor?: string;
  onClick?: () => void;
}

export type LogiLineRef = Ref<logi.maps.Line | null>;

function useLogiDottedLine(props: LogiDottedLineProps) {
  const { logiMap } = useLogiMap();

  const line = useRef(new logi.maps.Line()).current;

  useEffect(() => {
    if (!logiMap) {
      if (logiMap === undefined)
        console.error('<LogiDottedLine> has to be inside a Map component.');

      return;
    }

    line.setMap(logiMap);

    return () => {
      line.setMap(null);
    };
  }, [logiMap]);

  useEffect(() => {
    line.setLineProperty('DOT', {
      dotRadius: props.dotRadius,
      dotGap: props.dotGap,
      color: props.color,
      strokeWidth: props.strokeWidth,
      strokeColor: props.strokeColor,
    });
  }, [
    props.dotRadius,
    props.dotGap,
    props.color,
    props.strokeWidth,
    props.strokeColor,
  ]);

  useEffect(() => {
    if (!props.path) return;
    line.setLatLngs(props.path);
  }, [props.path]);

  return line;
}

export const LogiDottedLine = forwardRef(
  (props: LogiDottedLineProps, ref: LogiLineRef) => {
    const dottedLine = useLogiDottedLine(props);
    useImperativeHandle(ref, () => dottedLine);
    return null;
  },
);

LogiDottedLine.displayName = 'LogiDottedLine';
