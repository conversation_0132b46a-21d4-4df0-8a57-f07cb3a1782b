import React, { createContext, useState, ReactNode } from 'react';
import logi from '../../../jmodules/map/logi-maps-api.js';

interface LogiMapContextType {
  logiMap: logi.maps.Map | null;
  setLogiMap: (map: logi.maps.Map) => void;
}

const LogiMapContext = createContext<LogiMapContextType | undefined>(undefined);

interface LogiMapProviderProps {
  children: ReactNode;
}

const LogiMapProvider: React.FC<LogiMapProviderProps> = ({ children }) => {
  const [logiMap, setLogiMap] = useState<logi.maps.Map | null>(null);

  return (
    <LogiMapContext.Provider value={{ logiMap, setLogiMap }}>
      {children}
    </LogiMapContext.Provider>
  );
};

const useLogiMap = (): LogiMapContextType => {
  const context = React.useContext(LogiMapContext);
  if (!context) {
    throw new Error('useLogiMap must be used within a LogiMapProvider');
  }
  return context;
};

export { LogiMapProvider, useLogiMap };
