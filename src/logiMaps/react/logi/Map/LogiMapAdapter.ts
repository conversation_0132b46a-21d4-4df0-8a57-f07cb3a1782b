import { GeneralMapAdapter } from '../../general/Map/GeneralMapAdapter';
import logi from '.';

export class LogiMapAdapter implements GeneralMapAdapter {
  constructor(private map: logi.maps.Map) {}

  getScreenSize() {
    return this.map.getScreenSize();
  }

  setCenter(latlng: { lat: number; lng: number }) {
    this.map.setCenter(latlng);
  }

  setZoom(zoom: number) {
    this.map.setZoom(zoom);
  }

  zoomIn() {
    this.map.zoomIn();
  }

  zoomOut() {
    this.map.zoomOut();
  }

  world2plane(latlng: { lat: number; lng: number }, level: number) {
    return this.map.world2plane(latlng, level);
  }

  getBounds() {
    const result = {
      east: 180.0,
      north: 90.0,
      south: -90.0,
      west: -180.0,
    };
    const bounds = this.map.getBounds();
    if (bounds) {
      result.west = bounds.left;
      result.north = bounds.top;
      result.east = bounds.right;
      result.south = bounds.bottom;
    }
    return result;
  }

  fitBounds(
    latlngs: { lat: number; lng: number }[],
    padding: {
      top: string;
      right: string;
      bottom: string;
      left: string;
    },
  ) {
    function parsePaddingValue(
      value: string,
      relativeTo: number, // % 계산할 기준값 (width, height 등)
    ): number {
      if (value.endsWith('%')) {
        return (parseFloat(value) / 100.0) * relativeTo;
      }

      if (value.endsWith('px')) {
        return parseFloat(value);
      }

      return parseFloat(value);
    }

    const bounds = new logi.maps.Bounds();
    for (const latlng of latlngs) {
      bounds.extend(latlng);
    }

    const { width, height } = this.getScreenSize();

    const top = parsePaddingValue(padding.top, height * 0.5);
    const right = parsePaddingValue(padding.right, width * 0.5);
    const bottom = parsePaddingValue(padding.bottom, height * 0.5);
    const left = parsePaddingValue(padding.left, width * 0.5);

    this.map.fitBounds(bounds, { top, right, bottom, left });
  }

  isInBoundary(latlng: { lat: number; lng: number }) {
    const latlngBounds = this.getBounds();
    if (latlngBounds.west <= latlngBounds.east) {
      if (
        latlng.lat >= latlngBounds.south &&
        latlng.lat <= latlngBounds.north
      ) {
        if (
          latlng.lng >= latlngBounds.west &&
          latlng.lng <= latlngBounds.east
        ) {
          return true;
        }
      }
    } else {
      if (
        latlng.lat >= latlngBounds.south &&
        latlng.lat <= latlngBounds.north
      ) {
        if (
          (latlng.lng >= latlngBounds.west && latlng.lng <= 180.0) ||
          (latlng.lng <= latlngBounds.east && latlng.lng >= -180.0)
        ) {
          return true;
        }
      }
    }
    return false;
  }
}
