import React, { useCallback, useEffect, useRef, useState } from 'react';
import logi from '../../../jmodules/map/logi-maps-api.js';
import { useLogiMap } from './LogiMapContext';
import { GeneralMapAdapter } from '../../general/Map/GeneralMapAdapter';
import { LogiMapAdapter } from './LogiMapAdapter';

interface LogiMapProps {
  id?: string;
  className?: string;
  maxZoom: number;
  minZoom: number;
  defaultZoom: number;
  defaultCenter?: { lat: number; lng: number };
  extn?: string;
  region?: string;
  theme?: string;
  serverUrl?: string;
  children?: React.ReactNode;
  onInitMap?: (generalMapAdapter: GeneralMapAdapter) => void;
  onClick?: (event: MouseEvent) => void;
  onZoomChanged?: (zoom: number) => void;
  onSizeChanged?: (width: number, height: number) => void;
  onBoundsChanged?: (
    west: number,
    north: number,
    east: number,
    south: number,
  ) => void;
}

const LogiMap: React.FC<LogiMapProps> = ({
  id,
  className,
  children,
  maxZoom,
  minZoom,
  defaultZoom,
  defaultCenter,
  extn,
  region,
  theme,
  serverUrl,
  onInitMap,
  onClick,
  onZoomChanged,
  onSizeChanged,
  onBoundsChanged,
}) => {
  const { logiMap, setLogiMap } = useLogiMap();
  const mapDivRef = useRef<HTMLDivElement>(null);
  const logiMapRef = useRef<logi.maps.Map>();
  const handleInitMapRef = useRef<typeof onInitMap>();
  const handleClickRef = useRef<typeof onClick>();
  const handleZoomChangedRef = useRef<typeof onZoomChanged>();
  const handleSizeChangedRef = useRef<typeof onSizeChanged>();
  const handleBoundsChangedRef = useRef<typeof onBoundsChanged>();

  const [finalId] = useState(() => id ?? Math.random().toString(36).slice(-8));

  const isMouseMoveRef = useRef(false);
  const isMouseClickRef = useRef(false);

  /** useEffect */

  useEffect(() => {
    handleInitMapRef.current = onInitMap;
  }, [onInitMap]);

  useEffect(() => {
    handleClickRef.current = onClick;
  }, [onClick]);

  useEffect(() => {
    handleZoomChangedRef.current = onZoomChanged;
  }, [onZoomChanged]);

  useEffect(() => {
    handleSizeChangedRef.current = onSizeChanged;
  }, [onSizeChanged]);

  useEffect(() => {
    handleBoundsChangedRef.current = onBoundsChanged;
  }, [onBoundsChanged]);

  useEffect(() => {
    if (mapDivRef.current && !logiMapRef.current) {
      logiMapRef.current = new logi.maps.Map(`map_div_${finalId}`, {
        levelRange: {
          min: (minZoom < 4 ? 4 : minZoom) ?? 4,
          max: (maxZoom > 18 ? 18 : maxZoom) ?? 18,
        },
        center: defaultCenter ?? { lat: 40.73062, lng: -73.99314 },
        level: defaultZoom ?? 17,
        extn: extn ?? 'xvg',
        region: region ?? 'nam',
        theme: theme ?? 'default',
        serverUrl: serverUrl ?? 'https://map-tile2-qa-global.logisteq.com',
        customDivId: {
          overlay: `overlay_div_${finalId}`,
          event: `event_div_${finalId}`,
        },
      });

      logiMapRef.current.addEventListener('_zoom', handleZoomChanged);
      logiMapRef.current.addEventListener('_resize', handleSizeChanged);
      logiMapRef.current.addEventListener('_bounds', handleBoundsChanged);

      setLogiMap(logiMapRef.current);
    }
  }, [mapDivRef.current]);

  useEffect(() => {
    if (logiMap) {
      handleInitMapRef.current?.(new LogiMapAdapter(logiMap));
    }
  }, [logiMap]);

  /** Event Listener */

  const handleZoomChanged = useCallback((event: CustomEvent) => {
    handleZoomChangedRef.current?.(event.detail.zoom);
  }, []);

  const handleSizeChanged = useCallback((event: CustomEvent) => {
    handleSizeChangedRef.current?.(event.detail.width, event.detail.height);
  }, []);

  const handleBoundsChanged = useCallback((event: CustomEvent) => {
    handleBoundsChangedRef.current?.(
      event.detail.west,
      event.detail.north,
      event.detail.east,
      event.detail.south,
    );
  }, []);

  const handleMouseDown = useCallback(() => {
    isMouseMoveRef.current = false;
    isMouseClickRef.current = false;
  }, []);

  const handleMouseMove = useCallback(() => {
    isMouseMoveRef.current = true;
  }, []);

  const handleClick = useCallback((event: React.MouseEvent) => {
    if (isMouseClickRef.current) {
      handleClickRef.current?.(event.nativeEvent);
    }
  }, []);

  const handleMouseUp = useCallback(() => {
    if (!isMouseMoveRef.current) {
      isMouseClickRef.current = true;
    }
    isMouseMoveRef.current = false;
  }, []);

  return (
    <div className={className ?? 'w-full h-full'}>
      <div
        style={{ position: 'relative', width: '100%', height: '100%' }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onClick={handleClick}
        onMouseUp={handleMouseUp}
        id={`event_div_${finalId}`}
      >
        <div
          style={{ position: 'absolute', width: '100%', height: '100%' }}
          ref={mapDivRef}
          id={`map_div_${finalId}`}
        />
        <div
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
          }}
          id={`overlay_div_${finalId}`}
        >
          {children}
        </div>
      </div>
    </div>
  );
};

export default LogiMap;
