import React, { useEffect, useRef, useState } from 'react';
import logi from '../../../jmodules/map/logi-maps-api.js';
import { useLogiMap } from '../Map/LogiMapContext';

interface LogiInfoWindowProps {
  id?: string;
  className?: string;
  position: { lat: number; lng: number };
  pixelOffset?: [number, number];
  zIndex?: number;
  backgroundColor?: string;
  padding?: string;
  border?: string;
  borderRadius?: string;
  children?: React.ReactNode;
}

const LogiInfoWindow: React.FC<LogiInfoWindowProps> = ({
  id,
  className,
  position,
  pixelOffset,
  zIndex,
  backgroundColor,
  padding,
  borderRadius,
  children,
}) => {
  const { logiMap } = useLogiMap();
  const infoWindowDivRef = useRef<HTMLDivElement>(null);
  const [finalId] = useState(
    () => `iw-${id ?? Math.random().toString(36).slice(-8)}`,
  );

  const custom = useRef(new logi.maps.Meta(finalId)).current;

  useEffect(() => {
    if (!logiMap) {
      if (logiMap === undefined)
        console.error('<LogiInfoWindow> has to be inside a Map component.');

      return;
    }

    custom.setMap(logiMap);

    return () => {
      custom.setMap(null);
    };
  }, [logiMap]);

  useEffect(() => {
    if (infoWindowDivRef.current) {
      const divElement = infoWindowDivRef.current;
      if (divElement) {
        // div 요소에 이벤트 리스너 등록
        divElement.addEventListener('touchstart', handleTouchEvent);
        divElement.addEventListener('touchmove', handleTouchEvent);
        divElement.addEventListener('touchend', handleTouchEvent);
        divElement.addEventListener('dblclick', handleMouseEvent);
        divElement.addEventListener('mousedown', handleMouseEvent);
        divElement.addEventListener('mouseup', handleMouseEvent);
        divElement.addEventListener('mousemove', handleMouseEvent);
        divElement.addEventListener('wheel', handleWheel);
        return () => {
          // 컴포넌트 언마운트 시 이벤트 리스너 정리
          divElement.removeEventListener('touchstart', handleTouchEvent);
          divElement.removeEventListener('touchmove', handleTouchEvent);
          divElement.removeEventListener('touchend', handleTouchEvent);
          divElement.removeEventListener('dblclick', handleMouseEvent);
          divElement.removeEventListener('mousedown', handleMouseEvent);
          divElement.removeEventListener('mouseup', handleMouseEvent);
          divElement.removeEventListener('mousemove', handleMouseEvent);
          divElement.removeEventListener('wheel', handleWheel);
        };
      }
    }
  }, [infoWindowDivRef.current]);

  const handleTouchEvent = (event: TouchEvent) => {
    event.stopPropagation(); // 이벤트 전파를 막음
  };

  const handleMouseEvent = (event: MouseEvent) => {
    event.stopPropagation(); // 이벤트 전파를 막음
  };

  const handleWheel = (event: WheelEvent) => {
    event.stopPropagation(); // 이벤트 전파를 막음
  };

  const handleClick = (event: React.MouseEvent) => {
    event.stopPropagation(); // 이벤트 전파를 막음
  };

  return (
    <div
      id={finalId}
      data-category="infowindow"
      data-lat={position.lat}
      data-lng={position.lng}
      ref={infoWindowDivRef}
      onClick={handleClick}
      className={className}
      style={{
        position: 'absolute',
        visibility: 'hidden',
        zIndex: `${zIndex ?? 'auto'}`,
        backgroundColor: `${backgroundColor ?? '#ffffff'}`,
        padding: `${padding ?? '10px 6px 10px 6px'}`,
        borderRadius: `${borderRadius ?? '4px'}`,
        transform: `translate(-50%, -100%) translateX(${pixelOffset?.[0] ?? 0}px) translateY(${pixelOffset?.[1] ?? 0}px)`,
      }}
    >
      <div
        style={{
          position: 'absolute',
          left: '50%',
          bottom: '-6px',
          transform: 'translateX(-50%)',
          width: '0',
          height: '0',
          borderLeft: '6px solid transparent',
          borderRight: '6px solid transparent',
          borderTop: `6px solid ${backgroundColor ?? '6px solid white'}`,
        }}
      />
      {children}
    </div>
  );
};

export default LogiInfoWindow;
