import React, { forwardRef, Ref, useEffect, useImperativeHandle } from 'react';
import logi from '../../../jmodules/map/logi-maps-api.js';
import { useLogiMap } from '../Map/LogiMapContext.js';

interface LogiHeatmapProps {
  data: { latlng: { lat: number; lng: number }; intensity: number }[];
  radius?: number;
  opacity?: number;
}

export type LogiHeatmapRef = Ref<logi.maps.Map | null>;

function useLogiHeatmap(props: LogiHeatmapProps) {
  const { logiMap } = useLogiMap();

  useEffect(() => {
    return () => {
      logiMap?.setHeatmap(
        [] as { latlng: { lat: number; lng: number }; intensity: number }[],
        null,
        null,
      );
    };
  }, []);

  useEffect(() => {
    if (!logiMap) {
      if (logiMap === undefined)
        console.error('<LogiHeatmap> has to be inside a Map component.');
      return;
    }
  }, [logiMap]);

  useEffect(() => {
    if (!props.data || !logiMap) return;
    logiMap.setHeatmap(props.data, props.radius, props.opacity);
  }, [logiMap, props.data, props.radius, props.opacity]);

  return logiMap;
}

export const LogiHeatmap = forwardRef(
  (props: LogiHeatmapProps, ref: LogiHeatmapRef) => {
    const heatmap = useLogiHeatmap(props);
    useImperativeHandle(ref, () => heatmap);
    return null;
  },
);

LogiHeatmap.displayName = 'LogiHeatmap';
