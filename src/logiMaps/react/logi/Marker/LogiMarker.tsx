import React, { useEffect, useRef, useState } from 'react';
import LogiMarkerPin from './LogiMarkerPin';
import logi from '../../../jmodules/map/logi-maps-api.js';
import { useLogiMap } from '../Map/LogiMapContext';

interface LogiMarkerProps {
  id?: string;
  className?: string;
  position: { lat: number; lng: number };
  anchorPoint?: [string, string];
  offset?: { x: number; y: number };
  zIndex?: number;
  pinColor?: [string, string]; // 색상 파라미터
  holeColor?: string; // 구멍 색상 파라미터
  children?: React.ReactNode;
  onClick?: (id: string, position: { lat: number; lng: number }) => void;
}

const LogiMarker: React.FC<LogiMarkerProps> = ({
  id,
  className,
  position,
  anchorPoint,
  offset,
  zIndex,
  pinColor,
  holeColor,
  children,
  onClick,
}) => {
  const { logiMap } = useLogiMap();
  const [finalId] = useState(
    () => `mk-${id ?? Math.random().toString(36).slice(-8)}`,
  );

  const custom = useRef(new logi.maps.Meta(finalId)).current;

  useEffect(() => {
    if (!logiMap) {
      if (logiMap === undefined)
        console.error('<LogiMarker> has to be inside a Map component.');

      return;
    }

    custom.setMap(logiMap);

    return () => {
      custom.setMap(null);
    };
  }, [logiMap]);

  const handleClick = (event: React.MouseEvent) => {
    onClick?.(finalId, position);
    event.stopPropagation(); // 이벤트 전파를 막음
  };

  return (
    <div
      id={finalId}
      className={className}
      data-category="marker"
      data-lat={position.lat}
      data-lng={position.lng}
      style={{
        position: 'absolute',
        visibility: 'hidden',
        //left: `0px`,
        //top: `0px`,
        zIndex: `${zIndex ?? 'auto'}`,
        cursor: 'pointer',
        transform: `translate(-${anchorPoint?.[0] ?? '50%'}, -${anchorPoint?.[1] ?? '50%'}) translateX(${offset?.x ?? 0}px) translateY(${offset?.y ?? 0}px)`,
      }}
      onClick={handleClick}
    >
      {children ? (
        children
      ) : (
        <LogiMarkerPin pinColor={pinColor} holeColor={holeColor} />
      )}
    </div>
  );
};

export default LogiMarker;
