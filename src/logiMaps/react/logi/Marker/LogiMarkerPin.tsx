import React from 'react';

interface LogiMarkerPinProps {
  pinColor?: [string, string]; // 색상 파라미터
  holeColor?: string; // 구멍 색상 파라미터
}

const LogiMarkerPin: React.FC<LogiMarkerPinProps> = ({
  pinColor,
  holeColor,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      width="40"
      height="40"
      fill="none"
    >
      <path
        fill="url(#gradient)"
        d="M12 2C9 2 6 5 6 8.5C6 12 12 21 12 21C12 21 18 12 18 8.5C18 5 15 2 12 2Z"
      />
      <circle cx="12" cy="8" r="3" fill={holeColor ?? 'white'} />
      <defs>
        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop
            offset="0%"
            style={{ stopColor: pinColor?.[0] ?? 'red', stopOpacity: 1 }}
          />
          <stop
            offset="100%"
            style={{ stopColor: pinColor?.[1] ?? 'orange', stopOpacity: 1 }}
          />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default LogiMarkerPin;
