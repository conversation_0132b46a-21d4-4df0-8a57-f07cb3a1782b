import { LogiPoint } from '../../logi/Poly';
import { GooglePoint } from '../../google/Poly';
import { useMapSource } from '../Map';

export interface GeneralPointProps {
  id?: string;
  className?: string;
  center: { lat: number; lng: number };
  pixelRadius: number;
  fillColor: string;
  fillOpacity: number;
  strokeColor?: string;
  strokeOpacity?: number;
  strokeWeight?: number;
  onClick?: () => void;
}

const GeneralPoint = (props: GeneralPointProps) => {
  const mapSource = useMapSource();

  if (mapSource === 'logi') {
    return <LogiPoint {...props} />;
  }

  if (mapSource === 'google') {
    return <GooglePoint {...props} />;
  }

  return null;
};

export default GeneralPoint;
