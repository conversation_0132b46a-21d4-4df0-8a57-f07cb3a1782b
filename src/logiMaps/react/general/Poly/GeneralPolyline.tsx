import { LogiPolyline } from '../../logi/Poly';
import { GooglePolyline } from '../../google/Poly';
import { useMapSource } from '../Map';

export interface GeneralPolylineProps {
  id?: string;
  className?: string;
  path: { lat: number; lng: number }[];
  width?: number;
  color: string;
  strokeWidth?: number;
  strokeColor?: string;
  onClick?: () => void;
}

const GeneralPolyline = (props: GeneralPolylineProps) => {
  const mapSource = useMapSource();

  if (mapSource === 'logi') {
    return <LogiPolyline {...props} />;
  }

  if (mapSource === 'google') {
    return <GooglePolyline {...props} />;
  }

  return null;
};

export default GeneralPolyline;
