import { LogiCircle } from '../../logi/Poly';
import { GoogleCircle } from '../../google/Poly';
import { useMapSource } from '../Map';

export interface GeneralCircleProps {
  id?: string;
  className?: string;
  center: { lat: number; lng: number };
  radius: number;
  fillColor: string;
  fillOpacity: number;
  strokeColor?: string;
  strokeOpacity?: number;
  strokeWeight?: number;
  onClick?: () => void;
}

const GeneralCircle = (props: GeneralCircleProps) => {
  const mapSource = useMapSource();

  if (mapSource === 'logi') {
    return <LogiCircle {...props} />;
  }

  if (mapSource === 'google') {
    return <GoogleCircle {...props} />;
  }

  return null;
};

export default GeneralCircle;
