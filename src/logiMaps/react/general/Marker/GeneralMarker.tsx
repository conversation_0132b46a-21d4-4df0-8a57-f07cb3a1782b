import { LogiMarker } from '../../logi/Marker';
import { GoogleMarker } from '../../google/Marker';
import { useMapSource } from '../Map';

export interface GeneralMarkerProps {
  id: string;
  position: {
    lat: number;
    lng: number;
  };
  anchorPoint?: [string, string];
  zIndex?: number;
  children?: React.ReactNode;
  onClick?: (id: string, position: { lat: number; lng: number }) => void;
}

const GeneralMarker: React.FC<GeneralMarkerProps> = (props) => {
  const mapSource = useMapSource();

  if (mapSource === 'logi') {
    return <LogiMarker {...props} />;
  }

  if (mapSource === 'google') {
    return <GoogleMarker {...props} />;
  }

  return null;
};

export default GeneralMarker;
