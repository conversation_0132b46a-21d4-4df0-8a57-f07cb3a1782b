import { LogiHeatmap } from '../../logi/Heatmap';
import { GoogleHeatmap } from '../../google/Heatmap';
import { useMapSource } from '../Map';

export interface GeneralHeatmapProps {
  data: { latlng: { lat: number; lng: number }; intensity: number }[];
  radius: number;
  opacity: number;
}

const GeneralHeatmap = (props: GeneralHeatmapProps) => {
  const mapSource = useMapSource();

  if (mapSource === 'logi') {
    return <LogiHeatmap {...props} />;
  }

  if (mapSource === 'google') {
    return <GoogleHeatmap {...props} />;
  }

  return null;
};

export default GeneralHeatmap;
