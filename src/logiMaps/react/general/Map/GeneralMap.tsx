import React from 'react';
import { LogiMap, LogiMapProvider } from '../../logi/Map';
import { GoogleMap } from '../../google/Map';
import { GeneralMapAdapter } from './GeneralMapAdapter';
import { MapSourceProvider } from './MapSourceContext';

interface GeneralMapProps {
  mapSource: string;
  id: string;
  className?: string;
  maxZoom: number;
  minZoom: number;
  defaultZoom: number;
  defaultCenter?: { lat: number; lng: number };
  children?: React.ReactNode;
  onInitMap?: (generalMapAdapter: GeneralMapAdapter) => void; // 타입은 any로 추상화
  onClick?: (event: MouseEvent) => void;
  onZoomChanged?: (zoom: number) => void;
  onSizeChanged?: (width: number, height: number) => void;
  onBoundsChanged?: (
    west: number,
    north: number,
    east: number,
    south: number,
  ) => void;
}

const GeneralMap: React.FC<GeneralMapProps> = ({ mapSource, ...props }) => {
  if (mapSource === 'logi') {
    return (
      <MapSourceProvider source={mapSource}>
        <LogiMapProvider>
          <LogiMap {...props} />
        </LogiMapProvider>
      </MapSourceProvider>
    );
  }

  if (mapSource === 'google') {
    return (
      <MapSourceProvider source={mapSource}>
        <GoogleMap {...props} />;
      </MapSourceProvider>
    );
  }

  return null;
};

export default GeneralMap;
