export interface GeneralMapAdapter {
  getScreenSize(): { width: number; height: number };

  setCenter(latlng: { lat: number; lng: number }): void;

  setZoom(zoom: number): void;

  zoomIn(): void;
  zoomOut(): void;

  world2plane(
    latlng: { lat: number; lng: number },
    level: number,
  ): { x: number; y: number };

  getBounds(): {
    west: number;
    north: number;
    east: number;
    south: number;
  };

  fitBounds(
    latlngs: { lat: number; lng: number }[],
    padding: {
      top: string;
      right: string;
      bottom: string;
      left: string;
    },
  ): void;

  isInBoundary(latlng: { lat: number; lng: number }): boolean;
}
