import React, { createContext, ReactNode } from 'react';

type MapSourceContextType = 'google' | 'logi';

const MapSourceContext = createContext<
  { source: MapSourceContextType } | undefined
>(undefined);

interface MapSourceProviderProps {
  source: MapSourceContextType;
  children: ReactNode;
}

const MapSourceProvider: React.FC<MapSourceProviderProps> = ({
  source,
  children,
}) => {
  return (
    <MapSourceContext.Provider value={{ source: source }}>
      {children}
    </MapSourceContext.Provider>
  );
};

const useMapSource = (): MapSourceContextType => {
  const context = React.useContext(MapSourceContext);
  if (!context) {
    throw new Error(
      'useMapSource must be used within a MapSourceContext.Provider',
    );
  }
  return context.source;
};

export { MapSourceProvider, useMapSource };
