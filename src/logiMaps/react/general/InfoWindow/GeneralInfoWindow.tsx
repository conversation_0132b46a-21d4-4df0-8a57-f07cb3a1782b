import { LogiInfoWindow } from '../../logi/InfoWindow';
import { GoogleInfoWindow } from '../../google/InfoWindow';
import { useMapSource } from '../Map';

export interface GeneralInfoWindowProps {
  id: string;
  className?: string;
  position: { lat: number; lng: number };
  pixelOffset?: [number, number];
  zIndex?: number;
  backgroundColor?: string;
  padding?: string;
  border?: string;
  borderRadius?: string;
  children?: React.ReactNode;
}

const GeneralInfoWindow: React.FC<GeneralInfoWindowProps> = (props) => {
  const mapSource = useMapSource();

  if (mapSource === 'logi') {
    return <LogiInfoWindow {...props} />;
  }

  if (mapSource === 'google') {
    return <GoogleInfoWindow {...props} />;
  }

  return null;
};

export default GeneralInfoWindow;
