import { useEffect, useMemo, useRef } from 'react';
import { useMap, useMapsLibrary } from '@vis.gl/react-google-maps';

type GoogleHeatmapProps = {
  data: { latlng: { lat: number; lng: number }; intensity: number }[];
  radius: number;
  opacity: number;
};
const GoogleHeatmap = ({ data, radius, opacity }: GoogleHeatmapProps) => {
  const googleMap = useMap();
  const visualization = useMapsLibrary('visualization');
  const dataMvcArray = useRef<
    google.maps.MVCArray<google.maps.visualization.WeightedLocation>
  >(new google.maps.MVCArray());

  const heatmap = useMemo(() => {
    if (!visualization) return null;
    return new google.maps.visualization.HeatmapLayer({
      radius: radius,
      opacity: opacity,
      //maxIntensity
    });
  }, [visualization, radius, opacity]);

  useEffect(() => {
    //if (!dataMvcArray.current) {
    //  dataMvcArray.current = new google.maps.MVCArray();
    //}

    dataMvcArray.current.clear();
    data.forEach((value) => {
      dataMvcArray.current?.push({
        location: new google.maps.LatLng(value.latlng.lat, value.latlng.lng),
        weight: value.intensity,
      });
    });

    if (heatmap) {
      heatmap.setData(dataMvcArray.current);
    }
  }, [heatmap, data, radius, opacity]);

  useEffect(() => {
    if (!heatmap) return;

    heatmap.setMap(googleMap);

    return () => {
      heatmap.setMap(null);
    };
  }, [heatmap, googleMap]);

  return null;
};

export default GoogleHeatmap;
