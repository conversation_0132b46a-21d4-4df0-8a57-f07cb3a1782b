import { useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { useMap } from '@vis.gl/react-google-maps';

type Props = {
  position: google.maps.LatLngLiteral;
  children: React.ReactNode;
};

export default function GoogleOverlay({ position, children }: Props) {
  const map = useMap();
  const overlayRef = useRef<google.maps.OverlayView>();
  const containerRef = useRef<HTMLDivElement>(document.createElement('div'));

  useEffect(() => {
    if (!map) return;

    containerRef.current.style.position = 'absolute';

    class ReactOverlay extends google.maps.OverlayView {
      onAdd() {
        const panes = this.getPanes();
        if (panes) {
          panes.floatPane.appendChild(containerRef.current);
        }
      }

      draw() {
        const projection = this.getProjection();
        if (!projection) return;

        const point = projection.fromLatLngToDivPixel(
          new google.maps.LatLng(position.lat, position.lng),
        );

        if (point) {
          const container = containerRef.current;
          container.style.left = `${point.x}px`;
          container.style.top = `${point.y}px`;
        }
      }

      onRemove() {
        const container = containerRef.current;
        if (container && container.parentNode) {
          container.parentNode.removeChild(container);
        }
      }
    }

    overlayRef.current = new ReactOverlay();
    overlayRef.current.setMap(map);

    return () => {
      overlayRef.current?.setMap(null);
    };
  }, [map, position.lat, position.lng]);

  // 포탈은 오직 1회 생성되며, containerRef.current를 타겟으로 고정
  return ReactDOM.createPortal(children, containerRef.current);
}
