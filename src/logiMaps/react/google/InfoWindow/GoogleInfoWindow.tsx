import React, { useEffect, useRef, useState } from 'react';
import GoogleOverlay from '../Overlay/GoogleOverlay';

interface GoogleInfoWindowProps {
  id?: string;
  className?: string;
  children?: React.ReactNode;
  position: { lat: number; lng: number };
  pixelOffset?: [number, number];
  zIndex?: number;
  backgroundColor?: string;
  padding?: string;
  border?: string;
  borderRadius?: string;
}

const GoogleInfoWindow: React.FC<GoogleInfoWindowProps> = ({
  id,
  className,
  children,
  position,
  pixelOffset,
  zIndex,
  backgroundColor,
  padding,
  border,
  borderRadius,
}) => {
  const infoWindowDivRef = useRef<HTMLDivElement>(null);
  const [finalId] = useState(
    () => `iw-${id ?? Math.random().toString(36).slice(-8)}`,
  );

  useEffect(() => {
    if (infoWindowDivRef.current) {
      const divElement = infoWindowDivRef.current;
      if (divElement) {
        // div 요소에 이벤트 리스너 등록
        divElement.addEventListener('touchstart', handleTouchEvent);
        divElement.addEventListener('touchmove', handleTouchEvent);
        divElement.addEventListener('touchend', handleTouchEvent);
        divElement.addEventListener('dblclick', handleMouseEvent);
        divElement.addEventListener('mousedown', handleMouseEvent);
        divElement.addEventListener('mouseup', handleMouseEvent);
        divElement.addEventListener('mousemove', handleMouseEvent);
        divElement.addEventListener('wheel', handleWheel);
        return () => {
          // 컴포넌트 언마운트 시 이벤트 리스너 정리
          divElement.removeEventListener('touchstart', handleTouchEvent);
          divElement.removeEventListener('touchmove', handleTouchEvent);
          divElement.removeEventListener('touchend', handleTouchEvent);
          divElement.removeEventListener('dblclick', handleMouseEvent);
          divElement.removeEventListener('mousedown', handleMouseEvent);
          divElement.removeEventListener('mouseup', handleMouseEvent);
          divElement.removeEventListener('mousemove', handleMouseEvent);
          divElement.removeEventListener('wheel', handleWheel);
        };
      }
    }
  }, [infoWindowDivRef.current]);

  const handleTouchEvent = (event: TouchEvent) => {
    event.stopPropagation(); // 이벤트 전파를 막음
  };

  const handleMouseEvent = (event: MouseEvent) => {
    event.stopPropagation(); // 이벤트 전파를 막음
  };

  const handleWheel = (event: WheelEvent) => {
    event.stopPropagation(); // 이벤트 전파를 막음
  };

  const handleClick = (event: React.MouseEvent) => {
    event.stopPropagation(); // 이벤트 전파를 막음
  };

  return (
    /** InfoWindow에서는 가장 상위 창을 변경할 수 없어서 GoogleOverlay로 대체 */
    // <InfoWindow
    //   className={props.className}
    //   //**Setting headerContent causes an error in the build version.
    //   headerDisabled={true}
    //   key={props.id}
    //   position={{ lat: props.position.lat, lng: props.position.lng }}
    //   pixelOffset={props.pixelOffset}
    //   zIndex={props.zIndex}
    // >
    //   {props.children}
    // </InfoWindow>

    <GoogleOverlay position={{ lat: position.lat, lng: position.lng }}>
      <div
        id={finalId}
        className={className}
        ref={infoWindowDivRef}
        onClick={handleClick}
        style={{
          zIndex: `${zIndex ?? 'auto'}`,
          backgroundColor: `${backgroundColor ?? 'white'}`,
          padding: `${padding ?? '10px'}`,
          boxShadow: '0px 0px 10px rgba(0, 0, 0, 0.2)',
          border: `${border ?? '1px solid #ccc'}`,
          borderRadius: `${borderRadius ?? '5px'}`,
          transform: `translate(-50%, -100%) translateX(${pixelOffset?.[0] ?? 0}px) translateY(${(pixelOffset?.[1] ?? 0) - 6}px)`, //- 6: 아래쪽 역삼각형 높이
        }}
      >
        <div
          style={{
            position: 'absolute',
            left: '50%',
            bottom: '-6px',
            transform: 'translateX(-50%)',
            width: '0',
            height: '0',
            borderLeft: '6px solid transparent',
            borderRight: '6px solid transparent',
            borderTop: `6px solid ${backgroundColor ?? '6px solid white'}`,
          }}
        />
        {children}
      </div>
    </GoogleOverlay>
  );
};

export default GoogleInfoWindow;
