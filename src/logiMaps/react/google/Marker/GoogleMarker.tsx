import { AdvancedMarker } from '@vis.gl/react-google-maps';

export interface GoogleMarkerProps {
  id: string;
  position: {
    lat: number;
    lng: number;
  };
  anchorPoint?: [string, string];
  zIndex?: number;
  children?: React.ReactNode;
  onClick?: (id: string, position: { lat: number; lng: number }) => void;
}

const GoogleMarker = (props: GoogleMarkerProps) => {
  const handleMarkerClick = () => {
    props.onClick?.(props.id, props.position);
  };

  return (
    <AdvancedMarker
      position={{ lat: props.position.lat, lng: props.position.lng }}
      anchorPoint={props.anchorPoint}
      zIndex={props.zIndex}
      onClick={() => {
        handleMarkerClick();
      }}
    >
      {props.children}
    </AdvancedMarker>
  );
};

export default GoogleMarker;
