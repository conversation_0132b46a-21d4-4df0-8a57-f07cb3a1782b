import { PolyPoint } from './PolyPoint';

export interface GooglePointProps {
  id?: string;
  className?: string;
  center: { lat: number; lng: number };
  pixelRadius: number;
  fillColor: string;
  fillOpacity: number;
  strokeColor?: string;
  strokeOpacity?: number;
  strokeWeight?: number;
  onClick?: () => void;
}

const GooglePoint = (props: GooglePointProps) => {
  return <PolyPoint {...props} />;
};

export default GooglePoint;
