import { PolyCircle } from './PolyCircle';

export interface GoogleCircleProps {
  id?: string;
  className?: string;
  center: { lat: number; lng: number };
  radius: number;
  fillColor: string;
  fillOpacity: number;
  strokeColor?: string;
  strokeOpacity?: number;
  strokeWeight?: number;
  onClick?: () => void;
}

const GoogleCircle = (props: GoogleCircleProps) => {
  return <PolyCircle {...props} />;
};

export default GoogleCircle;
