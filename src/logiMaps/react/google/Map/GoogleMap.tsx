import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Map,
  MapCameraChangedEvent,
  MapMouseEvent,
  useMap,
} from '@vis.gl/react-google-maps';
import { GeneralMapAdapter } from '../../general/Map/GeneralMapAdapter';
import { GoogleMapAdapter } from './GoogleMapAdapter';
import { useUserLocation } from '../../common/Location';

interface GoogleMapProps {
  id: string;
  className?: string;
  maxZoom: number;
  minZoom: number;
  defaultZoom: number;
  defaultCenter?: { lat: number; lng: number };
  children?: React.ReactNode;
  onInitMap?: (generalMapAdapter: GeneralMapAdapter) => void;
  onClick?: (event: MouseEvent) => void;
  onZoomChanged?: (zoom: number) => void;
  onSizeChanged?: (width: number, height: number) => void;
  onBoundsChanged?: (
    west: number,
    north: number,
    east: number,
    south: number,
  ) => void;
}

const GoogleMap: React.FC<GoogleMapProps> = ({
  children,
  className,
  id,
  maxZoom,
  minZoom,
  defaultZoom,
  defaultCenter,
  onInitMap,
  onClick,
  onZoomChanged,
  onSizeChanged,
  onBoundsChanged,
}) => {
  const { userLocation } = useUserLocation();
  const googleMap = useMap(id);
  const [isMapInit, setIsMapInit] = useState(false);
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapSizeRef = useRef<{ width: number; height: number }>({
    width: 0,
    height: 0,
  });
  // const [latlngBounds] = useState<google.maps.LatLngBoundsLiteral>({
  //   east: 180.0,
  //   north: 90.0,
  //   south: -90.0,
  //   west: -180.0,
  // });
  //const zoomLevelRef = useRef(defaultZoom);
  const handleInitMapRef = useRef<typeof onInitMap>();
  const handleClickRef = useRef<typeof onClick>();
  const handleZoomChangedRef = useRef<typeof onZoomChanged>();
  const handleSizeChangedRef = useRef<typeof onSizeChanged>();
  const handleBoundsChangedRef = useRef<typeof onBoundsChanged>();

  /** useEffect */

  useEffect(() => {
    handleInitMapRef.current = onInitMap;
  }, [onInitMap]);

  useEffect(() => {
    handleClickRef.current = onClick;
  }, [onClick]);

  useEffect(() => {
    handleZoomChangedRef.current = onZoomChanged;
  }, [onZoomChanged]);

  useEffect(() => {
    handleSizeChangedRef.current = onSizeChanged;
  }, [onSizeChanged]);

  useEffect(() => {
    handleBoundsChangedRef.current = onBoundsChanged;
  }, [onBoundsChanged]);

  //container div 크기 추적
  useEffect(() => {
    let resizeObserver: ResizeObserver;

    if (mapContainerRef.current) {
      resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (entry.target === mapContainerRef.current) {
            if (
              mapSizeRef.current.width != entry.contentRect.width ||
              mapSizeRef.current.height != entry.contentRect.height
            ) {
              mapSizeRef.current.width = entry.contentRect.width;
              mapSizeRef.current.height = entry.contentRect.height;
              handleSizeChangedRef.current?.(
                mapSizeRef.current.width,
                mapSizeRef.current.height,
              );
            }
          }
        }
      });

      resizeObserver.observe(mapContainerRef.current);
    }

    return () => {
      if (mapContainerRef.current && resizeObserver) {
        resizeObserver.unobserve(mapContainerRef.current);
      }
    };
  }, [mapContainerRef.current]);

  //default InfoWindow events 비활성화
  useEffect(() => {
    if (googleMap) {
      googleMap.addListener('click', (event: google.maps.MapMouseEvent) => {
        event.stop(); //To disable the default InfoWindow events
      });

      const checkMapInit = () => {
        if (googleMap) {
          const proj = googleMap.getProjection();
          if (proj) {
            setIsMapInit(true);
          } else {
            setTimeout(checkMapInit, 250);
          }
        }
      };
      checkMapInit();
    }
  }, [googleMap]);

  useEffect(() => {
    if (isMapInit == true && googleMap) {
      handleInitMapRef.current?.(new GoogleMapAdapter(googleMap));
    }
  }, [isMapInit]);

  /** Event Listener */

  const handleMapBoundsChanged = useCallback((event: MapCameraChangedEvent) => {
    handleBoundsChangedRef.current?.(
      event.detail.bounds.west,
      event.detail.bounds.north,
      event.detail.bounds.east,
      event.detail.bounds.south,
    );
  }, []);

  const handleMapZoomChanged = useCallback((event: MapCameraChangedEvent) => {
    //if (event.detail.zoom !== undefined) {
    //  zoomLevelRef.current = event.detail.zoom;
    //}
    handleZoomChangedRef.current?.(event.detail.zoom);
  }, []);

  const handleClick = useCallback((event: MapMouseEvent) => {
    handleClickRef.current?.(event.domEvent as MouseEvent);
  }, []);

  return (
    <div ref={mapContainerRef} className={className ?? 'w-full h-full'}>
      {userLocation && (
        <Map
          className={className ?? 'w-full h-full'}
          id={id}
          mapId={id}
          disableDefaultUI={true}
          keyboardShortcuts={false}
          headingInteractionEnabled={false}
          tiltInteractionEnabled={false}
          maxZoom={maxZoom}
          minZoom={minZoom}
          defaultZoom={defaultZoom}
          defaultCenter={defaultCenter ?? userLocation}
          onClick={handleClick}
          onBoundsChanged={handleMapBoundsChanged}
          onZoomChanged={handleMapZoomChanged}
          renderingType={'VECTOR'}
        >
          {children}
        </Map>
      )}
    </div>
  );
};

export default GoogleMap;
