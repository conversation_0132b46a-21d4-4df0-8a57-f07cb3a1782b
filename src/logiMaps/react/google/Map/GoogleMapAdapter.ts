import { GeneralMapAdapter } from '../../general/Map/GeneralMapAdapter';

const POW_TABLE = [
  1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0, 128.0, 256.0, 512.0, 1024.0, 2048.0,
  4096.0, 8192.0, 16384.0, 32768.0, 65536.0, 131072.0, 262144.0, 524288.0,
  1048576.0, 2097152.0,
];

export class GoogleMapAdapter implements GeneralMapAdapter {
  constructor(private map: google.maps.Map) {}

  getScreenSize() {
    const mapDiv = this.map.getDiv();
    return { width: mapDiv.clientWidth, height: mapDiv.clientHeight };
  }

  setCenter(latlng: { lat: number; lng: number }) {
    this.map.setCenter(latlng);
  }

  setZoom(zoom: number) {
    this.map.setZoom(zoom);
  }

  zoomIn() {
    const currLevel = this.map.getZoom();
    if (currLevel) {
      this.map.setZoom(currLevel + 1);
    }
  }

  zoomOut() {
    const currLevel = this.map.getZoom();
    if (currLevel) {
      this.map.setZoom(currLevel - 1);
    }
  }

  world2screen(
    planeBounds: { east: number; north: number; south: number; west: number },
    zoomLevel: number,
    latlng: { lat: number; lng: number },
  ) {
    const projection = this.map.getProjection();
    if (projection) {
      const planePoint = projection.fromLatLngToPoint(latlng);
      if (planePoint) {
        const boundsRatio = this.#getBoundsRatio(zoomLevel);
        const screenX = (planePoint.x - planeBounds.west) * boundsRatio;
        const screenY = (planePoint.y - planeBounds.north) * boundsRatio;
        return { x: screenX, y: screenY };
      }
    }
    return { x: 0, y: 0 };
  }

  world2plane(latlng: { lat: number; lng: number }, level: number) {
    const projection = this.map.getProjection();
    if (projection) {
      const planePoint = projection.fromLatLngToPoint(latlng);
      if (planePoint) {
        const boundsRatio = this.#getBoundsRatio(level);
        const ratioPlaneX = planePoint.x * boundsRatio;
        const ratioPlaneY = planePoint.y * boundsRatio;
        return { x: ratioPlaneX, y: ratioPlaneY };
      }
    }
    return { x: 0, y: 0 };
  }

  getBounds() {
    const result = {
      east: 180.0,
      north: 90.0,
      south: -90.0,
      west: -180.0,
    };
    const bounds = this.map.getBounds();
    if (bounds) {
      const ne = bounds.getNorthEast();
      const sw = bounds.getSouthWest();

      result.west = sw.lng();
      result.north = ne.lat();
      result.east = ne.lng();
      result.south = sw.lat();
    }
    return result;
  }

  fitBounds(
    latlngs: { lat: number; lng: number }[],
    padding: {
      top: string;
      right: string;
      bottom: string;
      left: string;
    },
  ) {
    function parsePaddingValue(
      value: string,
      relativeTo: number, // % 계산할 기준값 (width, height 등)
    ): number {
      if (value.endsWith('%')) {
        return (parseFloat(value) / 100.0) * relativeTo;
      }

      if (value.endsWith('px')) {
        return parseFloat(value);
      }

      return parseFloat(value);
    }

    const bounds = new google.maps.LatLngBounds();
    for (const latlng of latlngs) {
      bounds.extend(latlng);
    }

    const { width, height } = this.getScreenSize();
    const top = parsePaddingValue(padding.top, height * 0.5);
    const right = parsePaddingValue(padding.right, width * 0.5);
    const bottom = parsePaddingValue(padding.bottom, height * 0.5);
    const left = parsePaddingValue(padding.left, width * 0.5);

    this.map.fitBounds(bounds, { top, right, bottom, left });
  }

  isInBoundary(latlng: { lat: number; lng: number }) {
    const latlngBounds = this.getBounds();
    if (latlngBounds.west <= latlngBounds.east) {
      if (
        latlng.lat >= latlngBounds.south &&
        latlng.lat <= latlngBounds.north
      ) {
        if (
          latlng.lng >= latlngBounds.west &&
          latlng.lng <= latlngBounds.east
        ) {
          return true;
        }
      }
    } else {
      if (
        latlng.lat >= latlngBounds.south &&
        latlng.lat <= latlngBounds.north
      ) {
        if (
          (latlng.lng >= latlngBounds.west && latlng.lng <= 180.0) ||
          (latlng.lng <= latlngBounds.east && latlng.lng >= -180.0)
        ) {
          return true;
        }
      }
    }
    return false;
  }

  #getBoundsRatio(level: number) {
    const intLevel = Math.floor(level);
    const ratio = POW_TABLE[intLevel];
    const remain = level - intLevel;
    if (remain > 0) {
      return ratio + POW_TABLE[intLevel] * remain;
    } else {
      return ratio;
    }
  }
}
