import React, { createContext, useState, useEffect, ReactNode } from 'react';

// 위치 정보 타입 정의
interface UserLocation {
  lat: number;
  lng: number;
}

// Context 상태와 업데이트 함수 타입
interface UserLocationContextType {
  userLocation: UserLocation | null;
  setUserLocation: React.Dispatch<React.SetStateAction<UserLocation | null>>;
}

// 초기 위치 값을 null로 설정
const UserLocationContext = createContext<UserLocationContextType | undefined>(
  undefined,
);

interface UserLocationProviderProps {
  children: ReactNode;
}

const UserLocationProvider: React.FC<UserLocationProviderProps> = ({
  children,
}) => {
  const [userLocation, setUserLocation] = useState<UserLocation | null>(null);

  useEffect(() => {
    // 사용자의 위치를 받아오는 함수
    const getUserLocation = () => {
      const defaultLocation = { lat: 37.542737, lng: 127.044839 };
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const { latitude, longitude } = position.coords;
            setUserLocation({ lat: latitude, lng: longitude });
          },
          (error) => {
            setUserLocation(defaultLocation);
            console.error('Failed to get the location.', error);
          },
        );
      } else {
        setUserLocation(defaultLocation);
        console.error('navigator.geolocation is null.');
      }
    };

    getUserLocation(); // 페이지 로드 시 위치 정보 가져오기
  }, []);

  return (
    <UserLocationContext.Provider value={{ userLocation, setUserLocation }}>
      {children}
    </UserLocationContext.Provider>
  );
};

const useUserLocation = (): UserLocationContextType => {
  const context = React.useContext(UserLocationContext);
  if (!context) {
    throw new Error(
      'useUserLocation must be used within a UserLocationProvider',
    );
  }
  return context;
};

export { UserLocationProvider, useUserLocation };
