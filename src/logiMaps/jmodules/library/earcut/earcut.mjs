/**
 * Bundled by jsDelivr using Rollup v2.79.2 and Terser v5.37.0.
 * Original file: /npm/earcut@3.0.1/src/earcut.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
function t(t,n,r=2){const i=n&&n.length,o=i?n[0]*r:t.length;let u=e(t,0,o,r,!0);const y=[];if(!u||u.next===u.prev)return y;let c,v,s;if(i&&(u=function(t,n,x,r){const i=[];for(let x=0,o=n.length;x<o;x++){const u=e(t,n[x]*r,x<o-1?n[x+1]*r:t.length,r,!1);u===u.next&&(u.steiner=!0),i.push(p(u))}i.sort(l);for(let t=0;t<i.length;t++)x=f(i[t],x);return x}(t,n,u,r)),t.length>80*r){c=1/0,v=1/0;let e=-1/0,n=-1/0;for(let x=r;x<o;x+=r){const r=t[x],i=t[x+1];r<c&&(c=r),i<v&&(v=i),r>e&&(e=r),i>n&&(n=i)}s=Math.max(e-c,n-v),s=0!==s?32767/s:0}return x(u,y,r,c,v,s,0),y}function e(t,e,n,x,r){let i;if(r===q(t,e,n,x)>0)for(let r=e;r<n;r+=x)i=z(r/x|0,t[r],t[r+1],i);else for(let r=n-x;r>=e;r-=x)i=z(r/x|0,t[r],t[r+1],i);return i&&Z(i,i.next)&&(b(i),i=i.next),i}function n(t,e){if(!t)return t;e||(e=t);let n,x=t;do{if(n=!1,x.steiner||!Z(x,x.next)&&0!==a(x.prev,x,x.next))x=x.next;else{if(b(x),x=e=x.prev,x===x.next)break;n=!0}}while(n||x!==e);return e}function x(t,e,l,f,y,p,v){if(!t)return;!v&&p&&function(t,e,n,x){let r=t;do{0===r.z&&(r.z=c(r.x,r.y,e,n,x)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next}while(r!==t);r.prevZ.nextZ=null,r.prevZ=null,function(t){let e,n=1;do{let x,r=t;t=null;let i=null;for(e=0;r;){e++;let o=r,u=0;for(let t=0;t<n&&(u++,o=o.nextZ,o);t++);let l=n;for(;u>0||l>0&&o;)0!==u&&(0===l||!o||r.z<=o.z)?(x=r,r=r.nextZ,u--):(x=o,o=o.nextZ,l--),i?i.nextZ=x:t=x,x.prevZ=i,i=x;r=o}i.nextZ=null,n*=2}while(e>1)}(r)}(t,f,y,p);let s=t;for(;t.prev!==t.next;){const c=t.prev,h=t.next;if(p?i(t,f,y,p):r(t))e.push(c.i,t.i,h.i),b(t),t=h.next,s=h.next;else if((t=h)===s){v?1===v?x(t=o(n(t),e),e,l,f,y,p,2):2===v&&u(t,e,l,f,y,p):x(n(t),e,l,f,y,p,1);break}}}function r(t){const e=t.prev,n=t,x=t.next;if(a(e,n,x)>=0)return!1;const r=e.x,i=n.x,o=x.x,u=e.y,l=n.y,f=x.y,y=Math.min(r,i,o),c=Math.min(u,l,f),p=Math.max(r,i,o),v=Math.max(u,l,f);let h=x.next;for(;h!==e;){if(h.x>=y&&h.x<=p&&h.y>=c&&h.y<=v&&s(r,u,i,l,o,f,h.x,h.y)&&a(h.prev,h,h.next)>=0)return!1;h=h.next}return!0}function i(t,e,n,x){const r=t.prev,i=t,o=t.next;if(a(r,i,o)>=0)return!1;const u=r.x,l=i.x,f=o.x,y=r.y,p=i.y,v=o.y,h=Math.min(u,l,f),Z=Math.min(y,p,v),M=Math.max(u,l,f),m=Math.max(y,p,v),d=c(h,Z,e,n,x),g=c(M,m,e,n,x);let w=t.prevZ,z=t.nextZ;for(;w&&w.z>=d&&z&&z.z<=g;){if(w.x>=h&&w.x<=M&&w.y>=Z&&w.y<=m&&w!==r&&w!==o&&s(u,y,l,p,f,v,w.x,w.y)&&a(w.prev,w,w.next)>=0)return!1;if(w=w.prevZ,z.x>=h&&z.x<=M&&z.y>=Z&&z.y<=m&&z!==r&&z!==o&&s(u,y,l,p,f,v,z.x,z.y)&&a(z.prev,z,z.next)>=0)return!1;z=z.nextZ}for(;w&&w.z>=d;){if(w.x>=h&&w.x<=M&&w.y>=Z&&w.y<=m&&w!==r&&w!==o&&s(u,y,l,p,f,v,w.x,w.y)&&a(w.prev,w,w.next)>=0)return!1;w=w.prevZ}for(;z&&z.z<=g;){if(z.x>=h&&z.x<=M&&z.y>=Z&&z.y<=m&&z!==r&&z!==o&&s(u,y,l,p,f,v,z.x,z.y)&&a(z.prev,z,z.next)>=0)return!1;z=z.nextZ}return!0}function o(t,e){let x=t;do{const n=x.prev,r=x.next.next;!Z(n,r)&&M(n,x,x.next,r)&&g(n,r)&&g(r,n)&&(e.push(n.i,x.i,r.i),b(x),b(x.next),x=t=r),x=x.next}while(x!==t);return n(x)}function u(t,e,r,i,o,u){let l=t;do{let t=l.next.next;for(;t!==l.prev;){if(l.i!==t.i&&h(l,t)){let f=w(l,t);return l=n(l,l.next),f=n(f,f.next),x(l,e,r,i,o,u,0),void x(f,e,r,i,o,u,0)}t=t.next}l=l.next}while(l!==t)}function l(t,e){let n=t.x-e.x;if(0===n&&(n=t.y-e.y,0===n)){n=(t.next.y-t.y)/(t.next.x-t.x)-(e.next.y-e.y)/(e.next.x-e.x)}return n}function f(t,e){const x=function(t,e){let n=e;const x=t.x,r=t.y;let i,o=-1/0;if(Z(t,n))return n;do{if(Z(t,n.next))return n.next;if(r<=n.y&&r>=n.next.y&&n.next.y!==n.y){const t=n.x+(r-n.y)*(n.next.x-n.x)/(n.next.y-n.y);if(t<=x&&t>o&&(o=t,i=n.x<n.next.x?n:n.next,t===x))return i}n=n.next}while(n!==e);if(!i)return null;const u=i,l=i.x,f=i.y;let c=1/0;n=i;do{if(x>=n.x&&n.x>=l&&x!==n.x&&v(r<f?x:o,r,l,f,r<f?o:x,r,n.x,n.y)){const e=Math.abs(r-n.y)/(x-n.x);g(n,t)&&(e<c||e===c&&(n.x>i.x||n.x===i.x&&y(i,n)))&&(i=n,c=e)}n=n.next}while(n!==u);return i}(t,e);if(!x)return e;const r=w(x,t);return n(r,r.next),n(x,x.next)}function y(t,e){return a(t.prev,t,e.prev)<0&&a(e.next,t,t.next)<0}function c(t,e,n,x,r){return(t=1431655765&((t=858993459&((t=252645135&((t=16711935&((t=(t-n)*r|0)|t<<8))|t<<4))|t<<2))|t<<1))|(e=1431655765&((e=858993459&((e=252645135&((e=16711935&((e=(e-x)*r|0)|e<<8))|e<<4))|e<<2))|e<<1))<<1}function p(t){let e=t,n=t;do{(e.x<n.x||e.x===n.x&&e.y<n.y)&&(n=e),e=e.next}while(e!==t);return n}function v(t,e,n,x,r,i,o,u){return(r-o)*(e-u)>=(t-o)*(i-u)&&(t-o)*(x-u)>=(n-o)*(e-u)&&(n-o)*(i-u)>=(r-o)*(x-u)}function s(t,e,n,x,r,i,o,u){return!(t===o&&e===u)&&v(t,e,n,x,r,i,o,u)}function h(t,e){return t.next.i!==e.i&&t.prev.i!==e.i&&!function(t,e){let n=t;do{if(n.i!==t.i&&n.next.i!==t.i&&n.i!==e.i&&n.next.i!==e.i&&M(n,n.next,t,e))return!0;n=n.next}while(n!==t);return!1}(t,e)&&(g(t,e)&&g(e,t)&&function(t,e){let n=t,x=!1;const r=(t.x+e.x)/2,i=(t.y+e.y)/2;do{n.y>i!=n.next.y>i&&n.next.y!==n.y&&r<(n.next.x-n.x)*(i-n.y)/(n.next.y-n.y)+n.x&&(x=!x),n=n.next}while(n!==t);return x}(t,e)&&(a(t.prev,t,e.prev)||a(t,e.prev,e))||Z(t,e)&&a(t.prev,t,t.next)>0&&a(e.prev,e,e.next)>0)}function a(t,e,n){return(e.y-t.y)*(n.x-e.x)-(e.x-t.x)*(n.y-e.y)}function Z(t,e){return t.x===e.x&&t.y===e.y}function M(t,e,n,x){const r=d(a(t,e,n)),i=d(a(t,e,x)),o=d(a(n,x,t)),u=d(a(n,x,e));return r!==i&&o!==u||(!(0!==r||!m(t,n,e))||(!(0!==i||!m(t,x,e))||(!(0!==o||!m(n,t,x))||!(0!==u||!m(n,e,x)))))}function m(t,e,n){return e.x<=Math.max(t.x,n.x)&&e.x>=Math.min(t.x,n.x)&&e.y<=Math.max(t.y,n.y)&&e.y>=Math.min(t.y,n.y)}function d(t){return t>0?1:t<0?-1:0}function g(t,e){return a(t.prev,t,t.next)<0?a(t,e,t.next)>=0&&a(t,t.prev,e)>=0:a(t,e,t.prev)<0||a(t,t.next,e)<0}function w(t,e){const n=k(t.i,t.x,t.y),x=k(e.i,e.x,e.y),r=t.next,i=e.prev;return t.next=e,e.prev=t,n.next=r,r.prev=n,x.next=n,n.prev=x,i.next=x,x.prev=i,x}function z(t,e,n,x){const r=k(t,e,n);return x?(r.next=x.next,r.prev=x,x.next.prev=r,x.next=r):(r.prev=r,r.next=r),r}function b(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function k(t,e,n){return{i:t,x:e,y:n,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}function j(t,e,n,x){const r=e&&e.length,i=r?e[0]*n:t.length;let o=Math.abs(q(t,0,i,n));if(r)for(let x=0,r=e.length;x<r;x++){const i=e[x]*n,u=x<r-1?e[x+1]*n:t.length;o-=Math.abs(q(t,i,u,n))}let u=0;for(let e=0;e<x.length;e+=3){const r=x[e]*n,i=x[e+1]*n,o=x[e+2]*n;u+=Math.abs((t[r]-t[o])*(t[i+1]-t[r+1])-(t[r]-t[i])*(t[o+1]-t[r+1]))}return 0===o&&0===u?0:Math.abs((u-o)/o)}function q(t,e,n,x){let r=0;for(let i=e,o=n-x;i<n;i+=x)r+=(t[o]-t[i])*(t[i+1]+t[o+1]),o=i;return r}function A(t){const e=[],n=[],x=t[0][0].length;let r=0,i=0;for(const o of t){for(const t of o)for(let n=0;n<x;n++)e.push(t[n]);i&&(r+=i,n.push(r)),i=o.length}return{vertices:e,holes:n,dimensions:x}}export{t as default,j as deviation,A as flatten};
