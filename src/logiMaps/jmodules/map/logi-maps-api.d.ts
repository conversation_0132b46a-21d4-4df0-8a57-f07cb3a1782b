export interface BaseLineOptions {
  key?: string;
  class?: string;
  zIndex?: number;
  color?: string;
  strokeWidth?: number;
  strokeColor?: string;
}

export interface StraightLineOptions extends BaseLineOptions {
  width?: number;
}

export interface PolyLineOptions extends BaseLineOptions {
  width?: number;
}

export interface DottedLineOptions extends BaseLineOptions {
  dotRadius?: number;
  dotGap?: number;
}

export interface DashedLineOptions extends BaseLineOptions {
  width?: number;
  dashLength?: number;
  dashSpace?: number;
}

export type LineOptions =
  | StraightLineOptions
  | PolyLineOptions
  | DottedLineOptions
  | DashedLineOptions;


export namespace logi.maps {
  export class LatLng {
    constructor(latitude: number, longitude: number);
    lat: number;
    lng: number;
  }
  export class Point {
    constructor(x: number, y: number);
    x: number;
    y: number;
  }
  export class Map {
    constructor(mapDivId: string, options: any);
    getScreenSize(): {width: number, height: number};
    setCenter(center: { lat: number, lng: number }): void;
    setZoom(zoom: number): void;
    getZoom(): number;
    zoomIn(): void;
    zoomOut(): void;
    addEventListener(eventName: string, func: function(event)): void;
    removeEventListener(eventName: string, func: function(event)): void;
    setOnLevelChange(func: function(event)): void;
    world2screen(latlng: {lat: number, lng: number}, level?: number): {x: number, y: number};
    screen2world(point: {x: number, y: number}, level?: number): {lat: number, lng: number};
    world2plane(latlng: {lat: number, lng: number}, level?: number): {x: number, y: number};
    getBounds(): {left: number, top: number, bottom: number, right: number};
    fitBounds(bounds: logi.maps.Bounds, padding: { top: number, right: number, bottom: number, left: number }): void;
    setHeatmap(data: {latlng: {lat: number, lng: number}, intensity: numer}[], radius: number | null | undefined, opacity: number | null | undefined): void;
  }
  export class Circle {
    constructor(center: {lat: number, lng: number}, radius: number, fillColor: string, options?: {lineWidth: number, lineColor: string, radiusUnit: string});
    setCenter(center: {lat: number, lng: number}): void;
    setRadius(radius: number, unit: string): void;
    setFillColor(fillColor: string): void;
    setLineProperty(lineWidth: number, lineColor: string): void;
    setMap(map: logi.maps.Map | null): void;
  }
  export class Line {
    constructor();
    setLineProperty(lineType: string, option: LineOptions): void;
    setLatLngs(latlngs: {lat: number, lng: number}[]): void
    setMap(map: logi.maps.Map | null): void;
  }
  export class Meta {
    constructor(refId: string);
    setMap(map: logi.maps.Map | null): void;
  }
  export class Bounds {
    constructor();
    extend({ lat: number, lng: number }): void;
  }
}

export default logi;
