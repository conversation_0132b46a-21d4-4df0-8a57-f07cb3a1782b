import a2_0x11a995 from '../config/lbsconfig.js?v=2.1.10.1';
import a2_0x302a7a from '../common/logi-maps-defines.js?v=2.1.10.1';
import a2_0x16be4b from '../common/logi-maps-types.js?v=2.1.10.1';
import a2_0x29adb8 from '../resource/logi-maps-resource.js?v=2.1.10.1';
import a2_0x30760b from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a2_0x480e33 from '../utility/logi-maps-bridge.js?v=2.1.10.1';
import a2_0x265c36 from '../object/logi-maps-object.js?v=2.1.10.1';
import a2_0xd4bebd from '../layer/logi-maps-mtlayer.js?v=2.1.10.1';
import a2_0x168c09 from '../layer/logi-maps-vtlayer.js?v=2.1.10.1';
import a2_0x3ec74a from '../layer/logi-maps-objlayer.js?v=2.1.10.1';
import a2_0x28e804 from '../layer/logi-maps-heatmaplayer.js?v=2.1.10.1';
import a2_0x3af6c6 from '../layer/logi-maps-overlaylayer.js?v=2.1.10.1';
import a2_0x2de4fe from '../layer/logi-maps-uilayer.js?v=2.1.10.1';
import a2_0x41fbe8 from '../map/logi-maps-coord.js?v=2.1.10.1';
import a2_0x541234 from '../graphics/logi-maps-gfx2d.js?v=2.1.10.1';
import a2_0x1dd00d from '../graphics/logi-maps-gfxgl.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Config'] = a2_0x11a995, logi['maps']['Defines'] = a2_0x302a7a, logi['maps']['Size'] = a2_0x16be4b['Size'], logi['maps']['LatLngBound'] = a2_0x16be4b['LatLngBound'], logi['maps']['BRIDGE_MAPEVENT'] = a2_0x16be4b['BRIDGE_MAPEVENT'], logi['maps']['DISTRICT_VISIBLETYPE'] = a2_0x16be4b['DISTRICT_VISIBLETYPE'], logi['maps']['DISTRICT_DATATYPE'] = a2_0x16be4b['DISTRICT_DATATYPE'], logi['maps']['EVENT'] = a2_0x16be4b['EVENT'], logi['maps']['Resource'] = a2_0x29adb8, logi['maps']['Utils'] = a2_0x30760b, logi['maps']['Bridge'] = a2_0x480e33, logi['maps']['Object'] = a2_0x265c36, logi['maps']['MtLayer'] = a2_0xd4bebd, logi['maps']['VtLayer'] = a2_0x168c09, logi['maps']['ObjLayer'] = a2_0x3ec74a, logi['maps']['HeatmapLayer'] = a2_0x28e804, logi['maps']['OverlayLayer'] = a2_0x3af6c6, logi['maps']['UiLayer'] = a2_0x2de4fe, logi['maps']['Coord'] = a2_0x41fbe8, logi['maps']['Gfx2d'] = a2_0x541234, logi['maps']['Gfxgl'] = a2_0x1dd00d, logi['maps']['Map'] = class {
    #mapCoord;
    #mapDiv;
    #mapScreenSize = new logi['maps']['Size']();
    #devicePixelRatio = 0x1;
    #resourceManager;
    #loopRenderId = 0x0;
    #currentFps = 0x0;
    #lastRenderTick = 0x0;
    #delayRender = {
        'cnt': 0x0,
        'max': 0x2
    };
    #freezeModeOnMoving = !![];
    #canvasWorldPosition = {
        'lng': 0x0,
        'lat': 0x0
    };
    #isMobileEnv = ![];
    #extn = logi['maps']['Defines']['MAP_TILE_EXT_PNG'];
    #supportGL;
    #moveAnims = [];
    #remoteServerUrl = '';
    #remoteVectorUrl = '';
    #isDragging = ![];
    #multiTouchActive = ![];
    #touchSingleDownStatus = ![];
    #touchStartPoint = {
        'x': 0x0,
        'y': 0x0
    };
    #touchPrevPoints = [];
    #touchPrevPoint = {
        'x': 0x0,
        'y': 0x0
    };
    #mouseLButtonDownStatus = ![];
    #mousePrevPoint = {
        'x': 0x0,
        'y': 0x0
    };
    #motionEventLock = ![];
    #wheelEventCheck = !![];
    #lastWheelTime = 0x0;
    #bridgeMapEvents = {};
    #mapLayers = [];
    #mtLayer = null;
    #vtLayer = null;
    #objLayer = null;
    #heatmapLayer = null;
    #uiLayer = null;
    #overlayLayer = null;
    #eventLayer = null;
    /**
   * @preserve .
   * @constructor
   * @description
   *  Map을 초기화 및 생성한다.
   * @param {String} mapDivId root Div ID
   * @param {Object} options option
   *  @param {function(event)} options.OnDoubleClick 더블클릭 이벤트 콜백 함수
   *  @param {function(event)} options.OnClick 클릭 이벤트 콜백 함수
   *  @param {function(event)} options.OnMouseDown 마우스 다운 이벤트 콜백 함수
   *  @param {function(event)} options.OnMouseUp 마우스 업 이벤트 콜백 함수
   *  @param {function(event)} options.OnMouseMove 마우스 이동 이벤트 콜백 함수
   *  @param {function(event)} options.OnMapWheel 마우스 휠 이벤트 콜백 함수
   *  @param {function()} options.OnDraw 맵 그리기 이벤트 콜백 함수
   *  @param {Object} options.levelRange 맵 레벨 범위 (default: {8, 18})
   *   @param {Number} options.levelRange.min 월드 레벨
   *   @param {Number} options.levelRange.max 디테일 레벨
   *  @param {logi.maps.LatLng} options.center 맵 위치
   *  @param {Number} options.level 맵 레벨
   *  @param {String} options.extn 맵 타입 (png, xvg ...)
   *  @param {String} options.region 국가 (kor, nam ...)
   *  @param {String} options.theme 리소스 테마명
   *  @param {String} options.serverUrl 맵 서버 주소
   *  @param {String} options.vectorUrl 벡터 서버 주소
   *  @param {{overlay:String, event:String}} options.customDivId 사용자 Div ID 지정
   * @example
   *  let logiMap = new logi.maps.Map('div_canvas', {center: {lat:37.542760, lng: 127.044996}, level: 16});
   *  //지도가 서울숲역으로 이동하여 level 16 축적으로 div_canvas 영역에 그린다.
   */
    constructor(_0x456af1, _0x20907d) {
        const _0x2512d3 = 0x0;
        this.#loadServerUrl(_0x20907d?.['serverUrl']), this.#loadVectorUrl(_0x20907d?.['vectorUrl']), this.#extn = logi['maps']['Defines']['MAP_TILE_EXT_PNG'];
        if (_0x2512d3 == 0x0)
            switch (_0x20907d?.['extn']) {
            case logi['maps']['Defines']['MAP_TILE_EXT_PNG']:
            case logi['maps']['Defines']['MAP_TILE_EXT_XVG']:
            case logi['maps']['Defines']['MAP_TILE_EXT_BIN']:
                this.#extn = _0x20907d?.['extn'];
                break;
            default:
                this.#extn = logi['maps']['Defines']['MAP_TILE_EXT_PNG'];
            }
        const _0x474d64 = _0x20907d?.['region'] ?? logi['maps']['Defines']['REGION_KOR'];
        console['log']('[logi.maps]\x20extn:\x20' + this.#extn), console['log']('[logi.maps]\x20region:\x20' + _0x474d64);
        const _0x29ea9e = [
            'all',
            'default',
            'none'
        ];
        this.#supportGL = _0x20907d?.['supportGL'] ?? 'default';
        !_0x29ea9e['includes'](this.#supportGL) && (this.#supportGL = 'default');
        this.#supportGL != 'none' && logi['maps']['Utils']['isWebGlAvailable']() == ![] && (this.#supportGL = 'none');
        this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG'] && (this.#supportGL = 'none');
        console['log']('[logi.maps]\x20webgl:\x20' + this.#supportGL), this.#resourceManager = new logi['maps']['Resource'](this.#remoteServerUrl, this.#extn, _0x474d64, _0x20907d?.['theme']);
        const _0x16fa31 = this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG'] ? 'flat' : 'smooth';
        this.#mapCoord = new logi['maps']['Coord'](_0x474d64, _0x20907d?.['levelRange'], { 'tileLevelOffsetType': _0x16fa31 }), this.#mapCoord['setOnZoomChange'](this.#handleZoomChange['bind'](this)), this.#mapCoord['setOnBoundsChange'](this.#handleBoundsChange['bind'](this)), this.#mapDiv = document['getElementById'](_0x456af1), this.#mapDiv['addEventListener']('resize', () => {
            this['resize']();
        }), this.#mtLayer = new logi['maps']['MtLayer']('parent', this.#mapDiv, this.#mapCoord, this.#resourceManager, this.#remoteServerUrl, this.#extn, _0x474d64, _0x2512d3, this.#supportGL), this.#mapLayers['push'](this.#mtLayer), this.#vtLayer = new logi['maps']['VtLayer']('parent', this.#mapDiv, this.#mapCoord, this.#remoteVectorUrl), this.#mapLayers['push'](this.#vtLayer), this.#objLayer = new logi['maps']['ObjLayer']('parent', this.#mapDiv, this.#mapCoord), this.#mapLayers['push'](this.#objLayer), this.#heatmapLayer = new logi['maps']['HeatmapLayer']('parent', this.#mapDiv, this.#mapCoord), this.#mapLayers['push'](this.#heatmapLayer), this.#uiLayer = new logi['maps']['UiLayer']('parent', this.#mapDiv, this.#mapCoord), this.#mapLayers['push'](this.#uiLayer);
        if (_0x20907d?.['customDivId']?.['overlay']) {
            const _0x4a6fdf = document['getElementById'](_0x20907d?.['customDivId']['overlay']);
            this.#overlayLayer = new logi['maps']['OverlayLayer']('target', _0x4a6fdf, this.#mapCoord), this.#mapLayers['push'](this.#overlayLayer);
        } else
            this.#overlayLayer = new logi['maps']['OverlayLayer']('parent', this.#mapDiv, this.#mapCoord), this.#mapLayers['push'](this.#overlayLayer);
        _0x20907d?.['customDivId']?.['event'] ? this.#eventLayer = document['getElementById'](_0x20907d?.['customDivId']['event']) : this.#eventLayer = this.#mapDiv;
        this.#checkUseMobile() && (this.#isMobileEnv = !![]);
        this['OnDoubleClick'] = _0x20907d?.['OnDoubleClick'], this['OnClick'] = _0x20907d?.['OnClick'], this['OnMouseDown'] = _0x20907d?.['OnMouseDown'], this['OnMouseUp'] = _0x20907d?.['OnMouseUp'], this['OnMouseMove'] = _0x20907d?.['OnMouseMove'], this['OnMapWheel'] = _0x20907d?.['OnMapWheel'], this['OnDraw'] = _0x20907d?.['OnDraw'], this.#bridgeMapEvents = {};
        for (const _0x38bf3a in logi['maps']['BRIDGE_MAPEVENT']) {
            this.#bridgeMapEvents[_0x38bf3a] = ![];
        }
        this['resize'](), this.#initEvent(), this.#setRenderFrame(logi['maps']['Defines']['FPS_MEDIUM']), _0x20907d?.['level'] && this['setLevel'](_0x20907d['level']), _0x20907d?.['center'] && this['setCenter'](_0x20907d['center']);
    }
    #handleZoomChange(_0x3c09d4) {
        this.#eventLayer['dispatchEvent'](new CustomEvent('_zoom', { 'detail': { 'zoom': _0x3c09d4 } }));
    }
    #handleBoundsChange(_0x3c7a9f) {
        this.#eventLayer['dispatchEvent'](new CustomEvent('_bounds', {
            'detail': {
                'west': _0x3c7a9f['west'],
                'north': _0x3c7a9f['north'],
                'east': _0x3c7a9f['east'],
                'south': _0x3c7a9f['south']
            }
        }));
    }
    ['setDebugMode'](_0x10d42c) {
        logi['maps']['Defines']['DEBUG_MODE'] = _0x10d42c;
    }
    ['setLogMode'](_0x5e3090) {
        logi['maps']['Defines']['LOG_MODE'] = _0x5e3090;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - 지도 스케일 범위를 변경 할 수 있다.
   * @param {Number} min 월드 레벨
   * @param {Number} max 디테일 레벨
   * @example
   *  logiMap.setLevelRange(8, 18);
   *  //지도 스케일 범위가 8 ~ 18로 설정된다.
   */
    ['setLevelRange'](_0x4da4ba, _0x27f48d) {
        this.#mapCoord['setLevelRange'](_0x4da4ba, _0x27f48d), this['updateMap']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - SVG(XVG) 맵 타일을 사용할 경우 맵 테마를 사용 할 수 있다.
   *  - resource/region/theme 이름 순으로 폴더가 구성 된다.
   * @param {String} name 테마명
   * @example
   *  logiMap.setTheme(“aloa”);
   *  //‘aloa’ 폴더 아래에 작성된 layout으로 그려진다.
   */
    async ['setTheme'](_0x1f0ec1) {
        if (_0x1f0ec1) {
            if (this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG'])
                console['log']('[logi.maps]\x20Image\x20map\x20tiles\x20do\x20not\x20support\x20this\x20function.');
            else
                try {
                    await this.#resourceManager['setTheme'](_0x1f0ec1);
                } catch (_0x45fb85) {
                    console['log']('[logi.maps]\x20setTheme\x20failed.\x20(' + _0x45fb85 + ')');
                }
        }
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - SVG(XVG) 맵 타일을 사용할 경우 맵 테마를 사용 할 수 있다.
   *  - resource/region/theme 이름 순으로 폴더가 구성 된다.
   * @returns {String} 테마명
   * @example
   *  let themeName = logiMap.getTheme();
   *  //themeName에 핸재 적용된 테마명이 리턴된다
   */
    ['getTheme']() {
        return this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG'] ? (console['log']('[logi.maps]\x20Image\x20map\x20tiles\x20do\x20not\x20support\x20this\x20function.'), null) : this.#resourceManager['getTheme']();
    }
    ['importStyle'](_0x2466e6) {
        if (this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG'])
            console['log']('[logi.maps]\x20Image\x20map\x20tiles\x20do\x20not\x20support\x20this\x20function.');
        else {
            const _0x32da05 = new FileReader();
            _0x32da05['onload'] = _0x2698fa => {
                const _0x59580b = _0x2698fa['target']['result'];
                this.#resourceManager['setUserStyle'](_0x59580b)['then'](() => {
                })['catch'](_0x210fcc => {
                    console['log']('[logi.maps]\x20setUserStyle\x20failed.\x20(' + _0x210fcc + ')');
                });
            }, _0x32da05['readAsText'](_0x2466e6);
        }
    }
    ['exportStyle'](_0x2b3688 = 'map_style.xml') {
        if (this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG'])
            console['log']('[logi.maps]\x20Image\x20map\x20tiles\x20do\x20not\x20support\x20this\x20function.');
        else {
            const _0x4e996b = this.#resourceManager['getCurrStyle']() ?? '', _0x125b14 = new Blob([_0x4e996b], { 'type': 'application/xml' }), _0x4fe76f = URL['createObjectURL'](_0x125b14), _0x31293d = document['createElement']('a');
            {
                _0x31293d['href'] = _0x4fe76f, _0x31293d['download'] = _0x2b3688, document['body']['appendChild'](_0x31293d), _0x31293d['click'](), document['body']['removeChild'](_0x31293d);
            }
        }
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - SVG(XVG) 맵 타일을 사용할 경우 맵 레이어 On/Off를 사용 할 수 있다.
   * @param {String} 레이어명
   * @param {Object} options 옵션
   *  @param {String} options.codeType 코드 타입 ('category' | 'poi')
   *  @param {String[]} options.codeList 코드 리스트 (카테고리 ID 또는 POI 코드의 배열)
   * @example
   *  logiMap.showLayer('rwy');
   *  //철도 라인이 그려진다.
   *  logiMap.showLayer('poi', {codeType: 'category', codeList: ['11']});
   *  //POI 심볼 중에서 카테고리 11(건물, 일반 기업)이 그려진다.
   *  logiMap.showLayer('poitext', {codeType: 'poi', codeList: ['92', '95']});
   *  //POI 글자 중에서 POI 코드 92XXXXXXX(판매업), 95XXXXXXX(생산업) 관련이 그려진다.
   */
    ['showLayer'](_0x87bf63, _0x2ba449) {
        this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG'] ? console['log']('[logi.maps]\x20Image\x20map\x20tiles\x20do\x20not\x20support\x20this\x20function.') : this.#resourceManager['showLayer'](_0x87bf63, _0x2ba449);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - SVG(XVG) 맵 타일을 사용할 경우 맵 레이어 On/Off를 사용 할 수 있다.
   * @param {String} 레이어명
   * @param {Object} options 옵션
   *  @param {String} options.codeType 코드 타입 ('category' | 'poi')
   *  @param {String[]} options.codeList 코드 리스트 (카테고리 ID 또는 POI 코드의 배열)
   * @example
   *  logiMap.hideLayer('rwy');
   *  //철도 라인이 안 그려진다.
   *  logiMap.hideLayer('poi', {codeType: 'category', codeList: ['11']});
   *  //POI 심볼 중에서 카테고리 11(건물, 일반 기업)이 안 그려진다.
   *  logiMap.hideLayer('poitext', {codeType: 'poi', codeList: ['92', '95']});
   *  //POI 글자 중에서 POI 코드 92XXXXXXX(판매업), 95XXXXXXX(생산업) 관련이 안 그려진다.
   */
    ['hideLayer'](_0x26121a, _0x1df21b) {
        this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG'] ? console['log']('[logi.maps]\x20Image\x20map\x20tiles\x20do\x20not\x20support\x20this\x20function.') : this.#resourceManager['hideLayer'](_0x26121a, _0x1df21b);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  사용할 폰트들을 등록한다.
   * @param {String} family 폰트패밀리
   * @param {String} source 경로
   * @example
   *  logiMap.addFont(“LogiFont”, “url(/font/XXXKR-Medium.ttf)”);
   *  //XXXKR-Medium.ttf가 ‘LogiFont’ 명으로 추가된다.
   */
    ['addFont'](_0x2bedd2, _0x4d987e) {
        this.#resourceManager['addFont'](_0x2bedd2, _0x4d987e);
    }
    ['getScreenSize']() {
        return {
            'width': this.#mapScreenSize['width'],
            'height': this.#mapScreenSize['height']
        };
    }
    ['resize']() {
        const _0x13a135 = this.#getWindowDevicePixelRatio();
        this.#setScreenSize({
            'width': this.#mapDiv['clientWidth'],
            'height': this.#mapDiv['clientHeight']
        }, _0x13a135), this.#eventLayer['dispatchEvent'](new CustomEvent('_resize', {
            'detail': {
                'width': this.#mapDiv['clientWidth'],
                'heigth': this.#mapDiv['clientHeight']
            }
        }));
    }
    ['getMapCoord']() {
        return this.#mapCoord;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  마우스(터치) 이벤트를 활성화 또는 비활성화한다.
   * @param {Boolean} eventLock 활성화 여부
   * @example
   *  logiMap.setMotionEventLock(true);
   *  //마우스(터치) 이벤트가 비활성화된다.
   */
    ['setMotionEventLock'](_0x3d3c8f) {
        this.#motionEventLock = _0x3d3c8f;
    }
    ['getMotionEventLock']() {
        return this.#motionEventLock;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  마우스 휠 이벤트를 활성화한다.
   * @example
   *  logiMap.enableWheelEvent();
   *  //마우스 휠에 따라 지도 레벨이 변경된다.
   */
    ['enableWheelEvent']() {
        this.#wheelEventCheck = !![];
    }
    /**
   * @preserve .
   * @method
   * @description
   *  마우스 휠 이벤트를 비활성화한다.
   * @example
   *  logiMap.disableWheelEvent();
   *  //마우스 휠에 따라 지도 레벨이 변경되지 않는다.
   */
    ['disableWheelEvent']() {
        this.#wheelEventCheck = ![];
    }
    /**
   * @preserve .
   * @method
   * @description
   *  마우스를 드래그하면 화면에 사각 영역을 표시한다.
   * @param {Boolean} mode 사각 영역 그리기 여부
   * @example
   *  logiMap.setDragAreaMode(true);
   *  //마우스를 드래그하면 화면에 사각 영역이 그려진다.
   */
    ['setDragAreaMode'](_0x2e1097) {
        _0x2e1097 == !![] ? (this.#uiLayer['setDragAreaMode'](!![]), this.#uiLayer['setDragAreaInit']()) : (this.#uiLayer['setDragAreaMode'](![]), this.#uiLayer['setDragAreaEnd']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  드래그 사각 영역의 스타일을 설정한다.
   * @param {Object} style style
   *  @param {Number} style.width 라인 넓이
   *  @param {Number} style.dashLength 점선 길이
   *  @param {Number} style.dashSpace 간격 길이
   *  @param {String} style.color 라인 색
   * @example
   *  logiMap.setDragAreaStyle(style);
   *  //설정된 스타일로 드래그 사각 영역이 그려진다.
   */
    ['setDragAreaStyle'](_0x336329) {
        this.#uiLayer['setDragAreaStyle'](_0x336329);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  드래그 사각 영역의 좌표 두개를 리턴한다.
   * @returns {logi.maps.LatLng[]} 드래그 사각 영역의 월드 좌표 2개
   * @example
   *  let rect = logiMap.getDragAreaRect();
   *  //드래그 사각 영역의 좌표 두개가 배열로 리턴된다
   */
    ['getDragAreaRect']() {
        return this.#uiLayer['getDragAreaRect']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  OrderType은 두가지 타입이 있다.
   *   - ‘object’(default): route, gps, line, polygon, circle, image, label 순으로 그려진다. 같은 object들은 zindex 값에 의해서 순서가 결정된다.
   *   - ‘zindex’: zindex 값으로만 그리는 순서가 결정된다. zindex 값이 같으면 route, gps, line, polygon, circle, image, label 순으로 그려진다.
   * @param {String} orderType 우선순위타입
   * @example
   *  logiMap.setOrderType(‘zindex’);
   *  //그리는 순서가 zindex 타입으로 변경된다.
   */
    ['setOrderType'](_0x30ca1b) {
        this.#objLayer['setOrderType'](_0x30ca1b);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  그룹 아이디가 같은 image와 label은 영역이 겹쳐지면 하나로 표시된다. setOverlapCheck를 이용하면 전체를 끄거나 켤 수 있다.
   * @param {Boolean} check 바운더리체크 여부
   * @example
   *  logiMap.setOverlapCheck(false);
   *  //영역 체크를 모두 해제한다.
   */
    ['setOverlapCheck'](_0x2a69b2) {
        this.#objLayer['setOverlapCheck'](_0x2a69b2);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  image 또는 label의 겹쳐진 수를 표시 할 지 여부를 지정한다.
   * @param {Boolean} visibility 겹친정보표시 여부
   * @example
   *  logiMap.setOverlapInfoVisibility(true);
   *  //겹쳐진 수를 표시한다.
   */
    ['setOverlapInfoVisibility'](_0x569d29) {
        this.#objLayer['setOverlapInfoVisibility'](_0x569d29);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Hover 모드에서 활성화된 영역의 정보를 리턴한다.
   * @returns {{code:Number, centroid:{lat:Number,lng:Number}, name:String, polygons:Array<{latlngs:logi.maps.LatLng[]}>}}
   * @example
   *  logiMap.getHoveredDistrict();
   *  //활성화된 영역의 정보가 리턴된다.(없으면 null)
   */
    ['getHoveredDistrict']() {
        return this.#vtLayer['getHoveredDistrict']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  행정 구역(우편번호, 시도, 시군구, 읍면동, ...) 표시 유형을 선택한다.
   * @param {DISTRICT_VISIBLETYPE} visibleType 행정 구역 표시 유형
   * @example
   *  logiMap.setDistrictVisible(logi.maps.DISTRICT_VISIBLETYPE.SGG_ON);
   *  //시군구 영역을 표시한다.
   */
    ['setDistrictVisible'](_0x4515f7) {
        this.#vtLayer['setDistrictVisible'](_0x4515f7);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  행정 구역(우편번호, 시도, 시군구, 읍면동, ...) 표시 범위를 지정한다.
   * @param {DISTRICT_DATATYPE} dataType 행정 구역 타입
   * @param {Number} fromLevel 시작 레벨
   * @param {Number} toLevel 끝 레벨
   * @example
   *  logiMap.setDistrictRange(logi.maps.DISTRICT_DATATYPE.SGG, 10, 18);
   *  //시군구 영역 표시는 레벨 10~18에서 활성화 된다.
   */
    ['setDistrictRange'](_0x14843b, _0x49dce7, _0x170b7d) {
        this.#vtLayer['setDistrictRange'](_0x14843b, _0x49dce7, _0x170b7d);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  행정 구역(우편번호, 시도, 시군구, 읍면동, ...) 스타일을 지정한다.
   * @param {DISTRICT_DATATYPE} dataType 행정 구역 타입
   * @param {Object | null} polygon 폴리곤 스타일
   *  @param {String} polygon.fillColor 영역 색상
   *  @param {Number} polygon.lineWidth 외곽 라인 두께
   *  @param {String} polygon.lineColor 외곽 라인 색상
   *  @param {Number} polygon.dashLength 점선 길이,
   *  @param {Number} polygon.dashSpace 점선 간격,
   * @param {Object | null} text 글자 스타일
   *  @param {String} text.fontFamily 폰트 명
   *  @param {Boolean} text.fontBold 글자 볼드
   *  @param {String} text.fontColor 글자 색상
   *  @param {Number[] | Number} text.fontSize 글자 크기 (fromLevel부터 fontSize가 순착적으로 적용)
   * @param {Object | null} textBox 글자 박스 스타일 (default: null)
   *  @param {String} textBox.fillColor 글자 박스 색상
   *  @param {Number} textBox.lineWidth 글자 박스 외곽 라인 두께
   *  @param {String} textBox.lineColor 글자 박스 외곽 라인 색상
   *  @param {Number} textBox.radius 글자 박스 라운드
   * @example
   *  logiMap.setDistrictStyle(logi.maps.DISTRICT_DATATYPE.SGG, {fillColor: "#FF0000", lineWidth: 1, lineColor: "#FF00FF"}, {fontColor: "#FFFF00", fontSize:18});
   *  //시군구 영역 색상은 빨간색, 외곽 실선은 자홍색, 글자 크기는 18 사이즈로 작성된다.
   */
    ['setDistrictStyle'](_0xc64e60, _0x25e8e6, _0x42ae7a, _0x42e135 = null) {
        this.#vtLayer['setDistrictStyle'](_0xc64e60, _0x25e8e6, _0x42ae7a, _0x42e135);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  행정 구역(우편번호, 시도, 시군구, 읍면동, ...) 스타일을 리턴한다.
   * @param {DISTRICT_DATATYPE} dataType 행정 구역 타입
   * @returns {DISTRICT_STYLE} 스타일
   * @example
   *  let style = logiMap.getDistrictStyle(logi.maps.DISTRICT_DATATYPE.SGG);
   *  //시군구에 설정된 스타일이 리턴된다
   */
    ['getDistrictStyle'](_0x2cb53f) {
        return this.#vtLayer['getDistrictStyle'](_0x2cb53f);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  행정 구역(우편번호, 시도, 시군구, 읍면동, ...) Hover 활성화 범위를 지정한다.
   * @param {DISTRICT_DATATYPE} dataType 행정 구역 타입
   * @param {Number} fromLevel 시작 레벨
   * @param {Number} toLevel 끝 레벨
   * @example
   *  logiMap.setDistrictHoverRange(logi.maps.DISTRICT_DATATYPE.SGG, 10, 18);
   *  //시군구 Hover 영역 표시는 레벨 10~18에서 활성화 된다.
   */
    ['setDistrictHoverRange'](_0x224a16, _0x30c8ef, _0x310d28) {
        this.#vtLayer['setDistrictHoverRange'](_0x224a16, _0x30c8ef, _0x310d28);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  행정 구역(우편번호, 시도, 시군구, 읍면동, ...) Hover 스타일을 지정한다.
   * @param {DISTRICT_DATATYPE} dataType 행정 구역 타입
   * @param {Object | null} polygon 폴리곤 스타일
   *  @param {String} polygon.fillColor 영역 색상
   *  @param {Number} polygon.lineWidth 외곽 라인 두께
   *  @param {String} polygon.lineColor 외곽 라인 색상
   *  @param {Number} polygon.dashLength 점선 길이,
   *  @param {Number} polygon.dashSpace 점선 간격,
   * @param {Object | null} text 글자 스타일
   *  @param {String} text.fontFamily 폰트 명
   *  @param {Boolean} text.fontBold 글자 볼드
   *  @param {String} text.fontColor 글자 색상
   *  @param {Number} text.fontSize 글자 크기
   * @param {Object | null} textBox 글자 박스 스타일 (default: null)
   *  @param {String} textBox.fillColor 글자 박스 색상
   *  @param {Number} textBox.lineWidth 글자 박스 외곽 라인 두께
   *  @param {String} textBox.lineColor 글자 박스 외곽 라인 색상
   *  @param {Number} textBox.radius 글자 박스 라운드
   * @example
   *  logiMap.setDistrictHoverStyle(logi.maps.DISTRICT_DATATYPE.SGG, {fillColor: "#FF0000", lineWidth: 1, lineColor: "#FF00FF"}, {fontColor: "#FFFF00", fontSize: 14});
   *  //시군구 Hover 영역 색상은 빨간색, 외곽 실선은 자홍색, 글자 크기는 14 사이즈로 작성된다.
   */
    ['setDistrictHoverStyle'](_0x21a100, _0x198a1f, _0xe9d940, _0x3b8bae = null) {
        this.#vtLayer['setDistrictHoverStyle'](_0x21a100, _0x198a1f, _0xe9d940, _0x3b8bae);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  행정 구역(우편번호, 시도, 시군구, 읍면동, ...) Hover 스타일을 리턴한다.
   * @param {DISTRICT_DATATYPE} dataType 행정 구역 타입
   * @returns {DISTRICT_STYLE} 스타일
   * @example
   *  let style = logiMap.getDistrictHoverStyle(logi.maps.DISTRICT_DATATYPE.SGG);
   *  //시군구 Hover에 설정된 스타일이 리턴된다
   */
    ['getDistrictHoverStyle'](_0x17fe4f) {
        return this.#vtLayer['getDistrictHoverStyle'](_0x17fe4f);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  행정 구역(시도, 시군구, 읍면동, ...) 코드에 맞는 폴리곤을 검색해서 화면에 그린다.
   * @param {String} code 행정 구역 코드 (10자리 PNU 코드: 시도 2자리, 시군구 3자리, 읍면동 3자리, 리 2자리)
   * @param {Object} padding 옵셋값 (default: undefined)
   *  @param {number} padding.left 왼쪽
   *  @param {number} padding.top 위쪽
   *  @param {number} padding.right 오른쪽
   *  @param {number} padding.bottom 아래쪽
   * @param {String} behavior 이동 방식 (default: undefined, ‘smooth’: 지도 변화(이동, 축척)를 애니메이션처럼 스텝을 나누어 적용)
   * @example
   *  logiMap.setSearchDistrict('1100000000');
   *  //서울특별시 영역 폴리곤이 그려지고 해당 폴리곤으로 이동한다.
   */
    async ['setSearchDistrict'](_0x205c6b, _0x354537, _0xab5c90) {
        const _0x3e81c2 = await this.#vtLayer['setSearchDistrict'](_0x205c6b);
        if (_0x3e81c2) {
            const _0x54ee6a = {
                'min': {
                    'lng': _0x3e81c2['boundaryRect']['xMin'],
                    'lat': _0x3e81c2['boundaryRect']['yMin']
                },
                'max': {
                    'lng': _0x3e81c2['boundaryRect']['xMax'],
                    'lat': _0x3e81c2['boundaryRect']['yMax']
                }
            };
            this['setBounds'](_0x54ee6a, _0x354537, _0xab5c90);
        }
    }
    /**
   * @preserve .
   * @method
   * @description
   *  검색된 행정 구역(시도, 시군구, 읍면동, ...) 스타일을 지정한다.
   * @param {Object | null} polygon 폴리곤 스타일
   *  @param {String} polygon.fillColor 영역 색상
   *  @param {Number} polygon.lineWidth 외곽 라인 두께
   *  @param {String} polygon.lineColor 외곽 라인 색상
   *  @param {Number} polygon.dashLength 점선 길이,
   *  @param {Number} polygon.dashSpace 점선 간격,
   * @param {Object | null} text 글자 스타일
   *  @param {String} text.fontFamily 폰트 명
   *  @param {Boolean} text.fontBold 글자 볼드
   *  @param {String} text.fontColor 글자 색상
   *  @param {Number} text.fontSize 글자 크기
   * @param {Object | null} textBox 글자 박스 스타일 (default: null)
   *  @param {String} textBox.fillColor 글자 박스 색상
   *  @param {Number} textBox.lineWidth 글자 박스 외곽 라인 두께
   *  @param {String} textBox.lineColor 글자 박스 외곽 라인 색상
   *  @param {Number} textBox.radius 글자 박스 라운드
   * @example
   *  logiMap.setSearchDistrictStyle({fillColor: "#FF0000", lineWidth: 1, lineColor: "#FF00FF"}, {fontColor: "#FFFF00", fontSize: 14});
   *  //영역 색상은 빨간색, 외곽 실선은 자홍색, 글자 크기는 14 사이즈로 작성된다.
   */
    ['setSearchDistrictStyle'](_0x6a6a40, _0x334c80, _0x430a4f = null) {
        this.#vtLayer['setSearchDistrictStyle'](_0x6a6a40, _0x334c80, _0x430a4f);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  검색된 행정 구역(시도, 시군구, 읍면동, ...) 스타일을 리턴한다.
   * @returns {DISTRICT_STYLE} 스타일
   * @example
   *  let style = logiMap.getSearchDistrictStyle();
   *  //설정된 스타일이 리턴된다
   */
    ['getSearchDistrictStyle']() {
        return this.#vtLayer['getSearchDistrictStyle']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  화면을 스크린샷한다.
   * @param {{width:Number, height:Number}} imageSize 이미지사이즈 (default: 화면 사이즈)
   *  @param {Number} imageSize.width 이미지 넓이
   *  @param {Number} imageSize.height 이미지 높이
   * @example
   *  logiMap.screenshot().then(blob => { … } );
   *  //현재 화면을 스크린샷해서 png blob 전달한다.
   */
    async ['screenshot'](_0x4d2a99) {
        const _0x5152cb = 0x0, _0x25843a = 0x0, _0x36b4a2 = this.#mapScreenSize['width'], _0x14873e = this.#mapScreenSize['height'], _0x880d0e = _0x4d2a99?.['width'] ?? this.#mapScreenSize['width'], _0x4b26ab = _0x4d2a99?.['height'] ?? this.#mapScreenSize['height'], _0x52341b = this.#mtLayer['getGfxCanvas'](0x0), _0xed1377 = this.#mtLayer['getGfx2d'](0x1), _0x539e54 = this.#objLayer['getGfx2d'](0x0), _0x7e7751 = new logi['maps']['Gfx2d'](new OffscreenCanvas(_0x880d0e, _0x4b26ab));
        return _0x52341b instanceof logi['maps']['Gfxgl'] ? _0x7e7751['drawImage'](await _0x52341b['screenshot'](), _0x5152cb, _0x25843a, _0x36b4a2, _0x14873e, 0x0, 0x0, _0x880d0e, _0x4b26ab) : _0x7e7751['drawImage'](_0x52341b, _0x5152cb, _0x25843a, _0x36b4a2, _0x14873e, 0x0, 0x0, _0x880d0e, _0x4b26ab), _0x7e7751['drawImage'](_0xed1377, _0x5152cb, _0x25843a, _0x36b4a2, _0x14873e, 0x0, 0x0, _0x880d0e, _0x4b26ab), _0x7e7751['drawImage'](_0x539e54, _0x5152cb, _0x25843a, _0x36b4a2, _0x14873e, 0x0, 0x0, _0x880d0e, _0x4b26ab), await _0x7e7751['convertToBlob']({ 'type': 'image/png' });
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지도에서 발생되는 이벤트를 등록된 리스너로 콜백한다.
   * @param {EVENT} eventName 이벤트명
   * @param {Function} func 콜백함수
   * @example
   *  logiMap.addEventListener(logi.maps.EVENT.dblclick, function(event){ console.log("double click"); });
   *  //지도에서 더블클릭하면 console 창에 “double click”이 출력된다.
   */
    ['addEventListener'](_0x2f981d, _0x5ba0aa, _0x5a2741 = ![]) {
        this.#eventLayer['addEventListener'](_0x2f981d, _0x5ba0aa, _0x5a2741);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 이벤트 리스너를 지운다.
   * @param {EVENT} eventName 이벤트명
   * @param {Function} func 콜백함수
   * @example
   *  logiMap.removeEventListener(logi.maps.EVENT.dblclick, function(event) { console.log("double click"); });
   *  //지도에서 더블클릭해도 console 창에 “double click”이 출력되지 않는다.
   */
    ['removeEventListener'](_0x3a38d5, _0x366241, _0x49d23b = ![]) {
        this.#eventLayer['removeEventListener'](_0x3a38d5, _0x366241, _0x49d23b);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  모바일 웹앱에서 Bridge로 전달 받을 이벤트를 등록한다.
   *  [message format]
   *   - ‘message’: ‘onMapEvent’, ‘type’:{eventName}, ‘pointX’:{screenX}, ‘pointY’:{screenY}
   * @param {BRIDGE_MAPEVENT} eventName 이벤트명
   * @param {Boolean} activity 활성화 여부
   * @example
   *  logiMap.setBridgeEvent(logi.maps.BRIDGE_MAPEVENT.touch, true);
   *  //맵을 터치하면 Bridge를 통해 onMapEvent 함수 또는 메시지로 정보가 전달된다.
   */
    ['setBridgeEvent'](_0x28d2fb, _0x22ee4f) {
        this.#bridgeMapEvents[_0x28d2fb] !== undefined && (this.#bridgeMapEvents[_0x28d2fb] = _0x22ee4f);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - 패닝 할 때 맵 타일을 제외한 UI 프리징 여/부 설정
   *  - 기본 모드는 freeze mode on
   * @param {Boolean} modeOn 모드 여부
   * @example
   *  logiMap.setFreezeModeOnMoving(true);
   *  //패닝 시작할 때 맵 타일을 제외한 UI를 캡쳐한다. 패닝에 따라서 캡쳐된 이미지를 이동 시킨다.
   */
    ['setFreezeModeOnMoving'](_0x650ce0) {
        this.#freezeModeOnMoving = _0x650ce0;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  화면 중앙에 마크를 그린다.
   * @param {Number} length 길이
   * @param {Number} thickness 두께
   * @param {String} color 색상
   * @example
   *  logiMap.setCenterMark(80, 1, ‘red’);
   *  //화면 중앙에 가로 세로 80 길이의 빨간색 십자 마크가 그려진다.
   */
    ['setCenterMark'](_0xd7f1c8, _0x1d8487, _0x51b107) {
        this.#uiLayer['setCenterMark'](_0xd7f1c8, _0x1d8487, _0x51b107);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지도를 강제로 다시 그린다.
   *  지도 갱신이 필요한 시점에 자동으로 호출 됨
   * @example
   *  logiMap.updateMap();
   *  //화면을 강제로 갱신한다.
   */
    ['updateMap']() {
        for (let _0x54d807 of this.#mapLayers) {
            _0x54d807['setUpdateFlag']();
        }
    }
    /**
   * @preserve .
   * @method
   * @description
   *  현재 지도 스케일이 변경한다.
   * @param {Number} zoom 지도 스케일
   * @example
   *  logiMap.setZoom(16);
   *  //지도 스케일이 16 으로 변경된다.
   */
    ['setZoom'](_0x47f340) {
        this.#mapCoord['setZoom'](_0x47f340), this.#mapCoord['adjustMapBoundRect'](), this.#objLayer['refreshTiledImageByLevel'](), this.#objLayer['refreshTiledLabelByLevel'](), this['updateMap']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  현재 지도 스케일이 전달한다.
   * @returns {Number} 지도 스케일
   * @example
   *  let zoom = logiMap.getZoom();
   *  //현재 지도 스케일이 전달된다.
   */
    ['getZoom']() {
        return this.#mapCoord['getZoom']();
    }
    /**
   * @preserve .
   * @method
   * @deprecated This method is deprecated. Use setZoom(zoom) instead.
   * @description
   *  현재 지도 타일 Level을 변경한다.
   * @param {Number} level 지도 타일 레벨
   * @example
   *  logiMap.setLevel(16);
   *  //지도 타일 레벨이 16 으로 변경된다.
   */
    ['setLevel'](_0xc9f96a) {
        this.#mapCoord['setTileLevelOffset'](0x1), this.#mapCoord['setLevel'](_0xc9f96a), this.#mapCoord['adjustMapBoundRect'](), this.#objLayer['refreshTiledImageByLevel'](), this.#objLayer['refreshTiledLabelByLevel'](), this['updateMap']();
    }
    /**
   * @preserve .
   * @method
   * @deprecated This method is deprecated. Use getZoom() instead.
   * @description
   *  현재 지도 타일 Level을 전달한다.
   * @returns {Number} 지도 타일 레벨
   * @example
   *  let level = logiMap.getLevel();
   *  //현재 지도 타일 Level이 전달된다.
   */
    ['getLevel']() {
        return this.#mapCoord['getLevel']();
    }
    ['isZoomInMax']() {
        return this.#mapCoord['isZoomInMax']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  현재 지도의 Level을 한 단계 확대한다.
   * @example
   *  logiMap.zoomIn();
   *  //지도를 한 단계 확대한다.
   */
    ['zoomIn']() {
        this['isZoomInMax']() == ![] && (this.#mapCoord['setTileLevelOffset'](0x1), this.#mapCoord['zoomIn'](), this.#mapCoord['adjustMapBoundRect'](), this.#objLayer['refreshTiledImageByLevel'](), this.#objLayer['refreshTiledLabelByLevel'](), this['updateMap']());
    }
    ['isZoomOutMax']() {
        return this.#mapCoord['isZoomOutMax']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  현재 지도의 Level을 한 단계 축소한다.
   * @example
   *  logiMap.zoomOut();
   *  //지도를 한 단계 축소한다.
   */
    ['zoomOut']() {
        this['isZoomOutMax']() == ![] && (this.#mapCoord['setTileLevelOffset'](0x1), this.#mapCoord['zoomOut'](), this.#mapCoord['adjustMapBoundRect'](), this.#objLayer['refreshTiledImageByLevel'](), this.#objLayer['refreshTiledLabelByLevel'](), this['updateMap']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  입력 받은 위치로 지도를 이동한다.
   * @param {logi.maps.LatLng} latlng 이동할 좌표
   * @example
   *  logiMap.setCenter({lat: 37.5436, lng: 127.0447});
   *  //서울숲역으로 지도가 이동된다.
   */
    ['setCenter'](_0xdef5d5) {
        this.#isDragging = ![], this.#mapCoord['setCenter'](_0xdef5d5['lng'], _0xdef5d5['lat']), this.#mapCoord['adjustMapBoundRect'](), this['updateMap']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  현재 지도 위치를 전달한다.
   * @returns {logi.maps.LatLng} 현재 지도 좌표
   * @example
   *  let center = logiMap.getCenter();
   *  //{lat: 37.5436, lng: 127.0447} 지도 위치가 전달된다.
   */
    ['getCenter']() {
        return this.#mapCoord['getCenter']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지도를 이동 또는 축소/확대를 한다.
   * @param {logi.maps.LatLng} latlng 이동할 좌표
   * @param {Number} level 이동할 레벨
   * @param {String} behavior 이동 방식 (default: undefined, ‘smooth’: 지도 변화(이동, 축척)를 애니메이션처럼 스텝을 나누어 적용)
   * @example
   *  logiMap.move({lat: 37.5436, lng: 127.0447}, 16);
   *  //서울숲역으로 지도가 이동되고 축척 레벨은 16으로 설정된다.
   */
    ['move'](_0x43b0f6, _0x489658, _0x6a6480) {
        if (_0x6a6480 == 'smooth') {
            const _0x29247e = this.#mapCoord['getCenter'](), _0x469e51 = this.#mapCoord['getLevel'](), _0x29be86 = Math['min'](_0x489658, _0x469e51) - 0.5, _0x4e012b = Math['max'](parseInt(this.#currentFps * 0.2), 0x2), _0x59b584 = Math['max'](parseInt(this.#currentFps * 0.8), 0x8);
            this.#moveAnims = [];
            for (let _0x4989ac = 0x0; _0x4989ac < _0x4e012b; ++_0x4989ac) {
                this.#moveAnims['push']({ 'zoomScale': { 'step': (_0x29be86 - _0x469e51) / _0x4e012b } });
            }
            for (let _0x538461 = 0x0; _0x538461 < _0x59b584; ++_0x538461) {
                this.#moveAnims['push']({
                    'position': {
                        'step': {
                            'lat': (_0x43b0f6['lat'] - _0x29247e['lat']) / _0x59b584,
                            'lng': (_0x43b0f6['lng'] - _0x29247e['lng']) / _0x59b584
                        }
                    }
                });
            }
            for (let _0x7a4a16 = 0x0; _0x7a4a16 < _0x4e012b; ++_0x7a4a16) {
                this.#moveAnims['push']({ 'zoomScale': { 'step': (_0x489658 - _0x29be86) / _0x4e012b } });
            }
            this.#moveAnims['push']({
                'zoomScale': { 'target': _0x489658 },
                'position': {
                    'target': {
                        'lat': _0x43b0f6['lat'],
                        'lng': _0x43b0f6['lng']
                    }
                }
            });
        } else
            this['setLevel'](_0x489658), this['setCenter'](_0x43b0f6);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  바운더리 경계가 포함되게 지도화면을 구성한다.
   * @param {logi.maps.LatLngBound} latlngBound 바운더리 좌표
   * @param {Object} padding 옵셋값 (default: undefined)
   *  @param {number} padding.left 왼쪽
   *  @param {number} padding.top 위쪽
   *  @param {number} padding.right 오른쪽
   *  @param {number} padding.bottom 아래쪽
   * @param {String} behavior 이동 방식 (default: undefined, ‘smooth’: 지도 변화(이동, 축척)를 애니메이션처럼 스텝을 나누어 적용)
   * @example
   *  logiMap.setBounds({min: {lat: 37.50346, lng: 127.01868}, max: {lat: 37.52752, lng: 127.04079}});
   *  //입력된 사각형 영역으로 지도를 이동시킨다.
   * @example
   *  logiMap.setBounds({min: {lat: 37.50346, lng: 127.01868}, max: {lat: 37.52752, lng: 127.04079}}, {left: 100, top: 100, right: 100, bottom: 100});
   *  //{100, 100, 100, 100} 패딩값이 적용된 사각형 영역으로 지도를 이동시킨다.
   */
    ['setBounds'](_0x1785de, _0x3a9fa8, _0x5b08ca) {
        if (_0x5b08ca == 'smooth') {
            this.#isDragging = ![], this.#mapCoord['setTileLevelOffset'](0x1);
            const _0x494cb4 = this.#mapCoord['getBoundsInfo'](_0x1785de['min']['lng'], _0x1785de['min']['lat'], _0x1785de['max']['lng'], _0x1785de['max']['lat'], _0x3a9fa8?.['left'] ?? 0x0, _0x3a9fa8?.['top'] ?? 0x0, _0x3a9fa8?.['right'] ?? 0x0, _0x3a9fa8?.['bottom'] ?? 0x0);
            if (_0x494cb4 == null)
                return;
            const _0x206381 = this.#mapCoord['getCenter'](), _0x292aac = this.#mapCoord['getLevel'](), _0x5c1257 = Math['min'](_0x494cb4['zoomInfo']['zoomScale'], _0x292aac) - 0.5, _0x2f37f3 = Math['max'](parseInt(this.#currentFps * 0.2), 0x2), _0x1c3997 = Math['max'](parseInt(this.#currentFps * 0.8), 0x8);
            this.#moveAnims = [];
            for (let _0x178fe9 = 0x0; _0x178fe9 < _0x2f37f3; ++_0x178fe9) {
                this.#moveAnims['push']({ 'zoomScale': { 'step': (_0x5c1257 - _0x292aac) / _0x2f37f3 } });
            }
            for (let _0x378069 = 0x0; _0x378069 < _0x1c3997; ++_0x378069) {
                this.#moveAnims['push']({
                    'position': {
                        'step': {
                            'lat': (_0x494cb4['center']['lat'] - _0x206381['lat']) / _0x1c3997,
                            'lng': (_0x494cb4['center']['lng'] - _0x206381['lng']) / _0x1c3997
                        }
                    }
                });
            }
            for (let _0x25870b = 0x0; _0x25870b < _0x2f37f3; ++_0x25870b) {
                this.#moveAnims['push']({ 'zoomScale': { 'step': (_0x494cb4['zoomInfo']['zoomScale'] - _0x5c1257) / _0x2f37f3 } });
            }
            this.#moveAnims['push']({
                'zoomScale': { 'target': _0x494cb4['zoomInfo']['zoomScale'] },
                'position': {
                    'target': {
                        'lat': _0x494cb4['center']['lat'],
                        'lng': _0x494cb4['center']['lng']
                    }
                }
            });
        } else
            this.#isDragging = ![], this.#mapCoord['setBounds'](_0x1785de['min']['lng'], _0x1785de['min']['lat'], _0x1785de['max']['lng'], _0x1785de['max']['lat'], _0x3a9fa8?.['left'] ?? 0x0, _0x3a9fa8?.['top'] ?? 0x0, _0x3a9fa8?.['right'] ?? 0x0, _0x3a9fa8?.['bottom'] ?? 0x0), this.#mapCoord['adjustMapBoundRect'](), this.#objLayer['refreshTiledImageByLevel'](), this.#objLayer['refreshTiledLabelByLevel'](), this['updateMap']();
    }
    ['fitBounds'](_0x3d0bac, _0x170eda) {
        const _0x5075d4 = _0x3d0bac['getNorthEast'](), _0x39c52c = _0x3d0bac['getSouthWest'](), _0xf78ada = _0x3d0bac['getCenter'](), _0x2a8489 = this.#mapScreenSize, _0xd21266 = _0x3d0bac['adjustCenter'](_0xf78ada, _0x2a8489, _0x170eda), _0x3e54a0 = _0x3d0bac['calculateZoomLevel'](_0x5075d4, _0x39c52c);
        this['setLevel'](_0x3e54a0), this['setCenter'](_0xd21266);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지도화면의 바운더리 경계값을 리턴한다.
   * @returns {{left:Number, top:Number, right:Number, bottom:Number}} 바운더리 좌표
   * @example
   *  let worldBoundary = logiMap.getBounds();
   *  //지도화면의 바운더리 경계값이 리턴된다.
   */
    ['getBounds']() {
        const _0x3769f5 = this.#mapCoord['getMapRect']();
        return {
            'left': _0x3769f5['west'],
            'top': _0x3769f5['north'],
            'right': _0x3769f5['east'],
            'bottom': _0x3769f5['south']
        };
    }
    /**
   * @preserve .
   * @method
   * @description
   *  생성된 Image, Label, Line, Polygon, Circle, Route, Gps, Custom을 Map에 추가한다.
   * @param {logi.maps.Object} obj 추가할 Object
   * @example
   *  let image = new logi.maps.Image('./sample.png', {lat:37.50346, lng:127.01868});
   *  logiMap.addObject(image);
   *  //생성된 image가 Map에 추가된다.
   */
    ['addObject'](_0x3765e7) {
        let _0x35a79a = _0x3765e7?.['getType']?.();
        if (_0x3765e7)
            switch (_0x35a79a) {
            case logi['maps']['Object']['OBJTYPE']['image']:
                this['addImage'](_0x3765e7);
                break;
            case logi['maps']['Object']['OBJTYPE']['label']:
                this['addLabel'](_0x3765e7);
                break;
            case logi['maps']['Object']['OBJTYPE']['line']:
                this['addLine'](_0x3765e7);
                break;
            case logi['maps']['Object']['OBJTYPE']['polygon']:
                this['addPolygon'](_0x3765e7);
                break;
            case logi['maps']['Object']['OBJTYPE']['circle']:
                this['addCircle'](_0x3765e7);
                break;
            case logi['maps']['Object']['OBJTYPE']['route']:
                this['addRoute'](_0x3765e7);
                break;
            case logi['maps']['Object']['OBJTYPE']['gps']:
                this['addGps'](_0x3765e7);
                break;
            case logi['maps']['Object']['OBJTYPE']['custom']:
                this['addCustom'](_0x3765e7);
                break;
            case logi['maps']['Object']['OBJTYPE']['meta']:
                this.#addMeta(_0x3765e7);
                break;
            default:
                break;
            }
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 Image, Label, Line, Polygon, Circle, Route, Gps, Custom을 Map에서 삭제한다.
   * @param {logi.maps.Object} obj 제거할 Object
   * @example
   *  logiMap.removeObject(image);
   *  //등록된 image가 Map에서 삭제된다.
   */
    ['removeObject'](_0x2ed780) {
        let _0x153c4e = _0x2ed780?.['getType']?.();
        if (_0x2ed780)
            switch (_0x153c4e) {
            case logi['maps']['Object']['OBJTYPE']['image']:
                this['removeImage'](_0x2ed780['getKey']());
                break;
            case logi['maps']['Object']['OBJTYPE']['label']:
                this['removeLabel'](_0x2ed780['getKey']());
                break;
            case logi['maps']['Object']['OBJTYPE']['line']:
                this['removeLine'](_0x2ed780['getKey']());
                break;
            case logi['maps']['Object']['OBJTYPE']['polygon']:
                this['removePolygon'](_0x2ed780['getKey']());
                break;
            case logi['maps']['Object']['OBJTYPE']['circle']:
                this['removeCircle'](_0x2ed780['getKey']());
                break;
            case logi['maps']['Object']['OBJTYPE']['route']:
                this['removeRoute'](_0x2ed780['getKey']());
                break;
            case logi['maps']['Object']['OBJTYPE']['gps']:
                this['removeGps'](_0x2ed780['getKey']());
                break;
            case logi['maps']['Object']['OBJTYPE']['custom']:
                this['removeCustom'](_0x2ed780['getKey']());
                break;
            case logi['maps']['Object']['OBJTYPE']['meta']:
                this.#removeMeta(_0x2ed780['getKey']());
                break;
            default:
                break;
            }
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 모든 Image, Label, Line, Polygon, Circle, Route, Gps, Custom을 Map에서 삭제한다.
   *  excludedKeys에 값이 있다면 해당 key를 가진 Object는 삭제 대상에서 제외된다.
   * @param {String[]} excludedKeys 제외할 Keys (default: undefined)
   * @example
   *  logiMap.removeAll();
   *  //등록된 모든 Image, Label, Line, Polygon, Circle, Route, Gps, Custom을 Map에서 삭제된다.
   * @example
   *  logiMap.removeAll([‘key-01’]);
   *  //key 값으로 등록된 Object만 제외되고 모두 Map에서 삭제된다.
   */
    ['removeAll'](_0x314f21) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeAll]\x20excludedKeys:' + _0x314f21), this.#objLayer['removeAll'](_0x314f21), this.#overlayLayer['removeAll'](_0x314f21);
    }
    ['hitObj'](_0x353aa1, _0x2d23fc) {
        return this.#objLayer['hitObj'](_0x353aa1, _0x2d23fc);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Image를 Map에 추가한다
   * @param {logi.maps.Image} image 추가할 이미지
   * @returns {logi.maps.Image} 추가된 이미지
   * @example
   *  let image = new logi.maps.Image('./sample.png', {lat:37.50346, lng:127.01868});
   *  logiMap.addImage(image);
   *  //생성된 image가 Map에 추가된다.
   */
    ['addImage'](_0x4a7869) {
        return logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[addImage]\x20key:' + _0x4a7869['getKey']() + '\x20position:\x20' + _0x4a7869['getPosition']()['lng'] + ',\x20' + _0x4a7869['getPosition']()['lat']), this.#objLayer['addImage'](_0x4a7869);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  key 값으로 등록된 Image가 있는지 확인한다.
   * @param {String} objKey 이미지 Key
   * @returns {Boolean} 등록 여부
   * @example
   *  let exist = logiMap.isExistImage(‘key-01’);
   *  //‘key-01’로 등록된 image가 있으면 true 없으면 false
   */
    ['isExistImage'](_0x27a3bf) {
        return this.#objLayer['isExistImage'](_0x27a3bf);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.
   *  - key, rect 또는 class 값으로 등록된 Image를 찾는다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 이미지 key
   *  @param {logi.maps.LatLng[]} [keyword.rect] 영역 rect
   *  @param {String} [keyword.class] 이미지 class
   * @returns {logi.maps.Image | logi.maps.Image[]}
   *  - logi.maps.Image: key 검색
   *  - logi.maps.Image[]: rect 또는 class 검색
   * @example
   *  let image = logiMap.findImage(‘key-01’);
   *  //‘key-01’로 등록된 image가 전달된다.
   * @example
   *  let image = logiMap.findImage({key: ‘key-01’});
   *  //key 값으로 등록된 image 전달된다.
   * @example
   *  let images = logiMap.findImage({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
   *  //영역에 포함되는 image 배열이 전달된다.
   * @example
   *  let images = logiMap.findImage({class: ‘pin’});
   *  //class 값으로 등록된 image 배열이 전달된다.
   */
    ['findImage'](_0x269b12) {
        return this.#objLayer['findImage'](_0x269b12);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.
   *  - key 또는 class 값으로 등록된 Image를 Map에서 삭제한다.
   *  - keyword 타입이 String이면 key로 처리된다.
   *  - key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 이미지 key
   *  @param {String} [keyword.class] 이미지 class
   * @example
   *  logiMap.removeImage(‘key-01’);
   * @example
   *  logiMap.removeImage({key: ‘key-01’});
   *  //key 값으로 등록된 Image가 Map에서 삭제된다.
   * @example
   *  logiMap.removeImage({class: ‘pin’});
   *  //class 값으로 등록된 Image들이 Map에서 삭제된다.
   */
    ['removeImage'](_0x3880d0) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeImage]\x20keyword:' + _0x3880d0), this.#objLayer['removeImage'](_0x3880d0);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 모든 Image를 삭제한다.
   *  excludedKeys에 값이 있다면 해당 key를 가진 Image는 삭제 대상에서 제외된다.
   * @param {String[]} excludedKeys 제외할 이미지 Keys (default: undefined)
   * @example
   *  logiMap.removeImageAll();
   *  //등록된 모든 Image를 삭제한다.
   * @example
   *  logiMap.removeImageAll([‘key-01’]);
   *  //key 값으로 등록된 Image만 제외되고 모두 Map에서 삭제된다.
   */
    ['removeImageAll'](_0x4dd266) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeImageAll]\x20excludedKeys:' + _0x4dd266), this.#objLayer['removeImageAll'](_0x4dd266);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  입력된 화면 좌표와 겹치는 Image를 찾는다.
   * @param {Number} x 스크린 좌표 X
   * @param {Number} y 스크린 좌표 Y
   * @returns {logi.maps.Image} 좌표와 겹치는 이미지
   * @example
   *  let image = logiMap.hitImage(250, 250);
   *  //스크린 좌표 (250, 250)에 있는 image가 전달된다.
   */
    ['hitImage'](_0x382a3d, _0x166bd8) {
        return this.#objLayer['hitImage'](_0x382a3d, _0x166bd8);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  입력된 화면 좌표와 겹치는 Image들을 찾는다.
   * @param {Number} x 스크린 좌표 X
   * @param {Number} y 스크린 좌표 Y
   * @returns {logi.maps.Image[]} 좌표와 겹치는 이미지들
   * @example
   *  let images = logiMap.hitImages(250, 250);
   *  //스크린 좌표 (250, 250)에 있는 image 배열이 전달된다.
   */
    ['hitImages'](_0x16f01e, _0x2bfe47) {
        return this.#objLayer['hitImages'](_0x16f01e, _0x2bfe47);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  입력된 화면 좌표와 겹치는 Image의 key를 찾는다.
   * @param {Number} x 스크린 좌표 X
   * @param {Number} y 스크린 좌표 Y
   * @returns {String} 좌표와 겹치는 이미지 Key
   * @example
   *  let imageKey = logiMap.hitImageKey(250, 250);
   *  //스크린 좌표 (250, 250)에 있는 image의 key가 전달된다.
   */
    ['hitImageKey'](_0x43b8a5, _0x51fee7) {
        const _0x89518f = this.#objLayer['hitImage'](_0x43b8a5, _0x51fee7);
        if (_0x89518f)
            return _0x89518f['getKey']();
        return '';
    }
    /**
   * @preserve .
   * @method
   * @description
   *  입력된 화면 좌표와 겹치는 Image들의 key들을 찾는다.
   * @param {Number} x 스크린 좌표 X
   * @param {Number} y 스크린 좌표 Y
   * @returns {String[]} 좌표와 겹치는 이미지 Keys
   * @example
   *  let imageKeys = logiMap.hitImageKeys(250, 250);
   *  //스크린 좌표 (250, 250)에 있는 image들의 key 배열이 전달된다.
   */
    ['hitImageKeys'](_0x27c9d4, _0xc129c6) {
        const _0x41a1ee = new Array(), _0xac3215 = this.#objLayer['hitImages'](_0x27c9d4, _0xc129c6);
        for (let _0x3366da of _0xac3215) {
            _0x41a1ee['push'](_0x3366da['getKey']());
        }
        return _0x41a1ee;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지도 이동할 때 이미지 그리기 여부를 결정한다.
   *  logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
   *  logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)
   * @param {Boolean} drawing 그리기 여부
   * @example
   *  logiMap.setDrawingImageOnMove(false);
   *  //지도 이동할 때는 이미지를 그리지 않는다.
   */
    ['setDrawingImageOnMove'](_0x5a81f9) {
        this.#objLayer['setDrawingImageOnMove'](_0x5a81f9);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Label을 Map에 추가한다
   * @param {logi.maps.Label} label 추가할 라벨
   * @returns {logi.maps.Label} 추가된 라벨
   * @example
   *  let label = new logi.maps.Label('label-text', {lat:37.50346, lng:127.01868});
   *  logiMap.addLabel(label);
   *  //생성된 label이 Map에 추가된다.
   */
    ['addLabel'](_0x4f9ee6) {
        return logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[addLabel]\x20key:' + _0x4f9ee6['getKey']() + '\x20position:\x20' + _0x4f9ee6['getPosition']()?.['lng'] + ',\x20' + _0x4f9ee6['getPosition']()?.['lat']), this.#objLayer['addLabel'](_0x4f9ee6);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  key 값으로 등록된 Label이 있는지 확인한다.
   * @param {String} objKey 라벨 Key
   * @returns {Boolean} 등록 여부
   * @example
   *  let exist = logiMap.isExistLabel(‘key-01’);
   *  //‘key-01’로 등록된 label이 있으면 true 없으면 false
   */
    ['isExistLabel'](_0x3af291) {
        return this.#objLayer['isExistLabel'](_0x3af291);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.
   *  - key, rect 또는 class 값으로 등록된 Label을 찾는다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 라벨 key
   *  @param {logi.maps.LatLng[]} [keyword.rect] 영역 rect
   *  @param {String} [keyword.class] 라벨 class
   * @returns {logi.maps.Label | logi.maps.Label[]}
   *  - logi.maps.Label: key 검색
   *  - logi.maps.Label[]: rect 또는 class 검색
   * @example
   *  let label = logiMap.findLabel(‘key-01’);
   *  //‘key-01’로 등록된 label이 전달된다.
   * @example
   *  let label = logiMap.findLabel({key: ‘key-01’});
   *  //key 값으로 등록된 label이 전달된다.
   * @example
   *  let labels = logiMap.findLabel({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
   *  //영역에 포함되는 label 배열이 전달된다.
   * @example
   *  let labels = logiMap.findLabel({class: ‘lbl’});
   *  //class 값으로 등록된 label 배열이 전달된다.
   */
    ['findLabel'](_0x2d9ed7) {
        return this.#objLayer['findLabel'](_0x2d9ed7);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.
   *  - key 또는 class 값으로 등록된 Label을 Map에서 삭제한다.
   *  - keyword 타입이 String이면 key로 처리된다.
   *  - key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 라벨 key
   *  @param {String} [keyword.class] 라벨 class
   * @example
   *  logiMap.removeLabel(‘key-01’);
   * @example
   *  logiMap.removeLabel({key: ‘key-01’});
   *  //key 값으로 등록된 Label이 Map에서 삭제된다.
   * @example
   *  logiMap.removeLabel({class: ‘lbl’});
   *  //class 값으로 등록된 Label들이 Map에서 삭제된다.
   */
    ['removeLabel'](_0x3c14c5) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeLabel]\x20keyword:' + _0x3c14c5), this.#objLayer['removeLabel'](_0x3c14c5);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 모든 Label을 삭제한다.
   *  excludedKeys에 값이 있다면 해당 key를 가진 Label은 삭제 대상에서 제외된다.
   * @param {String[]} excludedKeys 제외할 라벨 Keys (default: undefined)
   * @example
   *  logiMap.removeLabelAll();
   *  //등록된 모든 Label을 삭제한다.
   * @example
   *  logiMap.removeLabelAll([‘key-01’]);
   *  //key 값으로 등록된 Label만 제외되고 모두 Map에서 삭제된다.
   */
    ['removeLabelAll'](_0x2f40f3) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeLabelAll]\x20excludedKeys:' + _0x2f40f3), this.#objLayer['removeLabelAll'](_0x2f40f3);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지도 이동할 때 라벨 그리기 여부를 결정한다.
   *  logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
   *  logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)
   * @param {Boolean} drawing 그리기 여부
   * @example
   *  logiMap.setDrawingLabelOnMove(false);
   *  //지도 이동할 때는 라벨을 그리지 않는다.
   */
    ['setDrawingLabelOnMove'](_0x783eb2) {
        this.#objLayer['setDrawingLabelOnMove'](_0x783eb2);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Line을 Map에 추가한다
   * @param {logi.maps.Line} line 추가할 라인
   * @returns {logi.maps.Line} 추가된 라인
   * @example
   *  let line = new logi.maps.Line(logi.maps.LINETYPE.STRAIGHT, {fromLatLng:{lat: 37.50346, lng: 127.01868}, toLatLng:{lat: 37.50346, lng: 127.02468}, width: 4});
   *  logiMap.addLine(line);
   *  //생성된 line이 Map에 추가된다.
   */
    ['addLine'](_0x3ae633) {
        return logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[addLine]\x20key:' + _0x3ae633['getKey']()), this.#objLayer['addLine'](_0x3ae633);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  key 값으로 등록된 Line이 있는지 확인한다.
   * @param {String} objKey 라인 Key
   * @returns {Boolean} 등록 여부
   * @example
   *  let exist = logiMap.isExistLine(‘key-01’);
   *  //‘key-01’로 등록된 line이 있으면 true 없으면 false
   */
    ['isExistLine'](_0x16747d) {
        return this.#objLayer['isExistLine'](_0x16747d);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.
   *  - key, rect 또는 class 값으로 등록된 Line을 찾는다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 라인 key
   *  @param {logi.maps.LatLng[]} [keyword.rect] 영역 rect
   *  @param {String} [keyword.class] 라인 class
   * @returns {logi.maps.Line | logi.maps.Line[]}
   *  - logi.maps.Line: key 검색
   *  - logi.maps.Line[]: rect 또는 class 검색
   * @example
   *  let line = logiMap.findLine(‘key-01’);
   *  //‘key-01’로 등록된 line이 전달된다.
   * @example
   *  let line = logiMap.findLine({key: ‘key-01’});
   *  //key 값으로 등록된 line이 전달된다.
   * @example
   *  let lines = logiMap.findLine({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
   *  //영역에 포함되는 line 배열이 전달된다.
   * @example
   *  let lines = logiMap.findLine({class: ‘ln’});
   *  //class 값으로 등록된 line 배열이 전달된다.
   */
    ['findLine'](_0x45f78d) {
        return this.#objLayer['findLine'](_0x45f78d);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.
   *  - key 또는 class 값으로 등록된 Line을 Map에서 삭제한다.
   *  - keyword 타입이 String이면 key로 처리된다.
   *  - key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 라인 key
   *  @param {String} [keyword.class] 라인 class
   * @example
   *  logiMap.removeLine(‘key-01’);
   * @example
   *  logiMap.removeLine({key: ‘key-01’});
   *  //key 값으로 등록된 Line이 Map에서 삭제된다.
   * @example
   *  logiMap.removeLine({class: ‘ln’});
   *  //class 값으로 등록된 Line들이 Map에서 삭제된다.
   */
    ['removeLine'](_0x5a5e45) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeLine]\x20keyword:' + _0x5a5e45), this.#objLayer['removeLine'](_0x5a5e45);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 모든 Line을 삭제한다.
   *  excludedKeys에 값이 있다면 해당 key를 가진 Line은 삭제 대상에서 제외된다.
   * @param {String[]} excludedKeys 제외할 라인 Keys (default: undefined)
   * @example
   *  logiMap.removeLineAll();
   *  //등록된 모든 Line을 삭제한다.
   * @example
   *  logiMap.removeLineAll([‘key-01’]);
   *  //key 값으로 등록된 Line만 제외되고 모두 Map에서 삭제된다.
   */
    ['removeLineAll'](_0x4be519) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeLineAll]\x20excludedKeys:' + _0x4be519), this.#objLayer['removeLineAll'](_0x4be519);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지도 이동할 때 라인 그리기 여부를 결정한다.
   *  logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
   *  logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)
   * @param {Boolean} drawing 그리기 여부
   * @example
   *  logiMap.setDrawingLineOnMove(false);
   *  //지도 이동할 때는 라인을 그리지 않는다.
   */
    ['setDrawingLineOnMove'](_0x175eab) {
        this.#objLayer['setDrawingLineOnMove'](_0x175eab);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Polygon을 Map에 추가한다
   * @param {logi.maps.Polygon} polygon 추가할 폴리곤
   * @returns {logi.maps.Polygon} 추가된 폴리곤
   * @example
   *  let polygon = new logi.maps.Polygon([
   *  {lat: 37.5115557, lng: 127.0595261},
   *  {lat: 37.5062379, lng: 127.0050378},
   *  {lat: 37.5665960, lng: 127.0077020},
   *  {lat: 37.5115557, lng: 127.0595261}
   *  ], 'gray');
   *  logiMap.addPolygon(polygon);
   *  //생성된 polygon이 Map에 추가된다.
   */
    ['addPolygon'](_0xdd05e9) {
        return logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[addPolygon]\x20key:' + _0xdd05e9['getKey']()), this.#objLayer['addPolygon'](_0xdd05e9);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  key 값으로 등록된 Polygon이 있는지 확인한다.
   * @param {String} objKey 폴리곤 Key
   * @returns {Boolean} 등록 여부
   * @example
   *  let exist = logiMap.isExistPolygon(‘key-01’);
   *  //‘key-01’로 등록된 polygon이 있으면 true 없으면 false
   */
    ['isExistPolygon'](_0xce687c) {
        return this.#objLayer['isExistPolygon'](_0xce687c);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.
   *  - key, rect 또는 class 값으로 등록된 Polygon을 찾는다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 폴리곤 key
   *  @param {logi.maps.LatLng[]} [keyword.rect] 영역 rect
   *  @param {String} [keyword.class] 폴리곤 class
   * @returns {logi.maps.Polygon | logi.maps.Polygon[]}
   *  - logi.maps.Polygon: key 검색
   *  - logi.maps.Polygon[]: rect 또는 class 검색
   * @example
   *  let polygon = logiMap.findPolygon(‘key-01’);
   *  //‘key-01’로 등록된 polygon이 전달된다.
   * @example
   *  let polygon = logiMap.findPolygon({key: ‘key-01’});
   *  //key 값으로 등록된 polygon이 전달된다.
   * @example
   *  let polygons = logiMap.findPolygon({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
   *  //영역에 포함되는 polygon 배열이 전달된다.
   * @example
   *  let polygons = logiMap.findPolygon({class: ‘ply’});
   *  //class 값으로 등록된 polygon 배열이 전달된다.
   */
    ['findPolygon'](_0x3bee41) {
        return this.#objLayer['findPolygon'](_0x3bee41);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.
   *  - key 또는 class 값으로 등록된 Polygon을 Map에서 삭제한다.
   *  - keyword 타입이 String이면 key로 처리된다.
   *  - key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 폴리곤 key
   *  @param {String} [keyword.class] 폴리곤 class
   * @example
   *  logiMap.removePolygon(‘key-01’);
   * @example
   *  logiMap.removePolygon({key: ‘key-01’});
   *  //key 값으로 등록된 Polygon이 Map에서 삭제된다.
   * @example
   *  logiMap.removePolygon({class: ‘ply’});
   *  //class 값으로 등록된 Polygon들이 Map에서 삭제된다.
   */
    ['removePolygon'](_0x3fa131) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removePolygon]\x20keyword:' + _0x3fa131), this.#objLayer['removePolygon'](_0x3fa131);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 모든 Polygon을 삭제한다.
   *  excludedKeys에 값이 있다면 해당 key를 가진 Polygon은 삭제 대상에서 제외된다.
   * @param {String[]} excludedKeys 제외할 폴리곤 Keys (default: undefined)
   * @example
   *  logiMap.removePolygonAll();
   *  //등록된 모든 Polygon을 삭제한다.
   * @example
   *  logiMap.removePolygonAll([‘key-01’]);
   *  //key 값으로 등록된 Polygon만 제외되고 모두 Map에서 삭제된다.
   */
    ['removePolygonAll'](_0x2e04b3) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removePolygonAll]\x20excludedKeys:' + _0x2e04b3), this.#objLayer['removePolygonAll'](_0x2e04b3);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  입력된 화면 좌표와 겹치는 Polygon을 찾는다.
   * @param {Number} x 스크린 좌표 X
   * @param {Number} y 스크린 좌표 Y
   * @returns {logi.maps.Polygon} 좌표와 겹치는 폴리곤
   * @example
   *  let polygon = logiMap.hitPolygon(250, 250);
   *  //스크린 좌표 (250, 250)에 있는 polygon이 전달된다.
   */
    ['hitPolygon'](_0x28c533, _0x4ec2a0) {
        return this.#objLayer['hitPolygon'](_0x28c533, _0x4ec2a0);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  입력된 화면 좌표와 겹치는 Polygon의 key를 찾는다.
   * @param {Number} x 스크린 좌표 X
   * @param {Number} y 스크린 좌표 Y
   * @returns {String} 좌표와 겹치는 폴리곤 Key
   * @example
   *  let polygonKey = logiMap.hitPolygonKey(250, 250);
   *  //스크린 좌표 (250, 250)에 있는 polygon의 key가 전달된다.
   */
    ['hitPolygonKey'](_0x13771e, _0x1df659) {
        const _0xbe7878 = this.#objLayer['hitPolygon'](_0x13771e, _0x1df659);
        if (_0xbe7878)
            return _0xbe7878['getKey']();
        return '';
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지도 이동할 때 폴리곤 그리기 여부를 결정한다.
   *  logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
   *  logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)
   * @param {Boolean} drawing 그리기 여부
   * @example
   *  logiMap.setDrawingPolygonOnMove(false);
   *  //지도 이동할 때는 폴리곤을 그리지 않는다.
   */
    ['setDrawingPolygonOnMove'](_0x3b22f8) {
        this.#objLayer['setDrawingPolygonOnMove'](_0x3b22f8);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Circle을 Map에 추가한다
   * @param {logi.maps.Circle} circle 추가할 원
   * @returns {logi.maps.Circle} 추가된 원
   * @example
   *  let circle = new logi.maps.Circle(
   *  {lat: 37.5115557, lng: 127.0595261},
   *  64.0, 'gray');
   *  logiMap.addCircle(circle);
   *  //생성된 circle이 Map에 추가된다.
   */
    ['addCircle'](_0x3fc512) {
        return logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[addCircle]\x20key:' + _0x3fc512['getKey']()), this.#objLayer['addCircle'](_0x3fc512);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  key 값으로 등록된 Circle이 있는지 확인한다.
   * @param {String} objKey 원 Key
   * @returns {Boolean} 등록 여부
   * @example
   *  let exist = logiMap.isExistCircle(‘key-01’);
   *  //‘key-01’로 등록된 cirlce이 있으면 true 없으면 false
   */
    ['isExistCircle'](_0x40eedc) {
        return this.#objLayer['isExistCircle'](_0x40eedc);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.
   *  - key, rect 또는 class 값으로 등록된 Circle을 찾는다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 원 key
   *  @param {logi.maps.LatLng[]} [keyword.rect] 영역 rect
   *  @param {String} [keyword.class] 원 class
   * @returns {logi.maps.Circle | logi.maps.Circle[]}
   *  - logi.maps.Circle: key 검색
   *  - logi.maps.Circle[]: rect 또는 class 검색
   * @example
   *  let circle = logiMap.findCircle(‘key-01’);
   *  //‘key-01’로 등록된 circle이 전달된다.
   * @example
   *  let circle = logiMap.findCircle({key: ‘key-01’});
   *  //key 값으로 등록된 circle이 전달된다.
   * @example
   *  let circles = logiMap.findCircle({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
   *  //영역에 포함되는 circle 배열이 전달된다.
   * @example
   *  let circles = logiMap.findCircle({class: ‘cir’});
   *  //class 값으로 등록된 circle 배열이 전달된다.
   */
    ['findCircle'](_0x9f25fc) {
        return this.#objLayer['findCircle'](_0x9f25fc);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.
   *  - key 또는 class 값으로 등록된 Circle을 Map에서 삭제한다.
   *  - keyword 타입이 String이면 key로 처리된다.
   *  - key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 원 key
   *  @param {String} [keyword.class] 원 class
   * @example
   *  logiMap.removeCircle(‘key-01’);
   * @example
   *  logiMap.removeCircle({key: ‘key-01’});
   *  //key 값으로 등록된 Circle이 Map에서 삭제된다.
   * @example
   *  logiMap.removeCircle({class: ‘cir’});
   *  //class 값으로 등록된 Circle들이 Map에서 삭제된다.
   */
    ['removeCircle'](_0x88bf0c) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeCircle]\x20keyword:' + _0x88bf0c), this.#objLayer['removeCircle'](_0x88bf0c);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 모든 Circe을 삭제한다.
   *  excludedKeys에 값이 있다면 해당 key를 가진 Circle은 삭제 대상에서 제외된다.
   * @param {String[]} excludedKeys 제외할 폴리곤 Keys (default: undefined)
   * @example
   *  logiMap.removeCircleAll();
   *  //등록된 모든 Circle을 삭제한다.
   * @example
   *  logiMap.removeCircleAll([‘key-01’]);
   *  //key 값으로 등록된 Circle만 제외되고 모두 Map에서 삭제된다.
   */
    ['removeCircleAll'](_0x5618a9) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeCircleAll]\x20excludedKeys:' + _0x5618a9), this.#objLayer['removeCircleAll'](_0x5618a9);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  입력된 화면 좌표와 겹치는 Circle을 찾는다.
   * @param {Number} x 스크린 좌표 X
   * @param {Number} y 스크린 좌표 Y
   * @returns {logi.maps.Circle} 좌표와 겹치는 원
   * @example
   *  let circle = logiMap.hitCircle(250, 250);
   *  //스크린 좌표 (250, 250)에 있는 circle이 전달된다.
   */
    ['hitCircle'](_0x95929c, _0x3b0516) {
        return this.#objLayer['hitCircle'](_0x95929c, _0x3b0516);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  입력된 화면 좌표와 겹치는 Circle의 key를 찾는다.
   * @param {Number} x 스크린 좌표 X
   * @param {Number} y 스크린 좌표 Y
   * @returns {String} 좌표와 겹치는 원 Key
   * @example
   *  let circleKey = logiMap.hitCircleKey(250, 250);
   *  //스크린 좌표 (250, 250)에 있는 circle의 key가 전달된다.
   */
    ['hitCircleKey'](_0x2a413a, _0x57e025) {
        const _0x3253c3 = this.#objLayer['hitCircle'](_0x2a413a, _0x57e025);
        if (_0x3253c3)
            return _0x3253c3['getKey']();
        return '';
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지도 이동할 때 원 그리기 여부를 결정한다.
   *  logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
   *  logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)
   * @param {Boolean} drawing 그리기 여부
   * @example
   *  logiMap.setDrawingCircleOnMove(false);
   *  //지도 이동할 때는 원을 그리지 않는다.
   */
    ['setDrawingCircleOnMove'](_0x5b6dc9) {
        this.#objLayer['setDrawingCircleOnMove'](_0x5b6dc9);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Route를 Map에 추가한다
   * @param {logi.maps.Route} route 추가할 경로
   * @returns {logi.maps.Route} 추가된 경로
   * @example
   *  let route = new logi.maps.Route([
   *  {lat: 37.5115557, lng: 127.0595261},
   *  {lat: 37.5062379, lng: 127.0050378},
   *  {lat: 37.5665960, lng: 127.0077020}], {
   *  routeLine: {width: 4, color: ‘#0088FF’}});
   *  logiMap.addRoute(route);
   *  //생성된 route를 Map에 추가된다.
   */
    ['addRoute'](_0x82491d) {
        return logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[addRoute]\x20key:' + _0x82491d['getKey']()), this.#objLayer['addRoute'](_0x82491d);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  key 값으로 등록된 Route가 있는지 확인한다.
   * @param {String} objKey 라우트 Key
   * @returns {Boolean} 등록 여부
   * @example
   *  let exist = logiMap.isExistRoute(‘key-01’);
   *  //‘key-01’로 등록된 route가 있으면 true 없으면 false
   */
    ['isExistRoute'](_0x21414c) {
        return this.#objLayer['isExistRoute'](_0x21414c);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key, rect 또는 class로 구성 할 수 있다.
   *  - key, rect 또는 class 값으로 등록된 Route를 찾는다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 경로 key
   *  @param {logi.maps.LatLng[]} [keyword.rect] 영역 rect
   *  @param {String} [keyword.class] 경로 class
   * @returns {logi.maps.Route | logi.maps.Route[]}
   *  - logi.maps.Route: key 검색
   *  - logi.maps.Route[]: rect 또는 class 검색
   * @example
   *  let route = logiMap.findRoute(‘key-01’);
   *  //‘key-01’로 등록된 route가 전달된다.
   * @example
   *  let route = logiMap.findRoute({key: ‘key-01’});
   *  //key 값으로 등록된 route가 전달된다.
   * @example
   *  let routes = logiMap.findRoute({rect: [{lat: 50.35958, lng: -103.86657},{lat: 48.76354, lng: -101.09801}]});
   *  //영역에 포함되는 route 배열이 전달된다.
   * @example
   *  let routes = logiMap.findRoute({class: ‘rt’});
   *  //class 값으로 등록된 route 배열이 전달된다.
   */
    ['findRoute'](_0x51b3da) {
        return this.#objLayer['findRoute'](_0x51b3da);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.
   *  - key 또는 class 값으로 등록된 Route를 Map에서 삭제한다.
   *  - keyword 타입이 String이면 key로 처리된다.
   *  - key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 경로 key
   *  @param {String} [keyword.class] 경로 class
   * @example
   *  logiMap.removeRoute(‘key-01’);
   * @example
   *  logiMap.removeRoute({key: ‘key-01’});
   *  //key 값으로 등록된 Route가 Map에서 삭제된다.
   * @example
   *  logiMap.removeRoute({class: ‘rt’});
   *  //class 값으로 등록된 Route들이 Map에서 삭제된다.
   */
    ['removeRoute'](_0x357110) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeRoute]\x20keyword:' + _0x357110), this.#objLayer['removeRoute'](_0x357110);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 모든 Route를 삭제한다.
   *  excludedKeys에 값이 있다면 해당 key를 가진 Route는 삭제 대상에서 제외된다.
   * @param {String[]} excludedKeys 제외할 경로 Keys (default: undefined)
   * @example
   *  logiMap.removeRouteAll();
   *  //등록된 모든 Route를 삭제한다.
   * @example
   *  logiMap.removeRouteAll([‘key-01’]);
   *  //key 값으로 등록된 Route만 제외되고 모두 Map에서 삭제된다.
   */
    ['removeRouteAll'](_0x22df67) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeRouteAll]\x20excludedKeys:' + _0x22df67), this.#objLayer['removeRouteAll'](_0x22df67);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지도 이동할 때 경로 그리기 여부를 결정한다.
   *  logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
   *  logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)
   * @param {Boolean} drawing 그리기 여부
   * @example
   *  logiMap.setDrawingRouteOnMove(false);
   *  //지도 이동할 때는 경로를 그리지 않는다.
   */
    ['setDrawingRouteOnMove'](_0x5a471c) {
        this.#objLayer['setDrawingRouteOnMove'](_0x5a471c);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  GPS를 Map에 추가한다
   * @param {logi.maps.Gps} gps 추가할 GPS
   * @returns {logi.maps.Gps} 추가된 GPS
   * @example
   *  ...
   */
    ['addGps'](_0x550cb8) {
        return logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[addGps]\x20key:' + _0x550cb8['getKey']()), this.#objLayer['addGps'](_0x550cb8);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  key 값으로 등록된 GPS가 있는지 확인한다.
   * @param {String} objKey GPS Key
   * @returns {Boolean} 등록 여부
   * @example
   *  let exist = logiMap.isExistGps(‘key-01’);
   *  //‘key-01’로 등록된 gps가 있으면 true 없으면 false
   */
    ['isExistGps'](_0x2fb8e3) {
        return this.#objLayer['isExistGps'](_0x2fb8e3);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.
   *  - key 또는 class 값으로 등록된 GPS를 찾는다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] GPS key
   *  @param {String} [keyword.class] GPS class
   * @returns {logi.maps.Gps | logi.maps.Gps[]}
   *  - logi.maps.Gps: key 검색
   *  - logi.maps.Gps[]: class 검색
   * @example
   *  let gps = logiMap.findGps(‘key-01’);
   *  //‘key-01’로 등록된 gps가 전달된다.
   * @example
   *  let gps = logiMap.findGps({key: ‘key-01’});
   *  //key 값으로 등록된 gps가 전달된다.
   * @example
   *  let gpss = logiMap.findGps({class: ‘gps’});
   *  //class 값으로 등록된 gps 배열이 전달된다.
   */
    ['findGps'](_0x5b337d) {
        return this.#objLayer['findGps'](_0x5b337d);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.
   *  - key 또는 class 값으로 등록된 GPS를 Map에서 삭제한다.
   *  - keyword 타입이 String이면 key로 처리된다.
   *  - key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] GPS key
   *  @param {String} [keyword.class] GPS class
   * @example
   *  logiMap.removeGps(‘key-01’);
   * @example
   *  logiMap.removeGps({key: ‘key-01’});
   *  //key 값으로 등록된 GPS가 Map에서 삭제된다.
   * @example
   *  logiMap.removeGps({class: ‘gps’});
   *  //class 값으로 등록된 GPS들이 Map에서 삭제된다.
   */
    ['removeGps'](_0xb925eb) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeGps]\x20keyword:' + _0xb925eb), this.#objLayer['removeGps'](_0xb925eb);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 모든 GPS를 삭제한다.
   *  excludedKeys에 값이 있다면 해당 key를 가진 GPS는 삭제 대상에서 제외된다.
   * @param {String[]} excludedKeys 제외할 경로 Keys (default: undefined)
   * @example
   *  logiMap.removeGpsAll();
   *  //등록된 모든 GPS를 삭제한다.
   * @example
   *  logiMap.removeGpsAll([‘key-01’]);
   *  //key 값으로 등록된 GPS만 제외되고 모두 Map에서 삭제된다.
   */
    ['removeGpsAll'](_0x477725) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeGpsAll]\x20excludedKeys:' + _0x477725), this.#objLayer['removeGpsAll'](_0x477725);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지도 이동할 때 경로 그리기 여부를 결정한다.
   *  logiMap.setFreezeModeOnMoving(true)가 기본값으로 설정되어 있어서 logiMap.setDrawingImageOnMove(false)를 호출하더라도 적용되지 않는다.
   *  logiMap.setFreezeModeOnMoving(false)로 FreezeMode를 우선 꺼야함 (퍼포먼스 측면에서는 FreezeMode가 더 우수함)
   * @param {Boolean} drawing 그리기 여부
   * @example
   *  logiMap.setDrawingGpsOnMove(false);
   *  //지도 이동할 때는 경로를 그리지 않는다.
   */
    ['setDrawingGpsOnMove'](_0x16f59a) {
        this.#objLayer['setDrawingGpsOnMove'](_0x16f59a);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  커스텀 객체를 Map에 추가한다.
   *  overlay에 그려지는 요소라서 기존 요소(이미지, 라벨 등) 보다 가장 상위에 표시 됨
   * @param {logi.maps.Custom} custom 추가할 커스텀 객체
   * @returns {logi.maps.Custom} 추가된 커스텀 객체
   * @example
   *  let custom = new logi.maps.Custom({lat: 37.5115557, lng: 127.0595261}, {content: '<div>custom</div>'});
   *  logiMap.addCustom(custom);
   *  //생성된 커스텀 객체가 Map에 추가된다.
   */
    ['addCustom'](_0xcd7398) {
        return logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[addCustom]\x20key:' + _0xcd7398['getKey']()), this.#overlayLayer['addCustom'](_0xcd7398);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  - keyword(Object)의 프로퍼티는 key 또는 class로 구성 할 수 있다.
   *  - key 또는 class 값으로 등록된 커스텀 객체를 Map에서 삭제한다.
   *  - keyword 타입이 String이면 key로 처리된다.
   *  - key와 class 값이 둘 다 지정되어 있다면 class로 처리된다.
   * @param {Object} keyword keyword
   *  @param {String} [keyword.key] 커스텀 객체 key
   *  @param {String} [keyword.class] 커스텀 객체 class
   * @example
   *  logiMap.removeCustom(‘key-01’);
   * @example
   *  logiMap.removeCustom({key: ‘key-01’});
   *  //key 값으로 등록된 커스텀 객체가 Map에서 삭제된다.
   * @example
   *  logiMap.removeCustom({class: ‘pin’});
   *  //class 값으로 등록된 커스텀 객체들이 Map에서 삭제된다.
   */
    ['removeCustom'](_0x4d5813) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeCustom]\x20keyword:' + _0x4d5813), this.#overlayLayer['removeCustom'](_0x4d5813);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 모든 커스텀 객체를 삭제한다.
   *  excludedKeys에 값이 있다면 해당 key를 가진 커스텀 객체는 삭제 대상에서 제외된다.
   * @param {String[]} excludedKeys 제외할 커스텀 객체 Keys (default: undefined)
   * @example
   *  logiMap.removeCustomAll();
   *  //등록된 모든 커스텀 객체를 삭제한다.
   * @example
   *  logiMap.removeCustomAll([‘key-01’]);
   *  //key 값으로 등록된 커스텀 객체만 제외되고 모두 Map에서 삭제된다.
   */
    ['removeCustomAll'](_0x3ca3ec) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeCusomAll]\x20excludedKeys:' + _0x3ca3ec), this.#overlayLayer['removeCustomAll'](_0x3ca3ec);
    }
    #addMeta(_0x64040c) {
        return logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[addMeta]\x20key:' + _0x64040c['getKey']()), this.#overlayLayer['addMeta'](_0x64040c);
    }
    #removeMeta(_0x1f22ba) {
        logi['maps']['Defines']['LOG_MODE'] && console['log']('[logi.maps]\x20[removeMeta]\x20keyword:' + _0x1f22ba), this.#overlayLayer['removeMeta'](_0x1f22ba);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  스크린 좌표를 월드 좌표로 변환한다.
   * @param {logi.maps.Point} point 스크린 좌표
   * @param {Number} zoom 지도 스케일 (default: -1)
   * @returns {logi.maps.LatLng} 월드 좌표
   * @example
   *  let world = logiMap.screen2world({x: 200, y: 150});
   *  //{lat: 37.544773, lng: 127.045735} 월드 좌표로 변환된다.
   */
    ['screen2world'](_0x4edb38, _0xc9985a = -0x1) {
        return _0xc9985a >= 0x0 ? this.#mapCoord['screen2world'](_0x4edb38['x'], _0x4edb38['y'], { 'zoomScale': _0xc9985a }) : this.#mapCoord['screen2world'](_0x4edb38['x'], _0x4edb38['y']);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  월드 좌표를 스크린 좌표로 변환한다.
   * @param {logi.maps.LatLng} latlng 월드 좌표
   * @param {Number} zoom 지도 스케일 (default: -1)
   * @returns {logi.maps.Point} 스크린 좌표
   * @example
   *  let screen = logiMap.world2screen({lat: 37.544773, lng: 127.045735});
   *  //{x: 200, y: 150} 스크린 좌표로 변환된다.
   */
    ['world2screen'](_0x169340, _0x4c87fa = -0x1) {
        return _0x4c87fa >= 0x0 ? this.#mapCoord['world2screen'](_0x169340['lng'], _0x169340['lat'], { 'zoomScale': _0x4c87fa }) : this.#mapCoord['world2screen'](_0x169340['lng'], _0x169340['lat']);
    }
    ['world2plane'](_0x5df1ed, _0x24a439 = -0x1) {
        return _0x24a439 >= 0x0 ? this.#mapCoord['world2plane'](_0x5df1ed['lng'], _0x5df1ed['lat'], { 'zoomScale': _0x24a439 }) : this.#mapCoord['world2plane'](_0x5df1ed['lng'], _0x5df1ed['lat']);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  두 월드 좌표의 거리를 meter 단위로 계산한다.
   * @param {logi.maps.LatLng} fromLatLng 시작 월드 좌표
   * @param {logi.maps.LatLng} toLatLng 끝 월드 좌표
   * @returns {Number} meter 거리
   * @example
   *  let distance = logiMap.getRealDistance({lat:37.679709, lng:126.842542}, {lat: 37.501278, lng: 127.233375});
   *  //39742.926 (meter) 거리가 계산된다.
   */
    ['getRealDistance'](_0xe6cd36, _0x5c9fed) {
        const _0x47390a = 0x6136b8, _0x10e777 = logi['maps']['Utils']['degToRad'](_0xe6cd36['lat']), _0x5d3552 = logi['maps']['Utils']['degToRad'](_0x5c9fed['lat']), _0x1f2956 = logi['maps']['Utils']['degToRad'](_0x5c9fed['lat'] - _0xe6cd36['lat']), _0x576ed3 = logi['maps']['Utils']['degToRad'](_0x5c9fed['lng'] - _0xe6cd36['lng']), _0x49fc43 = Math['sin'](_0x1f2956 * 0.5), _0xf16532 = Math['sin'](_0x576ed3 * 0.5), _0x2906fc = _0x49fc43 * _0x49fc43 + Math['cos'](_0x10e777) * Math['cos'](_0x5d3552) * _0xf16532 * _0xf16532, _0x317b96 = 0x2 * Math['atan2'](Math['sqrt'](_0x2906fc), Math['sqrt'](0x1 - _0x2906fc));
        return _0x47390a * _0x317b96;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라인의 길이를 1.0으로 했을 경우 ratio(0.0~1.0) 위치의 좌표와 각도를 계산한다.
   * @param {logi.maps.LatLng[]} latlngs 시작 월드 좌표
   * @param {Number} ratio 끝 월드 좌표
   * @returns {{lat:Number, lng:Number, deg:Number}} 위치 정보 (위도, 경도, 각도)
   * @example
   *  let distance = logiMap.getRealDistance({lat:37.679709, lng:126.842542}, {lat: 37.501278, lng: 127.233375});
   *  //39742.926 (meter) 거리가 계산된다.
   */
    ['getSplitInfoOnPolyline'](_0x3ceaac, _0x4c7eec) {
        return logi['maps']['Utils']['getSplitInfoOnPolyline'](_0x3ceaac, _0x4c7eec);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  입력된 지점과 가장 가까운 라인의 점을 계산한다.
   * @param {logi.maps.LatLng[]} latlngs 대상 라인
   * @param {logi.maps.LatLng} latlng 기준점
   * @returns {{lat:Number, lng:Number, distance:Number}} 가장 가까운 지점 정보 (경도, 위도, 거리)
   * @example
   *  let result = logiMap.getNearestInfoOnPolyline([{lat: 37.54248, lng:127.04511}, {lat: 37.5427, lng: 127.04447}, {lat: 37.54304, lng:127.04457}], {lat:37.542816, lng:127.045103});
   *  //{lat: 37.542517, lng: 127.045000, distance: 0.000315}의 결과 값을 얻는다.
   */
    ['getNearestInfoOnPolyline'](_0x2fba09, _0x1dc0af) {
        let _0x35038f = {
                'lat': 0x0,
                'lng': 0x0,
                'distance': Infinity
            }, _0x1065a4 = null;
        for (let _0xb4c4a8 of _0x2fba09) {
            if (_0x1065a4 != null) {
                const _0x40c61d = logi['maps']['Utils']['getPerpendicularLatLng'](_0x1065a4, _0xb4c4a8, _0x1dc0af);
                _0x40c61d['distance'] < _0x35038f['distance'] && (_0x35038f['lng'] = _0x40c61d['lng'], _0x35038f['lat'] = _0x40c61d['lat'], _0x35038f['distance'] = _0x40c61d['distance']);
            }
            _0x1065a4 = _0xb4c4a8;
        }
        return _0x35038f;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  좌표배열과 확장 값을 입력하면 모든 좌표를 포함하는 폴리곤 좌표 리스트를 리턴한다.
   * @param {logi.maps.LatLng[]} latlngs 좌표 배열
   * @param {Number} expandMeter 확장값
   * @returns {logi.maps.LatLng[]} 폴리곤 좌표 배열
   * @example
   *  let convaxHull = logiMap. getConvaxHull([
   *  {lat: 37.5115557, lng: 127.0595261},
   *  {lat: 37.5062379, lng: 127.0050378},
   *  {lat: 37.566596, lng: 127.007702}
   *  ], 10);
   *  //ConvaxHull 계산을 하여 폴리곤 좌표가 리턴된다.
   */
    ['getConvaxHull'](_0x1c689a, _0x2dfda4) {
        const _0x1dcab9 = logi['maps']['Utils']['convexHull'](_0x1c689a), _0x8608ad = new Array();
        for (const _0x12e84f of _0x1dcab9) {
            _0x8608ad['push'](...logi['maps']['Utils']['getEllipseLatLng'](_0x12e84f['lng'], _0x12e84f['lat'], _0x2dfda4 * 0.00001));
        }
        const _0x2df4db = logi['maps']['Utils']['convexHull'](_0x8608ad);
        return _0x2df4db['length'] <= 0x2 ? [] : _0x2df4db;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  히트맵 데이터를 렌더링합니다.
   * @param {Array.<{latlng: {lat: Number, lng: Number}, intensity: Number}>} data 좌표 및 강도 정보 배열
   * @example
   *  logiMap.setHeatmap([
   *  {latlng: {lat: 37.5115557, lng: 127.0595261}, intensity: 0.5},
   *  {latlng: {lat: 37.5062379, lng: 127.0050378}, intensity: 1.0},
   *  {latlng: {lat: 37.566596, lng: 127.007702}, intensity: 0.4}
   *  ]);
   *  //히트맵 데이터가 그려진다.
   */
    ['setHeatmap'](_0x3c3037, _0x13401d, _0x5f1998) {
        this.#heatmapLayer['setData'](_0x3c3037, _0x13401d, _0x5f1998);
    }
    #getWindowDevicePixelRatio() {
        return window['devicePixelRatio'];
    }
    #setScreenSize(_0x3680dc, _0x4fbe05) {
        if (_0x3680dc['width'] == undefined || _0x3680dc['height'] == undefined || _0x4fbe05 == undefined)
            return;
        if (this.#mapScreenSize['width'] != _0x3680dc['width'] || this.#mapScreenSize['height'] != _0x3680dc['height'] || this.#devicePixelRatio != _0x4fbe05) {
            this.#mapScreenSize['width'] = _0x3680dc['width'], this.#mapScreenSize['height'] = _0x3680dc['height'], this.#devicePixelRatio = _0x4fbe05;
            for (let _0x3a0a10 of this.#mapLayers) {
                _0x3a0a10['setScreenSize'](this.#mapScreenSize['width'], this.#mapScreenSize['height'], this.#devicePixelRatio);
            }
            this.#mapCoord['setScreenSize'](this.#mapScreenSize['width'], this.#mapScreenSize['height'], this.#devicePixelRatio), this.#mapCoord['adjustMapBoundRect'](), this['updateMap']();
        }
    }
    #initEvent() {
        this.#isMobileEnv && (this.#eventLayer['addEventListener']('touchstart', _0x438fbb => this.#motionEventHandler(_0x438fbb)), this.#eventLayer['addEventListener']('touchmove', _0x66e875 => this.#motionEventHandler(_0x66e875)), this.#eventLayer['addEventListener']('touchend', _0x22fd25 => this.#motionEventHandler(_0x22fd25))), this.#eventLayer['addEventListener']('contextmenu', _0x54ec79 => _0x54ec79['preventDefault']()), this.#eventLayer['addEventListener']('dblclick', _0x466b90 => this.#motionEventHandler(_0x466b90)), this.#eventLayer['addEventListener']('click', _0xdb8675 => this.#motionEventHandler(_0xdb8675)), this.#eventLayer['addEventListener']('mousedown', _0x465ed6 => this.#motionEventHandler(_0x465ed6)), this.#eventLayer['addEventListener']('mouseup', _0x1e55dd => this.#motionEventHandler(_0x1e55dd)), this.#eventLayer['addEventListener']('mousemove', _0x5289db => this.#motionEventHandler(_0x5289db)), this.#eventLayer['addEventListener']('wheel', _0x124035 => this.#motionEventHandler(_0x124035)), this.#eventLayer['addEventListener']('mouseleave', _0x4919ba => this.#motionEventHandler(_0x4919ba));
    }
    #getTouchPoints(_0x1d65e6) {
        const _0x536153 = this.#eventLayer['getBoundingClientRect']();
        let _0x13184d = new Array();
        for (let _0x174ebb of _0x1d65e6) {
            _0x13184d['push']({
                'x': _0x174ebb['clientX'] - _0x536153['left'],
                'y': _0x174ebb['clientY'] - _0x536153['top']
            });
        }
        return _0x13184d;
    }
    #getMousePoint(_0x37f4ff) {
        const _0x2fa6c7 = this.#eventLayer['getBoundingClientRect']();
        return {
            'x': _0x37f4ff['clientX'] - _0x2fa6c7['left'],
            'y': _0x37f4ff['clientY'] - _0x2fa6c7['top']
        };
    }
    #panMap(_0x2ea038, _0x4d22c3, _0x3d6413, _0x418f12) {
        this.#mapCoord['panMap'](_0x2ea038, _0x4d22c3, _0x3d6413, _0x418f12), this.#mapCoord['adjustMapBoundRect'](), this['updateMap']();
    }
    #motionEventHandler(_0x35f02b) {
        if (this.#uiLayer['getDragAreaMode']() == !![]) {
            if (_0x35f02b['type'] == 'touchstart' || _0x35f02b['type'] == 'touchmove' || _0x35f02b['type'] == 'touchend') {
                if (_0x35f02b['type'] == 'touchstart') {
                    const _0x355b20 = this.#getTouchPoints(_0x35f02b['target']['getBoundingClientRect'](), _0x35f02b['targetTouches']);
                    this.#uiLayer['setDragAreaStart'](_0x355b20[0x0]['x'], _0x355b20[0x0]['y']);
                } else {
                    if (_0x35f02b['type'] == 'touchmove') {
                        const _0xf944cf = this.#getTouchPoints(_0x35f02b['target']['getBoundingClientRect'](), _0x35f02b['targetTouches']);
                        this.#uiLayer['setDragAreaChange'](_0xf944cf[0x0]['x'], _0xf944cf[0x0]['y']);
                    } else {
                        if (_0x35f02b['type'] == 'touchend') {
                            const _0x4805e9 = this.#getTouchPoints(_0x35f02b['target']['getBoundingClientRect'](), _0x35f02b['changedTouches']);
                            this.#uiLayer['setDragAreaChange'](_0x4805e9[0x0]['x'], _0x4805e9[0x0]['y']), this.#uiLayer['setDragAreaEnd']();
                        }
                    }
                }
            } else {
                if (_0x35f02b['type'] == 'mousedown')
                    this.#uiLayer['setDragAreaStart'](_0x35f02b['offsetX'], _0x35f02b['offsetY']);
                else {
                    if (_0x35f02b['type'] == 'mouseup')
                        this.#uiLayer['setDragAreaEnd']();
                    else {
                        if (_0x35f02b['type'] == 'mousemove')
                            this.#uiLayer['setDragAreaChange'](_0x35f02b['offsetX'], _0x35f02b['offsetY']);
                        else
                            _0x35f02b['type'] == 'mouseleave' && this.#uiLayer['setDragAreaEnd']();
                    }
                }
            }
            return;
        }
        if (this.#motionEventLock == ![]) {
            if (_0x35f02b['type'] == 'touchstart' || _0x35f02b['type'] == 'touchmove' || _0x35f02b['type'] == 'touchend') {
                _0x35f02b['targetTouches']['length'] <= 0x1 || _0x35f02b['type'] == 'touchend' ? this.#multiTouchActive = ![] : (this.#multiTouchActive = !![], this.#isDragging = ![]);
                if (_0x35f02b['type'] == 'touchstart') {
                    this.#mapDiv['focus']();
                    let _0x1c7716 = this.#getTouchPoints(_0x35f02b['touches']);
                    this.#multiTouchActive ? (this.#touchSingleDownStatus = ![], this.#touchPrevPoints = _0x1c7716) : this.#touchSingleDown(_0x1c7716[0x0]['x'], _0x1c7716[0x0]['y']);
                } else {
                    if (_0x35f02b['type'] == 'touchmove') {
                        let _0x32e328 = this.#getTouchPoints(_0x35f02b['touches']);
                        this.#multiTouchActive ? (this.#touchSingleDownStatus = ![], this.#touchMultiScale(_0x32e328)) : this.#touchSingleMove(_0x32e328[0x0]['x'], _0x32e328[0x0]['y']);
                    } else {
                        if (_0x35f02b['type'] == 'touchend') {
                            let _0x4f4388 = this.#getTouchPoints(_0x35f02b['touches']);
                            this.#touchUp(_0x4f4388[0x0]['x'], _0x4f4388[0x0]['y']);
                        }
                    }
                }
            } else {
                if (_0x35f02b['type'] == 'dblclick')
                    this['OnDoubleClick']?.(_0x35f02b);
                else {
                    if (_0x35f02b['type'] == 'click')
                        this['OnClick']?.(_0x35f02b);
                    else {
                        if (_0x35f02b['type'] == 'mousedown') {
                            this.#mapDiv['focus']();
                            if (_0x35f02b['buttons'] == 0x1) {
                                const _0x1080c2 = this.#getMousePoint(_0x35f02b);
                                this.#mouseLButtonDown(_0x1080c2['x'], _0x1080c2['y']);
                            } else
                                this.#mouseLButtonDownStatus = ![];
                            this['OnMouseDown']?.(_0x35f02b);
                        } else {
                            if (_0x35f02b['type'] == 'mouseup') {
                                const _0x5d6f7f = this.#mouseLButtonDownStatus == !![] && this.#isDragging == ![];
                                this.#mouseLButtonUp(this.#mousePrevPoint['x'], this.#mousePrevPoint['y']), _0x5d6f7f && this.#eventLayer['dispatchEvent'](new CustomEvent(logi['maps']['EVENT']['click2'], {
                                    'detail': { 'originalEvent': _0x35f02b },
                                    'bubbles': !![],
                                    'cancelable': !![]
                                })), this['OnMouseUp']?.(_0x35f02b);
                            } else {
                                if (_0x35f02b['type'] == 'mousemove') {
                                    if (_0x35f02b['buttons'] == 0x1) {
                                        const _0x2310bb = this.#getMousePoint(_0x35f02b);
                                        this.#mouseMove(_0x2310bb['x'], _0x2310bb['y']);
                                    } else
                                        this.#mouseLButtonDownStatus == !![] && this.#mouseLButtonUp(this.#mousePrevPoint['x'], this.#mousePrevPoint['y']);
                                    this.#vtLayer['setMousePos'](_0x35f02b['offsetX'], _0x35f02b['offsetY']), this['OnMouseMove']?.(_0x35f02b);
                                } else {
                                    if (_0x35f02b['type'] == 'wheel')
                                        this.#wheelEventCheck == !![] && (this.#mouseWheel(_0x35f02b['deltaY']), this['OnMapWheel']?.(_0x35f02b));
                                    else
                                        _0x35f02b['type'] == 'mouseleave' && this.#vtLayer['setMousePos'](null, null);
                                }
                            }
                        }
                    }
                }
            }
            this.#objLayer['triggerEvent'](_0x35f02b);
        }
        (_0x35f02b['type'] == 'wheel' || _0x35f02b['type'] == 'touchmove') && _0x35f02b['preventDefault']();
    }
    #touchSingleDown(_0x518b82, _0x1b0168) {
        this.#isDragging = ![], this.#captureFreezeCanvas(), this.#touchSingleDownStatus = !![], this.#touchStartPoint['x'] = _0x518b82, this.#touchStartPoint['y'] = _0x1b0168, this.#touchPrevPoint['x'] = _0x518b82, this.#touchPrevPoint['y'] = _0x1b0168, this.#bridgeMapEvents[logi['maps']['BRIDGE_MAPEVENT']['movestarted']] && logi['maps']['Bridge']['onMapEvent'](logi['maps']['BRIDGE_MAPEVENT']['movestarted']), this['updateMap']();
    }
    #touchUp(_0x5f13ed, _0xb9f9c6) {
        this.#isDragging = ![], this.#bridgeMapEvents[logi['maps']['BRIDGE_MAPEVENT']['movefinished']] && logi['maps']['Bridge']['onMapEvent'](logi['maps']['BRIDGE_MAPEVENT']['movefinished']), this.#touchSingleDownStatus == !![] && (Math['abs'](this.#touchStartPoint['x'] - _0x5f13ed) <= 0x2 && Math['abs'](this.#touchStartPoint['y'] - _0xb9f9c6) <= 0x2 && (this.#bridgeMapEvents[logi['maps']['BRIDGE_MAPEVENT']['touch']] && logi['maps']['Bridge']['onMapEvent'](logi['maps']['BRIDGE_MAPEVENT']['touch'], _0x5f13ed, _0xb9f9c6))), this.#touchSingleDownStatus = ![], this['updateMap']();
    }
    #touchSingleMove(_0x2eae23, _0x454635) {
        this.#touchSingleDownStatus == !![] && ((Math['abs'](this.#touchPrevPoint['x'] - _0x2eae23) > 0x4 || Math['abs'](this.#touchPrevPoint['y'] - _0x454635) > 0x4) && (this.#isDragging = !![], this.#panMap(this.#touchPrevPoint['x'], this.#touchPrevPoint['y'], _0x2eae23, _0x454635), this.#touchPrevPoint['x'] = _0x2eae23, this.#touchPrevPoint['y'] = _0x454635, this.#bridgeMapEvents[logi['maps']['BRIDGE_MAPEVENT']['moved']] && logi['maps']['Bridge']['onMapEvent'](logi['maps']['BRIDGE_MAPEVENT']['moved']), this['updateMap']()));
    }
    #mouseLButtonDown(_0x398005, _0xed816e) {
        this.#isDragging = ![], this.#captureFreezeCanvas(), this.#mouseLButtonDownStatus = !![], this.#mousePrevPoint['x'] = _0x398005, this.#mousePrevPoint['y'] = _0xed816e, this.#bridgeMapEvents[logi['maps']['BRIDGE_MAPEVENT']['movestarted']] && logi['maps']['Bridge']['onMapEvent'](logi['maps']['BRIDGE_MAPEVENT']['movestarted']), this['updateMap']();
    }
    #mouseLButtonUp() {
        this.#isDragging = ![], this.#mouseLButtonDownStatus = ![], this.#bridgeMapEvents[logi['maps']['BRIDGE_MAPEVENT']['movefinished']] && logi['maps']['Bridge']['onMapEvent'](logi['maps']['BRIDGE_MAPEVENT']['movefinished']), this['updateMap']();
    }
    #mouseMove(_0x5962e9, _0x3fa538) {
        this.#mouseLButtonDownStatus != !![] && (this.#isDragging = ![], this.#captureFreezeCanvas(), this.#mouseLButtonDownStatus = !![], this.#mousePrevPoint['x'] = _0x5962e9, this.#mousePrevPoint['y'] = _0x3fa538), (Math['abs'](this.#mousePrevPoint['x'] - _0x5962e9) > 0x4 || Math['abs'](this.#mousePrevPoint['y'] - _0x3fa538) > 0x4) && (this.#isDragging = !![], this.#panMap(this.#mousePrevPoint['x'], this.#mousePrevPoint['y'], _0x5962e9, _0x3fa538), this.#mousePrevPoint['x'] = _0x5962e9, this.#mousePrevPoint['y'] = _0x3fa538, this.#bridgeMapEvents[logi['maps']['BRIDGE_MAPEVENT']['moved']] && logi['maps']['Bridge']['onMapEvent'](logi['maps']['BRIDGE_MAPEVENT']['moved']), this['updateMap']());
    }
    #mouseWheel(_0x1a96aa) {
        if (this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG']) {
            const _0x2f7029 = logi['maps']['Utils']['getCurTick']();
            if (this.#lastWheelTime + 0xc8 > _0x2f7029)
                return;
            this.#lastWheelTime = _0x2f7029;
            if (_0x1a96aa > 0x0)
                this['zoomOut']();
            else
                _0x1a96aa < 0x0 && this['zoomIn']();
        } else {
            const _0x2fc7de = 0x4b0;
            let _0x1d300e = _0x1a96aa / _0x2fc7de;
            this.#accumulateScale(_0x1d300e);
        }
    }
    #touchMultiScale(_0x2b0334) {
        if (this.#touchPrevPoints['length'] >= 0x2 && _0x2b0334['length'] >= 0x2) {
            const _0x277fc2 = Math['sqrt'](Math['pow'](this.#touchPrevPoints[0x0]['x'] - this.#touchPrevPoints[0x1]['x'], 0x2) + Math['pow'](this.#touchPrevPoints[0x0]['y'] - this.#touchPrevPoints[0x1]['y'], 0x2)), _0xc35d95 = Math['sqrt'](Math['pow'](_0x2b0334[0x0]['x'] - _0x2b0334[0x1]['x'], 0x2) + Math['pow'](_0x2b0334[0x0]['y'] - _0x2b0334[0x1]['y'], 0x2)), _0x1c93f8 = 0x28;
            let _0x25b2f9 = (_0x277fc2 - _0xc35d95) * 0.16, _0x5e0996 = _0x25b2f9 / _0x1c93f8;
            this.#accumulateScale(_0x5e0996), this.#touchPrevPoints = _0x2b0334;
        }
    }
    #accumulateScale(_0x3e3232) {
        const _0x726a3c = this.#mapCoord['getTileLevelOffset']();
        if (_0x726a3c > 0x1)
            _0x3e3232 += (_0x726a3c - 0x1) / -0x1;
        else
            _0x726a3c < 0x1 && (_0x3e3232 += (_0x726a3c - 0x1) / -0.5);
        if (_0x3e3232 < 0x0)
            this['isZoomInMax']() == ![] ? (this.#mapCoord['setTileLevelOffset'](0x1 + -_0x3e3232 * 0x1), this['updateMap']()) : _0x726a3c != 0x1 && (this.#mapCoord['setTileLevelOffset'](0x1), this.#mapCoord['adjustMapBoundRect'](), this['updateMap']());
        else
            _0x3e3232 > 0x0 && (this['isZoomOutMax']() == ![] ? (this.#mapCoord['setTileLevelOffset'](0x1 + -_0x3e3232 * 0.5), this['updateMap']()) : _0x726a3c != 0x1 && (this.#mapCoord['setTileLevelOffset'](0x1), this.#mapCoord['adjustMapBoundRect'](), this['updateMap']()));
    }
    #checkUseMobile() {
        const _0x265f9d = 'win16|win32|win64|mac|macintel|navigator.platform|linux\x20x86_64';
        if (navigator['platform'])
            return 0x0 > _0x265f9d['indexOf'](navigator['platform']['toLowerCase']());
        return ![];
    }
    #loadServerUrl(_0x2c7b7d) {
        this.#remoteServerUrl = logi['maps']['Config']['server_url'], _0x2c7b7d && _0x2c7b7d['length'] > 0x0 ? this.#remoteServerUrl = _0x2c7b7d : typeof process !== 'undefined' && process?.['env']?.['NODE_ENV'] !== undefined && (process['env']['NODE_ENV'] === 'prod' ? this.#remoteServerUrl = logi['maps']['Config']['prod_server_url'] : this.#remoteServerUrl = logi['maps']['Config']['dev_server_url']);
    }
    #loadVectorUrl(_0x1b4690) {
        this.#remoteVectorUrl = logi['maps']['Config']['vector_url'], _0x1b4690 && _0x1b4690['length'] > 0x0 ? this.#remoteVectorUrl = _0x1b4690 : typeof process !== 'undefined' && process?.['env']?.['NODE_ENV'] !== undefined && (process['env']['NODE_ENV'] === 'prod' ? this.#remoteVectorUrl = logi['maps']['Config']['prod_vector_url'] : this.#remoteVectorUrl = logi['maps']['Config']['dev_vector_url']);
    }
    #setRenderFrame(_0x4e4162) {
        logi['maps']['Defines']['MEASURE_FPS'] && (_0x4e4162 = logi['maps']['Defines']['FPS_HIGH']), this.#currentFps != _0x4e4162 && (this.#loopRenderId != 0x0 && (clearInterval(this.#loopRenderId), this.#loopRenderId = 0x0), this.#currentFps = _0x4e4162, this.#loopRenderId = setInterval(() => {
            if (this.#moveAnims['length'] > 0x0) {
                const _0x48a342 = this.#moveAnims[0x0];
                if (_0x48a342['zoomScale']) {
                    if (_0x48a342['zoomScale']['step']) {
                        let _0x244760 = this['getZoom']();
                        _0x244760 += _0x48a342['zoomScale']['step'], this['setZoom'](_0x244760), this['updateMap']();
                    }
                    _0x48a342['zoomScale']['target'] && (this['setZoom'](_0x48a342['zoomScale']['target']), this['updateMap']());
                }
                if (_0x48a342['position']) {
                    if (_0x48a342['position']['step']) {
                        let _0x232ee3 = this['getCenter']();
                        _0x232ee3['lng'] += _0x48a342['position']['step']['lng'], _0x232ee3['lat'] += _0x48a342['position']['step']['lat'], this['setCenter'](_0x232ee3), this['updateMap']();
                    }
                    _0x48a342['position']['target'] && (this['setCenter'](_0x48a342['position']['target']), this['updateMap']());
                }
                this.#moveAnims['shift']();
            }
            logi['maps']['Defines']['MEASURE_FPS'] ? (logi['maps']['Utils']['FrameRate']['measure'](0x7d0), this.#renderMap(!![])) : this.#renderMap();
        }, Math['round'](0x3e8 / _0x4e4162)));
    }
    #renderMap(_0x5acc39 = ![]) {
        const _0x3bd6bb = this.#getWindowDevicePixelRatio();
        (this.#mapScreenSize['width'] != this.#mapDiv['clientWidth'] || this.#mapScreenSize['height'] != this.#mapDiv['clientHeight'] || this.#devicePixelRatio != _0x3bd6bb) && this.#setScreenSize({
            'width': this.#mapDiv['clientWidth'],
            'height': this.#mapDiv['clientHeight']
        }, _0x3bd6bb);
        const _0x303c4f = this.#resourceManager['getUpdateFlag']();
        this.#resourceManager['setUpdateFlag'](![]);
        if (_0x5acc39 || _0x303c4f) {
            for (let _0x317fba of this.#mapLayers) {
                _0x317fba['setUpdateFlag'](!![]);
            }
            _0x303c4f && this.#mtLayer['clearCache']();
        }
        for (let _0x1813da of this.#mapLayers) {
            _0x1813da['preWork']?.();
        }
        this.#resourceManager['preWork']();
        const _0x32b2d8 = this.#mapLayers['filter'](function (_0x2b2c5a) {
            return _0x2b2c5a['getUpdateFlag']() == !![];
        })['length'] > 0x0;
        if (_0x32b2d8)
            this.#updateCanvas(), this.#setRenderFrame(logi['maps']['Defines']['FPS_MEDIUM']), this.#lastRenderTick = logi['maps']['Utils']['getCurTick'](), this.#delayRender['cnt'] = 0x0;
        else {
            if (this.#currentFps > logi['maps']['Defines']['FPS_SLEEP']) {
                const _0x14baa2 = logi['maps']['Utils']['getCurTick']() - this.#lastRenderTick;
                this.#delayRender['cnt'] < this.#delayRender['max'] && _0x14baa2 >= (this.#delayRender['cnt'] + 0x1) * 0x64 && (this.#delayRender['cnt'] += 0x1, this['updateMap'](), this.#updateCanvas());
                if (this.#currentFps > logi['maps']['Defines']['FPS_LOW'])
                    _0x14baa2 >= logi['maps']['Defines']['SLOWMODE_TIME'] && this.#setRenderFrame(logi['maps']['Defines']['FPS_LOW']);
                else
                    this.#currentFps > logi['maps']['Defines']['FPS_SLEEP'] && (_0x14baa2 >= logi['maps']['Defines']['SLEEPMODE_TIME'] && this.#setRenderFrame(logi['maps']['Defines']['FPS_SLEEP']));
            }
        }
        this.#drawCanvas();
        for (let _0x5b3f16 of this.#mapLayers) {
            _0x5b3f16['postWork']?.();
        }
        this.#resourceManager['postWork']();
    }
    #captureFreezeCanvas() {
        this.#canvasWorldPosition = this['screen2world']({
            'x': this.#mapScreenSize['width'] * 0.5,
            'y': this.#mapScreenSize['height'] * 0.5
        });
        for (let _0x37f41c of this.#mapLayers) {
            _0x37f41c['captureFreezeCanvas']?.();
        }
    }
    #updateCanvas() {
        try {
            for (let _0x3774b7 of this.#mapLayers) {
                _0x3774b7['getUpdateFlag']() && _0x3774b7['updateCanvas']({ 'isDragging': this.#isDragging });
            }
        } catch (_0x421d5a) {
            console['log']('[logi.maps]\x20Failed\x20to\x20updateCanvas\x20(' + _0x421d5a + ')'), console['error']('[logi.maps]\x20stack\x20:\x0a', _0x421d5a['stack']);
        } finally {
            for (let _0x324deb of this.#mapLayers) {
                _0x324deb['setUpdateFlag'](![]);
            }
        }
    }
    #drawCanvas() {
        try {
            let _0x5dbf90 = ![];
            for (let _0x5e97d6 of this.#mapLayers) {
                if (_0x5e97d6['getDrawFlag']()) {
                    _0x5dbf90 = !![];
                    if (this.#isDragging && this.#freezeModeOnMoving && _0x5e97d6['drawFreezeCanvas']) {
                        let _0x4213fc = this['world2screen'](this.#canvasWorldPosition);
                        _0x4213fc['x'] -= this.#mapScreenSize['width'] * 0.5, _0x4213fc['y'] -= this.#mapScreenSize['height'] * 0.5, _0x5e97d6['drawFreezeCanvas'](_0x4213fc['x'], _0x4213fc['y']);
                    } else
                        _0x5e97d6['drawCanvas']({ 'isDragging': this.#isDragging });
                }
            }
            _0x5dbf90 && this['OnDraw']?.();
        } catch (_0x319393) {
            console['log']('[logi.maps]\x20Failed\x20to\x20drawCanvas\x20(' + _0x319393 + ')'), console['error']('[logi.maps]\x20stack\x20:\x0a', _0x319393['stack']);
        } finally {
            for (let _0x3c8266 of this.#mapLayers) {
                _0x3c8266['setDrawFlag'](![]);
            }
        }
    }
};
export default logi['maps']['Map'];