import a1_0x2dd039 from '../common/logi-maps-defines.js?v=2.1.10.1';
import a1_0x5946d7 from '../common/logi-maps-types.js?v=2.1.10.1';
import a1_0x21684 from '../utility/logi-maps-utils.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a1_0x2dd039, logi['maps']['LatLng'] = a1_0x5946d7['LatLng'], logi['maps']['Point'] = a1_0x5946d7['Point'], logi['maps']['MapRect'] = a1_0x5946d7['MapRect'], logi['maps']['Rect'] = a1_0x5946d7['Rect'], logi['maps']['TileUID'] = a1_0x5946d7['TileUID'], logi['maps']['TileOnScreen'] = a1_0x5946d7['TileOnScreen'], logi['maps']['TileInfo'] = a1_0x5946d7['TileInfo'], logi['maps']['ZoomInfo'] = a1_0x5946d7['ZoomInfo'], logi['maps']['Utils'] = a1_0x21684, logi['maps']['Coord'] = class {
    #region = logi['maps']['Defines']['REGION_KOR'];
    #mapBoundary = new logi['maps']['MapRect']();
    #screenRect = new logi['maps']['Rect']();
    #worldRect = new logi['maps']['MapRect']();
    #mapCenter = new logi['maps']['LatLng']();
    #zoomInfo = new logi['maps']['ZoomInfo']({ 'zoomScale': 0x0 });
    #tileLevelOffsetType = 'flat';
    #tileLevelOffsetMax = 1.3;
    #tileLevelOffsetMin = 0.85;
    #devicePixelRatio = 0x1;
    #onZoomChange = null;
    #onLevelChange = null;
    #onBoundsChange = null;
    constructor(_0x371eab, _0xaa938e, _0x4da147) {
        this.#region = _0x371eab ?? logi['maps']['Defines']['REGION_KOR'], this['setLevelRange'](_0xaa938e?.['min'], _0xaa938e?.['max']);
        _0x4da147?.['tileLevelOffsetType'] && (this.#tileLevelOffsetType = _0x4da147['tileLevelOffsetType']);
        this.#tileLevelOffsetType == 'flat' ? (this.#tileLevelOffsetMax = 1.3, this.#tileLevelOffsetMin = 0.85) : (this.#tileLevelOffsetMax = 1.5, this.#tileLevelOffsetMin = 0.75);
        this.#zoomInfo['setTileLevel'](logi['maps']['Defines']['DETAIL_LEVEL']), this.#mapCenter['lng'] = 0x0, this.#mapCenter['lat'] = 0x0;
        if (this.#region == logi['maps']['Defines']['REGION_KOR'])
            this.#mapBoundary['west'] = 123.8, this.#mapBoundary['south'] = 32.8, this.#mapBoundary['east'] = 131.95, this.#mapBoundary['north'] = 38.8;
        else {
            if (this.#region == logi['maps']['Defines']['REGION_NAM'])
                this.#mapBoundary['west'] = 0xa8, this.#mapBoundary['south'] = 0xa, this.#mapBoundary['east'] = -0x28, this.#mapBoundary['north'] = 0x52;
            else
                this.#region == logi['maps']['Defines']['REGION_EU'] ? (this.#mapBoundary['west'] = -0x1e, this.#mapBoundary['south'] = 34.5, this.#mapBoundary['east'] = 40.4, this.#mapBoundary['north'] = 0x4a) : (this.#mapBoundary['west'] = -0x168, this.#mapBoundary['south'] = -0x52, this.#mapBoundary['east'] = 0x168, this.#mapBoundary['north'] = 0x52);
        }
    }
    ['setOnZoomChange'](_0x551ed5) {
        this.#onZoomChange = _0x551ed5;
    }
    ['setOnLevelChange'](_0x5cafa0) {
        this.#onLevelChange = _0x5cafa0;
    }
    ['setOnBoundsChange'](_0x2c6e47) {
        this.#onBoundsChange = _0x2c6e47;
    }
    ['setLevelRange'](_0xb95c4d, _0x831241) {
        const _0x4dfea4 = _0xb95c4d ?? logi['maps']['Defines']['MIN_LEVEL'], _0x471cc6 = _0x831241 ?? logi['maps']['Defines']['MAX_LEVEL'];
        _0x471cc6 >= _0x4dfea4 ? (logi['maps']['Defines']['MIN_LEVEL'] = _0x4dfea4, logi['maps']['Defines']['MAX_LEVEL'] = _0x471cc6) : (logi['maps']['Defines']['MIN_LEVEL'] = _0x471cc6, logi['maps']['Defines']['MAX_LEVEL'] = _0x4dfea4), this['getZoom']() < logi['maps']['Defines']['MIN_LEVEL'] && this['setZoom'](logi['maps']['Defines']['MIN_LEVEL']), this['getZoom']() > logi['maps']['Defines']['MAX_LEVEL'] && this['setZoom'](logi['maps']['Defines']['MAX_LEVEL']);
    }
    ['setScreenSize'](_0x437f19, _0x2809d3, _0x90e1c9) {
        this.#screenRect['xMin'] = 0x0, this.#screenRect['yMin'] = 0x0, this.#screenRect['xMax'] = _0x437f19, this.#screenRect['yMax'] = _0x2809d3, this.#devicePixelRatio = _0x90e1c9, this.#calcMapBoundRect();
    }
    ['getScreenSize']() {
        return {
            'left': this.#screenRect['xMin'],
            'top': this.#screenRect['yMin'],
            'right': this.#screenRect['xMax'],
            'bottom': this.#screenRect['yMax']
        };
    }
    ['getMapBoundry']() {
        return { ...this.#mapBoundary };
    }
    ['screen2world'](_0x2af4b8, _0x487cb1, _0x32ea6b) {
        const _0xcb65b7 = new logi['maps']['LatLng'](), _0x34ce05 = _0x32ea6b?.['center'] ?? this.#mapCenter, _0x47a523 = {
                'tileLevel': this.#zoomInfo['tileLevel'],
                'tileLevelOffset': this.#zoomInfo['tileLevelOffset']
            };
        if (_0x32ea6b?.['zoomScale']) {
            const _0x2b9807 = logi['maps']['ZoomInfo']['toTileScale'](_0x32ea6b['zoomScale']);
            _0x47a523['tileLevel'] = _0x2b9807['tileLevel'], _0x47a523['tileLevelOffset'] = _0x2b9807['tileLevelOffset'];
        } else {
            if (_0x32ea6b?.['tileScale'])
                _0x47a523['tileLevel'] = Math['round'](_0x32ea6b['tileScale']['tileLevel']), _0x47a523['tileLevelOffset'] = _0x32ea6b['tileScale']['tileLevelOffset'];
            else
                _0x32ea6b?.['level'] && _0x32ea6b?.['extraScale'] && (_0x47a523['tileLevel'] = Math['round'](_0x32ea6b['level']), _0x47a523['tileLevelOffset'] = _0x32ea6b['extraScale']);
        }
        let _0x37bad8 = logi['maps']['Utils']['longitude2TileX'](_0x34ce05['lng'], _0x47a523['tileLevel']), _0x3e123b = logi['maps']['Utils']['latitude2TileY'](_0x34ce05['lat'], _0x47a523['tileLevel']);
        _0x37bad8 += (_0x2af4b8 - (this.#screenRect['xMax'] - this.#screenRect['xMin']) / 0x2) / (logi['maps']['Defines']['TILE_W'] * _0x47a523['tileLevelOffset']), _0x3e123b += (_0x487cb1 - (this.#screenRect['yMax'] - this.#screenRect['yMin']) / 0x2) / (logi['maps']['Defines']['TILE_H'] * _0x47a523['tileLevelOffset']);
        const _0x4659ee = logi['maps']['Defines']['POW_TABLE'][_0x47a523['tileLevel']];
        while (_0x37bad8 < 0x0) {
            _0x37bad8 += _0x4659ee;
        }
        while (_0x37bad8 >= _0x4659ee) {
            _0x37bad8 -= _0x4659ee;
        }
        return _0x37bad8 >= 0x0 && _0x37bad8 <= _0x4659ee && _0x3e123b >= 0x0 && _0x3e123b <= _0x4659ee && (_0xcb65b7['lng'] = logi['maps']['Utils']['tileX2Longitude'](_0x37bad8, _0x47a523['tileLevel']), _0xcb65b7['lat'] = logi['maps']['Utils']['tileY2Latitude'](_0x3e123b, _0x47a523['tileLevel'])), {
            'lng': _0xcb65b7['lng'],
            'lat': _0xcb65b7['lat'],
            'x': _0xcb65b7['lng'],
            'y': _0xcb65b7['lat']
        };
    }
    ['world2screen'](_0x911c2, _0x4c9a75, _0x53eb2f) {
        const _0x42cc9f = new logi['maps']['Point'](), _0x2bb3dd = _0x53eb2f?.['center'] ?? this.#mapCenter, _0x54b754 = {
                'tileLevel': this.#zoomInfo['tileLevel'],
                'tileLevelOffset': this.#zoomInfo['tileLevelOffset']
            };
        if (_0x53eb2f?.['zoomScale']) {
            const _0x342210 = logi['maps']['ZoomInfo']['toTileScale'](_0x53eb2f['zoomScale']);
            _0x54b754['tileLevel'] = _0x342210['tileLevel'], _0x54b754['tileLevelOffset'] = _0x342210['tileLevelOffset'];
        } else {
            if (_0x53eb2f?.['tileScale'])
                _0x54b754['tileLevel'] = Math['round'](_0x53eb2f['tileScale']['tileLevel']), _0x54b754['tileLevelOffset'] = _0x53eb2f['tileScale']['tileLevelOffset'];
            else
                _0x53eb2f?.['level'] && _0x53eb2f?.['extraScale'] && (_0x54b754['tileLevel'] = Math['round'](_0x53eb2f['level']), _0x54b754['tileLevelOffset'] = _0x53eb2f['extraScale']);
        }
        const _0x202d28 = logi['maps']['Utils']['longitude2TileX'](_0x2bb3dd['lng'], _0x54b754['tileLevel']), _0xb94e6 = logi['maps']['Utils']['latitude2TileY'](_0x2bb3dd['lat'], _0x54b754['tileLevel']);
        let _0x190422 = logi['maps']['Utils']['longitude2TileX'](_0x911c2, _0x54b754['tileLevel']), _0x28516e = logi['maps']['Utils']['latitude2TileY'](_0x4c9a75, _0x54b754['tileLevel']);
        const _0x39a6d2 = logi['maps']['Defines']['POW_TABLE'][_0x54b754['tileLevel']];
        if (_0x190422 >= 0x0 && _0x190422 <= _0x39a6d2 && _0x28516e >= 0x0 && _0x28516e <= _0x39a6d2) {
            if (Math['abs'](_0x190422 - _0x202d28) > _0x39a6d2 * 0.5) {
                const _0xff653e = Math['abs'](_0x190422 - _0x202d28), _0x93094 = Math['abs'](_0x190422 - _0x39a6d2 - _0x202d28), _0x7d38f1 = Math['abs'](_0x190422 + _0x39a6d2 - _0x202d28);
                _0x93094 <= _0xff653e && _0x93094 <= _0x7d38f1 && (_0x190422 -= _0x39a6d2), _0x7d38f1 <= _0xff653e && _0x7d38f1 <= _0x93094 && (_0x190422 += _0x39a6d2);
            }
            _0x42cc9f['x'] = (this.#screenRect['xMax'] - this.#screenRect['xMin']) / 0x2 + this.#screenRect['xMin'] + (_0x190422 - _0x202d28) * (logi['maps']['Defines']['TILE_W'] * _0x54b754['tileLevelOffset']), _0x42cc9f['y'] = (this.#screenRect['yMax'] - this.#screenRect['yMin']) / 0x2 + this.#screenRect['yMin'] + (_0x28516e - _0xb94e6) * (logi['maps']['Defines']['TILE_H'] * _0x54b754['tileLevelOffset']);
        }
        return _0x42cc9f['x'] = Number(_0x42cc9f['x']['toFixed'](0x1)), _0x42cc9f['y'] = Number(_0x42cc9f['y']['toFixed'](0x1)), _0x42cc9f;
    }
    ['world2plane'](_0xcba290, _0x11016c, _0x310a3f) {
        const _0x5526a9 = new logi['maps']['Point'](), _0x48b886 = {
                'tileLevel': this.#zoomInfo['tileLevel'],
                'tileLevelOffset': this.#zoomInfo['tileLevelOffset']
            };
        if (_0x310a3f?.['zoomScale']) {
            const _0x45dfc3 = logi['maps']['ZoomInfo']['toTileScale'](_0x310a3f['zoomScale']);
            _0x48b886['tileLevel'] = _0x45dfc3['tileLevel'], _0x48b886['tileLevelOffset'] = _0x45dfc3['tileLevelOffset'];
        } else {
            if (_0x310a3f?.['tileScale'])
                _0x48b886['tileLevel'] = Math['round'](_0x310a3f['tileScale']['tileLevel']), _0x48b886['tileLevelOffset'] = _0x310a3f['tileScale']['tileLevelOffset'];
            else
                _0x310a3f?.['level'] && _0x310a3f?.['extraScale'] && (_0x48b886['tileLevel'] = Math['round'](_0x310a3f['level']), _0x48b886['tileLevelOffset'] = _0x310a3f['extraScale']);
        }
        let _0x4eb9d7 = logi['maps']['Utils']['longitude2TileX'](_0xcba290, _0x48b886['tileLevel']), _0x382f7a = logi['maps']['Utils']['latitude2TileY'](_0x11016c, _0x48b886['tileLevel']);
        return _0x5526a9['x'] = _0x4eb9d7 * (logi['maps']['Defines']['TILE_W'] * _0x48b886['tileLevelOffset']), _0x5526a9['y'] = _0x382f7a * (logi['maps']['Defines']['TILE_H'] * _0x48b886['tileLevelOffset']), _0x5526a9;
    }
    ['meter2pixel'](_0x2936d0) {
        const _0x58368f = 40075016.68557849, _0x4b978c = logi['maps']['Defines']['TILE_W'], _0x2edd40 = logi['maps']['Defines']['POW_TABLE'][this.#zoomInfo['tileLevel']];
        return _0x4b978c * _0x2edd40 / _0x58368f * _0x2936d0 * this.#zoomInfo['tileLevelOffset'];
    }
    ['tileXY2screen'](_0xb86673, _0x126de8, _0x4d29af, _0xd1a905) {
        const _0x9f3972 = logi['maps']['Utils']['tileX2Longitude'](_0xb86673, _0x4d29af), _0x293a4f = logi['maps']['Utils']['tileY2Latitude'](_0x126de8, _0x4d29af);
        return this['world2screen'](_0x9f3972, _0x293a4f, {
            'tileScale': {
                'tileLevel': _0x4d29af,
                'tileLevelOffset': _0xd1a905
            }
        });
    }
    #calcMapBoundRect() {
        const _0x1c6d26 = this['screen2world'](this.#screenRect['xMin'], this.#screenRect['yMin']), _0x580293 = this['screen2world'](this.#screenRect['xMax'], this.#screenRect['yMax']);
        this.#worldRect['west'] = _0x1c6d26['lng'], this.#worldRect['north'] = _0x1c6d26['lat'], this.#worldRect['east'] = _0x580293['lng'], this.#worldRect['south'] = _0x580293['lat'], this.#onBoundsChange?.(this.#worldRect);
    }
    ['adjustMapBoundRect']() {
        function _0x2f7801(_0x409cf7, _0x57ada4, _0x2a9ecd, _0x711b60) {
            let _0x16bcf5 = 0x0;
            const _0x29a1e0 = _0x409cf7 - _0x2a9ecd, _0x1352a1 = _0x57ada4 - _0x711b60;
            if (_0x29a1e0 >= 0x0 && _0x1352a1 <= 0x0)
                _0x16bcf5 = 0x0;
            else {
                if (_0x29a1e0 < 0x0 && _0x1352a1 > 0x0)
                    _0x16bcf5 = (_0x29a1e0 + _0x1352a1) * -0.5;
                else {
                    if (_0x29a1e0 < 0x0 && _0x1352a1 <= 0x0)
                        _0x1352a1 >= _0x29a1e0 ? _0x16bcf5 = (_0x1352a1 + (_0x29a1e0 - _0x1352a1) * 0.5) * -0x1 : _0x16bcf5 = _0x29a1e0 * -0x1;
                    else
                        _0x29a1e0 >= 0x0 && _0x1352a1 > 0x0 && (_0x1352a1 >= _0x29a1e0 ? _0x16bcf5 = (_0x29a1e0 + (_0x1352a1 - _0x29a1e0) * 0.5) * -0x1 : _0x16bcf5 = _0x1352a1 * -0x1);
                }
            }
            return _0x16bcf5;
        }
        let _0x428940 = this.#worldRect['west'], _0x137c88 = this.#worldRect['east'], _0x1241ff = this.#mapBoundary['west'], _0x109ed8 = this.#mapBoundary['east'];
        (this.#worldRect['west'] > this.#worldRect['east'] || this.#mapBoundary['west'] > this.#mapBoundary['east']) && (this.#worldRect['west'] < 0x0 && this.#worldRect['west'] <= this.#worldRect['east'] && (_0x428940 = this.#worldRect['west'] + 0x168), this.#worldRect['east'] < 0x0 && (_0x137c88 = this.#worldRect['east'] + 0x168), this.#mapBoundary['west'] < 0x0 && this.#mapBoundary['west'] <= this.#mapBoundary['east'] && (_0x1241ff = this.#mapBoundary['west'] + 0x168), this.#mapBoundary['east'] < 0x0 && (_0x109ed8 = this.#mapBoundary['east'] + 0x168));
        const _0x52e850 = _0x2f7801(_0x428940, _0x137c88, _0x1241ff, _0x109ed8), _0xea2ff6 = _0x2f7801(this.#worldRect['south'], this.#worldRect['north'], this.#mapBoundary['south'], this.#mapBoundary['north']);
        this.#mapCenter['lng'] += _0x52e850, this.#mapCenter['lat'] += _0xea2ff6;
        const _0x127db6 = this['screen2world'](this.#screenRect['xMin'], this.#screenRect['yMin']), _0x1d96a8 = this['screen2world'](this.#screenRect['xMax'], this.#screenRect['yMax']);
        this.#worldRect['west'] = _0x127db6['lng'], this.#worldRect['north'] = _0x127db6['lat'], this.#worldRect['east'] = _0x1d96a8['lng'], this.#worldRect['south'] = _0x1d96a8['lat'], this.#onBoundsChange?.(this.#worldRect);
    }
    ['setCenter'](_0x4f5fad, _0x130349) {
        this.#mapCenter['lng'] = _0x4f5fad, this.#mapCenter['lat'] = _0x130349, this.#calcMapBoundRect();
    }
    ['getCenter']() {
        return {
            'lng': this.#mapCenter['lng'],
            'lat': this.#mapCenter['lat']
        };
    }
    ['getMapRect']() {
        return this.#worldRect;
    }
    ['getBoundsInfo'](_0x6cdc3d, _0x2a18bc, _0x34a391, _0x5de9c1, _0x15892a, _0x2ca758, _0x6c69dd, _0x23a0a4) {
        const _0x2b5dcb = {
                'cx': this.#screenRect['xMax'] - this.#screenRect['xMin'],
                'cy': this.#screenRect['yMax'] - this.#screenRect['yMin']
            }, _0x4e19e6 = _0x2b5dcb['cx'] - (_0x15892a + _0x6c69dd), _0x217516 = _0x2b5dcb['cy'] - (_0x2ca758 + _0x23a0a4);
        if (_0x4e19e6 <= 0x0 || _0x217516 <= 0x0)
            return null;
        let _0x55991f = {
                'lng': _0x6cdc3d,
                'lat': _0x2a18bc
            }, _0x3aac27 = {
                'lng': _0x34a391,
                'lat': _0x5de9c1
            };
        _0x6cdc3d = _0x55991f['lng'] < _0x3aac27['lng'] ? _0x55991f['lng'] : _0x3aac27['lng'], _0x2a18bc = _0x55991f['lat'] < _0x3aac27['lat'] ? _0x55991f['lat'] : _0x3aac27['lat'], _0x34a391 = _0x55991f['lng'] > _0x3aac27['lng'] ? _0x55991f['lng'] : _0x3aac27['lng'], _0x5de9c1 = _0x55991f['lat'] > _0x3aac27['lat'] ? _0x55991f['lat'] : _0x3aac27['lat'];
        this.#mapBoundary['west'] > this.#mapBoundary['east'] ? (_0x34a391 + _0x6cdc3d) * 0.5 >= 0x0 ? (_0x6cdc3d = this.#mapBoundary['west'] < _0x6cdc3d ? _0x6cdc3d : this.#mapBoundary['west'], _0x2a18bc = this.#mapBoundary['south'] < _0x2a18bc ? _0x2a18bc : this.#mapBoundary['south'], _0x34a391 = 0xb4 > _0x34a391 ? _0x34a391 : 0xb4, _0x5de9c1 = this.#mapBoundary['north'] > _0x5de9c1 ? _0x5de9c1 : this.#mapBoundary['north']) : (_0x6cdc3d = -0xb4 < _0x6cdc3d ? _0x6cdc3d : -0xb4, _0x2a18bc = this.#mapBoundary['south'] < _0x2a18bc ? _0x2a18bc : this.#mapBoundary['south'], _0x34a391 = this.#mapBoundary['east'] > _0x34a391 ? _0x34a391 : this.#mapBoundary['east'], _0x5de9c1 = this.#mapBoundary['north'] > _0x5de9c1 ? _0x5de9c1 : this.#mapBoundary['north']) : (_0x6cdc3d = this.#mapBoundary['west'] < _0x6cdc3d ? _0x6cdc3d : this.#mapBoundary['west'], _0x2a18bc = this.#mapBoundary['south'] < _0x2a18bc ? _0x2a18bc : this.#mapBoundary['south'], _0x34a391 = this.#mapBoundary['east'] > _0x34a391 ? _0x34a391 : this.#mapBoundary['east'], _0x5de9c1 = this.#mapBoundary['north'] > _0x5de9c1 ? _0x5de9c1 : this.#mapBoundary['north']);
        const _0x17162b = (_0x34a391 - _0x6cdc3d) * (_0x15892a / _0x4e19e6), _0x2e7471 = (_0x34a391 - _0x6cdc3d) * (_0x6c69dd / _0x4e19e6), _0x37b960 = (_0x5de9c1 - _0x2a18bc) * (_0x23a0a4 / _0x217516), _0x429b21 = (_0x5de9c1 - _0x2a18bc) * (_0x2ca758 / _0x217516);
        _0x6cdc3d -= _0x17162b, _0x34a391 += _0x2e7471, _0x2a18bc -= _0x37b960, _0x5de9c1 += _0x429b21;
        const _0x53a78c = {
            'lng': (_0x6cdc3d + _0x34a391) / 0x2,
            'lat': (_0x5de9c1 + _0x2a18bc) / 0x2
        };
        let _0x409756 = -0x1;
        for (let _0x2b23f2 = logi['maps']['Defines']['MAX_LEVEL']; _0x2b23f2 >= logi['maps']['Defines']['MIN_LEVEL'] && _0x409756 == -0x1; --_0x2b23f2) {
            let _0x222609 = this['world2screen'](_0x6cdc3d, _0x5de9c1, {
                    'center': _0x53a78c,
                    'tileScale': {
                        'tileLevel': _0x2b23f2,
                        'tileLevelOffset': 0x1
                    }
                }), _0x36af10 = this['world2screen'](_0x34a391, _0x2a18bc, {
                    'center': _0x53a78c,
                    'tileScale': {
                        'tileLevel': _0x2b23f2,
                        'tileLevelOffset': 0x1
                    }
                });
            logi['maps']['Utils']['pointInRect'](_0x222609['x'], _0x222609['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax']) && logi['maps']['Utils']['pointInRect'](_0x36af10['x'], _0x36af10['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax']) && (_0x409756 = _0x2b23f2);
        }
        let _0x186b6b = 0x1;
        if (_0x409756 == -0x1)
            _0x409756 = logi['maps']['Defines']['MIN_LEVEL'];
        else {
            const _0x24c925 = this.#tileLevelOffsetMax, _0xd30694 = this.#tileLevelOffsetMin;
            if (_0x409756 < logi['maps']['Defines']['MAX_LEVEL'])
                for (let _0x45c7ba = 0x1; _0x45c7ba >= _0xd30694; _0x45c7ba -= 0.05) {
                    let _0x38962c = this['world2screen'](_0x6cdc3d, _0x5de9c1, {
                            'center': _0x53a78c,
                            'tileScale': {
                                'tileLevel': _0x409756 + 0x1,
                                'tileLevelOffset': _0x45c7ba
                            }
                        }), _0x5c7f49 = this['world2screen'](_0x34a391, _0x2a18bc, {
                            'center': _0x53a78c,
                            'tileScale': {
                                'tileLevel': _0x409756 + 0x1,
                                'tileLevelOffset': _0x45c7ba
                            }
                        });
                    if (logi['maps']['Utils']['pointInRect'](_0x38962c['x'], _0x38962c['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax']) && logi['maps']['Utils']['pointInRect'](_0x5c7f49['x'], _0x5c7f49['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax'])) {
                        _0x186b6b = _0x45c7ba;
                        break;
                    }
                }
            if (_0x186b6b != 0x1)
                _0x409756 += 0x1;
            else
                for (let _0x2e76b8 = _0x24c925; _0x2e76b8 >= 0x1; _0x2e76b8 -= 0.1) {
                    let _0x28f8b7 = this['world2screen'](_0x6cdc3d, _0x5de9c1, {
                            'center': _0x53a78c,
                            'tileScale': {
                                'tileLevel': _0x409756,
                                'tileLevelOffset': _0x2e76b8
                            }
                        }), _0x1141f9 = this['world2screen'](_0x34a391, _0x2a18bc, {
                            'center': _0x53a78c,
                            'tileScale': {
                                'tileLevel': _0x409756,
                                'tileLevelOffset': _0x2e76b8
                            }
                        });
                    if (logi['maps']['Utils']['pointInRect'](_0x28f8b7['x'], _0x28f8b7['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax']) && logi['maps']['Utils']['pointInRect'](_0x1141f9['x'], _0x1141f9['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax'])) {
                        _0x186b6b = _0x2e76b8;
                        break;
                    }
                }
        }
        return {
            'center': {
                'lng': _0x53a78c['lng'],
                'lat': _0x53a78c['lat']
            },
            'zoomInfo': new logi['maps']['ZoomInfo']({
                'tileLevel': _0x409756,
                'tileLevelOffset': _0x186b6b
            })
        };
    }
    ['setBounds'](_0x1aafbe, _0x237369, _0xeaee54, _0x566fa9, _0x14071b, _0x42a5bd, _0x2c794b, _0x2fb581) {
        const _0x5ec562 = {
                'cx': this.#screenRect['xMax'] - this.#screenRect['xMin'],
                'cy': this.#screenRect['yMax'] - this.#screenRect['yMin']
            }, _0x5c9e6b = _0x5ec562['cx'] - (_0x14071b + _0x2c794b), _0x4fe05c = _0x5ec562['cy'] - (_0x42a5bd + _0x2fb581);
        if (_0x5c9e6b <= 0x0 || _0x4fe05c <= 0x0)
            return;
        let _0x41a762 = {
                'lng': _0x1aafbe,
                'lat': _0x237369
            }, _0x528d70 = {
                'lng': _0xeaee54,
                'lat': _0x566fa9
            };
        _0x1aafbe = _0x41a762['lng'] < _0x528d70['lng'] ? _0x41a762['lng'] : _0x528d70['lng'], _0x237369 = _0x41a762['lat'] < _0x528d70['lat'] ? _0x41a762['lat'] : _0x528d70['lat'], _0xeaee54 = _0x41a762['lng'] > _0x528d70['lng'] ? _0x41a762['lng'] : _0x528d70['lng'], _0x566fa9 = _0x41a762['lat'] > _0x528d70['lat'] ? _0x41a762['lat'] : _0x528d70['lat'];
        this.#mapBoundary['west'] > this.#mapBoundary['east'] ? (_0xeaee54 + _0x1aafbe) * 0.5 >= 0x0 ? (_0x1aafbe = this.#mapBoundary['west'] < _0x1aafbe ? _0x1aafbe : this.#mapBoundary['west'], _0x237369 = this.#mapBoundary['south'] < _0x237369 ? _0x237369 : this.#mapBoundary['south'], _0xeaee54 = 0xb4 > _0xeaee54 ? _0xeaee54 : 0xb4, _0x566fa9 = this.#mapBoundary['north'] > _0x566fa9 ? _0x566fa9 : this.#mapBoundary['north']) : (_0x1aafbe = -0xb4 < _0x1aafbe ? _0x1aafbe : -0xb4, _0x237369 = this.#mapBoundary['south'] < _0x237369 ? _0x237369 : this.#mapBoundary['south'], _0xeaee54 = this.#mapBoundary['east'] > _0xeaee54 ? _0xeaee54 : this.#mapBoundary['east'], _0x566fa9 = this.#mapBoundary['north'] > _0x566fa9 ? _0x566fa9 : this.#mapBoundary['north']) : (_0x1aafbe = this.#mapBoundary['west'] < _0x1aafbe ? _0x1aafbe : this.#mapBoundary['west'], _0x237369 = this.#mapBoundary['south'] < _0x237369 ? _0x237369 : this.#mapBoundary['south'], _0xeaee54 = this.#mapBoundary['east'] > _0xeaee54 ? _0xeaee54 : this.#mapBoundary['east'], _0x566fa9 = this.#mapBoundary['north'] > _0x566fa9 ? _0x566fa9 : this.#mapBoundary['north']);
        const _0x597e03 = (_0xeaee54 - _0x1aafbe) * (_0x14071b / _0x5c9e6b), _0x5228b0 = (_0xeaee54 - _0x1aafbe) * (_0x2c794b / _0x5c9e6b), _0x1566ee = (_0x566fa9 - _0x237369) * (_0x2fb581 / _0x4fe05c), _0x3de311 = (_0x566fa9 - _0x237369) * (_0x42a5bd / _0x4fe05c);
        _0x1aafbe -= _0x597e03, _0xeaee54 += _0x5228b0, _0x237369 -= _0x1566ee, _0x566fa9 += _0x3de311;
        const _0x3911a9 = {
            'lng': (_0x1aafbe + _0xeaee54) / 0x2,
            'lat': (_0x566fa9 + _0x237369) / 0x2
        };
        let _0x526353 = -0x1;
        for (let _0x360d52 = logi['maps']['Defines']['MAX_LEVEL']; _0x360d52 >= logi['maps']['Defines']['MIN_LEVEL'] && _0x526353 == -0x1; --_0x360d52) {
            let _0x2262ec = this['world2screen'](_0x1aafbe, _0x566fa9, {
                    'center': _0x3911a9,
                    'tileScale': {
                        'tileLevel': _0x360d52,
                        'tileLevelOffset': 0x1
                    }
                }), _0x285b9a = this['world2screen'](_0xeaee54, _0x237369, {
                    'center': _0x3911a9,
                    'tileScale': {
                        'tileLevel': _0x360d52,
                        'tileLevelOffset': 0x1
                    }
                });
            logi['maps']['Utils']['pointInRect'](_0x2262ec['x'], _0x2262ec['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax']) && logi['maps']['Utils']['pointInRect'](_0x285b9a['x'], _0x285b9a['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax']) && (_0x526353 = _0x360d52);
        }
        let _0x2d2e1d = 0x1;
        if (_0x526353 == -0x1)
            _0x526353 = logi['maps']['Defines']['MIN_LEVEL'];
        else {
            const _0x12bbdb = this.#tileLevelOffsetMax, _0x489664 = this.#tileLevelOffsetMin;
            if (_0x526353 < logi['maps']['Defines']['MAX_LEVEL'])
                for (let _0x4bc058 = 0x1; _0x4bc058 >= _0x489664; _0x4bc058 -= 0.05) {
                    let _0xaa6830 = this['world2screen'](_0x1aafbe, _0x566fa9, {
                            'center': _0x3911a9,
                            'tileScale': {
                                'tileLevel': _0x526353 + 0x1,
                                'tileLevelOffset': _0x4bc058
                            }
                        }), _0x5ce74f = this['world2screen'](_0xeaee54, _0x237369, {
                            'center': _0x3911a9,
                            'tileScale': {
                                'tileLevel': _0x526353 + 0x1,
                                'tileLevelOffset': _0x4bc058
                            }
                        });
                    if (logi['maps']['Utils']['pointInRect'](_0xaa6830['x'], _0xaa6830['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax']) && logi['maps']['Utils']['pointInRect'](_0x5ce74f['x'], _0x5ce74f['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax'])) {
                        _0x2d2e1d = _0x4bc058;
                        break;
                    }
                }
            if (_0x2d2e1d != 0x1)
                _0x526353 += 0x1;
            else
                for (let _0x41785d = _0x12bbdb; _0x41785d >= 0x1; _0x41785d -= 0.1) {
                    let _0x485ced = this['world2screen'](_0x1aafbe, _0x566fa9, {
                            'center': _0x3911a9,
                            'tileScale': {
                                'tileLevel': _0x526353,
                                'tileLevelOffset': _0x41785d
                            }
                        }), _0x125077 = this['world2screen'](_0xeaee54, _0x237369, {
                            'center': _0x3911a9,
                            'tileScale': {
                                'tileLevel': _0x526353,
                                'tileLevelOffset': _0x41785d
                            }
                        });
                    if (logi['maps']['Utils']['pointInRect'](_0x485ced['x'], _0x485ced['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax']) && logi['maps']['Utils']['pointInRect'](_0x125077['x'], _0x125077['y'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax'])) {
                        _0x2d2e1d = _0x41785d;
                        break;
                    }
                }
        }
        this.#zoomInfo['setTileLevel'](_0x526353), this.#mapCenter['lng'] = _0x3911a9['lng'], this.#mapCenter['lat'] = _0x3911a9['lat'], this.#calcMapBoundRect(), this['setTileLevelOffset'](_0x2d2e1d);
    }
    ['panMap'](_0x3b78a4, _0x2c2636, _0x55fab0, _0x52afc8) {
        const _0xe39d6f = logi['maps']['Utils']['longitude2TileX'](this.#mapCenter['lng'], this.#zoomInfo['tileLevel']) - (_0x55fab0 - _0x3b78a4) / (logi['maps']['Defines']['TILE_W'] * this.#zoomInfo['tileLevelOffset']), _0x575e20 = logi['maps']['Utils']['tileX2Longitude'](_0xe39d6f, this.#zoomInfo['tileLevel']), _0x55d6ab = logi['maps']['Utils']['latitude2TileY'](this.#mapCenter['lat'], this.#zoomInfo['tileLevel']) - (_0x52afc8 - _0x2c2636) / (logi['maps']['Defines']['TILE_H'] * this.#zoomInfo['tileLevelOffset']), _0xdec51a = logi['maps']['Utils']['tileY2Latitude'](_0x55d6ab, this.#zoomInfo['tileLevel']);
        this['setCenter'](_0x575e20, _0xdec51a);
    }
    ['setZoom'](_0x3259e4) {
        if (_0x3259e4 < logi['maps']['Defines']['MIN_LEVEL'] || _0x3259e4 > logi['maps']['Defines']['MAX_LEVEL'])
            return;
        this.#zoomInfo['setZoomScale'](_0x3259e4), this.#calcMapBoundRect(), this.#onZoomChange?.(this.#zoomInfo['zoomScale']), this.#onLevelChange?.(this.#zoomInfo['tileLevel']);
    }
    ['getZoom']() {
        return this.#zoomInfo['zoomScale'];
    }
    ['setLevel'](_0x1ed116) {
        if (_0x1ed116 < logi['maps']['Defines']['MIN_LEVEL'] || _0x1ed116 > logi['maps']['Defines']['MAX_LEVEL'])
            return;
        this.#zoomInfo['setTileLevel'](_0x1ed116), this.#calcMapBoundRect(), this.#onZoomChange?.(this.#zoomInfo['zoomScale']), this.#onLevelChange?.(this.#zoomInfo['tileLevel']);
    }
    ['getLevel']() {
        return this.#zoomInfo['tileLevel'];
    }
    ['isZoomInMax']() {
        return this['getLevel']() >= logi['maps']['Defines']['MAX_LEVEL'];
    }
    ['zoomIn']() {
        this.#zoomInfo['tileLevel'] + 0x1 <= logi['maps']['Defines']['MAX_LEVEL'] && this['setLevel'](this.#zoomInfo['tileLevel'] + 0x1);
    }
    ['isZoomOutMax']() {
        return this['getLevel']() <= logi['maps']['Defines']['MIN_LEVEL'];
    }
    ['zoomOut']() {
        this.#zoomInfo['tileLevel'] - 0x1 >= logi['maps']['Defines']['MIN_LEVEL'] && this['setLevel'](this.#zoomInfo['tileLevel'] - 0x1);
    }
    ['setTileLevelOffset'](_0x4a5124) {
        let _0x4432d0 = null;
        const _0x3dac82 = this.#tileLevelOffsetMax, _0x1df4c3 = this.#tileLevelOffsetMin;
        if (this.#tileLevelOffsetType == 'flat') {
            if (_0x4a5124 >= 0x2)
                _0x4432d0 = 'zoomIn', _0x4a5124 = 0x1;
            else {
                if (_0x4a5124 <= 0.5)
                    _0x4432d0 = 'zoomOut', _0x4a5124 = 0x1;
                else {
                    if (_0x4a5124 > _0x3dac82)
                        _0x4432d0 = 'zoomIn', _0x4a5124 = _0x1df4c3;
                    else
                        _0x4a5124 < _0x1df4c3 && (_0x4432d0 = 'zoomOut', _0x4a5124 = _0x3dac82);
                }
            }
        } else {
            if (_0x4a5124 >= 0x2)
                _0x4432d0 = 'zoomIn', _0x4a5124 = 0x1;
            else {
                if (_0x4a5124 <= 0.5)
                    _0x4432d0 = 'zoomOut', _0x4a5124 = 0x1;
                else {
                    if (_0x4a5124 > _0x3dac82)
                        _0x4432d0 = 'zoomIn', _0x4a5124 = _0x4a5124 * 0.5, _0x4a5124 = _0x4a5124 > _0x3dac82 ? _0x3dac82 : _0x4a5124, _0x4a5124 = _0x4a5124 < _0x1df4c3 ? _0x1df4c3 : _0x4a5124;
                    else
                        _0x4a5124 < _0x1df4c3 && (_0x4432d0 = 'zoomOut', _0x4a5124 = _0x4a5124 * 0x2, _0x4a5124 = _0x4a5124 > _0x3dac82 ? _0x3dac82 : _0x4a5124, _0x4a5124 = _0x4a5124 < _0x1df4c3 ? _0x1df4c3 : _0x4a5124);
                }
            }
        }
        let _0x5c7d50 = Math['floor'](logi['maps']['Defines']['TILE_W'] * _0x4a5124);
        _0x5c7d50 % 0x2 != 0x0 && (_0x5c7d50 += 0x1);
        _0x4a5124 = _0x5c7d50 / logi['maps']['Defines']['TILE_W'];
        const _0x3dce39 = this['getTileLevelOffset']();
        if (_0x4432d0 != null || _0x3dce39 != _0x4a5124) {
            if (_0x4432d0 == 'zoomIn')
                this.#zoomInfo['setTileLevelOffset'](_0x4a5124), this.#calcMapBoundRect(), this['zoomIn']();
            else
                _0x4432d0 == 'zoomOut' ? (this.#zoomInfo['setTileLevelOffset'](_0x4a5124), this.#calcMapBoundRect(), this['zoomOut']()) : (this.#zoomInfo['setTileLevelOffset'](_0x4a5124), this.#calcMapBoundRect(), this['adjustMapBoundRect'](), this.#onZoomChange?.(this.#zoomInfo['zoomScale']));
            return !![];
        }
        return ![];
    }
    ['getTileLevelOffset']() {
        return this.#zoomInfo['tileLevelOffset'];
    }
    #calcStartTilePositions(_0x83ee50, _0x252c97, _0x2c1278, _0x1a596c) {
        const _0x3f9d6e = logi['maps']['Utils']['longitude2TileX'](this.#mapCenter['lng'], _0x1a596c), _0x3d1f93 = logi['maps']['Utils']['latitude2TileY'](this.#mapCenter['lat'], _0x1a596c);
        _0x83ee50['tile']['x'] = parseInt(_0x3f9d6e), _0x83ee50['tile']['y'] = parseInt(_0x3d1f93), _0x252c97['tile']['x'] = parseInt(_0x3f9d6e), _0x252c97['tile']['y'] = parseInt(_0x3d1f93);
        const _0x278897 = (this.#screenRect['yMax'] - this.#screenRect['yMin']) / 0x2, _0x3733c1 = (this.#screenRect['xMax'] - this.#screenRect['xMin']) / 0x2, _0x2503a8 = this.#screenRect['xMin'] + _0x3733c1, _0x3f9d7c = this.#screenRect['yMin'] + _0x278897;
        _0x83ee50['screen']['x'] = _0x2503a8 - (_0x3f9d6e - Math['floor'](_0x3f9d6e)) * _0x2c1278['x'], _0x83ee50['screen']['y'] = _0x3f9d7c - (_0x3d1f93 - Math['floor'](_0x3d1f93)) * _0x2c1278['y'];
        if (_0x83ee50['screen']['x'] > 0x0) {
            const _0x46dcb2 = parseInt(Math['ceil'](_0x83ee50['screen']['x'] / _0x2c1278['x']));
            _0x83ee50['tile']['x'] -= _0x46dcb2, _0x83ee50['screen']['x'] -= _0x2c1278['x'] * _0x46dcb2;
        }
        if (_0x83ee50['screen']['y'] > 0x0) {
            const _0xd07804 = parseInt(Math['ceil'](_0x83ee50['screen']['y'] / _0x2c1278['y']));
            _0x83ee50['tile']['y'] -= _0xd07804, _0x83ee50['screen']['y'] -= _0x2c1278['y'] * _0xd07804;
        }
        _0x252c97['screen']['x'] = _0x2503a8 + (0x1 - (_0x3f9d6e - Math['floor'](_0x3f9d6e))) * _0x2c1278['x'], _0x252c97['screen']['y'] = _0x3f9d7c + (0x1 - (_0x3d1f93 - Math['floor'](_0x3d1f93))) * _0x2c1278['y'];
        if (_0x252c97['screen']['x'] + _0x2c1278['x'] < this.#screenRect['xMax']) {
            const _0x3ff110 = parseInt(Math['ceil']((this.#screenRect['xMax'] - (_0x252c97['screen']['x'] + _0x2c1278['x'])) / _0x2c1278['x']));
            _0x252c97['tile']['x'] += _0x3ff110, _0x252c97['screen']['x'] += _0x2c1278['x'] * _0x3ff110;
        }
        if (_0x252c97['screen']['y'] + _0x2c1278['y'] < this.#screenRect['yMax']) {
            const _0x428c34 = parseInt(Math['ceil']((this.#screenRect['yMax'] - (_0x252c97['screen']['y'] + _0x2c1278['y'])) / _0x2c1278['y']));
            _0x252c97['tile']['y'] += _0x428c34, _0x252c97['screen']['y'] += _0x2c1278['y'] * _0x428c34;
        }
    }
    ['getTilesOnScreen'](_0x55cea5 = null) {
        const _0x41cb63 = {
                'screen': new logi['maps']['Point'](),
                'tile': new logi['maps']['Point']()
            }, _0x5998c2 = {
                'screen': new logi['maps']['Point'](),
                'tile': new logi['maps']['Point']()
            }, _0x2b7c4d = _0x55cea5 ? _0x55cea5['tileLevel'] : this.#zoomInfo['tileLevel'], _0x1cc3b8 = _0x55cea5 ? _0x55cea5['tileLevelOffset'] : this.#zoomInfo['tileLevelOffset'], _0x47d047 = {
                'x': logi['maps']['Defines']['TILE_W'] * _0x1cc3b8,
                'y': logi['maps']['Defines']['TILE_H'] * _0x1cc3b8
            }, _0x14e563 = logi['maps']['Defines']['POW_TABLE'][_0x2b7c4d], _0x3fc054 = {
                'x': (this.#screenRect['xMax'] + this.#screenRect['xMin']) / 0x2,
                'y': (this.#screenRect['yMax'] + this.#screenRect['yMin']) / 0x2
            }, _0x403514 = new Array();
        this.#calcStartTilePositions(_0x41cb63, _0x5998c2, _0x47d047, _0x2b7c4d);
        for (let _0x14587f = _0x41cb63['screen']['y'], _0x16bfab = _0x41cb63['tile']['y']; _0x14587f <= this.#screenRect['yMax']; _0x14587f += _0x47d047['y'], ++_0x16bfab) {
            if (_0x16bfab >= 0x0 && _0x16bfab < _0x14e563)
                for (let _0xb5c144 = _0x41cb63['screen']['x'], _0x3e7228 = _0x41cb63['tile']['x']; _0xb5c144 <= this.#screenRect['xMax']; _0xb5c144 += _0x47d047['x'], ++_0x3e7228) {
                    while (_0x3e7228 < 0x0) {
                        _0x3e7228 += _0x14e563;
                    }
                    while (_0x3e7228 >= _0x14e563) {
                        _0x3e7228 -= _0x14e563;
                    }
                    const _0xacf3b9 = new logi['maps']['TileOnScreen']();
                    _0xacf3b9['tileUID']['setTile'](_0x3e7228, _0x16bfab, _0x2b7c4d), _0xacf3b9['rctOnScreen']['xMin'] = Math['floor'](_0xb5c144), _0xacf3b9['rctOnScreen']['yMin'] = Math['floor'](_0x14587f), _0xacf3b9['rctOnScreen']['xMax'] = Math['floor'](_0xb5c144) + Math['floor'](_0x47d047['x']), _0xacf3b9['rctOnScreen']['yMax'] = Math['floor'](_0x14587f) + Math['floor'](_0x47d047['y']);
                    if (logi['maps']['Utils']['rectOnRect'](_0xacf3b9['rctOnScreen']['xMin'], _0xacf3b9['rctOnScreen']['yMin'], _0xacf3b9['rctOnScreen']['xMax'], _0xacf3b9['rctOnScreen']['yMax'], this.#screenRect['xMin'], this.#screenRect['yMin'], this.#screenRect['xMax'], this.#screenRect['yMax'])) {
                        const _0x3272a0 = _0xb5c144 + _0x47d047['x'] / 0x2 - _0x3fc054['x'], _0x1e3a16 = _0x14587f + _0x47d047['y'] / 0x2 - _0x3fc054['y'];
                        _0xacf3b9['distanceFromCenter'] = Math['sqrt'](_0x3272a0 * _0x3272a0 + _0x1e3a16 * _0x1e3a16), _0x403514['push'](_0xacf3b9);
                    }
                }
        }
        return _0x403514['sort'](function (_0x5d6897, _0x3aafa7) {
            return _0x5d6897['distanceFromCenter'] - _0x3aafa7['distanceFromCenter'];
        }), _0x403514;
    }
    ['getTileInfo'](_0x1fcc24, _0x5c78e0, _0x53662b) {
        const _0x43d882 = Math['floor'](logi['maps']['Utils']['longitude2TileX'](_0x1fcc24, _0x53662b)), _0x39beae = Math['floor'](logi['maps']['Utils']['latitude2TileY'](_0x5c78e0, _0x53662b)), _0x4f0ca2 = logi['maps']['Utils']['tileX2Longitude'](_0x43d882, _0x53662b), _0x598fb8 = logi['maps']['Utils']['tileY2Latitude'](_0x39beae + 0x1, _0x53662b), _0x25a0ed = logi['maps']['Utils']['tileX2Longitude'](_0x43d882 + 0x1, _0x53662b), _0x39a8ef = logi['maps']['Utils']['tileY2Latitude'](_0x39beae, _0x53662b);
        let _0x36b798 = new logi['maps']['TileUID']();
        _0x36b798['setTile'](_0x43d882, _0x39beae, _0x53662b);
        let _0x50e209 = new logi['maps']['TileInfo']();
        return _0x50e209['tileId'] = _0x36b798['getId'](), _0x50e209['boundary']['xMin'] = _0x4f0ca2, _0x50e209['boundary']['yMin'] = _0x598fb8, _0x50e209['boundary']['xMax'] = _0x25a0ed, _0x50e209['boundary']['yMax'] = _0x39a8ef, _0x50e209;
    }
    ['getTilesInfo'](_0x492cd0, _0x5e6b1c, _0x5ad2ba, _0x112725, _0x2e550b) {
        let _0x48f75b = Math['floor'](logi['maps']['Utils']['longitude2TileX'](_0x492cd0, _0x2e550b)), _0x25f4e5 = Math['floor'](logi['maps']['Utils']['latitude2TileY'](_0x5ad2ba, _0x2e550b)), _0x30c069 = Math['floor'](logi['maps']['Utils']['longitude2TileX'](_0x5e6b1c, _0x2e550b)), _0x19b32c = Math['floor'](logi['maps']['Utils']['latitude2TileY'](_0x112725, _0x2e550b));
        const _0x4b7a27 = logi['maps']['Defines']['POW_TABLE'][_0x2e550b];
        _0x48f75b > _0x30c069 && (_0x30c069 += _0x4b7a27);
        let _0x21dcec = [];
        for (let _0x256c18 = _0x25f4e5; _0x256c18 <= _0x19b32c; ++_0x256c18) {
            for (let _0x1aed27 = _0x48f75b; _0x1aed27 <= _0x30c069; ++_0x1aed27) {
                const _0x20c82a = new logi['maps']['TileUID']();
                _0x1aed27 < _0x4b7a27 ? _0x20c82a['setTile'](_0x1aed27, _0x256c18, _0x2e550b) : _0x20c82a['setTile'](_0x1aed27 - _0x4b7a27, _0x256c18, _0x2e550b), _0x21dcec['push'](_0x20c82a['getId']());
            }
        }
        return _0x21dcec;
    }
};
export default logi['maps']['Coord'];