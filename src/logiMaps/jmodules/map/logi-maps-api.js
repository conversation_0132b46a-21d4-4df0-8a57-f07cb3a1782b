import a0_0x3578e3 from '../common/logi-maps-types.js?v=2.1.10.1';
import a0_0x4e520e from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a0_0x4c6e2e from '../utility/logi-maps-bounds.js?v=2.1.10.1';
import a0_0x34b268 from '../object/logi-maps-textinfo.js?v=2.1.10.1';
import a0_0x7edc26 from '../object/logi-maps-image.js?v=2.1.10.1';
import a0_0x409c79 from '../object/logi-maps-label.js?v=2.1.10.1';
import a0_0x1a4127 from '../object/logi-maps-polygon.js?v=2.1.10.1';
import a0_0x4902c8 from '../object/logi-maps-circle.js?v=2.1.10.1';
import a0_0x1343bf from '../object/logi-maps-line.js?v=2.1.10.1';
import a0_0xd4e91f from '../object/logi-maps-route.js?v=2.1.10.1';
import a0_0x515b35 from '../object/logi-maps-gps.js?v=2.1.10.1';
import a0_0x1fa4ff from '../object/logi-maps-custom.js?v=2.1.10.1';
import a0_0x1a77a9 from '../object/logi-maps-meta.js?v=2.1.10.1';
import a0_0x4bf547 from '../map/logi-maps-map.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['EVENT'] = a0_0x3578e3['EVENT'], logi['maps']['OBJEVENT'] = a0_0x3578e3['OBJEVENT'], logi['maps']['BRIDGE_MAPEVENT'] = a0_0x3578e3['BRIDGE_MAPEVENT'], logi['maps']['ALIGN'] = a0_0x3578e3['ALIGN'], logi['maps']['LINETYPE'] = a0_0x3578e3['LINETYPE'], logi['maps']['LatLng'] = a0_0x3578e3['LatLng'], logi['maps']['Point'] = a0_0x3578e3['Point'], logi['maps']['MapRect'] = a0_0x3578e3['MapRect'], logi['maps']['DISTRICT_VISIBLETYPE'] = a0_0x3578e3['DISTRICT_VISIBLETYPE'], logi['maps']['DISTRICT_DATATYPE'] = a0_0x3578e3['DISTRICT_DATATYPE'], logi['maps']['Utils'] = a0_0x4e520e, logi['maps']['Bounds'] = a0_0x4c6e2e, logi['maps']['TextInfo'] = a0_0x34b268, logi['maps']['Image'] = a0_0x7edc26, logi['maps']['Label'] = a0_0x409c79, logi['maps']['Polygon'] = a0_0x1a4127, logi['maps']['Circle'] = a0_0x4902c8, logi['maps']['Line'] = a0_0x1343bf, logi['maps']['Route'] = a0_0xd4e91f, logi['maps']['Gps'] = a0_0x515b35, logi['maps']['Custom'] = a0_0x1fa4ff, logi['maps']['Meta'] = a0_0x1a77a9, logi['maps']['Map'] = a0_0x4bf547;
export default logi;