import a3_0x2e2793 from '../utility/logi-maps-utils.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Utils'] = a3_0x2e2793, logi['maps']['Bridge'] = class {
    static ['loadFinished']() {
        if (logi['maps']['Utils']['isAndroid']())
            window['BRIDGE']['loadFinished']();
        else
            logi['maps']['Utils']['isIOS']() && window['webkit']['messageHandlers']['BRIDGE']['postMessage']('loadFinished');
    }
    static ['onMapEvent'](_0x38e8a9, _0x49fb67 = -0x1, _0x3f68a8 = -0x1) {
        if (logi['maps']['Utils']['isAndroid']())
            window['BRIDGE']['onMapEvent'](_0x38e8a9, _0x49fb67, _0x3f68a8);
        else {
            if (logi['maps']['Utils']['isIOS']()) {
                const _0x23e929 = {
                    'message': 'onMapEvent',
                    'type': _0x38e8a9,
                    'pointX': _0x49fb67,
                    'pointY': _0x3f68a8
                };
                window['webkit']['messageHandlers']['BRIDGE']['postMessage'](JSON['stringify'](_0x23e929));
            }
        }
    }
    static ['onObjEvent'](_0x644310) {
        if (logi['maps']['Utils']['isAndroid']())
            window['BRIDGE']['onObjEvent'](_0x644310['type'], _0x644310['source']['getKey'](), _0x644310['point']['x'], _0x644310['point']['y']);
        else {
            if (logi['maps']['Utils']['isIOS']()) {
                const _0x20aae5 = {
                    'message': 'onObjEvent',
                    'type': _0x644310['type'],
                    'key': _0x644310['source']['getKey'](),
                    'pointX': _0x644310['point']['x'],
                    'pointY': _0x644310['point']['y']
                };
                window['webkit']['messageHandlers']['BRIDGE']['postMessage'](JSON['stringify'](_0x20aae5));
            }
        }
    }
};
export default logi['maps']['Bridge'];