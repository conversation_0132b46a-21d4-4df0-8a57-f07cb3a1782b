import a0_0x323d97 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a0_0x3fe611 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Utils'] = a0_0x323d97, logi['maps']['BoundaryData'] = a0_0x3fe611, logi['maps']['BoundaryChecker'] = class {
    #storedBoundary;
    constructor() {
        this.#storedBoundary = [];
    }
    ['cleanBoundary']() {
        if (this.#storedBoundary['length'] == 0x0)
            return ![];
        return this.#storedBoundary = [], !![];
    }
    ['addBoundary'](_0x3fecf1, _0x622cea) {
        this.#storedBoundary['push'](_0x3fecf1), _0x622cea == !![] && this.#storedBoundary['sort'](logi['maps']['BoundaryData']['compare']);
    }
    ['isBoundaryOverlapped'](_0x38a89f) {
        let _0xed7e51 = new Array();
        if (_0x38a89f['groupId'] == 0x0)
            return ![];
        for (const _0x43eb36 of this.#storedBoundary) {
            if (_0x38a89f['groupId'] != _0x43eb36['groupId'])
                continue;
            if (_0x43eb36['bLoad'] != logi['maps']['BoundaryData']['STATUS']['LOAD'])
                continue;
            if (logi['maps']['BoundaryChecker']['circleOnRegion'](_0x43eb36['boundaryCircle'], _0x38a89f['boundaryCircle']) != !![])
                continue;
            if (logi['maps']['BoundaryChecker']['regionOnRegion'](_0x43eb36['boundaryRect'], _0x38a89f['boundaryRect']) != !![])
                continue;
            if (_0x43eb36['creationTick'] <= _0x38a89f['creationTick'])
                return _0x43eb36['overlapCnt'] += 0x1, !![];
            else {
                if (_0x43eb36['creationTick'] > _0x38a89f['creationTick']) {
                    _0xed7e51['push'](_0x43eb36), _0x38a89f['overlapCnt'] += 0x1;
                    continue;
                }
            }
        }
        for (const _0x5557e9 of _0xed7e51) {
            _0x5557e9['bLoad'] = logi['maps']['BoundaryData']['STATUS']['OVERLAP'], _0x5557e9['overlapCnt'] = 0x0, _0x5557e9['creationTick'] = logi['maps']['Utils']['getCurTick']();
        }
        return ![];
    }
    static #ORIENTATION(_0x31c6d0, _0x301c90, _0x181782, _0x5708ef, _0x24cd45, _0x5297f1) {
        return (_0x31c6d0 - _0x24cd45) * (_0x5708ef - _0x5297f1) - (_0x301c90 - _0x5297f1) * (_0x181782 - _0x24cd45);
    }
    static ['pointInRegion'](_0x2df0d8, _0x1cd507) {
        let _0x4aa989, _0x265c35, _0x3cc1cf = ![];
        const _0x142dd9 = _0x1cd507['length'];
        _0x265c35 = _0x142dd9 - 0x1;
        for (_0x4aa989 = 0x0; _0x4aa989 < _0x142dd9; _0x4aa989++) {
            (_0x1cd507[_0x265c35]['y'] <= _0x2df0d8['y'] && _0x2df0d8['y'] < _0x1cd507[_0x4aa989]['y'] && this.#ORIENTATION(_0x1cd507[_0x4aa989]['x'], _0x1cd507[_0x4aa989]['y'], _0x1cd507[_0x265c35]['x'], _0x1cd507[_0x265c35]['y'], _0x2df0d8['x'], _0x2df0d8['y']) > 0x0 || _0x1cd507[_0x4aa989]['y'] <= _0x2df0d8['y'] && _0x2df0d8['y'] < _0x1cd507[_0x265c35]['y'] && this.#ORIENTATION(_0x1cd507[_0x265c35]['x'], _0x1cd507[_0x265c35]['y'], _0x1cd507[_0x4aa989]['x'], _0x1cd507[_0x4aa989]['y'], _0x2df0d8['x'], _0x2df0d8['y']) > 0x0) && (_0x3cc1cf = !_0x3cc1cf), _0x265c35 = _0x4aa989;
        }
        return _0x3cc1cf;
    }
    static ['regionOnRegion'](_0x201630, _0xa72570) {
        for (let _0x470286 = 0x0; _0x470286 < _0x201630['length']; ++_0x470286) {
            if (logi['maps']['BoundaryChecker']['pointInRegion'](_0x201630[_0x470286], _0xa72570))
                return !![];
        }
        for (let _0x28a886 = 0x0; _0x28a886 < _0xa72570['length']; ++_0x28a886) {
            if (logi['maps']['BoundaryChecker']['pointInRegion'](_0xa72570[_0x28a886], _0x201630))
                return !![];
        }
        let _0x3629f1 = 0x0, _0x2ed8b1 = 0x0, _0x48633f = 0x0, _0x3691c4 = 0x0;
        for (_0x3629f1 = 0x0; _0x3629f1 < _0x201630['length']; ++_0x3629f1) {
            _0x2ed8b1 = (_0x3629f1 + 0x1) % _0x201630['length'];
            for (_0x48633f = 0x0; _0x48633f < _0xa72570['length']; ++_0x48633f) {
                _0x3691c4 = (_0x48633f + 0x1) % _0xa72570['length'];
                if (this.#isLineIntersect(_0x201630[_0x3629f1], _0x201630[_0x2ed8b1], _0xa72570[_0x48633f], _0xa72570[_0x3691c4]) == !![])
                    return !![];
            }
        }
        return ![];
    }
    static ['circleOnRegion'](_0xcbe558, _0x1a32f8) {
        const _0x553697 = _0xcbe558['center']['x'] - _0x1a32f8['center']['x'], _0x33fb31 = _0xcbe558['center']['y'] - _0x1a32f8['center']['y'], _0x2b6c20 = Math['sqrt'](_0x553697 * _0x553697 + _0x33fb31 * _0x33fb31);
        if (_0x2b6c20 > _0xcbe558['maxR'] + _0x1a32f8['maxR'])
            return ![];
        return !![];
    }
    static ['pointInPolyline'](_0xfc30d2, _0x3b0e3c, _0x3b7e66, _0x326202) {
        const _0x43cae5 = {
            'x': 0x0,
            'y': 0x0
        };
        _0x43cae5['x'] = _0xfc30d2['x'] - (_0x326202?.['x'] ?? 0x0), _0x43cae5['y'] = _0xfc30d2['y'] - (_0x326202?.['y'] ?? 0x0);
        const _0x8d4277 = _0x3b7e66 * 0.5 + 0x2;
        let _0xb3621 = null;
        for (const _0x4bf53e of _0x3b0e3c) {
            if (_0xb3621 != null) {
                const _0x2000a8 = logi['maps']['Utils']['getPerpendicularPoint'](_0xb3621, _0x4bf53e, _0x43cae5);
                if (_0x2000a8['distance'] <= _0x8d4277)
                    return !![];
            }
            _0xb3621 = _0x4bf53e;
        }
        return ![];
    }
    static ['regionOnPolyline'](_0x374990, _0x8bff53, _0x1f586e, _0x364150) {
        const _0x112108 = [];
        for (const _0x50a28e of _0x374990) {
            _0x112108['push']({
                'x': _0x50a28e['x'] - (_0x364150?.['x'] ?? 0x0),
                'y': _0x50a28e['y'] - (_0x364150?.['y'] ?? 0x0)
            });
        }
        for (let _0x1337eb = 0x0; _0x1337eb < _0x112108['length']; ++_0x1337eb) {
            if (logi['maps']['BoundaryChecker']['pointInPolyline'](_0x112108[_0x1337eb], _0x8bff53, _0x1f586e))
                return !![];
        }
        for (let _0x551d3c = 0x0; _0x551d3c < _0x8bff53['length']; ++_0x551d3c) {
            if (logi['maps']['BoundaryChecker']['pointInRegion'](_0x8bff53[_0x551d3c], _0x112108))
                return !![];
        }
        let _0xab9085 = 0x0, _0x11d498 = 0x0, _0x272cc5 = 0x0, _0x264547 = 0x0;
        for (_0xab9085 = 0x0; _0xab9085 < _0x112108['length']; ++_0xab9085) {
            _0x11d498 = (_0xab9085 + 0x1) % _0x112108['length'];
            for (_0x272cc5 = 0x0; _0x272cc5 < _0x8bff53['length']; ++_0x272cc5) {
                _0x264547 = (_0x272cc5 + 0x1) % _0x8bff53['length'];
                if (this.#isLineIntersect(_0x112108[_0xab9085], _0x112108[_0x11d498], _0x8bff53[_0x272cc5], _0x8bff53[_0x264547]) == !![])
                    return !![];
            }
        }
        return ![];
    }
    static ['pointInPolygon'](_0xec44ce, _0x1fede5, _0x409572) {
        let _0x94d574 = ![];
        if (_0x1fede5['length'] >= 0x4) {
            const _0x596996 = {
                'x': 0x0,
                'y': 0x0
            };
            _0x596996['x'] = _0xec44ce['x'] - (_0x409572?.['x'] ?? 0x0), _0x596996['y'] = _0xec44ce['y'] - (_0x409572?.['y'] ?? 0x0);
            let _0x4fdc16 = _0x1fede5[_0x1fede5['length'] - 0x1];
            for (const _0x50a719 of _0x1fede5) {
                const _0x3e6d07 = _0x50a719['y'] > _0x596996['y'] != _0x4fdc16['y'] > _0x596996['y'] && _0x596996['x'] < (_0x4fdc16['x'] - _0x50a719['x']) * (_0x596996['y'] - _0x50a719['y']) / (_0x4fdc16['y'] - _0x50a719['y']) + _0x50a719['x'];
                _0x3e6d07 && (_0x94d574 = !_0x94d574), _0x4fdc16 = _0x50a719;
            }
        }
        return _0x94d574;
    }
    static ['regionOnPolygon'](_0xf87ffb, _0x44daad, _0x909adc) {
        const _0x3df4af = [];
        for (const _0x379b37 of _0xf87ffb) {
            _0x3df4af['push']({
                'x': _0x379b37['x'] - (_0x909adc?.['x'] ?? 0x0),
                'y': _0x379b37['y'] - (_0x909adc?.['y'] ?? 0x0)
            });
        }
        for (let _0x22c8ea = 0x0; _0x22c8ea < _0x3df4af['length']; ++_0x22c8ea) {
            if (logi['maps']['BoundaryChecker']['pointInPolygon'](_0x3df4af[_0x22c8ea], _0x44daad))
                return !![];
        }
        for (let _0x410a3d = 0x0; _0x410a3d < _0x44daad['length']; ++_0x410a3d) {
            if (logi['maps']['BoundaryChecker']['pointInRegion'](_0x44daad[_0x410a3d], _0x3df4af))
                return !![];
        }
        let _0x2c4a87 = 0x0, _0x3d6b71 = 0x0, _0x3290bf = 0x0, _0x12f79f = 0x0;
        for (_0x2c4a87 = 0x0; _0x2c4a87 < _0x3df4af['length']; ++_0x2c4a87) {
            _0x3d6b71 = (_0x2c4a87 + 0x1) % _0x3df4af['length'];
            for (_0x3290bf = 0x0; _0x3290bf < _0x44daad['length']; ++_0x3290bf) {
                _0x12f79f = (_0x3290bf + 0x1) % _0x44daad['length'];
                if (this.#isLineIntersect(_0x3df4af[_0x2c4a87], _0x3df4af[_0x3d6b71], _0x44daad[_0x3290bf], _0x44daad[_0x12f79f]) == !![])
                    return !![];
            }
        }
        return ![];
    }
    static ['pointInCircle'](_0x4f8cf5, _0x15b574, _0x26a451) {
        const _0x42052f = Math['sqrt'](Math['pow'](_0x4f8cf5['x'] - _0x15b574['x'], 0x2) + Math['pow'](_0x4f8cf5['y'] - _0x15b574['y'], 0x2));
        return _0x42052f <= _0x26a451;
    }
    static ['regionOnCircle'](_0x14ac6f, _0x2d3fed, _0x5d7099) {
        for (let _0x5f2b34 = 0x0; _0x5f2b34 < _0x14ac6f['length']; ++_0x5f2b34) {
            if (logi['maps']['BoundaryChecker']['pointInCircle'](_0x14ac6f[_0x5f2b34], _0x2d3fed, _0x5d7099))
                return !![];
        }
        return ![];
    }
    static #onSegment(_0xc8878c, _0x589a2f, _0x425995) {
        if (_0x589a2f['x'] <= Math['max'](_0xc8878c['x'], _0x425995['x']) && _0x589a2f['x'] >= Math['min'](_0xc8878c['x'], _0x425995['x']) && _0x589a2f['y'] <= Math['max'](_0xc8878c['y'], _0x425995['y']) && _0x589a2f['y'] >= Math['min'](_0xc8878c['y'], _0x425995['y']))
            return !![];
        return ![];
    }
    static #orientation(_0x14ba00, _0x4283f7, _0x9cc681) {
        let _0x3bf0b0 = (_0x4283f7['y'] - _0x14ba00['y']) * (_0x9cc681['x'] - _0x4283f7['x']) - (_0x4283f7['x'] - _0x14ba00['x']) * (_0x9cc681['y'] - _0x4283f7['y']);
        if (_0x3bf0b0 == 0x0)
            return 0x0;
        return _0x3bf0b0 > 0x0 ? 0x1 : 0x2;
    }
    static #isLineIntersect(_0x56c2c4, _0x361c9f, _0x450444, _0x1fbca1) {
        let _0x3ed590 = this.#orientation(_0x56c2c4, _0x361c9f, _0x450444), _0x225853 = this.#orientation(_0x56c2c4, _0x361c9f, _0x1fbca1), _0x28a2d6 = this.#orientation(_0x450444, _0x1fbca1, _0x56c2c4), _0x3e402b = this.#orientation(_0x450444, _0x1fbca1, _0x361c9f);
        if (_0x3ed590 != _0x225853 && _0x28a2d6 != _0x3e402b)
            return !![];
        if (_0x3ed590 == 0x0 && this.#onSegment(_0x56c2c4, _0x450444, _0x361c9f))
            return !![];
        if (_0x225853 == 0x0 && this.#onSegment(_0x56c2c4, _0x1fbca1, _0x361c9f))
            return !![];
        if (_0x28a2d6 == 0x0 && this.#onSegment(_0x450444, _0x56c2c4, _0x1fbca1))
            return !![];
        if (_0x3e402b == 0x0 && this.#onSegment(_0x450444, _0x361c9f, _0x1fbca1))
            return !![];
        return ![];
    }
};
export default logi['maps']['BoundaryChecker'];