var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['BoundaryData'] = class {
    static ['STATUS'] = {
        'NOT_LOAD': 0x0,
        'LOAD': 0x1,
        'OVERLAP': 0x2,
        'LOAD_OUTSIDE': 0x3,
        'OVERLAP_OUTSIDE': 0x4
    };
    constructor() {
        this['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], this['groupId'] = 0x0, this['tagName'] = '', this['boundaryCircle'] = {
            'center': {
                'x': 0x0,
                'y': 0x0
            },
            'maxR': 0x0
        }, this['boundaryRect'] = [
            {
                'x': 0x0,
                'y': 0x0
            },
            {
                'x': 0x0,
                'y': 0x0
            },
            {
                'x': 0x0,
                'y': 0x0
            },
            {
                'x': 0x0,
                'y': 0x0
            }
        ], this['creationTick'] = 0x0, this['overlapCnt'] = 0x0;
    }
    static ['isSamePoint'](_0x388de9, _0x375e70) {
        return _0x388de9['x'] == _0x375e70['x'] && _0x388de9['y'] == _0x375e70['y'];
    }
    static ['compare'](_0x429d97, _0xb2f01d) {
        if (_0x429d97['creationTick'] == _0xb2f01d['creationTick']) {
            if (_0x429d97['tileId'] == _0xb2f01d['tileId'])
                return _0x429d97['tagName'] < _0xb2f01d['tagName'];
            return _0x429d97['tileId'] < _0xb2f01d['tileId'];
        }
        return _0x429d97['creationTick'] < _0xb2f01d['creationTick'];
    }
    ['setBoundary'](_0x24bd10, _0x510a7d) {
        this['boundaryCircle']['center']['x'] = _0x24bd10['center']['x'], this['boundaryCircle']['center']['y'] = _0x24bd10['center']['y'], this['boundaryCircle']['maxR'] = _0x24bd10['maxR'];
        for (let _0x1a056e = 0x0; _0x1a056e < 0x4; ++_0x1a056e) {
            this['boundaryRect'][_0x1a056e]['x'] = _0x510a7d[_0x1a056e]['x'], this['boundaryRect'][_0x1a056e]['y'] = _0x510a7d[_0x1a056e]['y'];
        }
    }
    static ['copy'](_0x172caa, _0x4e239e) {
        _0x172caa['creationTick'] = _0x4e239e['creationTick'], _0x172caa['bLoad'] = _0x4e239e['bLoad'], _0x172caa['groupId'] = _0x4e239e['groupId'], _0x172caa['tagName'] = _0x4e239e['tagName'], _0x172caa['boundaryCircle']['center']['x'] = _0x4e239e['boundaryCircle']['center']['x'], _0x172caa['boundaryCircle']['center']['y'] = _0x4e239e['boundaryCircle']['center']['y'], _0x172caa['boundaryCircle']['maxR'] = _0x4e239e['boundaryCircle']['maxR'], _0x172caa['boundaryRect'][0x0]['x'] = _0x4e239e['boundaryRect'][0x0]['x'], _0x172caa['boundaryRect'][0x0]['y'] = _0x4e239e['boundaryRect'][0x0]['y'], _0x172caa['boundaryRect'][0x1]['x'] = _0x4e239e['boundaryRect'][0x1]['x'], _0x172caa['boundaryRect'][0x1]['y'] = _0x4e239e['boundaryRect'][0x1]['y'], _0x172caa['boundaryRect'][0x2]['x'] = _0x4e239e['boundaryRect'][0x2]['x'], _0x172caa['boundaryRect'][0x2]['y'] = _0x4e239e['boundaryRect'][0x2]['y'], _0x172caa['boundaryRect'][0x3]['x'] = _0x4e239e['boundaryRect'][0x3]['x'], _0x172caa['boundaryRect'][0x3]['y'] = _0x4e239e['boundaryRect'][0x3]['y'];
    }
    static ['merge'](_0x515596, _0x5f1140) {
        _0x515596[0x0]['x'] = _0x515596[0x0]['x'] < _0x5f1140['lxMin'] ? _0x515596[0x0]['x'] : _0x5f1140['lxMin'], _0x515596[0x3]['x'] = _0x515596[0x3]['x'] < _0x5f1140['lxMin'] ? _0x515596[0x3]['x'] : _0x5f1140['lxMin'], _0x515596[0x1]['x'] = _0x515596[0x1]['x'] > _0x5f1140['lxMax'] ? _0x515596[0x1]['x'] : _0x5f1140['lxMax'], _0x515596[0x2]['x'] = _0x515596[0x2]['x'] > _0x5f1140['lxMax'] ? _0x515596[0x2]['x'] : _0x5f1140['lxMax'], _0x515596[0x0]['y'] = _0x515596[0x0]['y'] < _0x5f1140['lyMin'] ? _0x515596[0x0]['y'] : _0x5f1140['lyMin'], _0x515596[0x1]['y'] = _0x515596[0x1]['y'] < _0x5f1140['lyMin'] ? _0x515596[0x1]['y'] : _0x5f1140['lyMin'], _0x515596[0x2]['y'] = _0x515596[0x2]['y'] > _0x5f1140['lyMax'] ? _0x515596[0x2]['y'] : _0x5f1140['lyMax'], _0x515596[0x3]['y'] = _0x515596[0x3]['y'] > _0x5f1140['lyMax'] ? _0x515596[0x3]['y'] : _0x5f1140['lyMax'];
    }
};
export default logi['maps']['BoundaryData'];