import a2_0x12bb7e from '../common/logi-maps-defines.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a2_0x12bb7e, logi['maps']['Bounds'] = class {
    #northEast = {
        'lat': -0x5a,
        'lng': -0xb4
    };
    #southWest = {
        'lat': 0x5a,
        'lng': 0xb4
    };
    constructor() {
        this.#northEast = {
            'lat': -0x5a,
            'lng': -0xb4
        }, this.#southWest = {
            'lat': 0x5a,
            'lng': 0xb4
        };
    }
    ['extend'](_0x56e09d) {
        const _0x24dca9 = _0x56e09d['lat'], _0x3e07bc = _0x56e09d['lng'];
        _0x24dca9 > this.#northEast['lat'] && (this.#northEast['lat'] = _0x24dca9), _0x24dca9 < this.#southWest['lat'] && (this.#southWest['lat'] = _0x24dca9), _0x3e07bc > this.#northEast['lng'] && (this.#northEast['lng'] = _0x3e07bc), _0x3e07bc < this.#southWest['lng'] && (this.#southWest['lng'] = _0x3e07bc);
    }
    ['setNorthEast'](_0x2d8139, _0x5792bc) {
        this.#northEast['lat'] = _0x2d8139, this.#northEast['lng'] = _0x5792bc;
    }
    ['setSouthWest'](_0x1f9aaf, _0x275d80) {
        this.#southWest['lat'] = _0x1f9aaf, this.#southWest['lng'] = _0x275d80;
    }
    ['getNorthEast']() {
        return this.#northEast;
    }
    ['getSouthWest']() {
        return this.#southWest;
    }
    ['getCenter']() {
        const _0x8667b1 = (this.#northEast['lat'] + this.#southWest['lat']) / 0x2, _0x165037 = this.#calculateMidLongitude(this.#southWest['lng'], this.#northEast['lng']);
        return {
            'lat': _0x8667b1,
            'lng': _0x165037
        };
    }
    #calculateMidLongitude(_0x357b10, _0x5143e2) {
        let _0x2bba74 = _0x5143e2 - _0x357b10;
        while (_0x2bba74 > 0xb4) {
            _0x2bba74 -= 0x168;
        }
        while (_0x2bba74 < -0xb4) {
            _0x2bba74 += 0x168;
        }
        let _0x2a28c0 = (_0x357b10 + _0x2bba74 / 0x2) % 0x168;
        while (_0x2a28c0 > 0xb4) {
            _0x2a28c0 -= 0x168;
        }
        while (_0x2a28c0 < -0xb4) {
            _0x2a28c0 += 0x168;
        }
        return _0x2a28c0;
    }
    ['adjustCenter'](_0x4a97f4, _0x3d7e82, _0x2f14f0) {
        const _0x223119 = _0x4a97f4['lat'] - (_0x2f14f0['top'] - _0x2f14f0['bottom']) / _0x3d7e82['height'], _0x44789c = _0x4a97f4['lng'] - (_0x2f14f0['left'] - _0x2f14f0['right']) / _0x3d7e82['width'];
        return {
            'lat': _0x223119,
            'lng': _0x44789c
        };
    }
    ['calculateZoomLevel'](_0x413c5e, _0x1e582a) {
        const _0x1516c8 = _0x413c5e['lat'] - _0x1e582a['lat'], _0x51aae1 = Math['abs'](_0x413c5e['lng'] - _0x1e582a['lng']), _0x20bc04 = Math['log2'](0x168 / _0x1516c8), _0x49c50b = Math['log2'](0x168 / _0x51aae1), _0x10b9ee = Math['min'](_0x20bc04, _0x49c50b), _0x166e74 = Math['max'](logi['maps']['Defines']['MIN_LEVEL'], Math['min'](logi['maps']['Defines']['MAX_LEVEL'], _0x10b9ee));
        return _0x166e74;
    }
};
export default logi['maps']['Bounds'];