import a5_0x367cd1 from '../common/logi-maps-defines.js?v=2.1.10.1';
import a5_0x2e7ccd from '../common/logi-maps-types.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a5_0x367cd1, logi['maps']['ALIGN'] = a5_0x2e7ccd['ALIGN'], logi['maps']['GlMath'] = class {
    static ['loadIdentityMatrix']() {
        return [
            0x1,
            0x0,
            0x0,
            0x0,
            0x0,
            0x1,
            0x0,
            0x0,
            0x0,
            0x0,
            0x1,
            0x0,
            0x0,
            0x0,
            0x0,
            0x1
        ];
    }
    static ['multiplyMatrix'](_0x55d48f, _0xac0cf9) {
        const _0x5859 = _0x55d48f[0x0 * 0x4 + 0x0], _0x5054c2 = _0x55d48f[0x0 * 0x4 + 0x1], _0x4d03e1 = _0x55d48f[0x0 * 0x4 + 0x2], _0x3a73c4 = _0x55d48f[0x0 * 0x4 + 0x3], _0x2098ed = _0x55d48f[0x1 * 0x4 + 0x0], _0x11926a = _0x55d48f[0x1 * 0x4 + 0x1], _0x20ef16 = _0x55d48f[0x1 * 0x4 + 0x2], _0x133d2a = _0x55d48f[0x1 * 0x4 + 0x3], _0x1abea6 = _0x55d48f[0x2 * 0x4 + 0x0], _0x38ea83 = _0x55d48f[0x2 * 0x4 + 0x1], _0x4e5f6c = _0x55d48f[0x2 * 0x4 + 0x2], _0x64599a = _0x55d48f[0x2 * 0x4 + 0x3], _0x150aa4 = _0x55d48f[0x3 * 0x4 + 0x0], _0x3eb5d7 = _0x55d48f[0x3 * 0x4 + 0x1], _0x184608 = _0x55d48f[0x3 * 0x4 + 0x2], _0x25b6c0 = _0x55d48f[0x3 * 0x4 + 0x3], _0x59052e = _0xac0cf9[0x0 * 0x4 + 0x0], _0x2ead8b = _0xac0cf9[0x0 * 0x4 + 0x1], _0x5784b6 = _0xac0cf9[0x0 * 0x4 + 0x2], _0x5d0be6 = _0xac0cf9[0x0 * 0x4 + 0x3], _0x2ab63b = _0xac0cf9[0x1 * 0x4 + 0x0], _0x32f772 = _0xac0cf9[0x1 * 0x4 + 0x1], _0x2e13ac = _0xac0cf9[0x1 * 0x4 + 0x2], _0x12c7e5 = _0xac0cf9[0x1 * 0x4 + 0x3], _0x5dc58b = _0xac0cf9[0x2 * 0x4 + 0x0], _0x31b316 = _0xac0cf9[0x2 * 0x4 + 0x1], _0x5e5368 = _0xac0cf9[0x2 * 0x4 + 0x2], _0x425b04 = _0xac0cf9[0x2 * 0x4 + 0x3], _0x5f07c3 = _0xac0cf9[0x3 * 0x4 + 0x0], _0x1bdc62 = _0xac0cf9[0x3 * 0x4 + 0x1], _0x3b45f0 = _0xac0cf9[0x3 * 0x4 + 0x2], _0x40baa3 = _0xac0cf9[0x3 * 0x4 + 0x3];
        return [
            _0x59052e * _0x5859 + _0x2ead8b * _0x2098ed + _0x5784b6 * _0x1abea6 + _0x5d0be6 * _0x150aa4,
            _0x59052e * _0x5054c2 + _0x2ead8b * _0x11926a + _0x5784b6 * _0x38ea83 + _0x5d0be6 * _0x3eb5d7,
            _0x59052e * _0x4d03e1 + _0x2ead8b * _0x20ef16 + _0x5784b6 * _0x4e5f6c + _0x5d0be6 * _0x184608,
            _0x59052e * _0x3a73c4 + _0x2ead8b * _0x133d2a + _0x5784b6 * _0x64599a + _0x5d0be6 * _0x25b6c0,
            _0x2ab63b * _0x5859 + _0x32f772 * _0x2098ed + _0x2e13ac * _0x1abea6 + _0x12c7e5 * _0x150aa4,
            _0x2ab63b * _0x5054c2 + _0x32f772 * _0x11926a + _0x2e13ac * _0x38ea83 + _0x12c7e5 * _0x3eb5d7,
            _0x2ab63b * _0x4d03e1 + _0x32f772 * _0x20ef16 + _0x2e13ac * _0x4e5f6c + _0x12c7e5 * _0x184608,
            _0x2ab63b * _0x3a73c4 + _0x32f772 * _0x133d2a + _0x2e13ac * _0x64599a + _0x12c7e5 * _0x25b6c0,
            _0x5dc58b * _0x5859 + _0x31b316 * _0x2098ed + _0x5e5368 * _0x1abea6 + _0x425b04 * _0x150aa4,
            _0x5dc58b * _0x5054c2 + _0x31b316 * _0x11926a + _0x5e5368 * _0x38ea83 + _0x425b04 * _0x3eb5d7,
            _0x5dc58b * _0x4d03e1 + _0x31b316 * _0x20ef16 + _0x5e5368 * _0x4e5f6c + _0x425b04 * _0x184608,
            _0x5dc58b * _0x3a73c4 + _0x31b316 * _0x133d2a + _0x5e5368 * _0x64599a + _0x425b04 * _0x25b6c0,
            _0x5f07c3 * _0x5859 + _0x1bdc62 * _0x2098ed + _0x3b45f0 * _0x1abea6 + _0x40baa3 * _0x150aa4,
            _0x5f07c3 * _0x5054c2 + _0x1bdc62 * _0x11926a + _0x3b45f0 * _0x38ea83 + _0x40baa3 * _0x3eb5d7,
            _0x5f07c3 * _0x4d03e1 + _0x1bdc62 * _0x20ef16 + _0x3b45f0 * _0x4e5f6c + _0x40baa3 * _0x184608,
            _0x5f07c3 * _0x3a73c4 + _0x1bdc62 * _0x133d2a + _0x3b45f0 * _0x64599a + _0x40baa3 * _0x25b6c0
        ];
    }
    static ['translationMatrix'](_0x303faa, _0x2ec02b, _0x47293a) {
        return [
            0x1,
            0x0,
            0x0,
            0x0,
            0x0,
            0x1,
            0x0,
            0x0,
            0x0,
            0x0,
            0x1,
            0x0,
            _0x303faa,
            _0x2ec02b,
            _0x47293a,
            0x1
        ];
    }
    static ['rotationMatrix'](_0x3d0010, _0x26e95b) {
        const _0x42e5ff = Math['cos'](_0x26e95b), _0xfc0b30 = Math['sin'](_0x26e95b);
        switch (_0x3d0010) {
        case 'x':
            return [
                0x1,
                0x0,
                0x0,
                0x0,
                0x0,
                _0x42e5ff,
                _0xfc0b30,
                0x0,
                0x0,
                -_0xfc0b30,
                _0x42e5ff,
                0x0,
                0x0,
                0x0,
                0x0,
                0x1
            ];
        case 'y':
            return [
                _0x42e5ff,
                0x0,
                -_0xfc0b30,
                0x0,
                0x0,
                0x1,
                0x0,
                0x0,
                _0xfc0b30,
                0x0,
                _0x42e5ff,
                0x0,
                0x0,
                0x0,
                0x0,
                0x1
            ];
        case 'z':
            return [
                _0x42e5ff,
                _0xfc0b30,
                0x0,
                0x0,
                -_0xfc0b30,
                _0x42e5ff,
                0x0,
                0x0,
                0x0,
                0x0,
                0x1,
                0x0,
                0x0,
                0x0,
                0x0,
                0x1
            ];
        default:
            return [
                0x1,
                0x0,
                0x0,
                0x0,
                0x0,
                0x1,
                0x0,
                0x0,
                0x0,
                0x0,
                0x1,
                0x0,
                0x0,
                0x0,
                0x0,
                0x1
            ];
        }
    }
    static ['scalingMatrix'](_0xc6185e, _0x24100b, _0x1f4133) {
        return [
            _0xc6185e,
            0x0,
            0x0,
            0x0,
            0x0,
            _0x24100b,
            0x0,
            0x0,
            0x0,
            0x0,
            _0x1f4133,
            0x0,
            0x0,
            0x0,
            0x0,
            0x1
        ];
    }
    static ['translateMatrix'](_0x46c4fb, _0x239571, _0x5f34fb, _0x4fef60) {
        return this['multiplyMatrix'](_0x46c4fb, this['translationMatrix'](_0x239571, _0x5f34fb, _0x4fef60));
    }
    static ['rotateMatrix'](_0x29ba49, _0x24c1e7, _0x185dd8) {
        return this['multiplyMatrix'](_0x29ba49, this['rotationMatrix'](_0x24c1e7, _0x185dd8));
    }
    static ['scaleMatrix'](_0x48e3f5, _0x27f7ab, _0x50b92d, _0x4e980f) {
        return this['multiplyMatrix'](_0x48e3f5, this['scalingMatrix'](_0x27f7ab, _0x50b92d, _0x4e980f));
    }
    static ['orthographic'](_0x1aab44, _0x42efaa, _0x2f42cb, _0x2bd1b6, _0x271b6b, _0x57b930) {
        return [
            0x2 / (_0x42efaa - _0x1aab44),
            0x0,
            0x0,
            0x0,
            0x0,
            0x2 / (_0x2bd1b6 - _0x2f42cb),
            0x0,
            0x0,
            0x0,
            0x0,
            0x2 / (_0x271b6b - _0x57b930),
            0x0,
            (_0x1aab44 + _0x42efaa) / (_0x1aab44 - _0x42efaa),
            (_0x2f42cb + _0x2bd1b6) / (_0x2f42cb - _0x2bd1b6),
            (_0x271b6b + _0x57b930) / (_0x271b6b - _0x57b930),
            0x1
        ];
    }
    static ['magnitude'](_0x365a9f) {
        return Math['sqrt'](_0x365a9f['x'] * _0x365a9f['x'] + _0x365a9f['y'] * _0x365a9f['y']);
    }
    static ['normalize'](_0x576420) {
        const _0x210add = this['magnitude'](_0x576420);
        if (_0x210add == 0x0)
            return null;
        return {
            'x': _0x576420['x'] / _0x210add,
            'y': _0x576420['y'] / _0x210add
        };
    }
    static ['rotateVector'](_0x277729, _0x9df2bb) {
        const _0x1b2a42 = _0x9df2bb * (Math['PI'] / 0xb4), _0x38e46e = Math['cos'](_0x1b2a42), _0x18d5be = Math['sin'](_0x1b2a42);
        return {
            'x': _0x38e46e * _0x277729['x'] - _0x18d5be * _0x277729['y'],
            'y': _0x18d5be * _0x277729['x'] + _0x38e46e * _0x277729['y']
        };
    }
};
export default logi['maps']['GlMath'];