var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['DataView'] = class {
    #dataView;
    #offset;
    #byteLength;
    constructor(_0x1b8182) {
        this.#dataView = _0x1b8182, this.#offset = 0x0, this.#byteLength = _0x1b8182['buffer']['byteLength'];
    }
    ['setOffset'](_0x175c42) {
        this.#offset = _0x175c42;
    }
    ['moveOffset'](_0xc60cf3) {
        this.#offset += _0xc60cf3;
    }
    ['getRemainingBytes']() {
        return this.#byteLength - this.#offset;
    }
    ['getFloat32']() {
        const _0x3d6bb0 = this.#dataView['getFloat32'](this.#offset, !![]);
        return this.#offset += 0x4, _0x3d6bb0;
    }
    ['getInt32']() {
        const _0x1619f8 = this.#dataView['getInt32'](this.#offset, !![]);
        return this.#offset += 0x4, _0x1619f8;
    }
    ['getUint32']() {
        const _0x465995 = this.#dataView['getUint32'](this.#offset, !![]);
        return this.#offset += 0x4, _0x465995;
    }
    ['getInt16']() {
        const _0x16e202 = this.#dataView['getInt16'](this.#offset, !![]);
        return this.#offset += 0x2, _0x16e202;
    }
    ['getUint16']() {
        const _0x3b8c7d = this.#dataView['getUint16'](this.#offset, !![]);
        return this.#offset += 0x2, _0x3b8c7d;
    }
    ['getInt8']() {
        const _0x50df0e = this.#dataView['getInt8'](this.#offset, !![]);
        return this.#offset += 0x1, _0x50df0e;
    }
    ['getUint8']() {
        const _0x38b1c7 = this.#dataView['getUint8'](this.#offset, !![]);
        return this.#offset += 0x1, _0x38b1c7;
    }
    ['getString'](_0x5df27d) {
        let _0x1bd0c0 = '';
        for (let _0x5b4b9b = this.#offset; _0x5b4b9b < _0x5df27d + this.#offset; ++_0x5b4b9b) {
            const _0x170e16 = this.#dataView['getUint8'](_0x5b4b9b);
            _0x1bd0c0 += String['fromCharCode'](_0x170e16);
        }
        return this.#offset += _0x5df27d, _0x1bd0c0;
    }
    ['getString32']() {
        let _0x203840 = '';
        const _0x2b907e = this['getUint32']();
        for (let _0x435fd0 = this.#offset; _0x435fd0 < _0x2b907e + this.#offset; ++_0x435fd0) {
            const _0x401e53 = this.#dataView['getUint8'](_0x435fd0);
            _0x203840 += String['fromCharCode'](_0x401e53);
        }
        return this.#offset += _0x2b907e, _0x203840;
    }
    ['getString16']() {
        let _0x155564 = '';
        const _0x368254 = this['getUint16']();
        for (let _0x133b53 = this.#offset; _0x133b53 < _0x368254 + this.#offset; ++_0x133b53) {
            const _0x3ffb2c = this.#dataView['getUint8'](_0x133b53);
            _0x155564 += String['fromCharCode'](_0x3ffb2c);
        }
        return this.#offset += _0x368254, _0x155564;
    }
    ['getString8']() {
        let _0x3662e6 = '';
        const _0x9affae = this['getUint8']();
        for (let _0x21dfd8 = this.#offset; _0x21dfd8 < _0x9affae + this.#offset; ++_0x21dfd8) {
            const _0x1d8d88 = this.#dataView['getUint8'](_0x21dfd8);
            _0x3662e6 += String['fromCharCode'](_0x1d8d88);
        }
        return this.#offset += _0x9affae, _0x3662e6;
    }
};
export default logi['maps']['DataView'];