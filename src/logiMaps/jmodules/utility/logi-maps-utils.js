import a6_0xc22818 from '../common/logi-maps-defines.js?v=2.1.10.1';
import a6_0x214d6b from '../common/logi-maps-types.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a6_0xc22818, logi['maps']['ALIGN'] = a6_0x214d6b['ALIGN'], logi['maps']['Utils'] = class {
    static #androidEnv = null;
    static #iOSEnv = null;
    static ['FrameRate'] = {
        'frameCount': undefined,
        'startTime': undefined,
        'reset'() {
            this['frameCount'] = undefined, this['startTime'] = undefined;
        },
        'measure'(elapsed = 0x3e8) {
            if (this['frameCount'] == undefined)
                this['frameCount'] = 0x0, this['startTime'] = Date['now']();
            else {
                this['frameCount'] += 0x1;
                const currentTime = Date['now'](), elapsedTime = currentTime - this['startTime'], fps = this['frameCount'] / (elapsedTime / 0x3e8);
                elapsedTime >= elapsed && (console['log']('FPS:\x20' + Math['round'](fps)), this['frameCount'] = 0x0, this['startTime'] = Date['now']());
            }
        }
    };
    static ['getPowTableValue'](_0x120686) {
        const _0xac8b11 = Math['floor'](_0x120686), _0x3afffe = _0x120686 - _0xac8b11, _0x3dd616 = logi['maps']['Defines']['POW_TABLE'][_0xac8b11];
        return _0x3dd616 + _0x3dd616 * _0x3afffe;
    }
    static ['isAndroid']() {
        return this.#androidEnv == null && (this.#androidEnv = navigator['userAgent']['match'](/Android/i) == null ? ![] : !![]), this.#androidEnv;
    }
    static ['isIOS']() {
        return this.#iOSEnv == null && (this.#iOSEnv = navigator['userAgent']['match'](/iPhone|iPad|iPod/i) == null ? ![] : !![]), this.#iOSEnv;
    }
    static ['isWebGlAvailable']() {
        try {
            const _0x27d7eb = document['createElement']('canvas'), _0x1e892c = _0x27d7eb['getContext']('webgl', {
                    'antialias': !![],
                    'preserveDrawingBuffer': !![]
                });
            return _0x1e892c !== null;
        } catch (_0x35a207) {
            return ![];
        }
    }
    static ['getCurTick']() {
        return new Date()['getTime']();
    }
    static #fpsFrameCount = undefined;
    static #fpsStartTime = undefined;
    static ['measureFPS'](_0x3a7c3b) {
        switch (_0x3a7c3b) {
        case 'reset': {
                this.#fpsFrameCount = undefined, this.#fpsStartTime = undefined;
            }
            break;
        default: {
            }
            break;
        }
    }
    static ['longitude2TileX'](_0x1b54a5, _0x271d78) {
        const _0x496947 = logi['maps']['Utils']['getPowTableValue'](_0x271d78);
        while (_0x1b54a5 < -0xb4) {
            _0x1b54a5 += 0x168;
        }
        while (_0x1b54a5 > 0xb4) {
            _0x1b54a5 -= 0x168;
        }
        return (_0x1b54a5 + 0xb4) / 0x168 * _0x496947;
    }
    static ['latitude2TileY'](_0x2d8abb, _0x1bb0cf) {
        const _0x965825 = logi['maps']['Utils']['getPowTableValue'](_0x1bb0cf);
        return (0x1 - Math['log'](Math['tan'](_0x2d8abb * logi['maps']['Defines']['DIV_PI_180']) + 0x1 / Math['cos'](_0x2d8abb * logi['maps']['Defines']['DIV_PI_180'])) / Math['PI']) / 0x2 * _0x965825;
    }
    static ['tileX2Longitude'](_0x3fd5e7, _0x10b366) {
        const _0x34d3c4 = logi['maps']['Utils']['getPowTableValue'](_0x10b366);
        while (_0x3fd5e7 < 0x0) {
            _0x3fd5e7 += _0x34d3c4;
        }
        while (_0x3fd5e7 > _0x34d3c4) {
            _0x3fd5e7 -= _0x34d3c4;
        }
        return _0x3fd5e7 / _0x34d3c4 * 0x168 - 0xb4;
    }
    static ['tileY2Latitude'](_0x398af6, _0x56a2fa) {
        const _0x346716 = logi['maps']['Utils']['getPowTableValue'](_0x56a2fa);
        return Math['atan'](Math['sinh'](Math['PI'] * (0x1 - 0x2 * _0x398af6 / _0x346716))) * logi['maps']['Defines']['DIV_180_PI'];
    }
    static ['degToRad'](_0x43a7a9) {
        return _0x43a7a9 * Math['PI'] / 0xb4;
    }
    static ['rectOnRect'](_0x5f53fb, _0x45c7c2, _0x5980d3, _0x24736d, _0x149756, _0x5a9e61, _0x4bed9b, _0x4fd6b2) {
        return _0x5980d3 >= _0x149756 && _0x5f53fb <= _0x4bed9b && _0x24736d >= _0x5a9e61 && _0x45c7c2 <= _0x4fd6b2;
    }
    static ['pointInRect'](_0x244fa3, _0x4a31d1, _0x53a827, _0x1031da, _0x2809c1, _0x1010a4) {
        return _0x244fa3 >= _0x53a827 && _0x4a31d1 >= _0x1031da && _0x244fa3 <= _0x2809c1 && _0x4a31d1 <= _0x1010a4;
    }
    static ['rectOnMapRect'](_0xa99c8a, _0x3b3fd5) {
        return _0x3b3fd5['west'] > _0x3b3fd5['east'] ? _0xa99c8a['xMax'] >= _0x3b3fd5['west'] && _0xa99c8a['xMin'] <= 0xb4 && _0xa99c8a['yMax'] >= _0x3b3fd5['south'] && _0xa99c8a['yMin'] <= _0x3b3fd5['north'] || _0xa99c8a['xMax'] >= -0xb4 && _0xa99c8a['xMin'] <= _0x3b3fd5['east'] && _0xa99c8a['yMax'] >= _0x3b3fd5['south'] && _0xa99c8a['yMin'] <= _0x3b3fd5['north'] : _0xa99c8a['xMax'] >= _0x3b3fd5['west'] && _0xa99c8a['xMin'] <= _0x3b3fd5['east'] && _0xa99c8a['yMax'] >= _0x3b3fd5['south'] && _0xa99c8a['yMin'] <= _0x3b3fd5['north'];
    }
    static ['pointInMapRect'](_0x26537d, _0x4a9976) {
        return _0x4a9976['west'] > _0x4a9976['east'] ? _0x26537d['lng'] >= _0x4a9976['west'] && _0x26537d['lat'] >= _0x4a9976['south'] && _0x26537d['lng'] <= 0xb4 && _0x26537d['lat'] <= _0x4a9976['north'] || _0x26537d['lng'] >= -0xb4 && _0x26537d['lat'] >= _0x4a9976['south'] && _0x26537d['lng'] <= _0x4a9976['east'] && _0x26537d['lat'] <= _0x4a9976['north'] : _0x26537d['lng'] >= _0x4a9976['west'] && _0x26537d['lat'] >= _0x4a9976['south'] && _0x26537d['lng'] <= _0x4a9976['east'] && _0x26537d['lat'] <= _0x4a9976['north'];
    }
    static ['generalizeDegree'](_0x527347, _0x435eb7 = 0x168) {
        const _0x17723c = 0x80;
        for (let _0x4298f7 = 0x0; _0x4298f7 < _0x17723c && _0x527347 < 0x0; ++_0x4298f7) {
            _0x527347 += _0x435eb7;
        }
        for (let _0x12a266 = 0x0; _0x12a266 < _0x17723c && _0x527347 > _0x435eb7; ++_0x12a266) {
            _0x527347 -= _0x435eb7;
        }
        return _0x527347;
    }
    static ['getDegreeBetweenVectors'](_0x5bd37b, _0x3084dd) {
        const _0x6ef176 = _0x5bd37b['x'] * _0x3084dd['x'] + _0x5bd37b['y'] * _0x3084dd['y'], _0x163fea = _0x5bd37b['x'] * _0x3084dd['y'] - _0x5bd37b['y'] * _0x3084dd['x'];
        let _0x305ae2 = Math['acos'](_0x6ef176);
        return _0x163fea < 0x0 && (_0x305ae2 = Math['PI'] * 0x2 - _0x305ae2), logi['maps']['Utils']['generalizeDegree'](_0x305ae2 * (0xb4 / Math['PI']));
    }
    static ['getVectorNormalize'](_0x29368e) {
        const _0x3228fb = logi['maps']['Utils']['getVectorLength'](_0x29368e);
        if (_0x3228fb == 0x0)
            return {
                'x': _0x29368e['x'],
                'y': _0x29368e['y']
            };
        return {
            'x': _0x29368e['x'] / _0x3228fb,
            'y': _0x29368e['y'] / _0x3228fb
        };
    }
    static ['getVectorLength'](_0x180af6) {
        return Math['sqrt'](_0x180af6['x'] * _0x180af6['x'] + _0x180af6['y'] * _0x180af6['y']);
    }
    static ['getAlignPosition'](_0x4a9194, _0x150eae, _0x4e7d2b, _0x46def6, _0x1074b7) {
        let _0x55b98a = {
            'x': 0x0,
            'y': 0x0
        };
        switch (_0x4e7d2b) {
        case logi['maps']['ALIGN']['LT']:
        case logi['maps']['ALIGN']['LM']:
        case logi['maps']['ALIGN']['LB']:
            _0x55b98a['x'] = _0x4a9194;
            break;
        case logi['maps']['ALIGN']['CT']:
        case logi['maps']['ALIGN']['CM']:
        case logi['maps']['ALIGN']['CB']:
            _0x55b98a['x'] = _0x4a9194 - _0x46def6 * 0.5;
            break;
        case logi['maps']['ALIGN']['RT']:
        case logi['maps']['ALIGN']['RM']:
        case logi['maps']['ALIGN']['RB']:
            _0x55b98a['x'] = _0x4a9194 - _0x46def6 * 0x1;
            break;
        }
        switch (_0x4e7d2b) {
        case logi['maps']['ALIGN']['LT']:
        case logi['maps']['ALIGN']['CT']:
        case logi['maps']['ALIGN']['RT']:
            _0x55b98a['y'] = _0x150eae;
            break;
        case logi['maps']['ALIGN']['LM']:
        case logi['maps']['ALIGN']['CM']:
        case logi['maps']['ALIGN']['RM']:
            _0x55b98a['y'] = _0x150eae - _0x1074b7 * 0.5;
            break;
        case logi['maps']['ALIGN']['LB']:
        case logi['maps']['ALIGN']['CB']:
        case logi['maps']['ALIGN']['RB']:
            _0x55b98a['y'] = _0x150eae - _0x1074b7 * 0x1;
            break;
        }
        return _0x55b98a;
    }
    static ['getPivotPoint'](_0x5836b2, _0x1951c9, _0x39c822) {
        let _0x5745df = {
            'x': 0x0,
            'y': 0x0
        };
        switch (_0x5836b2) {
        case logi['maps']['ALIGN']['LT']:
        case logi['maps']['ALIGN']['LM']:
        case logi['maps']['ALIGN']['LB']:
            _0x5745df['x'] = 0x0;
            break;
        case logi['maps']['ALIGN']['CT']:
        case logi['maps']['ALIGN']['CM']:
        case logi['maps']['ALIGN']['CB']:
            _0x5745df['x'] = _0x1951c9 * 0.5;
            break;
        case logi['maps']['ALIGN']['RT']:
        case logi['maps']['ALIGN']['RM']:
        case logi['maps']['ALIGN']['RB']:
            _0x5745df['x'] = _0x1951c9 * 0x1;
            break;
        }
        switch (_0x5836b2) {
        case logi['maps']['ALIGN']['LT']:
        case logi['maps']['ALIGN']['CT']:
        case logi['maps']['ALIGN']['RT']:
            _0x5745df['y'] = 0x0;
            break;
        case logi['maps']['ALIGN']['LM']:
        case logi['maps']['ALIGN']['CM']:
        case logi['maps']['ALIGN']['RM']:
            _0x5745df['y'] = _0x39c822 * 0.5;
            break;
        case logi['maps']['ALIGN']['LB']:
        case logi['maps']['ALIGN']['CB']:
        case logi['maps']['ALIGN']['RB']:
            _0x5745df['y'] = _0x39c822 * 0x1;
            break;
        }
        return _0x5745df;
    }
    static ['convertToTextAlign'](_0x209cfe) {
        switch (_0x209cfe) {
        case logi['maps']['ALIGN']['LT']:
        case logi['maps']['ALIGN']['LM']:
        case logi['maps']['ALIGN']['LB']:
            return 'left';
        case logi['maps']['ALIGN']['CT']:
        case logi['maps']['ALIGN']['CM']:
        case logi['maps']['ALIGN']['CB']:
            return 'center';
        case logi['maps']['ALIGN']['RT']:
        case logi['maps']['ALIGN']['RM']:
        case logi['maps']['ALIGN']['RB']:
            return 'right';
        default:
            return 'left';
        }
    }
    static ['convertToTextBaseline'](_0xa0801a) {
        switch (_0xa0801a) {
        case logi['maps']['ALIGN']['LT']:
        case logi['maps']['ALIGN']['CT']:
        case logi['maps']['ALIGN']['RT']:
            return 'top';
        case logi['maps']['ALIGN']['LM']:
        case logi['maps']['ALIGN']['CM']:
        case logi['maps']['ALIGN']['RM']:
            return 'middle';
        case logi['maps']['ALIGN']['LB']:
        case logi['maps']['ALIGN']['CB']:
        case logi['maps']['ALIGN']['RB']:
            return 'bottom';
        default:
            return 'top';
        }
    }
    static ['convertToTextAnchorX'](_0x1aa52a) {
        switch (_0x1aa52a) {
        case logi['maps']['ALIGN']['LT']:
        case logi['maps']['ALIGN']['LM']:
        case logi['maps']['ALIGN']['LB']:
            return 0x0;
        case logi['maps']['ALIGN']['CT']:
        case logi['maps']['ALIGN']['CM']:
        case logi['maps']['ALIGN']['CB']:
            return 0.5;
        case logi['maps']['ALIGN']['RT']:
        case logi['maps']['ALIGN']['RM']:
        case logi['maps']['ALIGN']['RB']:
            return 0x1;
        default:
            return 0x0;
        }
    }
    static ['convertToTextAnchorY'](_0x473f1b) {
        switch (_0x473f1b) {
        case logi['maps']['ALIGN']['LT']:
        case logi['maps']['ALIGN']['CT']:
        case logi['maps']['ALIGN']['RT']:
            return 0x0;
        case logi['maps']['ALIGN']['LM']:
        case logi['maps']['ALIGN']['CM']:
        case logi['maps']['ALIGN']['RM']:
            return 0.5;
        case logi['maps']['ALIGN']['LB']:
        case logi['maps']['ALIGN']['CB']:
        case logi['maps']['ALIGN']['RB']:
            return 0x1;
        default:
            return 0x0;
        }
    }
    static ['getFormatFont'](_0x261d95, _0x17c051, _0x4ff502 = ![]) {
        return _0x261d95 != '' && document['fonts'] && !document['fonts']['check']('1em\x20' + _0x261d95) && (_0x261d95 = ''), _0x261d95 = _0x261d95 == '' ? '' + logi['maps']['Defines']['DEFAULT_FONT'] : '' + _0x261d95, _0x4ff502 ? 'bold\x20' + _0x17c051 + 'px\x20' + _0x261d95 : _0x17c051 + 'px\x20' + _0x261d95;
    }
    static ['getBoundaryBox'](_0x3c6a3e, _0x468de7, _0x4b1e21, _0x19fc2b, _0x4acd77, _0x2b8b86) {
        let _0x261288 = [
                {
                    'x': 0x0,
                    'y': 0x0
                },
                {
                    'x': 0x0,
                    'y': 0x0
                },
                {
                    'x': 0x0,
                    'y': 0x0
                },
                {
                    'x': 0x0,
                    'y': 0x0
                }
            ], _0x3d35b7 = logi['maps']['Utils']['getAlignPosition'](_0x3c6a3e['x'], _0x3c6a3e['y'], _0x19fc2b, _0x468de7, _0x4b1e21), _0x446f6a = _0x2b8b86?.['left'] ?? 0x0, _0x1e0bd1 = _0x2b8b86?.['top'] ?? 0x0, _0x61dfd5 = _0x2b8b86?.['right'] ?? 0x0, _0x17e293 = _0x2b8b86?.['bottom'] ?? 0x0;
        _0x446f6a + _0x61dfd5 > _0x468de7 && (_0x446f6a = _0x468de7 * 0.5, _0x61dfd5 = _0x468de7 * 0.5);
        _0x1e0bd1 + _0x17e293 > _0x4b1e21 && (_0x1e0bd1 = _0x4b1e21 * 0.5, _0x17e293 = _0x4b1e21 * 0.5);
        _0x261288[0x0] = {
            'x': _0x3d35b7['x'] + _0x446f6a,
            'y': _0x3d35b7['y'] + _0x1e0bd1
        }, _0x261288[0x1] = {
            'x': _0x3d35b7['x'] + _0x468de7 - _0x61dfd5,
            'y': _0x3d35b7['y'] + _0x1e0bd1
        }, _0x261288[0x2] = {
            'x': _0x3d35b7['x'] + _0x468de7 - _0x61dfd5,
            'y': _0x3d35b7['y'] + _0x4b1e21 - _0x17e293
        }, _0x261288[0x3] = {
            'x': _0x3d35b7['x'] + _0x446f6a,
            'y': _0x3d35b7['y'] + _0x4b1e21 - _0x17e293
        };
        if (_0x4acd77 != 0x0) {
            let _0x262c26 = logi['maps']['Utils']['getPivotPoint'](_0x19fc2b, _0x468de7, _0x4b1e21);
            _0x262c26['x'] += _0x3d35b7['x'], _0x262c26['y'] += _0x3d35b7['y'];
            const _0x3d5339 = logi['maps']['Utils']['degToRad'](_0x4acd77);
            for (let _0x16fe30 = 0x0; _0x16fe30 < _0x261288['length']; ++_0x16fe30) {
                const _0x178eab = _0x261288[_0x16fe30]['x'], _0x40799d = _0x261288[_0x16fe30]['y'];
                _0x261288[_0x16fe30]['x'] = _0x262c26['x'] + (_0x178eab - _0x262c26['x']) * Math['cos'](-_0x3d5339) + (_0x40799d - _0x262c26['y']) * Math['sin'](-_0x3d5339), _0x261288[_0x16fe30]['y'] = _0x262c26['y'] - (_0x178eab - _0x262c26['x']) * Math['sin'](-_0x3d5339) + (_0x40799d - _0x262c26['y']) * Math['cos'](-_0x3d5339);
            }
        }
        return _0x261288;
    }
    static ['getBoundaryCircle'](_0x2e34bb) {
        let _0x55447c = {
                'center': {
                    'x': 0x0,
                    'y': 0x0
                },
                'maxR': 0x0
            }, _0x2a88ca = {
                'x': _0x2e34bb[0x0]['x'],
                'y': _0x2e34bb[0x0]['y']
            }, _0x3de674 = {
                'x': _0x2e34bb[0x0]['x'],
                'y': _0x2e34bb[0x0]['y']
            };
        for (let _0x14e874 = 0x1; _0x14e874 < 0x4; ++_0x14e874) {
            _0x2a88ca['x'] = _0x2a88ca['x'] < _0x2e34bb[_0x14e874]['x'] ? _0x2a88ca['x'] : _0x2e34bb[_0x14e874]['x'], _0x2a88ca['y'] = _0x2a88ca['y'] < _0x2e34bb[_0x14e874]['y'] ? _0x2a88ca['y'] : _0x2e34bb[_0x14e874]['y'], _0x3de674['x'] = _0x3de674['x'] > _0x2e34bb[_0x14e874]['x'] ? _0x3de674['x'] : _0x2e34bb[_0x14e874]['x'], _0x3de674['y'] = _0x3de674['y'] > _0x2e34bb[_0x14e874]['y'] ? _0x3de674['y'] : _0x2e34bb[_0x14e874]['y'];
        }
        const _0x49957a = {
            'w': (_0x3de674['x'] - _0x2a88ca['x']) * 0.5,
            'h': (_0x3de674['y'] - _0x2a88ca['y']) * 0.5
        };
        return _0x55447c['center']['x'] = _0x2a88ca['x'] + _0x49957a['w'], _0x55447c['center']['y'] = _0x2a88ca['y'] + _0x49957a['h'], _0x55447c['maxR'] = Math['sqrt'](_0x49957a['w'] * _0x49957a['w'] + _0x49957a['h'] * _0x49957a['h']), _0x55447c;
    }
    static ['getRoughLatLngs'](_0xfaae95, _0x79e9ef = 0x4) {
        let _0x38895c = new Array();
        if (_0xfaae95['length'] > 0x0) {
            let _0x33417c = {
                    'x': 0x0,
                    'y': 0x0
                }, _0x59a9db = {
                    'x': 0x0,
                    'y': 0x0
                }, _0x2c6929 = {
                    'x': 0x0,
                    'y': 0x0
                };
            _0x59a9db['x'] = this['longitude2TileX'](_0xfaae95[0x0]['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W'], _0x59a9db['y'] = this['latitude2TileY'](_0xfaae95[0x0]['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H'], _0x38895c['push']({
                'lng': _0xfaae95[0x0]['lng'],
                'lat': _0xfaae95[0x0]['lat']
            });
            for (let _0x56c855 of _0xfaae95) {
                _0x33417c['x'] = this['longitude2TileX'](_0x56c855['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W'], _0x33417c['y'] = this['latitude2TileY'](_0x56c855['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H'];
                const _0x4e711e = Math['sqrt'](Math['pow'](_0x59a9db['x'] - _0x33417c['x'], 0x2) + Math['pow'](_0x59a9db['y'] - _0x33417c['y'], 0x2));
                _0x4e711e >= _0x79e9ef && (_0x38895c['length'] >= 0x2 ? this['isStraight'](_0x2c6929['x'], _0x2c6929['y'], _0x59a9db['x'], _0x59a9db['y'], _0x33417c['x'], _0x33417c['y']) == !![] ? _0x38895c[_0x38895c['length'] - 0x1] = {
                    'lng': _0x56c855['lng'],
                    'lat': _0x56c855['lat']
                } : _0x38895c['push']({
                    'lng': _0x56c855['lng'],
                    'lat': _0x56c855['lat']
                }) : _0x38895c['push']({
                    'lng': _0x56c855['lng'],
                    'lat': _0x56c855['lat']
                }), _0x2c6929['x'] = _0x59a9db['x'], _0x2c6929['y'] = _0x59a9db['y'], _0x59a9db['x'] = _0x33417c['x'], _0x59a9db['y'] = _0x33417c['y']);
            }
        }
        return _0x38895c;
    }
    static ['addRoughLatLngs'](_0x3fe044, _0x388228, _0x5d3424 = 0x4) {
        let _0x25e37d = ![];
        if (_0x3fe044['length'] == 0x0)
            _0x3fe044['push']({
                'lng': _0x388228['lng'],
                'lat': _0x388228['lat']
            }), _0x25e37d = !![];
        else {
            let _0x5ec764 = {
                    'x': 0x0,
                    'y': 0x0
                }, _0x3c165d = {
                    'x': 0x0,
                    'y': 0x0
                }, _0x196a7d = {
                    'x': 0x0,
                    'y': 0x0
                };
            _0x5ec764['x'] = this['longitude2TileX'](_0x388228['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W'], _0x5ec764['y'] = this['latitude2TileY'](_0x388228['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H'], _0x3c165d['x'] = this['longitude2TileX'](_0x3fe044[_0x3fe044['length'] - 0x1]['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W'], _0x3c165d['y'] = this['latitude2TileY'](_0x3fe044[_0x3fe044['length'] - 0x1]['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H'];
            const _0x218843 = Math['sqrt'](Math['pow'](_0x3c165d['x'] - _0x5ec764['x'], 0x2) + Math['pow'](_0x3c165d['y'] - _0x5ec764['y'], 0x2));
            _0x218843 >= _0x5d3424 && (_0x3fe044['length'] >= 0x2 ? (_0x196a7d['x'] = this['longitude2TileX'](_0x3fe044[_0x3fe044['length'] - 0x2]['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W'], _0x196a7d['y'] = this['latitude2TileY'](_0x3fe044[_0x3fe044['length'] - 0x2]['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H'], this['isStraight'](_0x196a7d['x'], _0x196a7d['y'], _0x3c165d['x'], _0x3c165d['y'], _0x5ec764['x'], _0x5ec764['y']) == !![] ? _0x3fe044[_0x3fe044['length'] - 0x1] = {
                'lng': _0x388228['lng'],
                'lat': _0x388228['lat']
            } : _0x3fe044['push']({
                'lng': _0x388228['lng'],
                'lat': _0x388228['lat']
            })) : _0x3fe044['push']({
                'lng': _0x388228['lng'],
                'lat': _0x388228['lat']
            }), _0x25e37d = !![]);
        }
        return _0x25e37d;
    }
    static ['isStraight'](_0x51e17e, _0x20c055, _0x1c957d, _0x363fbb, _0x54bbe6, _0x131a89, _0x5031b5 = 0x2) {
        _0x51e17e = Math['round'](_0x51e17e / _0x5031b5), _0x20c055 = Math['round'](_0x20c055 / _0x5031b5), _0x1c957d = Math['round'](_0x1c957d / _0x5031b5), _0x363fbb = Math['round'](_0x363fbb / _0x5031b5), _0x54bbe6 = Math['round'](_0x54bbe6 / _0x5031b5), _0x131a89 = Math['round'](_0x131a89 / _0x5031b5);
        const _0x3bc7de = (_0x363fbb - _0x20c055) * (_0x54bbe6 - _0x51e17e) + _0x20c055 * (_0x1c957d - _0x51e17e), _0x50aada = (_0x1c957d - _0x51e17e) * _0x131a89;
        return _0x3bc7de == _0x50aada;
    }
    static ['getTransformedPoint'](_0x419554, _0x151490, _0x2004b9) {
        return {
            'x': _0x419554 * _0x2004b9['a'] + _0x151490 * _0x2004b9['c'] + _0x2004b9['e'],
            'y': _0x419554 * _0x2004b9['b'] + _0x151490 * _0x2004b9['d'] + _0x2004b9['f']
        };
    }
    static ['convexHull'](_0xe39f6c) {
        function _0x4feb93(_0x237ad0, _0xa9a3f0) {
            return _0x237ad0['lat'] === _0xa9a3f0['lat'] ? _0x237ad0['lng'] - _0xa9a3f0['lng'] : _0x237ad0['lat'] - _0xa9a3f0['lat'];
        }
        function _0x3538d0(_0x330f96, _0x5ca4ab, _0x248f26) {
            return (_0x5ca4ab['lng'] - _0x330f96['lng']) * (_0x248f26['lat'] - _0x330f96['lat']) - (_0x5ca4ab['lat'] - _0x330f96['lat']) * (_0x248f26['lng'] - _0x330f96['lng']);
        }
        _0xe39f6c['sort'](_0x4feb93);
        const _0x942481 = [], _0x4a1e5f = [];
        for (const _0x162fed of _0xe39f6c) {
            while (_0x942481['length'] >= 0x2 && _0x3538d0(_0x942481[_0x942481['length'] - 0x2], _0x942481[_0x942481['length'] - 0x1], _0x162fed) <= 0x0) {
                _0x942481['pop']();
            }
            _0x942481['push'](_0x162fed);
        }
        for (const _0x5b8bc4 of _0xe39f6c['slice']()['reverse']()) {
            while (_0x4a1e5f['length'] >= 0x2 && _0x3538d0(_0x4a1e5f[_0x4a1e5f['length'] - 0x2], _0x4a1e5f[_0x4a1e5f['length'] - 0x1], _0x5b8bc4) <= 0x0) {
                _0x4a1e5f['pop']();
            }
            _0x4a1e5f['push'](_0x5b8bc4);
        }
        return _0x4a1e5f['pop'](), _0x942481['pop'](), _0x942481['concat'](_0x4a1e5f);
    }
    static ['getEllipseLatLng'](_0x502b84, _0xf3c2e0, _0x36ee29) {
        const _0x27f044 = 0x28;
        let _0x3b9e55 = 0x0;
        const _0x30618c = new Array();
        _0x30618c['push']({
            'lng': _0x502b84 + _0x36ee29,
            'lat': _0xf3c2e0
        });
        for (let _0xd2359b = 0x1; _0xd2359b <= _0x27f044; ++_0xd2359b) {
            _0x3b9e55 = Math['PI'] * 0x2 * _0xd2359b / _0x27f044, _0x30618c['push']({
                'lng': _0x502b84 + _0x36ee29 * Math['cos'](_0x3b9e55),
                'lat': _0xf3c2e0 + _0x36ee29 * Math['sin'](_0x3b9e55)
            });
        }
        return _0x30618c;
    }
    static ['getSplitInfoOnPolyline'](_0x41aff7, _0x498685) {
        if (_0x41aff7['length'] < 0x2 || _0x498685 < 0x0 || _0x498685 > 0x1)
            return null;
        let _0x119b24 = 0x0;
        const _0x5c967d = new Array();
        _0x5c967d['push'](0x0);
        const _0x5b0983 = {
            'x': 0x0,
            'y': 0x0
        };
        for (let _0x15658a = 0x1; _0x15658a < _0x41aff7['length']; ++_0x15658a) {
            _0x5b0983['x'] = _0x41aff7[_0x15658a]['lng'] - _0x41aff7[_0x15658a - 0x1]['lng'], _0x5b0983['y'] = _0x41aff7[_0x15658a]['lat'] - _0x41aff7[_0x15658a - 0x1]['lat'], _0x119b24 += Math['sqrt'](_0x5b0983['x'] * _0x5b0983['x'] + _0x5b0983['y'] * _0x5b0983['y']), _0x5c967d['push'](_0x119b24);
        }
        if (_0x119b24 == 0x0)
            return null;
        const _0xe9c425 = _0x119b24 * _0x498685;
        let _0x39df69 = _0x5c967d['length'] - 0x1, _0x3e6df9 = 0x1;
        for (let _0x1be8b8 = 0x1; _0x1be8b8 < _0x5c967d['length']; ++_0x1be8b8) {
            if (_0xe9c425 <= _0x5c967d[_0x1be8b8]) {
                _0x39df69 = _0x1be8b8, _0x3e6df9 = (_0xe9c425 - _0x5c967d[_0x1be8b8 - 0x1]) / (_0x5c967d[_0x1be8b8] - _0x5c967d[_0x1be8b8 - 0x1]);
                break;
            }
        }
        const _0x1abd8e = _0x41aff7[_0x39df69 - 0x1]['lng'] + (_0x41aff7[_0x39df69]['lng'] - _0x41aff7[_0x39df69 - 0x1]['lng']) * _0x3e6df9, _0x1807f4 = _0x41aff7[_0x39df69 - 0x1]['lat'] + (_0x41aff7[_0x39df69]['lat'] - _0x41aff7[_0x39df69 - 0x1]['lat']) * _0x3e6df9, _0x550229 = Math['atan2'](_0x41aff7[_0x39df69]['lat'] - _0x41aff7[_0x39df69 - 0x1]['lat'], _0x41aff7[_0x39df69]['lng'] - _0x41aff7[_0x39df69 - 0x1]['lng']);
        let _0x8fa8f4 = _0x550229 * (0xb4 / Math['PI']);
        _0x8fa8f4 -= 0x10e;
        while (_0x8fa8f4 < -0xb4) {
            _0x8fa8f4 += 0x168;
        }
        while (_0x8fa8f4 > 0xb4) {
            _0x8fa8f4 -= 0x168;
        }
        return {
            'lat': _0x1807f4,
            'lng': _0x1abd8e,
            'deg': _0x8fa8f4
        };
    }
    static ['getPerpendicularLatLng'](_0x22ea1a, _0x30a914, _0x567397) {
        let _0x2cdcb6 = {
            'lng': 0x0,
            'lat': 0x0,
            'distance': 0x0,
            'result': 0x0
        };
        const _0x415444 = _0x22ea1a['lng'] > _0x30a914['lng'] ? _0x30a914['lng'] : _0x22ea1a['lng'], _0x1be9d1 = _0x22ea1a['lat'] > _0x30a914['lat'] ? _0x30a914['lat'] : _0x22ea1a['lat'], _0x447237 = _0x22ea1a['lng'] > _0x30a914['lng'] ? _0x22ea1a['lng'] : _0x30a914['lng'], _0x15080b = _0x22ea1a['lat'] > _0x30a914['lat'] ? _0x22ea1a['lat'] : _0x30a914['lat'];
        if (_0x22ea1a['lng'] === _0x30a914['lng'] && _0x22ea1a['lat'] === _0x30a914['lat'])
            _0x2cdcb6['lng'] = _0x22ea1a['lng'], _0x2cdcb6['lat'] = _0x22ea1a['lat'], _0x2cdcb6['distance'] = this.#getLatLngDistance(_0x22ea1a, _0x567397), _0x2cdcb6['result'] = 0x1;
        else {
            if (_0x22ea1a['lng'] === _0x30a914['lng'])
                _0x567397['lat'] >= _0x1be9d1 && _0x567397['lat'] <= _0x15080b ? (_0x2cdcb6['lng'] = _0x22ea1a['lng'], _0x2cdcb6['lat'] = _0x567397['lat'], _0x2cdcb6['distance'] = Math['abs'](_0x567397['lng'] - _0x22ea1a['lng']), _0x2cdcb6['result'] = 0x1) : (_0x2cdcb6['lng'] = _0x22ea1a['lng'], Math['abs'](_0x22ea1a['lat'] - _0x567397['lat']) < Math['abs'](_0x30a914['lat'] - _0x567397['lat']) ? _0x2cdcb6['lat'] = _0x22ea1a['lat'] : _0x2cdcb6['lat'] = _0x30a914['lat'], _0x2cdcb6['distance'] = this.#getLatLngDistance(_0x567397, _0x2cdcb6), _0x2cdcb6['result'] = 0x2);
            else {
                if (_0x22ea1a['lat'] === _0x30a914['lat'])
                    _0x567397['lng'] >= _0x415444 && _0x567397['lng'] <= _0x447237 ? (_0x2cdcb6['lng'] = _0x567397['lng'], _0x2cdcb6['lat'] = _0x22ea1a['lat'], _0x2cdcb6['distance'] = Math['abs'](_0x567397['lat'] - _0x22ea1a['lat']), _0x2cdcb6['result'] = 0x1) : (_0x2cdcb6['lat'] = _0x22ea1a['lat'], Math['abs'](_0x22ea1a['lng'] - _0x567397['lng']) < Math['abs'](_0x30a914['lng'] - _0x567397['lng']) ? _0x2cdcb6['lng'] = _0x22ea1a['lng'] : _0x2cdcb6['lng'] = _0x30a914['lng'], _0x2cdcb6['distance'] = this.#getLatLngDistance(_0x567397, _0x2cdcb6), _0x2cdcb6['result'] = 0x2);
                else {
                    let _0x30c33a = (_0x30a914['lat'] - _0x22ea1a['lat']) / (_0x30a914['lng'] - _0x22ea1a['lng']), _0xad34f3 = -0x1 / _0x30c33a, _0x10c935 = (_0x567397['lat'] - _0xad34f3 * _0x567397['lng'] - _0x22ea1a['lat'] + _0x30c33a * _0x22ea1a['lng']) / (_0x30c33a - _0xad34f3), _0x494039 = _0x30c33a * (_0x10c935 - _0x22ea1a['lng']) + _0x22ea1a['lat'];
                    _0x2cdcb6['lng'] = _0x10c935, _0x2cdcb6['lat'] = _0x494039, _0x2cdcb6['lng'] >= _0x415444 && _0x2cdcb6['lng'] <= _0x447237 && _0x2cdcb6['lat'] >= _0x1be9d1 && _0x2cdcb6['lat'] <= _0x15080b ? (_0x2cdcb6['distance'] = this.#getLatLngDistance(_0x567397, _0x2cdcb6), _0x2cdcb6['result'] = 0x1) : (this.#getLatLngDistance(_0x22ea1a, _0x2cdcb6) < this.#getLatLngDistance(_0x30a914, _0x2cdcb6) ? (_0x2cdcb6['lng'] = _0x22ea1a['lng'], _0x2cdcb6['lat'] = _0x22ea1a['lat']) : (_0x2cdcb6['lng'] = _0x30a914['lng'], _0x2cdcb6['lat'] = _0x30a914['lat']), _0x2cdcb6['distance'] = this.#getLatLngDistance(_0x567397, _0x2cdcb6), _0x2cdcb6['result'] = 0x2);
                }
            }
        }
        return _0x2cdcb6;
    }
    static #getLatLngDistance(_0x7e5159, _0x842340) {
        let _0x5a2e53, _0x42b6bc;
        return _0x5a2e53 = _0x842340['lng'] - _0x7e5159['lng'], _0x42b6bc = _0x842340['lat'] - _0x7e5159['lat'], Math['sqrt'](_0x5a2e53 * _0x5a2e53 + _0x42b6bc * _0x42b6bc);
    }
    static ['getPerpendicularPoint'](_0x55c2de, _0x19298b, _0xa34091) {
        let _0x2a53d6 = {
            'x': 0x0,
            'y': 0x0,
            'distance': 0x0,
            'result': 0x0
        };
        const _0x2e9d68 = _0x55c2de['x'] > _0x19298b['x'] ? _0x19298b['x'] : _0x55c2de['x'], _0x1a999a = _0x55c2de['y'] > _0x19298b['y'] ? _0x19298b['y'] : _0x55c2de['y'], _0x1c24fe = _0x55c2de['x'] > _0x19298b['x'] ? _0x55c2de['x'] : _0x19298b['x'], _0x541056 = _0x55c2de['y'] > _0x19298b['y'] ? _0x55c2de['y'] : _0x19298b['y'];
        if (_0x55c2de['x'] === _0x19298b['x'] && _0x55c2de['y'] === _0x19298b['y'])
            _0x2a53d6['x'] = _0x55c2de['x'], _0x2a53d6['y'] = _0x55c2de['y'], _0x2a53d6['distance'] = this.#getPointDistance(_0x55c2de, _0xa34091), _0x2a53d6['result'] = 0x1;
        else {
            if (_0x55c2de['x'] === _0x19298b['x'])
                _0xa34091['y'] >= _0x1a999a && _0xa34091['y'] <= _0x541056 ? (_0x2a53d6['x'] = _0x55c2de['x'], _0x2a53d6['y'] = _0xa34091['y'], _0x2a53d6['distance'] = Math['abs'](_0xa34091['x'] - _0x55c2de['x']), _0x2a53d6['result'] = 0x1) : (_0x2a53d6['x'] = _0x55c2de['x'], Math['abs'](_0x55c2de['y'] - _0xa34091['y']) < Math['abs'](_0x19298b['y'] - _0xa34091['y']) ? _0x2a53d6['y'] = _0x55c2de['y'] : _0x2a53d6['y'] = _0x19298b['y'], _0x2a53d6['distance'] = this.#getPointDistance(_0xa34091, _0x2a53d6), _0x2a53d6['result'] = 0x2);
            else {
                if (_0x55c2de['y'] === _0x19298b['y'])
                    _0xa34091['x'] >= _0x2e9d68 && _0xa34091['x'] <= _0x1c24fe ? (_0x2a53d6['x'] = _0xa34091['x'], _0x2a53d6['y'] = _0x55c2de['y'], _0x2a53d6['distance'] = Math['abs'](_0xa34091['y'] - _0x55c2de['y']), _0x2a53d6['result'] = 0x1) : (_0x2a53d6['y'] = _0x55c2de['y'], Math['abs'](_0x55c2de['x'] - _0xa34091['x']) < Math['abs'](_0x19298b['x'] - _0xa34091['x']) ? _0x2a53d6['x'] = _0x55c2de['x'] : _0x2a53d6['x'] = _0x19298b['x'], _0x2a53d6['distance'] = this.#getPointDistance(_0xa34091, _0x2a53d6), _0x2a53d6['result'] = 0x2);
                else {
                    let _0x4b60fa = (_0x19298b['y'] - _0x55c2de['y']) / (_0x19298b['x'] - _0x55c2de['x']), _0x2569c7 = -0x1 / _0x4b60fa, _0x18f8cb = (_0xa34091['y'] - _0x2569c7 * _0xa34091['x'] - _0x55c2de['y'] + _0x4b60fa * _0x55c2de['x']) / (_0x4b60fa - _0x2569c7), _0x3f9be2 = _0x4b60fa * (_0x18f8cb - _0x55c2de['x']) + _0x55c2de['y'];
                    _0x2a53d6['x'] = _0x18f8cb, _0x2a53d6['y'] = _0x3f9be2, _0x2a53d6['x'] >= _0x2e9d68 && _0x2a53d6['x'] <= _0x1c24fe && _0x2a53d6['y'] >= _0x1a999a && _0x2a53d6['y'] <= _0x541056 ? (_0x2a53d6['distance'] = this.#getPointDistance(_0xa34091, _0x2a53d6), _0x2a53d6['result'] = 0x1) : (this.#getPointDistance(_0x55c2de, _0x2a53d6) < this.#getPointDistance(_0x19298b, _0x2a53d6) ? (_0x2a53d6['x'] = _0x55c2de['x'], _0x2a53d6['y'] = _0x55c2de['y']) : (_0x2a53d6['x'] = _0x19298b['x'], _0x2a53d6['y'] = _0x19298b['y']), _0x2a53d6['distance'] = this.#getPointDistance(_0xa34091, _0x2a53d6), _0x2a53d6['result'] = 0x2);
                }
            }
        }
        return _0x2a53d6;
    }
    static #getPointDistance(_0x3ca28a, _0x390ca6) {
        let _0x1dde2f, _0x40372a;
        return _0x1dde2f = _0x390ca6['x'] - _0x3ca28a['x'], _0x40372a = _0x390ca6['y'] - _0x3ca28a['y'], Math['sqrt'](_0x1dde2f * _0x1dde2f + _0x40372a * _0x40372a);
    }
    static ['findLatLngCentroid'](_0xcf8f26) {
        if (_0xcf8f26['length'] == 0x0)
            return {
                'lng': 0x0,
                'lat': 0x0
            };
        let _0x27b1ad = {
                'lng': _0xcf8f26[0x0]['lng'],
                'lat': _0xcf8f26[0x0]['lat']
            }, _0x97f184 = 0x0, _0xdeb0ff = 0x0, _0x2d37f7 = 0x0, _0x5a1313 = 0x0;
        for (let _0x3d13a9 = 0x0, _0x2675f3 = _0xcf8f26['length'] - 0x1; _0x3d13a9 < _0xcf8f26['length']; _0x2675f3 = _0x3d13a9++) {
            const _0x1aa28f = _0xcf8f26[_0x3d13a9], _0x646467 = _0xcf8f26[_0x2675f3];
            _0x5a1313 = (_0x1aa28f['lng'] - _0x27b1ad['lng']) * (_0x646467['lat'] - _0x27b1ad['lat']) - (_0x646467['lng'] - _0x27b1ad['lng']) * (_0x1aa28f['lat'] - _0x27b1ad['lat']), _0x97f184 += _0x5a1313, _0xdeb0ff += (_0x1aa28f['lng'] + _0x646467['lng'] - 0x2 * _0x27b1ad['lng']) * _0x5a1313, _0x2d37f7 += (_0x1aa28f['lat'] + _0x646467['lat'] - 0x2 * _0x27b1ad['lat']) * _0x5a1313;
        }
        return _0x5a1313 = _0x97f184 * 0x3, _0x27b1ad['lng'] = _0xdeb0ff / _0x5a1313 + _0x27b1ad['lng'], _0x27b1ad['lat'] = _0x2d37f7 / _0x5a1313 + _0x27b1ad['lat'], _0x27b1ad;
    }
    static ['findPointCentroid'](_0x1e6f5f) {
        if (_0x1e6f5f['length'] == 0x0)
            return {
                'x': 0x0,
                'y': 0x0
            };
        let _0x38eca1 = {
                'x': _0x1e6f5f[0x0]['x'],
                'y': _0x1e6f5f[0x0]['y']
            }, _0x434a41 = 0x0, _0x424611 = 0x0, _0x2bdfd0 = 0x0, _0x5f5050 = 0x0;
        for (let _0x376e67 = 0x0, _0x583084 = _0x1e6f5f['length'] - 0x1; _0x376e67 < _0x1e6f5f['length']; _0x583084 = _0x376e67++) {
            const _0x474243 = _0x1e6f5f[_0x376e67], _0x43cfa8 = _0x1e6f5f[_0x583084];
            _0x5f5050 = (_0x474243['x'] - _0x38eca1['x']) * (_0x43cfa8['y'] - _0x38eca1['y']) - (_0x43cfa8['x'] - _0x38eca1['x']) * (_0x474243['y'] - _0x38eca1['y']), _0x434a41 += _0x5f5050, _0x424611 += (_0x474243['x'] + _0x43cfa8['x'] - 0x2 * _0x38eca1['x']) * _0x5f5050, _0x2bdfd0 += (_0x474243['y'] + _0x43cfa8['y'] - 0x2 * _0x38eca1['y']) * _0x5f5050;
        }
        return _0x5f5050 = _0x434a41 * 0x3, _0x38eca1['x'] = _0x424611 / _0x5f5050 + _0x38eca1['x'], _0x38eca1['y'] = _0x2bdfd0 / _0x5f5050 + _0x38eca1['y'], _0x38eca1;
    }
    static ['getTextSize'](_0x291170, _0x137900, _0x5e702b, _0x2667bc, _0x59a6c0 = ![]) {
        _0x291170['font'] = logi['maps']['Utils']['getFormatFont'](_0x5e702b, _0x2667bc, _0x59a6c0);
        const _0x4351c1 = _0x291170['measureText'](_0x137900), _0x304554 = {
                'width': _0x4351c1['width'],
                'height': _0x2667bc * 1.2
            };
        return _0x4351c1['actualBoundingBoxAscent'] != null && _0x4351c1['actualBoundingBoxDescent'] != null && (_0x304554['height'] = _0x4351c1['actualBoundingBoxAscent'] + _0x4351c1['actualBoundingBoxDescent']), _0x304554;
    }
};
export default logi['maps']['Utils'];