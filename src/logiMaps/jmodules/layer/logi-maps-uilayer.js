import a5_0x1c3bc9 from '../common/logi-maps-defines.js?v=2.1.10.1';
import a5_0xb41071 from '../common/logi-maps-types.js?v=2.1.10.1';
import a5_0x2e7a76 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a5_0x2360fb from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a5_0x293bd8 from '../utility/logi-maps-boundarychecker.js?v=2.1.10.1';
import a5_0x3df4d4 from '../object/logi-maps-object.js?v=2.1.10.1';
import a5_0x5d4aa7 from '../object/logi-maps-line.js?v=2.1.10.1';
import a5_0x1eb075 from '../layer/logi-maps-layer.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a5_0x1c3bc9, logi['maps']['ObjectsInTile'] = a5_0xb41071['ObjectsInTile'], logi['maps']['Utils'] = a5_0x2e7a76, logi['maps']['BoundaryData'] = a5_0x2360fb, logi['maps']['BoundaryChecker'] = a5_0x293bd8, logi['maps']['Object'] = a5_0x3df4d4, logi['maps']['Line'] = a5_0x5d4aa7, logi['maps']['Layer'] = a5_0x1eb075, logi['maps']['UiLayer'] = class extends logi['maps']['Layer'] {
    #uiGfx2d = null;
    #centerMark;
    #dragArea;
    #devWaterMarkFlag;
    constructor(_0x4391b8, _0xe17332, _0xfc941c) {
        if (_0x4391b8 == 'parent') {
            const _0x73fbeb = _0xe17332['id'] + '_uilayer';
            var _0x2f74ad = _0xe17332['querySelector']('[id=\x22' + _0x73fbeb + '\x22]');
            if (_0x2f74ad)
                console['log']('Detected\x20existing\x20div.\x20Reusing\x20the\x20div.(' + _0x73fbeb + ')'), super(_0x2f74ad, _0xfc941c);
            else {
                const _0x3f7e70 = document['createElement']('div');
                _0xe17332['appendChild'](_0x3f7e70), _0x3f7e70['id'] = _0x73fbeb, _0x3f7e70['style'] = 'position:absolute;\x20width:100%;\x20height:100%;\x20overflow-y:hidden;\x20overflow-x:hidden;', super(_0x3f7e70, _0xfc941c);
            }
        } else
            super(_0xe17332, _0xfc941c);
        this['addGfxCanvas']('2d'), this.#uiGfx2d = this['getGfx2d'](0x0), this.#centerMark = {
            'length': 0x0,
            'thickness': 0x0,
            'color': 'red'
        }, this.#dragArea = {
            'modeOn': ![],
            'width': 0x1,
            'dashLength': 0x4,
            'dashSpace': 0x8,
            'color': 'black',
            'isReceivingCoords': ![],
            'pt0': {
                'x': 0x0,
                'y': 0x0
            },
            'pt1': {
                'x': 0x0,
                'y': 0x0
            }
        }, this.#devWaterMarkFlag = ![];
    }
    ['setCenterMark'](_0x47509e, _0x2190c8, _0x27ac29) {
        (this.#centerMark['length'] != _0x47509e || this.#centerMark['thickness'] != _0x2190c8 || this.#centerMark['color'] != _0x27ac29) && (this.#centerMark['length'] = _0x47509e ?? 0x0, this.#centerMark['thickness'] = _0x2190c8 ?? 0x0, this.#centerMark['color'] = _0x27ac29 ?? 'red', this['setUpdateFlag']());
    }
    ['setDragAreaMode'](_0x568511) {
        this.#dragArea['modeOn'] != _0x568511 && (this.#dragArea['modeOn'] = _0x568511, this['setUpdateFlag']());
    }
    ['getDragAreaMode']() {
        return this.#dragArea['modeOn'];
    }
    ['setDragAreaStyle'](_0x21c4ce) {
        this.#dragArea['width'] = _0x21c4ce['width'], this.#dragArea['dashLength'] = _0x21c4ce['dashLength'], this.#dragArea['dashSpace'] = _0x21c4ce['dashSpace'], this.#dragArea['color'] = _0x21c4ce['color'], this['setUpdateFlag']();
    }
    ['getDragAreaRect']() {
        const _0x26bfe4 = this['getMapCoord'](), _0x265875 = _0x26bfe4['screen2world'](this.#dragArea['pt0']['x'], this.#dragArea['pt0']['y']), _0x1da290 = _0x26bfe4['screen2world'](this.#dragArea['pt1']['x'], this.#dragArea['pt1']['y']);
        return [
            _0x265875,
            _0x1da290
        ];
    }
    ['setDragAreaInit']() {
        (this.#dragArea['isReceivingCoords'] != ![] || this.#dragArea['pt0']['x'] != 0x0 || this.#dragArea['pt0']['y'] != 0x0 || this.#dragArea['pt1']['x'] != 0x0 || this.#dragArea['pt1']['y'] != 0x0) && (this.#dragArea['isReceivingCoords'] = ![], this.#dragArea['pt0']['x'] = 0x0, this.#dragArea['pt0']['y'] = 0x0, this.#dragArea['pt1']['x'] = 0x0, this.#dragArea['pt1']['y'] = 0x0, this['setUpdateFlag']());
    }
    ['setDragAreaStart'](_0x596176, _0x523358) {
        (this.#dragArea['isReceivingCoords'] != !![] || this.#dragArea['pt0']['x'] != _0x596176 || this.#dragArea['pt0']['y'] != _0x523358 || this.#dragArea['pt1']['x'] != _0x596176 || this.#dragArea['pt1']['y'] != _0x523358) && (this.#dragArea['isReceivingCoords'] = !![], this.#dragArea['pt0']['x'] = _0x596176, this.#dragArea['pt0']['y'] = _0x523358, this.#dragArea['pt1']['x'] = _0x596176, this.#dragArea['pt1']['y'] = _0x523358, this['setUpdateFlag']());
    }
    ['setDragAreaChange'](_0x62e8ef, _0x58feac) {
        this.#dragArea['isReceivingCoords'] == !![] && ((this.#dragArea['pt1']['x'] != _0x62e8ef || this.#dragArea['pt1']['y'] != _0x58feac) && (this.#dragArea['pt1']['x'] = _0x62e8ef, this.#dragArea['pt1']['y'] = _0x58feac, this['setUpdateFlag']()));
    }
    ['setDragAreaEnd']() {
        this.#dragArea['isReceivingCoords'] != ![] && (this.#dragArea['isReceivingCoords'] = ![], this['setUpdateFlag']());
    }
    ['setDevWaterMarkFlag'](_0x4941eb) {
        this.#devWaterMarkFlag != _0x4941eb && (this.#devWaterMarkFlag = _0x4941eb, this['setUpdateFlag']());
    }
    ['triggerEvent']() {
    }
    ['hitObj']() {
        return null;
    }
    ['updateCanvas']() {
        this['setDrawFlag']();
    }
    ['drawCanvas']() {
        this['clearColor'](), this.#drawCrossHair(), this.#drawDragArea();
    }
    #drawCrossHair() {
        if (this.#centerMark['length'] > 0x0 && this.#centerMark['thickness'] > 0x0) {
            const _0x3d61a0 = this['getDevicePixelRatio'](), _0x23a665 = {
                    'x': this.#uiGfx2d['width'] / _0x3d61a0 * 0.5,
                    'y': this.#uiGfx2d['height'] / _0x3d61a0 * 0.5
                };
            this.#uiGfx2d['save'](), this.#uiGfx2d['scale'](_0x3d61a0, _0x3d61a0);
            const _0x5296d8 = this.#centerMark['length'] * 0.5;
            this.#uiGfx2d['drawObjLine']({
                'x': _0x23a665['x'] - _0x5296d8,
                'y': _0x23a665['y']
            }, {
                'x': _0x23a665['x'] + _0x5296d8,
                'y': _0x23a665['y']
            }, this.#centerMark['thickness'], this.#centerMark['color'] ?? 'red'), this.#uiGfx2d['drawObjLine']({
                'x': _0x23a665['x'],
                'y': _0x23a665['y'] - _0x5296d8
            }, {
                'x': _0x23a665['x'],
                'y': _0x23a665['y'] + _0x5296d8
            }, this.#centerMark['thickness'], this.#centerMark['color'] ?? 'red'), this.#uiGfx2d['restore']();
        }
    }
    #drawDragArea() {
        if (this.#dragArea['modeOn'] == !![] && this.#dragArea['isReceivingCoords'] == !![]) {
            const _0x497998 = this['getDevicePixelRatio']();
            this.#uiGfx2d['save'](), this.#uiGfx2d['scale'](_0x497998, _0x497998), this.#uiGfx2d['drawObjPolyDashedLine']([
                this.#dragArea['pt0'],
                {
                    'x': this.#dragArea['pt0']['x'],
                    'y': this.#dragArea['pt1']['y']
                },
                this.#dragArea['pt1'],
                {
                    'x': this.#dragArea['pt1']['x'],
                    'y': this.#dragArea['pt0']['y']
                },
                this.#dragArea['pt0']
            ], this.#dragArea['width'], [
                this.#dragArea['dashLength'],
                this.#dragArea['dashSpace']
            ], this.#dragArea['color'], 0x0, 0x0), this.#uiGfx2d['restore']();
        }
    }
    #drawDevWaterMark() {
        if (this.#devWaterMarkFlag) {
            const _0x4f3388 = this['getDevicePixelRatio']();
            this.#uiGfx2d['save'](), this.#uiGfx2d['scale'](_0x4f3388, _0x4f3388), this.#uiGfx2d['drawObjText']('dev-server', 0x10, this['getCanvasHeight']() / _0x4f3388 - 0x8, logi['maps']['Defines']['DEFAULT_FONT'], 0x20, !![], '#00000020', 'left-bottom');
        }
    }
};
export default logi['maps']['UiLayer'];