import a0_0x20a62c from '../layer/logi-maps-layer.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Layer'] = a0_0x20a62c, logi['maps']['HeatmapLayer'] = class extends logi['maps']['Layer'] {
    #heatmapGfx2d = null;
    #offscreen = null;
    #offCtx = null;
    #dataArray;
    #radius;
    #opacity;
    constructor(_0x59d705, _0x593375, _0x500e9c) {
        if (_0x59d705 == 'parent') {
            const _0x56271e = _0x593375['id'] + '_heatmaplayer';
            var _0x38c243 = _0x593375['querySelector']('[id=\x22' + _0x56271e + '\x22]');
            if (_0x38c243)
                console['log']('Detected\x20existing\x20div.\x20Reusing\x20the\x20div.(' + _0x56271e + ')'), super(_0x38c243, _0x500e9c);
            else {
                const _0x4c2d61 = document['createElement']('div');
                _0x593375['appendChild'](_0x4c2d61), _0x4c2d61['id'] = _0x56271e, _0x4c2d61['style'] = 'position:absolute;\x20width:100%;\x20height:100%;\x20overflow-y:hidden;\x20overflow-x:hidden;', super(_0x4c2d61, _0x500e9c);
            }
        } else
            super(_0x593375, _0x500e9c);
        this['addGfxCanvas']('2d'), this.#heatmapGfx2d = this['getGfx2d'](0x0), this.#offscreen = document['createElement']('canvas'), this.#offCtx = this.#offscreen['getContext']('2d'), this.#dataArray = [], this.#radius = 0x28, this.#opacity = 0.75, this['setEventListener']('resize', this.#onResizeScreen);
    }
    #onResizeScreen(_0x12e54f, _0x35ac63, _0x4a929b) {
        this.#offscreen['width'] = _0x12e54f * _0x4a929b, this.#offscreen['height'] = _0x35ac63 * _0x4a929b;
    }
    ['setData'](_0x3eca84, _0x5d0bd0, _0x2fdc88) {
        _0x5d0bd0 != null && this.#radius != _0x5d0bd0 && (this.#radius = _0x5d0bd0), _0x2fdc88 != null && this.#opacity != _0x2fdc88 && (this.#opacity = Math['min'](0x1, Math['max'](0x0, _0x2fdc88))), _0x3eca84 == null ? this.#dataArray = [] : this.#dataArray = _0x3eca84['map'](_0x19ed75 => ({
            'latlng': {
                'lat': _0x19ed75['latlng']['lat'],
                'lng': _0x19ed75['latlng']['lng']
            },
            'intensity': _0x19ed75['intensity']
        })), this.#normalizeIntensity(), this.#updateOffscreen();
    }
    #normalizeIntensity() {
        const _0x384e69 = this.#dataArray['map'](_0x5297ec => _0x5297ec['intensity']), _0x3bc9e0 = Math['min'](..._0x384e69), _0x5088e1 = Math['max'](..._0x384e69), _0x4d4da2 = _0x5088e1 - _0x3bc9e0 || 0x1;
        this.#dataArray['forEach'](_0x1688a6 => {
            _0x1688a6['intensity'] = (_0x1688a6['intensity'] - _0x3bc9e0) / _0x4d4da2;
        });
    }
    #updateOffscreen() {
        const _0x17dac2 = {
                'w': this.#radius * 0x2,
                'h': this.#radius * 0x2
            }, _0x309b80 = {
                'x': this.#radius,
                'y': this.#radius,
                'radius': this.#radius,
                'startAngle': 0x0,
                'endAngle': Math['PI'] * 0x2
            }, _0x4627a0 = {
                'x0': this.#radius,
                'y0': this.#radius,
                'r0': 0x0,
                'x1': this.#radius,
                'y1': this.#radius,
                'r1': this.#radius
            };
        let _0x58eb35;
        for (const _0x28136c of this.#dataArray) {
            !_0x28136c['offscreen'] ? (_0x28136c['offscreen'] = document['createElement']('canvas'), _0x58eb35 = !![]) : _0x58eb35 = ![];
            (_0x28136c['offscreen']['width'] != _0x17dac2['w'] || _0x28136c['offscreen']['height'] != _0x17dac2['h']) && (_0x28136c['offscreen']['width'] = _0x17dac2['w'], _0x28136c['offscreen']['height'] = _0x17dac2['h']);
            const _0xe0c01f = _0x28136c['offscreen']['getContext']('2d');
            _0x58eb35 == ![] && _0xe0c01f['clearRect'](0x0, 0x0, _0x17dac2['w'], _0x17dac2['h']);
            const _0x584c40 = _0xe0c01f['createRadialGradient'](_0x4627a0['x0'], _0x4627a0['y0'], _0x4627a0['r0'], _0x4627a0['x1'], _0x4627a0['y1'], _0x4627a0['r1']);
            _0x584c40['addColorStop'](0x0, 'rgba(0,0,0,' + _0x28136c['intensity'] + ')'), _0x584c40['addColorStop'](0x1, 'rgba(0,0,0,0)'), _0xe0c01f['fillStyle'] = _0x584c40, _0xe0c01f['beginPath'](), _0xe0c01f['arc'](_0x309b80['x'], _0x309b80['y'], _0x309b80['radius'], _0x309b80['startAngle'], _0x309b80['endAngle']), _0xe0c01f['fill']();
        }
    }
    #drawAlphaMap() {
        this.#offCtx['clearRect'](0x0, 0x0, this.#offscreen['width'], this.#offscreen['height']);
        const _0x5b0620 = this['getMapCoord'](), _0x2608ba = [];
        this.#dataArray['forEach'](({
            latlng: _0x92cfc0,
            offscreen: _0x294395
        }) => {
            const _0x529dda = _0x5b0620['world2screen'](_0x92cfc0['lng'], _0x92cfc0['lat']);
            _0x2608ba['push']([
                _0x529dda['x'],
                _0x529dda['y'],
                _0x294395
            ]);
        }), _0x2608ba['forEach'](([_0x5d1692, _0x4fdd00, _0xc2f235]) => {
            _0xc2f235 && this.#offCtx['drawImage'](_0xc2f235, _0x5d1692 - _0xc2f235['width'] * 0.5, _0x4fdd00 - _0xc2f235['height'] * 0.5);
        });
    }
    #colorize() {
        const _0x55821e = [
                {
                    'r': 0x0,
                    'g': 0xff,
                    'b': 0x0
                },
                {
                    'r': 0xff,
                    'g': 0xff,
                    'b': 0x0
                },
                {
                    'r': 0xff,
                    'g': 0x0,
                    'b': 0x0
                }
            ], _0x28fcb2 = this.#offCtx['getImageData'](0x0, 0x0, this.#offscreen['width'], this.#offscreen['height']), _0x3a4a81 = _0x28fcb2['data'];
        for (let _0x2e37c2 = 0x0; _0x2e37c2 < _0x3a4a81['length']; _0x2e37c2 += 0x4) {
            const _0x3ce955 = _0x3a4a81[_0x2e37c2 + 0x3];
            if (_0x3ce955 < 0x3)
                continue;
            const _0x9ac05c = this.#getSpectrumColor(_0x3ce955 / 0xff, _0x55821e);
            _0x3a4a81[_0x2e37c2 + 0x0] = _0x9ac05c['r'], _0x3a4a81[_0x2e37c2 + 0x1] = _0x9ac05c['g'], _0x3a4a81[_0x2e37c2 + 0x2] = _0x9ac05c['b'], _0x3a4a81[_0x2e37c2 + 0x3] = this.#opacity * 0xff;
        }
        this.#heatmapGfx2d['getCtx']()['putImageData'](_0x28fcb2, 0x0, 0x0);
    }
    #getSpectrumColor(_0x1530fc, _0x5a95a8) {
        _0x1530fc = Math['max'](0x0, Math['min'](0x1, _0x1530fc));
        const _0x19611b = _0x5a95a8['length'], _0x400db8 = _0x1530fc * (_0x19611b - 0x1), _0x1fd352 = Math['floor'](_0x400db8), _0x2e4849 = Math['min'](_0x1fd352 + 0x1, _0x19611b - 0x1), _0x2c6ea2 = _0x400db8 - _0x1fd352, _0xb110c2 = _0x5a95a8[_0x1fd352], _0x56ae4c = _0x5a95a8[_0x2e4849], _0x27d0ca = Math['round'](_0xb110c2['r'] + (_0x56ae4c['r'] - _0xb110c2['r']) * _0x2c6ea2), _0x5f3d1b = Math['round'](_0xb110c2['g'] + (_0x56ae4c['g'] - _0xb110c2['g']) * _0x2c6ea2), _0x8f072e = Math['round'](_0xb110c2['b'] + (_0x56ae4c['b'] - _0xb110c2['b']) * _0x2c6ea2);
        return {
            'r': _0x27d0ca,
            'g': _0x5f3d1b,
            'b': _0x8f072e
        };
    }
    ['triggerEvent']() {
    }
    ['hitObj']() {
        return null;
    }
    ['updateCanvas']() {
        this['setDrawFlag']();
    }
    ['drawCanvas']() {
        this['clearColor'](), this.#offscreen['width'] > 0x0 && this.#offscreen['height'] > 0x0 && (this.#dataArray?.['length'] > 0x0 && (this.#drawAlphaMap(), this.#colorize()));
    }
};
export default logi['maps']['HeatmapLayer'];