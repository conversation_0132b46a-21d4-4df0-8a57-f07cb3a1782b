import a6_0x3a0482 from '../common/logi-maps-defines.js?v=2.1.10.1';
import a6_0x79ddb3 from '../common/logi-maps-types.js?v=2.1.10.1';
import a6_0x5581ba from '../config/lbsconfig.js?v=2.1.10.1';
import a6_0x5505fc from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a6_0x9740a2 from '../object/logi-maps-object.js?v=2.1.10.1';
import a6_0x5d619b from '../object/logi-maps-polygon.js?v=2.1.10.1';
import a6_0x187a58 from '../layer/logi-maps-layer.js?v=2.1.10.1';
import a6_0x32f77a from '../database/logi-maps-vtdatabase.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a6_0x3a0482, logi['maps']['DISTRICT_VISIBLETYPE'] = a6_0x79ddb3['DISTRICT_VISIBLETYPE'], logi['maps']['DISTRICT_DATATYPE'] = a6_0x79ddb3['DISTRICT_DATATYPE'], logi['maps']['Config'] = a6_0x5581ba, logi['maps']['Utils'] = a6_0x5505fc, logi['maps']['Object'] = a6_0x9740a2, logi['maps']['Polygon'] = a6_0x5d619b, logi['maps']['Layer'] = a6_0x187a58, logi['maps']['VtDatabase'] = a6_0x32f77a;
class DistrictData {
    ['range'] = {
        'minLevel': 0x0,
        'maxLevel': 0x0
    };
    ['style'] = {
        'polygon': {
            'fillColor': '#000000',
            'lineWidth': 0x0,
            'lineColor': '#000000',
            'dashLength': 0x0,
            'dashSpace': 0x0
        },
        'text': {
            'fontFamily': logi['maps']['Defines']['DEFAULT_FONT'],
            'fontBold': !![],
            'fontColor': '#000000',
            'fontSize': [0x0]
        },
        'textBox': null
    };
    ['hoverRange'] = {
        'minLevel': 0x0,
        'maxLevel': 0x0
    };
    ['hoverStyle'] = {
        'polygon': {
            'fillColor': '#000000',
            'lineWidth': 0x0,
            'lineColor': '#000000',
            'dashLength': 0x0,
            'dashSpace': 0x0
        },
        'text': {
            'fontFamily': logi['maps']['Defines']['DEFAULT_FONT'],
            'fontBold': !![],
            'fontColor': '#000000',
            'fontSize': 0x0
        },
        'textBox': null
    };
    ['rects'] = new Array();
    ['cache'] = new Map();
    ['MAX_CACHE_SIZE'] = 0x0;
    ['waitingForResponse'] = new Map();
    ['MAX_WAITING_SIZE'] = 0x0;
    ['MAX_WAITING_TIME'] = 0x0;
    ['notFounds'] = new Map();
    ['MAX_NOT_FOUND_TIME'] = 0x0;
    constructor(_0x34b7d3, _0x427e56, _0x3fbeb2, _0x25f8a5, _0x28e620, _0x2ac633, _0x16026f, _0x12a8b4) {
        this['range'] = {
            'minLevel': _0x34b7d3['minLevel'],
            'maxLevel': _0x34b7d3['maxLevel']
        }, this['style'] = {
            'polygon': _0x427e56['polygon'],
            'text': _0x427e56['text'],
            'textBox': _0x427e56['textBox']
        }, this['hoverRange'] = {
            'minLevel': _0x3fbeb2['minLevel'],
            'maxLevel': _0x3fbeb2['maxLevel']
        }, this['hoverStyle'] = {
            'polygon': _0x25f8a5['polygon'],
            'text': _0x25f8a5['text'],
            'textBox': _0x25f8a5['textBox']
        }, this['rects'] = new Array(), this['cache'] = new Map(), this['MAX_CACHE_SIZE'] = _0x28e620, this['waitingForResponse'] = new Map(), this['MAX_WAITING_SIZE'] = _0x2ac633, this['MAX_WAITING_TIME'] = _0x16026f, this['notFounds'] = new Map(), this['MAX_NOT_FOUND_TIME'] = _0x12a8b4;
    }
}
class DistrictArea {
    ['code'] = '';
    ['centroid'] = {
        'lat': 0x0,
        'lng': 0x0
    };
    ['name'] = '';
    ['polygons'] = [];
    ['boundaryRect'] = {
        'xMin': 0x0,
        'yMin': 0x0,
        'xMax': 0x0,
        'yMax': 0x0
    };
    constructor(_0x449fbf, _0x3e54dc, _0x21745b, _0x49d9d4, _0x495041) {
        this['code'] = _0x449fbf, this['centroid'] = _0x3e54dc, this['name'] = _0x21745b, this['polygons'] = _0x49d9d4, this['boundaryRect'] = _0x495041;
    }
}
logi['maps']['VtLayer'] = class extends logi['maps']['Layer'] {
    #fillGfx2d = null;
    #strokeGfx2d = null;
    #textGfx2d = null;
    #remoteServerUrl = '';
    #vtDatabase;
    #visibleType = logi['maps']['DISTRICT_VISIBLETYPE']['ALL_OFF'];
    #mousePos = {
        'x': null,
        'y': null
    };
    #hoverDistrict = {
        'currentRaw': null,
        'lastFormatted': null
    };
    #searchDistrict = {
        'area': null,
        'style': {
            'polygon': {
                'fillColor': '#C8C8C899',
                'lineWidth': 0x1,
                'lineColor': '#C35C01AA',
                'dashLength': 0x0,
                'dashSpace': 0x0
            },
            'text': {
                'fontFamily': logi['maps']['Defines']['DEFAULT_FONT'],
                'fontBold': !![],
                'fontColor': '#004698FF',
                'fontSize': 0xe
            },
            'textBox': null
        }
    };
    #postData = new DistrictData({
        'minLevel': 0xe,
        'maxLevel': 0x13
    }, {
        'polygon': {
            'fillColor': '#C8C8C899',
            'lineWidth': 0.8,
            'lineColor': '#C35C01AA',
            'dashLength': 0x0,
            'dashSpace': 0x0
        },
        'text': {
            'fontFamily': logi['maps']['Defines']['DEFAULT_FONT'],
            'fontBold': !![],
            'fontColor': '#004698FF',
            'fontSize': [
                0x0,
                0xc,
                0xe,
                0x10,
                0x12,
                0x14
            ]
        },
        'textBox': null
    }, {
        'minLevel': 0xe,
        'maxLevel': 0x13
    }, {
        'polygon': {
            'fillColor': '#C8C8C899',
            'lineWidth': 0.8,
            'lineColor': '#C35C01AA',
            'dashLength': 0x0,
            'dashSpace': 0x0
        },
        'text': {
            'fontFamily': logi['maps']['Defines']['DEFAULT_FONT'],
            'fontBold': !![],
            'fontColor': '#004698FF',
            'fontSize': 0xe
        },
        'textBox': {
            'fillColor': '#FFFFFF',
            'lineWidth': 0x1,
            'lineColor': '#585858',
            'radius': 0x2
        }
    }, 0xa00, 0x400, 0xfa0, 0x1388);
    #sidoData = new DistrictData({
        'minLevel': 0x4,
        'maxLevel': 0x9
    }, {
        'polygon': {
            'fillColor': '#C8C8C899',
            'lineWidth': 0x1,
            'lineColor': '#C35C01AA',
            'dashLength': 0xa,
            'dashSpace': 0x4
        },
        'text': {
            'fontFamily': logi['maps']['Defines']['DEFAULT_FONT'],
            'fontBold': !![],
            'fontColor': '#004698FF',
            'fontSize': [
                0x0,
                0xa,
                0xb,
                0xc,
                0xd,
                0xe
            ]
        },
        'textBox': null
    }, {
        'minLevel': 0x4,
        'maxLevel': 0x9
    }, {
        'polygon': {
            'fillColor': '#C8C8C899',
            'lineWidth': 0x1,
            'lineColor': '#C35C01AA',
            'dashLength': 0xa,
            'dashSpace': 0x4
        },
        'text': {
            'fontFamily': logi['maps']['Defines']['DEFAULT_FONT'],
            'fontBold': !![],
            'fontColor': '#004698FF',
            'fontSize': 0xe
        },
        'textBox': {
            'fillColor': '#FFFFFF',
            'lineWidth': 0x1,
            'lineColor': '#585858',
            'radius': 0x2
        }
    }, 0x140, 0x80, 0xfa0, 0x1388);
    #sggData = new DistrictData({
        'minLevel': 0xa,
        'maxLevel': 0x13
    }, {
        'polygon': {
            'fillColor': '#C8C8C899',
            'lineWidth': 0x1,
            'lineColor': '#C35C01AA',
            'dashLength': 0xa,
            'dashSpace': 0x4
        },
        'text': {
            'fontFamily': logi['maps']['Defines']['DEFAULT_FONT'],
            'fontBold': !![],
            'fontColor': '#004698FF',
            'fontSize': [
                0x0,
                0xa,
                0xb,
                0xc,
                0xd,
                0xe,
                0xf,
                0x10,
                0x11,
                0x12
            ]
        },
        'textBox': null
    }, {
        'minLevel': 0xa,
        'maxLevel': 0x13
    }, {
        'polygon': {
            'fillColor': '#C8C8C899',
            'lineWidth': 0x1,
            'lineColor': '#C35C01AA',
            'dashLength': 0xa,
            'dashSpace': 0x4
        },
        'text': {
            'fontFamily': logi['maps']['Defines']['DEFAULT_FONT'],
            'fontBold': !![],
            'fontColor': '#004698FF',
            'fontSize': 0xe
        },
        'textBox': {
            'fillColor': '#FFFFFF',
            'lineWidth': 0x1,
            'lineColor': '#585858',
            'radius': 0x2
        }
    }, 0x500, 0x200, 0xfa0, 0x1388);
    #emdData = new DistrictData({
        'minLevel': 0xe,
        'maxLevel': 0x13
    }, {
        'polygon': {
            'fillColor': '#C8C8C899',
            'lineWidth': 0.8,
            'lineColor': '#C35C01AA',
            'dashLength': 0xa,
            'dashSpace': 0x4
        },
        'text': {
            'fontFamily': logi['maps']['Defines']['DEFAULT_FONT'],
            'fontBold': !![],
            'fontColor': '#004698FF',
            'fontSize': [
                0x0,
                0xa,
                0xc,
                0xe,
                0x10,
                0x12
            ]
        },
        'textBox': null
    }, {
        'minLevel': 0xe,
        'maxLevel': 0x13
    }, {
        'polygon': {
            'fillColor': '#C8C8C899',
            'lineWidth': 0.8,
            'lineColor': '#C35C01AA',
            'dashLength': 0xa,
            'dashSpace': 0x4
        },
        'text': {
            'fontFamily': logi['maps']['Defines']['DEFAULT_FONT'],
            'fontBold': !![],
            'fontColor': '#004698FF',
            'fontSize': 0xe
        },
        'textBox': {
            'fillColor': '#FFFFFF',
            'lineWidth': 0x1,
            'lineColor': '#585858',
            'radius': 0x2
        }
    }, 0xa00, 0x400, 0xfa0, 0x1388);
    constructor(_0x3da5ab, _0x47f22e, _0x114089, _0x10aada) {
        if (_0x3da5ab == 'parent') {
            const _0x4e603b = _0x47f22e['id'] + '_vtlayer';
            var _0x217013 = _0x47f22e['querySelector']('[id=\x22' + _0x4e603b + '\x22]');
            if (_0x217013)
                console['log']('Detected\x20existing\x20div.\x20Reusing\x20the\x20div.(' + _0x4e603b + ')'), super(_0x217013, _0x114089);
            else {
                const _0x22951a = document['createElement']('div');
                _0x47f22e['appendChild'](_0x22951a), _0x22951a['id'] = _0x4e603b, _0x22951a['style'] = 'position:absolute;\x20width:100%;\x20height:100%;\x20overflow-y:hidden;\x20overflow-x:hidden;', super(_0x22951a, _0x114089);
            }
        } else
            super(_0x47f22e, _0x114089);
        this['addGfxCanvas']('2d'), this.#fillGfx2d = this['getGfx2d'](0x0), this['addGfxCanvas']('2d'), this.#strokeGfx2d = this['getGfx2d'](0x1), this['addGfxCanvas']('2d'), this.#textGfx2d = this['getGfx2d'](0x2), this.#remoteServerUrl = _0x10aada, this.#vtDatabase = new logi['maps']['VtDatabase']();
        const _0x5d581c = this.#getVtDataVersionUrl();
        _0x5d581c !== '' && fetch(_0x5d581c)['then'](_0x3f275c => {
            return _0x3f275c['text']();
        })['then'](_0x23f312 => {
            this.#initDatabase(_0x23f312);
        })['catch'](() => {
            this.#initDatabase(null);
        });
    }
    ['getHoveredDistrict']() {
        if (!this.#hoverDistrict['currentRaw'])
            return null;
        if (this.#hoverDistrict['lastFormatted'] == null || this.#hoverDistrict['currentRaw']['code'] != this.#hoverDistrict['lastFormatted']['code']) {
            this.#hoverDistrict['lastFormatted'] = {
                'code': null,
                'centroid': {
                    'lat': null,
                    'lng': null
                },
                'name': '',
                'polygons': []
            }, this.#hoverDistrict['lastFormatted']['code'] = this.#hoverDistrict['currentRaw']['code'], this.#hoverDistrict['lastFormatted']['centroid']['lat'] = this.#hoverDistrict['currentRaw']['centroid']['lat'], this.#hoverDistrict['lastFormatted']['centroid']['lng'] = this.#hoverDistrict['currentRaw']['centroid']['lng'], this.#hoverDistrict['lastFormatted']['name'] = this.#hoverDistrict['currentRaw']['name'];
            for (let _0x328c23 of this.#hoverDistrict['currentRaw']['polygons']) {
                const _0x20beb6 = [];
                for (let _0x37d07e of _0x328c23['latlngs']) {
                    _0x20beb6['push']({
                        'lat': _0x37d07e['lat'],
                        'lng': _0x37d07e['lng']
                    });
                }
                this.#hoverDistrict['lastFormatted']['polygons']['push']({ 'latlngs': _0x20beb6 });
            }
        }
        return this.#hoverDistrict['lastFormatted'];
    }
    ['setDistrictVisible'](_0x43d6f6) {
        if (this.#visibleType != _0x43d6f6)
            switch (_0x43d6f6) {
            case logi['maps']['DISTRICT_VISIBLETYPE']['POST_ON']:
            case logi['maps']['DISTRICT_VISIBLETYPE']['SIDO_ON']:
            case logi['maps']['DISTRICT_VISIBLETYPE']['SGG_ON']:
            case logi['maps']['DISTRICT_VISIBLETYPE']['EMD_ON']:
            case logi['maps']['DISTRICT_VISIBLETYPE']['POST_HOVER']:
            case logi['maps']['DISTRICT_VISIBLETYPE']['SIDO_HOVER']:
            case logi['maps']['DISTRICT_VISIBLETYPE']['SGG_HOVER']:
            case logi['maps']['DISTRICT_VISIBLETYPE']['EMD_HOVER']:
            case logi['maps']['DISTRICT_VISIBLETYPE']['ALL_OFF']:
                this.#visibleType = _0x43d6f6, this.#hoverDistrict['currentRaw'] = null, this.#mousePos['x'] = null, this.#mousePos['y'] = null, this['setUpdateFlag']();
                break;
            default:
            }
    }
    ['setDistrictRange'](_0x1f7c7d, _0x3bf2a8, _0x23ba7f) {
        let _0x7dada2 = null;
        if (_0x1f7c7d == logi['maps']['DISTRICT_DATATYPE']['POST'])
            _0x7dada2 = this.#postData['range'];
        else {
            if (_0x1f7c7d == logi['maps']['DISTRICT_DATATYPE']['SIDO'])
                _0x7dada2 = this.#sidoData['range'];
            else {
                if (_0x1f7c7d == logi['maps']['DISTRICT_DATATYPE']['SGG'])
                    _0x7dada2 = this.#sggData['range'];
                else
                    _0x1f7c7d == logi['maps']['DISTRICT_DATATYPE']['EMD'] && (_0x7dada2 = this.#emdData['range']);
            }
        }
        _0x7dada2 && (_0x7dada2['minLevel'] != _0x3bf2a8 && (_0x7dada2['minLevel'] = _0x3bf2a8, this['setUpdateFlag']()), _0x7dada2['maxLevel'] != _0x23ba7f && (_0x7dada2['maxLevel'] = _0x23ba7f, this['setUpdateFlag']()));
    }
    ['setDistrictStyle'](_0xe90abc, _0x2d6f99, _0x11c26e, _0x1ae9ca = null) {
        let _0x42ca10 = null;
        if (_0xe90abc == logi['maps']['DISTRICT_DATATYPE']['POST'])
            _0x42ca10 = this.#postData['style'];
        else {
            if (_0xe90abc == logi['maps']['DISTRICT_DATATYPE']['SIDO'])
                _0x42ca10 = this.#sidoData['style'];
            else {
                if (_0xe90abc == logi['maps']['DISTRICT_DATATYPE']['SGG'])
                    _0x42ca10 = this.#sggData['style'];
                else
                    _0xe90abc == logi['maps']['DISTRICT_DATATYPE']['EMD'] && (_0x42ca10 = this.#emdData['style']);
            }
        }
        if (_0x42ca10) {
            if (!_0x2d6f99)
                _0x42ca10['polygon'] != _0x2d6f99 && (_0x42ca10['polygon'] = _0x2d6f99, this['setUpdateFlag']());
            else {
                !_0x42ca10['polygon'] && (_0x42ca10['polygon'] = {});
                const _0xa974c8 = _0x42ca10['polygon'];
                _0xa974c8['fillColor'] != _0x2d6f99['fillColor'] && _0x2d6f99['fillColor'] != null && (_0xa974c8['fillColor'] = _0x2d6f99['fillColor'], this['setUpdateFlag']()), _0xa974c8['lineWidth'] != _0x2d6f99['lineWidth'] && _0x2d6f99['lineWidth'] != null && (_0xa974c8['lineWidth'] = _0x2d6f99['lineWidth'], this['setUpdateFlag']()), _0xa974c8['lineColor'] != _0x2d6f99['lineColor'] && _0x2d6f99['lineColor'] != null && (_0xa974c8['lineColor'] = _0x2d6f99['lineColor'], this['setUpdateFlag']()), _0xa974c8['dashLength'] != _0x2d6f99['dashLength'] && _0x2d6f99['dashLength'] != null && (_0xa974c8['dashLength'] = _0x2d6f99['dashLength'], this['setUpdateFlag']()), _0xa974c8['dashSpace'] != _0x2d6f99['dashSpace'] && _0x2d6f99['dashSpace'] != null && (_0xa974c8['dashSpace'] = _0x2d6f99['dashSpace'], this['setUpdateFlag']());
            }
            if (!_0x11c26e)
                _0x42ca10['text'] != _0x11c26e && (_0x42ca10['text'] = _0x11c26e, this['setUpdateFlag']());
            else {
                !_0x42ca10['text'] && (_0x42ca10['text'] = {});
                const _0x1eaa20 = _0x42ca10['text'];
                _0x1eaa20['fontFamily'] != _0x11c26e['fontFamily'] && _0x11c26e['fontFamily'] != null && (_0x1eaa20['fontFamily'] = _0x11c26e['fontFamily'], this['setUpdateFlag']());
                _0x1eaa20['fontBold'] != _0x11c26e['fontBold'] && _0x11c26e['fontBold'] != null && (_0x1eaa20['fontBold'] = _0x11c26e['fontBold'], this['setUpdateFlag']());
                _0x1eaa20['fontColor'] != _0x11c26e['fontColor'] && _0x11c26e['fontColor'] != null && (_0x1eaa20['fontColor'] = _0x11c26e['fontColor'], this['setUpdateFlag']());
                if (_0x11c26e['fontSize'] != null) {
                    let _0x331891 = ![], _0x4dd81a = Array['isArray'](_0x11c26e['fontSize']) == !![] ? _0x11c26e['fontSize'] : [_0x11c26e['fontSize']];
                    if (_0x1eaa20['fontSize']['length'] != _0x4dd81a['length'])
                        _0x331891 = !![];
                    else
                        for (let _0x31506f = 0x0; _0x31506f < _0x4dd81a['length']; ++_0x31506f) {
                            if (_0x1eaa20['fontSize'][_0x31506f] != _0x4dd81a[_0x31506f]) {
                                _0x331891 = !![];
                                break;
                            }
                        }
                    if (_0x331891 == !![]) {
                        _0x1eaa20['fontSize'] = new Array();
                        for (let _0x11e31b = 0x0; _0x11e31b < _0x4dd81a['length']; ++_0x11e31b) {
                            _0x1eaa20['fontSize']['push'](_0x4dd81a[_0x11e31b]);
                        }
                        this['setUpdateFlag']();
                    }
                }
            }
            if (!_0x1ae9ca)
                _0x42ca10['textBox'] != _0x1ae9ca && (_0x42ca10['textBox'] = _0x1ae9ca, this['setUpdateFlag']());
            else {
                !_0x42ca10['textBox'] && (_0x42ca10['textBox'] = {});
                const _0x4d2400 = _0x42ca10['textBox'];
                _0x4d2400['fillColor'] != _0x1ae9ca['fillColor'] && _0x1ae9ca['fillColor'] != null && (_0x4d2400['fillColor'] = _0x1ae9ca['fillColor'], this['setUpdateFlag']()), _0x4d2400['lineWidth'] != _0x1ae9ca['lineWidth'] && _0x1ae9ca['lineWidth'] != null && (_0x4d2400['lineWidth'] = _0x1ae9ca['lineWidth'], this['setUpdateFlag']()), _0x4d2400['lineColor'] != _0x1ae9ca['lineColor'] && _0x1ae9ca['lineColor'] != null && (_0x4d2400['lineColor'] = _0x1ae9ca['lineColor'], this['setUpdateFlag']()), _0x4d2400['radius'] != _0x1ae9ca['radius'] && _0x1ae9ca['radius'] != null && (_0x4d2400['radius'] = _0x1ae9ca['radius'], this['setUpdateFlag']());
            }
        }
    }
    ['getDistrictStyle'](_0x1141cc) {
        if (_0x1141cc == logi['maps']['DISTRICT_DATATYPE']['POST'])
            return JSON['parse'](JSON['stringify'](this.#postData['style']));
        else {
            if (_0x1141cc == logi['maps']['DISTRICT_DATATYPE']['SIDO'])
                return JSON['parse'](JSON['stringify'](this.#sidoData['style']));
            else {
                if (_0x1141cc == logi['maps']['DISTRICT_DATATYPE']['SGG'])
                    return JSON['parse'](JSON['stringify'](this.#sggData['style']));
                else {
                    if (_0x1141cc == logi['maps']['DISTRICT_DATATYPE']['EMD'])
                        return JSON['parse'](JSON['stringify'](this.#emdData['style']));
                }
            }
        }
        return null;
    }
    ['setDistrictHoverRange'](_0xd6ac6e, _0x385ee3, _0x321601) {
        let _0x8c9e9a = null;
        if (_0xd6ac6e == logi['maps']['DISTRICT_DATATYPE']['POST'])
            _0x8c9e9a = this.#postData['hoverRange'];
        else {
            if (_0xd6ac6e == logi['maps']['DISTRICT_DATATYPE']['SIDO'])
                _0x8c9e9a = this.#sidoData['hoverRange'];
            else {
                if (_0xd6ac6e == logi['maps']['DISTRICT_DATATYPE']['SGG'])
                    _0x8c9e9a = this.#sggData['hoverRange'];
                else
                    _0xd6ac6e == logi['maps']['DISTRICT_DATATYPE']['EMD'] && (_0x8c9e9a = this.#emdData['hoverRange']);
            }
        }
        _0x8c9e9a && (_0x8c9e9a['minLevel'] != _0x385ee3 && (_0x8c9e9a['minLevel'] = _0x385ee3, this['setUpdateFlag']()), _0x8c9e9a['maxLevel'] != _0x321601 && (_0x8c9e9a['maxLevel'] = _0x321601, this['setUpdateFlag']()));
    }
    ['setDistrictHoverStyle'](_0x3893ea, _0x26d4e2, _0x758409, _0x5a7a2f = null) {
        let _0xbf762f = null;
        if (_0x3893ea == logi['maps']['DISTRICT_DATATYPE']['POST'])
            _0xbf762f = this.#postData['hoverStyle'];
        else {
            if (_0x3893ea == logi['maps']['DISTRICT_DATATYPE']['SIDO'])
                _0xbf762f = this.#sidoData['hoverStyle'];
            else {
                if (_0x3893ea == logi['maps']['DISTRICT_DATATYPE']['SGG'])
                    _0xbf762f = this.#sggData['hoverStyle'];
                else
                    _0x3893ea == logi['maps']['DISTRICT_DATATYPE']['EMD'] && (_0xbf762f = this.#emdData['hoverStyle']);
            }
        }
        if (_0xbf762f) {
            if (!_0x26d4e2)
                _0xbf762f['polygon'] != _0x26d4e2 && (_0xbf762f['polygon'] = _0x26d4e2, this['setUpdateFlag']());
            else {
                !_0xbf762f['polygon'] && (_0xbf762f['polygon'] = {});
                const _0x152cf6 = _0xbf762f['polygon'];
                _0x152cf6['fillColor'] != _0x26d4e2['fillColor'] && _0x26d4e2['fillColor'] != null && (_0x152cf6['fillColor'] = _0x26d4e2['fillColor'], this['setUpdateFlag']()), _0x152cf6['lineWidth'] != _0x26d4e2['lineWidth'] && _0x26d4e2['lineWidth'] != null && (_0x152cf6['lineWidth'] = _0x26d4e2['lineWidth'], this['setUpdateFlag']()), _0x152cf6['lineColor'] != _0x26d4e2['lineColor'] && _0x26d4e2['lineColor'] != null && (_0x152cf6['lineColor'] = _0x26d4e2['lineColor'], this['setUpdateFlag']()), _0x152cf6['dashLength'] != _0x26d4e2['dashLength'] && _0x26d4e2['dashLength'] != null && (_0x152cf6['dashLength'] = _0x26d4e2['dashLength'], this['setUpdateFlag']()), _0x152cf6['dashSpace'] != _0x26d4e2['dashSpace'] && _0x26d4e2['dashSpace'] != null && (_0x152cf6['dashSpace'] = _0x26d4e2['dashSpace'], this['setUpdateFlag']());
            }
            if (!_0x758409)
                _0xbf762f['text'] != _0x758409 && (_0xbf762f['text'] = _0x758409, this['setUpdateFlag']());
            else {
                !_0xbf762f['text'] && (_0xbf762f['text'] = {});
                const _0x29a6df = _0xbf762f['text'];
                _0x29a6df['fontFamily'] != _0x758409['fontFamily'] && _0x758409['fontFamily'] != null && (_0x29a6df['fontFamily'] = _0x758409['fontFamily'], this['setUpdateFlag']()), _0x29a6df['fontBold'] != _0x758409['fontBold'] && _0x758409['fontBold'] != null && (_0x29a6df['fontBold'] = _0x758409['fontBold'], this['setUpdateFlag']()), _0x29a6df['fontColor'] != _0x758409['fontColor'] && _0x758409['fontColor'] != null && (_0x29a6df['fontColor'] = _0x758409['fontColor'], this['setUpdateFlag']()), _0x29a6df['fontSize'] != _0x758409['fontSize'] && _0x758409['fontSize'] != null && (_0x29a6df['fontSize'] = _0x758409['fontSize'], this['setUpdateFlag']());
            }
            if (!_0x5a7a2f)
                _0xbf762f['textBox'] != _0x5a7a2f && (_0xbf762f['textBox'] = _0x5a7a2f, this['setUpdateFlag']());
            else {
                !_0xbf762f['textBox'] && (_0xbf762f['textBox'] = {});
                const _0x1681b3 = _0xbf762f['textBox'];
                _0x1681b3['fillColor'] != _0x5a7a2f['fillColor'] && _0x5a7a2f['fillColor'] != null && (_0x1681b3['fillColor'] = _0x5a7a2f['fillColor'], this['setUpdateFlag']()), _0x1681b3['lineWidth'] != _0x5a7a2f['lineWidth'] && _0x5a7a2f['lineWidth'] != null && (_0x1681b3['lineWidth'] = _0x5a7a2f['lineWidth'], this['setUpdateFlag']()), _0x1681b3['lineColor'] != _0x5a7a2f['lineColor'] && _0x5a7a2f['lineColor'] != null && (_0x1681b3['lineColor'] = _0x5a7a2f['lineColor'], this['setUpdateFlag']()), _0x1681b3['radius'] != _0x5a7a2f['radius'] && _0x5a7a2f['radius'] != null && (_0x1681b3['radius'] = _0x5a7a2f['radius'], this['setUpdateFlag']());
            }
        }
    }
    ['getDistrictHoverStyle'](_0x45a8be) {
        if (_0x45a8be == logi['maps']['DISTRICT_DATATYPE']['POST'])
            return JSON['parse'](JSON['stringify'](this.#postData['hoverStyle']));
        else {
            if (_0x45a8be == logi['maps']['DISTRICT_DATATYPE']['SIDO'])
                return JSON['parse'](JSON['stringify'](this.#sidoData['hoverStyle']));
            else {
                if (_0x45a8be == logi['maps']['DISTRICT_DATATYPE']['SGG'])
                    return JSON['parse'](JSON['stringify'](this.#sggData['hoverStyle']));
                else {
                    if (_0x45a8be == logi['maps']['DISTRICT_DATATYPE']['EMD'])
                        return JSON['parse'](JSON['stringify'](this.#emdData['hoverStyle']));
                }
            }
        }
        return null;
    }
    async ['setSearchDistrict'](_0x583f9a) {
        let _0x42a256 = null;
        if (_0x583f9a == null)
            this.#searchDistrict['area'] = null;
        else {
            if (_0x583f9a['length'] == 0xa) {
                const _0x4e8d52 = _0x583f9a['slice'](0x0, 0x2), _0x471639 = _0x583f9a['slice'](0x2, 0x5), _0x41a67f = _0x583f9a['slice'](0x5, 0x8);
                if (Number(_0x4e8d52) > 0x0) {
                    const _0x28c493 = {
                        'west': 0x7f,
                        'south': 0x24,
                        'east': 0x8a,
                        'north': 0x26
                    };
                    _0x42a256 = await this.#searchSidoData(_0x4e8d52 + '00000000', _0x28c493);
                }
                if (_0x42a256 && Number(_0x471639) > 0x0) {
                    const _0x39c2a5 = {
                        'west': _0x42a256['boundaryRect']['xMin'],
                        'south': _0x42a256['boundaryRect']['yMin'],
                        'east': _0x42a256['boundaryRect']['xMax'],
                        'north': _0x42a256['boundaryRect']['yMax']
                    };
                    _0x42a256 = await this.#searchSggData('' + _0x4e8d52 + _0x471639 + '00000', _0x39c2a5);
                }
                if (_0x42a256 && Number(_0x41a67f) > 0x0) {
                    const _0x4e3971 = {
                        'west': _0x42a256['boundaryRect']['xMin'],
                        'south': _0x42a256['boundaryRect']['yMin'],
                        'east': _0x42a256['boundaryRect']['xMax'],
                        'north': _0x42a256['boundaryRect']['yMax']
                    };
                    _0x42a256 = await this.#searchEmdData('' + _0x4e8d52 + _0x471639 + _0x41a67f + '00', _0x4e3971);
                }
            }
            this.#searchDistrict['area'] = _0x42a256;
        }
        return this['setUpdateFlag'](), _0x42a256;
    }
    ['setSearchDistrictStyle'](_0x28983f, _0x1ea999, _0x5c4ef9 = null) {
        const _0x4afb82 = this.#searchDistrict['style'];
        if (!_0x28983f)
            _0x4afb82['polygon'] != _0x28983f && (_0x4afb82['polygon'] = _0x28983f, this['setUpdateFlag']());
        else {
            !_0x4afb82['polygon'] && (_0x4afb82['polygon'] = {});
            const _0x228e9b = _0x4afb82['polygon'];
            _0x228e9b['fillColor'] != _0x28983f['fillColor'] && _0x28983f['fillColor'] != null && (_0x228e9b['fillColor'] = _0x28983f['fillColor'], this['setUpdateFlag']()), _0x228e9b['lineWidth'] != _0x28983f['lineWidth'] && _0x28983f['lineWidth'] != null && (_0x228e9b['lineWidth'] = _0x28983f['lineWidth'], this['setUpdateFlag']()), _0x228e9b['lineColor'] != _0x28983f['lineColor'] && _0x28983f['lineColor'] != null && (_0x228e9b['lineColor'] = _0x28983f['lineColor'], this['setUpdateFlag']()), _0x228e9b['dashLength'] != _0x28983f['dashLength'] && _0x28983f['dashLength'] != null && (_0x228e9b['dashLength'] = _0x28983f['dashLength'], this['setUpdateFlag']()), _0x228e9b['dashSpace'] != _0x28983f['dashSpace'] && _0x28983f['dashSpace'] != null && (_0x228e9b['dashSpace'] = _0x28983f['dashSpace'], this['setUpdateFlag']());
        }
        if (!_0x1ea999)
            _0x4afb82['text'] != _0x1ea999 && (_0x4afb82['text'] = _0x1ea999, this['setUpdateFlag']());
        else {
            !_0x4afb82['text'] && (_0x4afb82['text'] = {});
            const _0x5cc72a = _0x4afb82['text'];
            _0x5cc72a['fontFamily'] != _0x1ea999['fontFamily'] && _0x1ea999['fontFamily'] != null && (_0x5cc72a['fontFamily'] = _0x1ea999['fontFamily'], this['setUpdateFlag']()), _0x5cc72a['fontBold'] != _0x1ea999['fontBold'] && _0x1ea999['fontBold'] != null && (_0x5cc72a['fontBold'] = _0x1ea999['fontBold'], this['setUpdateFlag']()), _0x5cc72a['fontColor'] != _0x1ea999['fontColor'] && _0x1ea999['fontColor'] != null && (_0x5cc72a['fontColor'] = _0x1ea999['fontColor'], this['setUpdateFlag']()), _0x5cc72a['fontSize'] != _0x1ea999['fontSize'] && _0x1ea999['fontSize'] != null && (_0x5cc72a['fontSize'] = _0x1ea999['fontSize'], this['setUpdateFlag']());
        }
        if (!_0x5c4ef9)
            _0x4afb82['textBox'] != _0x5c4ef9 && (_0x4afb82['textBox'] = _0x5c4ef9, this['setUpdateFlag']());
        else {
            !_0x4afb82['textBox'] && (_0x4afb82['textBox'] = {});
            const _0x4e7953 = _0x4afb82['textBox'];
            _0x4e7953['fillColor'] != _0x5c4ef9['fillColor'] && _0x5c4ef9['fillColor'] != null && (_0x4e7953['fillColor'] = _0x5c4ef9['fillColor'], this['setUpdateFlag']()), _0x4e7953['lineWidth'] != _0x5c4ef9['lineWidth'] && _0x5c4ef9['lineWidth'] != null && (_0x4e7953['lineWidth'] = _0x5c4ef9['lineWidth'], this['setUpdateFlag']()), _0x4e7953['lineColor'] != _0x5c4ef9['lineColor'] && _0x5c4ef9['lineColor'] != null && (_0x4e7953['lineColor'] = _0x5c4ef9['lineColor'], this['setUpdateFlag']()), _0x4e7953['radius'] != _0x5c4ef9['radius'] && _0x5c4ef9['radius'] != null && (_0x4e7953['radius'] = _0x5c4ef9['radius'], this['setUpdateFlag']());
        }
    }
    ['getSearchDistrictStyle']() {
        return JSON['parse'](JSON['stringify'](this.#searchDistrict['style']));
    }
    ['setMousePos'](_0x4291e8, _0x5051a8) {
        (this.#visibleType == logi['maps']['DISTRICT_VISIBLETYPE']['POST_HOVER'] || this.#visibleType == logi['maps']['DISTRICT_VISIBLETYPE']['SIDO_HOVER'] || this.#visibleType == logi['maps']['DISTRICT_VISIBLETYPE']['SGG_HOVER'] || this.#visibleType == logi['maps']['DISTRICT_VISIBLETYPE']['EMD_HOVER']) && ((this.#mousePos['x'] != _0x4291e8 || this.#mousePos['y'] != _0x5051a8) && (this.#mousePos['x'] = _0x4291e8, this.#mousePos['y'] = _0x5051a8, this['setUpdateFlag']()));
    }
    ['preWork']() {
        const _0x2f1ba6 = [
            this.#postData,
            this.#sidoData,
            this.#sggData,
            this.#emdData
        ];
        for (const _0x94b843 of _0x2f1ba6) {
            if (_0x94b843['waitingForResponse']['size'] >= _0x94b843['MAX_WAITING_SIZE']) {
                const _0x4f24b0 = logi['maps']['Utils']['getCurTick']();
                for (const [_0x4069e2, _0x29293a] of _0x94b843['waitingForResponse']) {
                    _0x4f24b0 > _0x29293a + _0x94b843['MAX_WAITING_TIME'] && _0x94b843['waitingForResponse']['delete'](_0x4069e2);
                }
            }
        }
    }
    ['postWork']() {
        this.#vtDatabase['putDatas'](), this.#vtDatabase['resizeDatabase']();
    }
    ['updateCanvas']() {
        this['setDrawFlag']();
    }
    ['drawCanvas']() {
        const _0x1f6aa4 = this['getMapCoord'](), _0x4649b2 = _0x1f6aa4['getLevel']();
        this['clearColor']();
        switch (this.#visibleType) {
        case logi['maps']['DISTRICT_VISIBLETYPE']['POST_ON']:
            _0x4649b2 >= this.#postData['range']['minLevel'] && _0x4649b2 <= this.#postData['range']['maxLevel'] && this.#drawPostData();
            break;
        case logi['maps']['DISTRICT_VISIBLETYPE']['SIDO_ON']:
            _0x4649b2 >= this.#sidoData['range']['minLevel'] && _0x4649b2 <= this.#sidoData['range']['maxLevel'] && this.#drawSidoData();
            break;
        case logi['maps']['DISTRICT_VISIBLETYPE']['SGG_ON']:
            _0x4649b2 >= this.#sggData['range']['minLevel'] && _0x4649b2 <= this.#sggData['range']['maxLevel'] && this.#drawSggData();
            break;
        case logi['maps']['DISTRICT_VISIBLETYPE']['EMD_ON']:
            _0x4649b2 >= this.#emdData['range']['minLevel'] && _0x4649b2 <= this.#emdData['range']['maxLevel'] && this.#drawEmdData();
            break;
        case logi['maps']['DISTRICT_VISIBLETYPE']['POST_HOVER']:
            _0x4649b2 >= this.#postData['hoverRange']['minLevel'] && _0x4649b2 <= this.#postData['hoverRange']['maxLevel'] && this.#drawPostData();
            break;
        case logi['maps']['DISTRICT_VISIBLETYPE']['SIDO_HOVER']:
            _0x4649b2 >= this.#sidoData['hoverRange']['minLevel'] && _0x4649b2 <= this.#sidoData['hoverRange']['maxLevel'] && this.#drawSidoData();
            break;
        case logi['maps']['DISTRICT_VISIBLETYPE']['SGG_HOVER']:
            _0x4649b2 >= this.#sggData['hoverRange']['minLevel'] && _0x4649b2 <= this.#sggData['hoverRange']['maxLevel'] && this.#drawSggData();
            break;
        case logi['maps']['DISTRICT_VISIBLETYPE']['EMD_HOVER']:
            _0x4649b2 >= this.#emdData['hoverRange']['minLevel'] && _0x4649b2 <= this.#emdData['hoverRange']['maxLevel'] && this.#drawEmdData();
            break;
        default:
        }
        this.#drawSearchDistrictData();
        const _0x3ffd92 = [
                this.#postData,
                this.#sidoData,
                this.#sggData,
                this.#emdData
            ], _0x4b4a32 = logi['maps']['Utils']['getCurTick']();
        for (const _0x17c9cb of _0x3ffd92) {
            for (const [_0x71ea53, _0x2bd044] of _0x17c9cb['waitingForResponse']) {
                _0x4b4a32 > _0x2bd044 + _0x17c9cb['MAX_WAITING_TIME'] && _0x17c9cb['waitingForResponse']['delete'](_0x71ea53);
            }
            for (const [_0x547c98, _0x16defe] of _0x17c9cb['notFounds']) {
                _0x4b4a32 > _0x16defe + _0x17c9cb['MAX_NOT_FOUND_TIME'] && _0x17c9cb['notFounds']['delete'](_0x547c98);
            }
        }
        for (const _0x4f4d23 of _0x3ffd92) {
            if (_0x4f4d23['cache']['size'] > _0x4f4d23['MAX_CACHE_SIZE']) {
                let _0x79fb92 = parseInt(_0x4f4d23['MAX_CACHE_SIZE'] * 0.4);
                for (const [_0x260062] of _0x4f4d23['cache']) {
                    if (_0x79fb92 <= 0x0)
                        break;
                    _0x79fb92 -= 0x1, _0x4f4d23['cache']['delete'](_0x260062);
                }
            }
        }
    }
    async #initDatabase(_0x3d2faa) {
        _0x3d2faa = _0x3d2faa ?? '000000R00', console['log']('[logi.maps]\x20vector\x20data\x20version:\x20' + _0x3d2faa);
        try {
            await this.#vtDatabase['initDatabase'](), await this.#vtDatabase['checkDataVersion'](_0x3d2faa), this.#initCache(), this['setUpdateFlag']();
        } catch (_0x56f395) {
            this.#initCache(), this['setUpdateFlag']();
        }
    }
    #getVtDataVersionUrl() {
        if (this.#remoteServerUrl !== '')
            return this.#remoteServerUrl + '/getdataversion';
        return '';
    }
    #getPostIndexAllUrl() {
        if (this.#remoteServerUrl !== '')
            return this.#remoteServerUrl + '/getindexall?datatype=POST_DF2';
        return '';
    }
    #getPostDataUrl(_0x1728a8) {
        if (this.#remoteServerUrl !== '')
            return this.#remoteServerUrl + '/getdata?datatype=POST_DF2&meshidx=' + _0x1728a8;
        return '';
    }
    #getSidoIndexAllUrl() {
        if (this.#remoteServerUrl !== '')
            return this.#remoteServerUrl + '/getindexall?datatype=SIDO_DF2';
        return '';
    }
    #getSidoDataUrl(_0x2e5d0b) {
        if (this.#remoteServerUrl !== '')
            return this.#remoteServerUrl + '/getdata?datatype=SIDO_DF2&meshidx=' + _0x2e5d0b;
        return '';
    }
    #getSggIndexAllUrl() {
        if (this.#remoteServerUrl !== '')
            return this.#remoteServerUrl + '/getindexall?datatype=SGG_DF2';
        return '';
    }
    #getSggDataUrl(_0x3e5aa0) {
        if (this.#remoteServerUrl !== '')
            return this.#remoteServerUrl + '/getdata?datatype=SGG_DF2&meshidx=' + _0x3e5aa0;
        return '';
    }
    #getEmdIndexAllUrl() {
        if (this.#remoteServerUrl !== '')
            return this.#remoteServerUrl + '/getindexall?datatype=EMD_DF2';
        return '';
    }
    #getEmdDataUrl(_0x4a10b7) {
        if (this.#remoteServerUrl !== '')
            return this.#remoteServerUrl + '/getdata?datatype=EMD_DF2&meshidx=' + _0x4a10b7;
        return '';
    }
    #initCache() {
        this.#initPostData(), this.#initSidoData(), this.#initSggData(), this.#initEmdData();
    }
    #toDataStyle(_0x25419e, _0x567547, _0x34ec72) {
        const _0x523d11 = {
            'polygon': null,
            'text': null,
            'textBox': null
        };
        _0x25419e['polygon'] && (_0x523d11['polygon'] = {}, _0x523d11['polygon']['fillColor'] = _0x25419e['polygon']['fillColor'], _0x523d11['polygon']['lineWidth'] = _0x25419e['polygon']['lineWidth'], _0x523d11['polygon']['lineColor'] = _0x25419e['polygon']['lineColor'], _0x25419e['polygon']['dashLength'] != 0x0 || _0x25419e['polygon']['dashSpace'] != 0x0 ? _0x523d11['polygon']['lineDash'] = [
            _0x25419e['polygon']['dashLength'],
            _0x25419e['polygon']['dashSpace']
        ] : _0x523d11['polygon']['lineDash'] = null);
        if (_0x25419e['text']) {
            _0x523d11['text'] = {}, _0x523d11['text']['fontFamily'] = _0x25419e['text']['fontFamily'], _0x523d11['text']['fontBold'] = _0x25419e['text']['fontBold'], _0x523d11['text']['fontColor'] = _0x25419e['text']['fontColor'], _0x523d11['text']['fontSize'] = 0x0;
            if (Array['isArray'](_0x25419e['text']['fontSize']) == ![])
                _0x523d11['text']['fontSize'] = _0x25419e['text']['fontSize'];
            else {
                if (_0x25419e['text']['fontSize']['length'] > 0x0) {
                    const _0x406552 = _0x567547 - _0x34ec72['minLevel'], _0x43e824 = _0x25419e['text']['fontSize']['length'] > _0x406552 ? _0x406552 : _0x25419e['text']['fontSize']['length'] - 0x1;
                    _0x523d11['text']['fontSize'] = _0x25419e['text']['fontSize'][_0x43e824];
                }
            }
        }
        return _0x25419e['textBox'] && (_0x523d11['textBox'] = {}, _0x523d11['textBox']['fillColor'] = _0x25419e['textBox']['fillColor'], _0x523d11['textBox']['lineWidth'] = _0x25419e['textBox']['lineWidth'], _0x523d11['textBox']['lineColor'] = _0x25419e['textBox']['lineColor'], _0x523d11['textBox']['radius'] = _0x25419e['textBox']['radius']), _0x523d11;
    }
    #isHoveringPolygon(_0x443499, _0xd62e95) {
        let _0x2d48b8 = ![];
        for (const _0x350ae1 of _0x443499['polygons']) {
            if (logi['maps']['Utils']['pointInRect'](_0xd62e95['lng'], _0xd62e95['lat'], _0x350ae1['boundaryRect']['xMin'], _0x350ae1['boundaryRect']['yMin'], _0x350ae1['boundaryRect']['xMax'], _0x350ae1['boundaryRect']['yMax']) == !![]) {
                if (this.#latlngInPolygon(_0x350ae1, _0xd62e95) == !![]) {
                    _0x2d48b8 = !![];
                    break;
                }
            }
        }
        return _0x2d48b8;
    }
    #updateScreenCoord(_0x3beb59) {
        const _0x480617 = this['getMapCoord'](), _0x466723 = _0x480617['getLevel'](), _0x1291fe = _0x480617['getTileLevelOffset']();
        _0x3beb59['screenCoord'] == null && (_0x3beb59['screenCoord'] = {
            'tileLevel': null,
            'tileLevelOffset': null,
            'origin': {
                'x': 0x0,
                'y': 0x0
            },
            'polygons': new Array(),
            'centroidPt': {
                'x': 0x0,
                'y': 0x0
            }
        });
        const _0xe4658b = _0x3beb59['screenCoord'];
        if (_0xe4658b['tileLevel'] != _0x466723 || _0xe4658b['tileLevelOffset'] != _0x1291fe) {
            _0xe4658b['tileLevel'] = _0x466723, _0xe4658b['tileLevelOffset'] = _0x1291fe, _0xe4658b['origin'] = {
                'x': 0x0,
                'y': 0x0
            }, _0xe4658b['polygons'] = new Array(), _0xe4658b['centroidPt'] = {
                'x': 0x0,
                'y': 0x0
            };
            for (const _0x59c367 of _0x3beb59['polygons']) {
                _0xe4658b['origin']['x'] == 0x0 && _0xe4658b['origin']['y'] == 0x0 && _0xe4658b['centroidPt']['x'] == 0x0 && _0xe4658b['centroidPt']['y'] == 0x0 && (_0xe4658b['origin'] = _0x480617['world2screen'](_0x59c367['latlngs'][0x0]['lng'], _0x59c367['latlngs'][0x0]['lat']), _0xe4658b['centroidPt'] = _0x480617['world2screen'](_0x3beb59['centroid']['lng'], _0x3beb59['centroid']['lat']), _0xe4658b['centroidPt']['x'] = _0xe4658b['centroidPt']['x'] - _0xe4658b['origin']['x'], _0xe4658b['centroidPt']['y'] = _0xe4658b['centroidPt']['y'] - _0xe4658b['origin']['y']);
                const _0x6381d6 = new Array();
                let _0x3b47ab = {
                        'x': null,
                        'y': null
                    }, _0x96f0e4 = {
                        'x': null,
                        'y': null
                    };
                for (let _0x32c115 of _0x59c367['latlngs']) {
                    _0x3b47ab = _0x480617['world2screen'](_0x32c115['lng'], _0x32c115['lat']), _0x3b47ab['x'] = _0x3b47ab['x'] - _0xe4658b['origin']['x'], _0x3b47ab['y'] = _0x3b47ab['y'] - _0xe4658b['origin']['y'], (_0x96f0e4['x'] != _0x3b47ab['x'] || _0x96f0e4['y'] != _0x3b47ab['y']) && (_0x6381d6['push'](_0x3b47ab), _0x96f0e4 = _0x3b47ab);
                }
                if (_0x6381d6['length'] >= 0x3) {
                    const _0x243f94 = _0x6381d6[0x0], _0x1b22c8 = _0x6381d6[_0x6381d6['length'] - 0x1];
                    (_0x243f94['x'] != _0x1b22c8['x'] || _0x243f94['y'] != _0x1b22c8['y']) && _0x6381d6['push']({
                        'x': _0x243f94['x'],
                        'y': _0x243f94['y']
                    }), _0xe4658b['polygons']['push'](_0x6381d6);
                }
            }
        }
    }
    #drawFill(_0x2dea26, _0x1d9d6a) {
        const _0x573bd7 = _0x1d9d6a['polygon'];
        if (_0x573bd7) {
            const _0x303bb4 = this['getDevicePixelRatio']();
            this.#fillGfx2d['save'](), this.#fillGfx2d['scale'](_0x303bb4, _0x303bb4), this.#fillGfx2d['translate'](_0x2dea26['origin']['x'], _0x2dea26['origin']['y']);
            for (const _0x19cbe2 of _0x2dea26['polygons']) {
                this.#fillGfx2d['drawObjPolygon'](_0x19cbe2, _0x573bd7['fillColor']);
            }
            this.#fillGfx2d['restore']();
        }
    }
    #drawStroke(_0x5f4b26, _0x1eeb1b) {
        const _0x4e61b9 = _0x1eeb1b['polygon'];
        if (_0x4e61b9) {
            const _0x4f409f = this['getDevicePixelRatio']();
            if (_0x4e61b9['lineWidth'] > 0x0) {
                this.#strokeGfx2d['save'](), this.#strokeGfx2d['scale'](_0x4f409f, _0x4f409f), this.#strokeGfx2d['translate'](_0x5f4b26['origin']['x'], _0x5f4b26['origin']['y']);
                for (const _0x2dfe75 of _0x5f4b26['polygons']) {
                    _0x4e61b9['lineDash'] ? this.#strokeGfx2d['drawObjPolyDashedLine'](_0x2dfe75, _0x4e61b9['lineWidth'], _0x4e61b9['lineDash'], _0x4e61b9['lineColor']) : this.#strokeGfx2d['drawObjPolyLine'](_0x2dfe75, _0x4e61b9['lineWidth'], _0x4e61b9['lineColor']);
                }
                this.#strokeGfx2d['restore']();
            }
        }
    }
    #drawText(_0x3e9698, _0x3b1e7d, _0x59f083, _0x4f0fe2) {
        const _0x558a62 = _0x3b1e7d['text'], _0x131b21 = _0x3b1e7d['textBox'];
        if (_0x558a62) {
            const _0x4f9c0f = this['getDevicePixelRatio']();
            if (_0x558a62['fontSize'] > 0x0) {
                this.#textGfx2d['save'](), this.#textGfx2d['scale'](_0x4f9c0f, _0x4f9c0f), this.#textGfx2d['translate'](_0x3e9698['origin']['x'], _0x3e9698['origin']['y']);
                const _0x346865 = {
                    'w': 0x0,
                    'h': 0x0,
                    'padding': 0x0
                };
                if (_0x131b21) {
                    const _0x4ec3a3 = this.#textGfx2d['getTextSize'](_0x59f083, _0x558a62['fontFamily'], _0x558a62['fontSize']);
                    _0x346865['w'] = _0x4ec3a3['width'], _0x346865['h'] = _0x4ec3a3['height'], _0x346865['padding'] = 0x5;
                }
                if (_0x4f0fe2) {
                    const _0x418af2 = {
                        'x': this.#mousePos['x'] - _0x3e9698['origin']['x'],
                        'y': this.#mousePos['y'] - _0x3e9698['origin']['y']
                    };
                    _0x131b21 && this.#textGfx2d['drawObjRoundRect'](_0x418af2['x'], _0x418af2['y'] - _0x346865['h'] * 0.5 - _0x346865['padding'], _0x346865['w'] + _0x346865['padding'] * 0x2, _0x346865['h'] + _0x346865['padding'] * 0x2, _0x131b21['radius'], _0x131b21['fillColor'], _0x131b21['lineWidth'], _0x131b21['lineColor']), this.#textGfx2d['drawObjText'](_0x59f083, _0x418af2['x'] + _0x346865['padding'], _0x418af2['y'], _0x558a62['fontFamily'], _0x558a62['fontSize'], _0x558a62['fontBold'], _0x558a62['fontColor'], 'left-middle');
                } else
                    _0x131b21 && this.#textGfx2d['drawObjRoundRect'](_0x3e9698['centroidPt']['x'] - _0x346865['w'] * 0.5 - _0x346865['padding'], _0x3e9698['centroidPt']['y'] - _0x346865['h'] * 0.5 - _0x346865['padding'], _0x346865['w'] + _0x346865['padding'] * 0x2, _0x346865['h'] + _0x346865['padding'] * 0x2, _0x131b21['radius'], _0x131b21['fillColor'], _0x131b21['lineWidth'], _0x131b21['lineColor']), this.#textGfx2d['drawObjText'](_0x59f083, _0x3e9698['centroidPt']['x'], _0x3e9698['centroidPt']['y'], _0x558a62['fontFamily'], _0x558a62['fontSize'], _0x558a62['fontBold'], _0x558a62['fontColor'], 'center-middle');
                this.#textGfx2d['restore']();
            }
        }
    }
    async #initPostData() {
        if (this.#postData['rects']['length'] == 0x0) {
            const _0x311694 = 'post_index', _0x22d298 = this.#postData['cache']['get'](_0x311694);
            if (_0x22d298)
                this.#updatePostDataRects(_0x22d298);
            else {
                const _0x215fa9 = await this.#vtDatabase['getPostData'](_0x311694);
                if (_0x215fa9?.['dataValue'])
                    this.#updatePostDataRects(_0x215fa9['dataValue']);
                else {
                    const _0x4dc696 = this.#getPostIndexAllUrl(), _0x136e52 = await fetch(_0x4dc696), _0x589f71 = parseInt(_0x136e52['headers']['get']('content-length') ?? 0x3e7), _0xf1fd16 = _0x136e52['status'] >= 0xc8 && _0x136e52['status'] < 0x12c || _0x136e52['status'] == 0x130;
                    if (!_0xf1fd16 || _0x589f71 == 0x0)
                        console['log']('[logi.maps]\x20ERROR:\x20' + _0x136e52['status'] + ',\x20' + _0x136e52['statusText']), this.#postData['waitingForResponse']['delete'](_0x311694), this.#postData['notFounds']['set'](_0x311694, logi['maps']['Utils']['getCurTick']());
                    else {
                        const _0x3f5454 = await _0x136e52['arrayBuffer'](), _0x41901a = new Uint8Array(_0x3f5454);
                        this.#vtDatabase['addPostData'](_0x311694, _0x41901a), this.#updatePostDataRects(_0x41901a);
                    }
                }
            }
        }
    }
    #updatePostDataRects(_0x1f1d52) {
        const _0x136737 = new DataView(_0x1f1d52['buffer']);
        let _0x562d2f = 0x0;
        const _0x35f899 = _0x136737['getUint32'](_0x562d2f, !![]);
        _0x562d2f += 0x4, this.#postData['rects'] = [];
        for (let _0x39761b = 0x0; _0x39761b < _0x35f899; ++_0x39761b) {
            const _0x2b7b00 = _0x136737['getInt32'](_0x562d2f, !![]);
            _0x562d2f += 0x4;
            const _0x52cc95 = _0x136737['getInt32'](_0x562d2f, !![]);
            _0x562d2f += 0x4;
            const _0x9d6ebf = _0x136737['getInt32'](_0x562d2f, !![]);
            _0x562d2f += 0x4;
            const _0x32b923 = _0x136737['getInt32'](_0x562d2f, !![]);
            _0x562d2f += 0x4, this.#postData['rects']['push']({
                'xMin': _0x2b7b00 / 0xf4240,
                'yMin': _0x52cc95 / 0xf4240,
                'xMax': _0x9d6ebf / 0xf4240,
                'yMax': _0x32b923 / 0xf4240
            });
        }
    }
    #getPostIndexListRect(_0x5cb0ed) {
        const _0x1f3fd0 = new Array();
        let _0x2316e8 = 0x0;
        for (let _0x3d857f of this.#postData['rects']) {
            logi['maps']['Utils']['rectOnRect'](_0x5cb0ed['west'], _0x5cb0ed['south'], _0x5cb0ed['east'], _0x5cb0ed['north'], _0x3d857f['xMin'], _0x3d857f['yMin'], _0x3d857f['xMax'], _0x3d857f['yMax']) && _0x1f3fd0['push']('post_' + _0x2316e8), _0x2316e8 += 0x1;
        }
        return _0x1f3fd0;
    }
    #getPostData(_0x1aa45f, _0x158cc4) {
        const _0x1e759e = new DataView(_0x158cc4['buffer']);
        if (_0x1e759e['byteLength'] == 0x0)
            return [];
        else {
            let _0x316bbf = 0x0;
            _0x1e759e['getInt32'](_0x316bbf, !![]), _0x316bbf += 0x4;
            const _0x48329e = _0x1e759e['getInt32'](_0x316bbf, !![]);
            _0x316bbf += 0x4;
            const _0x728027 = new Array();
            let _0xf6707a, _0x5a7757, _0x1b6f1c, _0x554915, _0x25011a;
            for (let _0x228527 = 0x0; _0x228527 < _0x48329e; ++_0x228527) {
                _0xf6707a = _0x1e759e['getInt32'](_0x316bbf, !![]), _0x316bbf += 0x4, _0x1b6f1c = String(_0xf6707a)['padStart'](0x5, '0'), _0x554915 = _0x1e759e['getInt32'](_0x316bbf, !![]), _0x316bbf += 0x4;
                const _0xce6ab1 = {
                    'xMin': 0xb4,
                    'yMin': 0x5a,
                    'xMax': -0xb4,
                    'yMax': -0x5a
                };
                _0x25011a = new Array();
                for (let _0xcf2c98 = 0x0; _0xcf2c98 < _0x554915; ++_0xcf2c98) {
                    const _0x4d319c = _0x1e759e['getInt32'](_0x316bbf, !![]);
                    _0x316bbf += 0x4;
                    const _0x46335a = _0x1e759e['getInt32'](_0x316bbf, !![]);
                    _0x316bbf += 0x4;
                    const _0x121d39 = _0x4d319c / 0xf4240, _0x434fcb = _0x46335a / 0xf4240;
                    _0x25011a['push']({
                        'lat': _0x434fcb,
                        'lng': _0x121d39
                    }), _0xce6ab1['xMin'] = Math['min'](_0xce6ab1['xMin'], _0x121d39), _0xce6ab1['yMin'] = Math['min'](_0xce6ab1['yMin'], _0x434fcb), _0xce6ab1['xMax'] = Math['max'](_0xce6ab1['xMax'], _0x121d39), _0xce6ab1['yMax'] = Math['max'](_0xce6ab1['yMax'], _0x434fcb);
                }
                _0x5a7757 = logi['maps']['Utils']['findLatLngCentroid'](_0x25011a), _0x728027['push'](new DistrictArea(_0xf6707a, _0x5a7757, _0x1b6f1c, [{
                        'latlngs': _0x25011a,
                        'boundaryRect': {
                            'xMin': _0xce6ab1['xMin'],
                            'yMin': _0xce6ab1['yMin'],
                            'xMax': _0xce6ab1['xMax'],
                            'yMax': _0xce6ab1['yMax']
                        }
                    }], {
                    'xMin': _0xce6ab1['xMin'],
                    'yMin': _0xce6ab1['yMin'],
                    'xMax': _0xce6ab1['xMax'],
                    'yMax': _0xce6ab1['yMax']
                }));
            }
            return {
                'meshId': _0x1aa45f,
                'areas': _0x728027
            };
        }
    }
    async #drawPostData() {
        const _0x5c12e2 = this['getMapCoord'](), _0x356c1f = _0x5c12e2['getMapRect'](), _0x4a6186 = this.#getPostIndexListRect(_0x356c1f), _0x14f30a = new Array();
        for (let _0x150a33 of _0x4a6186) {
            const _0x528bbf = this.#postData['cache']['get'](_0x150a33);
            if (_0x528bbf)
                this.#drawPostDataOnCanvas(_0x528bbf);
            else {
                if (this.#postData['waitingForResponse']['size'] >= this.#postData['MAX_WAITING_SIZE'])
                    console['log']('[logi.maps]\x20postDataWaitingForResponse\x20size\x20is\x20full.');
                else {
                    const _0x58243d = this.#postData['waitingForResponse']['get'](_0x150a33), _0x9ea1e3 = this.#postData['notFounds']['get'](_0x150a33);
                    !_0x58243d && !_0x9ea1e3 && (this.#postData['waitingForResponse']['set'](_0x150a33, logi['maps']['Utils']['getCurTick']()), _0x14f30a['push'](_0x150a33));
                }
            }
        }
        const _0xb35034 = await this.#vtDatabase['getPostDatas'](_0x14f30a);
        for (let _0x3ccdec of _0xb35034) {
            if (_0x3ccdec['dataValue']) {
                const _0x56b0a4 = this.#getPostData(_0x3ccdec['dataKey'], _0x3ccdec['dataValue']);
                this.#drawPostDataOnCanvas(_0x56b0a4), this.#postData['cache']['set'](_0x3ccdec['dataKey'], _0x56b0a4), this.#postData['waitingForResponse']['delete'](_0x3ccdec['dataKey']);
            } else {
                const _0x153099 = _0x3ccdec['dataKey']['split']('_');
                if (_0x153099['length'] < 0x2)
                    console['log']('vector\x20data\x20id\x20is\x20invalid.');
                else {
                    const _0x1472c9 = _0x153099[0x1], _0x3632ce = this.#getPostDataUrl(_0x1472c9), _0x51270b = await fetch(_0x3632ce), _0x15878c = parseInt(_0x51270b['headers']['get']('content-length') ?? 0x3e7), _0x59fea3 = _0x51270b['status'] >= 0xc8 && _0x51270b['status'] < 0x12c || _0x51270b['status'] == 0x130;
                    if (!_0x59fea3 || _0x15878c == 0x0)
                        console['log']('[logi.maps]\x20ERROR:\x20' + _0x51270b['status'] + ',\x20' + _0x51270b['statusText']), this.#postData['waitingForResponse']['delete'](_0x3ccdec['dataKey']), this.#postData['notFounds']['set'](_0x3ccdec['dataKey'], logi['maps']['Utils']['getCurTick']());
                    else {
                        const _0x1f17c6 = await _0x51270b['arrayBuffer'](), _0x2e0231 = new Uint8Array(_0x1f17c6);
                        this.#vtDatabase['addPostData'](_0x3ccdec['dataKey'], _0x2e0231);
                        const _0x2d7e19 = this.#getPostData(_0x3ccdec['dataKey'], _0x2e0231);
                        this.#drawPostDataOnCanvas(_0x2d7e19), this.#postData['cache']['set'](_0x3ccdec['dataKey'], _0x2d7e19), this.#postData['waitingForResponse']['delete'](_0x3ccdec['dataKey']);
                    }
                }
            }
        }
    }
    #drawPostDataOnCanvas(_0xe59e92) {
        const _0x2864d6 = this['getMapCoord'](), _0x5a5a8c = _0x2864d6['getMapRect'](), _0x5ecc3b = _0x2864d6['getLevel'](), _0x1ec6dc = this.#visibleType == logi['maps']['DISTRICT_VISIBLETYPE']['POST_HOVER'];
        let _0x5c5c30 = null, _0x339d52 = null;
        if (_0x1ec6dc) {
            if (this.#mousePos['x'] == null || this.#mousePos['y'] == null) {
                this.#hoverDistrict['currentRaw'] = null;
                return;
            }
            _0x5c5c30 = this.#toDataStyle(this.#postData['hoverStyle'], _0x5ecc3b, this.#postData['range']), _0x339d52 = _0x2864d6['screen2world'](this.#mousePos['x'], this.#mousePos['y']);
        } else
            _0x5c5c30 = this.#toDataStyle(this.#postData['style'], _0x5ecc3b, this.#postData['range']);
        for (const _0x248255 of _0xe59e92['areas']) {
            if (!logi['maps']['Utils']['rectOnMapRect'](_0x248255['boundaryRect'], _0x5a5a8c))
                continue;
            if (_0x1ec6dc) {
                if (this.#isHoveringPolygon(_0x248255, _0x339d52) == ![])
                    continue;
                this.#hoverDistrict['currentRaw'] = _0x248255;
            }
            this.#updateScreenCoord(_0x248255);
            const _0x19489e = _0x248255['screenCoord'];
            _0x19489e['polygons']['length'] > 0x0 && (_0x19489e['origin'] = _0x2864d6['world2screen'](_0x248255['polygons'][0x0]['latlngs'][0x0]['lng'], _0x248255['polygons'][0x0]['latlngs'][0x0]['lat']), this.#drawFill(_0x19489e, _0x5c5c30), this.#drawStroke(_0x19489e, _0x5c5c30), this.#drawText(_0x19489e, _0x5c5c30, _0x248255['name'], _0x1ec6dc));
            if (_0x1ec6dc)
                break;
        }
    }
    async #initSidoData() {
        if (this.#sidoData['rects']['length'] == 0x0) {
            const _0x423ddd = 'sido_index', _0x426d4a = this.#sidoData['cache']['get'](_0x423ddd);
            if (_0x426d4a)
                this.#updateSidoDataRects(_0x426d4a);
            else {
                const _0x2a1b98 = await this.#vtDatabase['getSidoData'](_0x423ddd);
                if (_0x2a1b98?.['dataValue'])
                    this.#updateSidoDataRects(_0x2a1b98['dataValue']);
                else {
                    const _0x45c59e = this.#getSidoIndexAllUrl(), _0x24d4c7 = await fetch(_0x45c59e), _0x31ee2d = parseInt(_0x24d4c7['headers']['get']('content-length') ?? 0x3e7), _0x51e93e = _0x24d4c7['status'] >= 0xc8 && _0x24d4c7['status'] < 0x12c || _0x24d4c7['status'] == 0x130;
                    if (!_0x51e93e || _0x31ee2d == 0x0)
                        console['log']('[logi.maps]\x20ERROR:\x20' + _0x24d4c7['status'] + ',\x20' + _0x24d4c7['statusText']), this.#sidoData['waitingForResponse']['delete'](_0x423ddd), this.#sidoData['notFounds']['set'](_0x423ddd, logi['maps']['Utils']['getCurTick']());
                    else {
                        const _0x4c1733 = await _0x24d4c7['arrayBuffer'](), _0x4a99e5 = new Uint8Array(_0x4c1733);
                        this.#vtDatabase['addSidoData'](_0x423ddd, _0x4a99e5), this.#updateSidoDataRects(_0x4a99e5);
                    }
                }
            }
        }
    }
    #updateSidoDataRects(_0x398c31) {
        const _0x323036 = new DataView(_0x398c31['buffer']);
        let _0x4ec4a0 = 0x0;
        const _0x18149a = _0x323036['getUint32'](_0x4ec4a0, !![]);
        _0x4ec4a0 += 0x4, this.#sidoData['rects'] = [];
        for (let _0xf43b54 = 0x0; _0xf43b54 < _0x18149a; ++_0xf43b54) {
            const _0x36262e = _0x323036['getInt32'](_0x4ec4a0, !![]);
            _0x4ec4a0 += 0x4;
            const _0x455556 = _0x323036['getInt32'](_0x4ec4a0, !![]);
            _0x4ec4a0 += 0x4;
            const _0x2139dd = _0x323036['getInt32'](_0x4ec4a0, !![]);
            _0x4ec4a0 += 0x4;
            const _0x49d706 = _0x323036['getInt32'](_0x4ec4a0, !![]);
            _0x4ec4a0 += 0x4, this.#sidoData['rects']['push']({
                'xMin': _0x36262e / 0xf4240,
                'yMin': _0x455556 / 0xf4240,
                'xMax': _0x2139dd / 0xf4240,
                'yMax': _0x49d706 / 0xf4240
            });
        }
    }
    #getSidoIndexListRect(_0x13175a) {
        const _0x59f9fa = new Array();
        let _0x2bf35d = 0x0;
        for (let _0xa33ffb of this.#sidoData['rects']) {
            logi['maps']['Utils']['rectOnRect'](_0x13175a['west'], _0x13175a['south'], _0x13175a['east'], _0x13175a['north'], _0xa33ffb['xMin'], _0xa33ffb['yMin'], _0xa33ffb['xMax'], _0xa33ffb['yMax']) && _0x59f9fa['push']('sido_' + _0x2bf35d), _0x2bf35d += 0x1;
        }
        return _0x59f9fa;
    }
    #getSidoData(_0x22cc8d, _0x32e180) {
        const _0x52dc9f = new DataView(_0x32e180['buffer']);
        if (_0x52dc9f['byteLength'] == 0x0)
            return [];
        else {
            let _0x1406a8 = 0x0;
            _0x52dc9f['getInt32'](_0x1406a8, !![]), _0x1406a8 += 0x4;
            const _0x389f26 = _0x52dc9f['getInt32'](_0x1406a8, !![]);
            _0x1406a8 += 0x4;
            const _0x3a483f = new Array();
            let _0x4f9ae5, _0x21f716, _0x234d5a, _0x40949d;
            for (let _0x1a9833 = 0x0; _0x1a9833 < _0x389f26; ++_0x1a9833) {
                _0x4f9ae5 = _0x52dc9f['getBigInt64'](_0x1406a8, !![]), _0x1406a8 += 0x8;
                {
                    const _0xb4dc2c = _0x52dc9f['getInt32'](_0x1406a8, !![]);
                    _0x1406a8 += 0x4;
                    const _0x4e8486 = _0x52dc9f['getInt32'](_0x1406a8, !![]);
                    _0x1406a8 += 0x4, _0x21f716 = {
                        'lat': _0x4e8486 / 0xf4240,
                        'lng': _0xb4dc2c / 0xf4240
                    };
                }
                const _0x5a2271 = new Uint8Array(_0x32e180['buffer'], _0x1406a8, 0x40);
                _0x234d5a = new TextDecoder('euc-kr')['decode'](_0x5a2271)['replace'](/\0/g, '')['trim'](), _0x1406a8 += 0x40;
                const _0x36bb13 = _0x52dc9f['getInt32'](_0x1406a8, !![]);
                _0x1406a8 += 0x4, _0x40949d = new Array();
                let _0x33f643 = new Array();
                const _0x38444c = {
                        'xMin': 0xb4,
                        'yMin': 0x5a,
                        'xMax': -0xb4,
                        'yMax': -0x5a
                    }, _0x4063a7 = {
                        'xMin': 0xb4,
                        'yMin': 0x5a,
                        'xMax': -0xb4,
                        'yMax': -0x5a
                    };
                let _0x31150b = undefined, _0x56997d = undefined;
                for (let _0x59fdf1 = 0x0; _0x59fdf1 < _0x36bb13; ++_0x59fdf1) {
                    const _0x6304c5 = _0x52dc9f['getInt32'](_0x1406a8, !![]);
                    _0x1406a8 += 0x4;
                    const _0x4366e0 = _0x52dc9f['getInt32'](_0x1406a8, !![]);
                    _0x1406a8 += 0x4;
                    if (_0x6304c5 == _0x31150b && _0x4366e0 == _0x56997d)
                        _0x33f643['length'] >= 0x3 && _0x40949d['push']({
                            'latlngs': _0x33f643,
                            'boundaryRect': {
                                'xMin': _0x4063a7['xMin'],
                                'yMin': _0x4063a7['yMin'],
                                'xMax': _0x4063a7['xMax'],
                                'yMax': _0x4063a7['yMax']
                            }
                        }), _0x33f643 = new Array(), _0x4063a7['xMin'] = 0xb4, _0x4063a7['yMin'] = 0x5a, _0x4063a7['xMax'] = -0xb4, _0x4063a7['yMax'] = -0x5a, _0x31150b = undefined, _0x56997d = undefined;
                    else {
                        const _0x443aaa = _0x6304c5 / 0xf4240, _0x418416 = _0x4366e0 / 0xf4240;
                        _0x33f643['push']({
                            'lat': _0x418416,
                            'lng': _0x443aaa
                        }), _0x4063a7['xMin'] = Math['min'](_0x4063a7['xMin'], _0x443aaa), _0x4063a7['yMin'] = Math['min'](_0x4063a7['yMin'], _0x418416), _0x4063a7['xMax'] = Math['max'](_0x4063a7['xMax'], _0x443aaa), _0x4063a7['yMax'] = Math['max'](_0x4063a7['yMax'], _0x418416), _0x38444c['xMin'] = Math['min'](_0x38444c['xMin'], _0x443aaa), _0x38444c['yMin'] = Math['min'](_0x38444c['yMin'], _0x418416), _0x38444c['xMax'] = Math['max'](_0x38444c['xMax'], _0x443aaa), _0x38444c['yMax'] = Math['max'](_0x38444c['yMax'], _0x418416), _0x31150b = _0x6304c5, _0x56997d = _0x4366e0;
                    }
                }
                _0x33f643['length'] >= 0x3 && _0x40949d['push']({
                    'latlngs': _0x33f643,
                    'boundaryRect': {
                        'xMin': _0x4063a7['xMin'],
                        'yMin': _0x4063a7['yMin'],
                        'xMax': _0x4063a7['xMax'],
                        'yMax': _0x4063a7['yMax']
                    }
                }), _0x3a483f['push'](new DistrictArea(_0x4f9ae5, _0x21f716, _0x234d5a, _0x40949d, {
                    'xMin': _0x38444c['xMin'],
                    'yMin': _0x38444c['yMin'],
                    'xMax': _0x38444c['xMax'],
                    'yMax': _0x38444c['yMax']
                }));
            }
            return {
                'meshId': _0x22cc8d,
                'areas': _0x3a483f
            };
        }
    }
    async #drawSidoData() {
        const _0x8f202f = this['getMapCoord'](), _0x3fabe5 = _0x8f202f['getMapRect'](), _0x51d1b6 = this.#getSidoIndexListRect(_0x3fabe5), _0x15b482 = new Array();
        for (let _0x1eab71 of _0x51d1b6) {
            const _0x52db02 = this.#sidoData['cache']['get'](_0x1eab71);
            if (_0x52db02)
                this.#drawSidoDataOnCanvas(_0x52db02);
            else {
                if (this.#sidoData['waitingForResponse']['size'] >= this.#sidoData['MAX_WAITING_SIZE'])
                    console['log']('[logi.maps]\x20sidoDataWaitingForResponse\x20size\x20is\x20full.');
                else {
                    const _0x5a819f = this.#sidoData['waitingForResponse']['get'](_0x1eab71), _0x568960 = this.#sidoData['notFounds']['get'](_0x1eab71);
                    !_0x5a819f && !_0x568960 && (this.#sidoData['waitingForResponse']['set'](_0x1eab71, logi['maps']['Utils']['getCurTick']()), _0x15b482['push'](_0x1eab71));
                }
            }
        }
        const _0x144b4b = await this.#vtDatabase['getSidoDatas'](_0x15b482);
        for (let _0x319e7f of _0x144b4b) {
            if (_0x319e7f['dataValue']) {
                const _0x2d63df = this.#getSidoData(_0x319e7f['dataKey'], _0x319e7f['dataValue']);
                this.#drawSidoDataOnCanvas(_0x2d63df), this.#sidoData['cache']['set'](_0x319e7f['dataKey'], _0x2d63df), this.#sidoData['waitingForResponse']['delete'](_0x319e7f['dataKey']);
            } else {
                const _0x82a66 = _0x319e7f['dataKey']['split']('_');
                if (_0x82a66['length'] < 0x2)
                    console['log']('vector\x20data\x20id\x20is\x20invalid.');
                else {
                    const _0x4d66bc = _0x82a66[0x1], _0x40ed46 = this.#getSidoDataUrl(_0x4d66bc), _0x482dce = await fetch(_0x40ed46), _0x50e038 = parseInt(_0x482dce['headers']['get']('content-length') ?? 0x3e7), _0x1bbaba = _0x482dce['status'] >= 0xc8 && _0x482dce['status'] < 0x12c || _0x482dce['status'] == 0x130;
                    if (!_0x1bbaba || _0x50e038 == 0x0)
                        console['log']('[logi.maps]\x20ERROR:\x20' + _0x482dce['status'] + ',\x20' + _0x482dce['statusText']), this.#sidoData['waitingForResponse']['delete'](_0x319e7f['dataKey']), this.#sidoData['notFounds']['set'](_0x319e7f['dataKey'], logi['maps']['Utils']['getCurTick']());
                    else {
                        const _0xef21e2 = await _0x482dce['arrayBuffer'](), _0x922b27 = new Uint8Array(_0xef21e2);
                        this.#vtDatabase['addSidoData'](_0x319e7f['dataKey'], _0x922b27);
                        const _0x240a79 = this.#getSidoData(_0x319e7f['dataKey'], _0x922b27);
                        this.#drawSidoDataOnCanvas(_0x240a79), this.#sidoData['cache']['set'](_0x319e7f['dataKey'], _0x240a79), this.#sidoData['waitingForResponse']['delete'](_0x319e7f['dataKey']);
                    }
                }
            }
        }
    }
    #drawSidoDataOnCanvas(_0x5d4e4f) {
        const _0x33a8a9 = this['getMapCoord'](), _0x506cfb = _0x33a8a9['getMapRect'](), _0x44c870 = _0x33a8a9['getLevel'](), _0x3850d9 = this.#visibleType == logi['maps']['DISTRICT_VISIBLETYPE']['SIDO_HOVER'];
        let _0x397e68 = null, _0x54d6aa = null;
        if (_0x3850d9) {
            if (this.#mousePos['x'] == null || this.#mousePos['y'] == null) {
                this.#hoverDistrict['currentRaw'] = null;
                return;
            }
            _0x397e68 = this.#toDataStyle(this.#sidoData['hoverStyle'], _0x44c870, this.#sidoData['range']), _0x54d6aa = _0x33a8a9['screen2world'](this.#mousePos['x'], this.#mousePos['y']);
        } else
            _0x397e68 = this.#toDataStyle(this.#sidoData['style'], _0x44c870, this.#sidoData['range']);
        for (const _0x2d5c0d of _0x5d4e4f['areas']) {
            if (!logi['maps']['Utils']['rectOnMapRect'](_0x2d5c0d['boundaryRect'], _0x506cfb))
                continue;
            if (_0x3850d9) {
                if (this.#isHoveringPolygon(_0x2d5c0d, _0x54d6aa) == ![])
                    continue;
                this.#hoverDistrict['currentRaw'] = _0x2d5c0d;
            }
            this.#updateScreenCoord(_0x2d5c0d);
            const _0x339c4d = _0x2d5c0d['screenCoord'];
            _0x339c4d['polygons']['length'] > 0x0 && (_0x339c4d['origin'] = _0x33a8a9['world2screen'](_0x2d5c0d['polygons'][0x0]['latlngs'][0x0]['lng'], _0x2d5c0d['polygons'][0x0]['latlngs'][0x0]['lat']), this.#drawFill(_0x339c4d, _0x397e68), this.#drawStroke(_0x339c4d, _0x397e68), this.#drawText(_0x339c4d, _0x397e68, _0x2d5c0d['name'], _0x3850d9));
            if (_0x3850d9)
                break;
        }
    }
    async #searchSidoData(_0x21aef8, _0x3b5a61) {
        const _0x3ccf24 = this.#getSidoIndexListRect(_0x3b5a61), _0x205e1a = new Array();
        let _0x2b4bc5 = null;
        for (let _0x3fe922 of _0x3ccf24) {
            if (_0x2b4bc5 == null) {
                const _0x3a5760 = this.#sidoData['cache']['get'](_0x3fe922);
                if (_0x3a5760)
                    _0x2b4bc5 = this.#findSidoData(_0x21aef8, _0x3a5760);
                else {
                    if (this.#sidoData['waitingForResponse']['size'] >= this.#sidoData['MAX_WAITING_SIZE'])
                        console['log']('[logi.maps]\x20sidoDataWaitingForResponse\x20size\x20is\x20full.');
                    else {
                        const _0x5d7e7a = this.#sidoData['waitingForResponse']['get'](_0x3fe922), _0x55a816 = this.#sidoData['notFounds']['get'](_0x3fe922);
                        !_0x5d7e7a && !_0x55a816 && (this.#sidoData['waitingForResponse']['set'](_0x3fe922, logi['maps']['Utils']['getCurTick']()), _0x205e1a['push'](_0x3fe922));
                    }
                }
            }
        }
        const _0x1094ed = await this.#vtDatabase['getSidoDatas'](_0x205e1a);
        for (let _0x2b4003 of _0x1094ed) {
            if (_0x2b4bc5 != null)
                this.#sidoData['waitingForResponse']['delete'](_0x2b4003['dataKey']);
            else {
                if (_0x2b4003['dataValue']) {
                    const _0x162c4e = this.#getSidoData(_0x2b4003['dataKey'], _0x2b4003['dataValue']);
                    _0x2b4bc5 = this.#findSidoData(_0x21aef8, _0x162c4e), this.#sidoData['cache']['set'](_0x2b4003['dataKey'], _0x162c4e), this.#sidoData['waitingForResponse']['delete'](_0x2b4003['dataKey']);
                } else {
                    const _0x5b638b = _0x2b4003['dataKey']['split']('_');
                    if (_0x5b638b['length'] < 0x2)
                        console['log']('vector\x20data\x20id\x20is\x20invalid.');
                    else {
                        const _0x195148 = _0x5b638b[0x1], _0x5957ac = this.#getSidoDataUrl(_0x195148), _0x532162 = await fetch(_0x5957ac), _0x1e437b = parseInt(_0x532162['headers']['get']('content-length') ?? 0x3e7), _0xa747be = _0x532162['status'] >= 0xc8 && _0x532162['status'] < 0x12c || _0x532162['status'] == 0x130;
                        if (!_0xa747be || _0x1e437b == 0x0)
                            console['log']('[logi.maps]\x20ERROR:\x20' + _0x532162['status'] + ',\x20' + _0x532162['statusText']), this.#sidoData['waitingForResponse']['delete'](_0x2b4003['dataKey']), this.#sidoData['notFounds']['set'](_0x2b4003['dataKey'], logi['maps']['Utils']['getCurTick']());
                        else {
                            const _0x49c380 = await _0x532162['arrayBuffer'](), _0x24a2d9 = new Uint8Array(_0x49c380);
                            this.#vtDatabase['addSidoData'](_0x2b4003['dataKey'], _0x24a2d9);
                            const _0x17d0f6 = this.#getSidoData(_0x2b4003['dataKey'], _0x24a2d9);
                            _0x2b4bc5 = this.#findSidoData(_0x21aef8, _0x17d0f6), this.#sidoData['cache']['set'](_0x2b4003['dataKey'], _0x17d0f6), this.#sidoData['waitingForResponse']['delete'](_0x2b4003['dataKey']);
                        }
                    }
                }
            }
        }
        return _0x2b4bc5;
    }
    #findSidoData(_0x28fbb6, _0x2de4b3) {
        const _0x1628a2 = BigInt(_0x28fbb6);
        for (const _0xb74d39 of _0x2de4b3['areas']) {
            if (_0xb74d39['code'] == _0x1628a2)
                return _0xb74d39;
        }
        return null;
    }
    async #initSggData() {
        if (this.#sggData['rects']['length'] == 0x0) {
            const _0x186f7a = 'sgg_index', _0x7e48d2 = this.#sggData['cache']['get'](_0x186f7a);
            if (_0x7e48d2)
                this.#updateSggDataRects(_0x7e48d2);
            else {
                const _0x141545 = await this.#vtDatabase['getSggData'](_0x186f7a);
                if (_0x141545?.['dataValue'])
                    this.#updateSggDataRects(_0x141545['dataValue']);
                else {
                    const _0x388d5c = this.#getSggIndexAllUrl(), _0x5441a1 = await fetch(_0x388d5c), _0x18775b = parseInt(_0x5441a1['headers']['get']('content-length') ?? 0x3e7), _0x2b2bf7 = _0x5441a1['status'] >= 0xc8 && _0x5441a1['status'] < 0x12c || _0x5441a1['status'] == 0x130;
                    if (!_0x2b2bf7 || _0x18775b == 0x0)
                        console['log']('[logi.maps]\x20ERROR:\x20' + _0x5441a1['status'] + ',\x20' + _0x5441a1['statusText']), this.#sggData['waitingForResponse']['delete'](_0x186f7a), this.#sggData['notFounds']['set'](_0x186f7a, logi['maps']['Utils']['getCurTick']());
                    else {
                        const _0x5c3fde = await _0x5441a1['arrayBuffer'](), _0x35c718 = new Uint8Array(_0x5c3fde);
                        this.#vtDatabase['addSggData'](_0x186f7a, _0x35c718), this.#updateSggDataRects(_0x35c718);
                    }
                }
            }
        }
    }
    #updateSggDataRects(_0xc15dca) {
        const _0x465a3c = new DataView(_0xc15dca['buffer']);
        let _0x2b48b9 = 0x0;
        const _0x11adca = _0x465a3c['getUint32'](_0x2b48b9, !![]);
        _0x2b48b9 += 0x4, this.#sggData['rects'] = [];
        for (let _0x5e5973 = 0x0; _0x5e5973 < _0x11adca; ++_0x5e5973) {
            const _0x508139 = _0x465a3c['getInt32'](_0x2b48b9, !![]);
            _0x2b48b9 += 0x4;
            const _0xcce792 = _0x465a3c['getInt32'](_0x2b48b9, !![]);
            _0x2b48b9 += 0x4;
            const _0x47b10b = _0x465a3c['getInt32'](_0x2b48b9, !![]);
            _0x2b48b9 += 0x4;
            const _0x2fe8cf = _0x465a3c['getInt32'](_0x2b48b9, !![]);
            _0x2b48b9 += 0x4, this.#sggData['rects']['push']({
                'xMin': _0x508139 / 0xf4240,
                'yMin': _0xcce792 / 0xf4240,
                'xMax': _0x47b10b / 0xf4240,
                'yMax': _0x2fe8cf / 0xf4240
            });
        }
    }
    #getSggIndexListRect(_0x272a89) {
        const _0x26d98b = new Array();
        let _0x28db58 = 0x0;
        for (let _0x50523a of this.#sggData['rects']) {
            logi['maps']['Utils']['rectOnRect'](_0x272a89['west'], _0x272a89['south'], _0x272a89['east'], _0x272a89['north'], _0x50523a['xMin'], _0x50523a['yMin'], _0x50523a['xMax'], _0x50523a['yMax']) && _0x26d98b['push']('sgg_' + _0x28db58), _0x28db58 += 0x1;
        }
        return _0x26d98b;
    }
    #getSggData(_0x2e791d, _0x3a1cbe) {
        const _0x38299d = new DataView(_0x3a1cbe['buffer']);
        if (_0x38299d['byteLength'] == 0x0)
            return [];
        else {
            let _0x40b9a1 = 0x0;
            _0x38299d['getInt32'](_0x40b9a1, !![]), _0x40b9a1 += 0x4;
            const _0xb9e0bf = _0x38299d['getInt32'](_0x40b9a1, !![]);
            _0x40b9a1 += 0x4;
            const _0x9f2b4d = new Array();
            let _0x43babd, _0x44a6db, _0x2db7e6, _0x225c6d;
            for (let _0x81c2f8 = 0x0; _0x81c2f8 < _0xb9e0bf; ++_0x81c2f8) {
                _0x43babd = _0x38299d['getBigInt64'](_0x40b9a1, !![]), _0x40b9a1 += 0x8;
                {
                    const _0x399007 = _0x38299d['getInt32'](_0x40b9a1, !![]);
                    _0x40b9a1 += 0x4;
                    const _0x1f7bc2 = _0x38299d['getInt32'](_0x40b9a1, !![]);
                    _0x40b9a1 += 0x4, _0x44a6db = {
                        'lat': _0x1f7bc2 / 0xf4240,
                        'lng': _0x399007 / 0xf4240
                    };
                }
                const _0x46f4b6 = new Uint8Array(_0x3a1cbe['buffer'], _0x40b9a1, 0x40);
                _0x2db7e6 = new TextDecoder('euc-kr')['decode'](_0x46f4b6)['replace'](/\0/g, '')['trim'](), _0x40b9a1 += 0x40;
                const _0x475a21 = _0x38299d['getInt32'](_0x40b9a1, !![]);
                _0x40b9a1 += 0x4, _0x225c6d = new Array();
                let _0x5db5db = new Array();
                const _0x59b2e3 = {
                        'xMin': 0xb4,
                        'yMin': 0x5a,
                        'xMax': -0xb4,
                        'yMax': -0x5a
                    }, _0x129dae = {
                        'xMin': 0xb4,
                        'yMin': 0x5a,
                        'xMax': -0xb4,
                        'yMax': -0x5a
                    };
                let _0x5bcdd8 = undefined, _0x59072a = undefined;
                for (let _0x515ebd = 0x0; _0x515ebd < _0x475a21; ++_0x515ebd) {
                    const _0x234bb0 = _0x38299d['getInt32'](_0x40b9a1, !![]);
                    _0x40b9a1 += 0x4;
                    const _0x79c707 = _0x38299d['getInt32'](_0x40b9a1, !![]);
                    _0x40b9a1 += 0x4;
                    if (_0x234bb0 == _0x5bcdd8 && _0x79c707 == _0x59072a)
                        _0x5db5db['length'] >= 0x3 && _0x225c6d['push']({
                            'latlngs': _0x5db5db,
                            'boundaryRect': {
                                'xMin': _0x129dae['xMin'],
                                'yMin': _0x129dae['yMin'],
                                'xMax': _0x129dae['xMax'],
                                'yMax': _0x129dae['yMax']
                            }
                        }), _0x5db5db = new Array(), _0x129dae['xMin'] = 0xb4, _0x129dae['yMin'] = 0x5a, _0x129dae['xMax'] = -0xb4, _0x129dae['yMax'] = -0x5a, _0x5bcdd8 = undefined, _0x59072a = undefined;
                    else {
                        const _0x2fcdc0 = _0x234bb0 / 0xf4240, _0x45ce3e = _0x79c707 / 0xf4240;
                        _0x5db5db['push']({
                            'lat': _0x45ce3e,
                            'lng': _0x2fcdc0
                        }), _0x129dae['xMin'] = Math['min'](_0x129dae['xMin'], _0x2fcdc0), _0x129dae['yMin'] = Math['min'](_0x129dae['yMin'], _0x45ce3e), _0x129dae['xMax'] = Math['max'](_0x129dae['xMax'], _0x2fcdc0), _0x129dae['yMax'] = Math['max'](_0x129dae['yMax'], _0x45ce3e), _0x59b2e3['xMin'] = Math['min'](_0x59b2e3['xMin'], _0x2fcdc0), _0x59b2e3['yMin'] = Math['min'](_0x59b2e3['yMin'], _0x45ce3e), _0x59b2e3['xMax'] = Math['max'](_0x59b2e3['xMax'], _0x2fcdc0), _0x59b2e3['yMax'] = Math['max'](_0x59b2e3['yMax'], _0x45ce3e), _0x5bcdd8 = _0x234bb0, _0x59072a = _0x79c707;
                    }
                }
                _0x5db5db['length'] >= 0x3 && _0x225c6d['push']({
                    'latlngs': _0x5db5db,
                    'boundaryRect': {
                        'xMin': _0x129dae['xMin'],
                        'yMin': _0x129dae['yMin'],
                        'xMax': _0x129dae['xMax'],
                        'yMax': _0x129dae['yMax']
                    }
                }), _0x9f2b4d['push'](new DistrictArea(_0x43babd, _0x44a6db, _0x2db7e6, _0x225c6d, {
                    'xMin': _0x59b2e3['xMin'],
                    'yMin': _0x59b2e3['yMin'],
                    'xMax': _0x59b2e3['xMax'],
                    'yMax': _0x59b2e3['yMax']
                }));
            }
            return {
                'meshId': _0x2e791d,
                'areas': _0x9f2b4d
            };
        }
    }
    async #drawSggData() {
        const _0x231c7f = this['getMapCoord'](), _0x2c7b86 = _0x231c7f['getMapRect'](), _0x16a1de = this.#getSggIndexListRect(_0x2c7b86), _0x112171 = new Array();
        for (let _0x56d4f9 of _0x16a1de) {
            const _0x74b0cf = this.#sggData['cache']['get'](_0x56d4f9);
            if (_0x74b0cf)
                this.#drawSggDataOnCanvas(_0x74b0cf);
            else {
                if (this.#sggData['waitingForResponse']['size'] >= this.#sggData['MAX_WAITING_SIZE'])
                    console['log']('[logi.maps]\x20sggDataWaitingForResponse\x20size\x20is\x20full.');
                else {
                    const _0x117c9e = this.#sggData['waitingForResponse']['get'](_0x56d4f9), _0x3a3060 = this.#sggData['notFounds']['get'](_0x56d4f9);
                    !_0x117c9e && !_0x3a3060 && (this.#sggData['waitingForResponse']['set'](_0x56d4f9, logi['maps']['Utils']['getCurTick']()), _0x112171['push'](_0x56d4f9));
                }
            }
        }
        const _0x319ce6 = await this.#vtDatabase['getSggDatas'](_0x112171);
        for (let _0x195ecf of _0x319ce6) {
            if (_0x195ecf['dataValue']) {
                const _0x1c5301 = this.#getSggData(_0x195ecf['dataKey'], _0x195ecf['dataValue']);
                this.#drawSggDataOnCanvas(_0x1c5301), this.#sggData['cache']['set'](_0x195ecf['dataKey'], _0x1c5301), this.#sggData['waitingForResponse']['delete'](_0x195ecf['dataKey']);
            } else {
                const _0x9190c6 = _0x195ecf['dataKey']['split']('_');
                if (_0x9190c6['length'] < 0x2)
                    console['log']('vector\x20data\x20id\x20is\x20invalid.');
                else {
                    const _0x45a716 = _0x9190c6[0x1], _0xca6c7f = this.#getSggDataUrl(_0x45a716), _0x5eb8ab = await fetch(_0xca6c7f), _0x2e9764 = parseInt(_0x5eb8ab['headers']['get']('content-length') ?? 0x3e7), _0x29194f = _0x5eb8ab['status'] >= 0xc8 && _0x5eb8ab['status'] < 0x12c || _0x5eb8ab['status'] == 0x130;
                    if (!_0x29194f || _0x2e9764 == 0x0)
                        console['log']('[logi.maps]\x20ERROR:\x20' + _0x5eb8ab['status'] + ',\x20' + _0x5eb8ab['statusText']), this.#sggData['waitingForResponse']['delete'](_0x195ecf['dataKey']), this.#sggData['notFounds']['set'](_0x195ecf['dataKey'], logi['maps']['Utils']['getCurTick']());
                    else {
                        const _0x34e9ac = await _0x5eb8ab['arrayBuffer'](), _0x37972d = new Uint8Array(_0x34e9ac);
                        this.#vtDatabase['addSggData'](_0x195ecf['dataKey'], _0x37972d);
                        const _0x5ad9f0 = this.#getSggData(_0x195ecf['dataKey'], _0x37972d);
                        this.#drawSggDataOnCanvas(_0x5ad9f0), this.#sggData['cache']['set'](_0x195ecf['dataKey'], _0x5ad9f0), this.#sggData['waitingForResponse']['delete'](_0x195ecf['dataKey']);
                    }
                }
            }
        }
    }
    #drawSggDataOnCanvas(_0x2dc025) {
        const _0x4c7cd4 = this['getMapCoord'](), _0x1f09c9 = _0x4c7cd4['getMapRect'](), _0x4fee15 = _0x4c7cd4['getLevel'](), _0x3c53ca = this.#visibleType == logi['maps']['DISTRICT_VISIBLETYPE']['SGG_HOVER'];
        let _0x1b0112 = null, _0x5da3dc = null;
        if (_0x3c53ca) {
            if (this.#mousePos['x'] == null || this.#mousePos['y'] == null) {
                this.#hoverDistrict['currentRaw'] = null;
                return;
            }
            _0x1b0112 = this.#toDataStyle(this.#sggData['hoverStyle'], _0x4fee15, this.#sggData['range']), _0x5da3dc = _0x4c7cd4['screen2world'](this.#mousePos['x'], this.#mousePos['y']);
        } else
            _0x1b0112 = this.#toDataStyle(this.#sggData['style'], _0x4fee15, this.#sggData['range']);
        for (const _0x31dc0c of _0x2dc025['areas']) {
            if (!logi['maps']['Utils']['rectOnMapRect'](_0x31dc0c['boundaryRect'], _0x1f09c9))
                continue;
            if (_0x3c53ca) {
                if (this.#isHoveringPolygon(_0x31dc0c, _0x5da3dc) == ![])
                    continue;
                this.#hoverDistrict['currentRaw'] = _0x31dc0c;
            }
            this.#updateScreenCoord(_0x31dc0c);
            const _0x150dc0 = _0x31dc0c['screenCoord'];
            _0x150dc0['polygons']['length'] > 0x0 && (_0x150dc0['origin'] = _0x4c7cd4['world2screen'](_0x31dc0c['polygons'][0x0]['latlngs'][0x0]['lng'], _0x31dc0c['polygons'][0x0]['latlngs'][0x0]['lat']), this.#drawFill(_0x150dc0, _0x1b0112), this.#drawStroke(_0x150dc0, _0x1b0112), this.#drawText(_0x150dc0, _0x1b0112, _0x31dc0c['name'], _0x3c53ca));
            if (_0x3c53ca)
                break;
        }
    }
    async #searchSggData(_0x395dd3, _0x1ae454) {
        const _0x4394cf = this.#getSggIndexListRect(_0x1ae454), _0x4361cc = new Array();
        let _0x278122 = null;
        for (let _0x2b80b0 of _0x4394cf) {
            if (_0x278122 == null) {
                const _0x41ad2c = this.#sggData['cache']['get'](_0x2b80b0);
                if (_0x41ad2c)
                    _0x278122 = this.#findSggData(_0x395dd3, _0x41ad2c);
                else {
                    if (this.#sggData['waitingForResponse']['size'] >= this.#sggData['MAX_WAITING_SIZE'])
                        console['log']('[logi.maps]\x20sggDataWaitingForResponse\x20size\x20is\x20full.');
                    else {
                        const _0x267c06 = this.#sggData['waitingForResponse']['get'](_0x2b80b0), _0x12c979 = this.#sggData['notFounds']['get'](_0x2b80b0);
                        !_0x267c06 && !_0x12c979 && (this.#sggData['waitingForResponse']['set'](_0x2b80b0, logi['maps']['Utils']['getCurTick']()), _0x4361cc['push'](_0x2b80b0));
                    }
                }
            }
        }
        const _0x34bd56 = await this.#vtDatabase['getSggDatas'](_0x4361cc);
        for (let _0x45a37a of _0x34bd56) {
            if (_0x278122 != null)
                this.#sggData['waitingForResponse']['delete'](_0x45a37a['dataKey']);
            else {
                if (_0x45a37a['dataValue']) {
                    const _0x4b91fc = this.#getSggData(_0x45a37a['dataKey'], _0x45a37a['dataValue']);
                    _0x278122 = this.#findSggData(_0x395dd3, _0x4b91fc), this.#sggData['cache']['set'](_0x45a37a['dataKey'], _0x4b91fc), this.#sggData['waitingForResponse']['delete'](_0x45a37a['dataKey']);
                } else {
                    const _0x33eb9e = _0x45a37a['dataKey']['split']('_');
                    if (_0x33eb9e['length'] < 0x2)
                        console['log']('vector\x20data\x20id\x20is\x20invalid.');
                    else {
                        const _0x135014 = _0x33eb9e[0x1], _0x45750e = this.#getSggDataUrl(_0x135014), _0x164d5f = await fetch(_0x45750e), _0x545d9e = parseInt(_0x164d5f['headers']['get']('content-length') ?? 0x3e7), _0x6a06c5 = _0x164d5f['status'] >= 0xc8 && _0x164d5f['status'] < 0x12c || _0x164d5f['status'] == 0x130;
                        if (!_0x6a06c5 || _0x545d9e == 0x0)
                            console['log']('[logi.maps]\x20ERROR:\x20' + _0x164d5f['status'] + ',\x20' + _0x164d5f['statusText']), this.#sggData['waitingForResponse']['delete'](_0x45a37a['dataKey']), this.#sggData['notFounds']['set'](_0x45a37a['dataKey'], logi['maps']['Utils']['getCurTick']());
                        else {
                            const _0x3ddcef = await _0x164d5f['arrayBuffer'](), _0x18bfdc = new Uint8Array(_0x3ddcef);
                            this.#vtDatabase['addSggData'](_0x45a37a['dataKey'], _0x18bfdc);
                            const _0x5b6a63 = this.#getSggData(_0x45a37a['dataKey'], _0x18bfdc);
                            _0x278122 = this.#findSggData(_0x395dd3, _0x5b6a63), this.#sggData['cache']['set'](_0x45a37a['dataKey'], _0x5b6a63), this.#sggData['waitingForResponse']['delete'](_0x45a37a['dataKey']);
                        }
                    }
                }
            }
        }
        return _0x278122;
    }
    #findSggData(_0xda2d28, _0x4658bc) {
        const _0x54506e = BigInt(_0xda2d28);
        for (const _0x292676 of _0x4658bc['areas']) {
            if (_0x292676['code'] == _0x54506e)
                return _0x292676;
        }
        return null;
    }
    async #initEmdData() {
        if (this.#emdData['rects']['length'] == 0x0) {
            const _0x377396 = 'emd_index', _0x1c9616 = this.#emdData['cache']['get'](_0x377396);
            if (_0x1c9616)
                this.#updateEmdDataRects(_0x1c9616);
            else {
                const _0x5d83d0 = await this.#vtDatabase['getEmdData'](_0x377396);
                if (_0x5d83d0?.['dataValue'])
                    this.#updateEmdDataRects(_0x5d83d0['dataValue']);
                else {
                    const _0x59b83b = this.#getEmdIndexAllUrl(), _0x400870 = await fetch(_0x59b83b), _0x35a720 = parseInt(_0x400870['headers']['get']('content-length') ?? 0x3e7), _0x579d8d = _0x400870['status'] >= 0xc8 && _0x400870['status'] < 0x12c || _0x400870['status'] == 0x130;
                    if (!_0x579d8d || _0x35a720 == 0x0)
                        console['log']('[logi.maps]\x20ERROR:\x20' + _0x400870['status'] + ',\x20' + _0x400870['statusText']), this.#emdData['waitingForResponse']['delete'](_0x377396), this.#emdData['notFounds']['set'](_0x377396, logi['maps']['Utils']['getCurTick']());
                    else {
                        const _0x391d4f = await _0x400870['arrayBuffer'](), _0x95ff8 = new Uint8Array(_0x391d4f);
                        this.#vtDatabase['addEmdData'](_0x377396, _0x95ff8), this.#updateEmdDataRects(_0x95ff8);
                    }
                }
            }
        }
    }
    #updateEmdDataRects(_0x29e0b2) {
        const _0x27e7f2 = new DataView(_0x29e0b2['buffer']);
        let _0x3d96d0 = 0x0;
        const _0x18942a = _0x27e7f2['getUint32'](_0x3d96d0, !![]);
        _0x3d96d0 += 0x4, this.#emdData['rects'] = [];
        for (let _0x350f85 = 0x0; _0x350f85 < _0x18942a; ++_0x350f85) {
            const _0x453e8e = _0x27e7f2['getInt32'](_0x3d96d0, !![]);
            _0x3d96d0 += 0x4;
            const _0x3ec54c = _0x27e7f2['getInt32'](_0x3d96d0, !![]);
            _0x3d96d0 += 0x4;
            const _0x28b5d7 = _0x27e7f2['getInt32'](_0x3d96d0, !![]);
            _0x3d96d0 += 0x4;
            const _0x1d6695 = _0x27e7f2['getInt32'](_0x3d96d0, !![]);
            _0x3d96d0 += 0x4, this.#emdData['rects']['push']({
                'xMin': _0x453e8e / 0xf4240,
                'yMin': _0x3ec54c / 0xf4240,
                'xMax': _0x28b5d7 / 0xf4240,
                'yMax': _0x1d6695 / 0xf4240
            });
        }
    }
    #getEmdIndexListRect(_0x574c4e) {
        const _0x2404aa = new Array();
        let _0x535523 = 0x0;
        for (let _0x390560 of this.#emdData['rects']) {
            logi['maps']['Utils']['rectOnRect'](_0x574c4e['west'], _0x574c4e['south'], _0x574c4e['east'], _0x574c4e['north'], _0x390560['xMin'], _0x390560['yMin'], _0x390560['xMax'], _0x390560['yMax']) && _0x2404aa['push']('emd_' + _0x535523), _0x535523 += 0x1;
        }
        return _0x2404aa;
    }
    #getEmdData(_0x475455, _0x4be6a4) {
        const _0x4c1ef5 = new DataView(_0x4be6a4['buffer']);
        if (_0x4c1ef5['byteLength'] == 0x0)
            return [];
        else {
            let _0x443ee7 = 0x0;
            _0x4c1ef5['getInt32'](_0x443ee7, !![]), _0x443ee7 += 0x4;
            const _0xaa4e2b = _0x4c1ef5['getInt32'](_0x443ee7, !![]);
            _0x443ee7 += 0x4;
            const _0x4fb86c = new Array();
            let _0x1f8b73, _0x6ac2c1, _0x5bc403, _0x296184;
            for (let _0x5291ac = 0x0; _0x5291ac < _0xaa4e2b; ++_0x5291ac) {
                _0x1f8b73 = _0x4c1ef5['getBigInt64'](_0x443ee7, !![]), _0x443ee7 += 0x8;
                {
                    const _0x500c3a = _0x4c1ef5['getInt32'](_0x443ee7, !![]);
                    _0x443ee7 += 0x4;
                    const _0x4f03d3 = _0x4c1ef5['getInt32'](_0x443ee7, !![]);
                    _0x443ee7 += 0x4, _0x6ac2c1 = {
                        'lat': _0x4f03d3 / 0xf4240,
                        'lng': _0x500c3a / 0xf4240
                    };
                }
                const _0x4dd0e1 = new Uint8Array(_0x4be6a4['buffer'], _0x443ee7, 0x40);
                _0x5bc403 = new TextDecoder('euc-kr')['decode'](_0x4dd0e1)['replace'](/\0/g, '')['trim'](), _0x443ee7 += 0x40;
                const _0x7f3d36 = _0x4c1ef5['getInt32'](_0x443ee7, !![]);
                _0x443ee7 += 0x4, _0x296184 = new Array();
                let _0x4828b4 = new Array();
                const _0xd8accc = {
                        'xMin': 0xb4,
                        'yMin': 0x5a,
                        'xMax': -0xb4,
                        'yMax': -0x5a
                    }, _0x12dc52 = {
                        'xMin': 0xb4,
                        'yMin': 0x5a,
                        'xMax': -0xb4,
                        'yMax': -0x5a
                    };
                let _0x328841 = undefined, _0x263d06 = undefined;
                for (let _0x3d0cd4 = 0x0; _0x3d0cd4 < _0x7f3d36; ++_0x3d0cd4) {
                    const _0x251e4d = _0x4c1ef5['getInt32'](_0x443ee7, !![]);
                    _0x443ee7 += 0x4;
                    const _0x433812 = _0x4c1ef5['getInt32'](_0x443ee7, !![]);
                    _0x443ee7 += 0x4;
                    if (_0x251e4d == _0x328841 && _0x433812 == _0x263d06)
                        _0x4828b4['length'] >= 0x3 && _0x296184['push']({
                            'latlngs': _0x4828b4,
                            'boundaryRect': {
                                'xMin': _0x12dc52['xMin'],
                                'yMin': _0x12dc52['yMin'],
                                'xMax': _0x12dc52['xMax'],
                                'yMax': _0x12dc52['yMax']
                            }
                        }), _0x4828b4 = new Array(), _0x12dc52['xMin'] = 0xb4, _0x12dc52['yMin'] = 0x5a, _0x12dc52['xMax'] = -0xb4, _0x12dc52['yMax'] = -0x5a, _0x328841 = undefined, _0x263d06 = undefined;
                    else {
                        const _0x390c8c = _0x251e4d / 0xf4240, _0x3e63c5 = _0x433812 / 0xf4240;
                        _0x4828b4['push']({
                            'lat': _0x3e63c5,
                            'lng': _0x390c8c
                        }), _0x12dc52['xMin'] = Math['min'](_0x12dc52['xMin'], _0x390c8c), _0x12dc52['yMin'] = Math['min'](_0x12dc52['yMin'], _0x3e63c5), _0x12dc52['xMax'] = Math['max'](_0x12dc52['xMax'], _0x390c8c), _0x12dc52['yMax'] = Math['max'](_0x12dc52['yMax'], _0x3e63c5), _0xd8accc['xMin'] = Math['min'](_0xd8accc['xMin'], _0x390c8c), _0xd8accc['yMin'] = Math['min'](_0xd8accc['yMin'], _0x3e63c5), _0xd8accc['xMax'] = Math['max'](_0xd8accc['xMax'], _0x390c8c), _0xd8accc['yMax'] = Math['max'](_0xd8accc['yMax'], _0x3e63c5), _0x328841 = _0x251e4d, _0x263d06 = _0x433812;
                    }
                }
                _0x4828b4['length'] >= 0x3 && _0x296184['push']({
                    'latlngs': _0x4828b4,
                    'boundaryRect': {
                        'xMin': _0x12dc52['xMin'],
                        'yMin': _0x12dc52['yMin'],
                        'xMax': _0x12dc52['xMax'],
                        'yMax': _0x12dc52['yMax']
                    }
                }), _0x4fb86c['push'](new DistrictArea(_0x1f8b73, _0x6ac2c1, _0x5bc403, _0x296184, {
                    'xMin': _0xd8accc['xMin'],
                    'yMin': _0xd8accc['yMin'],
                    'xMax': _0xd8accc['xMax'],
                    'yMax': _0xd8accc['yMax']
                }));
            }
            return {
                'meshId': _0x475455,
                'areas': _0x4fb86c
            };
        }
    }
    async #drawEmdData() {
        const _0x10110a = this['getMapCoord'](), _0x78c8a3 = _0x10110a['getMapRect'](), _0x31220f = this.#getEmdIndexListRect(_0x78c8a3), _0x2e44f7 = new Array();
        for (let _0x40717b of _0x31220f) {
            const _0x5ea35c = this.#emdData['cache']['get'](_0x40717b);
            if (_0x5ea35c)
                this.#drawEmdDataOnCanvas(_0x5ea35c);
            else {
                if (this.#emdData['waitingForResponse']['size'] >= this.#emdData['MAX_WAITING_SIZE'])
                    console['log']('[logi.maps]\x20emdDataWaitingForResponse\x20size\x20is\x20full.');
                else {
                    const _0x56c738 = this.#emdData['waitingForResponse']['get'](_0x40717b), _0x946016 = this.#emdData['notFounds']['get'](_0x40717b);
                    !_0x56c738 && !_0x946016 && (this.#emdData['waitingForResponse']['set'](_0x40717b, logi['maps']['Utils']['getCurTick']()), _0x2e44f7['push'](_0x40717b));
                }
            }
        }
        const _0x58c95b = await this.#vtDatabase['getEmdDatas'](_0x2e44f7);
        for (let _0x22c9a9 of _0x58c95b) {
            if (_0x22c9a9['dataValue']) {
                const _0x247969 = this.#getEmdData(_0x22c9a9['dataKey'], _0x22c9a9['dataValue']);
                this.#drawEmdDataOnCanvas(_0x247969), this.#emdData['cache']['set'](_0x22c9a9['dataKey'], _0x247969), this.#emdData['waitingForResponse']['delete'](_0x22c9a9['dataKey']);
            } else {
                const _0x183390 = _0x22c9a9['dataKey']['split']('_');
                if (_0x183390['length'] < 0x2)
                    console['log']('vector\x20data\x20id\x20is\x20invalid.');
                else {
                    const _0x5a7904 = _0x183390[0x1], _0x50d107 = this.#getEmdDataUrl(_0x5a7904), _0x580589 = await fetch(_0x50d107), _0x379716 = parseInt(_0x580589['headers']['get']('content-length') ?? 0x3e7), _0x5efeb1 = _0x580589['status'] >= 0xc8 && _0x580589['status'] < 0x12c || _0x580589['status'] == 0x130;
                    if (!_0x5efeb1 || _0x379716 == 0x0)
                        console['log']('[logi.maps]\x20ERROR:\x20' + _0x580589['status'] + ',\x20' + _0x580589['statusText']), this.#emdData['waitingForResponse']['delete'](_0x22c9a9['dataKey']), this.#emdData['notFounds']['set'](_0x22c9a9['dataKey'], logi['maps']['Utils']['getCurTick']());
                    else {
                        const _0x1d9e53 = await _0x580589['arrayBuffer'](), _0x54b404 = new Uint8Array(_0x1d9e53);
                        this.#vtDatabase['addEmdData'](_0x22c9a9['dataKey'], _0x54b404);
                        const _0x3a61fc = this.#getEmdData(_0x22c9a9['dataKey'], _0x54b404);
                        this.#drawEmdDataOnCanvas(_0x3a61fc), this.#emdData['cache']['set'](_0x22c9a9['dataKey'], _0x3a61fc), this.#emdData['waitingForResponse']['delete'](_0x22c9a9['dataKey']);
                    }
                }
            }
        }
    }
    #drawEmdDataOnCanvas(_0x3df5f2) {
        const _0x3339bb = this['getMapCoord'](), _0x7288a2 = _0x3339bb['getMapRect'](), _0xf8c76f = _0x3339bb['getLevel'](), _0x24133b = this.#visibleType == logi['maps']['DISTRICT_VISIBLETYPE']['EMD_HOVER'];
        let _0x28debf = null, _0x1c0e94 = null;
        if (_0x24133b) {
            if (this.#mousePos['x'] == null || this.#mousePos['y'] == null) {
                this.#hoverDistrict['currentRaw'] = null;
                return;
            }
            _0x28debf = this.#toDataStyle(this.#emdData['hoverStyle'], _0xf8c76f, this.#emdData['range']), _0x1c0e94 = _0x3339bb['screen2world'](this.#mousePos['x'], this.#mousePos['y']);
        } else
            _0x28debf = this.#toDataStyle(this.#emdData['style'], _0xf8c76f, this.#emdData['range']);
        for (const _0x440056 of _0x3df5f2['areas']) {
            if (!logi['maps']['Utils']['rectOnMapRect'](_0x440056['boundaryRect'], _0x7288a2))
                continue;
            if (_0x24133b) {
                if (this.#isHoveringPolygon(_0x440056, _0x1c0e94) == ![])
                    continue;
                this.#hoverDistrict['currentRaw'] = _0x440056;
            }
            this.#updateScreenCoord(_0x440056);
            const _0x46582d = _0x440056['screenCoord'];
            _0x46582d['polygons']['length'] > 0x0 && (_0x46582d['origin'] = _0x3339bb['world2screen'](_0x440056['polygons'][0x0]['latlngs'][0x0]['lng'], _0x440056['polygons'][0x0]['latlngs'][0x0]['lat']), this.#drawFill(_0x46582d, _0x28debf), this.#drawStroke(_0x46582d, _0x28debf), this.#drawText(_0x46582d, _0x28debf, _0x440056['name'], _0x24133b));
            if (_0x24133b)
                break;
        }
    }
    async #searchEmdData(_0xabc6de, _0x40806f) {
        const _0x56be85 = this.#getEmdIndexListRect(_0x40806f), _0x7434fa = new Array();
        let _0x220f41 = null;
        for (let _0x36448f of _0x56be85) {
            if (_0x220f41 == null) {
                const _0x280297 = this.#emdData['cache']['get'](_0x36448f);
                if (_0x280297)
                    _0x220f41 = this.#findEmdData(_0xabc6de, _0x280297);
                else {
                    if (this.#emdData['waitingForResponse']['size'] >= this.#emdData['MAX_WAITING_SIZE'])
                        console['log']('[logi.maps]\x20emdDataWaitingForResponse\x20size\x20is\x20full.');
                    else {
                        const _0x1afb0a = this.#emdData['waitingForResponse']['get'](_0x36448f), _0x13c68b = this.#emdData['notFounds']['get'](_0x36448f);
                        !_0x1afb0a && !_0x13c68b && (this.#emdData['waitingForResponse']['set'](_0x36448f, logi['maps']['Utils']['getCurTick']()), _0x7434fa['push'](_0x36448f));
                    }
                }
            }
        }
        const _0x23ff3e = await this.#vtDatabase['getEmdDatas'](_0x7434fa);
        for (let _0x417501 of _0x23ff3e) {
            if (_0x220f41 != null)
                this.#emdData['waitingForResponse']['delete'](_0x417501['dataKey']);
            else {
                if (_0x417501['dataValue']) {
                    const _0x26a21e = this.#getEmdData(_0x417501['dataKey'], _0x417501['dataValue']);
                    _0x220f41 = this.#findEmdData(_0xabc6de, _0x26a21e), this.#emdData['cache']['set'](_0x417501['dataKey'], _0x26a21e), this.#emdData['waitingForResponse']['delete'](_0x417501['dataKey']);
                } else {
                    const _0x3f36b4 = _0x417501['dataKey']['split']('_');
                    if (_0x3f36b4['length'] < 0x2)
                        console['log']('vector\x20data\x20id\x20is\x20invalid.');
                    else {
                        const _0x584a6d = _0x3f36b4[0x1], _0xdf9ac = this.#getEmdDataUrl(_0x584a6d), _0x4e1685 = await fetch(_0xdf9ac), _0x5bfedf = parseInt(_0x4e1685['headers']['get']('content-length') ?? 0x3e7), _0x27cab1 = _0x4e1685['status'] >= 0xc8 && _0x4e1685['status'] < 0x12c || _0x4e1685['status'] == 0x130;
                        if (!_0x27cab1 || _0x5bfedf == 0x0)
                            console['log']('[logi.maps]\x20ERROR:\x20' + _0x4e1685['status'] + ',\x20' + _0x4e1685['statusText']), this.#emdData['waitingForResponse']['delete'](_0x417501['dataKey']), this.#emdData['notFounds']['set'](_0x417501['dataKey'], logi['maps']['Utils']['getCurTick']());
                        else {
                            const _0x3b6ff4 = await _0x4e1685['arrayBuffer'](), _0xa29281 = new Uint8Array(_0x3b6ff4);
                            this.#vtDatabase['addEmdData'](_0x417501['dataKey'], _0xa29281);
                            const _0x4c6ae5 = this.#getEmdData(_0x417501['dataKey'], _0xa29281);
                            _0x220f41 = this.#findEmdData(_0xabc6de, _0x4c6ae5), this.#emdData['cache']['set'](_0x417501['dataKey'], _0x4c6ae5), this.#emdData['waitingForResponse']['delete'](_0x417501['dataKey']);
                        }
                    }
                }
            }
        }
        return _0x220f41;
    }
    #findEmdData(_0x353950, _0x1107bb) {
        const _0x1d8c67 = BigInt(_0x353950);
        for (const _0x132e84 of _0x1107bb['areas']) {
            if (_0x132e84['code'] == _0x1d8c67)
                return _0x132e84;
        }
        return null;
    }
    async #drawSearchDistrictData() {
        const _0x3abdfe = this['getMapCoord'](), _0x56fe3c = _0x3abdfe['getMapRect'](), _0x42530e = _0x3abdfe['getLevel']();
        if (this.#searchDistrict['area']) {
            const _0x414fa6 = this.#searchDistrict['area'], _0x365505 = this.#toDataStyle(this.#searchDistrict['style'], _0x42530e, _0x42530e);
            if (!logi['maps']['Utils']['rectOnMapRect'](_0x414fa6['boundaryRect'], _0x56fe3c))
                return;
            this.#updateScreenCoord(_0x414fa6);
            const _0x1fa3d8 = _0x414fa6['screenCoord'];
            _0x1fa3d8['polygons']['length'] > 0x0 && (_0x1fa3d8['origin'] = _0x3abdfe['world2screen'](_0x414fa6['polygons'][0x0]['latlngs'][0x0]['lng'], _0x414fa6['polygons'][0x0]['latlngs'][0x0]['lat']), console['log']('dataStyle', _0x365505), this.#drawFill(_0x1fa3d8, _0x365505), this.#drawStroke(_0x1fa3d8, _0x365505), this.#drawText(_0x1fa3d8, _0x365505, _0x414fa6['name'], ![]));
        }
    }
    #latlngInPolygon(_0x5e5095, _0xad6afb) {
        let _0x401621 = ![];
        if (_0x5e5095['latlngs']['length'] >= 0x4) {
            let _0x14addf = _0x5e5095['latlngs'][_0x5e5095['latlngs']['length'] - 0x1];
            for (let _0x471395 of _0x5e5095['latlngs']) {
                const _0xbe0330 = _0x471395['lat'] > _0xad6afb['lat'] != _0x14addf['lat'] > _0xad6afb['lat'] && _0xad6afb['lng'] < (_0x14addf['lng'] - _0x471395['lng']) * (_0xad6afb['lat'] - _0x471395['lat']) / (_0x14addf['lat'] - _0x471395['lat']) + _0x471395['lng'];
                _0xbe0330 && (_0x401621 = !_0x401621), _0x14addf = _0x471395;
            }
        }
        return _0x401621;
    }
};
export default logi['maps']['VtLayer'];