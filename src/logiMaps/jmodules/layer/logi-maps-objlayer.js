import a3_0x27cdad from '../common/logi-maps-types.js?v=2.1.10.1';
import a3_0x3407be from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a3_0x497d6b from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a3_0x23649c from '../utility/logi-maps-boundarychecker.js?v=2.1.10.1';
import a3_0xd2f6c4 from '../object/logi-maps-object.js?v=2.1.10.1';
import a3_0x43012b from '../layer/logi-maps-layer.js?v=2.1.10.1';
import a3_0x1aa125 from '../graphics/logi-maps-gfx2d.js?v=2.1.10.1';
import a3_0x7c9126 from '../graphics/logi-maps-gfxgl.js?v=2.1.10.1';
import a3_0x4cc76f from '../handler/logi-maps-imagehandler.js?v=2.1.10.1';
import a3_0x5f4f90 from '../handler/logi-maps-labelhandler.js?v=2.1.10.1';
import a3_0x4bc693 from '../handler/logi-maps-linehandler.js?v=2.1.10.1';
import a3_0x39432f from '../handler/logi-maps-polygonhandler.js?v=2.1.10.1';
import a3_0x3146f8 from '../handler/logi-maps-circlehandler.js?v=2.1.10.1';
import a3_0x19a3e2 from '../handler/logi-maps-routehandler.js?v=2.1.10.1';
import a3_0x30bbe7 from '../handler/logi-maps-gpshandler.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectsInTile'] = a3_0x27cdad['ObjectsInTile'], logi['maps']['OBJEVENT'] = a3_0x27cdad['OBJEVENT'], logi['maps']['ALIGN'] = a3_0x27cdad['ALIGN'], logi['maps']['Utils'] = a3_0x3407be, logi['maps']['BoundaryData'] = a3_0x497d6b, logi['maps']['BoundaryChecker'] = a3_0x23649c, logi['maps']['Object'] = a3_0xd2f6c4, logi['maps']['Layer'] = a3_0x43012b, logi['maps']['Gfx2d'] = a3_0x1aa125, logi['maps']['Gfxgl'] = a3_0x7c9126, logi['maps']['ImageHandler'] = a3_0x4cc76f, logi['maps']['LabelHandler'] = a3_0x5f4f90, logi['maps']['LineHandler'] = a3_0x4bc693, logi['maps']['PolygonHandler'] = a3_0x39432f, logi['maps']['CircleHandler'] = a3_0x3146f8, logi['maps']['RouteHandler'] = a3_0x19a3e2, logi['maps']['GpsHandler'] = a3_0x30bbe7, logi['maps']['ObjLayer'] = class extends logi['maps']['Layer'] {
    #objGfx2d = null;
    #imageHandler;
    #labelHandler;
    #lineHandler;
    #polygonHandler;
    #circleHandler;
    #routeHandler;
    #gpsHandler;
    #overlapCheck;
    #overlapInfoVisibility;
    #boundaryChecker;
    #orderType = logi['maps']['Object']['ORDERTYPE']['object'];
    #freezeOffGfx2d;
    #tapChecker;
    #doubleTabFlag;
    #clickChecker;
    #doubleClickFlag;
    constructor(_0x20e4b0, _0x395341, _0x58877a) {
        if (_0x20e4b0 == 'parent') {
            const _0x3cb307 = _0x395341['id'] + '_objlayer';
            var _0x1a7e10 = _0x395341['querySelector']('[id=\x22' + _0x3cb307 + '\x22]');
            if (_0x1a7e10)
                console['log']('Detected\x20existing\x20div.\x20Reusing\x20the\x20div.(' + _0x3cb307 + ')'), super(_0x1a7e10, _0x58877a);
            else {
                const _0x19204a = document['createElement']('div');
                _0x395341['appendChild'](_0x19204a), _0x19204a['id'] = _0x3cb307, _0x19204a['style'] = 'position:absolute;\x20width:100%;\x20height:100%;\x20overflow-y:hidden;\x20overflow-x:hidden;', super(_0x19204a, _0x58877a);
            }
        } else
            super(_0x395341, _0x58877a);
        this['addGfxCanvas']('2d'), this.#objGfx2d = this['getGfx2d'](0x0), this.#imageHandler = new logi['maps']['ImageHandler'](_0x58877a), this.#labelHandler = new logi['maps']['LabelHandler'](_0x58877a), this.#lineHandler = new logi['maps']['LineHandler'](_0x58877a), this.#polygonHandler = new logi['maps']['PolygonHandler'](_0x58877a), this.#circleHandler = new logi['maps']['CircleHandler'](_0x58877a), this.#routeHandler = new logi['maps']['RouteHandler'](_0x58877a), this.#gpsHandler = new logi['maps']['GpsHandler'](_0x58877a), this.#overlapCheck = !![], this.#overlapInfoVisibility = ![], this.#boundaryChecker = new logi['maps']['BoundaryChecker'](), this.#freezeOffGfx2d = new logi['maps']['Gfx2d'](new OffscreenCanvas(0x100, 0x100)), this.#tapChecker = {
            'tapped': ![],
            'point': {
                'x': 0x0,
                'y': 0x0
            }
        }, this.#doubleTabFlag = ![], this.#clickChecker = {
            'clicked': ![],
            'point': {
                'x': 0x0,
                'y': 0x0
            }
        }, this.#doubleClickFlag = ![];
    }
    ['triggerEvent'](_0x5b53b9) {
        let _0x310417 = {
            'type': '',
            'point': {
                'x': 0x0,
                'y': 0x0
            }
        };
        switch (_0x5b53b9['type']) {
        case 'touchstart': {
                if (_0x5b53b9['targetTouches']['length'] == 0x1) {
                    let _0x10d08b = this.#getTouchPoint(_0x5b53b9['target']['getBoundingClientRect'](), _0x5b53b9['targetTouches']);
                    this.#tapChecker['tapped'] = !![], this.#tapChecker['point']['x'] = _0x10d08b['x'], this.#tapChecker['point']['y'] = _0x10d08b['y'];
                } else
                    this.#tapChecker['tapped'] = ![];
            }
            break;
        case 'touchend': {
                if (this.#tapChecker['tapped'] == !![]) {
                    let _0x54462d = this.#getTouchPoint(_0x5b53b9['target']['getBoundingClientRect'](), _0x5b53b9['changedTouches']);
                    Math['abs'](this.#tapChecker['point']['x'] - _0x54462d['x']) <= 0x2 && Math['abs'](this.#tapChecker['point']['y'] - _0x54462d['y']) <= 0x2 && (!this.#doubleTabFlag ? (this.#doubleTabFlag = !![], setTimeout(() => {
                        this.#doubleTabFlag == !![] && (_0x310417['type'] = logi['maps']['OBJEVENT']['touch'], _0x310417['point']['x'] = _0x54462d['x'], _0x310417['point']['y'] = _0x54462d['y'], this.#sendEvent(_0x310417)), this.#doubleTabFlag = ![];
                    }, 0xc8)) : (this.#doubleTabFlag = ![], _0x310417['type'] = logi['maps']['OBJEVENT']['dbltouch'], _0x310417['point']['x'] = _0x54462d['x'], _0x310417['point']['y'] = _0x54462d['y'], this.#sendEvent(_0x310417)));
                }
            }
            break;
        case 'mousedown': {
                let _0x31af9a = {
                    'x': _0x5b53b9['offsetX'],
                    'y': _0x5b53b9['offsetY']
                };
                this.#clickChecker['clicked'] = !![], this.#clickChecker['point']['x'] = _0x31af9a['x'], this.#clickChecker['point']['y'] = _0x31af9a['y'];
            }
            break;
        case 'mouseup':
            if (this.#clickChecker['clicked'] == !![]) {
                let _0x1b5413 = {
                    'x': _0x5b53b9['offsetX'],
                    'y': _0x5b53b9['offsetY']
                };
                Math['abs'](this.#clickChecker['point']['x'] - _0x1b5413['x']) <= 0x2 && Math['abs'](this.#clickChecker['point']['y'] - _0x1b5413['y']) <= 0x2 && (!this.#doubleClickFlag ? (this.#doubleClickFlag = !![], setTimeout(() => {
                    this.#doubleClickFlag == !![] && (_0x310417['type'] = logi['maps']['OBJEVENT']['click'], _0x310417['point']['x'] = _0x1b5413['x'], _0x310417['point']['y'] = _0x1b5413['y'], this.#sendEvent(_0x310417)), this.#doubleClickFlag = ![];
                }, 0xc8)) : (this.#doubleClickFlag = ![], _0x310417['type'] = logi['maps']['OBJEVENT']['dblclick'], _0x310417['point']['x'] = _0x1b5413['x'], _0x310417['point']['y'] = _0x1b5413['y'], this.#sendEvent(_0x310417)));
            }
            break;
        default:
        }
    }
    ['setOrderType'](_0x14795a) {
        if (_0x14795a == logi['maps']['Object']['ORDERTYPE']['object'])
            this.#orderType = logi['maps']['Object']['ORDERTYPE']['object'];
        else
            _0x14795a == logi['maps']['Object']['ORDERTYPE']['zindex'] && (this.#orderType = logi['maps']['Object']['ORDERTYPE']['zindex']);
    }
    ['setOverlapCheck'](_0x30a939) {
        this.#overlapCheck != _0x30a939 && (this.#overlapCheck = _0x30a939, this['setUpdateFlag']());
    }
    ['setOverlapInfoVisibility'](_0x46f1f0) {
        this.#overlapInfoVisibility != _0x46f1f0 && (this.#overlapInfoVisibility = _0x46f1f0, this['setUpdateFlag']());
    }
    ['updateCanvas']() {
        this['setDrawFlag']();
    }
    ['drawCanvas'](_0x5db230) {
        this.#boundaryCheck(), this['clearColor'](), this.#drawObjects(_0x5db230?.['isDragging'] ?? ![]);
    }
    ['captureFreezeCanvas']() {
        this.#freezeOffGfx2d['width'] = this.#objGfx2d['width'], this.#freezeOffGfx2d['height'] = this.#objGfx2d['height'], this.#freezeOffGfx2d['clearColor'](), this.#freezeOffGfx2d['drawImage'](this.#objGfx2d, 0x0, 0x0);
    }
    ['drawFreezeCanvas'](_0x33870b, _0x3ca8c4) {
        const _0x595f3b = this['getDevicePixelRatio']();
        this.#objGfx2d['clearColor'](), this.#objGfx2d['drawImage'](this.#freezeOffGfx2d, _0x33870b * _0x595f3b, _0x3ca8c4 * _0x595f3b);
    }
    ['hitObj'](_0xc58263, _0x53866e) {
        let _0x4ba7e1;
        _0x4ba7e1 = this['hitLabel'](_0xc58263, _0x53866e);
        if (_0x4ba7e1 !== null)
            return _0x4ba7e1;
        _0x4ba7e1 = this['hitImage'](_0xc58263, _0x53866e);
        if (_0x4ba7e1 !== null)
            return _0x4ba7e1;
        _0x4ba7e1 = this['hitLine'](_0xc58263, _0x53866e);
        if (_0x4ba7e1 !== null)
            return _0x4ba7e1;
        _0x4ba7e1 = this['hitPolygon'](_0xc58263, _0x53866e);
        if (_0x4ba7e1 !== null)
            return _0x4ba7e1;
        _0x4ba7e1 = this['hitCircle'](_0xc58263, _0x53866e);
        if (_0x4ba7e1 !== null)
            return _0x4ba7e1;
        _0x4ba7e1 = this['hitRoute'](_0xc58263, _0x53866e);
        if (_0x4ba7e1 !== null)
            return _0x4ba7e1;
        _0x4ba7e1 = this['hitGps'](_0xc58263, _0x53866e);
        if (_0x4ba7e1 !== null)
            return _0x4ba7e1;
        return null;
    }
    ['removeAll'](_0x29f35d = []) {
        this['removeImageAll'](_0x29f35d), this['removeLabelAll'](_0x29f35d), this['removeLineAll'](_0x29f35d), this['removePolygonAll'](_0x29f35d), this['removeCircleAll'](_0x29f35d), this['removeRouteAll'](_0x29f35d), this['removeGpsAll'](_0x29f35d);
    }
    ['addImage'](_0x5dc99d) {
        if (this.#imageHandler['addImage'](_0x5dc99d, this) == !![])
            return this['setUpdateFlag'](), _0x5dc99d;
        return null;
    }
    ['isExistImage'](_0x252344) {
        return this.#imageHandler['isExistImage'](_0x252344);
    }
    ['findImage'](_0x242872) {
        return this.#imageHandler['findImage'](_0x242872);
    }
    ['removeImage'](_0x47be7d) {
        this.#imageHandler['removeImage'](_0x47be7d) == !![] && this['setUpdateFlag']();
    }
    ['removeImageAll'](_0x4a6cbb = []) {
        this.#imageHandler['removeImageAll'](_0x4a6cbb), this['setUpdateFlag']();
    }
    ['hitImage'](_0x393fa5, _0x429764) {
        return this.#imageHandler['hitImage'](_0x393fa5, _0x429764);
    }
    ['hitImages'](_0x1a62b0, _0x4cfc8b) {
        return this.#imageHandler['hitImages'](_0x1a62b0, _0x4cfc8b);
    }
    ['setDrawingImageOnMove'](_0x29fe10) {
        this.#imageHandler['setDrawingImageOnMove'](_0x29fe10) == !![] && this['setUpdateFlag']();
    }
    ['refreshTiledImageByLevel']() {
        this.#imageHandler['refreshTiledImageByLevel'](), this['setUpdateFlag']();
    }
    ['addLabel'](_0xca9a8c) {
        if (this.#labelHandler['addLabel'](_0xca9a8c, this) == !![])
            return this['setUpdateFlag'](), _0xca9a8c;
        return null;
    }
    ['isExistLabel'](_0x15285d) {
        return this.#labelHandler['isExistLabel'](_0x15285d);
    }
    ['findLabel'](_0x3e2a7c) {
        return this.#labelHandler['findLabel'](_0x3e2a7c);
    }
    ['removeLabel'](_0x19cb5f) {
        this.#labelHandler['removeLabel'](_0x19cb5f) == !![] && this['setUpdateFlag']();
    }
    ['removeLabelAll'](_0xae45a4 = []) {
        this.#labelHandler['removeLabelAll'](_0xae45a4), this['setUpdateFlag']();
    }
    ['hitLabel'](_0x180703, _0x2be015) {
        return this.#labelHandler['hitLabel'](_0x180703, _0x2be015);
    }
    ['setDrawingLabelOnMove'](_0x921e43) {
        this.#labelHandler['setDrawingLabelOnMove'](_0x921e43) == !![] && this['setUpdateFlag']();
    }
    ['refreshTiledLabelByLevel']() {
        this.#labelHandler['refreshTiledLabelByLevel'](), this['setUpdateFlag']();
    }
    ['addLine'](_0xe0856e) {
        if (this.#lineHandler['addLine'](_0xe0856e, this) == !![])
            return this['setUpdateFlag'](), _0xe0856e;
        return null;
    }
    ['isExistLine'](_0x27f827) {
        return this.#lineHandler['isExistLine'](_0x27f827);
    }
    ['findLine'](_0x23ea8b) {
        return this.#lineHandler['findLine'](_0x23ea8b);
    }
    ['removeLine'](_0x2d9069) {
        this.#lineHandler['removeLine'](_0x2d9069) == !![] && this['setUpdateFlag']();
    }
    ['removeLineAll'](_0x534e97 = []) {
        this.#lineHandler['removeLineAll'](_0x534e97), this['setUpdateFlag']();
    }
    ['hitLine'](_0x506ca1, _0xea9e99) {
        return this.#lineHandler['hitLine'](_0x506ca1, _0xea9e99);
    }
    ['setDrawingLineOnMove'](_0x52f8ce) {
        this.#lineHandler['setDrawingLineOnMove'](_0x52f8ce) == !![] && this['setUpdateFlag']();
    }
    ['addPolygon'](_0x76809a) {
        if (this.#polygonHandler['addPolygon'](_0x76809a, this) == !![])
            return this['setUpdateFlag'](), _0x76809a;
        return null;
    }
    ['isExistPolygon'](_0xddaa8f) {
        return this.#polygonHandler['isExistPolygon'](_0xddaa8f);
    }
    ['findPolygon'](_0x1e0c5e) {
        return this.#polygonHandler['findPolygon'](_0x1e0c5e);
    }
    ['removePolygon'](_0xec99b9) {
        this.#polygonHandler['removePolygon'](_0xec99b9) == !![] && this['setUpdateFlag']();
    }
    ['removePolygonAll'](_0xa366b4 = []) {
        this.#polygonHandler['removePolygonAll'](_0xa366b4), this['setUpdateFlag']();
    }
    ['hitPolygon'](_0x3c3846, _0x21e535) {
        return this.#polygonHandler['hitPolygon'](_0x3c3846, _0x21e535);
    }
    ['setDrawingPolygonOnMove'](_0x2fcfff) {
        this.#polygonHandler['setDrawingPolygonOnMove'](_0x2fcfff) == !![] && this['setUpdateFlag']();
    }
    ['addCircle'](_0x271eb5) {
        if (this.#circleHandler['addCircle'](_0x271eb5, this) == !![])
            return this['setUpdateFlag'](), _0x271eb5;
        return null;
    }
    ['isExistCircle'](_0x18e8f6) {
        return this.#circleHandler['isExistCircle'](_0x18e8f6);
    }
    ['findCircle'](_0x51e513) {
        return this.#circleHandler['findCircle'](_0x51e513);
    }
    ['removeCircle'](_0x24886c) {
        this.#circleHandler['removeCircle'](_0x24886c) == !![] && this['setUpdateFlag']();
    }
    ['removeCircleAll'](_0x414901 = []) {
        this.#circleHandler['removeCircleAll'](_0x414901), this['setUpdateFlag']();
    }
    ['hitCircle'](_0x4caf30, _0xe577e6) {
        return this.#circleHandler['hitCircle'](_0x4caf30, _0xe577e6);
    }
    ['setDrawingCircleOnMove'](_0x8ec6bb) {
        this.#circleHandler['setDrawingCircleOnMove'](_0x8ec6bb) == !![] && this['setUpdateFlag']();
    }
    ['addRoute'](_0x40ee65) {
        if (this.#routeHandler['addRoute'](_0x40ee65, this) == !![])
            return this['setUpdateFlag'](), _0x40ee65;
        return null;
    }
    ['isExistRoute'](_0x285d17) {
        return this.#routeHandler['isExistRoute'](_0x285d17);
    }
    ['findRoute'](_0x54283a) {
        return this.#routeHandler['findRoute'](_0x54283a);
    }
    ['removeRoute'](_0x175eff) {
        this.#routeHandler['removeRoute'](_0x175eff) == !![] && this['setUpdateFlag']();
    }
    ['removeRouteAll'](_0x1a464a = []) {
        this.#routeHandler['removeRouteAll'](_0x1a464a), this['setUpdateFlag']();
    }
    ['hitRoute'](_0x2de5a7, _0x51c128) {
        return this.#routeHandler['hitRoute'](_0x2de5a7, _0x51c128);
    }
    ['setDrawingRouteOnMove'](_0x3fbd50) {
        this.#routeHandler['setDrawingRouteOnMove'](_0x3fbd50) == !![] && this['setUpdateFlag']();
    }
    ['addGps'](_0x159d9d) {
        if (this.#gpsHandler['addGps'](_0x159d9d, this) == !![])
            return this['setUpdateFlag'](), _0x159d9d;
        return null;
    }
    ['isExistGps'](_0x262bbe) {
        return this.#gpsHandler['isExistGps'](_0x262bbe);
    }
    ['findGps'](_0x2bc365) {
        return this.#gpsHandler['findGps'](_0x2bc365);
    }
    ['removeGps'](_0x5cbb12) {
        this.#gpsHandler['removeGps'](_0x5cbb12) == !![] && this['setUpdateFlag']();
    }
    ['removeGpsAll'](_0x184c1f = []) {
        this.#gpsHandler['removeGpsAll'](_0x184c1f), this['setUpdateFlag']();
    }
    ['hitGps'](_0x1a3eae, _0x474482) {
        return this.#routeHandler['hitGps'](_0x1a3eae, _0x474482);
    }
    ['setDrawingGpsOnMove'](_0x501667) {
        this.#gpsHandler['setDrawingGpsOnMove'](_0x501667) == !![] && this['setUpdateFlag']();
    }
    #sendEvent(_0x84fac4) {
        this.#imageHandler['sendEvent'](_0x84fac4), this.#labelHandler['sendEvent'](_0x84fac4), this.#lineHandler['sendEvent'](_0x84fac4), this.#polygonHandler['sendEvent'](_0x84fac4), this.#circleHandler['sendEvent'](_0x84fac4), this.#routeHandler['sendEvent'](_0x84fac4), this.#gpsHandler['sendEvent'](_0x84fac4);
    }
    #getTouchPoint(_0x53b28a, _0x8d625b) {
        return {
            'x': _0x8d625b[0x0]['clientX'] - _0x53b28a['left'],
            'y': _0x8d625b[0x0]['clientY'] - _0x53b28a['top']
        };
    }
    #boundaryCheck() {
        const _0x3c3eb1 = this['getMapCoord']()['getMapRect']();
        this.#boundaryChecker['cleanBoundary'](), this.#imageHandler['boundaryCheckInit'](this.#overlapInfoVisibility), this.#labelHandler['boundaryCheckInit'](this.#overlapInfoVisibility), this.#imageHandler['updateOverlapCheck1st'](this.#overlapCheck, this.#boundaryChecker), this.#labelHandler['updateOverlapCheck1st'](this.#overlapCheck, this.#boundaryChecker), this.#imageHandler['updateOverlapCheck2nd'](this.#overlapCheck, this.#boundaryChecker), this.#labelHandler['updateOverlapCheck2nd'](this.#overlapCheck, this.#boundaryChecker);
    }
    #drawObjects(_0x4b44a6 = ![]) {
        let _0x5723e5 = [];
        {
            const _0x374f7f = this.#routeHandler['getDrawObjects'](_0x4b44a6);
            if (this.#orderType == logi['maps']['Object']['ORDERTYPE']['object'])
                for (const _0x213b3e of _0x374f7f) {
                    _0x213b3e['drawCanvas']();
                }
            else
                _0x5723e5 = _0x5723e5['concat'](_0x374f7f);
            const _0x24be72 = this.#gpsHandler['getDrawObjects'](_0x4b44a6);
            if (this.#orderType == logi['maps']['Object']['ORDERTYPE']['object'])
                for (const _0x318083 of _0x24be72) {
                    _0x318083['drawCanvas']();
                }
            else
                _0x5723e5 = _0x5723e5['concat'](_0x24be72);
            const _0x205b8d = this.#lineHandler['getDrawObjects'](_0x4b44a6);
            if (this.#orderType == logi['maps']['Object']['ORDERTYPE']['object'])
                for (const _0x469baf of _0x205b8d) {
                    _0x469baf['drawCanvas']();
                }
            else
                _0x5723e5 = _0x5723e5['concat'](_0x205b8d);
            const _0x82f0e8 = this.#circleHandler['getDrawObjects'](_0x4b44a6);
            if (this.#orderType == logi['maps']['Object']['ORDERTYPE']['object'])
                for (const _0x37bb45 of _0x82f0e8) {
                    _0x37bb45['drawCanvas']();
                }
            else
                _0x5723e5 = _0x5723e5['concat'](_0x82f0e8);
            const _0xeba9ed = this.#polygonHandler['getDrawObjects'](_0x4b44a6);
            if (this.#orderType == logi['maps']['Object']['ORDERTYPE']['object'])
                for (const _0x23c4d3 of _0xeba9ed) {
                    _0x23c4d3['drawCanvas']();
                }
            else
                _0x5723e5 = _0x5723e5['concat'](_0xeba9ed);
            const _0x17a038 = this.#imageHandler['getDrawObjects'](_0x4b44a6);
            if (this.#orderType == logi['maps']['Object']['ORDERTYPE']['object'])
                for (const _0x48f262 of _0x17a038) {
                    _0x48f262['drawCanvas']();
                }
            else
                _0x5723e5 = _0x5723e5['concat'](_0x17a038);
            const _0x2d5a1b = this.#labelHandler['getDrawObjects'](_0x4b44a6);
            if (this.#orderType == logi['maps']['Object']['ORDERTYPE']['object'])
                for (const _0x57cf40 of _0x2d5a1b) {
                    _0x57cf40['drawCanvas']();
                }
            else
                _0x5723e5 = _0x5723e5['concat'](_0x2d5a1b);
            if (this.#orderType == logi['maps']['Object']['ORDERTYPE']['zindex']) {
                const _0x3d70d0 = _0x5723e5['sort']((_0x3bcdc3, _0x3d10d1) => _0x3bcdc3['zIndex'] != _0x3d10d1['zIndex'] ? _0x3bcdc3['zIndex'] - _0x3d10d1['zIndex'] : _0x3bcdc3['objOrder'] - _0x3d10d1['objOrder']);
                for (const _0x195793 of _0x3d70d0) {
                    _0x195793['drawCanvas']();
                }
                _0x5723e5['length'] = 0x0;
            }
        }
    }
};
export default logi['maps']['ObjLayer'];