import a1_0x597160 from '../graphics/logi-maps-gfx2d.js?v=2.1.10.1';
import a1_0x281d52 from '../graphics/logi-maps-gfxgl.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Gfx2d'] = a1_0x597160, logi['maps']['Gfxgl'] = a1_0x281d52, logi['maps']['Layer'] = class {
    #mapCoord;
    #layerDiv;
    #gfxCanvases;
    #devicePixelRatio = 0x1;
    #updateFlag;
    #drawFlag;
    #onResizeScreen = null;
    constructor(_0x451915, _0x2bf6b6) {
        this.#mapCoord = _0x2bf6b6, this.#gfxCanvases = new Array(), this.#layerDiv = _0x451915, this.#updateFlag = !![], this.#drawFlag = ![];
    }
    ['addGfxCanvas'](_0x396516) {
        const _0x5bad68 = document['createElement']('canvas');
        _0x5bad68['style'] = 'position:absolute;\x20width:100%;\x20height:100%;\x20overflow-y:hidden;\x20overflow-x:hidden;', this.#layerDiv['appendChild'](_0x5bad68), _0x396516 == 'webgl' ? this.#gfxCanvases['push'](new logi['maps']['Gfxgl'](_0x5bad68)) : this.#gfxCanvases['push'](new logi['maps']['Gfx2d'](_0x5bad68)), this.#updateFlag = !![];
    }
    ['setEventListener'](_0x475301, _0x3e6a38) {
        _0x475301 == 'resize' && (this.#onResizeScreen = _0x3e6a38);
    }
    ['setScreenSize'](_0x4036be, _0x54570b, _0xdc1c9) {
        this.#devicePixelRatio = _0xdc1c9;
        for (const _0x58bd18 of this.#gfxCanvases) {
            _0x58bd18['getType']() == 'webgl' ? _0x58bd18['setViewport'](_0x4036be, _0x54570b) : (_0x58bd18['width'] = Math['floor'](_0x4036be * _0xdc1c9), _0x58bd18['height'] = Math['floor'](_0x54570b * _0xdc1c9));
        }
        this.#onResizeScreen && this.#onResizeScreen(_0x4036be, _0x54570b, _0xdc1c9);
    }
    ['getScreenSize']() {
        return {
            'width': this.#gfxCanvases[0x0]['width'],
            'height': this.#gfxCanvases[0x0]['height']
        };
    }
    ['setUpdateFlag'](_0x3b2039 = !![]) {
        this.#updateFlag = _0x3b2039;
    }
    ['getUpdateFlag']() {
        return this.#updateFlag;
    }
    ['setDrawFlag'](_0x27e586 = !![]) {
        this.#drawFlag = _0x27e586;
    }
    ['getDrawFlag']() {
        return this.#drawFlag;
    }
    ['getGfxCanvas'](_0x401ae5 = 0x0) {
        return this.#gfxCanvases[_0x401ae5];
    }
    ['getGfxgl'](_0x9edd03 = 0x0) {
        const _0x446b15 = this.#gfxCanvases[_0x9edd03];
        if (_0x446b15 instanceof logi['maps']['Gfxgl'])
            return _0x446b15;
        return null;
    }
    ['getGfx2d'](_0x33a529 = 0x0) {
        const _0x2c94cd = this.#gfxCanvases[_0x33a529];
        if (_0x2c94cd instanceof logi['maps']['Gfx2d'])
            return _0x2c94cd;
        return null;
    }
    ['getDevicePixelRatio']() {
        return this.#devicePixelRatio;
    }
    ['clearColor']() {
        for (const _0x262dc5 of this.#gfxCanvases) {
            _0x262dc5['clearColor']();
        }
    }
    ['getMapCoord']() {
        return this.#mapCoord;
    }
};
export default logi['maps']['Layer'];