import a2_0x4d1af1 from '../common/logi-maps-defines.js?v=2.1.10.1';
import a2_0x1820af from '../common/logi-maps-types.js?v=2.1.10.1';
import a2_0x19b7a8 from '../config/lbsconfig.js?v=2.1.10.1';
import a2_0x5a8a75 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a2_0x587aec from '../utility/logi-maps-dataview.js?v=2.1.10.1';
import a2_0x4e84f4 from '../layer/logi-maps-layer.js?v=2.1.10.1';
import a2_0x1d80b6 from '../graphics/logi-maps-gfx2d.js?v=2.1.10.1';
import a2_0x160c78 from '../graphics/logi-maps-gfxgl.js?v=2.1.10.1';
import a2_0x5cb7cd from '../database/logi-maps-mtdatabase.js?v=2.1.10.1';
import a2_0x55f23e from '../library/pako/pako.mjs';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a2_0x4d1af1, logi['maps']['TileUID'] = a2_0x1820af['TileUID'], logi['maps']['Config'] = a2_0x19b7a8, logi['maps']['Utils'] = a2_0x5a8a75, logi['maps']['DataView'] = a2_0x587aec, logi['maps']['Layer'] = a2_0x4e84f4, logi['maps']['Gfx2d'] = a2_0x1d80b6, logi['maps']['Gfxgl'] = a2_0x160c78, logi['maps']['MtDatabase'] = a2_0x5cb7cd, logi['maps']['MtLayer'] = class extends logi['maps']['Layer'] {
    #remoteServerUrl = '';
    #provider = 0x0;
    #extn = logi['maps']['Defines']['MAP_TILE_EXT_PNG'];
    #region;
    #resourceManager;
    #mtDatabase;
    #tileGfxgl = null;
    #tileGfx2d = null;
    #notationGfx2d = null;
    #supportWebGL;
    #toDrawTileGroup = {
        'tiles': new Array(),
        'reset': function () {
            this['tiles'] = new Array();
        },
        'push': function (tile, tileData) {
            this['tiles']['push']({
                'tile': tile,
                'tileData': tileData
            });
        }
    };
    #toDrawVectorGLDatas = {
        'layers': new Map(),
        'reset': function () {
            this['layers'] = new Map();
        },
        'push': function (tile, width, height, layerData, mode = 'gfxgl') {
            const layerKey = layerData['drawType'] + '.' + layerData['layerName'];
            let layerValue = this['layers']['get'](layerKey);
            !layerValue && (this['layers']['set'](layerKey, {
                'layerStyle': layerData['layerStyle'],
                'datas': new Array()
            }), layerValue = this['layers']['get'](layerKey)), layerValue['datas']['push']({
                'tile': tile,
                'width': width,
                'height': height,
                'data': layerData,
                'mode': mode
            });
        },
        'sort': function () {
            this['layers'] = new Map([...this['layers']['entries']()]['sort']((a, b) => a[0x1]['layerStyle']['depth'] - b[0x1]['layerStyle']['depth']));
        }
    };
    #lastPreloadInfo = {
        'tileLevel': null,
        'cnt': null
    };
    #tileMapCache = new Map();
    #MAX_TILE_MAP_CACHE_SIZE = 0x21c;
    #tileMapsWaitingForResponse = new Map();
    #MAX_WAITING_TILE_MAPS_SIZE = 0x200;
    #MAX_WAITING_TILE_MAPS_TIME = 0xfa0;
    #notFoundTileMaps = new Map();
    #MAX_NOT_FOUND_TILE_MAPS_TIME = 0x1388;
    #MAX_TILE_MAP_PRELOAD_CNT = 0x36;
    #tileOffGfx2dPixelRatio = 1.5;
    #tileOffGfx2dCache = new Array();
    static ['TileRawData'] = class {
        ['width'] = null;
        ['height'] = null;
        ['dtmData'] = { 'href': null };
        ['layerDatas'] = new Array();
    };
    static ['LayerRawData'] = class {
        ['drawType'] = null;
        ['layerName'] = null;
        ['alpha3Code'] = null;
        ['elemDatas'] = new Array();
    };
    static ['PolygonRawData'] = class {
        ['categoryId'] = null;
        ['pathString'] = null;
    };
    static ['PolylineRawData'] = class {
        ['categoryId'] = null;
        ['pathString'] = null;
        ['extension'] = ![];
    };
    static ['SymbolRawData'] = class {
        ['categoryId'] = null;
        ['transform'] = null;
        ['image'] = {
            'x': 0x0,
            'y': 0x0,
            'transform': null
        };
        ['texts'] = new Array();
    };
    static ['TextRawData'] = class {
        ['categoryId'] = null;
        ['transform'] = null;
        ['texts'] = new Array();
    };
    static ['LayerRenderData'] = class {
        ['drawType'] = null;
        ['layerName'] = null;
        ['layerStyle'] = null;
        ['layerType'] = null;
        ['fontFamily'] = null;
        ['elemDatas'] = new Array();
    };
    static ['PolygonRenderData'] = class {
        ['categoryId'] = null;
        ['style'] = null;
        ['geometry'] = {
            'pathString': null,
            'path2d': null,
            'coords': null
        };
        ['meshBuffers'] = {};
    };
    static ['PolylineRenderData'] = class {
        ['categoryId'] = null;
        ['style'] = null;
        ['extension'] = null;
        ['geometry'] = {
            'pathString': null,
            'path2d': null,
            'coords': null
        };
        ['meshBuffers'] = {};
    };
    static ['SymbolRenderData'] = class {
        ['categoryId'] = null;
        ['style'] = null;
        ['symbol'] = null;
        ['transforms'] = {
            'trfString': null,
            'trfArray': []
        };
        ['geometry'] = {
            'image': {
                'x': 0x0,
                'y': 0x0,
                'transforms': {
                    'trfString': null,
                    'trfArray': []
                }
            },
            'texts': null
        };
        ['meshBuffers'] = {};
    };
    static ['TextRenderData'] = class {
        ['categoryId'] = null;
        ['style'] = null;
        ['transforms'] = {
            'trfString': null,
            'trfArray': []
        };
        ['geometry'] = { 'texts': null };
        ['meshBuffers'] = {};
    };
    static ['RasterTileData'] = class {
        ['type'] = 'raster';
        ['image'] = null;
    };
    static ['Vector2DTileData'] = class {
        ['type'] = 'vector_2d';
        ['width'] = null;
        ['height'] = null;
        ['offsetRatio'] = null;
        ['layerDatas'] = new Array();
        ['offGfx2d'] = {
            'baseLayer': null,
            'overLayer': null
        };
        ['dtmInfo'] = {
            'drawType': 'image',
            'layerName': 'dtm',
            'layerStyle': null,
            'image': null
        };
    };
    static ['VectorGLTileData'] = class {
        ['type'] = 'vector_gl';
        ['width'] = null;
        ['height'] = null;
        ['layerDatas'] = new Array();
        ['dtmInfo'] = {
            'drawType': 'image',
            'layerName': 'dtm',
            'layerStyle': null,
            'image': null,
            'meshBuffers': {}
        };
    };
    constructor(_0x438968, _0x429a73, _0xf86766, _0x48f873, _0x537c84, _0x41773e, _0x12927e, _0x272372, _0x2275bc) {
        if (_0x438968 == 'parent') {
            const _0x879a86 = _0x429a73['id'] + '_mtlayer';
            var _0x34c6c7 = _0x429a73['querySelector']('[id=\x22' + _0x879a86 + '\x22]');
            if (_0x34c6c7)
                console['log']('Detected\x20existing\x20div.\x20Reusing\x20the\x20div.(' + _0x879a86 + ')'), super(_0x34c6c7, _0xf86766);
            else {
                const _0x39b62f = document['createElement']('div');
                _0x429a73['appendChild'](_0x39b62f), _0x39b62f['id'] = _0x879a86, _0x39b62f['style'] = 'position:absolute;\x20width:100%;\x20height:100%;\x20overflow-y:hidden;\x20overflow-x:hidden;', super(_0x39b62f, _0xf86766);
            }
        } else
            super(_0x429a73, _0xf86766);
        this.#supportWebGL = _0x2275bc;
        this.#supportWebGL != 'none' ? (this['addGfxCanvas']('webgl'), this.#tileGfxgl = this['getGfxgl'](0x0)) : (this['addGfxCanvas']('2d'), this.#tileGfx2d = this['getGfx2d'](0x0));
        this['addGfxCanvas']('2d'), this.#notationGfx2d = this['getGfx2d'](0x1), this.#extn = _0x41773e, this.#region = _0x12927e ?? logi['maps']['Defines']['REGION_KOR'], this.#provider = _0x272372, this.#remoteServerUrl = _0x537c84, this.#resourceManager = _0x48f873, this.#mtDatabase = new logi['maps']['MtDatabase'](this.#extn);
        const _0xbc03c2 = this.#getMapDataVersionUrl(this.#provider);
        _0xbc03c2 !== '' && fetch(_0xbc03c2)['then'](_0x319bf8 => {
            return _0x319bf8['text']();
        })['then'](_0x440c74 => {
            this.#initDatabase(_0x440c74);
        })['catch'](() => {
            this.#initDatabase(null);
        }), this['setEventListener']('resize', this.#onResizeScreen);
    }
    #onResizeScreen(_0x50799d, _0xf372c7, _0x248d90) {
        this.#MAX_TILE_MAP_CACHE_SIZE = (Math['floor'](_0x50799d / _0x248d90 / logi['maps']['Defines']['TILE_W']) + 0x2) * (Math['floor'](_0xf372c7 / _0x248d90 / logi['maps']['Defines']['TILE_H']) + 0x2) * 0x8, this.#MAX_TILE_MAP_CACHE_SIZE = Math['min'](this.#MAX_TILE_MAP_CACHE_SIZE, (Math['floor'](0xf00 / logi['maps']['Defines']['TILE_W']) + 0x2) * (Math['floor'](0x870 / logi['maps']['Defines']['TILE_H']) + 0x2) * 0x8), this.#MAX_TILE_MAP_CACHE_SIZE = Math['max'](this.#MAX_TILE_MAP_CACHE_SIZE, (Math['floor'](0x500 / logi['maps']['Defines']['TILE_W']) + 0x2) * (Math['floor'](0x2d0 / logi['maps']['Defines']['TILE_H']) + 0x2) * 0x8), this.#MAX_TILE_MAP_PRELOAD_CNT = (Math['floor'](_0x50799d / _0x248d90 / logi['maps']['Defines']['TILE_W']) + 0x2) * (Math['floor'](_0xf372c7 / _0x248d90 / logi['maps']['Defines']['TILE_H']) + 0x2);
        if (this.#supportWebGL != 'none')
            this.#tileGfxgl['setDevicePixelRatio'](_0x248d90);
        else {
            if (this.#extn != logi['maps']['Defines']['MAP_TILE_EXT_PNG']) {
                if (this.#tileOffGfx2dPixelRatio != _0x248d90 * 1.5) {
                    this.#tileOffGfx2dPixelRatio = _0x248d90 * 1.5, this['clearCache'](), this.#tileOffGfx2dCache = new Array();
                    for (let _0x56cf19 = 0x0; _0x56cf19 < this.#MAX_TILE_MAP_CACHE_SIZE; ++_0x56cf19) {
                        this.#tileOffGfx2dCache['push'](this.#makeOffGfx2d());
                    }
                }
            }
        }
    }
    #makeOffGfx2d() {
        const _0x510c1c = logi['maps']['Defines']['TILE_W'] * this.#tileOffGfx2dPixelRatio, _0xd4abe8 = logi['maps']['Defines']['TILE_H'] * this.#tileOffGfx2dPixelRatio;
        return new logi['maps']['Gfx2d'](new OffscreenCanvas(_0x510c1c, _0xd4abe8));
    }
    #getClearColor() {
        const _0x36f6cd = this.#resourceManager['getColorStyle']('bg', 0x0, 0x0);
        return _0x36f6cd?.['colorData'];
    }
    #getMapDataVersionUrl(_0x27d083) {
        if (_0x27d083 === 0x0 && this.#remoteServerUrl !== '')
            return this.#remoteServerUrl + '/getmapversion?extn=' + this.#extn + '&region=' + this.#region;
        return '';
    }
    #getMapDataUrl(_0x41c102, _0x4bf87c, _0x2c9e2e, _0x6587e7) {
        logi['maps']['Defines']['ENABLE_FUTURE_FEATURE'] && (_0x4bf87c == 0x13 && (_0x4bf87c -= 0x1, _0x2c9e2e = Math['floor'](_0x2c9e2e / 0x2), _0x6587e7 = Math['floor'](_0x6587e7 / 0x2)));
        if (_0x41c102 === 0x0 && this.#remoteServerUrl !== '')
            return this.#remoteServerUrl + '/' + this.#extn + '?zoom=' + _0x4bf87c + '&tilex=' + _0x2c9e2e + '&tiley=' + _0x6587e7 + '&region=' + this.#region;
        else {
            if (_0x41c102 === 0x1)
                return 'https://mt.google.com/vt/lyrs=m&x=' + _0x2c9e2e + '&y=' + _0x6587e7 + '&z=' + _0x4bf87c;
            else {
                if (_0x41c102 === 0x2)
                    return 'https://maps.wikimedia.org/osm-intl/' + _0x4bf87c + '/' + _0x2c9e2e + '/' + _0x6587e7 + '.png';
                else {
                    if (_0x41c102 === 0x3)
                        return 'http://c.tile.stamen.com/watercolor/' + _0x4bf87c + '/' + _0x2c9e2e + '/' + _0x6587e7 + '.png';
                    else {
                        if (_0x41c102 === 0x4)
                            return 'http://a.tile.stamen.com/toner/' + _0x4bf87c + '/' + _0x2c9e2e + '/' + _0x6587e7 + '.png';
                    }
                }
            }
        }
        return '';
    }
    async #initDatabase(_0x5b84ec) {
        _0x5b84ec = _0x5b84ec ?? '000000R00', console['log']('[logi.maps]\x20api\x20version:\x20' + logi['maps']['Config']['api_version']), console['log']('[logi.maps]\x20map\x20version:\x20' + _0x5b84ec);
        try {
            await this.#mtDatabase['initDatabase']();
            const _0x1146e8 = await this.#mtDatabase['checkDataVersion'](_0x5b84ec);
            _0x1146e8 != !![] && (this['clearCache'](), this['setUpdateFlag']());
        } catch (_0x4bdc94) {
            this['clearCache'](), this['setUpdateFlag']();
        }
    }
    ['clearCache'](_0x55a46a = null) {
        for (const [_0x3ea92c, _0x4d611a] of this.#tileMapCache) {
            if (_0x55a46a != null) {
                if (_0x55a46a <= 0x0)
                    break;
                _0x55a46a -= 0x1;
            }
            _0x4d611a['type'] == 'raster' && URL['revokeObjectURL'](_0x4d611a['image']['src']);
            _0x4d611a['type'] == 'vector_2d' && (_0x4d611a['offGfx2d']['baseLayer'] && this.#tileOffGfx2dCache['push'](_0x4d611a['offGfx2d']['baseLayer']), _0x4d611a['offGfx2d']['overLayer'] && this.#tileOffGfx2dCache['push'](_0x4d611a['offGfx2d']['overLayer']));
            if (_0x4d611a['type'] == 'vector_gl') {
                for (const _0xe0800e of _0x4d611a['layerDatas']) {
                    for (const _0x1aa654 of _0xe0800e['elemDatas']) {
                        this.#tileGfxgl['deleteBuffers'](_0x1aa654['meshBuffers']);
                    }
                }
                this.#tileGfxgl['deleteBuffer'](_0x4d611a['dtmInfo']['meshBuffers']);
            }
            this.#tileMapCache['delete'](_0x3ea92c);
        }
    }
    ['preWork']() {
        if (this.#tileMapsWaitingForResponse['size'] >= this.#MAX_WAITING_TILE_MAPS_SIZE) {
            const _0x577e5f = logi['maps']['Utils']['getCurTick']();
            for (const [_0x5c3486, _0x5bf081] of this.#tileMapsWaitingForResponse) {
                _0x577e5f > _0x5bf081 + this.#MAX_WAITING_TILE_MAPS_TIME && this.#tileMapsWaitingForResponse['delete'](_0x5c3486);
            }
        }
    }
    ['postWork']() {
        this.#preloadMap(), this.#mtDatabase['putDatas'](), this.#mtDatabase['resizeDatabase']();
    }
    async #preloadMap() {
        const _0x4e258c = this['getMapCoord'](), _0x301c16 = _0x4e258c['getTileLevelOffset'](), _0x577431 = this.#MAX_TILE_MAP_PRELOAD_CNT;
        let _0xc175b3 = 0x0, _0x4dc03d = null;
        _0x301c16 > 0x1 && _0x4e258c['isZoomInMax']() == ![] && (_0xc175b3 = parseInt((_0x301c16 - 0x1) / 0.5 * _0x577431), _0x4dc03d = _0x4e258c['getLevel']() + 0x1);
        _0x301c16 < 0x1 && _0x4e258c['isZoomOutMax']() == ![] && (_0xc175b3 = parseInt((0x1 - _0x301c16) / 0.25 * _0x577431), _0x4dc03d = _0x4e258c['getLevel']() - 0x1);
        if (_0x4dc03d == null || _0xc175b3 == 0x0)
            return;
        if (this.#lastPreloadInfo['tileLevel'] == _0x4dc03d && this.#lastPreloadInfo['cnt'] == _0xc175b3)
            return;
        this.#lastPreloadInfo['tileLevel'] = null, this.#lastPreloadInfo['cnt'] = null, await this.#updateTilesToDraw({
            'preload': {
                'tileLevel': _0x4dc03d,
                'cnt': _0xc175b3
            }
        });
    }
    async ['updateCanvas']() {
        await this.#updateTilesToDraw();
    }
    async #updateTilesToDraw(_0x46b848) {
        const _0x12a937 = _0x46b848?.['preload'], _0x362a6d = this['getMapCoord'](), _0xde0de9 = new Array();
        let _0x6d5039 = _0x362a6d['getTilesOnScreen'](_0x12a937?.['tileLevel'] ? {
            'tileLevel': _0x12a937?.['tileLevel'],
            'tileLevelOffset': 0x1
        } : null);
        _0x12a937 ? _0x6d5039 = _0x6d5039['slice'](0x0, _0x12a937['cnt']) : this.#toDrawTileGroup['reset']();
        for (let _0x290d0 of _0x6d5039) {
            const _0x5793b8 = this.#tileMapCache['get'](_0x290d0['getId']());
            if (_0x5793b8)
                this.#tileMapCache['delete'](_0x290d0['getId']()), this.#tileMapCache['set'](_0x290d0['getId'](), _0x5793b8), this.#addTileToDraw(_0x290d0, _0x5793b8);
            else {
                if (this.#tileMapsWaitingForResponse['size'] >= this.#MAX_WAITING_TILE_MAPS_SIZE)
                    console['log']('[logi.maps]\x20tileMapsWaitingForResponse\x20size\x20is\x20full.');
                else {
                    const _0x1a5317 = this.#tileMapsWaitingForResponse['get'](_0x290d0['getId']()), _0x3ad846 = this.#notFoundTileMaps['get'](_0x290d0['getId']());
                    if (!_0x1a5317 && !_0x3ad846) {
                        this.#tileMapsWaitingForResponse['set'](_0x290d0['getId'](), logi['maps']['Utils']['getCurTick']()), _0xde0de9['push'](_0x290d0['getId']());
                        if (_0x12a937)
                            break;
                    }
                }
            }
        }
        _0x12a937 && (_0xde0de9['length'] == 0x0 && (this.#lastPreloadInfo['tileLevel'] = _0x12a937['tileLevel'], this.#lastPreloadInfo['cnt'] = _0x12a937['cnt']));
        const _0x5367d4 = await this.#mtDatabase['getDatas'](_0xde0de9);
        for (let _0x3c5d08 of _0x5367d4) {
            const _0x37c2c1 = new logi['maps']['TileUID']();
            _0x37c2c1['setId'](_0x3c5d08['dataKey']);
            if (_0x3c5d08['dataValue']) {
                if (this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG']) {
                    const _0x4347ac = await this.#makeRasterTileData(_0x3c5d08['dataValue']);
                    this.#addTileToDraw(_0x37c2c1, _0x4347ac);
                } else {
                    const _0x46a5b9 = this.#supportWebGL == 'none' ? await this.#makeVector2DTileData(_0x3c5d08['dataValue'], _0x37c2c1['getLevel']()) : await this.#makeVectorGLTileData(_0x3c5d08['dataValue'], _0x37c2c1['getLevel']());
                    this.#addTileToDraw(_0x37c2c1, _0x46a5b9);
                }
            } else {
                const _0x305f60 = this.#getMapDataUrl(this.#provider, _0x37c2c1['getLevel'](), _0x37c2c1['getTileX'](), _0x37c2c1['getTileY']()), _0x5beff9 = await fetch(_0x305f60), _0x130bca = parseInt(_0x5beff9['headers']['get']('content-length') ?? 0x3e7), _0x17ea3b = _0x5beff9['status'] >= 0xc8 && _0x5beff9['status'] < 0x12c || _0x5beff9['status'] == 0x130;
                if (!_0x17ea3b || _0x130bca == 0x0)
                    console['log']('[logi.maps]\x20ERROR:\x20(' + _0x5beff9['status'] + ')\x20' + _0x5beff9['statusText']), _0x130bca > 0x0 && console['log']('[logi.maps]\x20ERROR:\x20' + await _0x5beff9['text']()), this.#tileMapsWaitingForResponse['delete'](_0x37c2c1['getId']()), this.#notFoundTileMaps['set'](_0x37c2c1['getId'](), logi['maps']['Utils']['getCurTick']());
                else {
                    if (_0x130bca <= 0x20)
                        console['log']('[logi.maps]\x20MapTile(' + _0x37c2c1['getLevel']() + ',\x20' + _0x37c2c1['getTileX']() + ',\x20' + _0x37c2c1['getTileY']() + ')\x20>>\x20(' + await _0x5beff9['text']() + ')'), this.#tileMapsWaitingForResponse['delete'](_0x37c2c1['getId']()), this.#notFoundTileMaps['set'](_0x37c2c1['getId'](), logi['maps']['Utils']['getCurTick']());
                    else {
                        if (this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG']) {
                            const _0x40c571 = await _0x5beff9['blob']();
                            this.#mtDatabase['addData'](_0x37c2c1['getId'](), _0x40c571);
                            const _0x3d161b = await this.#makeRasterTileData(_0x40c571);
                            this.#addTileToDraw(_0x37c2c1, _0x3d161b);
                        } else {
                            const _0x2132e6 = await _0x5beff9['arrayBuffer'](), _0x264335 = new Uint8Array(_0x2132e6), _0x229290 = this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_XVG'] ? ((() => {
                                    const _0x1442fd = a2_0x55f23e['inflate'](_0x264335, { 'to': 'string' });
                                    return this.#parseSvgTileRawData(_0x1442fd);
                                })()) : ((() => {
                                    const _0x2d0008 = a2_0x55f23e['inflate'](_0x264335);
                                    return this.#parseBinTileRawData(_0x2d0008, _0x37c2c1['getId']());
                                })());
                            this.#mtDatabase['addData'](_0x37c2c1['getId'](), _0x229290);
                            const _0x42fa8c = this.#supportWebGL == 'none' ? await this.#makeVector2DTileData(_0x229290, _0x37c2c1['getLevel']()) : await this.#makeVectorGLTileData(_0x229290, _0x37c2c1['getLevel']());
                            this.#addTileToDraw(_0x37c2c1, _0x42fa8c);
                        }
                    }
                }
            }
        }
        {
            const _0xfea60c = logi['maps']['Utils']['getCurTick']();
            for (const [_0x291277, _0x4ae255] of this.#tileMapsWaitingForResponse) {
                _0xfea60c > _0x4ae255 + this.#MAX_WAITING_TILE_MAPS_TIME && this.#tileMapsWaitingForResponse['delete'](_0x291277);
            }
            for (const [_0x34a3d7, _0x5ae8d6] of this.#notFoundTileMaps) {
                _0xfea60c > _0x5ae8d6 + this.#MAX_NOT_FOUND_TILE_MAPS_TIME && this.#notFoundTileMaps['delete'](_0x34a3d7);
            }
        }
        if (this.#tileMapCache['size'] > this.#MAX_TILE_MAP_CACHE_SIZE) {
            let _0x459227 = parseInt(this.#MAX_TILE_MAP_CACHE_SIZE * 0.25);
            this['clearCache'](_0x459227);
        }
    }
    ['drawCanvas']() {
        const _0xeb04ee = this.#getClearColor();
        this.#extn == logi['maps']['Defines']['MAP_TILE_EXT_PNG'] ? (this.#tileGfx2d?.['clearColor'](_0xeb04ee), this.#drawRasterTileData()) : this.#supportWebGL == 'none' ? (this.#upateVector2DTileData(), this.#tileGfx2d?.['clearColor'](_0xeb04ee), this.#notationGfx2d['clearColor'](), this.#drawVector2DTileData()) : (this.#tileGfxgl['readyBufferCache'](), this.#updateVectorGLTileData(), this.#tileGfxgl?.['clearColor'](_0xeb04ee), this.#notationGfx2d['clearColor'](), this.#drawVectorGLTileData(), this.#tileGfxgl['purgeBufferCache']());
    }
    #addTileToDraw(_0x4256fe, _0x124f22) {
        const _0x1b3352 = this['getMapCoord']();
        _0x4256fe['getLevel']() == _0x1b3352['getLevel']() && (this.#toDrawTileGroup['push'](_0x4256fe, _0x124f22), this['setDrawFlag']()), this.#tileMapCache['has'](_0x4256fe['getId']()) == ![] && this.#tileMapCache['set'](_0x4256fe['getId'](), _0x124f22), this.#tileMapsWaitingForResponse['delete'](_0x4256fe['getId']());
    }
    #parseSvgTileRawData(_0x470fa3) {
        const _0x3aa1b6 = new DOMParser(), _0x17791e = _0x3aa1b6['parseFromString'](_0x470fa3, 'image/svg+xml'), _0x58be09 = _0x17791e['documentElement'], _0xd886b4 = _0x17791e['getElementsByTagName']('svg')[0x0], _0x230684 = new logi['maps']['MtLayer']['TileRawData']();
        _0x230684['width'] = parseInt(_0xd886b4?.['getAttribute']('width') ?? logi['maps']['Defines']['TILE_W']), _0x230684['height'] = parseInt(_0xd886b4?.['getAttribute']('height') ?? logi['maps']['Defines']['TILE_H']);
        for (const _0x5df9c9 of _0x58be09['children']) {
            const _0x420942 = _0x5df9c9['getAttribute']('dtype');
            let _0x41def3 = null;
            if (_0x5df9c9['tagName'] == 'image') {
                const _0x22497e = _0x5df9c9['getAttribute']('layer');
                _0x22497e == 'dtm' && (_0x230684['dtmData']['href'] = _0x5df9c9['getAttribute']('href'));
                continue;
            } else {
                if (_0x5df9c9['tagName'] == 'use') {
                    let _0x45f787 = _0x5df9c9['getAttribute']('href');
                    _0x45f787 = _0x45f787['startsWith']('#') ? _0x45f787['substring'](0x1) : _0x45f787;
                    for (const _0xd7470a of _0x58be09['children']) {
                        if (_0xd7470a['getAttribute']('id') == _0x45f787) {
                            _0x41def3 = _0xd7470a;
                            break;
                        }
                    }
                } else
                    _0x41def3 = _0x5df9c9;
            }
            if (_0x41def3)
                switch (_0x420942) {
                case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYGON']:
                    _0x230684['layerDatas']['push'](this.#parseSvgPolygonRawData(_0x41def3));
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYLINE']:
                    _0x230684['layerDatas']['push'](this.#parseSvgPolylineRawData(_0x41def3));
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_SYMBOL']:
                    _0x230684['layerDatas']['push'](this.#parseSvgSymbolRawData(_0x41def3));
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_TEXT']:
                    _0x230684['layerDatas']['push'](this.#parseSvgTextRawData(_0x41def3));
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_CURVED']:
                    _0x230684['layerDatas']['push'](this.#parseSvgCurvedRawData(_0x41def3));
                    break;
                }
        }
        return _0x230684;
    }
    #parseSvgPolygonRawData(_0x44bffa) {
        const _0x1a2491 = new logi['maps']['MtLayer']['LayerRawData']();
        _0x1a2491['drawType'] = _0x44bffa['getAttribute']('dtype'), _0x1a2491['layerName'] = _0x44bffa['getAttribute']('layer'), _0x1a2491['alpha3Code'] = _0x44bffa['getAttribute']('alpha3');
        for (const _0x348d25 of _0x44bffa['children']) {
            const _0x24f09e = new logi['maps']['MtLayer']['PolygonRawData']();
            _0x24f09e['categoryId'] = parseInt(_0x348d25['getAttribute']('cat')), _0x24f09e['pathString'] = _0x348d25['getAttribute']('d'), _0x1a2491['elemDatas']['push'](_0x24f09e);
        }
        return _0x1a2491;
    }
    #parseSvgPolylineRawData(_0x16a941) {
        const _0x2df166 = new logi['maps']['MtLayer']['LayerRawData']();
        _0x2df166['drawType'] = _0x16a941['getAttribute']('dtype'), _0x2df166['layerName'] = _0x16a941['getAttribute']('layer'), _0x2df166['alpha3Code'] = _0x16a941['getAttribute']('alpha3');
        for (const _0x302d1f of _0x16a941['children']) {
            const _0x2695a4 = new logi['maps']['MtLayer']['PolylineRawData']();
            _0x2695a4['categoryId'] = parseInt(_0x302d1f['getAttribute']('cat')), _0x2695a4['pathString'] = _0x302d1f['getAttribute']('d'), _0x2695a4['extension'] = parseInt(_0x302d1f['getAttribute']('ext') ?? 0x0) != 0x0 ? !![] : ![], _0x2df166['elemDatas']['push'](_0x2695a4);
        }
        return _0x2df166;
    }
    #parseSvgSymbolRawData(_0x26f443) {
        const _0x139fad = new logi['maps']['MtLayer']['LayerRawData']();
        _0x139fad['drawType'] = _0x26f443['getAttribute']('dtype'), _0x139fad['layerName'] = _0x26f443['getAttribute']('layer'), _0x139fad['alpha3Code'] = _0x26f443['getAttribute']('alpha3');
        for (const _0x198fea of _0x26f443['children']) {
            const _0x50be54 = new logi['maps']['MtLayer']['SymbolRawData']();
            _0x50be54['categoryId'] = parseInt(_0x198fea['getAttribute']('cat')), _0x50be54['poiCode'] = _0x198fea['getAttribute']('pcode'), _0x50be54['transform'] = _0x198fea['getAttribute']('transform');
            for (const _0x1377e8 of _0x198fea['children']) {
                _0x1377e8['tagName'] == 'image' && (_0x50be54['image']['x'] = parseFloat(_0x1377e8['getAttribute']('x')), _0x50be54['image']['y'] = parseFloat(_0x1377e8['getAttribute']('y')), _0x50be54['image']['transform'] = _0x1377e8['getAttribute']('transform')), _0x1377e8['tagName'] == 'text' && _0x50be54['texts']['push']({
                    'x': parseFloat(_0x1377e8['getAttribute']('x')),
                    'y': parseFloat(_0x1377e8['getAttribute']('y')),
                    'textContent': _0x1377e8['textContent']
                });
            }
            _0x139fad['elemDatas']['push'](_0x50be54);
        }
        return _0x139fad;
    }
    #parseSvgTextRawData(_0x24b7da) {
        const _0x1572d7 = new logi['maps']['MtLayer']['LayerRawData']();
        _0x1572d7['drawType'] = _0x24b7da['getAttribute']('dtype'), _0x1572d7['layerName'] = _0x24b7da['getAttribute']('layer'), _0x1572d7['alpha3Code'] = _0x24b7da['getAttribute']('alpha3');
        for (const _0x183940 of _0x24b7da['children']) {
            const _0x349e4a = new logi['maps']['MtLayer']['TextRawData']();
            _0x349e4a['categoryId'] = parseInt(_0x183940['getAttribute']('cat')), _0x349e4a['poiCode'] = _0x183940['getAttribute']('pcode'), _0x349e4a['transform'] = _0x183940['getAttribute']('transform');
            for (const _0x53b94d of _0x183940['children']) {
                _0x53b94d['tagName'] == 'text' && _0x349e4a['texts']['push']({
                    'transform': _0x53b94d['getAttribute']('transform'),
                    'x': parseFloat(_0x53b94d['getAttribute']('x')),
                    'y': parseFloat(_0x53b94d['getAttribute']('y')),
                    'textContent': _0x53b94d['textContent']
                });
            }
            _0x1572d7['elemDatas']['push'](_0x349e4a);
        }
        return _0x1572d7;
    }
    #parseSvgCurvedRawData(_0x1a077e) {
        const _0x4ed1c9 = new logi['maps']['MtLayer']['LayerRawData']();
        _0x4ed1c9['drawType'] = _0x1a077e['getAttribute']('dtype'), _0x4ed1c9['layerName'] = _0x1a077e['getAttribute']('layer'), _0x4ed1c9['alpha3Code'] = _0x1a077e['getAttribute']('alpha3');
        for (const _0x587499 of _0x1a077e['children']) {
            const _0x28d2b5 = new logi['maps']['MtLayer']['TextRawData']();
            _0x28d2b5['categoryId'] = parseInt(_0x587499['getAttribute']('cat')), _0x28d2b5['transform'] = _0x587499['getAttribute']('transform');
            for (const _0x3dd730 of _0x587499['children']) {
                _0x3dd730['tagName'] == 'text' && _0x28d2b5['texts']['push']({
                    'transform': _0x3dd730['getAttribute']('transform'),
                    'x': parseFloat(_0x3dd730['getAttribute']('x')),
                    'y': parseFloat(_0x3dd730['getAttribute']('y')),
                    'textContent': _0x3dd730['textContent']
                });
            }
            _0x4ed1c9['elemDatas']['push'](_0x28d2b5);
        }
        return _0x4ed1c9;
    }
    #parseBinTileRawData(_0x3e8082, _0x316c4b) {
        const _0x1e2574 = new logi['maps']['DataView'](new DataView(_0x3e8082['buffer'])), _0x184eda = new logi['maps']['MtLayer']['TileRawData']();
        _0x184eda['width'] = _0x1e2574['getUint16'](), _0x184eda['height'] = _0x1e2574['getUint16'](), _0x1e2574['getUint16'](), _0x1e2574['getUint8']();
        let _0x5f243f = ![];
        while (_0x1e2574['getRemainingBytes']() > 0x3) {
            if (_0x5f243f)
                break;
            const _0x2c4d8f = _0x1e2574['getString'](0x3);
            switch (_0x2c4d8f) {
            case 'b64': {
                    const _0xdfaf70 = _0x1e2574['getString8']();
                    _0xdfaf70 == 'dtm' ? _0x184eda['dtmData']['href'] = _0x1e2574['getString16']() : (console['log']('[logi.maps]\x20broken\x20map\x20data(bin.b64):\x20' + _0x316c4b), _0x5f243f = !![]);
                }
                break;
            case 'grp': {
                    const _0x27b89e = _0x1e2574['getString'](0x3);
                    switch (_0x27b89e) {
                    case 'pgn': {
                            _0x184eda['layerDatas']['push'](this.#parseBinPolygonRawData(_0x1e2574));
                        }
                        break;
                    case 'pln': {
                            _0x184eda['layerDatas']['push'](this.#parseBinPolylineRawData(_0x1e2574));
                        }
                        break;
                    case 'sym': {
                            _0x184eda['layerDatas']['push'](this.#parseBinSymbolRawData(_0x1e2574));
                        }
                        break;
                    case 'txt': {
                            _0x184eda['layerDatas']['push'](this.#parseBinTextRawData(_0x1e2574));
                        }
                        break;
                    case 'cur': {
                            _0x184eda['layerDatas']['push'](this.#parseBinCurvedRawData(_0x1e2574));
                        }
                        break;
                    default: {
                            console['log']('[logi.maps]\x20broken\x20map\x20data(bin.grp):\x20' + _0x316c4b), _0x5f243f = !![];
                        }
                        break;
                    }
                }
                break;
            default: {
                    console['log']('[logi.maps]\x20broken\x20map\x20data(bin):\x20' + _0x316c4b), _0x5f243f = !![];
                }
                break;
            }
        }
        return _0x184eda;
    }
    #parseBinPolygonRawData(_0x159f70) {
        const _0x147400 = new logi['maps']['MtLayer']['LayerRawData']();
        _0x147400['drawType'] = 'polygon', _0x147400['layerName'] = _0x159f70['getString8'](), _0x147400['alpha3Code'] = _0x159f70['getString'](0x3), _0x159f70['getString8']();
        const _0x2491db = _0x159f70['getUint8']();
        for (let _0x29f904 = 0x0; _0x29f904 < _0x2491db; ++_0x29f904) {
            const _0x29a97f = new logi['maps']['MtLayer']['PolygonRawData']();
            _0x159f70['getString'](0x3), _0x29a97f['categoryId'] = _0x159f70['getInt32'](), _0x29a97f['pathString'] = '';
            const _0x88b110 = _0x159f70['getUint8']();
            for (let _0x298323 = 0x0; _0x298323 < _0x88b110; ++_0x298323) {
                const _0x2cd31f = _0x159f70['getUint16']();
                for (let _0x3c2a1c = 0x0; _0x3c2a1c < _0x2cd31f; ++_0x3c2a1c) {
                    _0x3c2a1c == 0x0 ? (_0x298323 > 0x0 && (_0x29a97f['pathString'] += '\x20'), _0x29a97f['pathString'] += 'm' + _0x159f70['getInt16']() + ',' + _0x159f70['getInt16']() + '\x20') : _0x29a97f['pathString'] += _0x159f70['getInt8']() + ',' + _0x159f70['getInt8']() + '\x20', _0x3c2a1c + 0x1 == _0x2cd31f && (_0x29a97f['pathString'] += 'z');
                }
            }
            _0x147400['elemDatas']['push'](_0x29a97f);
        }
        return _0x147400;
    }
    #parseBinPolylineRawData(_0x50abd9) {
        const _0x6d5f35 = new logi['maps']['MtLayer']['LayerRawData']();
        _0x6d5f35['drawType'] = 'polyline', _0x6d5f35['layerName'] = _0x50abd9['getString8'](), _0x6d5f35['alpha3Code'] = _0x50abd9['getString'](0x3), _0x50abd9['getString8']();
        const _0x28ce25 = _0x50abd9['getUint8']();
        for (let _0x8d95d9 = 0x0; _0x8d95d9 < _0x28ce25; ++_0x8d95d9) {
            const _0x2b2b5b = new logi['maps']['MtLayer']['PolylineRawData']();
            _0x50abd9['getString'](0x3), _0x2b2b5b['categoryId'] = _0x50abd9['getInt32'](), _0x2b2b5b['extension'] = _0x50abd9['getInt8']() != 0x0 ? !![] : ![], _0x2b2b5b['pathString'] = '';
            const _0x20a4c7 = _0x50abd9['getUint8']();
            for (let _0x33726d = 0x0; _0x33726d < _0x20a4c7; ++_0x33726d) {
                const _0x3d33fd = _0x50abd9['getUint16']();
                for (let _0x3f197e = 0x0; _0x3f197e < _0x3d33fd; ++_0x3f197e) {
                    _0x3f197e == 0x0 ? (_0x33726d > 0x0 && (_0x2b2b5b['pathString'] += '\x20'), _0x2b2b5b['pathString'] += 'm' + _0x50abd9['getInt16']() + ',' + _0x50abd9['getInt16']() + '\x20') : _0x2b2b5b['pathString'] += _0x50abd9['getInt8']() + ',' + _0x50abd9['getInt8']() + '\x20';
                }
            }
            _0x6d5f35['elemDatas']['push'](_0x2b2b5b);
        }
        return _0x6d5f35;
    }
    #parseBinSymbolRawData(_0x52a538) {
        const _0x3065ea = new logi['maps']['MtLayer']['LayerRawData']();
        _0x3065ea['drawType'] = 'symbol', _0x3065ea['layerName'] = _0x52a538['getString8'](), _0x3065ea['alpha3Code'] = _0x52a538['getString'](0x3), _0x52a538['getString8']();
        const _0x521d51 = _0x52a538['getUint8']();
        for (let _0xedbd63 = 0x0; _0xedbd63 < _0x521d51; ++_0xedbd63) {
            const _0x7bba40 = new logi['maps']['MtLayer']['SymbolRawData']();
            _0x52a538['getString'](0x3), _0x7bba40['categoryId'] = _0x52a538['getInt32']();
            {
                _0x7bba40['transform'] = '';
                const _0x49fb24 = _0x52a538['getUint8']();
                for (let _0x2d54fa = 0x0; _0x2d54fa < _0x49fb24; ++_0x2d54fa) {
                    const _0x1496a7 = _0x52a538['getString'](0x2);
                    if (_0x1496a7 == 't2') {
                        const _0x1c4d66 = _0x52a538['getInt16'](), _0x55411e = _0x52a538['getInt16']();
                        _0x7bba40['transform']['length'] > 0x0 && (_0x7bba40['transform'] += '\x20'), _0x7bba40['transform'] += 'translate(' + _0x1c4d66 + ',' + _0x55411e + ')';
                    }
                }
            }
            {
                _0x52a538['getString'](0x3), _0x7bba40['image']['x'] = _0x52a538['getInt16'](), _0x7bba40['image']['y'] = _0x52a538['getInt16'](), _0x7bba40['image']['transform'] = '';
                const _0x485b21 = _0x52a538['getUint8']();
                for (let _0x294ef8 = 0x0; _0x294ef8 < _0x485b21; ++_0x294ef8) {
                    const _0x258822 = _0x52a538['getString'](0x2);
                    if (_0x258822 == 's1') {
                        const _0xfb2153 = _0x52a538['getFloat32']();
                        _0x7bba40['image']['transform']['length'] > 0x0 && (_0x7bba40['image']['transform'] += '\x20'), _0x7bba40['image']['transform'] += 'scale(' + _0xfb2153 + ')';
                    } else {
                        if (_0x258822 == 'r1') {
                            const _0x33b1b7 = _0x52a538['getInt16']();
                            _0x7bba40['image']['transform']['length'] > 0x0 && (_0x7bba40['image']['transform'] += '\x20'), _0x7bba40['image']['transform'] += 'rotate(' + _0x33b1b7 + ')';
                        }
                    }
                }
            }
            {
                const _0x562940 = _0x52a538['getUint8']();
                for (let _0x49715d = 0x0; _0x49715d < _0x562940; ++_0x49715d) {
                    _0x52a538['getString'](0x3), _0x7bba40['texts']['push']({
                        'x': _0x52a538['getInt16'](),
                        'y': _0x52a538['getInt16'](),
                        'textContent': _0x52a538['getString8']()
                    });
                }
            }
            _0x3065ea['elemDatas']['push'](_0x7bba40);
        }
        return _0x3065ea;
    }
    #parseBinTextRawData(_0x179254) {
        const _0x1ccced = new logi['maps']['MtLayer']['LayerRawData']();
        _0x1ccced['drawType'] = 'text', _0x1ccced['layerName'] = _0x179254['getString8'](), _0x1ccced['alpha3Code'] = _0x179254['getString'](0x3), _0x179254['getString8']();
        const _0x199e90 = _0x179254['getUint8']();
        for (let _0x630647 = 0x0; _0x630647 < _0x199e90; ++_0x630647) {
            const _0x1ff107 = new logi['maps']['MtLayer']['TextRawData']();
            _0x179254['getString'](0x3), _0x1ff107['categoryId'] = _0x179254['getInt32']();
            {
                _0x1ff107['transform'] = '';
                const _0x5539ce = _0x179254['getUint8']();
                for (let _0x25d0ed = 0x0; _0x25d0ed < _0x5539ce; ++_0x25d0ed) {
                    const _0x177b3d = _0x179254['getString'](0x2);
                    if (_0x177b3d == 'r3') {
                        const _0x1d1d70 = _0x179254['getInt16'](), _0x2fe5d0 = _0x179254['getInt16'](), _0x5a12f2 = _0x179254['getInt16']();
                        _0x1ff107['transform']['length'] > 0x0 && (_0x1ff107['transform'] += '\x20'), _0x1ff107['transform'] += 'roate(' + _0x1d1d70 + ',' + _0x2fe5d0 + ',' + _0x5a12f2 + ')';
                    }
                }
            }
            {
                const _0x5a94a9 = _0x179254['getUint8']();
                for (let _0x388af6 = 0x0; _0x388af6 < _0x5a94a9; ++_0x388af6) {
                    _0x179254['getString'](0x3);
                    let _0x5ede47 = '';
                    const _0x246f43 = _0x179254['getUint8']();
                    for (let _0x370d77 = 0x0; _0x370d77 < _0x246f43; ++_0x370d77) {
                        const _0x4ff0b6 = _0x179254['getString'](0x2);
                        if (_0x4ff0b6 == 'r3') {
                            const _0x55e18e = _0x179254['getInt16'](), _0xb8a8d0 = _0x179254['getInt16'](), _0x4b67e1 = _0x179254['getInt16']();
                            _0x5ede47['length'] > 0x0 && (_0x5ede47 += '\x20'), _0x5ede47 += 'roate(' + _0x55e18e + ',' + _0xb8a8d0 + ',' + _0x4b67e1 + ')';
                        }
                    }
                    _0x1ff107['texts']['push']({
                        'transform': _0x5ede47,
                        'x': _0x179254['getInt16'](),
                        'y': _0x179254['getInt16'](),
                        'textContent': _0x179254['getString8']()
                    });
                }
            }
            _0x1ccced['elemDatas']['push'](_0x1ff107);
        }
        return _0x1ccced;
    }
    #parseBinCurvedRawData(_0x2dd20e) {
        const _0x2712d2 = new logi['maps']['MtLayer']['LayerRawData']();
        _0x2712d2['drawType'] = 'curved', _0x2712d2['layerName'] = _0x2dd20e['getString8'](), _0x2712d2['alpha3Code'] = _0x2dd20e['getString'](0x3), _0x2dd20e['getString8']();
        const _0x99f983 = _0x2dd20e['getUint8']();
        for (let _0x2c0231 = 0x0; _0x2c0231 < _0x99f983; ++_0x2c0231) {
            const _0x27e7d3 = new logi['maps']['MtLayer']['TextRawData']();
            _0x2dd20e['getString'](0x3), _0x27e7d3['categoryId'] = _0x2dd20e['getInt32']();
            {
                _0x27e7d3['transform'] = '';
                const _0x5cb3e7 = _0x2dd20e['getUint8']();
                for (let _0x204bd4 = 0x0; _0x204bd4 < _0x5cb3e7; ++_0x204bd4) {
                    const _0x35d22c = _0x2dd20e['getString'](0x2);
                    if (_0x35d22c == 'r3') {
                        const _0x199046 = _0x2dd20e['getInt16'](), _0x431af6 = _0x2dd20e['getInt16'](), _0x2f534d = _0x2dd20e['getInt16']();
                        _0x27e7d3['transform']['length'] > 0x0 && (_0x27e7d3['transform'] += '\x20'), _0x27e7d3['transform'] += 'roate(' + _0x199046 + ',' + _0x431af6 + ',' + _0x2f534d + ')';
                    }
                }
            }
            {
                const _0x914cf7 = _0x2dd20e['getUint8']();
                for (let _0x2383a2 = 0x0; _0x2383a2 < _0x914cf7; ++_0x2383a2) {
                    _0x2dd20e['getString'](0x3);
                    let _0x1457c8 = '';
                    const _0x49b654 = _0x2dd20e['getUint8']();
                    for (let _0x532a70 = 0x0; _0x532a70 < _0x49b654; ++_0x532a70) {
                        const _0x21205a = _0x2dd20e['getString'](0x2);
                        if (_0x21205a == 'r3') {
                            const _0x5e61d7 = _0x2dd20e['getInt16'](), _0xc601e2 = _0x2dd20e['getInt16'](), _0x1a966a = _0x2dd20e['getInt16']();
                            _0x1457c8['length'] > 0x0 && (_0x1457c8 += '\x20'), _0x1457c8 += 'roate(' + _0x5e61d7 + ',' + _0xc601e2 + ',' + _0x1a966a + ')';
                        }
                    }
                    _0x27e7d3['texts']['push']({
                        'transform': _0x1457c8,
                        'x': _0x2dd20e['getInt16'](),
                        'y': _0x2dd20e['getInt16'](),
                        'textContent': _0x2dd20e['getString8']()
                    });
                }
            }
            _0x2712d2['elemDatas']['push'](_0x27e7d3);
        }
        return _0x2712d2;
    }
    #makeRasterTileData(_0x33e5d7) {
        return new Promise(_0x5c7c99 => {
            const _0x2a2fc5 = new Image();
            _0x2a2fc5['src'] = URL['createObjectURL'](_0x33e5d7), _0x2a2fc5['onload'] = () => {
                const _0x49b893 = new logi['maps']['MtLayer']['RasterTileData']();
                _0x49b893['image'] = _0x2a2fc5, _0x5c7c99(_0x49b893);
            };
        });
    }
    #drawRasterTileData() {
        for (const {
                    tile: _0x3f07f6,
                    tileData: _0x568937
                } of this.#toDrawTileGroup['tiles']) {
            const _0x4771b8 = _0x568937['image'], _0x1a098d = this['getMapCoord'](), _0x5af134 = this['getDevicePixelRatio'](), _0x490d33 = _0x1a098d['getTileLevelOffset'](), _0x26fd2e = _0x1a098d['tileXY2screen'](_0x3f07f6['getTileX'](), _0x3f07f6['getTileY'](), _0x3f07f6['getLevel'](), _0x490d33), _0x2de5ff = 0.5, _0x2baf1c = _0x26fd2e['x'], _0x178ce = _0x26fd2e['y'], _0x395c18 = logi['maps']['Defines']['TILE_W'] * _0x490d33 + _0x2de5ff, _0x576ad6 = logi['maps']['Defines']['TILE_H'] * _0x490d33 + _0x2de5ff;
            this.#tileGfx2d['save'](), this.#tileGfx2d['scale'](_0x5af134, _0x5af134), this.#tileGfx2d['drawImage'](_0x4771b8, _0x2baf1c, _0x178ce, _0x395c18, _0x576ad6), this.#tileGfx2d['restore']();
        }
    }
    #makeVector2DTileData(_0x53cb92, _0x5a7ff9) {
        return new Promise(_0x1e0c12 => {
            const _0x188348 = { 'baseSide': !![] }, _0x3f7bc7 = new logi['maps']['MtLayer']['Vector2DTileData']();
            _0x3f7bc7['width'] = _0x53cb92['width'], _0x3f7bc7['height'] = _0x53cb92['height'];
            for (const _0x4c2b00 of _0x53cb92['layerDatas']) {
                switch (_0x4c2b00['drawType']) {
                case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYGON']: {
                        const _0x17c9e1 = this.#makeVector2DPolygonData(_0x4c2b00, _0x5a7ff9, _0x188348);
                        _0x17c9e1['elemDatas']['length'] > 0x0 && _0x3f7bc7['layerDatas']['push'](_0x17c9e1);
                    }
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYLINE']: {
                        const _0x241d59 = this.#makeVector2DPolylineData(_0x4c2b00, _0x5a7ff9, _0x188348);
                        _0x241d59['elemDatas']['length'] > 0x0 && _0x3f7bc7['layerDatas']['push'](_0x241d59);
                    }
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_SYMBOL']: {
                        const _0x5c9e1c = this.#makeVector2DSymbolData(_0x4c2b00, _0x5a7ff9, _0x188348);
                        _0x5c9e1c['elemDatas']['length'] > 0x0 && _0x3f7bc7['layerDatas']['push'](_0x5c9e1c);
                    }
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_TEXT']: {
                        const _0x421b40 = this.#makeVector2DTextData(_0x4c2b00, _0x5a7ff9, _0x188348);
                        _0x421b40['elemDatas']['length'] > 0x0 && _0x3f7bc7['layerDatas']['push'](_0x421b40);
                    }
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_CURVED']: {
                        const _0xef7bb3 = this.#makeVector2DCurvedData(_0x4c2b00, _0x5a7ff9, _0x188348);
                        _0xef7bb3['elemDatas']['length'] > 0x0 && _0x3f7bc7['layerDatas']['push'](_0xef7bb3);
                    }
                    break;
                }
            }
            _0x3f7bc7['layerDatas']['sort']((_0x593a2f, _0x149278) => _0x593a2f['layerStyle']['depth'] - _0x149278['layerStyle']['depth']);
            if (_0x53cb92['dtmData']?.['href']) {
                const _0x53fcc0 = new Image();
                _0x53fcc0['src'] = _0x53cb92['dtmData']['href'], _0x53fcc0['onload'] = () => {
                    _0x3f7bc7['dtmInfo']['layerStyle'] = this.#resourceManager['getLayerStyle']('image', 'dtm'), _0x3f7bc7['dtmInfo']['image'] = _0x53fcc0, _0x1e0c12(_0x3f7bc7);
                };
            } else
                _0x1e0c12(_0x3f7bc7);
        });
    }
    #makeVector2DPolygonData(_0xe3f5d8, _0x573940, _0xa6caff) {
        const _0x59d8b0 = new logi['maps']['MtLayer']['LayerRenderData']();
        _0x59d8b0['drawType'] = _0xe3f5d8['drawType'], _0x59d8b0['layerName'] = _0xe3f5d8['layerName'], _0x59d8b0['layerStyle'] = this.#resourceManager['getLayerStyle']('polygon', _0xe3f5d8['layerName']);
        for (const _0x19d128 of _0xe3f5d8['elemDatas']) {
            const _0xf6219d = this.#resourceManager['getPolygonStyle'](_0xe3f5d8['layerName'], _0x19d128['categoryId'], _0x573940);
            if (_0xf6219d) {
                const _0x2be0d5 = new logi['maps']['MtLayer']['PolygonRenderData']();
                _0x2be0d5['categoryId'] = _0x19d128['categoryId'], _0x2be0d5['style'] = _0xf6219d, _0x2be0d5['geometry']['pathString'] = _0x19d128['pathString'], _0x59d8b0['elemDatas']['push'](_0x2be0d5);
            }
        }
        return _0x59d8b0['layerType'] = _0xa6caff['baseSide'] ? 'base' : 'over', _0x59d8b0;
    }
    #makeVector2DPolylineData(_0xb031e6, _0x418f40, _0x2a7881) {
        const _0x49b707 = new logi['maps']['MtLayer']['LayerRenderData']();
        _0x49b707['drawType'] = _0xb031e6['drawType'], _0x49b707['layerName'] = _0xb031e6['layerName'], _0x49b707['layerStyle'] = this.#resourceManager['getLayerStyle']('polyline', _0xb031e6['layerName']);
        for (const _0x368a69 of _0xb031e6['elemDatas']) {
            const _0x4dfee5 = this.#resourceManager['getPolylineStyle'](_0xb031e6['layerName'], _0x368a69['categoryId'], _0x418f40);
            if (_0x4dfee5) {
                if (this.#supportWebGL == 'none' || _0x368a69['extension'] == ![]) {
                    const _0x3c21de = new logi['maps']['MtLayer']['PolylineRenderData']();
                    _0x3c21de['categoryId'] = _0x368a69['categoryId'], _0x3c21de['style'] = _0x4dfee5, _0x3c21de['extension'] = _0x368a69['extension'], _0x3c21de['geometry']['pathString'] = _0x368a69['pathString'], _0x49b707['elemDatas']['push'](_0x3c21de), _0x2a7881['baseSide'] && _0x4dfee5['fixedScale'] == !![] && (_0x2a7881['baseSide'] = ![]);
                }
            }
        }
        return _0x49b707['layerType'] = _0x2a7881['baseSide'] ? 'base' : 'over', _0x49b707;
    }
    #makeVector2DSymbolData(_0xf8b8f3, _0x423715, _0x517de4) {
        const _0x314252 = new logi['maps']['MtLayer']['LayerRenderData']();
        _0x314252['drawType'] = _0xf8b8f3['drawType'], _0x314252['layerName'] = _0xf8b8f3['layerName'], _0x314252['layerStyle'] = this.#resourceManager['getLayerStyle']('symbol', _0xf8b8f3['layerName']);
        for (const _0x382f05 of _0xf8b8f3['elemDatas']) {
            const _0x54e8f4 = this.#resourceManager['getSymbolStyle'](_0xf8b8f3['layerName'], _0x382f05['categoryId'], _0x382f05['poiCode'], _0x423715);
            if (_0x54e8f4) {
                const _0x1ac65f = new logi['maps']['MtLayer']['SymbolRenderData']();
                _0x1ac65f['categoryId'] = _0x382f05['categoryId'], _0x1ac65f['style'] = _0x54e8f4, _0x1ac65f['symbol'] = this.#resourceManager['getSymbol'](_0x54e8f4['iconGroup'], _0x54e8f4['iconId']), _0x1ac65f['transforms']['trfString'] = structuredClone(_0x382f05['transform']), _0x1ac65f['geometry']['image']['x'] = _0x382f05['image']['x'], _0x1ac65f['geometry']['image']['y'] = _0x382f05['image']['y'], _0x1ac65f['geometry']['image']['transforms']['trfString'] = _0x382f05['image']['transform'], _0x1ac65f['geometry']['texts'] = structuredClone(_0x382f05['texts']), _0x314252['elemDatas']['push'](_0x1ac65f), _0x517de4['baseSide'] && _0x54e8f4['fixedScale'] == !![] && (_0x517de4['baseSide'] = ![]);
            }
        }
        return _0x314252['layerType'] = _0x517de4['baseSide'] ? 'base' : 'notation', _0x314252['fontFamily'] = this.#resourceManager['getFontFamily'](_0xf8b8f3['alpha3Code']), _0x314252;
    }
    #makeVector2DTextData(_0x54e2be, _0x3db3c4, _0x56f1f4) {
        const _0x4a9e07 = new logi['maps']['MtLayer']['LayerRenderData']();
        _0x4a9e07['drawType'] = _0x54e2be['drawType'], _0x4a9e07['layerName'] = _0x54e2be['layerName'], _0x4a9e07['layerStyle'] = this.#resourceManager['getLayerStyle']('text', _0x54e2be['layerName']);
        for (const _0x5c04b4 of _0x54e2be['elemDatas']) {
            const _0x4c908a = this.#resourceManager['getTextStyle'](_0x54e2be['layerName'], _0x5c04b4['categoryId'], _0x5c04b4['poiCode'], _0x3db3c4);
            if (_0x4c908a) {
                const _0x13f9f9 = new logi['maps']['MtLayer']['TextRenderData']();
                _0x13f9f9['categoryId'] = _0x5c04b4['categoryId'], _0x13f9f9['style'] = _0x4c908a, _0x13f9f9['transforms']['trfString'] = structuredClone(_0x5c04b4['transform']), _0x13f9f9['geometry']['texts'] = structuredClone(_0x5c04b4['texts']), _0x4a9e07['elemDatas']['push'](_0x13f9f9), _0x56f1f4['baseSide'] && _0x4c908a['fixedScale'] == !![] && (_0x56f1f4['baseSide'] = ![]);
            }
        }
        return _0x4a9e07['layerType'] = _0x56f1f4['baseSide'] ? 'base' : 'notation', _0x4a9e07['fontFamily'] = this.#resourceManager['getFontFamily'](_0x54e2be['alpha3Code']), _0x4a9e07;
    }
    #makeVector2DCurvedData(_0x334678, _0x20631f, _0x467bcf) {
        const _0x94855 = new logi['maps']['MtLayer']['LayerRenderData']();
        _0x94855['drawType'] = _0x334678['drawType'], _0x94855['layerName'] = _0x334678['layerName'], _0x94855['layerStyle'] = this.#resourceManager['getLayerStyle']('text', _0x334678['layerName']);
        for (const _0x564b28 of _0x334678['elemDatas']) {
            const _0x27fb70 = this.#resourceManager['getTextStyle'](_0x334678['layerName'], _0x564b28['categoryId'], _0x564b28['poiCode'], _0x20631f);
            if (_0x27fb70) {
                const _0x15abe1 = new logi['maps']['MtLayer']['TextRenderData']();
                _0x15abe1['categoryId'] = _0x564b28['categoryId'], _0x15abe1['style'] = _0x27fb70, _0x15abe1['transforms']['trfString'] = structuredClone(_0x564b28['transform']), _0x15abe1['geometry']['texts'] = structuredClone(_0x564b28['texts']), _0x94855['elemDatas']['push'](_0x15abe1), _0x467bcf['baseSide'] && _0x27fb70['fixedScale'] == !![] && (_0x467bcf['baseSide'] = ![]);
            }
        }
        return _0x94855['layerType'] = _0x467bcf['baseSide'] ? 'base' : 'notation', _0x94855['fontFamily'] = this.#resourceManager['getFontFamily'](_0x334678['alpha3Code']), _0x94855;
    }
    #upateVector2DTileData() {
        const _0x5c3638 = this['getMapCoord'](), _0x4c9fb6 = _0x5c3638['getTileLevelOffset']();
        let _0x48c784 = 0x1;
        if (_0x4c9fb6 <= 0.875)
            _0x48c784 = 0.83334;
        else
            _0x4c9fb6 >= 1.25 && (_0x48c784 = 1.33334);
        for (const {
                    tile: _0x5c9dbe,
                    tileData: _0x260c73
                } of this.#toDrawTileGroup['tiles']) {
            const _0x28e534 = _0x260c73['offGfx2d'];
            if (_0x28e534['baseLayer'] == null) {
                const _0x42e84a = this.#getClearColor();
                _0x28e534['baseLayer'] = this.#tileOffGfx2dCache['length'] > 0x0 ? this.#tileOffGfx2dCache['pop']() : this.#makeOffGfx2d(), _0x28e534['baseLayer']['save'](), _0x28e534['baseLayer']['scale'](this.#tileOffGfx2dPixelRatio, this.#tileOffGfx2dPixelRatio), _0x28e534['baseLayer']['clearColor'](_0x42e84a);
                if (logi['maps']['Defines']['ENABLE_FUTURE_FEATURE']) {
                    if (_0x5c9dbe['getLevel']() == 0x13) {
                        const _0x40c3cf = _0x5c9dbe['getTileX']() % 0x2, _0x52cb42 = _0x5c9dbe['getTileY']() % 0x2;
                        _0x28e534['baseLayer']['scale'](0x2, 0x2), _0x28e534['baseLayer']['translate'](_0x40c3cf == 0x0 ? 0x0 : _0x260c73['width'] * -0.5, _0x52cb42 == 0x0 ? 0x0 : _0x260c73['height'] * -0.5);
                    }
                }
                let _0x571fc1 = !![];
                for (const _0x46d8e7 of _0x260c73['layerDatas']) {
                    _0x46d8e7['layerType'] == 'over' && (_0x571fc1 = ![]);
                    if (_0x571fc1 == !![] && _0x46d8e7['layerType'] != 'notation')
                        switch (_0x46d8e7['drawType']) {
                        case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYGON']:
                            _0x28e534['baseLayer']['drawSvgPolygons'](_0x46d8e7);
                            break;
                        case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYLINE']:
                            _0x28e534['baseLayer']['drawSvgPolylines'](_0x46d8e7);
                            break;
                        case logi['maps']['Defines']['MAP_DRAW_TYPE_SYMBOL']:
                            _0x28e534['baseLayer']['drawSvgSymbols'](_0x46d8e7);
                            break;
                        case logi['maps']['Defines']['MAP_DRAW_TYPE_TEXT']:
                            _0x28e534['baseLayer']['drawSvgTexts'](_0x46d8e7);
                            break;
                        case logi['maps']['Defines']['MAP_DRAW_TYPE_CURVED']:
                            _0x28e534['baseLayer']['drawSvgCurveds'](_0x46d8e7);
                            break;
                        }
                }
                _0x28e534['baseLayer']['restore']();
            }
            if (_0x260c73['offsetRatio'] != _0x48c784) {
                _0x28e534['overLayer'] == null && (_0x28e534['overLayer'] = this.#tileOffGfx2dCache['length'] > 0x0 ? this.#tileOffGfx2dCache['pop']() : this.#makeOffGfx2d());
                _0x260c73['offsetRatio'] = _0x48c784, _0x28e534['overLayer']['save'](), _0x28e534['overLayer']['scale'](this.#tileOffGfx2dPixelRatio, this.#tileOffGfx2dPixelRatio), _0x28e534['overLayer']['clearColor'](), _0x28e534['overLayer']['drawImage'](_0x28e534['baseLayer'], 0x0, 0x0, _0x260c73['width'], _0x260c73['height']);
                if (logi['maps']['Defines']['ENABLE_FUTURE_FEATURE']) {
                    if (_0x5c9dbe['getLevel']() == 0x13) {
                        const _0xb51f0f = _0x5c9dbe['getTileX']() % 0x2, _0x3c227a = _0x5c9dbe['getTileY']() % 0x2;
                        _0x28e534['overLayer']['scale'](0x2, 0x2), _0x28e534['overLayer']['translate'](_0xb51f0f == 0x0 ? 0x0 : _0x260c73['width'] * -0.5, _0x3c227a == 0x0 ? 0x0 : _0x260c73['height'] * -0.5);
                    }
                }
                let _0x8bdbad = ![];
                for (const _0x3efbb4 of _0x260c73['layerDatas']) {
                    _0x3efbb4['layerType'] == 'over' && (_0x8bdbad = !![]);
                    if (_0x8bdbad == !![] && _0x3efbb4['layerType'] != 'notation')
                        switch (_0x3efbb4['drawType']) {
                        case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYGON']:
                            _0x28e534['overLayer']['drawSvgPolygons'](_0x3efbb4);
                            break;
                        case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYLINE']:
                            _0x28e534['overLayer']['drawSvgPolylines'](_0x3efbb4, _0x48c784);
                            break;
                        }
                }
                _0x260c73['dtmInfo']['image'] && _0x28e534['overLayer']['drawDtm'](_0x260c73['dtmInfo'], 0x0, 0x0, _0x260c73['width'], _0x260c73['height']), _0x28e534['overLayer']['restore']();
            }
        }
    }
    #drawVector2DTileData() {
        const _0x538256 = this['getMapCoord'](), _0x2e1c7c = this['getDevicePixelRatio'](), _0x3a8399 = _0x538256['getTileLevelOffset'](), _0x30014f = [];
        for (const {
                    tile: _0x396cf0,
                    tileData: _0x5047d1
                } of this.#toDrawTileGroup['tiles']) {
            const _0x29c793 = _0x538256['tileXY2screen'](_0x396cf0['getTileX'](), _0x396cf0['getTileY'](), _0x396cf0['getLevel'](), _0x3a8399);
            logi['maps']['Defines']['ENABLE_FUTURE_FEATURE'] && (tileLevel == 0x13 && _0x30014f['parentLevelTiles']['push']({
                'tx': Math['floor'](_0x396cf0['getTileX']() / 0x2),
                'ty': Math['floor'](_0x396cf0['getTileY']() / 0x2)
            }));
            const _0x354a06 = 0.5, _0x50ecc6 = _0x29c793['x'], _0x1f8b8d = _0x29c793['y'], _0x5476ff = logi['maps']['Defines']['TILE_W'] * _0x3a8399 + _0x354a06, _0x240202 = logi['maps']['Defines']['TILE_H'] * _0x3a8399 + _0x354a06, _0x5b2361 = _0x5047d1['offGfx2d'];
            this.#tileGfx2d['save'](), this.#tileGfx2d['scale'](_0x2e1c7c, _0x2e1c7c), this.#tileGfx2d['drawImage'](_0x5b2361['overLayer'], _0x50ecc6, _0x1f8b8d, _0x5476ff, _0x240202), this.#tileGfx2d['restore'](), this.#notationGfx2d['save'](), this.#notationGfx2d['scale'](_0x2e1c7c, _0x2e1c7c), this.#notationGfx2d['translate'](_0x29c793['x'], _0x29c793['y']);
            let _0x34a031 = _0x3a8399;
            if (logi['maps']['Defines']['ENABLE_FUTURE_FEATURE']) {
                if (_0x396cf0['getLevel']() == 0x13) {
                    const _0x2501ef = _0x396cf0['getTileX']() % 0x2, _0x348b70 = _0x396cf0['getTileY']() % 0x2;
                    _0x34a031 = _0x3a8399 * 0x2, this.#notationGfx2d['translate'](_0x2501ef == 0x0 ? 0x0 : _0x5047d1['width'] * -0.5 * _0x34a031, _0x348b70 == 0x0 ? 0x0 : _0x5047d1['height'] * -0.5 * _0x34a031);
                }
            }
            for (const _0x3a6672 of _0x5047d1['layerDatas']) {
                if (_0x3a6672['layerType'] == 'notation')
                    switch (_0x3a6672['drawType']) {
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_SYMBOL']:
                        this.#notationGfx2d['drawSvgSymbols'](_0x3a6672, _0x34a031);
                        break;
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_TEXT']:
                        this.#notationGfx2d['drawSvgTexts'](_0x3a6672, _0x34a031);
                        break;
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_CURVED']:
                        this.#notationGfx2d['drawSvgCurveds'](_0x3a6672, _0x34a031);
                        break;
                    }
            }
            this.#notationGfx2d['restore']();
        }
    }
    #makeVectorGLTileData(_0x299447, _0x3de67c) {
        return new Promise(_0x54f0ee => {
            const _0x3624d1 = new logi['maps']['MtLayer']['VectorGLTileData']();
            _0x3624d1['width'] = _0x299447['width'], _0x3624d1['height'] = _0x299447['height'];
            for (const _0x25b046 of _0x299447['layerDatas']) {
                switch (_0x25b046['drawType']) {
                case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYGON']: {
                        const _0x1d2ba2 = this.#makeVectorGLPolygonData(_0x25b046, _0x3de67c);
                        _0x1d2ba2['elemDatas']['length'] > 0x0 && _0x3624d1['layerDatas']['push'](_0x1d2ba2);
                    }
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYLINE']: {
                        const _0x4ab0d7 = this.#makeVectorGLPolylineData(_0x25b046, _0x3de67c);
                        _0x4ab0d7['elemDatas']['length'] > 0x0 && _0x3624d1['layerDatas']['push'](_0x4ab0d7);
                    }
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_SYMBOL']: {
                        const _0x1ca65a = this.#makeVectorGLSymbolData(_0x25b046, _0x3de67c);
                        _0x1ca65a['elemDatas']['length'] > 0x0 && _0x3624d1['layerDatas']['push'](_0x1ca65a);
                    }
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_TEXT']: {
                        const _0x523382 = this.#makeVectorGLTextData(_0x25b046, _0x3de67c);
                        _0x523382['elemDatas']['length'] > 0x0 && _0x3624d1['layerDatas']['push'](_0x523382);
                    }
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_CURVED']: {
                        const _0x4a66e3 = this.#makeVectorGLCurvedData(_0x25b046, _0x3de67c);
                        _0x4a66e3['elemDatas']['length'] > 0x0 && _0x3624d1['layerDatas']['push'](_0x4a66e3);
                    }
                    break;
                }
            }
            if (_0x299447['dtmData']?.['href']) {
                const _0x454c05 = new Image();
                _0x454c05['src'] = _0x299447['dtmData']['href'], _0x454c05['onload'] = () => {
                    _0x3624d1['dtmInfo']['layerStyle'] = this.#resourceManager['getLayerStyle']('image', 'dtm'), _0x3624d1['dtmInfo']['image'] = _0x454c05, _0x54f0ee(_0x3624d1);
                };
            } else
                _0x54f0ee(_0x3624d1);
        });
    }
    #makeVectorGLPolygonData(_0x49f5be, _0x3fadaa) {
        const _0x32b852 = new logi['maps']['MtLayer']['LayerRenderData']();
        _0x32b852['drawType'] = _0x49f5be['drawType'], _0x32b852['layerName'] = _0x49f5be['layerName'], _0x32b852['layerStyle'] = this.#resourceManager['getLayerStyle']('polygon', _0x49f5be['layerName']);
        for (const _0x284b9d of _0x49f5be['elemDatas']) {
            const _0x3ff474 = this.#resourceManager['getPolygonStyle'](_0x49f5be['layerName'], _0x284b9d['categoryId'], _0x3fadaa);
            if (_0x3ff474) {
                const _0x268a0f = new logi['maps']['MtLayer']['PolygonRenderData']();
                _0x268a0f['categoryId'] = _0x284b9d['categoryId'], _0x268a0f['style'] = _0x3ff474, _0x268a0f['geometry']['pathString'] = _0x284b9d['pathString'], _0x32b852['elemDatas']['push'](_0x268a0f);
            }
        }
        return _0x32b852;
    }
    #makeVectorGLPolylineData(_0x3a8adf, _0x3eeca0) {
        const _0x40fd92 = new logi['maps']['MtLayer']['LayerRenderData']();
        _0x40fd92['drawType'] = _0x3a8adf['drawType'], _0x40fd92['layerName'] = _0x3a8adf['layerName'], _0x40fd92['layerStyle'] = this.#resourceManager['getLayerStyle']('polyline', _0x3a8adf['layerName']);
        for (const _0x1fea9d of _0x3a8adf['elemDatas']) {
            const _0x3d3a19 = this.#resourceManager['getPolylineStyle'](_0x3a8adf['layerName'], _0x1fea9d['categoryId'], _0x3eeca0);
            if (_0x3d3a19) {
                if (this.#supportWebGL == 'none' || _0x1fea9d['extension'] == ![]) {
                    const _0x5a9881 = new logi['maps']['MtLayer']['PolylineRenderData']();
                    _0x5a9881['categoryId'] = _0x1fea9d['categoryId'], _0x5a9881['style'] = _0x3d3a19, _0x5a9881['extension'] = _0x1fea9d['extension'], _0x5a9881['geometry']['pathString'] = _0x1fea9d['pathString'], _0x40fd92['elemDatas']['push'](_0x5a9881);
                }
            }
        }
        return _0x40fd92;
    }
    #makeVectorGLSymbolData(_0x562050, _0x383078) {
        const _0x300e88 = new logi['maps']['MtLayer']['LayerRenderData']();
        _0x300e88['drawType'] = _0x562050['drawType'], _0x300e88['layerName'] = _0x562050['layerName'], _0x300e88['layerStyle'] = this.#resourceManager['getLayerStyle']('symbol', _0x562050['layerName']);
        for (const _0x4a938b of _0x562050['elemDatas']) {
            const _0x2984d2 = this.#resourceManager['getSymbolStyle'](_0x562050['layerName'], _0x4a938b['categoryId'], _0x4a938b['poiCode'], _0x383078);
            if (_0x2984d2) {
                const _0x4ca407 = new logi['maps']['MtLayer']['SymbolRenderData']();
                _0x4ca407['categoryId'] = _0x4a938b['categoryId'], _0x4ca407['style'] = _0x2984d2, _0x4ca407['symbol'] = this.#resourceManager['getSymbol'](_0x2984d2['iconGroup'], _0x2984d2['iconId']), _0x4ca407['transforms']['trfString'] = structuredClone(_0x4a938b['transform']), _0x4ca407['geometry']['image']['x'] = _0x4a938b['image']['x'], _0x4ca407['geometry']['image']['y'] = _0x4a938b['image']['y'], _0x4ca407['geometry']['image']['transforms']['trfString'] = _0x4a938b['image']['transform'], _0x4ca407['geometry']['texts'] = structuredClone(_0x4a938b['texts']), _0x300e88['elemDatas']['push'](_0x4ca407);
            }
        }
        return _0x300e88['fontFamily'] = this.#resourceManager['getFontFamily'](_0x562050['alpha3Code']), _0x300e88;
    }
    #makeVectorGLTextData(_0x2b3753, _0x33e6ea) {
        const _0x56fb8f = new logi['maps']['MtLayer']['LayerRenderData']();
        _0x56fb8f['drawType'] = _0x2b3753['drawType'], _0x56fb8f['layerName'] = _0x2b3753['layerName'], _0x56fb8f['layerStyle'] = this.#resourceManager['getLayerStyle']('text', _0x2b3753['layerName']);
        for (const _0x51a142 of _0x2b3753['elemDatas']) {
            const _0x29736f = this.#resourceManager['getTextStyle'](_0x2b3753['layerName'], _0x51a142['categoryId'], _0x51a142['poiCode'], _0x33e6ea);
            if (_0x29736f) {
                const _0x52e3d2 = new logi['maps']['MtLayer']['TextRenderData']();
                _0x52e3d2['categoryId'] = _0x51a142['categoryId'], _0x52e3d2['style'] = _0x29736f, _0x52e3d2['transforms']['trfString'] = structuredClone(_0x51a142['transform']), _0x52e3d2['geometry']['texts'] = structuredClone(_0x51a142['texts']), _0x56fb8f['elemDatas']['push'](_0x52e3d2);
            }
        }
        return _0x56fb8f['fontFamily'] = this.#resourceManager['getFontFamily'](_0x2b3753['alpha3Code']), _0x56fb8f;
    }
    #makeVectorGLCurvedData(_0x1a8ff3, _0x3d1ad4) {
        const _0x49b9ac = new logi['maps']['MtLayer']['LayerRenderData']();
        _0x49b9ac['drawType'] = _0x1a8ff3['drawType'], _0x49b9ac['layerName'] = _0x1a8ff3['layerName'], _0x49b9ac['layerStyle'] = this.#resourceManager['getLayerStyle']('text', _0x1a8ff3['layerName']);
        for (const _0x11c161 of _0x1a8ff3['elemDatas']) {
            const _0x4113bb = this.#resourceManager['getTextStyle'](_0x1a8ff3['layerName'], _0x11c161['categoryId'], _0x11c161['poiCode'], _0x3d1ad4);
            if (_0x4113bb) {
                const _0x17f2db = new logi['maps']['MtLayer']['TextRenderData']();
                _0x17f2db['categoryId'] = _0x11c161['categoryId'], _0x17f2db['style'] = _0x4113bb, _0x17f2db['transforms']['trfString'] = structuredClone(_0x11c161['transform']), _0x17f2db['geometry']['texts'] = structuredClone(_0x11c161['texts']), _0x49b9ac['elemDatas']['push'](_0x17f2db);
            }
        }
        return _0x49b9ac['fontFamily'] = this.#resourceManager['getFontFamily'](_0x1a8ff3['alpha3Code']), _0x49b9ac;
    }
    #updateVectorGLTileData() {
        const _0x116ab5 = this['getMapCoord'](), _0x419fdf = _0x116ab5['getTileLevelOffset']();
        let _0x524a2e = 0x1;
        if (_0x419fdf <= 0.875)
            _0x524a2e = 0.83334;
        else
            _0x419fdf >= 1.25 && (_0x524a2e = 1.33334);
        this.#toDrawVectorGLDatas['reset']();
        for (const {
                    tile: _0x417d4f,
                    tileData: _0x194a8f
                } of this.#toDrawTileGroup['tiles']) {
            for (const _0x565ae2 of _0x194a8f['layerDatas']) {
                switch (_0x565ae2['drawType']) {
                case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYGON']:
                    this.#tileGfxgl['bindSvgPolygonBuffers'](_0x565ae2), this.#toDrawVectorGLDatas['push'](_0x417d4f, _0x194a8f['width'], _0x194a8f['height'], _0x565ae2, 'gfxgl');
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYLINE']:
                    this.#tileGfxgl['bindSvgPolyLineBuffers'](_0x565ae2, _0x524a2e), this.#toDrawVectorGLDatas['push'](_0x417d4f, _0x194a8f['width'], _0x194a8f['height'], _0x565ae2, 'gfxgl');
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_SYMBOL']:
                    this.#supportWebGL == 'all' ? (this.#tileGfxgl['bindSvgSymbolBuffers'](_0x565ae2), this.#toDrawVectorGLDatas['push'](_0x417d4f, _0x194a8f['width'], _0x194a8f['height'], _0x565ae2, 'gfxgl')) : this.#toDrawVectorGLDatas['push'](_0x417d4f, _0x194a8f['width'], _0x194a8f['height'], _0x565ae2, 'gfx2d');
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_TEXT']:
                    this.#supportWebGL == 'all' ? (this.#tileGfxgl['bindSvgTextBuffers'](_0x565ae2), this.#toDrawVectorGLDatas['push'](_0x417d4f, _0x194a8f['width'], _0x194a8f['height'], _0x565ae2, 'gfxgl')) : this.#toDrawVectorGLDatas['push'](_0x417d4f, _0x194a8f['width'], _0x194a8f['height'], _0x565ae2, 'gfx2d');
                    break;
                case logi['maps']['Defines']['MAP_DRAW_TYPE_CURVED']:
                    this.#supportWebGL == 'all' ? (this.#tileGfxgl['bindSvgCurvedBuffers'](_0x565ae2), this.#toDrawVectorGLDatas['push'](_0x417d4f, _0x194a8f['width'], _0x194a8f['height'], _0x565ae2, 'gfxgl')) : this.#toDrawVectorGLDatas['push'](_0x417d4f, _0x194a8f['width'], _0x194a8f['height'], _0x565ae2, 'gfx2d');
                    break;
                }
            }
            _0x194a8f['dtmInfo']['image'] && (this.#tileGfxgl['bindSvgDtmBuffer'](_0x194a8f['dtmInfo'], _0x194a8f['width'], _0x194a8f['height']), this.#toDrawVectorGLDatas['push'](_0x417d4f, _0x194a8f['width'], _0x194a8f['height'], _0x194a8f['dtmInfo'], 'gfxgl'));
        }
        this.#toDrawVectorGLDatas['sort']();
    }
    #drawVectorGLTileData() {
        const _0x1552e5 = this['getMapCoord'](), _0x3a317f = this['getDevicePixelRatio'](), _0x3ab477 = _0x1552e5['getTileLevelOffset']();
        for (const [, _0x561063] of this.#toDrawVectorGLDatas['layers']) {
            for (const {
                        tile: _0x222fed,
                        width: _0x2f8c79,
                        height: _0x2ebf2b,
                        data: _0x4756d2,
                        mode: _0xabc33
                    } of _0x561063['datas']) {
                const _0x60e79c = _0x1552e5['tileXY2screen'](_0x222fed['getTileX'](), _0x222fed['getTileY'](), _0x222fed['getLevel'](), _0x3ab477);
                if (_0xabc33 == 'gfx2d') {
                    this.#notationGfx2d['save'](), this.#notationGfx2d['scale'](_0x3a317f, _0x3a317f), this.#notationGfx2d['translate'](_0x60e79c['x'], _0x60e79c['y']);
                    let _0x59f348 = _0x3ab477;
                    if (logi['maps']['Defines']['ENABLE_FUTURE_FEATURE']) {
                        if (_0x222fed['getLevel']() == 0x13) {
                            const _0xe2398d = _0x222fed['getTileX']() % 0x2, _0xccf421 = _0x222fed['getTileY']() % 0x2;
                            _0x59f348 = _0x3ab477 * 0x2, this.#notationGfx2d['translate'](_0xe2398d == 0x0 ? 0x0 : _0x2f8c79 * -0.5 * _0x59f348, _0xccf421 == 0x0 ? 0x0 : _0x2ebf2b * -0.5 * _0x59f348);
                        }
                    }
                    switch (_0x4756d2['drawType']) {
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_SYMBOL']:
                        this.#notationGfx2d['drawSvgSymbols'](_0x4756d2, _0x59f348);
                        break;
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_TEXT']:
                        this.#notationGfx2d['drawSvgTexts'](_0x4756d2, _0x59f348);
                        break;
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_CURVED']:
                        this.#notationGfx2d['drawSvgCurveds'](_0x4756d2, _0x59f348);
                        break;
                    }
                    this.#notationGfx2d['restore']();
                } else {
                    this.#tileGfxgl['reset'](), this.#tileGfxgl['scale'](_0x3ab477, _0x3ab477), this.#tileGfxgl['setOrigin'](_0x60e79c['x'], _0x60e79c['y']);
                    let _0x4b4d1c = _0x3ab477;
                    if (logi['maps']['Defines']['ENABLE_FUTURE_FEATURE']) {
                        if (_0x222fed['getLevel']() == 0x13) {
                            const _0x55eaa4 = _0x222fed['getTileX']() % 0x2, _0x631d95 = _0x222fed['getTileY']() % 0x2;
                            _0x4b4d1c = _0x3ab477 * 0x2, this.#tileGfxgl['scale'](0x2, 0x2), this.#tileGfxgl['translate'](_0x55eaa4 == 0x0 ? 0x0 : _0x2f8c79 * -0.5, _0x631d95 == 0x0 ? 0x0 : _0x2ebf2b * -0.5);
                        }
                    }
                    switch (_0x4756d2['drawType']) {
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYGON']:
                        this.#tileGfxgl['drawSvgPolygonBuffers'](_0x4756d2);
                        break;
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_POLYLINE']:
                        this.#tileGfxgl['drawSvgPolyLineBuffers'](_0x4756d2);
                        break;
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_SYMBOL']:
                        this.#tileGfxgl['drawSvgSymbolBuffers'](_0x4756d2, _0x4b4d1c);
                        break;
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_TEXT']:
                        this.#tileGfxgl['drawSvgTextBuffers'](_0x4756d2, _0x4b4d1c);
                        break;
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_CURVED']:
                        this.#tileGfxgl['drawSvgCurvedBuffers'](_0x4756d2, _0x4b4d1c);
                        break;
                    case logi['maps']['Defines']['MAP_DRAW_TYPE_IMAGE']:
                        _0x4756d2['layerName'] == 'dtm' && this.#tileGfxgl['drawSvgDtmBuffer'](_0x4756d2);
                        break;
                    }
                }
            }
        }
    }
};
export default logi['maps']['MtLayer'];