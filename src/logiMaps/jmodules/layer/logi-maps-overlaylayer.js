import a4_0x5a95e1 from '../common/logi-maps-types.js?v=2.1.10.1';
import a4_0x45fd65 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a4_0x2ac2c7 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a4_0x3405ad from '../utility/logi-maps-boundarychecker.js?v=2.1.10.1';
import a4_0x56bfaf from '../object/logi-maps-object.js?v=2.1.10.1';
import a4_0x4db5ec from '../layer/logi-maps-layer.js?v=2.1.10.1';
import a4_0x57cdd3 from '../graphics/logi-maps-gfx2d.js?v=2.1.10.1';
import a4_0x2e0b14 from '../graphics/logi-maps-gfxgl.js?v=2.1.10.1';
import a4_0x43d9d0 from '../handler/logi-maps-customhandler.js?v=2.1.10.1';
import a4_0x3736a7 from '../handler/logi-maps-metahandler.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectsInTile'] = a4_0x5a95e1['ObjectsInTile'], logi['maps']['OBJEVENT'] = a4_0x5a95e1['OBJEVENT'], logi['maps']['ALIGN'] = a4_0x5a95e1['ALIGN'], logi['maps']['Utils'] = a4_0x45fd65, logi['maps']['BoundaryData'] = a4_0x2ac2c7, logi['maps']['BoundaryChecker'] = a4_0x3405ad, logi['maps']['Object'] = a4_0x56bfaf, logi['maps']['Layer'] = a4_0x4db5ec, logi['maps']['Gfx2d'] = a4_0x57cdd3, logi['maps']['Gfxgl'] = a4_0x2e0b14, logi['maps']['CustomHandler'] = a4_0x43d9d0, logi['maps']['MetaHandler'] = a4_0x3736a7, logi['maps']['OverlayLayer'] = class extends logi['maps']['Layer'] {
    #divElem;
    #customHandler;
    #metaHandler;
    #overlapCheck;
    #overlapInfoVisibility;
    constructor(_0x4f400a, _0x1db645, _0x1a17c2) {
        if (_0x4f400a == 'parent') {
            const _0x35a497 = _0x1db645['id'] + '_overlaylayer';
            var _0x4c94d1 = _0x1db645['querySelector']('[id=\x22' + _0x35a497 + '\x22]');
            if (_0x4c94d1)
                console['log']('Detected\x20existing\x20div.\x20Reusing\x20the\x20div.(' + _0x35a497 + ')'), super(_0x4c94d1, _0x1a17c2);
            else {
                const _0x2f0680 = document['createElement']('div');
                _0x1db645['appendChild'](_0x2f0680), _0x2f0680['id'] = _0x35a497, _0x2f0680['style'] = 'position:absolute;\x20width:100%;\x20height:100%;\x20overflow-y:hidden;\x20overflow-x:hidden;', super(_0x2f0680, _0x1a17c2), this.#divElem = _0x2f0680;
            }
        } else
            super(_0x1db645, _0x1a17c2), this.#divElem = _0x1db645;
        this.#customHandler = new logi['maps']['CustomHandler'](_0x1a17c2), this.#metaHandler = new logi['maps']['MetaHandler'](_0x1a17c2), this.#overlapCheck = !![], this.#overlapInfoVisibility = ![];
    }
    ['getDivElem']() {
        return this.#divElem;
    }
    ['setOverlapCheck'](_0xe962f6) {
        this.#overlapCheck != _0xe962f6 && (this.#overlapCheck = _0xe962f6, this['setUpdateFlag']());
    }
    ['setOverlapInfoVisibility'](_0x12be18) {
        this.#overlapInfoVisibility != _0x12be18 && (this.#overlapInfoVisibility = _0x12be18, this['setUpdateFlag']());
    }
    ['updateCanvas']() {
        this['setDrawFlag']();
    }
    ['drawCanvas']() {
        this.#drawObjects();
    }
    ['removeAll'](_0x547595) {
        this['removeCustomAll'](_0x547595);
    }
    ['addCustom'](_0x4289a2) {
        if (this.#customHandler['addCustom'](_0x4289a2, this) == !![])
            return this['setUpdateFlag'](), _0x4289a2;
        return null;
    }
    ['removeCustom'](_0x4e3f80) {
        this.#customHandler['removeCustom'](_0x4e3f80) == !![] && this['setUpdateFlag']();
    }
    ['removeCustomAll'](_0x5cb0f0 = []) {
        this.#customHandler['removeCustomAll'](_0x5cb0f0) == !![] && this['setUpdateFlag']();
    }
    ['addMeta'](_0x5275e7) {
        if (this.#metaHandler['addMeta'](_0x5275e7, this) == !![])
            return this['setUpdateFlag'](), _0x5275e7;
        return null;
    }
    ['removeMeta'](_0x1dcb67) {
        this.#metaHandler['removeMeta'](_0x1dcb67) == !![] && this['setUpdateFlag']();
    }
    #drawObjects() {
        const _0x4839d8 = this.#customHandler['getDrawObjects']();
        for (const _0x4ccea6 of _0x4839d8) {
            _0x4ccea6['drawCanvas']();
        }
        const _0x21e464 = this.#metaHandler['getDrawObjects']();
        for (const _0x4c0a35 of _0x21e464) {
            _0x4c0a35['drawCanvas']();
        }
    }
};
export default logi['maps']['OverlayLayer'];