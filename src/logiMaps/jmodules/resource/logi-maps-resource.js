import a0_0x590979 from '../common/logi-maps-defines.js?v=2.1.10.1';
import a0_0x2e1d7f from '../database/logi-maps-resdatabase.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a0_0x590979, logi['maps']['ResDatabase'] = a0_0x2e1d7f, logi['maps']['Resource'] = class {
    static #MAX_IMAGE_CACHE_CNT = 0x200;
    static #imageCache = new Map();
    static #imageCalls = new Map();
    #updateFlag = ![];
    #resourceUrl = '';
    #resDatabase;
    #extn = logi['maps']['Defines']['MAP_TILE_EXT_XVG'];
    #region = logi['maps']['Defines']['REGION_KOR'];
    #resTheme = '';
    #resUserStyle = ![];
    #symbolGroups = new Map();
    #mapStyle = null;
    #xmlStyleText = null;
    #MAX_LOADED_FONTS_CNT = 0x40;
    #loadedFonts = new Set();
    #disableLayers = new Map();
    #disableCategories = new Map();
    #disablePoiCodes = new Map();
    #alignCodeDic = new Map([
        [
            0xb,
            'left-top'
        ],
        [
            0x15,
            'center-top'
        ],
        [
            0x1f,
            'right-top'
        ],
        [
            0xc,
            'left-middle'
        ],
        [
            0x16,
            'center-middle'
        ],
        [
            0x20,
            'right-middle'
        ],
        [
            0xd,
            'left-bottom'
        ],
        [
            0x17,
            'center-bottom'
        ],
        [
            0x21,
            'right-bottom'
        ]
    ]);
    constructor(_0x5215c4, _0x23e17c, _0x419a35, _0x4b345b) {
        this.#resourceUrl = _0x5215c4, this.#resDatabase = new logi['maps']['ResDatabase'](), this.#extn = _0x23e17c ?? logi['maps']['Defines']['MAP_TILE_EXT_XVG'], this.#region = _0x419a35 ?? logi['maps']['Defines']['REGION_KOR'];
        if (this.#extn != logi['maps']['Defines']['MAP_TILE_EXT_PNG']) {
            const _0x1a7eb9 = this.#getResourceVersionUrl();
            _0x1a7eb9 !== '' && fetch(_0x1a7eb9)['then'](_0x5eba8d => {
                return _0x5eba8d['text']();
            })['then'](_0x636d58 => {
                this.#initDatabase(_0x636d58, _0x4b345b);
            })['catch'](() => {
                this.#initDatabase(null, _0x4b345b);
            });
        }
    }
    ['setUpdateFlag'](_0x1dd226) {
        this.#updateFlag = _0x1dd226;
    }
    ['getUpdateFlag']() {
        return this.#updateFlag;
    }
    async #initDatabase(_0x59ed31, _0x535e12) {
        _0x59ed31 = _0x59ed31 ?? '0.00.000', console['log']('[logi.maps]\x20res\x20version:\x20' + _0x59ed31);
        try {
            await this.#resDatabase['initDatabase'](), await this.#resDatabase['checkDataVersion'](_0x59ed31), this['setTheme'](_0x535e12);
        } catch (_0x4c3176) {
            this['setTheme'](_0x535e12);
        }
    }
    #getResourceVersionUrl() {
        if (this.#resourceUrl !== '')
            return this.#resourceUrl + '/res?extn=' + this.#extn + '&region=' + this.#region + '&path=version';
        return '';
    }
    #getResourceDataUrl(_0x239d70) {
        if (this.#resourceUrl !== '')
            return this.#resourceUrl + '/res?extn=' + this.#extn + '&region=' + this.#region + '&path=' + this.#resTheme + '/' + _0x239d70;
        return '';
    }
    static ['getImage'](_0x4482b7) {
        if (this.#imageCache['size'] >= this.#MAX_IMAGE_CACHE_CNT) {
            let _0x3ffdd9 = this.#MAX_IMAGE_CACHE_CNT * 0.25;
            for (const [_0x2c9c83] of this.#imageCache) {
                this.#imageCache['delete'](_0x2c9c83);
                if (--_0x3ffdd9 <= 0x0)
                    break;
            }
            this.#imageCalls['clear']();
        }
        return new Promise((_0x2d6f99, _0x32ad69) => {
            const _0x5dc9b0 = _0x3b5dba => {
                if (_0x3b5dba > 0x0 && this.#imageCalls['get'](_0x4482b7))
                    setTimeout(_0x5dc9b0, 0x1f4, --_0x3b5dba);
                else {
                    if (!_0x4482b7 || _0x4482b7 == '')
                        this.#imageCalls['delete'](_0x4482b7), _0x32ad69();
                    else {
                        const _0x3fe998 = this.#imageCache['get'](_0x4482b7);
                        _0x3fe998 ? (this.#imageCalls['get'](_0x4482b7) && this.#imageCalls['delete'](_0x4482b7), _0x2d6f99(_0x3fe998)) : (this.#imageCalls['set'](_0x4482b7, 'ready'), this.#requestImage(_0x4482b7, 0x2)['then'](_0x2857a5 => {
                            this.#imageCache['set'](_0x4482b7, _0x2857a5), this.#imageCalls['delete'](_0x4482b7), _0x2d6f99(_0x2857a5);
                        })['catch'](_0x328fda => {
                            this.#imageCalls['delete'](_0x4482b7), _0x32ad69(_0x328fda);
                        }));
                    }
                }
            };
            setTimeout(_0x5dc9b0, 0x0, 0x4);
        });
    }
    static #requestImage(_0x2c55d3, _0x4a5f12) {
        return new Promise(_0x500793 => {
            const _0x4b5f4e = new Image();
            _0x4b5f4e['crossOrigin'] = 'Anonymous', _0x4b5f4e['onload'] = () => {
                _0x500793(_0x4b5f4e);
            }, _0x4b5f4e['onerror'] = _0x15f841 => {
                if (_0x4a5f12 <= 0x0)
                    throw _0x15f841;
                else
                    setTimeout(async () => {
                        const _0x4e0d2f = await this.#requestImage(_0x2c55d3, --_0x4a5f12);
                        return _0x4e0d2f;
                    }, 0x3e8);
            }, _0x4b5f4e['src'] = _0x2c55d3;
        });
    }
    async ['setTheme'](_0xc6e4a6) {
        _0xc6e4a6 = _0xc6e4a6 ?? 'default', (this.#resTheme != _0xc6e4a6 || this.#resUserStyle == !![]) && (this.#resTheme = _0xc6e4a6, this.#resUserStyle = ![], await this.#loadStyle());
    }
    ['getTheme']() {
        return this.#resTheme;
    }
    async ['setUserStyle'](_0xd21e96) {
        !_0xd21e96 ? await this['setTheme'](this.#resTheme) : (this.#resUserStyle = !![], await this.#parseXmlText(_0xd21e96));
    }
    ['getCurrStyle']() {
        return this.#xmlStyleText;
    }
    ['showLayer'](_0x3c83bb, _0x2a608d) {
        let _0x8ceaa = null;
        if (_0x2a608d?.['codeType'] === 'category') {
            if (_0x2a608d?.['codeList'] && _0x2a608d['codeList']['length'] > 0x0)
                for (const _0xfe2762 of _0x2a608d['codeList']) {
                    _0x8ceaa = _0x3c83bb + ':c.' + _0xfe2762, this.#disableCategories['has'](_0x8ceaa) && (this.#disableCategories['delete'](_0x8ceaa), this.#updateFlag = !![]);
                }
        } else {
            if (_0x2a608d?.['codeType'] === 'poi') {
                if (_0x2a608d?.['codeList'] && _0x2a608d['codeList']['length'] > 0x0)
                    for (const _0x3aeea1 of _0x2a608d['codeList']) {
                        _0x8ceaa = _0x3c83bb + ':p.' + _0x3aeea1, this.#disablePoiCodes['has'](_0x8ceaa) && (this.#disablePoiCodes['delete'](_0x8ceaa), this.#updateFlag = !![]);
                    }
            } else
                _0x8ceaa = '' + _0x3c83bb, this.#disableLayers['has'](_0x8ceaa) && (this.#disableLayers['delete'](_0x8ceaa), this.#updateFlag = !![]);
        }
    }
    ['hideLayer'](_0x316877, _0xad5d10) {
        let _0xaab3f3 = null;
        if (_0xad5d10?.['codeType'] === 'category') {
            if (_0xad5d10?.['codeList'] && _0xad5d10['codeList']['length'] > 0x0)
                for (const _0x78a918 of _0xad5d10['codeList']) {
                    _0xaab3f3 = _0x316877 + ':c.' + _0x78a918, !this.#disableCategories['has'](_0xaab3f3) && (this.#disableCategories['set'](_0xaab3f3, {}), this.#updateFlag = !![]);
                }
        } else {
            if (_0xad5d10?.['codeType'] === 'poi') {
                if (_0xad5d10?.['codeList'] && _0xad5d10['codeList']['length'] > 0x0)
                    for (const _0x39aaa1 of _0xad5d10['codeList']) {
                        _0xaab3f3 = _0x316877 + ':p.' + _0x39aaa1, !this.#disablePoiCodes['has'](_0xaab3f3) && (this.#disablePoiCodes['set'](_0xaab3f3, {}), this.#updateFlag = !![]);
                    }
            } else
                _0xaab3f3 = '' + _0x316877, !this.#disableLayers['has'](_0xaab3f3) && (this.#disableLayers['set'](_0xaab3f3, {}), this.#updateFlag = !![]);
        }
    }
    #isLayerVisible(_0x4e38aa, _0x56f739, _0x2894ae) {
        if (this.#disableLayers['has'](_0x4e38aa))
            return ![];
        if (_0x56f739 && this.#disableCategories['size'] > 0x0) {
            const _0x2f25e3 = _0x4e38aa + ':c.' + _0x56f739;
            if (this.#disableCategories['has'](_0x2f25e3))
                return ![];
        }
        if (_0x2894ae && this.#disablePoiCodes['size'] > 0x0) {
            const _0x45dcc0 = _0x4e38aa + ':p.' + _0x2894ae;
            for (const _0x3f76f7 of this.#disablePoiCodes['keys']()) {
                if (_0x45dcc0['startsWith'](_0x3f76f7))
                    return ![];
            }
        }
        return !![];
    }
    ['addFont'](_0x345c98, _0x3be7c2) {
        return new Promise((_0x1922db, _0x482bf6) => {
            if (document['fonts']) {
                if (this.#loadedFonts['has'](_0x345c98))
                    _0x1922db();
                else {
                    this.#loadedFonts['add'](_0x345c98);
                    const _0x597a1a = new FontFace(_0x345c98, _0x3be7c2);
                    _0x597a1a['load']()['then'](() => {
                        this.#loadedFonts['size'] > this.#MAX_LOADED_FONTS_CNT && this.#loadedFonts['clear'](), document['fonts']['add'](_0x597a1a), this.#updateFlag = !![], _0x1922db();
                    })['catch'](_0x492a56 => {
                        this.#loadedFonts['delete'](_0x345c98), console['log']('[logi.maps]\x20Load\x20font\x20failed.\x20(' + _0x345c98 + ',\x20' + _0x492a56 + ')'), _0x482bf6(_0x492a56);
                    });
                }
            } else
                console['log']('[logi.maps]\x20Load\x20font\x20failed.\x20(document.fonts\x20is\x20not\x20supported)'), _0x482bf6('document.fonts\x20is\x20not\x20supported');
        });
    }
    ['getSymbol'](_0x2f56db, _0x3253fd) {
        let _0x379d91 = this.#symbolGroups['get'](_0x2f56db);
        return _0x379d91?.['get'](_0x3253fd);
    }
    #parseXmlText(_0x213ce7) {
        const _0x204d1b = [], _0x2413f8 = new DOMParser(), _0x2952dc = _0x2413f8['parseFromString'](_0x213ce7, 'application/xml'), _0x20a75c = _0x2952dc['getElementsByTagName']('data')[0x0], _0x5303bc = new Object();
        _0x5303bc['fontMap'] = new Map(), _0x5303bc['iconMap'] = new Map(), _0x5303bc['colorMap'] = new Map(), _0x5303bc['polygonMap'] = new Map(), _0x5303bc['polylineMap'] = new Map(), _0x5303bc['symbolMap'] = new Map(), _0x5303bc['textMap'] = new Map(), _0x5303bc['layerMap'] = new Map();
        {
            for (const _0x5a98db of _0x20a75c['getElementsByTagName']('FONTS')) {
                for (const _0x2c5aa2 of _0x5a98db['getElementsByTagName']('FONT')) {
                    const _0x4a8ead = _0x2c5aa2['getAttribute']('ALPHA3'), _0x38996e = _0x2c5aa2['getAttribute']('FONT_FAMILY'), _0x217a02 = _0x2c5aa2['getAttribute']('FONT_NAME');
                    !_0x5303bc['fontMap']['get'](_0x4a8ead) && _0x5303bc['fontMap']['set'](_0x4a8ead, {
                        'fontFamily': _0x38996e,
                        'fontName': _0x217a02
                    });
                }
            }
            _0x204d1b['push'](new Promise((_0x2c5dc3, _0x2df101) => {
                this.#loadFont(_0x5303bc['fontMap'])['then'](() => {
                    _0x2c5dc3();
                })['catch'](_0x1652fa => {
                    _0x2df101(_0x1652fa);
                });
            }));
        }
        {
            for (const _0x21d3ac of _0x20a75c['getElementsByTagName']('ICONS')) {
                const _0x197eef = _0x21d3ac['getAttribute']('GROUP');
                let _0x5f0dba = _0x5303bc['iconMap']['get'](_0x197eef);
                !_0x5f0dba && (_0x5303bc['iconMap']['set'](_0x197eef, new Array()), _0x5f0dba = _0x5303bc['iconMap']['get'](_0x197eef));
                for (const _0x5dbd95 of _0x21d3ac['getElementsByTagName']('ICON')) {
                    const _0x21b96f = parseInt(_0x5dbd95['getAttribute']('ICON_ID')), _0x33c50e = _0x5dbd95['getAttribute']('IMG_NAME');
                    _0x5f0dba['push']({
                        'iconId': _0x21b96f,
                        'imgName': _0x33c50e
                    });
                }
            }
            _0x204d1b['push'](new Promise((_0x56a8d8, _0x49ec8e) => {
                this.#loadSymbol(_0x5303bc['iconMap'])['then'](() => {
                    _0x56a8d8();
                })['catch'](_0x424c24 => {
                    _0x49ec8e(_0x424c24);
                });
            }));
        }
        for (const _0x66939d of _0x20a75c['getElementsByTagName']('COLORS')) {
            const _0x30bc0e = _0x66939d['getAttribute']('LAYER');
            let _0x2cdbd5 = _0x5303bc['colorMap']['get'](_0x30bc0e);
            !_0x2cdbd5 && (_0x5303bc['colorMap']['set'](_0x30bc0e, new Array()), _0x2cdbd5 = _0x5303bc['colorMap']['get'](_0x30bc0e));
            for (const _0x520492 of _0x66939d['getElementsByTagName']('COLOR')) {
                const _0x4482bf = parseInt(_0x520492['getAttribute']('CATEGORY')), _0x5dd061 = parseInt(_0x520492['getAttribute']('LEVEL')), _0x4e4d13 = parseInt(_0x520492['getAttribute']('R'))['toString'](0x10)['padStart'](0x2, '0'), _0x197cdd = parseInt(_0x520492['getAttribute']('G'))['toString'](0x10)['padStart'](0x2, '0'), _0x119dd5 = parseInt(_0x520492['getAttribute']('B'))['toString'](0x10)['padStart'](0x2, '0'), _0x22dc49 = '#' + _0x4e4d13 + _0x197cdd + _0x119dd5, _0x41c7c3 = parseFloat(_0x520492['getAttribute']('R')) / 0xff, _0x1fcf9e = parseFloat(_0x520492['getAttribute']('G')) / 0xff, _0x162030 = parseFloat(_0x520492['getAttribute']('B')) / 0xff;
                _0x2cdbd5['push']({
                    'categoryId': _0x4482bf,
                    'level': _0x5dd061,
                    'colorData': {
                        'hexString': _0x22dc49,
                        'fltValue': {
                            'r': _0x41c7c3,
                            'g': _0x1fcf9e,
                            'b': _0x162030,
                            'a': 0x1
                        }
                    }
                });
            }
        }
        for (const _0x4fb043 of _0x20a75c['getElementsByTagName']('IMAGES')) {
            const _0x213509 = _0x4fb043['getAttribute']('LAYER'), _0x5e9a58 = parseInt(_0x4fb043['getAttribute']('DEPTH') ?? 0x0);
            _0x5303bc['layerMap']['set']('image.' + _0x213509, { 'depth': _0x5e9a58 });
        }
        for (const _0x19a6ea of _0x20a75c['getElementsByTagName']('POLYGONS')) {
            const _0x3b0ca0 = _0x19a6ea['getAttribute']('LAYER'), _0x187f2e = parseInt(_0x19a6ea['getAttribute']('DEPTH') ?? 0x0), _0x8819e6 = _0x19a6ea['getAttribute']('FILL_STYLE') ?? 'stroke-fill';
            _0x5303bc['layerMap']['set']('polygon.' + _0x3b0ca0, {
                'depth': _0x187f2e,
                'fillStyle': _0x8819e6
            });
            let _0x13ea57 = _0x5303bc['polygonMap']['get'](_0x3b0ca0);
            !_0x13ea57 && (_0x5303bc['polygonMap']['set'](_0x3b0ca0, {
                'layer': _0x3b0ca0,
                'fillStyle': _0x8819e6,
                'items': new Array()
            }), _0x13ea57 = _0x5303bc['polygonMap']['get'](_0x3b0ca0));
            for (const _0x38fa4f of _0x19a6ea['getElementsByTagName']('POLYGON')) {
                const _0xd856ae = parseInt(_0x38fa4f['getAttribute']('CATEGORY')), _0x5cfc8d = parseInt(_0x38fa4f['getAttribute']('LEVEL')), _0x11a935 = parseInt(_0x38fa4f['getAttribute']('DEPTH') ?? 0x0), _0x5855d9 = parseInt(_0x38fa4f['getAttribute']('FIXED') ?? 0x0) != 0x0, _0x27e108 = _0x38fa4f['getAttribute']('FILL_COLOR')['split'](','), _0x360ecb = parseInt(_0x27e108[0x0] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x17d96e = parseInt(_0x27e108[0x1] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x33775c = parseInt(_0x27e108[0x2] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x4bddbe = parseInt(_0x27e108[0x3] ?? 0xff)['toString'](0x10)['padStart'](0x2, '0'), _0x34287b = '#' + _0x360ecb + _0x17d96e + _0x33775c + _0x4bddbe, _0x32eb8e = parseFloat(_0x27e108[0x0] ?? 0x0) / 0xff, _0x57d192 = parseFloat(_0x27e108[0x1] ?? 0x0) / 0xff, _0x4ebfa8 = parseFloat(_0x27e108[0x2] ?? 0x0) / 0xff, _0x1822d9 = parseFloat(_0x27e108[0x3] ?? 0xff) / 0xff, _0x479f6d = parseFloat(_0x38fa4f['getAttribute']('STROKE') ?? 0x0), _0x206a44 = _0x38fa4f['getAttribute']('STROKE_COLOR')?.['split'](',') ?? [
                        0x0,
                        0x0,
                        0x0
                    ], _0x256616 = parseInt(_0x206a44[0x0] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x1051d5 = parseInt(_0x206a44[0x1] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x2b5544 = parseInt(_0x206a44[0x2] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x49de9a = parseInt(_0x206a44[0x3] ?? 0xff)['toString'](0x10)['padStart'](0x2, '0'), _0xd55b9 = '#' + _0x256616 + _0x1051d5 + _0x2b5544 + _0x49de9a, _0x47d645 = parseFloat(_0x206a44[0x0] ?? 0x0) / 0xff, _0x135b09 = parseFloat(_0x206a44[0x1] ?? 0x0) / 0xff, _0x35de44 = parseFloat(_0x206a44[0x2] ?? 0x0) / 0xff, _0x196958 = parseFloat(_0x206a44[0x3] ?? 0xff) / 0xff;
                _0x13ea57['items']['push']({
                    'categoryId': _0xd856ae,
                    'level': _0x5cfc8d,
                    'depthZ': _0x187f2e * 0.01 + _0x11a935 * 0.0001,
                    'fixedScale': _0x5855d9,
                    'fillColorData': {
                        'hexString': _0x34287b,
                        'fltValue': {
                            'r': _0x32eb8e,
                            'g': _0x57d192,
                            'b': _0x4ebfa8,
                            'a': _0x1822d9
                        }
                    },
                    'stroke': _0x479f6d,
                    'strokeColorData': {
                        'hexString': _0xd55b9,
                        'fltValue': {
                            'r': _0x47d645,
                            'g': _0x135b09,
                            'b': _0x35de44,
                            'a': _0x196958
                        }
                    }
                });
            }
        }
        for (const _0x10ed56 of _0x20a75c['getElementsByTagName']('POLYLINES')) {
            const _0x37e7a6 = _0x10ed56['getAttribute']('LAYER'), _0x524145 = parseInt(_0x10ed56['getAttribute']('DEPTH') ?? 0x0);
            _0x5303bc['layerMap']['set']('polyline.' + _0x37e7a6, { 'depth': _0x524145 });
            let _0x3bdeb8 = _0x5303bc['polylineMap']['get'](_0x37e7a6);
            !_0x3bdeb8 && (_0x5303bc['polylineMap']['set'](_0x37e7a6, {
                'layer': _0x37e7a6,
                'items': new Array()
            }), _0x3bdeb8 = _0x5303bc['polylineMap']['get'](_0x37e7a6));
            for (const _0x1e770e of _0x10ed56['getElementsByTagName']('POLYLINE')) {
                const _0x17a39b = parseInt(_0x1e770e['getAttribute']('CATEGORY')), _0x2efb2d = parseInt(_0x1e770e['getAttribute']('LEVEL')), _0x3d85a2 = parseInt(_0x1e770e['getAttribute']('DEPTH') ?? 0x0), _0x11c71a = parseInt(_0x1e770e['getAttribute']('FIXED') ?? 0x0) != 0x0, _0x1739d0 = parseFloat(_0x1e770e['getAttribute']('WIDTH')), _0x52b2f7 = _0x1e770e['getAttribute']('COLOR')['split'](','), _0x5d71ec = parseInt(_0x52b2f7[0x0] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x39be0c = parseInt(_0x52b2f7[0x1] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x329bb5 = parseInt(_0x52b2f7[0x2] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x30fff6 = parseInt(_0x52b2f7[0x3] ?? 0xff)['toString'](0x10)['padStart'](0x2, '0'), _0x286a5e = '#' + _0x5d71ec + _0x39be0c + _0x329bb5 + _0x30fff6, _0x560c3b = parseFloat(_0x52b2f7[0x0] ?? 0x0) / 0xff, _0x3f8341 = parseFloat(_0x52b2f7[0x1] ?? 0x0) / 0xff, _0x36ac87 = parseFloat(_0x52b2f7[0x2] ?? 0x0) / 0xff, _0x5753bf = parseFloat(_0x52b2f7[0x3] ?? 0xff) / 0xff, _0x1a9f09 = parseFloat(_0x1e770e['getAttribute']('STROKE') ?? 0x0), _0x55acc8 = _0x1e770e['getAttribute']('STROKE_COLOR')?.['split'](',') ?? [
                        0x0,
                        0x0,
                        0x0
                    ], _0x59bd98 = parseInt(_0x55acc8[0x0] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x22e117 = parseInt(_0x55acc8[0x1] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x1640d6 = parseInt(_0x55acc8[0x2] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x5e396b = parseInt(_0x55acc8[0x3] ?? 0xff)['toString'](0x10)['padStart'](0x2, '0'), _0x5ec104 = '#' + _0x59bd98 + _0x22e117 + _0x1640d6 + _0x5e396b, _0x423cee = parseFloat(_0x55acc8[0x0] ?? 0x0) / 0xff, _0x4df8f6 = parseFloat(_0x55acc8[0x1] ?? 0x0) / 0xff, _0x1b2d28 = parseFloat(_0x55acc8[0x2] ?? 0x0) / 0xff, _0x3c3e61 = parseFloat(_0x55acc8[0x3] ?? 0xff) / 0xff, _0x41ed22 = parseFloat(_0x1e770e['getAttribute']('DASH') ?? 0x0), _0x23d1a1 = parseFloat(_0x1e770e['getAttribute']('GAP') ?? 0x0);
                _0x3bdeb8['items']['push']({
                    'categoryId': _0x17a39b,
                    'level': _0x2efb2d,
                    'depthZ': _0x524145 * 0.01 + _0x3d85a2 * 0.0001,
                    'fixedScale': _0x11c71a,
                    'width': _0x1739d0,
                    'colorData': {
                        'hexString': _0x286a5e,
                        'fltValue': {
                            'r': _0x560c3b,
                            'g': _0x3f8341,
                            'b': _0x36ac87,
                            'a': _0x5753bf
                        }
                    },
                    'stroke': _0x1a9f09,
                    'strokeColorData': {
                        'hexString': _0x5ec104,
                        'fltValue': {
                            'r': _0x423cee,
                            'g': _0x4df8f6,
                            'b': _0x1b2d28,
                            'a': _0x3c3e61
                        }
                    },
                    'dash': _0x41ed22,
                    'gap': _0x23d1a1
                });
            }
        }
        for (const _0x53a9c4 of _0x20a75c['getElementsByTagName']('SYMBOLS')) {
            const _0x592230 = _0x53a9c4['getAttribute']('LAYER'), _0x33f856 = parseInt(_0x53a9c4['getAttribute']('DEPTH') ?? 0x0);
            _0x5303bc['layerMap']['set']('symbol.' + _0x592230, { 'depth': _0x33f856 });
            let _0x23df79 = _0x5303bc['symbolMap']['get'](_0x592230);
            !_0x23df79 && (_0x5303bc['symbolMap']['set'](_0x592230, {
                'layer': _0x592230,
                'items': new Array()
            }), _0x23df79 = _0x5303bc['symbolMap']['get'](_0x592230));
            for (const _0x548a07 of _0x53a9c4['getElementsByTagName']('SYMBOL')) {
                const _0x16796d = parseInt(_0x548a07['getAttribute']('CATEGORY')), _0x4960c5 = parseInt(_0x548a07['getAttribute']('LEVEL')), _0x219db6 = parseInt(_0x548a07['getAttribute']('DEPTH') ?? 0x0), _0x4a3eea = parseInt(_0x548a07['getAttribute']('FIXED') ?? 0x0) != 0x0, _0x289f21 = _0x548a07['getAttribute']('ICON_GROUP'), _0x2ece9c = parseInt(_0x548a07['getAttribute']('ICON_ID')), _0x6906b5 = parseInt(_0x548a07['getAttribute']('FONT_SIZE')), _0x26562f = _0x548a07['getAttribute']('TEXT_COLOR')['split'](','), _0x1111a2 = parseInt(_0x26562f[0x0] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x12f900 = parseInt(_0x26562f[0x1] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x37b247 = parseInt(_0x26562f[0x2] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x222c5d = parseInt(_0x26562f[0x3] ?? 0xff)['toString'](0x10)['padStart'](0x2, '0'), _0x2c6063 = '#' + _0x1111a2 + _0x12f900 + _0x37b247 + _0x222c5d, _0x3cede9 = parseFloat(_0x26562f[0x0] ?? 0x0) / 0xff, _0x100d00 = parseFloat(_0x26562f[0x1] ?? 0x0) / 0xff, _0x1d8ef9 = parseFloat(_0x26562f[0x2] ?? 0x0) / 0xff, _0x4e2227 = parseFloat(_0x26562f[0x3] ?? 0xff) / 0xff, _0x51eef7 = parseInt(_0x548a07['getAttribute']('BOLD') ?? 0x0), _0x5da412 = parseFloat(_0x548a07['getAttribute']('STROKE') ?? 0x0), _0xab9952 = _0x548a07['getAttribute']('STROKE_COLOR')['split'](','), _0x469e85 = parseInt(_0xab9952[0x0] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x4cb3e5 = parseInt(_0xab9952[0x1] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x1b84e2 = parseInt(_0xab9952[0x2] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0xebfb7d = parseInt(_0xab9952[0x3] ?? 0xff)['toString'](0x10)['padStart'](0x2, '0'), _0x3b1b9f = '#' + _0x469e85 + _0x4cb3e5 + _0x1b84e2 + _0xebfb7d, _0x414b0b = parseFloat(_0xab9952[0x0] ?? 0x0) / 0xff, _0x6684b4 = parseFloat(_0xab9952[0x1] ?? 0x0) / 0xff, _0xb693e = parseFloat(_0xab9952[0x2] ?? 0x0) / 0xff, _0x3310de = parseFloat(_0xab9952[0x3] ?? 0xff) / 0xff, _0x2a6b0e = this.#alignCodeDic['get'](parseInt(_0x548a07['getAttribute']('TEXT_ALIGN'))) ?? 'center-middle';
                _0x23df79['items']['push']({
                    'categoryId': _0x16796d,
                    'level': _0x4960c5,
                    'depthZ': _0x33f856 * 0.01 + _0x219db6 * 0.0001,
                    'fixedScale': _0x4a3eea,
                    'iconGroup': _0x289f21,
                    'iconId': _0x2ece9c,
                    'fontSize': _0x6906b5,
                    'textColorData': {
                        'hexString': _0x2c6063,
                        'fltValue': {
                            'r': _0x3cede9,
                            'g': _0x100d00,
                            'b': _0x1d8ef9,
                            'a': _0x4e2227
                        }
                    },
                    'bold': _0x51eef7,
                    'stroke': _0x5da412,
                    'strokeColorData': {
                        'hexString': _0x3b1b9f,
                        'fltValue': {
                            'r': _0x414b0b,
                            'g': _0x6684b4,
                            'b': _0xb693e,
                            'a': _0x3310de
                        }
                    },
                    'textAlign': _0x2a6b0e
                });
            }
        }
        for (const _0x5c1884 of _0x20a75c['getElementsByTagName']('TEXTS')) {
            const _0x129421 = _0x5c1884['getAttribute']('LAYER'), _0x4a21a5 = parseInt(_0x5c1884['getAttribute']('DEPTH') ?? 0x0);
            _0x5303bc['layerMap']['set']('text.' + _0x129421, { 'depth': _0x4a21a5 });
            let _0x52eac1 = _0x5303bc['textMap']['get'](_0x129421);
            !_0x52eac1 && (_0x5303bc['textMap']['set'](_0x129421, {
                'layer': _0x129421,
                'items': new Array()
            }), _0x52eac1 = _0x5303bc['textMap']['get'](_0x129421));
            for (const _0x1c7cd9 of _0x5c1884['getElementsByTagName']('TEXT')) {
                const _0x419600 = parseInt(_0x1c7cd9['getAttribute']('CATEGORY')), _0x440d15 = parseInt(_0x1c7cd9['getAttribute']('LEVEL')), _0x8e9e37 = parseInt(_0x1c7cd9['getAttribute']('DEPTH') ?? 0x0), _0x5bb614 = parseInt(_0x1c7cd9['getAttribute']('FIXED') ?? 0x0) != 0x0, _0x57a2e0 = parseInt(_0x1c7cd9['getAttribute']('FONT_SIZE')), _0x226453 = _0x1c7cd9['getAttribute']('TEXT_COLOR')['split'](','), _0x624fda = parseInt(_0x226453[0x0] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x3776c9 = parseInt(_0x226453[0x1] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x592171 = parseInt(_0x226453[0x2] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x3082e6 = parseInt(_0x226453[0x3] ?? 0xff)['toString'](0x10)['padStart'](0x2, '0'), _0x45730a = '#' + _0x624fda + _0x3776c9 + _0x592171 + _0x3082e6, _0x3ca4e0 = parseFloat(_0x226453[0x0] ?? 0x0) / 0xff, _0x2cc939 = parseFloat(_0x226453[0x1] ?? 0x0) / 0xff, _0x5d9869 = parseFloat(_0x226453[0x2] ?? 0x0) / 0xff, _0x51a6ab = parseFloat(_0x226453[0x3] ?? 0xff) / 0xff, _0x170ab7 = parseInt(_0x1c7cd9['getAttribute']('BOLD') ?? 0x0), _0x50c4b5 = parseFloat(_0x1c7cd9['getAttribute']('STROKE') ?? 0x0), _0x2063a6 = _0x1c7cd9['getAttribute']('STROKE_COLOR')['split'](','), _0x296374 = parseInt(_0x2063a6[0x0] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x26eb8b = parseInt(_0x2063a6[0x1] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0xf30695 = parseInt(_0x2063a6[0x2] ?? 0x0)['toString'](0x10)['padStart'](0x2, '0'), _0x4f1a04 = parseInt(_0x2063a6[0x3] ?? 0xff)['toString'](0x10)['padStart'](0x2, '0'), _0x4c5c3c = '#' + _0x296374 + _0x26eb8b + _0xf30695 + _0x4f1a04, _0x328d20 = parseFloat(_0x2063a6[0x0] ?? 0x0) / 0xff, _0x36880c = parseFloat(_0x2063a6[0x1] ?? 0x0) / 0xff, _0x59f1a6 = parseFloat(_0x2063a6[0x2] ?? 0x0) / 0xff, _0x327f7a = parseFloat(_0x2063a6[0x3] ?? 0xff) / 0xff, _0x3396b1 = this.#alignCodeDic['get'](parseInt(_0x1c7cd9['getAttribute']('TEXT_ALIGN'))) ?? 'center-middle';
                _0x52eac1['items']['push']({
                    'categoryId': _0x419600,
                    'level': _0x440d15,
                    'depthZ': _0x4a21a5 * 0.01 + _0x8e9e37 * 0.0001,
                    'fixedScale': _0x5bb614,
                    'fontSize': _0x57a2e0,
                    'textColorData': {
                        'hexString': _0x45730a,
                        'fltValue': {
                            'r': _0x3ca4e0,
                            'g': _0x2cc939,
                            'b': _0x5d9869,
                            'a': _0x51a6ab
                        }
                    },
                    'bold': _0x170ab7,
                    'stroke': _0x50c4b5,
                    'strokeColorData': {
                        'hexString': _0x4c5c3c,
                        'fltValue': {
                            'r': _0x328d20,
                            'g': _0x36880c,
                            'b': _0x59f1a6,
                            'a': _0x327f7a
                        }
                    },
                    'textAlign': _0x3396b1
                });
            }
        }
        return new Promise((_0x3c4362, _0x205faa) => {
            Promise['all'](_0x204d1b)['then'](() => {
                this.#mapStyle = _0x5303bc, this.#xmlStyleText = _0x213ce7, this.#updateFlag = !![], _0x3c4362();
            })['catch'](_0x36469c => {
                _0x205faa(_0x36469c);
            });
        });
    }
    async #loadStyle() {
        const _0x2333e3 = this.#getResourceDataUrl('stylesheet/map_style.xml'), _0x52f8ee = 'map_style.xml-' + this.#extn + '_' + this.#region + '/' + this.#resTheme + '/stylesheet', _0x106bac = await this.#resDatabase['getResData'](_0x52f8ee);
        if (_0x106bac?.['dataValue'])
            await this.#parseXmlText(_0x106bac['dataValue']);
        else {
            const _0x59629e = await fetch(_0x2333e3), _0xb999e8 = await _0x59629e['text']();
            this.#resDatabase['addResData'](_0x52f8ee, _0xb999e8), await this.#parseXmlText(_0xb999e8);
        }
    }
    #loadFont(_0x14ff51) {
        const _0x54ab21 = [];
        this.#loadedFonts['clear']();
        for (const [, _0x496ebf] of _0x14ff51) {
            _0x54ab21['push'](new Promise((_0x561897, _0x116a0e) => {
                const _0x34bd86 = _0x496ebf['fontName'] + '-' + this.#extn + '_' + this.#region + '/' + this.#resTheme + '/fonts';
                this.#resDatabase['getResData'](_0x34bd86)['then'](_0x500616 => {
                    const _0x36fc64 = _0x496ebf['fontFamily'];
                    if (_0x500616?.['dataValue']) {
                        const _0x1c6868 = URL['createObjectURL'](_0x500616['dataValue']);
                        this['addFont'](_0x36fc64, 'url(' + _0x1c6868 + ')')['then'](() => {
                            _0x561897();
                        })['catch'](_0x8340a7 => {
                            _0x116a0e(_0x8340a7);
                        });
                    } else {
                        const _0x2616b3 = this.#getResourceDataUrl('fonts/' + _0x496ebf['fontName']);
                        fetch(_0x2616b3)['then'](_0x2493f6 => {
                            const _0x46f891 = parseInt(_0x2493f6['headers']['get']('content-length') ?? 0x3e7), _0x2e9c0e = _0x2493f6['status'] >= 0xc8 && _0x2493f6['status'] < 0x12c || _0x2493f6['status'] == 0x130;
                            !_0x2e9c0e || _0x46f891 == 0x0 ? (console['log']('[logi.maps]\x20ERROR:\x20' + _0x2493f6['status'] + ',\x20' + _0x2493f6['statusText']), _0x116a0e()) : _0x2493f6['blob']()['then'](_0x56cc93 => {
                                this.#resDatabase['addResData'](_0x500616['dataKey'], _0x56cc93);
                                const _0x5585a6 = URL['createObjectURL'](_0x56cc93);
                                this['addFont'](_0x36fc64, 'url(' + _0x5585a6 + ')')['then'](() => {
                                    _0x561897();
                                })['catch'](_0x3b16ae => {
                                    _0x116a0e(_0x3b16ae);
                                });
                            });
                        });
                    }
                });
            }));
        }
        return new Promise((_0x48386f, _0x12c239) => {
            Promise['all'](_0x54ab21)['then'](() => {
                _0x48386f();
            })['catch'](_0x4a0603 => {
                _0x12c239(_0x4a0603);
            });
        });
    }
    #loadSymbol(_0x42b32f) {
        function _0x4f2b4d(_0x1dde01) {
            return new Promise((_0x3bbd57, _0x300c6a) => {
                let _0x39b289 = new Image();
                _0x39b289['crossOrigin'] = 'Anonymous', _0x39b289['onload'] = () => _0x3bbd57(_0x39b289), _0x39b289['onerror'] = _0x300c6a, _0x39b289['src'] = _0x1dde01;
            });
        }
        const _0x572609 = [];
        this.#symbolGroups = new Map();
        for (const [_0x3e5bbc, _0x48142d] of _0x42b32f) {
            this.#symbolGroups['set'](_0x3e5bbc, new Map());
            let _0x470a59 = this.#symbolGroups['get'](_0x3e5bbc);
            for (const _0x115c30 of _0x48142d) {
                const _0x463317 = this.#getResourceDataUrl('symbol/' + _0x115c30['imgName']), _0x67ff01 = new Promise((_0x7ec929, _0x21a161) => {
                        const _0x3dc78b = _0x115c30['imgName'] + '-' + this.#extn + '_' + this.#region + '/' + this.#resTheme + '/symbol';
                        this.#resDatabase['getResData'](_0x3dc78b)['then'](_0x5445f8 => {
                            if (_0x5445f8?.['dataValue']) {
                                const _0xe4f9b9 = URL['createObjectURL'](_0x5445f8['dataValue']);
                                _0x4f2b4d(_0xe4f9b9)['then'](_0x13dbec => {
                                    _0x470a59['set'](_0x115c30['iconId'], _0x13dbec), this.#updateFlag = !![], _0x7ec929();
                                })['catch'](_0x396681 => {
                                    console['log']('[logi.maps]\x20Load\x20symbol\x20failed.\x20(' + _0x396681 + ')'), _0x21a161(_0x396681);
                                });
                            } else
                                fetch(_0x463317)['then'](_0x4dbf54 => {
                                    const _0x54c2be = parseInt(_0x4dbf54['headers']['get']('content-length') ?? 0x3e7), _0x96fe57 = _0x4dbf54['status'] >= 0xc8 && _0x4dbf54['status'] < 0x12c || _0x4dbf54['status'] == 0x130;
                                    !_0x96fe57 || _0x54c2be == 0x0 ? (console['log']('[logi.maps]\x20ERROR:\x20' + _0x4dbf54['status'] + ',\x20' + _0x4dbf54['statusText']), _0x21a161()) : _0x4dbf54['blob']()['then'](_0x37281f => {
                                        this.#resDatabase['addResData'](_0x5445f8['dataKey'], _0x37281f);
                                        const _0x382c6a = URL['createObjectURL'](_0x37281f);
                                        _0x4f2b4d(_0x382c6a)['then'](_0x4f4e7c => {
                                            _0x470a59['set'](_0x115c30['iconId'], _0x4f4e7c), this.#updateFlag = !![], _0x7ec929();
                                        })['catch'](_0x1796bb => {
                                            console['log']('[logi.maps]\x20Load\x20symbol\x20failed.\x20(' + _0x1796bb + ')'), _0x21a161(_0x1796bb);
                                        });
                                    });
                                });
                        });
                    });
                _0x572609['push'](_0x67ff01);
            }
        }
        return new Promise((_0x3b7aaf, _0x20eccf) => {
            Promise['all'](_0x572609)['then'](() => {
                _0x3b7aaf();
            })['catch'](_0x3652f3 => {
                _0x20eccf(_0x3652f3);
            });
        });
    }
    ['getFontFamily'](_0x3982cd) {
        let _0xab651c = this.#mapStyle?.['fontMap']['get'](_0x3982cd);
        return !_0xab651c && (_0xab651c = this.#mapStyle?.['fontMap']['get']('default'), !_0xab651c && (_0xab651c = {
            'fontFamily': 'NotoSans-Bold',
            'fontName': 'NotoSans-Bold.ttf'
        })), _0xab651c['fontFamily'];
    }
    ['getColorStyle'](_0x34f2c3, _0x1f9a4b, _0x33cab1) {
        const _0x3ecd28 = this.#mapStyle?.['colorMap']['get'](_0x34f2c3);
        if (_0x3ecd28) {
            for (const _0x55d0e5 of _0x3ecd28) {
                if (_0x55d0e5['categoryId'] == _0x1f9a4b && _0x55d0e5['level'] == _0x33cab1)
                    return _0x55d0e5;
            }
            for (const _0x3a369a of _0x3ecd28) {
                if (_0x3a369a['categoryId'] == _0x1f9a4b && _0x3a369a['level'] == 0x0)
                    return _0x3a369a;
            }
            for (const _0x3b507f of _0x3ecd28) {
                if (_0x3b507f['categoryId'] == 0x0 && _0x3b507f['level'] == _0x33cab1)
                    return _0x3b507f;
            }
            for (const _0x99c5e8 of _0x3ecd28) {
                if (_0x99c5e8['categoryId'] == 0x0 && _0x99c5e8['level'] == 0x0)
                    return _0x99c5e8;
            }
        }
        return null;
    }
    ['getPolygonStyle'](_0x9c4778, _0x207352, _0x13384c) {
        const _0x1f5cd4 = this.#isLayerVisible(_0x9c4778, _0x207352, null);
        if (_0x1f5cd4 == ![])
            return null;
        const _0x1e5b5f = this.#mapStyle?.['polygonMap']['get'](_0x9c4778);
        if (_0x1e5b5f) {
            for (const _0x1bb69b of _0x1e5b5f['items']) {
                if (_0x1bb69b['categoryId'] == _0x207352 && _0x1bb69b['level'] == _0x13384c)
                    return _0x1bb69b;
            }
            for (const _0x588b91 of _0x1e5b5f['items']) {
                if (_0x588b91['categoryId'] == _0x207352 && _0x588b91['level'] == 0x0)
                    return _0x588b91;
            }
            for (const _0x2dac8e of _0x1e5b5f['items']) {
                if (_0x2dac8e['categoryId'] == 0x0 && _0x2dac8e['level'] == _0x13384c)
                    return _0x2dac8e;
            }
            for (const _0x33b2b2 of _0x1e5b5f['items']) {
                if (_0x33b2b2['categoryId'] == 0x0 && _0x33b2b2['level'] == 0x0)
                    return _0x33b2b2;
            }
        }
        return null;
    }
    ['getPolylineStyle'](_0x39d8bf, _0x1e24b7, _0x4de804) {
        const _0x1147e3 = this.#isLayerVisible(_0x39d8bf, _0x1e24b7, null);
        if (_0x1147e3 == ![])
            return null;
        const _0x32546e = this.#mapStyle?.['polylineMap']['get'](_0x39d8bf);
        if (_0x32546e) {
            for (const _0x4b3e0b of _0x32546e['items']) {
                if (_0x4b3e0b['categoryId'] == _0x1e24b7 && _0x4b3e0b['level'] == _0x4de804)
                    return _0x4b3e0b;
            }
            for (const _0x243e82 of _0x32546e['items']) {
                if (_0x243e82['categoryId'] == _0x1e24b7 && _0x243e82['level'] == 0x0)
                    return _0x243e82;
            }
            for (const _0x501aa1 of _0x32546e['items']) {
                if (_0x501aa1['categoryId'] == 0x0 && _0x501aa1['level'] == _0x4de804)
                    return _0x501aa1;
            }
            for (const _0x1df234 of _0x32546e['items']) {
                if (_0x1df234['categoryId'] == 0x0 && _0x1df234['level'] == 0x0)
                    return _0x1df234;
            }
        }
        return null;
    }
    ['getSymbolStyle'](_0x2eac73, _0x1a1fb1, _0x470a70, _0x4f83af) {
        const _0x356421 = this.#isLayerVisible(_0x2eac73, _0x1a1fb1, _0x470a70);
        if (_0x356421 == ![])
            return null;
        const _0x5d652c = this.#mapStyle?.['symbolMap']['get'](_0x2eac73);
        if (_0x5d652c) {
            for (const _0x597f33 of _0x5d652c['items']) {
                if (_0x597f33['categoryId'] == _0x1a1fb1 && _0x597f33['level'] == _0x4f83af)
                    return _0x597f33;
            }
            for (const _0x38cffe of _0x5d652c['items']) {
                if (_0x38cffe['categoryId'] == _0x1a1fb1 && _0x38cffe['level'] == 0x0)
                    return _0x38cffe;
            }
            for (const _0x28c0e5 of _0x5d652c['items']) {
                if (_0x28c0e5['categoryId'] == 0x0 && _0x28c0e5['level'] == _0x4f83af)
                    return _0x28c0e5;
            }
            for (const _0x500e95 of _0x5d652c['items']) {
                if (_0x500e95['categoryId'] == 0x0 && _0x500e95['level'] == 0x0)
                    return _0x500e95;
            }
        }
        return null;
    }
    ['getTextStyle'](_0xc5b359, _0x5b3532, _0x12ffbd, _0x3ffee9) {
        const _0x1304c3 = this.#isLayerVisible(_0xc5b359, _0x5b3532, _0x12ffbd);
        if (_0x1304c3 == ![])
            return null;
        const _0x3bb6a = this.#mapStyle?.['textMap']['get'](_0xc5b359);
        if (_0x3bb6a) {
            for (const _0x220af7 of _0x3bb6a['items']) {
                if (_0x220af7['categoryId'] == _0x5b3532 && _0x220af7['level'] == _0x3ffee9)
                    return _0x220af7;
            }
            for (const _0x258604 of _0x3bb6a['items']) {
                if (_0x258604['categoryId'] == _0x5b3532 && _0x258604['level'] == 0x0)
                    return _0x258604;
            }
            for (const _0x589a0c of _0x3bb6a['items']) {
                if (_0x589a0c['categoryId'] == 0x0 && _0x589a0c['level'] == _0x3ffee9)
                    return _0x589a0c;
            }
            for (const _0x33ff34 of _0x3bb6a['items']) {
                if (_0x33ff34['categoryId'] == 0x0 && _0x33ff34['level'] == 0x0)
                    return _0x33ff34;
            }
        }
        return null;
    }
    ['getLayerStyle'](_0x295f05, _0x41b085) {
        const _0xc6b6ef = this.#mapStyle?.['layerMap']['get'](_0x295f05 + '.' + _0x41b085);
        return _0xc6b6ef ?? {};
    }
    ['preWork']() {
    }
    ['postWork']() {
        this.#resDatabase['putDatas'](), this.#resDatabase['resizeDatabase']();
    }
};
export default logi['maps']['Resource'];