import a1_0x522dd4 from '../common/logi-maps-defines.js?v=2.1.10.1';
import a1_0x1d8d3c from './logi-maps-databasestore.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a1_0x522dd4, logi['maps']['DatabaseStore'] = a1_0x1d8d3c, logi['maps']['MtDatabase'] = class {
    #database = null;
    #tileStore = null;
    #pngStore = null;
    #xvgStore = null;
    #binStore = null;
    #indexedDBName = 'logimaps';
    #indexedDBVersion = 0x5;
    constructor(_0x316e07) {
        {
            const _0x1fcaa2 = {
                'versionKey': '_VERSION_',
                'maxRowCnt': 0x3200,
                'checkRowCntTickTime': 0xfa0,
                'maxStoreDataSize': 0x200,
                'maxDelayPutCnt': 0x20,
                'minDelayPutTickTime': 0x3e8,
                'maxDelayPutTickTime': 0xfa0
            };
            this.#pngStore = new logi['maps']['DatabaseStore']('tiledmap-png', 'index_tiledmap-png', _0x1fcaa2);
        }
        {
            const _0x26bf3a = {
                'versionKey': '_VERSION_',
                'maxRowCnt': 0x4b00,
                'checkRowCntTickTime': 0xfa0,
                'maxStoreDataSize': 0x200,
                'maxDelayPutCnt': 0x20,
                'minDelayPutTickTime': 0x3e8,
                'maxDelayPutTickTime': 0xfa0
            };
            this.#xvgStore = new logi['maps']['DatabaseStore']('tiledmap-xvg', 'index_tiledmap-xvg', _0x26bf3a);
        }
        {
            const _0x2edf79 = {
                'versionKey': '_VERSION_',
                'maxRowCnt': 0x4b00,
                'checkRowCntTickTime': 0xfa0,
                'maxStoreDataSize': 0x200,
                'maxDelayPutCnt': 0x20,
                'minDelayPutTickTime': 0x3e8,
                'maxDelayPutTickTime': 0xfa0
            };
            this.#binStore = new logi['maps']['DatabaseStore']('tiledmap-bin', 'index_tiledmap-bin', _0x2edf79);
        }
        if (_0x316e07 == logi['maps']['Defines']['MAP_TILE_EXT_BIN'])
            this.#tileStore = this.#binStore;
        else
            _0x316e07 == logi['maps']['Defines']['MAP_TILE_EXT_XVG'] ? this.#tileStore = this.#xvgStore : this.#tileStore = this.#pngStore;
    }
    async ['initDatabase']() {
        let _0x2f3768 = null;
        try {
            _0x2f3768 = await this.#initDatabase();
        } catch (_0x10fa3e) {
            await this.#deleteDatabase(), _0x2f3768 = await this.#initDatabase();
        }
        return _0x2f3768;
    }
    #initDatabase() {
        return new Promise((_0x2ed32d, _0x135fa4) => {
            try {
                if (!window['indexedDB'])
                    _0x135fa4();
                else {
                    const _0x3bb032 = window['indexedDB']['open'](this.#indexedDBName, this.#indexedDBVersion);
                    _0x3bb032['onupgradeneeded'] = _0x3a8a0a => {
                        const _0xb54fb8 = _0x3a8a0a['target']['result'];
                        {
                            console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Upgrade\x20database'), this.#pngStore['initStore'](_0xb54fb8), this.#xvgStore['initStore'](_0xb54fb8), this.#binStore['initStore'](_0xb54fb8);
                        }
                        {
                            const _0x127201 = 'tiledmap';
                            _0xb54fb8['objectStoreNames']['contains'](_0x127201) && (console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Delete\x20' + _0x127201), _0xb54fb8['deleteObjectStore'](_0x127201));
                        }
                    }, _0x3bb032['onsuccess'] = () => {
                        this.#database = _0x3bb032['result'], this.#tileStore != this.#pngStore && this.#pngStore['clearAll'](this.#database), this.#tileStore != this.#xvgStore && this.#xvgStore['clearAll'](this.#database), this.#tileStore != this.#binStore && this.#binStore['clearAll'](this.#database), console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Init'), _0x2ed32d();
                    }, _0x3bb032['onerror'] = _0x2234a5 => {
                        const _0x39de6c = _0x2234a5['target']['error'];
                        console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Failed\x20to\x20init:\x20' + _0x39de6c['message']), _0x135fa4();
                    };
                }
            } catch (_0xc98fba) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0xc98fba['message']), _0x135fa4();
            }
        });
    }
    #deleteDatabase() {
        return this.#database = null, new Promise(_0x1cb641 => {
            try {
                if (!window['indexedDB'])
                    _0x1cb641();
                else {
                    const _0x32618b = window['indexedDB']['deleteDatabase'](this.#indexedDBName);
                    _0x32618b['onsuccess'] = () => {
                        console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Deleted\x20successfully'), _0x1cb641();
                    }, _0x32618b['onerror'] = _0x3cc171 => {
                        const _0x2a97d0 = _0x3cc171['target']['error'];
                        console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Error\x20deleting:\x20' + _0x2a97d0['message']), _0x1cb641();
                    };
                }
            } catch (_0x39b456) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0x39b456['message']), _0x1cb641();
            }
        });
    }
    ['existDatabase']() {
        return this.#database != null;
    }
    async ['resizeDatabase']() {
        if (this.#database != null)
            try {
                await this.#tileStore['resizeStore'](this.#database);
            } catch (_0xd0452b) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0xd0452b['message']), await this.#deleteDatabase();
            }
    }
    async ['checkDataVersion'](_0xb98964) {
        if (this.#database != null)
            try {
                return await this.#tileStore['checkDataVersion'](this.#database, _0xb98964);
            } catch (_0x291b7d) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0x291b7d['message']), await this.#deleteDatabase();
            }
        return ![];
    }
    ['addData'](_0x65d370, _0x3a928d) {
        this.#database != null && this.#tileStore['addData'](_0x65d370, _0x3a928d);
    }
    async ['putDatas']() {
        if (this.#database != null)
            try {
                await this.#tileStore['putDatas'](this.#database);
            } catch (_0x57bd16) {
                console['log']('[logi.maps]\x20Failed\x20to\x20putDatas\x20(' + _0x57bd16['message'] + ')'), await this.#deleteDatabase();
            }
    }
    async ['getDatas'](_0x48f38b) {
        if (_0x48f38b?.['length'] > 0x0) {
            if (this.#database == null) {
                const _0x35d502 = [];
                for (let _0x47bfbd of _0x48f38b) {
                    _0x35d502['push']({
                        'dataKey': _0x47bfbd,
                        'dataValue': null
                    });
                }
                return _0x35d502;
            } else
                try {
                    return await this.#tileStore['getDatas'](this.#database, _0x48f38b);
                } catch (_0x32005c) {
                    console['log']('[logi.maps]\x20Failed\x20to\x20getDatas\x20(' + _0x32005c['message'] + ')'), await this.#deleteDatabase();
                }
        }
        return [];
    }
};
export default logi['maps']['MtDatabase'];