import a3_0xc96a1c from '../common/logi-maps-defines.js?v=2.1.10.1';
import a3_0x3d42e9 from './logi-maps-databasestore.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a3_0xc96a1c, logi['maps']['DatabaseStore'] = a3_0x3d42e9, logi['maps']['VtDatabase'] = class {
    #database = null;
    #postStore = null;
    #sidoStore = null;
    #sggStore = null;
    #emdStore = null;
    #indexedDBName = 'logivts';
    #indexedDBVersion = 0x4;
    constructor() {
        const _0x3d6243 = {
            'versionKey': '_VERSION_',
            'maxRowCnt': 0x2800,
            'checkRowCntTickTime': 0xfa0,
            'maxStoreDataSize': 0x200,
            'maxDelayPutCnt': 0x20,
            'minDelayPutTickTime': 0x3e8,
            'maxDelayPutTickTime': 0xfa0
        };
        this.#postStore = new logi['maps']['DatabaseStore']('post', 'index_post', _0x3d6243), this.#sidoStore = new logi['maps']['DatabaseStore']('sido', 'index_sido', _0x3d6243), this.#sggStore = new logi['maps']['DatabaseStore']('sgg', 'index_sgg', _0x3d6243), this.#emdStore = new logi['maps']['DatabaseStore']('emd', 'index_emd', _0x3d6243);
    }
    async ['initDatabase']() {
        let _0x54594a = null;
        try {
            _0x54594a = await this.#initDatabase();
        } catch (_0x25ae1d) {
            await this.#deleteDatabase(), _0x54594a = await this.#initDatabase();
        }
        return _0x54594a;
    }
    #initDatabase() {
        return new Promise((_0x41e776, _0xb08cac) => {
            try {
                if (!window['indexedDB'])
                    _0xb08cac();
                else {
                    const _0x568e61 = window['indexedDB']['open'](this.#indexedDBName, this.#indexedDBVersion);
                    _0x568e61['onupgradeneeded'] = _0x5da446 => {
                        const _0x26c0c9 = _0x5da446['target']['result'];
                        {
                            console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Upgrade\x20database'), this.#postStore['initStore'](_0x26c0c9), this.#sidoStore['initStore'](_0x26c0c9), this.#sggStore['initStore'](_0x26c0c9), this.#emdStore['initStore'](_0x26c0c9);
                        }
                    }, _0x568e61['onsuccess'] = () => {
                        this.#database = _0x568e61['result'], console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Init'), _0x41e776();
                    }, _0x568e61['onerror'] = _0xb273ff => {
                        const _0x3a4d2b = _0xb273ff['target']['error'];
                        console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Failed\x20to\x20init:\x20' + _0x3a4d2b['message']), _0xb08cac();
                    };
                }
            } catch (_0x24a3a6) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0x24a3a6['message']), _0xb08cac();
            }
        });
    }
    #deleteDatabase() {
        return this.#database = null, new Promise(_0x533937 => {
            try {
                if (!window['indexedDB'])
                    _0x533937();
                else {
                    const _0x2067bd = window['indexedDB']['deleteDatabase'](this.#indexedDBName);
                    _0x2067bd['onsuccess'] = () => {
                        console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Deleted\x20successfully'), _0x533937();
                    }, _0x2067bd['onerror'] = _0x261e4c => {
                        const _0x48f96e = _0x261e4c['target']['error'];
                        console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Error\x20deleting:\x20' + _0x48f96e['message']), _0x533937();
                    };
                }
            } catch (_0x4de012) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0x4de012['message']), _0x533937();
            }
        });
    }
    ['existDatabase']() {
        return this.#database != null;
    }
    async ['resizeDatabase']() {
        if (this.#database != null)
            try {
                await this.#postStore['resizeStore'](this.#database), await this.#sidoStore['resizeStore'](this.#database), await this.#sggStore['resizeStore'](this.#database), await this.#emdStore['resizeStore'](this.#database);
            } catch (_0x30152d) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0x30152d['message']), await this.#deleteDatabase();
            }
    }
    async ['checkDataVersion'](_0x34e995) {
        if (this.#database != null)
            try {
                const _0x4438da = await this.#postStore['checkDataVersion'](this.#database, _0x34e995), _0x2b491f = await this.#sidoStore['checkDataVersion'](this.#database, _0x34e995), _0x22c5a2 = await this.#sggStore['checkDataVersion'](this.#database, _0x34e995), _0x5415c0 = await this.#emdStore['checkDataVersion'](this.#database, _0x34e995);
                return _0x4438da && _0x2b491f && _0x22c5a2 && _0x5415c0;
            } catch (_0x4e254d) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0x4e254d['message']), await this.#deleteDatabase();
            }
        return ![];
    }
    ['addPostData'](_0x406765, _0x1e3550) {
        this.#database != null && this.#postStore['addData'](_0x406765, _0x1e3550);
    }
    ['addSidoData'](_0x278256, _0x26555b) {
        this.#database != null && this.#sidoStore['addData'](_0x278256, _0x26555b);
    }
    ['addSggData'](_0x2e9673, _0x5f49e4) {
        this.#database != null && this.#sggStore['addData'](_0x2e9673, _0x5f49e4);
    }
    ['addEmdData'](_0x4eee2b, _0x2156fe) {
        this.#database != null && this.#emdStore['addData'](_0x4eee2b, _0x2156fe);
    }
    async ['putDatas']() {
        if (this.#database != null)
            try {
                await this.#postStore['putDatas'](this.#database), await this.#sidoStore['putDatas'](this.#database), await this.#sggStore['putDatas'](this.#database), await this.#emdStore['putDatas'](this.#database);
            } catch (_0x292f57) {
                console['log']('[logi.maps]\x20Failed\x20to\x20putDatas\x20(' + _0x292f57['message'] + ')'), await this.#deleteDatabase();
            }
    }
    async ['getPostDatas'](_0x50a1b3) {
        if (_0x50a1b3?.['length'] > 0x0) {
            if (this.#database == null) {
                const _0xcd83a = [];
                for (let _0x541dba of _0x50a1b3) {
                    _0xcd83a['push']({
                        'dataKey': _0x541dba,
                        'dataValue': null
                    });
                }
                return _0xcd83a;
            } else
                try {
                    return await this.#postStore['getDatas'](this.#database, _0x50a1b3);
                } catch (_0x15b785) {
                    console['log']('[logi.maps]\x20Failed\x20to\x20getPostDatas\x20(' + _0x15b785['message'] + ')'), await this.#deleteDatabase();
                }
        }
        return [];
    }
    async ['getSidoDatas'](_0x57bbe5) {
        if (_0x57bbe5?.['length'] > 0x0) {
            if (this.#database == null) {
                const _0x5d02d1 = [];
                for (let _0xd181b3 of _0x57bbe5) {
                    _0x5d02d1['push']({
                        'dataKey': _0xd181b3,
                        'dataValue': null
                    });
                }
                return _0x5d02d1;
            } else
                try {
                    return await this.#sidoStore['getDatas'](this.#database, _0x57bbe5);
                } catch (_0x3d5e47) {
                    console['log']('[logi.maps]\x20Failed\x20to\x20getSidoDatas\x20(' + _0x3d5e47['message'] + ')'), await this.#deleteDatabase();
                }
        }
        return [];
    }
    async ['getSggDatas'](_0x18cd1) {
        if (_0x18cd1?.['length'] > 0x0) {
            if (this.#database == null) {
                const _0x4acae6 = [];
                for (let _0x2a32a5 of _0x18cd1) {
                    _0x4acae6['push']({
                        'dataKey': _0x2a32a5,
                        'dataValue': null
                    });
                }
                return _0x4acae6;
            } else
                try {
                    return await this.#sggStore['getDatas'](this.#database, _0x18cd1);
                } catch (_0x3d5b1b) {
                    console['log']('[logi.maps]\x20Failed\x20to\x20getSggDatas\x20(' + _0x3d5b1b['message'] + ')'), await this.#deleteDatabase();
                }
        }
        return [];
    }
    async ['getEmdDatas'](_0x599a98) {
        if (_0x599a98?.['length'] > 0x0) {
            if (this.#database == null) {
                const _0x514edd = [];
                for (let _0x144e88 of _0x599a98) {
                    _0x514edd['push']({
                        'dataKey': _0x144e88,
                        'dataValue': null
                    });
                }
                return _0x514edd;
            } else
                try {
                    return await this.#emdStore['getDatas'](this.#database, _0x599a98);
                } catch (_0x10961d) {
                    console['log']('[logi.maps]\x20Failed\x20to\x20getEmdDatas\x20(' + _0x10961d['message'] + ')'), await this.#deleteDatabase();
                }
        }
        return [];
    }
    async ['getPostData'](_0x173459) {
        if (this.#database != null)
            return await this.#postStore['getData'](this.#database, _0x173459);
        return {
            'dataKey': _0x173459,
            'dataValue': null
        };
    }
    async ['getSidoData'](_0xbe57ee) {
        if (this.#database != null)
            return await this.#sidoStore['getData'](this.#database, _0xbe57ee);
        return {
            'dataKey': _0xbe57ee,
            'dataValue': null
        };
    }
    async ['getSggData'](_0x311803) {
        if (this.#database != null)
            return await this.#sggStore['getData'](this.#database, _0x311803);
        return {
            'dataKey': _0x311803,
            'dataValue': null
        };
    }
    async ['getEmdData'](_0x447054) {
        if (this.#database != null)
            return await this.#emdStore['getData'](this.#database, _0x447054);
        return {
            'dataKey': _0x447054,
            'dataValue': null
        };
    }
};
export default logi['maps']['VtDatabase'];