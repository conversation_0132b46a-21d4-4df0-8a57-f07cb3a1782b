import a2_0x228aec from '../common/logi-maps-defines.js?v=2.1.10.1';
import a2_0x46ba98 from './logi-maps-databasestore.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a2_0x228aec, logi['maps']['DatabaseStore'] = a2_0x46ba98, logi['maps']['ResDatabase'] = class {
    #database = null;
    #resStore = null;
    #indexedDBName = 'logires';
    #indexedDBVersion = 0x1;
    constructor() {
        const _0x4a8222 = {
            'versionKey': '_VERSION_',
            'maxRowCnt': 0x400,
            'checkRowCntTickTime': 0xfa0,
            'maxStoreDataSize': 0x100,
            'maxDelayPutCnt': 0x20,
            'minDelayPutTickTime': 0x3e8,
            'maxDelayPutTickTime': 0xfa0
        };
        this.#resStore = new logi['maps']['DatabaseStore']('res', 'index_res', _0x4a8222);
    }
    async ['initDatabase']() {
        let _0xbc713b = null;
        try {
            _0xbc713b = await this.#initDatabase();
        } catch (_0x2763f2) {
            await this.#deleteDatabase(), _0xbc713b = await this.#initDatabase();
        }
        return _0xbc713b;
    }
    #initDatabase() {
        return new Promise((_0x1f2460, _0x219800) => {
            try {
                if (!window['indexedDB'])
                    _0x219800();
                else {
                    const _0x3f8190 = window['indexedDB']['open'](this.#indexedDBName, this.#indexedDBVersion);
                    _0x3f8190['onupgradeneeded'] = _0x4de28b => {
                        const _0x269307 = _0x4de28b['target']['result'];
                        {
                            console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Upgrade\x20database'), this.#resStore['initStore'](_0x269307);
                        }
                    }, _0x3f8190['onsuccess'] = () => {
                        this.#database = _0x3f8190['result'], console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Init'), _0x1f2460();
                    }, _0x3f8190['onerror'] = _0x41c3da => {
                        const _0x4577e1 = _0x41c3da['target']['error'];
                        console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Failed\x20to\x20init:\x20' + _0x4577e1['message']), _0x219800();
                    };
                }
            } catch (_0x44048d) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0x44048d['message']), _0x219800();
            }
        });
    }
    #deleteDatabase() {
        return this.#database = null, new Promise(_0x3322b1 => {
            try {
                if (!window['indexedDB'])
                    _0x3322b1();
                else {
                    const _0x3bf63d = window['indexedDB']['deleteDatabase'](this.#indexedDBName);
                    _0x3bf63d['onsuccess'] = () => {
                        console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Deleted\x20successfully'), _0x3322b1();
                    }, _0x3bf63d['onerror'] = _0x477219 => {
                        const _0x4a32a0 = _0x477219['target']['error'];
                        console['log']('[logi.maps][' + this.#indexedDBName + ']\x20Error\x20deleting:\x20' + _0x4a32a0['message']), _0x3322b1();
                    };
                }
            } catch (_0xafe394) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0xafe394['message']), _0x3322b1();
            }
        });
    }
    ['existDatabase']() {
        return this.#database != null;
    }
    async ['resizeDatabase']() {
        if (this.#database != null)
            try {
                await this.#resStore['resizeStore'](this.#database);
            } catch (_0x1c4401) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0x1c4401['message']), await this.#deleteDatabase();
            }
    }
    async ['checkDataVersion'](_0x35b1fd) {
        if (this.#database != null)
            try {
                return await this.#resStore['checkDataVersion'](this.#database, _0x35b1fd);
            } catch (_0x3c84af) {
                console['log']('[logi.maps][' + this.#indexedDBName + ']:\x20' + _0x3c84af['message']), await this.#deleteDatabase();
            }
        return ![];
    }
    ['addResData'](_0x5da1ca, _0x1a1241) {
        this.#database != null && this.#resStore['addData'](_0x5da1ca, _0x1a1241);
    }
    async ['putDatas']() {
        if (this.#database != null)
            try {
                await this.#resStore['putDatas'](this.#database);
            } catch (_0x5d5038) {
                console['log']('[logi.maps]\x20Failed\x20to\x20putDatas\x20(' + _0x5d5038['message'] + ')'), await this.#deleteDatabase();
            }
    }
    async ['getResData'](_0x32a62f) {
        if (this.#database != null)
            return await this.#resStore['getData'](this.#database, _0x32a62f);
        return {
            'dataKey': _0x32a62f,
            'dataValue': null
        };
    }
};
export default logi['maps']['ResDatabase'];