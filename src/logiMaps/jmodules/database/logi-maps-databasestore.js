import a0_0x3e5010 from '../common/logi-maps-defines.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a0_0x3e5010, logi['maps']['DatabaseStore'] = class {
    #storeName = '';
    #indexName = '';
    #versionKey = '_VERSION_';
    #MAX_ROW_CNT = 0x2800;
    #CHECK_ROW_CNT_TICKTIME = 0xfa0;
    #lastCheckRowCntTickTime = 0x0;
    #storeDatas = new Map();
    #MAX_STORE_DATA_SIZE = 0x200;
    #MAX_DELAY_PUT_CNT = 0x20;
    #MIN_DELAY_PUT_TICKTIME = 0x3e8;
    #MAX_DELAY_PUT_TICKTIME = 0xfa0;
    #lastPutTickTime = 0x0;
    constructor(_0xed1d24, _0x5eade7, _0x254c44) {
        this.#storeName = _0xed1d24, this.#indexName = _0x5eade7, _0x254c44?.['versionKey'] && (this.#versionKey = _0x254c44['versionKey']), _0x254c44?.['maxRowCnt'] && (this.#MAX_ROW_CNT = _0x254c44['maxRowCnt']), _0x254c44?.['checkRowCntTickTime'] && (this.#CHECK_ROW_CNT_TICKTIME = _0x254c44['checkRowCntTickTime']), _0x254c44?.['maxStoreDataSize'] && (this.#MAX_STORE_DATA_SIZE = _0x254c44['maxStoreDataSize']), _0x254c44?.['maxDelayPutCnt'] && (this.#MAX_DELAY_PUT_CNT = _0x254c44['maxDelayPutCnt']), _0x254c44?.['minDelayPutTickTime'] && (this.#MIN_DELAY_PUT_TICKTIME = _0x254c44['minDelayPutTickTime']), _0x254c44?.['maxDelayPutTickTime'] && (this.#MAX_DELAY_PUT_TICKTIME = _0x254c44['maxDelayPutTickTime']);
    }
    ['initStore'](_0x24e598) {
        _0x24e598['objectStoreNames']['contains'](this.#storeName) && (_0x24e598['deleteObjectStore'](this.#storeName), console['log']('[logi.maps]\x20Delete\x20store:\x20' + this.#storeName));
        const _0x4165f6 = _0x24e598['createObjectStore'](this.#storeName, { 'keyPath': 'key' });
        _0x4165f6['createIndex'](this.#indexName, 'key', { 'unique': !![] }), console['log']('[logi.maps]\x20Create\x20store:\x20' + this.#storeName);
    }
    ['resizeStore'](_0x179224) {
        return new Promise((_0x32893e, _0x1b7713) => {
            try {
                const _0x41ace6 = new Date()['getTime']();
                if (_0x41ace6 < this.#lastCheckRowCntTickTime + this.#CHECK_ROW_CNT_TICKTIME)
                    _0x32893e();
                else {
                    this.#lastCheckRowCntTickTime = _0x41ace6 + this.#CHECK_ROW_CNT_TICKTIME * 0x4;
                    const _0x3c2d51 = _0x179224['transaction']([this.#storeName], 'readwrite'), _0x26464c = _0x3c2d51['objectStore'](this.#storeName), _0x4d33f4 = _0x26464c['count']();
                    _0x4d33f4['onsuccess'] = () => {
                        if (_0x4d33f4['result'] > this.#MAX_ROW_CNT) {
                            let _0x5cc236 = parseInt(this.#MAX_ROW_CNT * 0.4);
                            const _0x452448 = _0x26464c['openCursor'](null, 'prev');
                            _0x452448['onsuccess'] = _0x23b48d => {
                                const _0x594f94 = _0x23b48d['target']['result'];
                                _0x594f94 && (_0x594f94['key'] !== this.#versionKey && (_0x594f94['delete'](), _0x5cc236 -= 0x1), _0x5cc236 >= 0x0 && _0x594f94['continue']());
                            };
                        }
                        this.#lastCheckRowCntTickTime = new Date()['getTime'](), _0x32893e();
                    }, _0x4d33f4['onerror'] = _0xaaa4a6 => {
                        _0x1b7713(_0xaaa4a6['target']['error']);
                    };
                }
            } catch (_0x3abce6) {
                _0x1b7713(_0x3abce6);
            }
        });
    }
    async ['checkDataVersion'](_0x1bb278, _0x56859d) {
        try {
            const _0x5e2141 = await this.#getDataVersion(_0x1bb278);
            if (_0x56859d == _0x5e2141)
                return !![];
            await this['clearAll'](_0x1bb278), await this.#putDataVersion(_0x1bb278, _0x56859d);
        } catch (_0x41c1df) {
        }
        return ![];
    }
    ['addData'](_0x553cc4, _0x351f9d) {
        this.#storeDatas['size'] < this.#MAX_STORE_DATA_SIZE && this.#storeDatas['set'](_0x553cc4, _0x351f9d);
    }
    ['putDatas'](_0x588320) {
        return new Promise((_0x44b2c2, _0x56dac4) => {
            const _0x22c75a = new Date()['getTime']();
            if (this.#storeDatas['size'] >= this.#MAX_DELAY_PUT_CNT && _0x22c75a >= this.#lastPutTickTime + this.#MIN_DELAY_PUT_TICKTIME || this.#storeDatas['size'] > 0x0 && _0x22c75a >= this.#lastPutTickTime + this.#MAX_DELAY_PUT_TICKTIME) {
                this.#lastPutTickTime = _0x22c75a + this.#MAX_DELAY_PUT_TICKTIME * 0x4;
                const _0x12b1ea = _0x588320['transaction']([this.#storeName], 'readwrite'), _0x4f7a8d = _0x12b1ea['objectStore'](this.#storeName);
                let _0x5854dd = this.#MAX_DELAY_PUT_CNT, _0x39d5a4 = 0x0, _0x16669f = 0x0;
                this.#MAX_STORE_DATA_SIZE == this.#storeDatas['size'] ? _0x16669f = 0x2 : _0x16669f = 0x1;
                for (const [_0x59352d, _0x25e3c8] of this.#storeDatas) {
                    if (++_0x39d5a4 % _0x16669f == 0x0) {
                        if (--_0x5854dd < 0x0)
                            break;
                        _0x4f7a8d['put']({
                            'key': _0x59352d,
                            'value': _0x25e3c8
                        });
                    }
                    this.#storeDatas['delete'](_0x59352d);
                }
                _0x12b1ea['oncomplete'] = () => {
                    this.#lastPutTickTime = new Date()['getTime'](), _0x44b2c2();
                }, _0x12b1ea['onerror'] = _0x4320bd => {
                    this.#lastPutTickTime = new Date()['getTime'](), _0x56dac4(_0x4320bd['target']['error']);
                };
            } else
                _0x44b2c2();
        });
    }
    ['getDatas'](_0x10152a, _0x250982) {
        try {
            const _0x1435d6 = _0x10152a['transaction']([this.#storeName], 'readonly'), _0x13077f = _0x1435d6['objectStore'](this.#storeName), _0x4f1a1e = _0x13077f['index'](this.#indexName);
            let _0x47618d = [];
            for (let _0x17ff94 of _0x250982) {
                const _0x51bc88 = new Promise(_0x5ada93 => {
                    const _0xe0be1d = _0x4f1a1e['get'](_0x17ff94);
                    _0xe0be1d['onsuccess'] = _0x8a5362 => {
                        _0x5ada93({
                            'dataKey': _0x17ff94,
                            'dataValue': _0x8a5362['target']['result']?.['value']
                        });
                    }, _0xe0be1d['onerror'] = () => {
                        _0x5ada93({
                            'dataKey': _0x17ff94,
                            'dataValue': null
                        });
                    };
                });
                _0x47618d['push'](_0x51bc88);
            }
            return new Promise(_0x1fe38e => {
                Promise['all'](_0x47618d)['then'](_0x75e6d9 => {
                    _0x1fe38e(_0x75e6d9);
                });
            });
        } catch (_0x34fc4a) {
            return console['log']('[logi.maps]\x20' + this.#storeName + '\x20getDatas:\x20' + _0x34fc4a), new Promise(_0x2143fd => {
                _0x2143fd([]);
            });
        }
    }
    ['getData'](_0x4cf6ec, _0x3373a1) {
        return new Promise((_0x1869bf, _0x5e9199) => {
            try {
                const _0x394335 = _0x4cf6ec['transaction']([this.#storeName], 'readonly'), _0x5a6e50 = _0x394335['objectStore'](this.#storeName), _0x43e676 = _0x5a6e50['index'](this.#indexName), _0x59eed5 = _0x43e676['get'](_0x3373a1);
                _0x59eed5['onsuccess'] = _0x2873ca => {
                    _0x1869bf({
                        'dataKey': _0x3373a1,
                        'dataValue': _0x2873ca['target']['result']?.['value']
                    });
                }, _0x59eed5['onerror'] = () => {
                    _0x1869bf({
                        'dataKey': _0x3373a1,
                        'dataValue': null
                    });
                };
            } catch (_0x73a091) {
                _0x5e9199(_0x73a091);
            }
        });
    }
    ['clearAll'](_0x37f285) {
        return new Promise((_0x20b26a, _0x56f5c6) => {
            try {
                const _0x47f446 = _0x37f285['transaction']([this.#storeName], 'readwrite'), _0x4381df = _0x47f446['objectStore'](this.#storeName), _0x2eb9c9 = _0x4381df['clear']();
                _0x2eb9c9['onsuccess'] = () => {
                    _0x20b26a();
                }, _0x2eb9c9['onerror'] = _0xfafabc => {
                    _0x56f5c6(_0xfafabc['target']['error']);
                };
            } catch (_0x2361ab) {
                _0x56f5c6(_0x2361ab);
            }
        });
    }
    #putDataVersion(_0x11a0a6, _0x445bea) {
        return new Promise((_0xabefe5, _0x50c986) => {
            try {
                const _0x355922 = _0x11a0a6['transaction']([this.#storeName], 'readwrite'), _0x5bff83 = _0x355922['objectStore'](this.#storeName), _0x4cafb0 = {
                        'key': this.#versionKey,
                        'value': _0x445bea
                    }, _0x271219 = _0x5bff83['put'](_0x4cafb0);
                _0x271219['onsuccess'] = () => {
                    _0xabefe5();
                }, _0x271219['onerror'] = _0x1f19c8 => {
                    _0x50c986(_0x1f19c8['target']['error']);
                };
            } catch (_0xbc8b90) {
                _0x50c986(_0xbc8b90);
            }
        });
    }
    #getDataVersion(_0x488166) {
        return new Promise((_0x547d24, _0x41e94d) => {
            try {
                const _0x245b6f = _0x488166['transaction']([this.#storeName], 'readonly'), _0x46521c = _0x245b6f['objectStore'](this.#storeName), _0x474b32 = _0x46521c['index'](this.#indexName), _0xb845fb = _0x474b32['get'](this.#versionKey);
                _0xb845fb['onsuccess'] = _0xd0a9e2 => {
                    _0x547d24(_0xd0a9e2['target']['result']?.['value']);
                }, _0xb845fb['onerror'] = _0x5d365c => {
                    _0x547d24(_0x5d365c['target']['error']);
                };
            } catch (_0x350c4a) {
                _0x41e94d(_0x350c4a);
            }
        });
    }
};
export default logi['maps']['DatabaseStore'];