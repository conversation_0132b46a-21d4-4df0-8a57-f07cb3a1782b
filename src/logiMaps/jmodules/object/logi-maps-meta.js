import a6_0x2a545c from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a6_0x28b610 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a6_0x495e33 from '../utility/logi-maps-boundarychecker.js?v=2.1.10.1';
import a6_0x283488 from '../object/logi-maps-object.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Utils'] = a6_0x2a545c, logi['maps']['BoundaryData'] = a6_0x28b610, logi['maps']['BoundaryChecker'] = a6_0x495e33, logi['maps']['Object'] = a6_0x283488, logi['maps']['Meta'] = class extends logi['maps']['Object'] {
    #refId = null;
    #htmlElem = null;
    #retryCnt = 0x0;
    constructor(_0x734d89, _0x44fdca) {
        const _0x30b583 = _0x44fdca?.['key'] ?? 'me_' + Math['random']()['toString'](0x24)['slice'](-0x8), _0x501629 = _0x44fdca?.['class'] ?? '';
        super(_0x30b583, logi['maps']['Object']['OBJTYPE']['meta'], _0x501629, 0x0), this.#refId = _0x734d89, this['setMap'](_0x44fdca?.['map']);
    }
    ['isHit'](_0x2e5ed8) {
        return ![];
    }
    ['isOverlap'](_0x348a97) {
        if (!this['getLayer']())
            return ![];
        return ![];
    }
    #updateMetaPostion(_0x5a2b46) {
        if (_0x5a2b46['dataset']?.['lng'] && _0x5a2b46['dataset']?.['lat']) {
            const _0x2c9183 = this['getMapCoord'](), _0x331501 = _0x2c9183['world2screen'](Number(_0x5a2b46['dataset']['lng']), Number(_0x5a2b46['dataset']['lat']));
            _0x5a2b46['style']['visibility'] != 'visible' && (_0x5a2b46['style']['visibility'] = 'visible'), _0x5a2b46['style']['left'] = _0x331501['x'] + 'px', _0x5a2b46['style']['top'] = _0x331501['y'] + 'px';
        }
    }
    ['drawCanvas']() {
        if (!this.#htmlElem) {
            const _0x24a1ab = this['getLayer']()?.['getDivElem']();
            if (_0x24a1ab) {
                const _0x56053d = _0x24a1ab['querySelector']('[id=\x22' + this.#refId + '\x22]');
                _0x56053d && (_0x56053d['dataset']?.['lat'] && _0x56053d['dataset']?.['lng'] && (this.#htmlElem = _0x56053d));
            }
        }
        this.#htmlElem ? (this.#retryCnt = 0x0, this.#updateMetaPostion(this.#htmlElem)) : (this.#retryCnt += 0x1, this.#retryCnt > 0x40 && this['setMap'](null));
    }
};
export default logi['maps']['Meta'];