import a3_0x1771c5 from '../common/logi-maps-defines.js?v=2.1.10.1';
import a3_0x525957 from '../common/logi-maps-types.js?v=2.1.10.1';
import a3_0x974b5a from '../resource/logi-maps-resource.js?v=2.1.10.1';
import a3_0x178789 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a3_0x3c7fa5 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a3_0x33eb5a from '../utility/logi-maps-boundarychecker.js?v=2.1.10.1';
import a3_0x528bd8 from '../object/logi-maps-object.js?v=2.1.10.1';
import a3_0x44eb67 from '../object/logi-maps-textinfo.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a3_0x1771c5, logi['maps']['Point'] = a3_0x525957['Point'], logi['maps']['TileInfo'] = a3_0x525957['TileInfo'], logi['maps']['ALIGN'] = a3_0x525957['ALIGN'], logi['maps']['Resource'] = a3_0x974b5a, logi['maps']['Utils'] = a3_0x178789, logi['maps']['BoundaryData'] = a3_0x3c7fa5, logi['maps']['BoundaryChecker'] = a3_0x33eb5a, logi['maps']['Object'] = a3_0x528bd8, logi['maps']['TextInfo'] = a3_0x44eb67, logi['maps']['Image'] = class extends logi['maps']['Object'] {
    #src = '';
    #position = {
        'lng': 0x0,
        'lat': 0x0
    };
    #offsetX;
    #offsetY;
    #angle;
    #align;
    #image = null;
    #withTextInfos = new Array();
    #tileInfo = new logi['maps']['TileInfo']();
    #expiredTileId = !![];
    #overlapInfo = {
        'screenPos': new logi['maps']['Point'](),
        'visibility': ![],
        'bgRadius': 0xa,
        'bgColor': '#BBBBBB88',
        'fontSize': 0xc,
        'textColor': '#000000DD'
    };
    #boundaryData = new logi['maps']['BoundaryData']();
    #boundaryPadding = {
        'left': 0x0,
        'top': 0x0,
        'right': 0x0,
        'bottom': 0x0
    };
    /**
   * @preserve .
   * @constructor
   * @description
   *  이미지를 생성한다.
   * @param {String} src 이미지 경로
   * @param {logi.maps.LatLng} position 이미지 위치
   * @param {Object} options option
   *  @param {String} options.key image key (default: random 생성)
   *  @param {String} options.class image class (CSS의 class와 비슷함)
   *  @param {Number} options.zIndex 그리기 순서 (default: 0)
   *  @param {Number} options.angle 이미지 각도
   *  @param {ALIGN} options.align 이미지 정렬
   *  @param {Number} options.groupId 같은 그룹끼리 바운더리가 겹치면 하나가 제거된다. (default: 0은 제외)
   *  @param {Number} options.offsetX offset x
   *  @param {Number} options.offsetY offset y
   *  @param {logi.maps.TextInfo} options.textInfo Text 정보
   *  @param {logi.maps.Map} options.map 표시될 Map
   * @example
   *  let image = new logi.maps.Image('/img/icon.png', { lat: 37.566596, lng: 127.007702 }, {map: logiMap});
   *  //동대문시장 위에 이미지가 표시된다.
   */
    constructor(_0x26e559, _0x9aed0c, _0xfc237) {
        const _0x4b6342 = _0xfc237?.['key'] ?? 'im_' + Math['random']()['toString'](0x24)['slice'](-0x8), _0x23fc5d = _0xfc237?.['class'] ?? '', _0x76e8f6 = _0xfc237?.['zIndex'] ?? 0x0;
        super(_0x4b6342, logi['maps']['Object']['OBJTYPE']['image'], _0x23fc5d, _0x76e8f6), this['setImageSrc'](_0x26e559 ?? ''), this.#position['lng'] = _0x9aed0c?.['lng'] ?? 0x0, this.#position['lat'] = _0x9aed0c?.['lat'] ?? 0x0, this.#offsetX = _0xfc237?.['offsetX'] ?? 0x0, this.#offsetY = _0xfc237?.['offsetY'] ?? 0x0, this.#angle = _0xfc237?.['angle'] ?? 0x0, this.#align = _0xfc237?.['align'] ?? logi['maps']['ALIGN']['CM'], this.#boundaryData['groupId'] = _0xfc237?.['groupId'] ?? 0x0, this['resetBoundary'](), _0xfc237?.['textInfo'] && (this['textInfo'] = _0xfc237['textInfo']), this['setMap'](_0xfc237?.['map']);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  이미지를 등록한다.
   * @param {String} src 이미지 주소
   * @example
   *  image.setImageSrc('/img/icon.png');
   *  //icon.png 이미지가 등록된다.
   */
    async ['setImageSrc'](_0x5a3821) {
        try {
            this.#src = _0x5a3821 ?? '';
            const _0xd3f550 = await logi['maps']['Resource']['getImage'](this.#src);
            this.#image = _0xd3f550, this['setUpdateFlag']();
        } catch (_0xac3f55) {
            this.#src = '', this.#image = null, this['setUpdateFlag']();
            _0xac3f55 && console['log'](_0xac3f55);
            throw _0xac3f55;
        }
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 이미지의 주소를 전달한다.
   * @returns {String} 이미지 주소
   * @example
   *  let imageSrc = image.getImageSrc();
   *  //등록된 이미지의 주소가 전달된다.
   */
    ['getImageSrc']() {
        return this.#src;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  이미지의 위치를 지정한다.
   * @param {logi.maps.LatLng} latlng 이미지 위치
   * @example
   *  image.setPosition({lat: 37.566596, lng: 127.007702});
   *  //동대문시장 위에 이미지가 그려진다.
   */
    ['setPosition'](_0x3ab00e) {
        (this.#position['lng'] !== _0x3ab00e['lng'] || this.#position['lat'] !== _0x3ab00e['lat']) && (this.#position['lng'] = _0x3ab00e['lng'], this.#position['lat'] = _0x3ab00e['lat'], (this.#position['lng'] < this['tileInfo']['boundary']['xMin'] || this.#position['lng'] > this['tileInfo']['boundary']['xMax'] || this.#position['lat'] < this['tileInfo']['boundary']['yMin'] || this.#position['lat'] > this['tileInfo']['boundary']['yMax']) && (this['expiredTileId'] = !![]), this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  이미지의 위치를 전달한다.
   * @returns {logi.maps.LatLng} 이미지 위치
   * @example
   *  let position = image.getPosition();
   *  //이미지의 위치가 전달된다.
   */
    ['getPosition']() {
        return {
            'lng': this.#position['lng'],
            'lat': this.#position['lat']
        };
    }
    /**
   * @preserve .
   * @method
   * @description
   *  이미지 위치의 offset을 설정한다.
   * @param {Number} offsetX offset X
   * @example
   *  image.setOffsetX(100);
   *  //이미지의 실제 위치에서 화면 x좌표 100만큼 떨어진 곳에 그려진다.
   */
    ['setOffsetX'](_0x3d66fa) {
        _0x3d66fa = _0x3d66fa ?? 0x0, this.#offsetX !== _0x3d66fa && (this.#offsetX = _0x3d66fa, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  이미지의 offset을 전달한다.
   * @returns {Number} offset X
   * @example
   *  let offset = image.getOffsetX();
   *  //이미지의 offset 값이 전달된다.
   */
    ['getOffsetX']() {
        return this.#offsetX;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  이미지 위치의 offset을 설정한다.
   * @param {Number} offsetY offset Y
   * @example
   *  image.setOffsetY(100);
   *  //이미지의 실제 위치에서 화면 y좌표 100만큼 떨어진 곳에 그려진다.
   */
    ['setOffsetY'](_0x245701) {
        _0x245701 = _0x245701 ?? 0x0, this.#offsetY !== _0x245701 && (this.#offsetY = _0x245701, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  이미지의 offset을 전달한다.
   * @returns {Number} offset Y
   * @example
   *  let offset = image.getOffsetY();
   *  //이미지의 offset 값이 전달된다.
   */
    ['getOffsetY']() {
        return this.#offsetY;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  이미지의 각도를 지정한다.
   *  시계 방향으로 회전
   * @param {Number} angle 각도
   * @example
   *  image.setAngle(90);
   *  //이미지가 90도 회전되어 그려진다.
   */
    ['setAngle'](_0x2c5dc0) {
        this.#angle !== _0x2c5dc0 && (this.#angle = _0x2c5dc0, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  이미지의 각도를 전달한다.
   *  시계 방향으로 회전
   * @returns {Number} 각도
   * @example
   *  let angle = image.getAngle();
   *  //이미지의 각도가 전달된다.
   */
    ['getAngle']() {
        return this.#angle;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  이미지의 위치와 각도를 지정한다.
   * @param {logi.maps.LatLng} latlng 위치
   * @param {Number} angle 각도
   * @example
   *  image.move({lat: 37.566596, lng: 127.007702}, 90);
   *  //동대문시장 위에 이미지가 90도 회전되어 그려진다.
   */
    ['move'](_0x5476ee, _0x46721a) {
        if (this.#position['lng'] !== _0x5476ee['lng'] || this.#position['lat'] !== _0x5476ee['lat'] || this.#angle !== _0x46721a)
            return this['setPosition'](_0x5476ee), this['setAngle'](_0x46721a), !![];
        return ![];
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Text를 등록한다.
   * @param {logi.maps.TextInfo} textInfo text정보
   * @example
   *  image.textInfo = {offsetX: 0, offsetY: 0, text: ' test-text', textColor: 'red', fontSize: 12};
   *  //이미지에 글자가 추가된다.
   */
    set ['textInfo'](_0x3ef2d6) {
        this.#setTextInfo(_0x3ef2d6);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Text를 조회한다.
   *  Block member variable access using getter.
   * @returns {logi.maps.TextInfo} text정보
   * @example
   *  let textInfo = image.textInfo;
   *  //text정보를 가져온다.
   */
    get ['textInfo']() {
        return !this.#withTextInfos[0x0] && (this.#withTextInfos[0x0] = new logi['maps']['TextInfo'](this)), this.#withTextInfos[0x0];
    }
    /**
   * @preserve .
   * @method
   * @description
   *  TextInfo를 추가한다.
   * @param {logi.maps.TextInfo} textInfo text정보
   * @example
   *  image.addTextInfo({offsetX: 0, offsetY: 0, text: 'test-text', textColor: 'red', fontSize: 12});
   *  //TextInfo가 추가된다.
   */
    ['addTextInfo'](_0x1ef30a) {
        this.#setTextInfo(_0x1ef30a, this.#withTextInfos['length']);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  배열 인덱스에 위치한 TextInfo를 전달한다.
   * @param {Number} idx 배열 인덱스
   * @returns {logi.maps.TextInfo} text정보 (null: 해당 배열에 값이 없음)
   * @example
   *  let textInfo = image.getTextInfo(0);
   *  //0번째 위치한 TextInfo가 전달된다.
   */
    ['getTextInfo'](_0x451ec6) {
        if (_0x451ec6 >= this.#withTextInfos['length'])
            return null;
        return this.#withTextInfos[_0x451ec6];
    }
    /**
   * @preserve .
   * @method
   * @description
   *  배열 인덱스에 위치한 TextInfo를 삭제한다.
   * @param {Number} idx 배열 인덱스
   * @example
   *  image.removeTextInfo(0);
   * //0번째 위치한 TextInfo가 삭제된다.
   */
    ['removeTextInfo'](_0x3bbea0) {
        this.#withTextInfos['splice'](_0x3bbea0, 0x1), this['setUpdateFlag']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 모든 TextInfo를 삭제한다.
   * @example
   *  image.removeTextInfos();
   * //모든 TextInfo가 삭제된다.
   */
    ['removeTextInfos']() {
        this.#withTextInfos = new Array(), this['setUpdateFlag']();
    }
    get ['tileInfo']() {
        return this.#tileInfo;
    }
    set ['expiredTileId'](_0x1c40e0) {
        this.#expiredTileId = _0x1c40e0;
    }
    get ['expiredTileId']() {
        return this.#expiredTileId;
    }
    get ['overlapInfo']() {
        return this.#overlapInfo;
    }
    get ['boundaryData']() {
        return this.#boundaryData;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  이미지의 바운더리를 조정할 수 있다. padding 정보를 고려하여 overlap을 체크한다.
   * @param {Number} left padding left
   * @param {Number} top padding top
   * @param {Number} right padding right
   * @param {Number} bottom padding bottom
   * @example
   *  image.setBoundaryPadding(10, 10, 10, 10);
   *  //실제 바운더리보다 상하좌우 10정도 작게 체크한다.
   */
    ['setBoundaryPadding'](_0x471d56, _0x231901, _0x5e6d3b, _0x3bf7d0) {
        _0x471d56 = _0x471d56 ?? 0x0, _0x231901 = _0x231901 ?? 0x0, _0x5e6d3b = _0x5e6d3b ?? 0x0, _0x3bf7d0 = _0x3bf7d0 ?? 0x0, (this.#boundaryPadding['left'] != _0x471d56 || this.#boundaryPadding['top'] != _0x231901 || this.#boundaryPadding['right'] != _0x5e6d3b || this.#boundaryPadding['bottom'] != _0x3bf7d0) && (this.#boundaryPadding['left'] = _0x471d56, this.#boundaryPadding['top'] = _0x231901, this.#boundaryPadding['right'] = _0x5e6d3b, this.#boundaryPadding['bottom'] = _0x3bf7d0, this['setUpdateFlag']());
    }
    ['resetBoundary']() {
        this.#overlapInfo['screenPos']['x'] = 0x0, this.#overlapInfo['screenPos']['y'] = 0x0, this.#boundaryData['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], this.#boundaryData['tagName'] = this['getKey'](), this.#boundaryData['boundaryCircle'] = {
            'center': {
                'x': 0x0,
                'y': 0x0
            },
            'maxR': 0x0
        }, this.#boundaryData['boundaryRect'] = [
            {
                'x': 0x0,
                'y': 0x0
            },
            {
                'x': 0x0,
                'y': 0x0
            },
            {
                'x': 0x0,
                'y': 0x0
            },
            {
                'x': 0x0,
                'y': 0x0
            }
        ], this.#boundaryData['creationTick'] = logi['maps']['Utils']['getCurTick'](), this.#boundaryData['overlapCnt'] = 0x0;
    }
    ['updateBoundary']() {
        let _0x1ae472 = [
                {
                    'x': 0x0,
                    'y': 0x0
                },
                {
                    'x': 0x0,
                    'y': 0x0
                },
                {
                    'x': 0x0,
                    'y': 0x0
                },
                {
                    'x': 0x0,
                    'y': 0x0
                }
            ], _0x180ada = {
                'center': {
                    'x': 0x0,
                    'y': 0x0
                },
                'maxR': 0x0
            }, _0x5740e7 = this['getMapCoord'](), _0x14f12f = _0x5740e7['world2screen'](this.#position['lng'], this.#position['lat']);
        const _0x5a2ab0 = {
                'x': _0x14f12f['x'] + this.#offsetX,
                'y': _0x14f12f['y'] + this.#offsetY
            }, _0x2aeae1 = this.#image?.['naturalWidth'] ?? 0x4, _0x14dbb4 = this.#image?.['naturalHeight'] ?? 0x4;
        _0x1ae472 = logi['maps']['Utils']['getBoundaryBox'](_0x5a2ab0, _0x2aeae1, _0x14dbb4, this.#align, this.#angle, this.#boundaryPadding), _0x180ada = logi['maps']['Utils']['getBoundaryCircle'](_0x1ae472), this.#overlapInfo['screenPos']['x'] = Math['max'](_0x1ae472[0x0]['x'], _0x1ae472[0x2]['x']), this.#overlapInfo['screenPos']['y'] = Math['min'](_0x1ae472[0x0]['y'], _0x1ae472[0x2]['y']), this.#boundaryData['setBoundary'](_0x180ada, _0x1ae472);
    }
    /**
   * @preserve .
   * @method
   * @deprecated
   * 'changeImage' was declared deprecated. (>> setImageSrc)
   */
    async ['changeImage'](_0x25a8fd) {
        await this['setImageSrc'](_0x25a8fd);
    }
    /**
   * @preserve .
   * @method
   * @deprecated
   * 'createImage' was declared deprecated. (no need to call)
   */
    ['createImage']() {
    }
    /**
   * @preserve .
   * @method
   * @deprecated
   * 'changeTextBgImage' was declared deprecated. (no need to call)
   */
    ['changeTextBgImage']() {
    }
    async ['getSize']() {
        if (this.#src && this.#src != '') {
            if (!this.#image)
                await this['setImageSrc'](this.#src);
            else {
                const _0x384c8f = new URL(this.#image['src']);
                _0x384c8f['pathname'] != this.#src && await this['setImageSrc'](this.#src);
            }
            return {
                'width': this.#image?.['naturalWidth'] ?? 0x0,
                'height': this.#image?.['naturalHeight'] ?? 0x0
            };
        }
        return {
            'width': 0x0,
            'height': 0x0
        };
    }
    ['getAlign']() {
        return this.#align;
    }
    /**
   * @preserve .
   * @method
   * @deprecated
   * 'setText' was declared deprecated. (>> setter textInfo)
   */
    ['setText'](_0x419da1) {
        this['textInfo'] = _0x419da1;
    }
    ['isHit'](_0x4cc1a7) {
        if (!this['getLayer']())
            return ![];
        if (this.#boundaryData['bLoad'] != logi['maps']['BoundaryData']['STATUS']['LOAD'])
            return ![];
        return logi['maps']['BoundaryChecker']['pointInRegion'](_0x4cc1a7, this.#boundaryData['boundaryRect']);
    }
    ['isOverlap'](_0x4cc391) {
        if (!this['getLayer']())
            return ![];
        const _0x59fab9 = this['getMapCoord'](), _0x5c8022 = _0x59fab9['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x5c8022) == ![])
            return ![];
        return logi['maps']['BoundaryChecker']['regionOnRegion'](_0x4cc391, this.#boundaryData['boundaryRect']);
    }
    ['drawCanvas']() {
        if (!this['getLayer']())
            return;
        if (this.#boundaryData['bLoad'] != logi['maps']['BoundaryData']['STATUS']['LOAD'])
            return;
        const _0x13b53b = this['getMapCoord'](), _0x2ed1b5 = this['getGfx2d'](), _0x67bb66 = this['getDevicePixelRatio']();
        _0x2ed1b5['save'](), _0x2ed1b5['scale'](_0x67bb66, _0x67bb66);
        let _0x3639dd = _0x13b53b['world2screen'](this.#position['lng'], this.#position['lat']);
        _0x3639dd['x'] += this.#offsetX, _0x3639dd['y'] += this.#offsetY;
        if (this.#image) {
            const _0x162bd7 = this.#image['naturalWidth'], _0x21815f = this.#image['naturalHeight'];
            let _0x2988de = logi['maps']['Utils']['getAlignPosition'](_0x3639dd['x'], _0x3639dd['y'], this.#align, _0x162bd7, _0x21815f), _0x1b8843 = logi['maps']['Utils']['getPivotPoint'](this.#align, _0x162bd7, _0x21815f);
            _0x2ed1b5['drawObjImage'](this.#image, _0x2988de['x'], _0x2988de['y'], this.#angle, _0x1b8843['x'], _0x1b8843['y']);
        }
        for (let _0x19c285 = 0x0; _0x19c285 < this.#withTextInfos['length']; ++_0x19c285) {
            let _0x436a1d = this.#withTextInfos[_0x19c285];
            _0x436a1d?.['text'] && _0x436a1d?.['text'] !== '' && _0x436a1d['drawCanvas'](_0x3639dd['x'], _0x3639dd['y']);
        }
        if (this.#overlapInfo['visibility'] == !![] && this.#boundaryData['overlapCnt'] > 0x0) {
            const _0x1bbd10 = this.#boundaryData['overlapCnt'], _0x5d3003 = this.#overlapInfo['screenPos']['x'], _0x4e9949 = this.#overlapInfo['screenPos']['y'], _0x421f5a = this.#overlapInfo['bgRadius'], _0x3370ce = this.#overlapInfo['bgColor'], _0x5d1e98 = this.#overlapInfo['fontSize'], _0x535972 = this.#overlapInfo['textColor'];
            _0x2ed1b5['drawObjDot'](_0x5d3003, _0x4e9949, _0x421f5a, _0x3370ce), _0x2ed1b5['drawObjText'](_0x1bbd10, _0x5d3003, _0x4e9949, '', _0x5d1e98, ![], _0x535972, logi['maps']['ALIGN']['CM']);
        }
        logi['maps']['Defines']['DEBUG_MODE'] && _0x2ed1b5['drawObjPolyLine'](this.#boundaryData['boundaryRect'], 0x2, 'red'), _0x2ed1b5['restore']();
    }
    #setTextInfo(_0x49d835, _0x4b230c = 0x0) {
        _0x49d835 == null ? (this.#withTextInfos = new Array(), this['setUpdateFlag']()) : (!this.#withTextInfos[_0x4b230c] && (this.#withTextInfos[_0x4b230c] = new logi['maps']['TextInfo'](this)), _0x49d835['offsetX'] && (this.#withTextInfos[_0x4b230c]['offsetX'] = _0x49d835['offsetX']), _0x49d835['offsetY'] && (this.#withTextInfos[_0x4b230c]['offsetY'] = _0x49d835['offsetY']), _0x49d835['text'] && (this.#withTextInfos[_0x4b230c]['text'] = _0x49d835['text']), _0x49d835['fontFamily'] && (this.#withTextInfos[_0x4b230c]['fontFamily'] = _0x49d835['fontFamily']), _0x49d835['textBold'] && (this.#withTextInfos[_0x4b230c]['textBold'] = _0x49d835['textBold']), _0x49d835['textColor'] && (this.#withTextInfos[_0x4b230c]['textColor'] = _0x49d835['textColor']), _0x49d835['fontSize'] && (this.#withTextInfos[_0x4b230c]['fontSize'] = _0x49d835['fontSize']), _0x49d835['textAlign'] && (this.#withTextInfos[_0x4b230c]['textAlign'] = _0x49d835['textAlign']), this.#withTextInfos[_0x4b230c]['bgImgSrc'] = _0x49d835['bgImgSrc'] ?? _0x49d835['bgImg'], _0x49d835['bgImgAlign'] && (this.#withTextInfos[_0x4b230c]['bgImgAlign'] = _0x49d835['bgImgAlign']), _0x49d835['bgImgOffsetX'] && (this.#withTextInfos[_0x4b230c]['bgImgOffsetX'] = _0x49d835['bgImgOffsetX']), _0x49d835['bgImgOffsetY'] && (this.#withTextInfos[_0x4b230c]['bgImgOffsetY'] = _0x49d835['bgImgOffsetY']));
    }
};
export default logi['maps']['Image'];