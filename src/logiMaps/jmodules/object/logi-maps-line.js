import a5_0x1634e8 from '../common/logi-maps-types.js?v=2.1.10.1';
import a5_0x20a64b from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a5_0x596970 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a5_0x1d2e8a from '../utility/logi-maps-boundarychecker.js?v=2.1.10.1';
import a5_0x1a7750 from '../object/logi-maps-object.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['LINETYPE'] = a5_0x1634e8['LINETYPE'], logi['maps']['Utils'] = a5_0x20a64b, logi['maps']['BoundaryData'] = a5_0x596970, logi['maps']['BoundaryChecker'] = a5_0x1d2e8a, logi['maps']['Object'] = a5_0x1a7750, logi['maps']['Line'] = class extends logi['maps']['Object'] {
    #type = logi['maps']['LINETYPE']['STRAIGHT'];
    #info = {};
    #screenCoord = {
        'baseLayer': null,
        'tileLevel': null,
        'tileLevelOffset': null,
        'width': 0x0,
        'origin': {
            'x': 0x0,
            'y': 0x0
        },
        'points': new Array()
    };
    /**
   * @preserve .
   * @constructor
   * @description
   *  라인을 생성한다.
   * @param {LINETYPE} lineType 라인 타입 (STRAIGHT, POLY, DOT, DASH)
   * @param {Object} options option - line
   *  @param {String} options.key line key (default: random 생성)
   *  @param {String} options.class line class (CSS의 class와 비슷함)
   *  @param {Number} options.zIndex 그리기 순서 (default: 0)
   *  @param {logi.maps.LatLng} options.fromLatLng 시작 좌표(WGS84)
   *  @param {logi.maps.LatLng} options.toLatLng 끝 좌표(WGS84)
   *  @param {Number} options.width 라인 넓이
   *  @param {String} options.color 라인 색
   *  @param {Number} options.strokeWidth 스트로크 넓이
   *  @param {String} options.strokeColor 스트로크 색
   *  @param {logi.maps.Map} options.map 표시될 Map
   * @param {Object} options option - polyline
   *  @param {String} options.key [polyline] line key (default: random 생성)
   *  @param {String} options.class line class (CSS의 class와 비슷함)
   *  @param {Number} options.zIndex 그리기 순서 (default: 0)
   *  @param {logi.maps.LatLng[]} options.latlngs 좌표 리스트(WGS84)
   *  @param {Number} options.width 라인 넓이
   *  @param {String} options.color 라인 색
   *  @param {Number} options.strokeWidth 스트로크 넓이
   *  @param {String} options.strokeColor 스트로크 색
   *  @param {logi.maps.Map} options.map 표시될 Map
   * @param {Object} options option - dottedline
   *  @param {String} options.key line key (default: random 생성)
   *  @param {String} options.class As : line class (CSS의 class와 비슷함)
   *  @param {Number} options.zIndex As : 그리기 순서 (default: 0)
   *  @param {logi.maps.LatLng[]} options.latlngs As Array[]: 좌표 리스트((WGS84))
   *  @param {Number} options.dotRadius As : 점의 반지름
   *  @param {Number} options.dotGap As : 간격 길이
   *  @param {String} options.color As : 점 색
   *  @param {Number} options.strokeWidth As : 스트로크 넓이
   *  @param {String} options.strokeColor As : 스트로크 색
   *  @param {logi.maps.Map} options.map As : 표시될 Map
   * @param {Object} options option - dashedline
   *  @param {String} options.key line key (default: random 생성)
   *  @param {String} options.class As : line class (CSS의 class와 비슷함)
   *  @param {Number} options.zIndex As : 그리기 순서 (default: 0)
   *  @param {logi.maps.LatLng[]} options.latlngs As Array[]: 좌표 리스트((WGS84))
   *  @param {Number} options.width As : 라인 넓이
   *  @param {Number} options.dashLength As : 점선 길이
   *  @param {Number} options.dashSpace As : 간격 길이
   *  @param {String} options.color As : 라인 색
   *  @param {Number} options.strokeWidth As : 스트로크 넓이
   *  @param {String} options.strokeColor As : 스트로크 색
   *  @param {logi.maps.Map} options.map As : 표시될 Map
   * @example
   *  let line = new logi.maps.Line(logi.maps.LINETYPE.STRAIGHT, {
   *   fromLatLng: {lat: 37.37976, lng: 127.11697},
   *   toLatLng: {lat: 37.36575, lng: 127.12836},
   *   width: 2,
   *   color: 'white',
   *   map: logiMap
   *  });
   *  //직선을 생성한다.
   * @example
   *  let line = new logi.maps.Line(logi.maps.LINETYPE.POLY, {
   *   latlngs: [
   *    {lat: 37.5115557, lng: 127.0595261},
   *    {lat: 37.5062379, lng: 127.0050378},
   *    {lat: 37.566596, lng: 127.007702},
   *    {lat: 37.5251644, lng: 126.9255491}
   *   ],
   *   width : 5,
   *   color : '#0A32FF',
   *   strokeWidth : 1,
   *   strokeColor : 'red',
   *   map: logiMap
   *  });
   *  //폴리라인을 생성한다.
   * @example
   *  let line = new logi.maps.Line(logi.maps.LINETYPE.DOT, {
   *   latlngs: [
   *    {lat: 37.5115557, lng: 127.0595261},
   *    {lat: 37.5062379, lng: 127.0050378},
   *    {lat: 37.566596, lng: 127.007702},
   *    {lat: 37.5251644, lng: 126.9255491}
   *   ],
   *   dotRadius: 4,
   *   dotGap: 10,
   *   color: 'yellow',
   *   map: logiMap
   *  });
   *  //점선을 생성한다.
   * @example
   *  let line = new logi.maps.Line(logi.maps.LINETYPE.DASH, {
   *   latlngs: [
   *    {lat: 37.5115557, lng: 127.0595261},
   *    {lat: 37.5062379, lng: 127.0050378},
   *    {lat: 37.566596, lng: 127.007702},
   *    {lat: 37.5251644, lng: 126.9255491}
   *   ],
   *   width: 3,
   *   dashLength: 5,
   *   color: 'yellow',
   *   map: logiMap
   *  });
   *  //대쉬라인을 생성한다.
   */
    constructor(_0x12523f, _0x354449) {
        const _0x359c1c = _0x354449?.['key'] ?? 'ln_' + Math['random']()['toString'](0x24)['slice'](-0x8), _0x4a202b = _0x354449?.['class'] ?? '', _0x4a0005 = _0x354449?.['zIndex'] ?? 0x0;
        super(_0x359c1c, logi['maps']['Object']['OBJTYPE']['line'], _0x4a202b, _0x4a0005), this.#type = _0x12523f;
        switch (_0x12523f) {
        case logi['maps']['LINETYPE']['STRAIGHT']:
            this.#info['latlngs'] = new Array(), this.#info['latlngs']['push']({
                'lng': _0x354449?.['fromLatLng']?.['lng'] ?? 0x0,
                'lat': _0x354449?.['fromLatLng']?.['lat'] ?? 0x0
            }), this.#info['latlngs']['push']({
                'lng': _0x354449?.['toLatLng']?.['lng'] ?? 0x0,
                'lat': _0x354449?.['toLatLng']?.['lat'] ?? 0x0
            }), this.#info['width'] = _0x354449?.['width'] ?? 0x0, this.#info['color'] = _0x354449?.['color'] ?? '#000000', this.#info['strokeWidth'] = _0x354449?.['strokeWidth'] ?? 0x0, this.#info['strokeColor'] = _0x354449?.['strokeColor'] ?? '#000000';
            break;
        case logi['maps']['LINETYPE']['POLY']:
            this.#info['latlngs'] = logi['maps']['Utils']['getRoughLatLngs'](_0x354449?.['latlngs'] ?? []), this.#info['width'] = _0x354449?.['width'] ?? 0x0, this.#info['color'] = _0x354449?.['color'] ?? '#000000', this.#info['strokeWidth'] = _0x354449?.['strokeWidth'] ?? 0x0, this.#info['strokeColor'] = _0x354449?.['strokeColor'] ?? '#000000';
            break;
        case logi['maps']['LINETYPE']['DOT']:
            this.#info['latlngs'] = logi['maps']['Utils']['getRoughLatLngs'](_0x354449?.['latlngs'] ?? []), this.#info['dotRadius'] = _0x354449?.['dotRadius'] ?? 0x0, this.#info['dotGap'] = _0x354449?.['dotGap'] ?? _0x354449?.['dotRadius'] * 0x2 ?? 0x0, this.#info['color'] = _0x354449?.['color'] ?? '#000000', this.#info['strokeWidth'] = _0x354449?.['strokeWidth'] ?? 0x0, this.#info['strokeColor'] = _0x354449?.['strokeColor'] ?? '#000000';
            break;
        case logi['maps']['LINETYPE']['DASH']:
            this.#info['latlngs'] = logi['maps']['Utils']['getRoughLatLngs'](_0x354449?.['latlngs'] ?? []), this.#info['width'] = _0x354449?.['width'] ?? 0x0, this.#info['dashLength'] = _0x354449?.['dashLength'] ?? 0x0, this.#info['dashSpace'] = _0x354449?.['dashSpace'] ?? _0x354449?.['dashLength'] ?? 0x0, this.#info['color'] = _0x354449?.['color'] ?? '#000000', this.#info['strokeWidth'] = _0x354449?.['strokeWidth'] ?? 0x0, this.#info['strokeColor'] = _0x354449?.['strokeColor'] ?? '#000000';
            break;
        default:
        }
        this['setMap'](_0x354449?.['map']);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라인 타입을 리턴한다.
   * @returns {String} 라인 타입
   * @example
   *  const lineType = line.getLineType();
   * //라인 타입이 리턴된다.
   */
    ['getLineType']() {
        return this.#type;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라인의 속성을 변경한다.
   * @param {LINETYPE} lineType 라인 타입 (STRAIGHT, POLY, DOT, DASH)
   * @param {Object} property property - line
   *  @param {Number} width 라인 넓이
   *  @param {String} color 라인 색
   *  @param {Number} strokeWidth 스트로크 넓이
   *  @param {String} strokeColor 스트로크 색
   * @param {Object} property property - polyline
   *  @param {Number} width 라인 넓이
   *  @param {String} color 라인 색
   *  @param {Number} strokeWidth 스트로크 넓이
   *  @param {String} strokeColor 스트로크 색
   * @param {Object} property property - dottedline
   *  @param {Number} dotRadius 점의 반지름
   *  @param {Number} dotGap 간격 길이
   *  @param {String} color 점 색
   *  @param {Number} strokeWidth 스트로크 넓이
   *  @param {String} strokeColor 스트로크 색
   * @param {Object} property property - dashedline
   *  @param {Number} width 라인 넓이
   *  @param {Number} dashLength 점선 길이
   *  @param {Number} dashSpace 간격 길이
   *  @param {String} color 라인 색
   *  @param {Number} strokeWidth 스트로크 넓이
   *  @param {String} strokeColor 스트로크 색
   * @example
   *  line.setLineProperty(logi.maps.LINETYPE.STRAIGHT, {
   *   width: 2,
   *   color: 'white'
   *  });
   *  //직선의 속성 정보가 변경된다.
   * @example
   *  line.setLineProperty(logi.maps.LINETYPE.POLY, {
   *   width : 5,
   *   color : '#0A32FF',
   *   strokeWidth : 1,
   *   strokeColor : 'red'
   *  });
   *  //폴리라인의 속성 정보가 변경된다.
   * @example
   *  line.setLineProperty(logi.maps.LINETYPE.DOT, {
   *   dotRadius: 4,
   *   dotGap: 10,
   *   color: 'yellow'
   *  });
   *  //점선의 속성 정보가 변경된다.
   * @example
   *  line.setLineProperty(logi.maps.LINETYPE.DASH, {
   *   width: 3,
   *   dashLength: 5,
   *   color: 'yellow'
   *  });
   *  //대쉬라인의 속성 정보가 변경된다.
   */
    ['setLineProperty'](_0x25123e, _0x2b6761) {
        this.#type != _0x25123e && (this.#type = _0x25123e, this.#info = { 'latlngs': this.#info['latlngs'] });
        let _0x48c34e = ![], _0x32d322 = null;
        switch (_0x25123e) {
        case logi['maps']['LINETYPE']['STRAIGHT']:
            _0x32d322 = _0x2b6761?.['width'] ?? 0x0;
            this.#info['width'] != _0x32d322 && (this.#info['width'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['color'] ?? '#000000';
            this.#info['color'] != _0x32d322 && (this.#info['color'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['strokeWidth'] ?? 0x0;
            this.#info['strokeWidth'] != _0x32d322 && (this.#info['strokeWidth'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['strokeColor'] ?? '#000000';
            this.#info['strokeColor'] != _0x32d322 && (this.#info['strokeColor'] = _0x32d322, _0x48c34e = !![]);
            break;
        case logi['maps']['LINETYPE']['POLY']:
            _0x32d322 = _0x2b6761?.['width'] ?? 0x0;
            this.#info['width'] != _0x32d322 && (this.#info['width'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['color'] ?? '#000000';
            this.#info['color'] != _0x32d322 && (this.#info['color'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['strokeWidth'] ?? 0x0;
            this.#info['strokeWidth'] != _0x32d322 && (this.#info['strokeWidth'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['strokeColor'] ?? '#000000';
            this.#info['strokeColor'] != _0x32d322 && (this.#info['strokeColor'] = _0x32d322, _0x48c34e = !![]);
            break;
        case logi['maps']['LINETYPE']['DOT']:
            _0x32d322 = _0x2b6761?.['dotRadius'] ?? 0x0;
            this.#info['dotRadius'] != _0x32d322 && (this.#info['dotRadius'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['dotGap'] ?? _0x2b6761?.['dotRadius'] * 0x2 ?? 0x0;
            this.#info['dotGap'] != _0x32d322 && (this.#info['dotGap'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['color'] ?? '#000000';
            this.#info['color'] != _0x32d322 && (this.#info['color'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['strokeWidth'] ?? 0x0;
            this.#info['strokeWidth'] != _0x32d322 && (this.#info['strokeWidth'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['strokeColor'] ?? '#000000';
            this.#info['strokeColor'] != _0x32d322 && (this.#info['strokeColor'] = _0x32d322, _0x48c34e = !![]);
            break;
        case logi['maps']['LINETYPE']['DASH']:
            _0x32d322 = _0x2b6761?.['width'] ?? 0x0;
            this.#info['width'] != _0x32d322 && (this.#info['width'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['dashLength'] ?? 0x0;
            this.#info['dashLength'] != _0x32d322 && (this.#info['dashLength'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['dashSpace'] ?? _0x2b6761?.['dashLength'] ?? 0x0;
            this.#info['dashSpace'] != _0x32d322 && (this.#info['dashSpace'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['color'] ?? '#000000';
            this.#info['color'] != _0x32d322 && (this.#info['color'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['strokeWidth'] ?? 0x0;
            this.#info['strokeWidth'] != _0x32d322 && (this.#info['strokeWidth'] = _0x32d322, _0x48c34e = !![]);
            _0x32d322 = _0x2b6761?.['strokeColor'] ?? '#000000';
            this.#info['strokeColor'] != _0x32d322 && (this.#info['strokeColor'] = _0x32d322, _0x48c34e = !![]);
            break;
        default:
        }
        _0x48c34e == !![] && this['setUpdateFlag']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라인의 좌표 정보를 변경한다.
   * @param {logi.maps.LatLng[]} latlngs 좌표 리스트(WGS84)
   * @example
   *  line.setLatLngs(
   *   [
   *    {lat: 37.5115557, lng: 127.0595261},
   *    {lat: 37.5062379, lng: 127.0050378},
   *    {lat: 37.566596, lng: 127.007702},
   *    {lat: 37.5251644, lng: 126.9255491}
   *   ]
   * );
   * //변경된 좌표로 라인이 그려진다.
   */
    ['setLatLngs'](_0x5068a8) {
        switch (this.#type) {
        case logi['maps']['LINETYPE']['STRAIGHT']:
            this.#info['latlngs'] = new Array(), this.#info['latlngs']['push']({
                'lng': _0x5068a8?.[0x0]?.['lng'] ?? 0x0,
                'lat': _0x5068a8?.[0x0]?.['lat'] ?? 0x0
            }), this.#info['latlngs']['push']({
                'lng': _0x5068a8?.[0x1]?.['lng'] ?? 0x0,
                'lat': _0x5068a8?.[0x1]?.['lat'] ?? 0x0
            }), this.#screenCoord = {
                'baseLayer': null,
                'tileLevel': null,
                'tileLevelOffset': null,
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            }, this['setUpdateFlag']();
            break;
        default:
            this.#info['latlngs'] = logi['maps']['Utils']['getRoughLatLngs'](_0x5068a8 ?? []), this.#screenCoord = {
                'baseLayer': null,
                'tileLevel': null,
                'tileLevelOffset': null,
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            }, this['setUpdateFlag']();
            break;
        }
    }
    ['isHit'](_0x44cec8) {
        if (!this['getLayer']())
            return ![];
        const _0x37224a = this['getMapCoord'](), _0x29bfcb = _0x37224a['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x29bfcb) == ![])
            return ![];
        if (this.#screenCoord['points']['length'] >= 0x2 && this.#screenCoord['width'] > 0x0)
            return logi['maps']['BoundaryChecker']['pointInPolyline'](_0x44cec8, this.#screenCoord['points'], this.#screenCoord['width'], this.#screenCoord['origin']);
        return ![];
    }
    ['isOverlap'](_0x1b78d4) {
        if (!this['getLayer']())
            return ![];
        const _0x567825 = this['getMapCoord'](), _0x1d973e = _0x567825['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x1d973e) == ![])
            return ![];
        if (this.#screenCoord['points']['length'] >= 0x2 && this.#screenCoord['width'] > 0x0)
            return logi['maps']['BoundaryChecker']['regionOnPolyline'](_0x1b78d4, this.#screenCoord['points'], this.#screenCoord['width'], this.#screenCoord['origin']);
        return ![];
    }
    ['drawCanvas']() {
        if (!this['getLayer']()) {
            this.#screenCoord['baseLayer'] != null && (this.#screenCoord = {
                'baseLayer': null,
                'tileLevel': null,
                'tileLevelOffset': null,
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            });
            return;
        }
        const _0x1ba64f = this['getMapCoord'](), _0x285bbb = _0x1ba64f['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x285bbb) == ![]) {
            this.#screenCoord['baseLayer'] != null && (this.#screenCoord = {
                'baseLayer': null,
                'tileLevel': null,
                'tileLevelOffset': null,
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            });
            return;
        }
        if (!this.#info) {
            this.#screenCoord['baseLayer'] != null && (this.#screenCoord = {
                'baseLayer': null,
                'tileLevel': null,
                'tileLevelOffset': null,
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            });
            return;
        }
        const _0x49f74f = this['getGfx2d'](), _0x50540d = this['getDevicePixelRatio'](), _0x2ff846 = _0x1ba64f['getTileLevelOffset']();
        if (this.#screenCoord['baseLayer'] != this['getLayer']() || this.#screenCoord['tileLevel'] != _0x1ba64f['getLevel']() || this.#screenCoord['tileLevelOffset'] != _0x2ff846) {
            this.#screenCoord['baseLayer'] = this['getLayer'](), this.#screenCoord['tileLevel'] = _0x1ba64f['getLevel'](), this.#screenCoord['tileLevelOffset'] = _0x2ff846, this.#screenCoord['width'] = 0x0, this.#screenCoord['origin'] = {
                'x': 0x0,
                'y': 0x0
            }, this.#screenCoord['points'] = new Array();
            if (this.#info['latlngs']?.['length'] >= 0x2) {
                this.#screenCoord['origin'] = _0x1ba64f['world2screen'](this.#info['latlngs'][0x0]['lng'], this.#info['latlngs'][0x0]['lat']);
                let _0x325ab2 = {
                        'x': null,
                        'y': null
                    }, _0x55c44f = {
                        'x': null,
                        'y': null
                    };
                for (let _0x523881 of this.#info['latlngs']) {
                    _0x325ab2 = _0x1ba64f['world2screen'](_0x523881['lng'], _0x523881['lat']), _0x325ab2['x'] = _0x325ab2['x'] - this.#screenCoord['origin']['x'], _0x325ab2['y'] = _0x325ab2['y'] - this.#screenCoord['origin']['y'], (_0x55c44f['x'] != _0x325ab2['x'] || _0x55c44f['y'] != _0x325ab2['y']) && (this.#screenCoord['points']['push'](_0x325ab2), _0x55c44f = _0x325ab2);
                }
            }
        }
        if (this.#screenCoord['points']['length'] >= 0x2) {
            this.#screenCoord['origin'] = _0x1ba64f['world2screen'](this.#info['latlngs'][0x0]['lng'], this.#info['latlngs'][0x0]['lat']), _0x49f74f['save'](), _0x49f74f['scale'](_0x50540d, _0x50540d), _0x49f74f['translate'](this.#screenCoord['origin']['x'], this.#screenCoord['origin']['y']);
            switch (this.#type) {
            case logi['maps']['LINETYPE']['STRAIGHT']: {
                    if (this.#screenCoord['points'][0x0]['x'] != this.#screenCoord['points'][0x1]['x'] || this.#screenCoord['points'][0x0]['y'] != this.#screenCoord['points'][0x1]['y']) {
                        const _0x245590 = this.#info['width'];
                        this.#screenCoord['width'] = _0x245590;
                        if (this.#info['strokeWidth'] > 0x0) {
                            const _0x1987ed = _0x245590 + this.#info['strokeWidth'] * 0x2;
                            this.#screenCoord['width'] = _0x1987ed, _0x49f74f['drawObjLine'](this.#screenCoord['points'][0x0], this.#screenCoord['points'][0x1], _0x1987ed, this.#info['strokeColor']);
                        }
                        _0x49f74f['drawObjLine'](this.#screenCoord['points'][0x0], this.#screenCoord['points'][0x1], _0x245590, this.#info['color']);
                    }
                }
                break;
            case logi['maps']['LINETYPE']['POLY']: {
                    const _0x591981 = this.#info['width'];
                    this.#screenCoord['width'] = _0x591981;
                    if (this.#info['strokeWidth'] > 0x0) {
                        const _0x5a4b81 = _0x591981 + this.#info['strokeWidth'] * 0x2;
                        this.#screenCoord['width'] = _0x5a4b81, _0x49f74f['drawObjPolyLine'](this.#screenCoord['points'], _0x5a4b81, this.#info['strokeColor'], this.#info['offsetX'], this.#info['offsetY']);
                    }
                    _0x49f74f['drawObjPolyLine'](this.#screenCoord['points'], _0x591981, this.#info['color'], this.#info['offsetX'], this.#info['offsetY']);
                }
                break;
            case logi['maps']['LINETYPE']['DOT']: {
                    const _0x1672f4 = this.#info['dotRadius'];
                    let _0x3c4e4f = this.#info['dotGap'] + _0x1672f4 * 0x2;
                    this.#screenCoord['width'] = _0x1672f4 * 0x2;
                    if (this.#info['strokeWidth'] > 0x0) {
                        const _0x2b2401 = _0x1672f4 + this.#info['strokeWidth'];
                        _0x3c4e4f = this.#info['dotGap'] + _0x2b2401 * 0x2, this.#screenCoord['width'] = _0x2b2401 * 0x2, _0x49f74f['drawObjDottedLine'](this.#screenCoord['points'], _0x2b2401, _0x3c4e4f, this.#info['strokeColor']);
                    }
                    _0x49f74f['drawObjDottedLine'](this.#screenCoord['points'], _0x1672f4, _0x3c4e4f, this.#info['color']);
                }
                break;
            case logi['maps']['LINETYPE']['DASH']: {
                    const _0x26459b = this.#info['width'];
                    let _0x2364fa = this.#info['dashSpace'] + _0x26459b;
                    this.#screenCoord['width'] = _0x26459b;
                    if (this.#info['strokeWidth'] > 0x0) {
                        const _0x3168fd = _0x26459b + this.#info['strokeWidth'] * 0x2;
                        _0x2364fa = this.#info['dashSpace'] + _0x3168fd, this.#screenCoord['width'] = _0x3168fd, _0x49f74f['drawObjPolyDashedLine'](this.#screenCoord['points'], _0x3168fd, [
                            this.#info['dashLength'],
                            _0x2364fa
                        ], this.#info['strokeColor'], this.#info['offsetX'], this.#info['offsetY']);
                    }
                    _0x49f74f['drawObjPolyDashedLine'](this.#screenCoord['points'], _0x26459b, [
                        this.#info['dashLength'],
                        _0x2364fa
                    ], this.#info['color'], this.#info['offsetX'], this.#info['offsetY']);
                }
                break;
            }
            _0x49f74f['restore']();
        }
    }
    static ['getDashPaths'](_0x1a680b, _0x5bc875, _0x2a237d) {
        const _0x116932 = new Array();
        let _0x32a37d, _0x20abac, _0x538388;
        for (const _0x58b10a of _0x1a680b) {
            const _0x135736 = new Array(), _0xc4e2dd = {
                    'x': _0x58b10a[0x0],
                    'y': _0x58b10a[0x1]
                }, _0x59cbeb = {
                    'x': _0x58b10a[0x0],
                    'y': _0x58b10a[0x1]
                };
            _0x32a37d = !![], _0x20abac = _0x5bc875;
            for (let _0x24d00e = 0x2; _0x24d00e < _0x58b10a['length']; _0x24d00e += 0x2) {
                _0x59cbeb['x'] = _0x58b10a[_0x24d00e + 0x0], _0x59cbeb['y'] = _0x58b10a[_0x24d00e + 0x1], _0x538388 = Math['sqrt']((_0x59cbeb['x'] - _0xc4e2dd['x']) ** 0x2 + (_0x59cbeb['y'] - _0xc4e2dd['y']) ** 0x2);
                while (_0x538388 > _0x20abac) {
                    if (_0x32a37d) {
                        const _0x1a813b = {
                            'pt0': {
                                'x': 0x0,
                                'y': 0x0
                            },
                            'pt1': {
                                'x': 0x0,
                                'y': 0x0
                            }
                        };
                        _0x1a813b['pt0']['x'] = _0xc4e2dd['x'], _0x1a813b['pt0']['y'] = _0xc4e2dd['y'], _0x1a813b['pt1']['x'] = _0xc4e2dd['x'] + (_0x59cbeb['x'] - _0xc4e2dd['x']) / _0x538388 * _0x20abac, _0x1a813b['pt1']['y'] = _0xc4e2dd['y'] + (_0x59cbeb['y'] - _0xc4e2dd['y']) / _0x538388 * _0x20abac, _0xc4e2dd['x'] = _0x1a813b['pt1']['x'], _0xc4e2dd['y'] = _0x1a813b['pt1']['y'], _0x135736['push'](_0x1a813b);
                    } else
                        _0xc4e2dd['x'] = _0xc4e2dd['x'] + (_0x59cbeb['x'] - _0xc4e2dd['x']) / _0x538388 * _0x20abac, _0xc4e2dd['y'] = _0xc4e2dd['y'] + (_0x59cbeb['y'] - _0xc4e2dd['y']) / _0x538388 * _0x20abac;
                    _0x538388 -= _0x20abac, _0x32a37d = !_0x32a37d, _0x20abac = _0x32a37d ? _0x5bc875 : _0x2a237d;
                }
                if (_0x538388 <= _0x20abac) {
                    if (_0x32a37d) {
                        const _0x211f03 = {
                            'pt0': {
                                'x': 0x0,
                                'y': 0x0
                            },
                            'pt1': {
                                'x': 0x0,
                                'y': 0x0
                            }
                        };
                        _0x211f03['pt0']['x'] = _0xc4e2dd['x'], _0x211f03['pt0']['y'] = _0xc4e2dd['y'], _0x211f03['pt1']['x'] = _0x59cbeb['x'], _0x211f03['pt1']['y'] = _0x59cbeb['y'], _0x135736['push'](_0x211f03);
                    }
                    _0x538388 = 0x0, _0x20abac -= _0x538388;
                }
                _0xc4e2dd['x'] = _0x59cbeb['x'], _0xc4e2dd['y'] = _0x59cbeb['y'];
            }
            _0x116932['push'](_0x135736);
        }
        return _0x116932;
    }
};
export default logi['maps']['Line'];