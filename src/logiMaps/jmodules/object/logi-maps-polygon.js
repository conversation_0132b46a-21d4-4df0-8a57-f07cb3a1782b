import a8_0x355c58 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a8_0x511f8a from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a8_0x4017cd from '../utility/logi-maps-boundarychecker.js?v=2.1.10.1';
import a8_0x1d70d9 from '../object/logi-maps-object.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Utils'] = a8_0x355c58, logi['maps']['BoundaryData'] = a8_0x511f8a, logi['maps']['BoundaryChecker'] = a8_0x4017cd, logi['maps']['Object'] = a8_0x1d70d9, logi['maps']['Polygon'] = class extends logi['maps']['Object'] {
    #latlngs = [];
    #fillColor;
    #lineWidth;
    #lineColor;
    #screenCoord = {
        'baseLayer': null,
        'tileLevel': null,
        'tileLevelOffset': null,
        'origin': {
            'x': 0x0,
            'y': 0x0
        },
        'points': new Array()
    };
    /**
   * @preserve .
   * @constructor
   * @description
   *  폴리곤을 생성한다.
   * @param {logi.maps.LatLng[]} latlngs 좌표 리스트(WGS84)
   * @param {String} fillColor 채우기 색상
   * @param {Object} options option
   *  @param {String} options.key polygon key (default: random 생성)
   *  @param {String} options.class polygon class (CSS의 class와 비슷함)
   *  @param {Number} options.zIndex 그리기 순서 (default: 0)
   *  @param {Number} options.lineWidth 선 두께
   *  @param {String} options.lineColor 선 색상
   *  @param {logi.maps.Map} options.map 표시될 Map
   * @example
   *  let polygon = new logi.maps.Polygon(
   *   [
   *    {lat: 37.5115557, lng: 127.0595261},
   *    {lat: 37.5062379, lng: 127.0050378},
   *    {lat: 37.5665960, lng: 127.0077020},
   *    {lat: 37.5115557, lng: 127.0595261}
   *   ], 'blue', {
   *   lineWidth: 5,
   *   lineColor: '#0a32ff',
   *   map: logiMap
   *  });
   *  //선 두께가 5인 파랑색 폴리곤을 그린다.
   */
    constructor(_0x547acb, _0x2a1224, _0x3c1d6d) {
        const _0x3a86e2 = _0x3c1d6d?.['key'] ?? 'pg_' + Math['random']()['toString'](0x24)['slice'](-0x8), _0x22438f = _0x3c1d6d?.['class'] ?? '', _0xde0b2b = _0x3c1d6d?.['zIndex'] ?? 0x0;
        super(_0x3a86e2, logi['maps']['Object']['OBJTYPE']['polygon'], _0x22438f, _0xde0b2b), this.#latlngs = logi['maps']['Utils']['getRoughLatLngs'](_0x547acb ?? []), this.#fillColor = _0x2a1224 ?? '#000000', this.#lineWidth = _0x3c1d6d?.['lineWidth'] ?? 0x0, this.#lineColor = _0x3c1d6d?.['lineColor'] ?? '#000000', this['setMap'](_0x3c1d6d?.['map']);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  폴리곤의 색상을 변경한다.
   * @param {String} fillColor 색상
   * @example
   *  polygon.setFillColor(‘#FFFFFF’);
   *  //폴리곤의 색상이 하얀색으로 변경된다.
   */
    ['setFillColor'](_0x3d5c27) {
        _0x3d5c27 = _0x3d5c27 ?? '#000000', this.#fillColor != _0x3d5c27 && (this.#fillColor = _0x3d5c27, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  폴리곤 외곽 라인의 속성을 변경한다.
   * @param {Number} linewidth 외곽 라인 넓이
   * @param {String} lineColor 외곽 라인 색
   * @example
   *  polygon.setLineProperty(1, ‘#FFFFFF’);
   *  //폴리곤 외곽 라인이 하얀색으로 그려진다.
   */
    ['setLineProperty'](_0x18e427, _0x5518f6) {
        _0x18e427 = _0x18e427 ?? 0x0, _0x5518f6 = _0x5518f6 ?? '#000000', (this.#lineWidth != _0x18e427 || this.#lineColor != _0x5518f6) && (this.#lineWidth = _0x18e427, this.#lineColor = _0x5518f6, this['setUpdateFlag']());
    }
    ['isHit'](_0x42cf64) {
        if (!this['getLayer']())
            return ![];
        const _0x1f25e0 = this['getMapCoord'](), _0x4440e9 = _0x1f25e0['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x4440e9) == ![])
            return ![];
        return logi['maps']['BoundaryChecker']['pointInPolygon'](_0x42cf64, this.#screenCoord['points'], this.#screenCoord['origin']);
    }
    ['isOverlap'](_0x417c49) {
        if (!this['getLayer']())
            return ![];
        const _0x2b37a8 = this['getMapCoord'](), _0x27945e = _0x2b37a8['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x27945e) == ![])
            return ![];
        return logi['maps']['BoundaryChecker']['regionOnPolygon'](_0x417c49, this.#screenCoord['points'], this.#screenCoord['origin']);
    }
    ['drawCanvas']() {
        if (!this['getLayer']()) {
            this.#screenCoord['baseLayer'] != null && (this.#screenCoord = {
                'baseLayer': null,
                'tileLevel': null,
                'tileLevelOffset': null,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            });
            return;
        }
        const _0x4c39b0 = this['getMapCoord'](), _0x1151b8 = _0x4c39b0['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x1151b8) == ![]) {
            this.#screenCoord['baseLayer'] != null && (this.#screenCoord = {
                'baseLayer': null,
                'tileLevel': null,
                'tileLevelOffset': null,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            });
            return;
        }
        const _0x51e674 = this['getGfx2d'](), _0x49cdb6 = this['getDevicePixelRatio'](), _0x5dbe62 = _0x4c39b0['getTileLevelOffset']();
        if (this.#screenCoord['baseLayer'] != this['getLayer']() || this.#screenCoord['tileLevel'] != _0x4c39b0['getLevel']() || this.#screenCoord['tileLevelOffset'] != _0x5dbe62) {
            this.#screenCoord['baseLayer'] = this['getLayer'](), this.#screenCoord['tileLevel'] = _0x4c39b0['getLevel'](), this.#screenCoord['tileLevelOffset'] = _0x5dbe62, this.#screenCoord['origin'] = {
                'x': 0x0,
                'y': 0x0
            }, this.#screenCoord['points'] = new Array();
            if (this.#latlngs['length'] >= 0x3) {
                this.#screenCoord['origin'] = _0x4c39b0['world2screen'](this.#latlngs[0x0]['lng'], this.#latlngs[0x0]['lat']);
                let _0x9a3df2 = {
                        'x': null,
                        'y': null
                    }, _0x25454b = {
                        'x': null,
                        'y': null
                    };
                for (let _0x27b8db of this.#latlngs) {
                    _0x9a3df2 = _0x4c39b0['world2screen'](_0x27b8db['lng'], _0x27b8db['lat']), _0x9a3df2['x'] = _0x9a3df2['x'] - this.#screenCoord['origin']['x'], _0x9a3df2['y'] = _0x9a3df2['y'] - this.#screenCoord['origin']['y'], (_0x25454b['x'] != _0x9a3df2['x'] || _0x25454b['y'] != _0x9a3df2['y']) && (this.#screenCoord['points']['push'](_0x9a3df2), _0x25454b = _0x9a3df2);
                }
                if (this.#screenCoord['points']['length'] >= 0x3) {
                    const _0x4794d3 = this.#screenCoord['points'][0x0], _0x18d922 = this.#screenCoord['points'][this.#screenCoord['points']['length'] - 0x1];
                    (_0x4794d3['x'] != _0x18d922['x'] || _0x4794d3['y'] != _0x18d922['y']) && this.#screenCoord['points']['push']({
                        'x': _0x4794d3['x'],
                        'y': _0x4794d3['y']
                    });
                }
            }
        }
        this.#screenCoord['points']['length'] >= 0x4 && (this.#screenCoord['origin'] = _0x4c39b0['world2screen'](this.#latlngs[0x0]['lng'], this.#latlngs[0x0]['lat']), _0x51e674['save'](), _0x51e674['scale'](_0x49cdb6, _0x49cdb6), _0x51e674['translate'](this.#screenCoord['origin']['x'], this.#screenCoord['origin']['y']), _0x51e674['drawObjPolygon'](this.#screenCoord['points'], this.#fillColor, this.#lineWidth, this.#lineColor), _0x51e674['restore']());
    }
};
export default logi['maps']['Polygon'];