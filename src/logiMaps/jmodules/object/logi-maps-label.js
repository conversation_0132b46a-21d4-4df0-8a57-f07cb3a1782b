import a4_0x15763d from '../common/logi-maps-defines.js?v=2.1.10.1';
import a4_0x192693 from '../common/logi-maps-types.js?v=2.1.10.1';
import a4_0xe8cff8 from '../resource/logi-maps-resource.js?v=2.1.10.1';
import a4_0x50b0e2 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a4_0x444295 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a4_0x190c5b from '../utility/logi-maps-boundarychecker.js?v=2.1.10.1';
import a4_0x2a355d from '../object/logi-maps-object.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a4_0x15763d, logi['maps']['Point'] = a4_0x192693['Point'], logi['maps']['TileInfo'] = a4_0x192693['TileInfo'], logi['maps']['ALIGN'] = a4_0x192693['ALIGN'], logi['maps']['Resource'] = a4_0xe8cff8, logi['maps']['Utils'] = a4_0x50b0e2, logi['maps']['BoundaryData'] = a4_0x444295, logi['maps']['BoundaryChecker'] = a4_0x190c5b, logi['maps']['Object'] = a4_0x2a355d, logi['maps']['Label'] = class extends logi['maps']['Object'] {
    #position = {
        'lng': 0x0,
        'lat': 0x0
    };
    #offsetX;
    #offsetY;
    #angle;
    #align;
    #text;
    #textItems;
    #fontFamily;
    #textBold;
    #textColor;
    #fontSize;
    #bgBox = null;
    #bgImgSrc;
    #bgImgOffsetX;
    #bgImgOffsetY;
    #bgImage = null;
    #tileInfo = new logi['maps']['TileInfo']();
    #expiredTileId = !![];
    #overlapInfo = {
        'screenPos': new logi['maps']['Point'](),
        'visibility': ![],
        'bgRadius': 0xa,
        'bgColor': '#BBBBBB88',
        'fontSize': 0xc,
        'textColor': '#000000DD'
    };
    #boundaryData = new logi['maps']['BoundaryData']();
    #boundaryPadding = {
        'left': 0x0,
        'top': 0x0,
        'right': 0x0,
        'bottom': 0x0
    };
    /**
   * @preserve .
   * @constructor
   * @description
   *  라벨을 생성한다.
   * @param {String} text 글자
   * @param {logi.maps.LatLng} position 라벨 위치
   * @param {Object} options option
   *  @param {String} options.key label key (default: random 생성)
   *  @param {String} options.class label class (CSS의 class와 비슷함)
   *  @param {Number} options.zIndex 그리기 순서 (default: 0)
   *  @param {String} options.fontFamily 폰트 명
   *  @param {Boolean} options.textBold 볼드
   *  @param {String} options.color 색상
   *  @param {Number} options.fontSize 크기
   *  @param {Number} options.groupId 같은 그룹끼리 바운더리가 겹치면 하나가 제거된다. (default: 0은 제외)
   *  @param {Number} options.offsetX offset x
   *  @param {Number} options.offsetY offset y
   *  @param {Number} options.angle 라벨 각도
   *  @param {ALIGN} options.align 라벨 정렬
   *  @param {Object} options.bgBox 배경 박스 설정
   *   @param {number} options.bgBox.width - 배경 박스의 너비 (0 이면 라벨 사이즈에 맞춤)
   *   @param {number} options.bgBox.height - 배경 박스의 높이 (0 이면 라벨 사이즈에 맞춤)
   *   @param {number} options.bgBox.radius - 배경 박스의 라운드
   *   @param {string} options.bgBox.fillColor - 배경 박스의 배경 색상
   *   @param {string} options.bgBox.lineColor - 배경 박스의 테두리 색상
   *   @param {number} options.bgBox.lineWidth - 배경 박스의 테두리 두께
   *   @param {number} options.bgBox.offsetX - 배경 박스의 X 오프셋
   *   @param {number} options.bgBox.offsetY - 배경 박스의 Y 오프셋
   *   @param {Object} options.bgBox.padding - 배경 박스의 패딩 정보
   *    @param {number} options.bgBox.padding.left - 배경 박스의 패딩 left
   *    @param {number} options.bgBox.padding.top - 배경 박스의 패딩 top
   *    @param {number} options.bgBox.padding.right - 배경 박스의 패딩 right
   *    @param {number} options.bgBox.padding.bottom - 배경 박스의 패딩 bottom
   *  @param {String} options.bgImgSrc 배경 이미지 주소
   *  @param {Number} options.bgImgOffsetX 배경 이미지 offset x
   *  @param {Number} options.bgImgOffsetY 배경 이미지 offset y
   *  @param {logi.maps.Map} options.map 표시될 Map
   * @example
   *  let label = new logi.maps.Label('test-text', { lat: 37.566596, lng: 127.007702 }, {map: logiMap});
   *  //동대문시장 위에 ‘test-text’ 글자가 표시된다.
   */
    constructor(_0x16f120, _0x217242, _0x1bee0a) {
        const _0x38e0b9 = _0x1bee0a?.['key'] ?? 'lb_' + Math['random']()['toString'](0x24)['slice'](-0x8), _0x511615 = _0x1bee0a?.['class'] ?? '', _0x46a749 = _0x1bee0a?.['zIndex'] ?? 0x0;
        super(_0x38e0b9, logi['maps']['Object']['OBJTYPE']['label'], _0x511615, _0x46a749), this.#position['lng'] = _0x217242?.['lng'] ?? 0x0, this.#position['lat'] = _0x217242?.['lat'] ?? 0x0, this.#offsetX = _0x1bee0a?.['offsetX'] ?? 0x0, this.#offsetY = _0x1bee0a?.['offsetY'] ?? 0x0, this.#angle = _0x1bee0a?.['angle'] ?? 0x0, this.#align = _0x1bee0a?.['align'] ?? logi['maps']['ALIGN']['LT'], this.#text = _0x16f120 ?? '', this.#textItems = this.#text['toString']()['split']('\x0a'), this.#fontFamily = _0x1bee0a?.['fontFamily'] ?? '', this.#textBold = _0x1bee0a?.['textBold'] ?? ![], this.#textColor = _0x1bee0a?.['color'] ?? '#000000', this.#fontSize = _0x1bee0a?.['fontSize'] ?? 0x12, this['setBgImgSrc'](_0x1bee0a?.['bgImgSrc'] ?? ''), this.#bgImgOffsetX = _0x1bee0a?.['bgImgOffsetX'] ?? 0x0, this.#bgImgOffsetY = _0x1bee0a?.['bgImgOffsetY'] ?? 0x0, this.#bgBox = _0x1bee0a?.['bgBox'], this.#boundaryData['groupId'] = _0x1bee0a?.['groupId'] ?? 0x0, this['resetBoundary'](), this['setMap'](_0x1bee0a?.['map']);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 위치를 지정한다.
   * @param {logi.maps.LatLng} latlng 위치 좌표
   * @example
   *  label.setPosition({lat: 37.566596, lng: 127.007702});
   *  //동대문시장 위에 라벨이 그려진다.
   */
    ['setPosition'](_0xcd97a1) {
        _0xcd97a1['lng'] = _0xcd97a1['lng'] ?? 0x0, _0xcd97a1['lat'] = _0xcd97a1['lat'] ?? 0x0, (this.#position['lng'] !== _0xcd97a1['lng'] || this.#position['lat'] !== _0xcd97a1['lat']) && (this.#position['lng'] = _0xcd97a1['lng'], this.#position['lat'] = _0xcd97a1['lat'], (this.#position['lng'] < this['tileInfo']['boundary']['xMin'] || this.#position['lng'] > this['tileInfo']['boundary']['xMax'] || this.#position['lat'] < this['tileInfo']['boundary']['yMin'] || this.#position['lat'] > this['tileInfo']['boundary']['yMax']) && (this['expiredTileId'] = !![]), this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 위치를 전달한다.
   * @returns {logi.maps.LatLng} 위치 좌표
   * @example
   *  let position = label.getPosition();
   *  //라벨의 위치를 전달한다.
   */
    ['getPosition']() {
        return {
            'lng': this.#position['lng'],
            'lat': this.#position['lat']
        };
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨 위치의 offset을 설정한다.
   * @param {Number} offsetX offset X
   * @example
   *  label.setOffsetX(100);
   *  //라벨이 실제 위치에서 화면 x좌표 100만큼 떨어진 곳에 그려진다.
   */
    ['setOffsetX'](_0x2bd662) {
        _0x2bd662 = _0x2bd662 ?? 0x0, this.#offsetX !== _0x2bd662 && (this.#offsetX = _0x2bd662, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 offset을 전달한다.
   * @returns {Number} offset X
   * @example
   *  let offset = label.getOffsetX();
   *  //라벨의 offset 값이 전달된다.
   */
    ['getOffsetX']() {
        return this.#offsetX;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨 위치의 offset을 설정한다.
   * @param {Number} offsetY offset Y
   * @example
   *  label.setOffsetY(100);
   *  //라벨이 실제 위치에서 화면 y좌표 100만큼 떨어진 곳에 그려진다.
   */
    ['setOffsetY'](_0x198d77) {
        _0x198d77 = _0x198d77 ?? 0x0, this.#offsetY !== _0x198d77 && (this.#offsetY = _0x198d77, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨 위치의 offset을 설정한다.
   * @param {Number} offsetY offset Y
   * @example
   *  label.setOffsetY(100);
   *  //라벨이 실제 위치에서 화면 y좌표 100만큼 떨어진 곳에 그려진다.
   */
    ['getOffsetY']() {
        return this.#offsetY;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 각도를 설정한다.
   * @param {Number} angle 각도
   * @example
   *  label.setAngle(45);
   *  //라벨이 45도 회전된다.
   */
    ['setAngle'](_0x18ba53) {
        _0x18ba53 = _0x18ba53 ?? 0x0, this.#angle !== _0x18ba53 && (this.#angle = _0x18ba53, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 각도를 전달한다.
   * @returns {Number} 각도
   * @example
   *  let angle = label.getAngle();
   *  //라벨의 각도가 전달된다.
   */
    ['getAngle']() {
        return this.#angle;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 정렬 값을 설정한다.
   * @param {ALIGN} align 정렬
   * @example
   *  label.setAlign(logi.maps.LT);
   *  //라벨이 좌상단을 기준으로 정렬된다.
   */
    ['setAlign'](_0x5bb942) {
        _0x5bb942 = _0x5bb942 ?? logi['maps']['ALIGN']['LT'], this.#align !== _0x5bb942 && (this.#align = _0x5bb942, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 정렬을 전달한다.
   * @returns {ALIGN} 정렬
   * @example
   *  let align = label.getAlign();
   *  //라벨의 정렬 값이 전달된다.
   */
    ['getAlign']() {
        return this.#align;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 글자를 설정한다.
   * @param {String} text 글자
   * @example
   *  label.setText(‘label-text’);
   *  //라벨의 글자가 ‘label-text’로 그려진다.
   */
    ['setText'](_0xb7929f) {
        _0xb7929f = _0xb7929f ?? '', this.#text !== _0xb7929f && (this.#text = _0xb7929f, this.#textItems = this.#text['toString']()['split']('\x0a'), this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 글자를 전달한다.
   * @returns {String} 글자
   * @example
   *  let text = label.getText();
   *  //라벨의 글자가 전달된다.
   */
    ['getText']() {
        return this.#text;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 글자 색상을 설정한다.
   * @param {String} textColor 글자색
   * @example
   *  label.setTextColor(‘blue’);
   *  //라벨의 글자가 blue 색상으로 그려진다.
   */
    ['setTextColor'](_0x158765) {
        _0x158765 = _0x158765 ?? 'FFFFFF', this.#textColor !== _0x158765 && (this.#textColor = _0x158765, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 글자 색상을 전달한다.
   * @returns {String} 글자색
   * @example
   *  let textColor = label.getTextColor();
   *  //라벨의 글자 색상이 전달된다.
   */
    ['getTextColor']() {
        return this.#textColor;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 글자 크기를 설정한다.
   * @param {Number} fontSize 폰트 사이즈
   * @example
   *  label.setFontSize(21);
   *  //라벨의 글자가 21 사이즈로 그려진다.
   */
    ['setFontSize'](_0x14d5a6) {
        _0x14d5a6 = _0x14d5a6 ?? 0x12, this.#fontSize !== _0x14d5a6 && (this.#fontSize = _0x14d5a6, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 글자 크기를 전달한다.
   * @returns {Number} 폰트 사이즈
   * @example
   *  let fontSize = label.getFontSize();
   *  //라벨의 글자 크기가 전달된다.
   */
    ['getFontSize']() {
        return this.#fontSize;
    }
    /**
   * @preserve .
   * @method
   * @description
   * 라벨의 배경 박스를 설정한다.
   * @param {Object} bgBox 배경 박스 설정
   *  @param {number} bgBox.width - 배경 박스의 너비 (0 이면 라벨 사이즈에 맞춤)
   *  @param {number} bgBox.height - 배경 박스의 높이 (0 이면 라벨 사이즈에 맞춤)
   *  @param {number} bgBox.radius - 배경 박스의 라운드
   *  @param {string} bgBox.fillColor - 배경 박스의 배경 색상
   *  @param {string} bgBox.lineColor - 배경 박스의 테두리 색상
   *  @param {number} bgBox.lineWidth - 배경 박스의 테두리 두께
   *  @param {number} bgBox.offsetX - 배경 박스의 X 오프셋
   *  @param {number} bgBox.offsetY - 배경 박스의 Y 오프셋
   *  @param {Object} bgBox.padding - 배경 박스의 패딩 정보
   *   @param {number} bgBox.padding.left - 배경 박스의 패딩 left
   *   @param {number} bgBox.padding.top - 배경 박스의 패딩 top
   *   @param {number} bgBox.padding.right - 배경 박스의 패딩 right
   *   @param {number} bgBox.padding.bottom - 배경 박스의 패딩 bottom
   * @example
   * label.setBgBox({
   *  width: 200,
   *  height: 100,
   *  radius: 10,
   *  fillColor: '#FFFFFF',
   *  lineColor: '#000000',
   *  lineWidth: 1,
   *  offsetX: 0,
   *  offsetY: 0,
   *  padding: {
   *   left: 10,
   *   top: 10,
   *   right: 10,
   *   bottom: 10,
   * }});
   * //라벨의 배경 박스가 설정된다.
   * @example
   * label.setBgBox({
   *  width: 0,
   *  height: 0,
   * });
   * //라벨 사이즈에 맞게 배경 박스가 설정된다.
   */
    ['setBgBox'](_0x2ecda1) {
        _0x2ecda1 ? (this.#bgBox = {
            'width': _0x2ecda1['width'] ?? 0x0,
            'height': _0x2ecda1['height'] ?? 0x0,
            'radius': _0x2ecda1['radius'] ?? 0x0,
            'fillColor': _0x2ecda1['fillColor'] ?? '#FFFFFF',
            'lineColor': _0x2ecda1['lineColor'] ?? '#000000',
            'lineWidth': _0x2ecda1['lineWidth'] ?? 0x1,
            'offsetX': _0x2ecda1['offsetX'] ?? 0x0,
            'offsetY': _0x2ecda1['offsetY'] ?? 0x0
        }, this['setUpdateFlag']()) : (this.#bgBox = null, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 배경 이미지를 지정한다.
   * @param {String} src 배경 이미지 주소
   * @example
   *  label.setBgImgSrc(‘./bg.png’);
   *  //‘./bg.png’ 이미지가 라벨의 배경이미지로 사용된다.
   */
    async ['setBgImgSrc'](_0x2c3010) {
        this.#bgImgSrc = _0x2c3010 ?? '';
        if (this.#bgImgSrc == '')
            this.#bgImage = null, this['setUpdateFlag']();
        else
            try {
                const _0xe4ff46 = await logi['maps']['Resource']['getImage'](this.#bgImgSrc);
                this.#bgImage = _0xe4ff46, this['setUpdateFlag']();
            } catch (_0x4b0529) {
                this.#bgImage = null, this['setUpdateFlag'](), _0x4b0529 && console['log'](_0x4b0529);
            }
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 배경 이미지 주소를 전달한다.
   * @returns {String} 이미지 주소
   * @example
   *  let bgImgSrc = label.getBgImgSrc();
   *  //라벨의 배경 이미지 주소가 전달된다.
   */
    ['getBgImgSrc']() {
        return this.#bgImgSrc;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 배경 이미지 위치 옵셋을 지정한다.
   * @param {Number} bgImgOffsetX 배경 이미지 offset X
   * @example
   *  label.setBgImgOffsetX(50);
   *  //배경 이미지의 실제 위치에서 화면 x좌표 50만큼 떨어진 곳에 그려진다.
   */
    ['setBgImgOffsetX'](_0x4030f0) {
        _0x4030f0 = _0x4030f0 ?? 0x0, this.#bgImgOffsetX !== _0x4030f0 && (this.#bgImgOffsetX = _0x4030f0, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 배경 이미지 위치 옵셋을 전달한다.
   * @returns {Number} 배경 이미지 offset X
   * @example
   *  let bgImgOffsetX = label.getBgImgOffsetX();
   *  //라벨의 배경 이미지 위치 옵셋이 전달된다.
   */
    ['getBgImgOffsetX']() {
        return this.#bgImgOffsetX;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 배경 이미지 위치 옵셋을 지정한다.
   * @param {Number} bgImgOffsetY 배경 이미지 offset Y
   * @example
   *  label.setBgImgOffsetY(50);
   *  //배경 이미지의 실제 위치에서 화면 y좌표 50만큼 떨어진 곳에 그려진다.
   */
    ['setBgImgOffsetY'](_0x464b61) {
        _0x464b61 = _0x464b61 ?? 0x0, this.#bgImgOffsetY !== _0x464b61 && (this.#bgImgOffsetY = _0x464b61, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 배경 이미지 위치 옵셋을 전달한다.
   * @returns {Number} 배경 이미지 offset Y
   * @example
   *  let bgImgOffsetY = label.getBgImgOffsetY();
   *  //라벨의 배경 이미지 위치 옵셋이 전달된다.
   */
    ['getBgImgOffsetY']() {
        return this.#bgImgOffsetY;
    }
    get ['tileInfo']() {
        return this.#tileInfo;
    }
    set ['expiredTileId'](_0x48f0db) {
        this.#expiredTileId = _0x48f0db;
    }
    get ['expiredTileId']() {
        return this.#expiredTileId;
    }
    get ['overlapInfo']() {
        return this.#overlapInfo;
    }
    get ['boundaryData']() {
        return this.#boundaryData;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  라벨의 바운더리를 조정할 수 있다. padding 정보를 고려하여 overlap을 체크한다.
   * @param {Number} left padding left
   * @param {Number} top padding top
   * @param {Number} right padding right
   * @param {Number} bottom padding bottom
   * @example
   *  image.setBoundaryPadding(10, 10, 10, 10);
   *  //실제 바운더리보다 상하좌우 10정도 작게 체크한다.
   */
    ['setBoundaryPadding'](_0x32b335, _0x24cc02, _0x110063, _0x171704) {
        _0x32b335 = _0x32b335 ?? 0x0, _0x24cc02 = _0x24cc02 ?? 0x0, _0x110063 = _0x110063 ?? 0x0, _0x171704 = _0x171704 ?? 0x0, (this.#boundaryPadding['left'] != _0x32b335 || this.#boundaryPadding['top'] != _0x24cc02 || this.#boundaryPadding['right'] != _0x110063 || this.#boundaryPadding['bottom'] != _0x171704) && (this.#boundaryPadding['left'] = _0x32b335, this.#boundaryPadding['top'] = _0x24cc02, this.#boundaryPadding['right'] = _0x110063, this.#boundaryPadding['bottom'] = _0x171704, this['setUpdateFlag']());
    }
    ['resetBoundary']() {
        this.#overlapInfo['screenPos']['x'] = 0x0, this.#overlapInfo['screenPos']['y'] = 0x0, this.#boundaryData['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], this.#boundaryData['tagName'] = this['getKey'](), this.#boundaryData['boundaryCircle'] = {
            'center': {
                'x': 0x0,
                'y': 0x0
            },
            'maxR': 0x0
        }, this.#boundaryData['boundaryRect'] = [
            {
                'x': 0x0,
                'y': 0x0
            },
            {
                'x': 0x0,
                'y': 0x0
            },
            {
                'x': 0x0,
                'y': 0x0
            },
            {
                'x': 0x0,
                'y': 0x0
            }
        ], this.#boundaryData['creationTick'] = logi['maps']['Utils']['getCurTick'](), this.#boundaryData['overlapCnt'] = 0x0;
    }
    ['updateBoundary']() {
        let _0x4ad468 = [
                {
                    'x': 0x0,
                    'y': 0x0
                },
                {
                    'x': 0x0,
                    'y': 0x0
                },
                {
                    'x': 0x0,
                    'y': 0x0
                },
                {
                    'x': 0x0,
                    'y': 0x0
                }
            ], _0xc4307d = {
                'center': {
                    'x': 0x0,
                    'y': 0x0
                },
                'maxR': 0x0
            }, _0x36be2d = this['getMapCoord'](), _0x18f8b4 = this['getGfx2d'](), _0x3871ed = _0x36be2d['world2screen'](this.#position['lng'], this.#position['lat']);
        const _0x103f50 = this.#fontSize * 1.2;
        let _0x3b38b9 = {
                'x': 0x0,
                'y': 0x0
            }, _0x233823 = {
                'w': 0x0,
                'h': 0x0
            };
        _0x3b38b9['x'] = _0x3871ed['x'] + this.#offsetX, _0x3b38b9['y'] = _0x3871ed['y'] + this.#offsetY, this.#textItems['forEach'](_0x59a8e5 => {
            const _0x3df80c = _0x18f8b4['getTextSize'](_0x59a8e5, this.#fontFamily, this.#fontSize);
            _0x3df80c['width'] > _0x233823['w'] && (_0x233823['w'] = _0x3df80c['width']), _0x233823['h'] += _0x103f50;
        }), _0x4ad468 = logi['maps']['Utils']['getBoundaryBox'](_0x3b38b9, _0x233823['w'], _0x233823['h'], this.#align, this.#angle, this.#boundaryPadding), _0xc4307d = logi['maps']['Utils']['getBoundaryCircle'](_0x4ad468), this.#overlapInfo['screenPos']['x'] = Math['max'](_0x4ad468[0x0]['x'], _0x4ad468[0x2]['x']), this.#overlapInfo['screenPos']['y'] = Math['min'](_0x4ad468[0x0]['y'], _0x4ad468[0x2]['y']), this.#boundaryData['setBoundary'](_0xc4307d, _0x4ad468);
    }
    ['isHit'](_0x5964dc) {
        if (!this['getLayer']())
            return ![];
        if (this.#boundaryData['bLoad'] != logi['maps']['BoundaryData']['STATUS']['LOAD'])
            return ![];
        return logi['maps']['BoundaryChecker']['pointInRegion'](_0x5964dc, this.#boundaryData['boundaryRect']);
    }
    ['isOverlap'](_0x3a1d2a) {
        if (!this['getLayer']())
            return ![];
        const _0x22ba7a = this['getMapCoord'](), _0x2abeaa = _0x22ba7a['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x2abeaa) == ![])
            return ![];
        return logi['maps']['BoundaryChecker']['regionOnRegion'](_0x3a1d2a, this.#boundaryData['boundaryRect']);
    }
    ['drawCanvas']() {
        if (!this['getLayer']())
            return;
        if (this.#boundaryData['bLoad'] != logi['maps']['BoundaryData']['STATUS']['LOAD'])
            return;
        const _0xc66815 = this['getMapCoord'](), _0x1ffaef = this['getGfx2d'](), _0x37c723 = this['getDevicePixelRatio']();
        _0x1ffaef['save'](), _0x1ffaef['scale'](_0x37c723, _0x37c723);
        let _0x491d8a = _0xc66815['world2screen'](this.#position['lng'], this.#position['lat']);
        _0x491d8a['x'] += this.#offsetX, _0x491d8a['y'] += this.#offsetY;
        if (this.#bgBox) {
            let _0x47bae8 = this.#bgBox['width'] ?? 0x0, _0x4cdf5f = this.#bgBox['height'] ?? 0x0;
            if (_0x47bae8 == 0x0 && _0x4cdf5f == 0x0) {
                const _0x3c0366 = {
                    'width': 0x0,
                    'height': 0x0
                };
                this.#textItems['forEach']((_0x538a94, _0x3af862) => {
                    const _0x506df8 = _0x1ffaef['getTextSize'](_0x538a94, this.#fontFamily, this.#fontSize);
                    _0x3c0366['width'] = Math['max'](_0x3c0366['width'], _0x506df8['width']), _0x3af862 == this.#textItems['length'] - 0x1 ? _0x3c0366['height'] += this.#fontSize : _0x3c0366['height'] += this.#fontSize * 1.2;
                }), _0x47bae8 = _0x3c0366['width'], _0x4cdf5f = _0x3c0366['height'];
            }
            let _0x2b629d = logi['maps']['Utils']['getAlignPosition'](_0x491d8a['x'], _0x491d8a['y'], this.#align, _0x47bae8, _0x4cdf5f), _0x13d617 = logi['maps']['Utils']['getPivotPoint'](this.#align, _0x47bae8, _0x4cdf5f);
            _0x1ffaef['drawObjRoundRect'](_0x2b629d['x'], _0x2b629d['y'], _0x47bae8, _0x4cdf5f, this.#bgBox['radius'], this.#bgBox['fillColor'], this.#bgBox['lineWidth'], this.#bgBox['lineColor'], this.#angle, _0x13d617['x'], _0x13d617['y'], 0x0, 0x0, this.#bgBox['offsetX'], this.#bgBox['offsetY'], this.#bgBox['padding']);
        }
        if (this.#bgImage) {
            const _0x39b379 = this.#bgImage?.['naturalWidth'], _0x45bc4b = this.#bgImage?.['naturalHeight'];
            let _0x2f2e27 = logi['maps']['Utils']['getAlignPosition'](_0x491d8a['x'], _0x491d8a['y'], this.#align, _0x39b379, _0x45bc4b), _0x4eaec3 = logi['maps']['Utils']['getPivotPoint'](this.#align, _0x39b379, _0x45bc4b);
            _0x1ffaef['drawObjImage'](this.#bgImage, _0x2f2e27['x'], _0x2f2e27['y'], this.#angle, _0x4eaec3['x'], _0x4eaec3['y'], 0x0, 0x0, this.#bgImgOffsetX, this.#bgImgOffsetY);
        }
        const _0x287c9f = this.#fontSize * 1.2;
        let _0x635dde = 0x0;
        if (this.#textItems['length'] >= 0x2) {
            if (this.#align == logi['maps']['ALIGN']['LM'] || this.#align == logi['maps']['ALIGN']['CM'] || this.#align == logi['maps']['ALIGN']['RM'])
                _0x635dde = (this.#textItems['length'] - 0x1) * _0x287c9f * -0.5;
            else
                (this.#align == logi['maps']['ALIGN']['LB'] || this.#align == logi['maps']['ALIGN']['CB'] || this.#align == logi['maps']['ALIGN']['RB']) && (_0x635dde = (this.#textItems['length'] - 0x1) * _0x287c9f * -0x1);
        }
        this.#angle == 0x0 ? this.#textItems['forEach'](_0x9fb076 => {
            _0x1ffaef['drawObjText'](_0x9fb076, _0x491d8a['x'], _0x491d8a['y'] + _0x635dde, this.#fontFamily, this.#fontSize, this.#textBold, this.#textColor, this.#align), _0x635dde += _0x287c9f;
        }) : (_0x1ffaef['save'](), _0x1ffaef['translate'](_0x491d8a['x'], _0x491d8a['y']), _0x1ffaef['rotate'](this.#angle), this.#textItems['forEach'](_0x5ed0e6 => {
            _0x1ffaef['drawObjText'](_0x5ed0e6, 0x0, _0x635dde, this.#fontFamily, this.#fontSize, this.#textBold, this.#textColor, this.#align), _0x635dde += _0x287c9f;
        }), _0x1ffaef['restore']());
        if (this.#overlapInfo['visibility'] == !![] && this.#boundaryData['overlapCnt'] > 0x0) {
            const _0x64055a = this.#boundaryData['overlapCnt'], _0x44f49f = this.#overlapInfo['screenPos']['x'], _0x307261 = this.#overlapInfo['screenPos']['y'], _0x15536b = this.#overlapInfo['bgRadius'], _0x828b3d = this.#overlapInfo['bgColor'], _0x104565 = this.#overlapInfo['fontSize'], _0x5522be = this.#overlapInfo['textColor'];
            _0x1ffaef['drawObjDot'](_0x44f49f, _0x307261, _0x15536b, _0x828b3d), _0x1ffaef['drawObjText'](_0x64055a, _0x44f49f, _0x307261, '', _0x104565, ![], _0x5522be, logi['maps']['ALIGN']['CM']);
        }
        logi['maps']['Defines']['DEBUG_MODE'] && _0x1ffaef['drawObjPolyLine'](this.#boundaryData['boundaryRect'], 0x2, 'red'), _0x1ffaef['restore']();
    }
};
export default logi['maps']['Label'];