import a9_0x29daff from '../common/logi-maps-types.js?v=2.1.10.1';
import a9_0x156321 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a9_0x1ce579 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a9_0x55738f from '../utility/logi-maps-boundarychecker.js?v=2.1.10.1';
import a9_0x34019e from '../object/logi-maps-object.js?v=2.1.10.1';
import a9_0x53b773 from '../object/logi-maps-line.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['LINETYPE'] = a9_0x29daff['LINETYPE'], logi['maps']['Utils'] = a9_0x156321, logi['maps']['BoundaryData'] = a9_0x1ce579, logi['maps']['BoundaryChecker'] = a9_0x55738f, logi['maps']['Object'] = a9_0x34019e, logi['maps']['Line'] = a9_0x53b773, logi['maps']['Route'] = class extends logi['maps']['Object'] {
    #routeLatLngs = new Array();
    #routeLine = {};
    #passedLatLngs = new Array();
    #passedLine = {};
    #pastRecordLatLngs = new Array();
    #pastRecordLine = {};
    #cutPoint = {
        'lng': 0x0,
        'lat': 0x0
    };
    #cutDegree = 0x0;
    #mode = '';
    #screenCoord = {
        'baseLayer': null,
        'tileLevel': null,
        'tileLevelOffset': null,
        'route': {
            'width': 0x0,
            'origin': {
                'x': 0x0,
                'y': 0x0
            },
            'points': new Array()
        },
        'passed': {
            'width': 0x0,
            'origin': {
                'x': 0x0,
                'y': 0x0
            },
            'points': new Array()
        },
        'record': {
            'width': 0x0,
            'origin': {
                'x': 0x0,
                'y': 0x0
            },
            'points': new Array()
        }
    };
    /**
   * @preserve .
   * @constructor
   * @description
   *  경로를 생성한다.
   * @param {logi.maps.LatLng[]} latlngs 좌표 리스트(WGS84)
   * @param {Object} options option
   *  @param {String} options.key route key (default: random 생성)
   *  @param {String} options.class route class (CSS의 class와 비슷함)
   *  @param {Number} options.zIndex 그리기 순서 (default: 0)
   *  @param {Object} options.routeLine 전체 경로
   *   @param {Number} options.routeLine.width 경로 넓이
   *   @param {String} options.routeLine.color 경로 색
   *   @param {Number} options.routeLine.strokeWidth 경로 스트로크 넓이
   *   @param {String} options.routeLine.strokeColor 경로 스트로크 색
   *  @param {Object} options.passedLine 지나온 경로
   *   @param {Number} options.passedLine.width 지난 경로 넓이
   *   @param {String} options.passedLine.color 지난 경로 색
   *   @param {Number} options.passedLine.strokeWidth 지난 경로 스트로크 넓이
   *   @param {String} options.passedLine.strokeColor 지난 경로 스트로크 색
   *  @param {Object} options.pastRecordLine 경로 재탐색 전 최초 출발지에서 현재까지 이동한 경로
   *   @param {Number} options.pastRecordLine.dotRadius 경로 점의 반지름
   *   @param {Number} options.pastRecordLine.dotGap 경로 점의 간격 길이
   *   @param {String} options.pastRecordLine.color 경로 점의 색
   *   @param {Number} options.pastRecordLine.strokeWidth 경로 점의 스트로크 넓이
   *   @param {String} options.pastRecordLine.strokeColor 경로 점의 스트로크 색
   *  @param {String} options.mode 'PASSED_LINE' OR 'CUT_POINT'
   *  @param {logi.maps.Map} options.map 표시될 Map
   * @example
   *  let route = new logi.maps.Route(
   *   [{lat: 37.5062379, lng: 127.0050378}, {lat: 37.566596, lng: 127.007702}, {lat: 37.5251644, lng: 126.9255491}, {lat: 37.5125585, lng: 127.1025353}, {lat: 37.563692, lng: 126.9822107}, {lat: 37.5173108, lng: 126.9033793}],
   *   {
   *    routeLine: {width: 4, color: '#0088FF', strokeWidth: 1, strokeColor: '#000000'},
   *    passedLine: {width: 4, color: '#999999', strokeWidth: 1, strokeColor: '#959595'},
   *    pastRecordLine: {dotRadius: 2, dotGap: 4, color: '#999999', strokeWidth: 1, strokeColor: '#959595'},
   *    map: logiMap
   *   }
   *  );
   *  //경로선을 그린다.
   */
    constructor(_0x55260c, _0x57ac0a) {
        const _0x3de737 = _0x57ac0a?.['key'] ?? 'rt_' + Math['random']()['toString'](0x24)['slice'](-0x8), _0x195f3a = _0x57ac0a?.['class'] ?? '', _0x55c32d = _0x57ac0a?.['zIndex'] ?? 0x0;
        super(_0x3de737, logi['maps']['Object']['OBJTYPE']['route'], _0x195f3a, _0x55c32d), this.#routeLatLngs = logi['maps']['Utils']['getRoughLatLngs'](_0x55260c ?? []), this.#routeLine['width'] = _0x57ac0a?.['routeLine']?.['width'] ?? 0x4, this.#routeLine['color'] = _0x57ac0a?.['routeLine']?.['color'] ?? '#0088FF', this.#routeLine['strokeWidth'] = _0x57ac0a?.['routeLine']?.['strokeWidth'] ?? 0x0, this.#routeLine['strokeColor'] = _0x57ac0a?.['routeLine']?.['strokeColor'] ?? '#000000', this.#passedLine['width'] = _0x57ac0a?.['passedLine']?.['width'] ?? this.#routeLine['width'], this.#passedLine['color'] = _0x57ac0a?.['passedLine']?.['color'] ?? '#999999', this.#passedLine['strokeWidth'] = _0x57ac0a?.['passedLine']?.['strokeWidth'] ?? 0x0, this.#passedLine['strokeColor'] = _0x57ac0a?.['passedLine']?.['strokeColor'] ?? '#959595', this.#pastRecordLine['dotRadius'] = _0x57ac0a?.['pastRecordLine']?.['dotRadius'] ?? this.#routeLine['width'] * 0.5, this.#pastRecordLine['dotGap'] = _0x57ac0a?.['pastRecordLine']?.['dotGap'] ?? this.#routeLine['width'] * 0.5, this.#pastRecordLine['color'] = _0x57ac0a?.['pastRecordLine']?.['color'] ?? this.#passedLine['color'], this.#pastRecordLine['strokeWidth'] = _0x57ac0a?.['pastRecordLine']?.['strokeWidth'] ?? 0x0, this.#pastRecordLine['strokeColor'] = _0x57ac0a?.['pastRecordLine']?.['strokeColor'] ?? this.#passedLine['strokeColor'], this.#mode = _0x57ac0a?.['mode'] ?? 'PASSED_LINE', this['setMap'](_0x57ac0a?.['map']);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  RouteLine의 속성 정보를 변경한다.
   * @param {Number} width 경로 넓이
   * @param {String} color 경로 색
   * @param {Number} strokeWidth 경로 스트로크 넓이
   * @param {String} strokeColor 경로 스트로크 색
   * @example
   *  route.setRouteLineProperty(4, ‘#FFFFFF’, 1, ‘#000000’);
   *  //RouteLine의 속성 정보가 변경된다.
   */
    ['setRouteLineProperty'](_0x44af0d, _0x3a77d5, _0xa0bc1f, _0x25034d) {
        let _0x437d0e = ![], _0x50adeb = null;
        _0x50adeb = _0x44af0d ?? 0x0, this.#routeLine['width'] != _0x50adeb && (this.#routeLine['width'] = _0x50adeb, _0x437d0e = !![]), _0x50adeb = _0x3a77d5 ?? '#000000', this.#routeLine['color'] != _0x50adeb && (this.#routeLine['color'] = _0x50adeb, _0x437d0e = !![]), _0x50adeb = _0xa0bc1f ?? 0x0, this.#routeLine['strokeWidth'] != _0x50adeb && (this.#routeLine['strokeWidth'] = _0x50adeb, _0x437d0e = !![]), _0x50adeb = _0x25034d ?? '#000000', this.#routeLine['strokeColor'] != _0x50adeb && (this.#routeLine['strokeColor'] = _0x50adeb, _0x437d0e = !![]), _0x437d0e == !![] && this['setUpdateFlag']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  PassedLine의 속성 정보를 변경한다.
   * @param {Number} width 지난 경로 넓이
   * @param {String} color 지난 경로 색
   * @param {Number} strokeWidth 지난 경로 스트로크 넓이
   * @param {String} strokeColor 지난 경로 스트로크 색
   * @example
   *  route.setPassedLineProperty(4, ‘#999999’, 1, ‘#888888’);
   *  //PassedLine의 속성 정보가 변경된다.
   */
    ['setPassedLineProperty'](_0x125996, _0x20bd5e, _0x4e57b4, _0x2964b9) {
        let _0x39a817 = ![], _0x551f9d = null;
        _0x551f9d = _0x125996 ?? 0x0, this.#passedLine['width'] != _0x551f9d && (this.#passedLine['width'] = _0x551f9d, _0x39a817 = !![]), _0x551f9d = _0x20bd5e ?? '#000000', this.#passedLine['color'] != _0x551f9d && (this.#passedLine['color'] = _0x551f9d, _0x39a817 = !![]), _0x551f9d = _0x4e57b4 ?? 0x0, this.#passedLine['strokeWidth'] != _0x551f9d && (this.#passedLine['strokeWidth'] = _0x551f9d, _0x39a817 = !![]), _0x551f9d = _0x2964b9 ?? '#000000', this.#passedLine['strokeColor'] != _0x551f9d && (this.#passedLine['strokeColor'] = _0x551f9d, _0x39a817 = !![]), _0x39a817 == !![] && this['setUpdateFlag']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  PastRecordLine의 속성 정보를 변경한다.
   * @param {Number} dotRadius 경로 점의 반지름
   * @param {Number} dotGap 경로 점의 간격 길이
   * @param {String} color 경로 점의 색
   * @param {Number} strokeWidth 경로 점의 스트로크 넓이
   * @param {String} strokeColor 경로 점의 스트로크 색
   * @example
   *  route.setPastRecordLineProperty(2, 4, ‘#999999’, 1, ‘888888#’);
   *  //PastRecordLine의 속성 정보가 변경된다.
   */
    ['setPastRecordLineProperty'](_0x2c9ce4, _0x375122, _0x2bed46, _0x53b0fd, _0x677e2d) {
        let _0x532f41 = ![], _0x5963fb = null;
        _0x5963fb = _0x2c9ce4 ?? 0x0, this.#pastRecordLine['dotRadius'] != _0x5963fb && (this.#pastRecordLine['dotRadius'] = _0x5963fb, _0x532f41 = !![]), _0x5963fb = _0x375122 ?? 0x0, this.#pastRecordLine['dotGap'] != _0x5963fb && (this.#pastRecordLine['dotGap'] = _0x5963fb, _0x532f41 = !![]), _0x5963fb = _0x2bed46 ?? '#000000', this.#pastRecordLine['color'] != _0x5963fb && (this.#pastRecordLine['color'] = _0x5963fb, _0x532f41 = !![]), _0x5963fb = _0x53b0fd ?? 0x0, this.#pastRecordLine['strokeWidth'] != _0x5963fb && (this.#pastRecordLine['strokeWidth'] = _0x5963fb, _0x532f41 = !![]), _0x5963fb = _0x677e2d ?? '#000000', this.#pastRecordLine['strokeColor'] != _0x5963fb && (this.#pastRecordLine['strokeColor'] = _0x5963fb, _0x532f41 = !![]), _0x532f41 == !![] && this['setUpdateFlag']();
    }
    ['isHit'](_0x2277b8) {
        if (!this['getLayer']())
            return ![];
        const _0x40b221 = this['getMapCoord'](), _0x2eef9b = _0x40b221['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x2eef9b) == ![])
            return ![];
        if (this.#screenCoord['route']['points']['length'] >= 0x2 && this.#screenCoord['route']['width'] > 0x0)
            return logi['maps']['BoundaryChecker']['pointInPolyline'](_0x2277b8, this.#screenCoord['route']['points'], this.#screenCoord['route']['width'], this.#screenCoord['route']['origin']);
        return ![];
    }
    ['isOverlap'](_0x186a61) {
        if (!this['getLayer']())
            return ![];
        const _0x44a9f3 = this['getMapCoord'](), _0x3b1dd4 = _0x44a9f3['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x3b1dd4) == ![])
            return ![];
        if (this.#screenCoord['route']['points']['length'] >= 0x2 && this.#screenCoord['route']['width'] > 0x0)
            return logi['maps']['BoundaryChecker']['regionOnPolyline'](_0x186a61, this.#screenCoord['route']['points'], this.#screenCoord['route']['width'], this.#screenCoord['route']['origin']);
        return ![];
    }
    /**
   * @preserve .
   * @method
   * @description
   *  그릴 경로를 설정한다.
   * @param {logi.maps.LatLng[]} latlngs 경로 좌표
   * @example
   *  route.setRouteLine([{lat: 37.5062379, lng: 127.0050378}, {lat: 37.566596, lng: 127.007702}, {lat: 37.5251644, lng: 126.9255491}]);
   *  //경로의 좌표를 전달한다.
   */
    ['setRouteLine'](_0x89966f) {
        this.#routeLatLngs = logi['maps']['Utils']['getRoughLatLngs'](_0x89966f), this.#screenCoord = {
            'baseLayer': null,
            'tileLevel': null,
            'tileLevelOffset': null,
            'route': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            },
            'passed': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            },
            'record': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            }
        }, this['setUpdateFlag']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  경로 재탐색 전 최초 출발지에서 현재까지 이동한 경로를 설정한다
   * @param {logi.maps.LatLng[]} latlngs 경로 좌표
   * @example
   *  route.setPastRecordLine([{lat: 37.5115557, lng: 127.0595261}, {lat: 37.5062379, lng: 127.0050378}]);
   *  //경로 재탐색 위치에서 최초 출발지까지의 좌표를 전달한다.
   */
    ['setPastRecordLine'](_0x52a181) {
        this.#pastRecordLatLngs = logi['maps']['Utils']['getRoughLatLngs'](_0x52a181), this.#screenCoord = {
            'baseLayer': null,
            'tileLevel': null,
            'tileLevelOffset': null,
            'route': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            },
            'passed': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            },
            'record': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            }
        }, this['setUpdateFlag']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지나온 경로 좌표를 설정한다.
   * @param {logi.maps.LatLng[]} latlngs 경로 좌표
   * @example
   *  route.setPassedLine([{lat: 37.5115557, lng: 127.0595261}, {lat: 37.5062379, lng: 127.0050378}]);
   *  //지나온 경로 좌표를 전달한다.
   */
    ['setPassedLine'](_0x50c6af) {
        this.#mode === 'PASSED_LINE' && (this.#passedLatLngs = logi['maps']['Utils']['getRoughLatLngs'](_0x50c6af), this.#screenCoord = {
            'baseLayer': null,
            'tileLevel': null,
            'tileLevelOffset': null,
            'route': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            },
            'passed': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            },
            'record': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            }
        }, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  지나온 경로 좌표를 추가한다.
   * @param {logi.maps.LatLng} latlng 경로 좌표
   * @example
   *  route.setPassedLine({lat: 37.5002379, lng: 127.0050378});
   *  //지나온 경로에 추가할 좌표를 전달한다.
   */
    ['addPassedPoint'](_0x3fe1a1) {
        this.#mode === 'PASSED_LINE' && (logi['maps']['Utils']['addRoughLatLngs'](this.#passedLatLngs, _0x3fe1a1) == !![] && (this.#screenCoord = {
            'baseLayer': null,
            'tileLevel': null,
            'tileLevelOffset': null,
            'route': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            },
            'passed': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            },
            'record': {
                'width': 0x0,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'points': new Array()
            }
        }, this['setUpdateFlag']()));
    }
    ['setCutPoint'](_0x2466f8, _0x1bf03e) {
        if (this.#mode === 'CUT_POINT') {
            if (this.#cutPoint['lng'] !== _0x2466f8['lng'] || this.#cutPoint['lat'] !== _0x2466f8['lat'] || this.#cutDegree !== _0x1bf03e) {
                this.#cutPoint['lng'] = _0x2466f8['lng'], this.#cutPoint['lat'] = _0x2466f8['lat'], this.#cutDegree = _0x1bf03e;
                const _0x2a8313 = this.#getSnappedLine(this.#routeLatLngs, this.#cutPoint, this.#cutDegree);
                this.#passedLatLngs = _0x2a8313['passed'], this.#screenCoord = {
                    'baseLayer': null,
                    'tileLevel': null,
                    'tileLevelOffset': null,
                    'route': {
                        'width': 0x0,
                        'origin': {
                            'x': 0x0,
                            'y': 0x0
                        },
                        'points': new Array()
                    },
                    'passed': {
                        'width': 0x0,
                        'origin': {
                            'x': 0x0,
                            'y': 0x0
                        },
                        'points': new Array()
                    },
                    'record': {
                        'width': 0x0,
                        'origin': {
                            'x': 0x0,
                            'y': 0x0
                        },
                        'points': new Array()
                    }
                }, this['setUpdateFlag']();
            }
        }
    }
    ['drawCanvas']() {
        if (!this['getLayer']()) {
            this.#screenCoord['baseLayer'] != null && (this.#screenCoord = {
                'baseLayer': null,
                'tileLevel': null,
                'tileLevelOffset': null,
                'route': {
                    'width': 0x0,
                    'origin': {
                        'x': 0x0,
                        'y': 0x0
                    },
                    'points': new Array()
                },
                'passed': {
                    'width': 0x0,
                    'origin': {
                        'x': 0x0,
                        'y': 0x0
                    },
                    'points': new Array()
                },
                'record': {
                    'width': 0x0,
                    'origin': {
                        'x': 0x0,
                        'y': 0x0
                    },
                    'points': new Array()
                }
            });
            return;
        }
        const _0x125218 = this['getMapCoord'](), _0x397142 = _0x125218['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x397142) == ![]) {
            this.#screenCoord['baseLayer'] != null && (this.#screenCoord = {
                'baseLayer': null,
                'tileLevel': null,
                'tileLevelOffset': null,
                'route': {
                    'width': 0x0,
                    'origin': {
                        'x': 0x0,
                        'y': 0x0
                    },
                    'points': new Array()
                },
                'passed': {
                    'width': 0x0,
                    'origin': {
                        'x': 0x0,
                        'y': 0x0
                    },
                    'points': new Array()
                },
                'record': {
                    'width': 0x0,
                    'origin': {
                        'x': 0x0,
                        'y': 0x0
                    },
                    'points': new Array()
                }
            });
            return;
        }
        const _0x4eefde = this['getGfx2d'](), _0x45956c = this['getDevicePixelRatio'](), _0x2c02af = _0x125218['getTileLevelOffset']();
        if (this.#screenCoord['baseLayer'] != this['getLayer']() || this.#screenCoord['tileLevel'] != _0x125218['getLevel']() || this.#screenCoord['tileLevelOffset'] != _0x2c02af) {
            this.#screenCoord['baseLayer'] = this['getLayer'](), this.#screenCoord['tileLevel'] = _0x125218['getLevel'](), this.#screenCoord['tileLevelOffset'] = _0x2c02af;
            {
                this.#screenCoord['record']['width'] = 0x0, this.#screenCoord['record']['origin'] = {
                    'x': 0x0,
                    'y': 0x0
                }, this.#screenCoord['record']['points'] = new Array();
                if (this.#pastRecordLatLngs['length'] >= 0x2) {
                    this.#screenCoord['record']['origin'] = _0x125218['world2screen'](this.#pastRecordLatLngs[0x0]['lng'], this.#pastRecordLatLngs[0x0]['lat']);
                    let _0xd662e9 = {
                            'x': null,
                            'y': null
                        }, _0x153fa4 = {
                            'x': null,
                            'y': null
                        };
                    for (let _0x2bd3d7 of this.#pastRecordLatLngs) {
                        _0xd662e9 = _0x125218['world2screen'](_0x2bd3d7['lng'], _0x2bd3d7['lat']), _0xd662e9['x'] = _0xd662e9['x'] - this.#screenCoord['record']['origin']['x'], _0xd662e9['y'] = _0xd662e9['y'] - this.#screenCoord['record']['origin']['y'], (_0x153fa4['x'] != _0xd662e9['x'] || _0x153fa4['y'] != _0xd662e9['y']) && (this.#screenCoord['record']['points']['push'](_0xd662e9), _0x153fa4 = _0xd662e9);
                    }
                }
            }
            {
                this.#screenCoord['route']['width'] = 0x0, this.#screenCoord['route']['origin'] = {
                    'x': 0x0,
                    'y': 0x0
                }, this.#screenCoord['route']['points'] = new Array();
                if (this.#routeLatLngs['length'] >= 0x2) {
                    this.#screenCoord['route']['origin'] = _0x125218['world2screen'](this.#routeLatLngs[0x0]['lng'], this.#routeLatLngs[0x0]['lat']);
                    let _0x84d393 = {
                            'x': null,
                            'y': null
                        }, _0x2e1cbe = {
                            'x': null,
                            'y': null
                        };
                    for (let _0x1c980c of this.#routeLatLngs) {
                        _0x84d393 = _0x125218['world2screen'](_0x1c980c['lng'], _0x1c980c['lat']), _0x84d393['x'] = _0x84d393['x'] - this.#screenCoord['route']['origin']['x'], _0x84d393['y'] = _0x84d393['y'] - this.#screenCoord['route']['origin']['y'], (_0x2e1cbe['x'] != _0x84d393['x'] || _0x2e1cbe['y'] != _0x84d393['y']) && (this.#screenCoord['route']['points']['push'](_0x84d393), _0x2e1cbe = _0x84d393);
                    }
                }
            }
            {
                this.#screenCoord['passed']['width'] = 0x0, this.#screenCoord['passed']['origin'] = {
                    'x': 0x0,
                    'y': 0x0
                }, this.#screenCoord['passed']['points'] = new Array();
                if (this.#passedLatLngs['length'] >= 0x2) {
                    this.#screenCoord['passed']['origin'] = _0x125218['world2screen'](this.#passedLatLngs[0x0]['lng'], this.#passedLatLngs[0x0]['lat']);
                    let _0x1a3cd6 = {
                            'x': null,
                            'y': null
                        }, _0x285ccb = {
                            'x': null,
                            'y': null
                        };
                    for (let _0xb2555b of this.#passedLatLngs) {
                        _0x1a3cd6 = _0x125218['world2screen'](_0xb2555b['lng'], _0xb2555b['lat']), _0x1a3cd6['x'] = _0x1a3cd6['x'] - this.#screenCoord['passed']['origin']['x'], _0x1a3cd6['y'] = _0x1a3cd6['y'] - this.#screenCoord['passed']['origin']['y'], (_0x285ccb['x'] != _0x1a3cd6['x'] || _0x285ccb['y'] != _0x1a3cd6['y']) && (this.#screenCoord['passed']['points']['push'](_0x1a3cd6), _0x285ccb = _0x1a3cd6);
                    }
                }
            }
        }
        if (this.#screenCoord['record']['points']['length'] >= 0x2) {
            this.#screenCoord['record']['origin'] = _0x125218['world2screen'](this.#pastRecordLatLngs[0x0]['lng'], this.#pastRecordLatLngs[0x0]['lat']), _0x4eefde['save'](), _0x4eefde['scale'](_0x45956c, _0x45956c), _0x4eefde['translate'](this.#screenCoord['record']['origin']['x'], this.#screenCoord['record']['origin']['y']);
            const _0x1286ce = this.#pastRecordLine['dotRadius'];
            this.#screenCoord['record']['width'] = _0x1286ce;
            let _0x300a02 = this.#pastRecordLine['dotGap'] + _0x1286ce * 0x2;
            if (this.#pastRecordLine['strokeWidth'] > 0x0) {
                const _0x5ea113 = _0x1286ce + this.#pastRecordLine['strokeWidth'];
                this.#screenCoord['record']['width'] = _0x5ea113, _0x300a02 = this.#pastRecordLine['dotGap'] + _0x5ea113 * 0x2, _0x4eefde['drawObjDottedLine'](this.#screenCoord['record']['points'], _0x5ea113, _0x300a02, this.#pastRecordLine['strokeColor']);
            }
            _0x4eefde['drawObjDottedLine'](this.#screenCoord['record']['points'], _0x1286ce, _0x300a02, this.#pastRecordLine['color']), _0x4eefde['restore']();
        }
        if (this.#screenCoord['route']['points']['length'] >= 0x2) {
            this.#screenCoord['route']['origin'] = _0x125218['world2screen'](this.#routeLatLngs[0x0]['lng'], this.#routeLatLngs[0x0]['lat']), _0x4eefde['save'](), _0x4eefde['scale'](_0x45956c, _0x45956c), _0x4eefde['translate'](this.#screenCoord['route']['origin']['x'], this.#screenCoord['route']['origin']['y']);
            const _0x52fb50 = this.#routeLine['width'];
            this.#screenCoord['route']['width'] = _0x52fb50;
            if (this.#routeLine['strokeWidth'] > 0x0) {
                const _0x2dd29d = _0x52fb50 + this.#routeLine['strokeWidth'] * 0x2;
                this.#screenCoord['route']['width'] = _0x2dd29d, _0x4eefde['drawObjPolyLine'](this.#screenCoord['route']['points'], _0x2dd29d, this.#routeLine['strokeColor']);
            }
            _0x4eefde['drawObjPolyLine'](this.#screenCoord['route']['points'], _0x52fb50, this.#routeLine['color']), _0x4eefde['restore']();
        }
        if (this.#screenCoord['passed']['points']['length'] >= 0x2) {
            this.#screenCoord['passed']['origin'] = _0x125218['world2screen'](this.#passedLatLngs[0x0]['lng'], this.#passedLatLngs[0x0]['lat']), _0x4eefde['save'](), _0x4eefde['scale'](_0x45956c, _0x45956c), _0x4eefde['translate'](this.#screenCoord['passed']['origin']['x'], this.#screenCoord['passed']['origin']['y']);
            const _0x226f85 = this.#passedLine['width'];
            this.#screenCoord['passed']['width'] = _0x226f85;
            if (this.#passedLine['strokeWidth'] > 0x0) {
                const _0x32167c = _0x226f85 + this.#passedLine['strokeWidth'] * 0x2;
                this.#screenCoord['passed']['width'] = _0x32167c, _0x4eefde['drawObjPolyLine'](this.#screenCoord['passed']['points'], _0x32167c, this.#passedLine['strokeColor']);
            }
            _0x4eefde['drawObjPolyLine'](this.#screenCoord['passed']['points'], _0x226f85, this.#passedLine['color']), _0x4eefde['restore']();
        }
    }
    #getSnappedLine(_0x4826aa, _0x1fa937, _0x167d1e) {
        const _0x57998b = this.#nearestPointOnLine(_0x4826aa, _0x1fa937, _0x167d1e), _0x3a16ca = {
                'lng': _0x57998b['lng'],
                'lat': _0x57998b['lat']
            }, _0x4e6c = _0x57998b['index'];
        let _0xac2bea = _0x4826aa['slice'](0x0, _0x4e6c + 0x1);
        _0xac2bea['push'](_0x3a16ca);
        let _0x2bc6fa = _0x4826aa['slice'](_0x4e6c);
        return _0x2bc6fa[0x0] = _0x3a16ca, {
            'passed': _0xac2bea,
            'passing': _0x2bc6fa
        };
    }
    #nearestPointOnLine(_0x146bdf, _0x414f2c, _0x58ad5e) {
        let _0x2fb60e = {
                'lng': 0x0,
                'lat': 0x0,
                'distance': Infinity,
                'index': 0x0
            }, _0xf43dab = {
                'lng': 0x0,
                'lat': 0x0,
                'distance': Infinity,
                'index': 0x0
            };
        _0x58ad5e && (_0x58ad5e = logi['maps']['Utils']['generalizeDegree'](_0x58ad5e));
        let _0x29a638 = 0x0, _0x44697d = null;
        for (let _0x8c374 of _0x146bdf) {
            if (_0x44697d != null) {
                const _0x1fcbd = logi['maps']['Utils']['getPerpendicularLatLng'](_0x44697d, _0x8c374, _0x414f2c);
                let _0x5730fb = ![];
                if (_0x58ad5e) {
                    let _0x3bacdc = {
                            'x': _0x8c374['lng'] - _0x44697d['lng'],
                            'y': _0x8c374['lat'] - _0x44697d['lat']
                        }, _0x552fa8 = {
                            'x': 0x0,
                            'y': 0x1
                        };
                    _0x3bacdc = logi['maps']['Utils']['getVectorNormalize'](_0x3bacdc), _0x552fa8 = logi['maps']['Utils']['getVectorNormalize'](_0x552fa8);
                    const _0x53c802 = logi['maps']['Utils']['getDegreeBetweenVectors'](_0x3bacdc, _0x552fa8), _0x49e194 = _0x53c802 - _0x58ad5e;
                    _0x5730fb = _0x49e194 < -0x87 || _0x49e194 > 0x87;
                }
                _0x5730fb === ![] ? _0x1fcbd['distance'] < _0x2fb60e['distance'] && (_0x2fb60e['lng'] = _0x1fcbd['lng'], _0x2fb60e['lat'] = _0x1fcbd['lat'], _0x2fb60e['distance'] = _0x1fcbd['distance'], _0x2fb60e['index'] = _0x29a638 - 0x1) : _0x1fcbd['distance'] < _0xf43dab['distance'] && (_0xf43dab['lng'] = _0x1fcbd['lng'], _0xf43dab['lat'] = _0x1fcbd['lat'], _0xf43dab['distance'] = _0x1fcbd['distance'], _0xf43dab['index'] = _0x29a638 - 0x1);
            }
            _0x44697d = _0x8c374, _0x29a638 += 0x1;
        }
        return _0x2fb60e['distance'] < Infinity ? _0x2fb60e : _0xf43dab;
    }
};
export default logi['maps']['Route'];