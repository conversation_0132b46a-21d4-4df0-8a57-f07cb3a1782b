import a7_0x280ff6 from '../common/logi-maps-types.js?v=2.1.10.1';
import a7_0x113035 from '../utility/logi-maps-bridge.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['OBJEVENT'] = a7_0x280ff6['OBJEVENT'], logi['maps']['Bridge'] = a7_0x113035, logi['maps']['Object'] = class {
    static ['OBJTYPE'] = {
        'image': 'image',
        'label': 'label',
        'line': 'line',
        'polygon': 'polygon',
        'circle': 'circle',
        'route': 'route',
        'gps': 'gps',
        'custom': 'custom',
        'meta': 'meta'
    };
    static ['ORDERTYPE'] = {
        'object': 'object',
        'zindex': 'zindex'
    };
    #map = null;
    #objKey;
    #objType;
    #objClass;
    #zIndex;
    #baseLayer;
    #isVisible;
    #renderRange = {
        'fromLevel': 0x0,
        'toLevel': 0x20
    };
    /**
   * @preserve .
   * @constructor
   * @description
   *  Object class
   * @param {String} objKey object key
   * @param {String} objType object type (image, label, line, polygon, route)
   * @param {String} objClass class (CSS의 class와 비슷함) (default: '')
   * @param {Number} zIndex 그리기 순서 (default: 0)
   * @param {String} baseLayer 표시될 레이어 (default: undefined)
   */
    constructor(_0x29acf1, _0x3112cb, _0x4b4fb9 = '', _0x599bb7 = 0x0, _0xff08ae) {
        this.#objKey = _0x29acf1, this.#objType = _0x3112cb, this.#objClass = _0x4b4fb9, this.#zIndex = _0x599bb7, this.#isVisible = !![], this['eventHandlers'] = {};
        for (const _0x12667e in logi['maps']['OBJEVENT']) {
            this['eventHandlers'][_0x12667e] = [];
        }
        this['setLayer'](_0xff08ae);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  표시될 Map을 등록한다.
   *  setMap(null)을 입력하면 지도에서 이미지가 제거된다.
   * @param {logi.maps.Map} map 표시될 Map
   * @example
   *  object.setMap(map);
   *  //map 지도 위에 그려진다.
   */
    ['setMap'](_0x354751) {
        this.#map?.['removeObject'](this), this.#map = _0x354751, this.#map?.['addObject'](this);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 Map을 전달한다.
   * @returns {logi.maps.Map} 등록된 Map
   * @example
   *  let map = object.getMap();
   *  //등록된 Map이 전달된다.
   */
    ['getMap']() {
        return this.#map;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  발생되는 이벤트를 등록된 리스너로 콜백한다.
   * @param {OBJEVENT} eventName 이벤트명
   * @param {Function} func 콜백함수
   * @example
   *  object.addEventListener(logi.maps.OBJEVENT.dblclick, function(event) { console.log("double click"); });
   *  //더블클릭하면 console 창에 “double click”이 출력된다.
   */
    ['addEventListener'](_0x5e782b, _0x12279c) {
        let _0x4bd41b = this['eventHandlers'][_0x5e782b];
        _0x4bd41b !== undefined && (_0x4bd41b['forEach'](_0x27eef3 => {
            if (_0x27eef3 == _0x12279c)
                return;
        }), _0x4bd41b['push'](_0x12279c));
    }
    /**
   * @preserve .
   * @method
   * @description
   *  등록된 이벤트 리스너를 지운다.
   * @param {OBJEVENT} eventName 이벤트명
   * @param {Function} func 콜백함수
   * @example
   *  object.removeEventListener(logi.maps.OBJEVENT.dblclick, onEvent);
   *  //onEvent 함수가 이미지 이벤트 리스너에서 제거 된다.
   */
    ['removeEventListener'](_0x4c808f, _0xf86ab5) {
        let _0x3deab8 = this['eventHandlers'][_0x4c808f];
        _0x3deab8 !== undefined && _0x3deab8['forEach']((_0x23fa8c, _0x119c7e) => {
            if (_0x23fa8c == _0xf86ab5) {
                _0x3deab8['splice'](_0x119c7e, 0x1);
                return;
            }
        });
    }
    #sendBridgeEvent(_0x58bab2) {
        logi['maps']['Bridge']['onObjEvent'](_0x58bab2);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  모바일 웹앱에서 Bridge로 전달 받을 이벤트를 등록한다.
   *  [message format]
   *   - ‘message’: ‘onMapEvent’, ‘type’:{eventName}, ‘pointX’:{screenX}, ‘pointY’:{screenY}
   * @param {BRIDGE_MAPEVENT} eventName 이벤트명
   * @param {Boolean} activity 활성화 여부
   * @example
   *  object.setBridgeEvent(logi.maps.OBJEVENT.touch, true);
   *  //터치하면 Bridge를 통해 onObjEvent 함수 또는 메시지로 정보가 전달된다.
   */
    ['setBridgeEvent'](_0x52776c, _0x24c323) {
        _0x24c323 ? this['addEventListener'](_0x52776c, this.#sendBridgeEvent) : this['removeEventListener'](_0x52776c, this.#sendBridgeEvent);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  object를 보이게 하거나 숨긴다.
   * @param {Boolean} visible 보이기(true), 숨기기(false)
   * @example
   *  object.setVisible(false);
   *  //object가 숨겨진다.
   */
    ['setVisible'](_0x5de0af) {
        this.#isVisible != _0x5de0af && (this.#isVisible = _0x5de0af, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  object의 보임/숨김 상태를 전달한다.
   * @returns {Boolean} 보이기(true), 숨기기(false)
   * @example
   *  let visible = object.getVisible();
   *  //object의 보임/숨김 상태가 전달된다.
   */
    ['getVisible']() {
        return this.#isVisible;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  object가 그려져야 할 레벨 범위를 지정한다.
   *  설정하지 않으면 모든 레벨에서 그려진다.
   * @param {Number} fromLevel 시작 레벨
   * @param {Number} toLevel 끝 레벨
   * @example
   *  object.setRenderRange(16, 17);
   *  //맵 레벨이 16~18일 때 object가 그려진다.
   */
    ['setRenderRange'](_0x29cd01, _0x15eaab) {
        (this.#renderRange['fromLevel'] != _0x29cd01 || this.#renderRange['toLevel'] != _0x15eaab) && (this.#renderRange['fromLevel'] = _0x29cd01, this.#renderRange['toLevel'] = _0x15eaab, this['setUpdateFlag']());
    }
    ['checkRenderRange'](_0x594feb) {
        if (this.#renderRange['fromLevel'] <= _0x594feb && this.#renderRange['toLevel'] >= _0x594feb)
            return !![];
        return ![];
    }
    /**
   * @preserve .
   * @method
   * @deprecated
   * 'key' was declared deprecated. (>> getKey())
   */
    get ['key']() {
        return this.#objKey;
    }
    ['getKey']() {
        return this.#objKey;
    }
    ['getType']() {
        return this.#objType;
    }
    ['getClass']() {
        return this.#objClass;
    }
    get ['zIndex']() {
        return this.#zIndex;
    }
    ['getZIndex']() {
        return this.#zIndex;
    }
    ['setUpdateFlag']() {
        this.#baseLayer?.['setUpdateFlag']();
    }
    ['setLayer'](_0xd38bb8) {
        this.#baseLayer != _0xd38bb8 && (this.#baseLayer = _0xd38bb8, this['setUpdateFlag']());
    }
    ['getLayer']() {
        return this.#baseLayer;
    }
    ['getGfxCanvas']() {
        return this.#baseLayer?.['getGfxCanvas'](0x0);
    }
    ['getGfxgl']() {
        return this.#baseLayer?.['getGfxgl'](0x0);
    }
    ['getGfx2d']() {
        return this.#baseLayer?.['getGfx2d'](0x0);
    }
    ['getDevicePixelRatio']() {
        return this.#baseLayer?.['getDevicePixelRatio']();
    }
    ['getMapCoord']() {
        return this.#baseLayer?.['getMapCoord']();
    }
    ['show']() {
        this.#isVisible != !![] && (this.#isVisible = !![], this['setUpdateFlag']());
    }
    ['hide']() {
        this.#isVisible != ![] && (this.#isVisible = ![], this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @deprecated
   * 'on' was declared deprecated. (>> addEventListener(eventName, onEventHandle))
   */
    ['on'](_0xbdccbe, _0x1dfd71) {
        let _0x302fe5 = this['eventHandlers'][_0xbdccbe];
        _0x302fe5 && (_0x302fe5['length'] = 0x0, _0x302fe5['push'](_0x1dfd71));
    }
};
export default logi['maps']['Object'];