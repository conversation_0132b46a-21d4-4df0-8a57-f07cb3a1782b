import a1_0x310079 from '../utility/logi-maps-utils.js?v=********';
import a1_0x1a7c62 from '../utility/logi-maps-boundarydata.js?v=********';
import a1_0x15d7de from '../utility/logi-maps-boundarychecker.js?v=********';
import a1_0x56695e from '../object/logi-maps-object.js?v=********';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Utils'] = a1_0x310079, logi['maps']['BoundaryData'] = a1_0x1a7c62, logi['maps']['BoundaryChecker'] = a1_0x15d7de, logi['maps']['Object'] = a1_0x56695e, logi['maps']['Custom'] = class extends logi['maps']['Object'] {
    #position = null;
    #content = null;
    #htmlElem = null;
    /**
   * @preserve .
   * @constructor
   * @description
   *  커스텀 객체를 생성한다.
   *   - content(String 또는 HtmlElement)를 생성해서 월드 좌표를 스크린 좌표로 실시간 업데이트 함
   *   - 오버레이 레이어에 그려지기 때문에 다른 요소 (Image, Label ...) 보다 상위에 위치 함
   * @param {logi.maps.LatLng} position 중심 좌표(WGS84)
   * @param {String | HTMLElement} content 지도에 그릴 요소
   * @param {Object} options option
   *  @param {String} options.key custom key (default: random 생성)
   *  @param {String} options.class custom class (CSS의 class와 비슷함)
   *  @param {logi.maps.Map} options.map 표시될 Map
   * @example
   *  let custom = new logi.maps.Custom(
   *    {lat: 37.5115557, lng: 127.0595261},
   *    '<div>custom</div>', {
   *    map: logiMap
   *  });
   *  //content를 그려진다.
   */
    constructor(_0x8dbb56, _0x4ab049, _0x3218b2) {
        const _0x2677d9 = _0x3218b2?.['key'] ?? 'cs_' + Math['random']()['toString'](0x24)['slice'](-0x8), _0x2d3c37 = _0x3218b2?.['class'] ?? '';
        super(_0x2677d9, logi['maps']['Object']['OBJTYPE']['custom'], _0x2d3c37, 0x0), this.#position = _0x8dbb56, this.#content = _0x4ab049, this['setMap'](_0x3218b2?.['map']);
    }
    ['destroy']() {
        this.#htmlElem && this.#htmlElem['parentNode'] && this.#htmlElem['parentNode']['removeChild'](this.#htmlElem);
    }
    ['isHit'](_0x5703b6) {
        return ![];
    }
    ['isOverlap'](_0x15a4f6) {
        if (!this['getLayer']())
            return ![];
        return ![];
    }
    #updateCustomPostion(_0x336c57) {
        if (_0x336c57['dataset']?.['lng'] && _0x336c57['dataset']?.['lat']) {
            const _0x322ec1 = this['getMapCoord'](), _0x3a338f = _0x322ec1['world2screen'](Number(_0x336c57['dataset']['lng']), Number(_0x336c57['dataset']['lat']));
            _0x336c57['style']['left'] = _0x3a338f['x'] + 'px', _0x336c57['style']['top'] = _0x3a338f['y'] + 'px';
        }
    }
    ['drawCanvas']() {
        if (!this.#htmlElem) {
            const _0x2f15f1 = this['getLayer']()?.['getDivElem']();
            if (_0x2f15f1) {
                const _0x90677 = document['createElement']('div');
                _0x90677['style']['position'] = 'absolute', _0x90677['dataset']['lng'] = this.#position['lng'], _0x90677['dataset']['lat'] = this.#position['lat'], this.#content instanceof HTMLElement ? _0x90677['replaceChildren'](this.#content) : _0x90677['innerHTML'] = this.#content, _0x2f15f1['appendChild'](_0x90677), this.#htmlElem = _0x90677;
            }
        }
        this.#htmlElem && this.#updateCustomPostion(this.#htmlElem);
    }
};
export default logi['maps']['Custom'];