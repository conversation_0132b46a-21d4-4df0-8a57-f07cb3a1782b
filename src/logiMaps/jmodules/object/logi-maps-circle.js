import a0_0x18b63e from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a0_0x5d24b3 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a0_0x57c6da from '../utility/logi-maps-boundarychecker.js?v=2.1.10.1';
import a0_0x591e41 from '../object/logi-maps-object.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Utils'] = a0_0x18b63e, logi['maps']['BoundaryData'] = a0_0x5d24b3, logi['maps']['BoundaryChecker'] = a0_0x57c6da, logi['maps']['Object'] = a0_0x591e41, logi['maps']['Cirlce'] = class extends logi['maps']['Object'] {
    #center;
    #radius;
    #radiusUnit = 'pixel';
    #fillColor;
    #lineWidth;
    #lineColor;
    #screenCoord = {
        'center': {
            'x': 0x0,
            'y': 0x0
        }
    };
    /**
   * @preserve .
   * @constructor
   * @description
   *  원을 생성한다.
   * @param {logi.maps.LatLng} center 중심 좌표(WGS84)
   * @param {String} radius 반지름
   * @param {String} fillColor 채우기 색상
   * @param {Object} options option
   *  @param {String} options.key circle key (default: random 생성)
   *  @param {String} options.class circle class (CSS의 class와 비슷함)
   *  @param {Number} options.zIndex 그리기 순서 (default: 0)
   *  @param {Number} options.strokeWidth 선 두께
   *  @param {String} options.strokeColor 선 색상
   *  @param {String} options.radiusUnit meter, pixel
   *  @param {logi.maps.Map} options.map 표시될 Map
   * @example
   *  let circle = new logi.maps.Cirlce(
   *    {lat: 37.5115557, lng: 127.0595261},
   *    64.0,
   *    'blue', {
   *    strokeWidth: 2,
   *    strokeColor: '#0a32ff',
   *    map: logiMap
   *  });
   *  //선 두께가 2인 파랑색 원을 그린다.
   */
    constructor(_0xc3acbc, _0x19e5b7, _0x84b918, _0x4a7e20) {
        const _0x2184b5 = _0x4a7e20?.['key'] ?? 'cc_' + Math['random']()['toString'](0x24)['slice'](-0x8), _0x19eea2 = _0x4a7e20?.['class'] ?? '', _0x35ca0e = _0x4a7e20?.['zIndex'] ?? 0x0;
        super(_0x2184b5, logi['maps']['Object']['OBJTYPE']['circle'], _0x19eea2, _0x35ca0e), this.#center = { ..._0xc3acbc }, this.#radius = _0x19e5b7, this.#fillColor = _0x84b918 ?? '#000000', this.#lineWidth = _0x4a7e20?.['lineWidth'] ?? 0x0, this.#lineColor = _0x4a7e20?.['lineColor'] ?? '#000000', this.#radiusUnit = _0x4a7e20['radiusUnit'] ?? 'pixel', this['setMap'](_0x4a7e20?.['map']);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  원의 중심 좌표를 변경한다.
   * @param {logi.maps.LatLng} center 중심 좌표(WGS84)
   * @example
   *  circle.setCenter({lat: 37.5115557, lng: 127.0595261});
   *  //원의 중심 좌표가 변경된다.
   */
    ['setCenter'](_0xb1f96a) {
        _0xb1f96a && this.#center != _0xb1f96a && (this.#center = { ..._0xb1f96a }, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  원의 반지름을 변경한다.
   * @param {Number} radius
   * @example
   *  circle.setCenter({lat: 37.5115557, lng: 127.0595261});
   *  //원의 중심 좌표가 변경된다.
   */
    ['setRadius'](_0x1c411a, _0x83023e = 'pixel') {
        _0x1c411a && this.#radius != _0x1c411a && (this.#radius = _0x1c411a, this.#radiusUnit = _0x83023e ?? 'pixel', this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  원의 색상을 변경한다.
   * @param {String} fillColor 색상
   * @example
   *  circle.setFillColor(‘#FFFFFF’);
   *  //원의 색상이 하얀색으로 변경된다.
   */
    ['setFillColor'](_0x598025) {
        _0x598025 = _0x598025 ?? '#000000', this.#fillColor != _0x598025 && (this.#fillColor = _0x598025, this['setUpdateFlag']());
    }
    /**
   * @preserve .
   * @method
   * @description
   *  원 외곽 라인의 속성을 변경한다.
   * @param {Number} linewidth 외곽 라인 넓이
   * @param {String} lineColor 외곽 라인 색
   * @example
   *  circle.setLineProperty(1, ‘#FFFFFF’);
   *  //원의 외곽 라인이 하얀색으로 그려진다.
   */
    ['setLineProperty'](_0x48d7c8, _0xf45817) {
        _0x48d7c8 = _0x48d7c8 ?? 0x0, _0xf45817 = _0xf45817 ?? '#000000', (this.#lineWidth != _0x48d7c8 || this.#lineColor != _0xf45817) && (this.#lineWidth = _0x48d7c8, this.#lineColor = _0xf45817, this['setUpdateFlag']());
    }
    ['isHit'](_0x4784e2) {
        if (!this['getLayer']())
            return ![];
        const _0x146efc = this['getMapCoord'](), _0xee8b2b = _0x146efc['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0xee8b2b) == ![])
            return ![];
        return logi['maps']['BoundaryChecker']['pointInCirlce'](_0x4784e2, this.#screenCoord['center'], this.#radius);
    }
    ['isOverlap'](_0x179c7b) {
        if (!this['getLayer']())
            return ![];
        const _0x4acc2c = this['getMapCoord'](), _0x4eb790 = _0x4acc2c['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x4eb790) == ![])
            return ![];
        return logi['maps']['BoundaryChecker']['regionOnCirlce'](_0x179c7b, this.#screenCoord['center'], this.#radius);
    }
    ['drawCanvas']() {
        if (!this['getLayer']()) {
            this.#screenCoord['center'] = {
                'x': 0x0,
                'y': 0x0
            };
            return;
        }
        const _0x2f0b00 = this['getMapCoord'](), _0x10c4ab = _0x2f0b00['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x10c4ab) == ![]) {
            this.#screenCoord['center'] = {
                'x': 0x0,
                'y': 0x0
            };
            return;
        }
        const _0x26dbe6 = this['getGfx2d'](), _0x4946a6 = this['getDevicePixelRatio']();
        this.#screenCoord['center'] = _0x2f0b00['world2screen'](this.#center['lng'], this.#center['lat']);
        let _0x5e37ef = this.#radius;
        this.#radiusUnit == 'meter' && (_0x5e37ef = _0x2f0b00['meter2pixel'](this.#radius)), _0x26dbe6['save'](), _0x26dbe6['scale'](_0x4946a6, _0x4946a6), _0x26dbe6['drawObjCircle'](this.#screenCoord['center']['x'], this.#screenCoord['center']['y'], _0x5e37ef, this.#fillColor, this.#lineWidth, this.#lineColor), _0x26dbe6['restore']();
    }
};
export default logi['maps']['Cirlce'];