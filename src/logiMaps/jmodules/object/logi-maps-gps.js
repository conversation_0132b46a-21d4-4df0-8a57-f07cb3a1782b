import a2_0x4028da from '../common/logi-maps-defines.js?v=2.1.10.1';
import a2_0x30b06d from '../common/logi-maps-types.js?v=2.1.10.1';
import a2_0x4ba034 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a2_0x691ff0 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a2_0x124b32 from '../utility/logi-maps-boundarychecker.js?v=2.1.10.1';
import a2_0x4fbf5f from '../object/logi-maps-object.js?v=2.1.10.1';
import a2_0x3f2214 from '../object/logi-maps-line.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a2_0x4028da, logi['maps']['LINETYPE'] = a2_0x30b06d['LINETYPE'], logi['maps']['Utils'] = a2_0x4ba034, logi['maps']['BoundaryData'] = a2_0x691ff0, logi['maps']['BoundaryChecker'] = a2_0x124b32, logi['maps']['Object'] = a2_0x4fbf5f, logi['maps']['Line'] = a2_0x3f2214, logi['maps']['Gps'] = class extends logi['maps']['Object'] {
    #rawLatLngs = new Array();
    #rawAttr = {};
    #rawVisible = !![];
    #matchedLatLngs = new Array();
    #matchedAttr = {};
    #matchedVisible = !![];
    #relAttr = {};
    #relVisible = !![];
    #screenCoord = {
        'baseLayer': null,
        'tileLevel': null,
        'tileLevelOffset': null,
        'origin': {
            'x': 0x0,
            'y': 0x0
        },
        'raw': {
            'width': 0x0,
            'points': new Array()
        },
        'matched': {
            'width': 0x0,
            'points': new Array()
        },
        'rel': { 'width': 0x0 }
    };
    /**
   * @preserve .
   * @constructor
   * @description
   *  로그 정보를 생성한다.
   * @param {logi.maps.LatLng[]} rawLatLngs Raw GPS 좌표 배열(WGS84)
   * @param {logi.maps.LatLng[]} matchedLatLngs Matched GPS 좌표 배열(WGS84)
   * @param {Object} options option
   *  @param {String} options.key GPS key (default: random 생성)
   *  @param {String} options.class GPS class (CSS의 class와 비슷함)
   *  @param {Number} options.zIndex 그리기 순서 (default: 0)
   *  @param {Object} options.rawAttr Raw GPS 속성
   *   @param {Number} options.rawAttr.width Raw GPS 경로 넓이
   *   @param {String} options.rawAttr.color Raw GPS 경로 색
   *   @param {Number} options.rawAttr.strokeWidth Raw GPS 경로 스트로크 넓이
   *   @param {String} options.rawAttr.strokeColor Raw GPS 경로 스트로크 색
   *   @param {Number} options.rawAttr.dotRadius Raw GPS 점 반지름
   *   @param {String} options.rawAttr.dotColor Raw GPS 점 색
   *  @param {Object} options.matchedAttr Matched GPS 속성
   *   @param {Number} options.matchedAttr.width Matched GPS 경로 넓이
   *   @param {String} options.matchedAttr.color Matched GPS 경로 색
   *   @param {Number} options.matchedAttr.strokeWidth Matched GPS 경로 스트로크 넓이
   *   @param {String} options.matchedAttr.strokeColor Matched GPS 경로 스트로크 색
   *   @param {Number} options.matchedAttr.dotRadius Matched GPS 점 반지름
   *   @param {String} options.matchedAttr.dotColor Matched GPS 점 색
   *  @param {Object} options.relAttr Raw&Matched 연결 속성
   *   @param {Number} options.relAttr.width Raw&Matched 연결 경로 넓이
   *   @param {String} options.relAttr.color Raw&Matched 연결 경로 색
   *   @param {Number} options.relAttr.strokeWidth Raw&Matched 연결 경로 스트로크 넓이
   *   @param {String} options.relAttr.strokeColor Raw&Matched 연결 경로 스트로크 색
   *  @param {logi.maps.Map} options.map 표시될 Map
   * @example
   *  let gps = new logi.maps.Gps(
   *   [{lat: 37.5062379, lng: 127.0050378}, {lat: 37.566596, lng: 127.007702}, {lat: 37.5251644, lng: 126.9255491}, {lat: 37.5125585, lng: 127.1025353}, {lat: 37.563692, lng: 126.9822107}, {lat: 37.5173108, lng: 126.9033793}],
   *   [{lat: 37.5062379, lng: 127.0050378}, {lat: 37.566596, lng: 127.007702}, {lat: 37.5251644, lng: 126.9255491}, {lat: 37.5125585, lng: 127.1025353}, {lat: 37.563692, lng: 126.9822107}, {lat: 37.5173108, lng: 126.9033793}],
   *   {
   *    rawAttr: {width: 4, color: '#0088FF', strokeWidth: 1, strokeColor: '#000000', dotRadius: 5, strokeColor: '#963522'},
   *    matchedAttr: {width: 4, color: '#999999', strokeWidth: 1, strokeColor: '#959595', dotRadius: 5, strokeColor: '#8B00FF'},
   *    map: logiMap
   *   }
   *  );
   *  //GPS를 그린다.
   */
    constructor(_0x1f38a6, _0x512f37, _0x22c353) {
        const _0x1240ca = _0x22c353?.['key'] ?? 'gp_' + Math['random']()['toString'](0x24)['slice'](-0x8), _0xa931ad = _0x22c353?.['class'] ?? '', _0x3b5508 = _0x22c353?.['zIndex'] ?? 0x0;
        super(_0x1240ca, logi['maps']['Object']['OBJTYPE']['gps'], _0xa931ad, _0x3b5508), this.#setRoughGps(_0x1f38a6, _0x512f37), this.#rawAttr['width'] = _0x22c353?.['rawAttr']?.['width'] ?? 0x4, this.#rawAttr['color'] = _0x22c353?.['rawAttr']?.['color'] ?? '#96352288', this.#rawAttr['strokeWidth'] = _0x22c353?.['rawAttr']?.['strokeWidth'] ?? 0x0, this.#rawAttr['strokeColor'] = _0x22c353?.['rawAttr']?.['strokeColor'] ?? '#000000', this.#rawAttr['dotRadius'] = _0x22c353?.['rawAttr']?.['dotRadius'] ?? 0x5, this.#rawAttr['dotColor'] = _0x22c353?.['rawAttr']?.['dotColor'] ?? '#963522', this.#matchedAttr['width'] = _0x22c353?.['matchedAttr']?.['width'] ?? 0x10, this.#matchedAttr['color'] = _0x22c353?.['matchedAttr']?.['color'] ?? '#8B00FF88', this.#matchedAttr['strokeWidth'] = _0x22c353?.['matchedAttr']?.['strokeWidth'] ?? 0x0, this.#matchedAttr['strokeColor'] = _0x22c353?.['matchedAttr']?.['strokeColor'] ?? '#000000', this.#matchedAttr['dotRadius'] = _0x22c353?.['matchedAttr']?.['dotRadius'] ?? 0x5, this.#matchedAttr['dotColor'] = _0x22c353?.['matchedAttr']?.['dotColor'] ?? '#8B00FF', this.#relAttr['width'] = _0x22c353?.['relAttr']?.['width'] ?? 0x1, this.#relAttr['color'] = _0x22c353?.['relAttr']?.['color'] ?? '#444444', this.#relAttr['strokeWidth'] = _0x22c353?.['relAttr']?.['strokeWidth'] ?? 0x0, this.#relAttr['strokeColor'] = _0x22c353?.['relAttr']?.['strokeColor'] ?? '#959595', this['setMap'](_0x22c353?.['map']);
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Raw GPS 속성 정보를 변경한다.
   * @param {Number} width Raw GPS 경로 넓이
   * @param {String} color Raw GPS 경로 색
   * @param {Number} strokeWidth Raw GPS 경로 스트로크 넓이
   * @param {String} strokeColor Raw GPS경로 스트로크 색
   * @param {Number} dotRadius Raw GPS 점 반지름
   * @param {String} dotColor Raw GPS 점 색
   * @example
   *  gps.setRawProperty(4, ‘#FFFFFF’, 1, ‘#000000’, 5, #963522');
   *  //Raw GPS 속성 정보가 변경된다.
   */
    ['setRawProperty'](_0xce8adc, _0x431ac0, _0x16d1a0, _0x5c1174, _0x4e88bf, _0x1e3f4a) {
        let _0x4191d1 = ![], _0x2b1672 = null;
        _0x2b1672 = _0xce8adc ?? 0x0, this.#rawAttr['width'] != _0x2b1672 && (this.#rawAttr['width'] = _0x2b1672, _0x4191d1 = !![]), _0x2b1672 = _0x431ac0 ?? '#000000', this.#rawAttr['color'] != _0x2b1672 && (this.#rawAttr['color'] = _0x2b1672, _0x4191d1 = !![]), _0x2b1672 = _0x16d1a0 ?? 0x0, this.#rawAttr['strokeWidth'] != _0x2b1672 && (this.#rawAttr['strokeWidth'] = _0x2b1672, _0x4191d1 = !![]), _0x2b1672 = _0x5c1174 ?? '#000000', this.#rawAttr['strokeColor'] != _0x2b1672 && (this.#rawAttr['strokeColor'] = _0x2b1672, _0x4191d1 = !![]), _0x2b1672 = _0x4e88bf ?? 0x0, this.#rawAttr['dotRadius'] != _0x2b1672 && (this.#rawAttr['dotRadius'] = _0x2b1672, _0x4191d1 = !![]), _0x2b1672 = _0x1e3f4a ?? '#000000', this.#rawAttr['dotColor'] != _0x2b1672 && (this.#rawAttr['dotColor'] = _0x2b1672, _0x4191d1 = !![]), _0x4191d1 == !![] && this['setUpdateFlag']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Matched GPS 속성 정보를 변경한다.
   * @param {Number} width Matched GPS 경로 넓이
   * @param {String} color Matched GPS 경로 색
   * @param {Number} strokeWidth Matched GPS 경로 스트로크 넓이
   * @param {String} strokeColor Matched GPS 경로 스트로크 색
   * @param {Number} dotRadius Matched GPS 점 반지름
   * @param {String} dotColor Matched GPS 점 색
   * @example
   *  gps.setMatchedProperty(4, ‘#999999’, 1, ‘#888888’, 5, '#8B00FF');
   *  //Matched GPS 속성 정보가 변경된다.
   */
    ['setMatchedProperty'](_0x1eb925, _0x3bb4f7, _0x77e5d3, _0x39c410, _0x356d5d, _0x3a72ca) {
        let _0x28d7bd = ![], _0x14c808 = null;
        _0x14c808 = _0x1eb925 ?? 0x0, this.#matchedAttr['width'] != _0x14c808 && (this.#matchedAttr['width'] = _0x14c808, _0x28d7bd = !![]), _0x14c808 = _0x3bb4f7 ?? '#000000', this.#matchedAttr['color'] != _0x14c808 && (this.#matchedAttr['color'] = _0x14c808, _0x28d7bd = !![]), _0x14c808 = _0x77e5d3 ?? 0x0, this.#matchedAttr['strokeWidth'] != _0x14c808 && (this.#matchedAttr['strokeWidth'] = _0x14c808, _0x28d7bd = !![]), _0x14c808 = _0x39c410 ?? '#000000', this.#matchedAttr['strokeColor'] != _0x14c808 && (this.#matchedAttr['strokeColor'] = _0x14c808, _0x28d7bd = !![]), _0x14c808 = _0x356d5d ?? 0x0, this.#matchedAttr['dotRadius'] != _0x14c808 && (this.#matchedAttr['dotRadius'] = _0x14c808, _0x28d7bd = !![]), _0x14c808 = _0x3a72ca ?? '#000000', this.#matchedAttr['dotColor'] != _0x14c808 && (this.#matchedAttr['dotColor'] = _0x14c808, _0x28d7bd = !![]), _0x28d7bd == !![] && this['setUpdateFlag']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Raw&Matched 연결 속성 정보를 변경한다.
   * @param {Number} width Raw&Matched 연결 선 넓이
   * @param {String} color Raw&Matched 연결 선 색
   * @param {Number} strokeWidth Raw&Matched 연결 선 스트로크 넓이
   * @param {String} strokeColor Raw&Matched 연결 선 스트로크 색
   * @example
   *  gps.setRelProperty(4, ‘#999999’, 1, ‘#888888’);
   *  //Raw&Matched 연결 속성 정보가 변경된다.
   */
    ['setRelProperty'](_0x1df792, _0x50b1ba, _0x35ced7, _0x283a50) {
        let _0x5a0925 = ![], _0x1daf5a = null;
        _0x1daf5a = _0x1df792 ?? 0x0, this.#relAttr['width'] != _0x1daf5a && (this.#relAttr['width'] = _0x1daf5a, _0x5a0925 = !![]), _0x1daf5a = _0x50b1ba ?? '#000000', this.#relAttr['color'] != _0x1daf5a && (this.#relAttr['color'] = _0x1daf5a, _0x5a0925 = !![]), _0x1daf5a = _0x35ced7 ?? 0x0, this.#relAttr['strokeWidth'] != _0x1daf5a && (this.#relAttr['strokeWidth'] = _0x1daf5a, _0x5a0925 = !![]), _0x1daf5a = _0x283a50 ?? '#000000', this.#relAttr['strokeColor'] != _0x1daf5a && (this.#relAttr['strokeColor'] = _0x1daf5a, _0x5a0925 = !![]), _0x5a0925 == !![] && this['setUpdateFlag']();
    }
    ['setRawVisible'](_0x433b1d) {
        this.#rawVisible != _0x433b1d && (this.#rawVisible = _0x433b1d, this['setUpdateFlag']());
    }
    ['setMatchedVisible'](_0x5ef0b4) {
        this.#matchedVisible != _0x5ef0b4 && (this.#matchedVisible = _0x5ef0b4, this['setUpdateFlag']());
    }
    ['setRelVisible'](_0x4952c2) {
        this.#relVisible != _0x4952c2 && (this.#relVisible = _0x4952c2, this['setUpdateFlag']());
    }
    ['isHit']() {
        return ![];
    }
    ['isOverlap']() {
        return ![];
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Raw&Matched GPS 좌표를 설정한다.
   * @param {logi.maps.LatLng[]} rawLatLngs Raw GPS 좌표 배열
   * @param {logi.maps.LatLng[]} matchedLatLngs Matched GPS 좌표 배열
   * @example
   *  gps.setGPS([{lat: 37.5062379, lng: 127.0050378}, {lat: 37.566596, lng: 127.007702}, {lat: 37.5251644, lng: 126.9255491}],
   *  [{lat: 37.5062379, lng: 127.0050378}, {lat: 37.566596, lng: 127.007702}, {lat: 37.5251644, lng: 126.9255491}]);
   *  //Raw&Matched GPS 좌표가 설정된다.
   */
    ['setGps'](_0xb31ae1, _0x2cdbff) {
        this.#setRoughGps(_0xb31ae1, _0x2cdbff), this.#screenCoord = {
            'baseLayer': null,
            'tileLevel': null,
            'tileLevelOffset': null,
            'origin': {
                'x': 0x0,
                'y': 0x0
            },
            'raw': {
                'width': 0x0,
                'points': new Array()
            },
            'matched': {
                'width': 0x0,
                'points': new Array()
            },
            'rel': { 'width': 0x0 }
        }, this['setUpdateFlag']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  Raw&Matched GPS 좌표를 추가한다.
   * @param logi.maps.LatLng rawLatLng Raw GPS 좌표
   * @param logi.maps.LatLng matchedLatLng Matched GPS 좌표
   * @example
   *  gps.setMatched([{lat: 37.5115557, lng: 127.0595261}, {lat: 37.5062379, lng: 127.0050378}]);
   *  //Raw&Matched GPS 좌표가 추가된다.
   */
    ['addGps'](_0x2fe1e1, _0x59612a) {
        this.#addRoughGps(_0x2fe1e1, _0x59612a), this.#screenCoord = {
            'baseLayer': null,
            'tileLevel': null,
            'tileLevelOffset': null,
            'origin': {
                'x': 0x0,
                'y': 0x0
            },
            'raw': {
                'width': 0x0,
                'points': new Array()
            },
            'matched': {
                'width': 0x0,
                'points': new Array()
            },
            'rel': { 'width': 0x0 }
        }, this['setUpdateFlag']();
    }
    ['drawCanvas']() {
        if (!this['getLayer']()) {
            this.#screenCoord['baseLayer'] != null && (this.#screenCoord = {
                'baseLayer': null,
                'tileLevel': null,
                'tileLevelOffset': null,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'raw': {
                    'width': 0x0,
                    'points': new Array()
                },
                'matched': {
                    'width': 0x0,
                    'points': new Array()
                },
                'rel': { 'width': 0x0 }
            });
            return;
        }
        const _0x31b1f3 = this['getMapCoord'](), _0x4d9a12 = _0x31b1f3['getLevel']();
        if (this['getVisible']() == ![] || this['checkRenderRange'](_0x4d9a12) == ![]) {
            this.#screenCoord['baseLayer'] != null && (this.#screenCoord = {
                'baseLayer': null,
                'tileLevel': null,
                'tileLevelOffset': null,
                'origin': {
                    'x': 0x0,
                    'y': 0x0
                },
                'raw': {
                    'width': 0x0,
                    'points': new Array()
                },
                'matched': {
                    'width': 0x0,
                    'points': new Array()
                },
                'rel': { 'width': 0x0 }
            });
            return;
        }
        const _0x3f623d = this['getGfx2d'](), _0x5ad3ed = this['getDevicePixelRatio'](), _0x154829 = _0x31b1f3['getTileLevelOffset'](), _0x2074ab = this.#rawLatLngs['length'] <= this.#matchedLatLngs['length'] ? this.#rawLatLngs['length'] : this.#matchedLatLngs['length'];
        if (this.#screenCoord['baseLayer'] != this['getLayer']() || this.#screenCoord['tileLevel'] != _0x31b1f3['getLevel']() || this.#screenCoord['tileLevelOffset'] != _0x154829) {
            this.#screenCoord['baseLayer'] = this['getLayer'](), this.#screenCoord['tileLevel'] = _0x31b1f3['getLevel'](), this.#screenCoord['tileLevelOffset'] = _0x154829;
            {
                this.#screenCoord['origin'] = {
                    'x': 0x0,
                    'y': 0x0
                }, this.#screenCoord['raw']['width'] = 0x0, this.#screenCoord['raw']['points'] = new Array(), this.#screenCoord['matched']['width'] = 0x0, this.#screenCoord['matched']['points'] = new Array(), this.#screenCoord['rel']['width'] = 0x0;
                if (_0x2074ab > 0x0) {
                    this.#screenCoord['origin'] = _0x31b1f3['world2screen'](this.#matchedLatLngs[0x0]['lng'], this.#matchedLatLngs[0x0]['lat']);
                    let _0x8e96a0 = {
                            'x': null,
                            'y': null
                        }, _0x5b2744 = {
                            'x': null,
                            'y': null
                        }, _0x2ba593 = {
                            'x': null,
                            'y': null
                        }, _0xd9218e = {
                            'x': null,
                            'y': null
                        };
                    for (let _0x149881 = 0x0; _0x149881 < _0x2074ab; ++_0x149881) {
                        _0x8e96a0 = _0x31b1f3['world2screen'](this.#rawLatLngs[_0x149881]['lng'], this.#rawLatLngs[_0x149881]['lat']), _0x8e96a0['x'] = _0x8e96a0['x'] - this.#screenCoord['origin']['x'], _0x8e96a0['y'] = _0x8e96a0['y'] - this.#screenCoord['origin']['y'], _0x2ba593 = _0x31b1f3['world2screen'](this.#matchedLatLngs[_0x149881]['lng'], this.#matchedLatLngs[_0x149881]['lat']), _0x2ba593['x'] = _0x2ba593['x'] - this.#screenCoord['origin']['x'], _0x2ba593['y'] = _0x2ba593['y'] - this.#screenCoord['origin']['y'], (_0x5b2744['x'] != _0x8e96a0['x'] || _0x5b2744['y'] != _0x8e96a0['y'] || _0xd9218e['x'] != _0x2ba593['x'] || _0xd9218e['y'] != _0x2ba593['y']) && (this.#screenCoord['raw']['points']['push'](_0x8e96a0), this.#screenCoord['matched']['points']['push'](_0x2ba593), _0x5b2744['x'] = _0x8e96a0['x'], _0x5b2744['y'] = _0x8e96a0['y'], _0xd9218e['x'] = _0x2ba593['x'], _0xd9218e['y'] = _0x2ba593['y']);
                    }
                }
            }
        }
        if (_0x2074ab > 0x0) {
            this.#screenCoord['origin'] = _0x31b1f3['world2screen'](this.#matchedLatLngs[0x0]['lng'], this.#matchedLatLngs[0x0]['lat']);
            if (this.#rawVisible && this.#screenCoord['raw']['points']['length'] >= 0x2) {
                _0x3f623d['save'](), _0x3f623d['scale'](_0x5ad3ed, _0x5ad3ed), _0x3f623d['translate'](this.#screenCoord['origin']['x'], this.#screenCoord['origin']['y']);
                const _0x4452f5 = this.#rawAttr['width'];
                this.#screenCoord['raw']['width'] = _0x4452f5;
                if (this.#rawAttr['strokeWidth'] > 0x0) {
                    const _0x4b9f34 = _0x4452f5 + this.#rawAttr['strokeWidth'] * 0x2;
                    this.#screenCoord['raw']['width'] = _0x4b9f34, _0x3f623d['drawObjPolyLine'](this.#screenCoord['raw']['points'], _0x4b9f34, this.#rawAttr['strokeColor']);
                }
                _0x3f623d['drawObjPolyLine'](this.#screenCoord['raw']['points'], _0x4452f5, this.#rawAttr['color']), _0x3f623d['restore']();
            }
            if (this.#matchedVisible && this.#screenCoord['matched']['points']['length'] >= 0x2) {
                _0x3f623d['save'](), _0x3f623d['scale'](_0x5ad3ed, _0x5ad3ed), _0x3f623d['translate'](this.#screenCoord['origin']['x'], this.#screenCoord['origin']['y']);
                const _0x1d21e6 = this.#matchedAttr['width'];
                this.#screenCoord['matched']['width'] = _0x1d21e6;
                if (this.#matchedAttr['strokeWidth'] > 0x0) {
                    const _0x139989 = _0x1d21e6 + this.#matchedAttr['strokeWidth'] * 0x2;
                    this.#screenCoord['matched']['width'] = _0x139989, _0x3f623d['drawObjPolyLine'](this.#screenCoord['matched']['points'], _0x139989, this.#matchedAttr['strokeColor']);
                }
                _0x3f623d['drawObjPolyLine'](this.#screenCoord['matched']['points'], _0x1d21e6, this.#matchedAttr['color']), _0x3f623d['restore']();
            }
            if (this.#screenCoord['raw']['points']['length'] > 0x0 || this.#screenCoord['matched']['points']['length'] > 0x0) {
                _0x3f623d['save'](), _0x3f623d['scale'](_0x5ad3ed, _0x5ad3ed), _0x3f623d['translate'](this.#screenCoord['origin']['x'], this.#screenCoord['origin']['y']);
                if (this.#relVisible) {
                    const _0x4a0414 = this.#relAttr['width'];
                    this.#screenCoord['rel']['width'] = _0x4a0414;
                    if (this.#relAttr['strokeWidth'] > 0x0) {
                        const _0x1306c0 = _0x4a0414 + this.#relAttr['strokeWidth'] * 0x2;
                        this.#screenCoord['rel']['width'] = _0x1306c0, _0x3f623d['drawObjLines'](this.#screenCoord['raw']['points'], this.#screenCoord['matched']['points'], _0x1306c0, this.#relAttr['strokeColor']);
                    }
                    _0x3f623d['drawObjLines'](this.#screenCoord['raw']['points'], this.#screenCoord['matched']['points'], _0x4a0414, this.#relAttr['color']);
                }
                if (this.#rawVisible)
                    for (let _0x37617e of this.#screenCoord['raw']['points']) {
                        _0x3f623d['drawObjDot'](_0x37617e['x'], _0x37617e['y'], this.#rawAttr['dotRadius'], this.#rawAttr['dotColor']);
                    }
                if (this.#matchedVisible)
                    for (let _0x10ca91 of this.#screenCoord['matched']['points']) {
                        _0x3f623d['drawObjDot'](_0x10ca91['x'], _0x10ca91['y'], this.#matchedAttr['dotRadius'], this.#matchedAttr['dotColor']);
                    }
                _0x3f623d['restore']();
            }
        }
    }
    #setRoughGps(_0x1764f1, _0x1bb273, _0xb26001 = 0x4) {
        this.#rawLatLngs = [], this.#matchedLatLngs = [];
        let _0x55b183 = null, _0x52e3ea = null, _0x56d997 = {
                'x': null,
                'y': null
            }, _0xb61e56 = {
                'x': null,
                'y': null
            }, _0x264216 = {
                'x': null,
                'y': null
            }, _0x32df20 = {
                'x': null,
                'y': null
            };
        if (_0x1764f1['length'] > 0x0 && _0x1bb273['length'] > 0x0) {
            _0x55b183 = _0x1764f1[0x0], _0x52e3ea = _0x1bb273[0x0], this.#rawLatLngs['push']({
                'lng': _0x55b183['lng'],
                'lat': _0x55b183['lat']
            }), this.#matchedLatLngs['push']({
                'lng': _0x52e3ea['lng'],
                'lat': _0x52e3ea['lat']
            }), _0xb61e56['x'] = parseInt(logi['maps']['Utils']['longitude2TileX'](_0x55b183['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W']), _0xb61e56['y'] = parseInt(logi['maps']['Utils']['latitude2TileY'](_0x55b183['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H']), _0x32df20['x'] = parseInt(logi['maps']['Utils']['longitude2TileX'](_0x52e3ea['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W']), _0x32df20['y'] = parseInt(logi['maps']['Utils']['latitude2TileY'](_0x52e3ea['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H']);
            const _0x578abc = _0x1764f1['length'] > _0x1bb273['length'] ? _0x1764f1['length'] : _0x1bb273['length'];
            for (let _0x530fd0 = 0x1; _0x530fd0 < _0x578abc; ++_0x530fd0) {
                _0x55b183 = _0x530fd0 < _0x1764f1['length'] ? _0x1764f1[_0x530fd0] : _0x1764f1[_0x1764f1['length'] - 0x1], _0x52e3ea = _0x530fd0 < _0x1bb273['length'] ? _0x1bb273[_0x530fd0] : _0x1bb273[_0x1bb273['length'] - 0x1], _0x56d997['x'] = parseInt(logi['maps']['Utils']['longitude2TileX'](_0x55b183['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W']), _0x56d997['y'] = parseInt(logi['maps']['Utils']['latitude2TileY'](_0x55b183['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H']), _0x264216['x'] = parseInt(logi['maps']['Utils']['longitude2TileX'](_0x52e3ea['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W']), _0x264216['y'] = parseInt(logi['maps']['Utils']['latitude2TileY'](_0x52e3ea['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H']);
                const _0x3e6b96 = Math['sqrt'](Math['pow'](_0xb61e56['x'] - _0x56d997['x'], 0x2) + Math['pow'](_0xb61e56['y'] - _0x56d997['y'], 0x2)), _0x81eaeb = Math['sqrt'](Math['pow'](_0x32df20['x'] - _0x264216['x'], 0x2) + Math['pow'](_0x32df20['y'] - _0x264216['y'], 0x2));
                (_0x3e6b96 >= _0xb26001 || _0x81eaeb >= _0xb26001) && (this.#rawLatLngs['push']({
                    'lng': _0x55b183['lng'],
                    'lat': _0x55b183['lat']
                }), this.#matchedLatLngs['push']({
                    'lng': _0x52e3ea['lng'],
                    'lat': _0x52e3ea['lat']
                }), _0xb61e56['x'] = _0x56d997['x'], _0xb61e56['y'] = _0x56d997['y'], _0x32df20['x'] = _0x264216['x'], _0x32df20['y'] = _0x264216['y']);
            }
        }
    }
    #addRoughGps(_0x33730d, _0x5a8fcb, _0xb716d2 = 0x4) {
        let _0x4fd9ac = {
                'x': null,
                'y': null
            }, _0x5478fe = {
                'x': null,
                'y': null
            }, _0x91b683 = {
                'x': null,
                'y': null
            }, _0x826e4d = {
                'x': null,
                'y': null
            };
        const _0x5d84e8 = this.#rawLatLngs['length'] <= this.#matchedLatLngs['length'] ? this.#rawLatLngs['length'] : this.#matchedLatLngs['length'];
        if (_0x5d84e8 == 0x0)
            this.#rawLatLngs['push']({
                'lng': _0x33730d['lng'],
                'lat': _0x33730d['lat']
            }), this.#matchedLatLngs['push']({
                'lng': _0x5a8fcb['lng'],
                'lat': _0x5a8fcb['lat']
            });
        else {
            _0x5478fe['x'] = parseInt(logi['maps']['Utils']['longitude2TileX'](this.#rawLatLngs[this.#rawLatLngs['length'] - 0x1]['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W']), _0x5478fe['y'] = parseInt(logi['maps']['Utils']['latitude2TileY'](this.#rawLatLngs[this.#rawLatLngs['length'] - 0x1]['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H']), _0x826e4d['x'] = parseInt(logi['maps']['Utils']['longitude2TileX'](this.#matchedLatLngs[this.#matchedLatLngs['length'] - 0x1]['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W']), _0x826e4d['y'] = parseInt(logi['maps']['Utils']['latitude2TileY'](this.#matchedLatLngs[this.#matchedLatLngs['length'] - 0x1]['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H']), _0x4fd9ac['x'] = parseInt(logi['maps']['Utils']['longitude2TileX'](_0x33730d['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W']), _0x4fd9ac['y'] = parseInt(logi['maps']['Utils']['latitude2TileY'](_0x33730d['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H']), _0x91b683['x'] = parseInt(logi['maps']['Utils']['longitude2TileX'](_0x5a8fcb['lng'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_W']), _0x91b683['y'] = parseInt(logi['maps']['Utils']['latitude2TileY'](_0x5a8fcb['lat'], logi['maps']['Defines']['MAX_LEVEL']) * logi['maps']['Defines']['TILE_H']);
            const _0x3db1ad = Math['sqrt'](Math['pow'](_0x5478fe['x'] - _0x4fd9ac['x'], 0x2) + Math['pow'](_0x5478fe['y'] - _0x4fd9ac['y'], 0x2)), _0x119ae1 = Math['sqrt'](Math['pow'](_0x826e4d['x'] - _0x91b683['x'], 0x2) + Math['pow'](_0x826e4d['y'] - _0x91b683['y'], 0x2));
            (_0x3db1ad >= _0xb716d2 || _0x119ae1 >= _0xb716d2) && (this.#rawLatLngs['push']({
                'lng': _0x33730d['lng'],
                'lat': _0x33730d['lat']
            }), this.#matchedLatLngs['push']({
                'lng': _0x5a8fcb['lng'],
                'lat': _0x5a8fcb['lat']
            }));
        }
    }
};
export default logi['maps']['Gps'];