import a10_0x240379 from '../common/logi-maps-defines.js?v=2.1.10.1';
import a10_0x237b06 from '../common/logi-maps-types.js?v=2.1.10.1';
import a10_0x806c80 from '../resource/logi-maps-resource.js?v=2.1.10.1';
import a10_0x50d4c9 from '../utility/logi-maps-utils.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a10_0x240379, logi['maps']['ALIGN'] = a10_0x237b06['ALIGN'], logi['maps']['Resource'] = a10_0x806c80, logi['maps']['Utils'] = a10_0x50d4c9, logi['maps']['TextInfo'] = class {
    #_owner;
    #_offsetX;
    #_offsetY;
    #_text;
    #_textItems;
    #_fontFamily;
    #_textBold;
    #_textColor;
    #_fontSize;
    #_textAlign;
    #_bgImgSrc;
    #_bgImgAlign;
    #_bgImgOffsetX;
    #_bgImgOffsetY;
    #_bgImage = null;
    /**
   * @preserve .
   * @constructor
   * @description
   *  Block member variable access using setter/getter
   */
    constructor(_0x46762e, _0x3df11b, _0x318fcf, _0x190d8a, _0x21aae7, _0x5ac532, _0x48b318, _0x4a5a43, _0x4342eb, _0x107456, _0x388237) {
        this.#_owner = _0x46762e, this['offsetX'] = _0x3df11b ?? 0x0, this['offsetY'] = _0x318fcf ?? 0x0, this['text'] = _0x190d8a ?? '', this['fontFamily'] = '', this['textBold'] = ![], this['textColor'] = _0x21aae7 ?? '#000000', this['fontSize'] = _0x5ac532 ?? 0x12, this['textAlign'] = _0x48b318 ?? logi['maps']['ALIGN']['CM'], this['bgImgSrc'] = _0x4a5a43 ?? '', this['bgImgAlign'] = _0x4342eb ?? logi['maps']['ALIGN']['CM'], this['bgImgOffsetX'] = _0x107456 ?? 0x0, this['bgImgOffsetY'] = _0x388237 ?? 0x0, this.#_owner?.['setUpdateFlag']();
    }
    /**
   * @preserve .
   * @method
   * @description
   *  offsetX을 설정한다.
   * @param {Number} offsetX offsetX
   * @example
   *  textInfo.offsetX = 0;
   *  //offsetX 값이 0으로 설정된다.
   */
    set ['offsetX'](_0x53124c) {
        _0x53124c = _0x53124c ?? 0x0, this.#_offsetX !== _0x53124c && (this.#_offsetX = _0x53124c, this.#_owner?.['setUpdateFlag']());
    }
    get ['offsetX']() {
        return this.#_offsetX;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  offsetY를 설정한다.
   * @param {Number} offsetY offsetY
   * @example
   *  textInfo.offsetY = 0;
   *  //offsetY 값이 0으로 설정된다.
   */
    set ['offsetY'](_0x5cc457) {
        _0x5cc457 = _0x5cc457 ?? 0x0, this.#_offsetY !== _0x5cc457 && (this.#_offsetY = _0x5cc457, this.#_owner?.['setUpdateFlag']());
    }
    get ['offsetY']() {
        return this.#_offsetY;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  text를 설정한다.
   * @param {String} text text
   * @example
   *  textInfo.text = 'test';
   *  //text 값이 'test'로 설정된다.
   */
    set ['text'](_0x445e37) {
        _0x445e37 = _0x445e37 ?? '', this.#_text !== _0x445e37 && (this.#_text = _0x445e37, this.#_textItems = this.#_text['toString']()['split']('\x0a'), this.#_owner?.['setUpdateFlag']());
    }
    get ['text']() {
        return this.#_text;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  fontFamily를 설정한다.
   * @param {String} fontFamily fontFamily
   * @example
   *  textInfo.fontFamily = 'NotoSansKR-Bold-Hestia';
   *  //fontFamily 값이 'NotoSansKR-Bold-Hestia'로 설정된다.
   */
    set ['fontFamily'](_0x5a134a) {
        _0x5a134a = _0x5a134a ?? '', this.#_fontFamily !== _0x5a134a && (this.#_fontFamily = _0x5a134a, this.#_owner?.['setUpdateFlag']());
    }
    get ['fontFamily']() {
        return this.#_fontFamily;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  글자 bold를 설정한다.
   * @param {Boolean} textBold 글자 bold
   * @example
   *  textInfo.textBold = true;
   *  //글자 bold가 활성화된다.
   */
    set ['textBold'](_0x288426) {
        _0x288426 = _0x288426 ?? ![], this.#_textBold !== _0x288426 && (this.#_textBold = _0x288426, this.#_owner?.['setUpdateFlag']());
    }
    get ['textBold']() {
        return this.#_textBold;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  글자 색상을 설정한다.
   * @param {String} textColor 글자색
   * @example
   *  textInfo.textColor = ‘blue’;
   *  //글자가 blue 색상으로 그려진다.
   */
    set ['textColor'](_0x1aa0cb) {
        _0x1aa0cb = _0x1aa0cb ?? '#000000', this.#_textColor !== _0x1aa0cb && (this.#_textColor = _0x1aa0cb, this.#_owner?.['setUpdateFlag']());
    }
    get ['textColor']() {
        return this.#_textColor;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  글자 색상을 설정한다.
   * @param {String} textColor 글자색
   * @example
   *  textInfo.textColor = ‘blue’;
   *  //글자가 blue 색상으로 그려진다.
   */
    set ['fontSize'](_0x523498) {
        _0x523498 = _0x523498 ?? 0x12, this.#_fontSize !== _0x523498 && (this.#_fontSize = _0x523498, this.#_owner?.['setUpdateFlag']());
    }
    get ['fontSize']() {
        return this.#_fontSize;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  글자 정렬을 설정한다.
   * @param {ALIGN} textAlign 글자정렬
   * @example
   *  textInfo.textAlign = logi.maps.ALIGN.CM;
   *  //글자가 중앙 정렬로 작성된다.
   */
    set ['textAlign'](_0x4aa344) {
        _0x4aa344 = _0x4aa344 ?? logi['maps']['ALIGN']['CM'], this.#_textAlign !== _0x4aa344 && (this.#_textAlign = _0x4aa344, this.#_owner?.['setUpdateFlag']());
    }
    get ['textAlign']() {
        return this.#_textAlign;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  배경 이미지를 설정한다.
   * @param {String} bgImgSrc 배경 이미지 경로
   * @example
   *  textInfo.bgImgSrc = '/img/bg.png';
   *  //지정된 배경 이미지가 그려진다.
   */
    set ['bgImgSrc'](_0x27e484) {
        this.#_bgImgSrc = _0x27e484 ?? '', this.#_bgImgSrc == '' ? (this.#_bgImage = null, this.#_owner['setUpdateFlag']()) : logi['maps']['Resource']['getImage'](this.#_bgImgSrc)['then'](_0xe17d4c => {
            this.#_bgImage = _0xe17d4c, this.#_owner['setUpdateFlag']();
        })['catch'](_0x2af743 => {
            this.#_bgImage = null, this.#_owner['setUpdateFlag'](), _0x2af743 && console['log'](_0x2af743);
        });
    }
    get ['bgImgSrc']() {
        return this.#_bgImgSrc;
    }
    /**
   * @preserve .
   * @method
   * @deprecated
   * 'bgImg' was declared deprecated. (>> bgImgSrc)
   */
    set ['bgImg'](_0x18b090) {
        this['bgImgSrc'] = _0x18b090;
    }
    /**
   * @preserve .
   * @method
   * @deprecated
   * 'bgImg' was declared deprecated. (>> bgImgSrc)
   */
    get ['bgImg']() {
        return this['bgImgSrc'];
    }
    /**
   * @preserve .
   * @method
   * @description
   *  배경 이미지 정렬을 설정한다.
   * @param {ALIGN} bgImgAlign 배경 이미지 정렬
   * @example
   *  textInfo.bgImgAlign = logi.maps.ALIGN.CM;
   *  //배경 이미지가 중앙 정렬로 그려진다.
   */
    set ['bgImgAlign'](_0x496573) {
        _0x496573 = _0x496573 ?? logi['maps']['ALIGN']['CM'], this.#_bgImgAlign !== _0x496573 && (this.#_bgImgAlign = _0x496573, this.#_owner?.['setUpdateFlag']());
    }
    get ['bgImgAlign']() {
        return this.#_bgImgAlign;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  배경 이미지 위치의 offset을 설정한다.
   * @param {Number} bgImgOffsetX 배경 이미지 offsetX
   * @example
   *  textInfo.bgImgOffsetX = 10;
   *  //배경 이미지의 위치가 offsetX(10) 만큼 이동된다.
   */
    set ['bgImgOffsetX'](_0x2219c4) {
        _0x2219c4 = _0x2219c4 ?? 0x0, this.#_bgImgOffsetX !== _0x2219c4 && (this.#_bgImgOffsetX = _0x2219c4, this.#_owner?.['setUpdateFlag']());
    }
    get ['bgImgOffsetX']() {
        return this.#_bgImgOffsetX;
    }
    /**
   * @preserve .
   * @method
   * @description
   *  배경 이미지 위치의 offset을 설정한다.
   * @param {Number} bgImgOffsetY 배경 이미지 offsetY
   * @example
   *  textInfo.bgImgOffsetY = 10;
   *  //배경 이미지의 위치가 offsetY(10) 만큼 이동된다.
   */
    set ['bgImgOffsetY'](_0x530545) {
        _0x530545 = _0x530545 ?? 0x0, this.#_bgImgOffsetY !== _0x530545 && (this.#_bgImgOffsetY = _0x530545, this.#_owner?.['setUpdateFlag']());
    }
    get ['bgImgOffsetY']() {
        return this.#_bgImgOffsetY;
    }
    ['drawCanvas'](_0x3c9db0, _0x48de51) {
        if (this.#_text !== '') {
            const _0x111ada = this.#_owner?.['getGfx2d']();
            if (_0x111ada) {
                const _0x3b5703 = {
                    'x': _0x3c9db0 + this.#_offsetX,
                    'y': _0x48de51 + this.#_offsetY
                };
                if (this.#_bgImage) {
                    let _0x22f77f = {
                        'x': _0x3b5703['x'] + this.#_bgImgOffsetX,
                        'y': _0x3b5703['y'] + this.#_bgImgOffsetY
                    };
                    _0x22f77f = logi['maps']['Utils']['getAlignPosition'](_0x22f77f['x'], _0x22f77f['y'], this.#_bgImgAlign, this.#_bgImage['naturalWidth'], this.#_bgImage['naturalHeight']), _0x111ada['drawImage'](this.#_bgImage, _0x22f77f['x'], _0x22f77f['y']);
                }
                const _0x3c9b37 = this.#_fontSize * 1.2;
                let _0x1777a4 = 0x0;
                this.#_textItems['length'] >= 0x2 && ((this.#_textAlign == logi['maps']['ALIGN']['LM'] || this.#_textAlign == logi['maps']['ALIGN']['CM'] || this.#_textAlign == logi['maps']['ALIGN']['RM']) && (_0x1777a4 = (this.#_textItems['length'] - 0x1) * _0x3c9b37 * -0.5), (this.#_textAlign == logi['maps']['ALIGN']['LB'] || this.#_textAlign == logi['maps']['ALIGN']['CB'] || this.#_textAlign == logi['maps']['ALIGN']['RB']) && (_0x1777a4 = (this.#_textItems['length'] - 0x1) * _0x3c9b37 * -0x1)), this.#_textItems['forEach'](_0x1fcf4f => {
                    _0x111ada['drawObjText'](_0x1fcf4f, _0x3b5703['x'], _0x3b5703['y'] + _0x1777a4, this.#_fontFamily, this.#_fontSize, this.#_textBold, this.#_textColor, this.#_textAlign), _0x1777a4 += _0x3c9b37;
                });
            }
        }
    }
};
export default logi['maps']['TextInfo'];