var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['GfxCanvas'] = class {
    #canvas = null;
    #type = '2d';
    constructor(_0x4c878f, _0x5a18ea) {
        this.#canvas = _0x4c878f, this.#type = _0x5a18ea;
    }
    ['getCanvas']() {
        return this.#canvas;
    }
    ['getType']() {
        return this.#type;
    }
    get ['width']() {
        return this.#canvas['width'];
    }
    set ['width'](_0x568d9c) {
        this.#canvas['width'] = _0x568d9c;
    }
    get ['height']() {
        return this.#canvas['height'];
    }
    set ['height'](_0x38b3b4) {
        this.#canvas['height'] = _0x38b3b4;
    }
    ['dispatchEvent'](..._0x50a900) {
        this.#canvas['dispatchEvent'](..._0x50a900);
    }
    ['addEventListener'](..._0x2e8a54) {
        this.#canvas['addEventListener'](..._0x2e8a54);
    }
    ['removeEventListener'](..._0x3b66b2) {
        this.#canvas['removeEventListener'](..._0x3b66b2);
    }
    async ['convertToBlob'](_0x296983) {
        if (this.#canvas instanceof OffscreenCanvas)
            return await this.#canvas['convertToBlob'](_0x296983);
        else
            throw new Error('This\x20canvas\x20is\x20not\x20OffscreenCanvas.');
    }
};
export default logi['maps']['GfxCanvas'];