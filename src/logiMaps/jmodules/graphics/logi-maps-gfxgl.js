import a2_0x5568ad from './logi-maps-gfxcanvas.js?v=2.1.10.1';
import a2_0x38210 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a2_0xaa3707 from '../utility/logi-maps-glmath.js?v=2.1.10.1';
import a2_0x386f58 from '../library/earcut/earcut.mjs?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['GfxCanvas'] = a2_0x5568ad, logi['maps']['Utils'] = a2_0x38210, logi['maps']['GlMath'] = a2_0xaa3707;
const _earcut = a2_0x386f58['default'] ? a2_0x386f58['default'] : a2_0x386f58;
logi['maps']['Gfxgl'] = class extends logi['maps']['GfxCanvas'] {
    #gl = null;
    #programInfo;
    #devicePixelRatio = 0x1;
    #origin = {
        'x': 0x0,
        'y': 0x0,
        'z': 0x0
    };
    #modelMatrix;
    #viewMatrix;
    #projectionMatrix;
    #viewMatrixStack = [];
    #bufferCache = {
        'purgeIdx': 0x0,
        'vertex': new Map(),
        'index': new Map(),
        'texture': new Map()
    };
    #vsSource = '\x0a\x20\x20\x20\x20\x20\x20\x20\x20attribute\x20vec4\x20aVertexPosition;\x0a\x20\x20\x20\x20\x20\x20\x20\x20attribute\x20vec2\x20aTexCoord;\x0a\x20\x20\x20\x20\x20\x20\x20\x20uniform\x20mat4\x20uModelViewMatrix;\x0a\x20\x20\x20\x20\x20\x20\x20\x20uniform\x20mat4\x20uProjectionMatrix;\x0a\x20\x20\x20\x20\x20\x20\x20\x20varying\x20vec2\x20vTexCoord;\x0a\x20\x20\x20\x20\x20\x20\x20\x20void\x20main()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20vTexCoord\x20=\x20aTexCoord;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20gl_Position\x20=\x20uProjectionMatrix\x20*\x20uModelViewMatrix\x20*\x20aVertexPosition;\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20';
    #fsSource = '\x0a\x20\x20\x20\x20\x20\x20\x20\x20precision\x20mediump\x20float;\x0a\x20\x20\x20\x20\x20\x20\x20\x20uniform\x20bool\x20uEnableTex;\x0a\x20\x20\x20\x20\x20\x20\x20\x20uniform\x20bool\x20uFontMode;\x0a\x20\x20\x20\x20\x20\x20\x20\x20uniform\x20lowp\x20vec4\x20uColor;\x0a\x20\x20\x20\x20\x20\x20\x20\x20uniform\x20sampler2D\x20uImage;\x0a\x20\x20\x20\x20\x20\x20\x20\x20varying\x20vec2\x20vTexCoord;\x0a\x20\x20\x20\x20\x20\x20\x20\x20void\x20main()\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if(uEnableTex)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20gl_FragColor\x20=\x20texture2D(uImage,\x20vTexCoord);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20gl_FragColor\x20=\x20uColor;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if(uFontMode)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if(gl_FragColor.a\x20<\x200.4)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20discard;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//gl_FragColor.a\x20=\x20min(gl_FragColor.a\x20*\x201.25,\x201.0);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20';
    static ['MeshBuffer'] = class {
        ['vertex'] = null;
        ['isRefVertex'] = ![];
        ['index'] = null;
        ['isRefIndex'] = ![];
        ['indexCount'] = 0x0;
        ['texture'] = null;
        ['isRefTexture'] = ![];
    };
    static ['CacheBuffer'] = class {
        ['purgeIdx'] = 0x0;
        ['buffer'] = null;
    };
    constructor(_0x3cfdf1) {
        super(_0x3cfdf1, 'webgl'), this.#gl = _0x3cfdf1['getContext']('webgl', {
            'antialias': !![],
            'preserveDrawingBuffer': !![]
        });
        if (!this.#gl) {
            console['log']('[logi.maps]\x20webgl:\x20not\x20supported');
            return;
        }
        const _0x1c8a33 = this.#gl;
        _0x1c8a33['enable'](_0x1c8a33['CULL_FACE']), _0x1c8a33['frontFace'](_0x1c8a33['CCW']), _0x1c8a33['enable'](_0x1c8a33['DEPTH_TEST']), _0x1c8a33['depthFunc'](_0x1c8a33['LEQUAL']), _0x1c8a33['enable'](_0x1c8a33['BLEND']), _0x1c8a33['blendFunc'](_0x1c8a33['SRC_ALPHA'], _0x1c8a33['ONE_MINUS_SRC_ALPHA']), this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#viewMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#projectionMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this['setViewport'](this['width'], this['height']), this.#initShader();
    }
    ['getCtx']() {
        return this.#gl;
    }
    ['setDevicePixelRatio'](_0x54adf0) {
        this.#devicePixelRatio = _0x54adf0;
    }
    ['setViewport'](_0xd9cb45, _0x1120d9) {
        const _0x25d048 = this.#gl;
        this['width'] = _0xd9cb45, this['height'] = _0x1120d9, this.#projectionMatrix = logi['maps']['GlMath']['orthographic'](0x0, _0xd9cb45, _0x1120d9, 0x0, -0x1, 0x1), _0x25d048['viewport'](0x0, 0x0, _0xd9cb45, _0x1120d9);
    }
    ['deleteBuffers'](_0x444dd9) {
        for (const _0x592459 in _0x444dd9) {
            const _0x4af32b = _0x444dd9[_0x592459];
            if (Array['isArray'](_0x4af32b))
                for (const _0x2b1e86 of _0x4af32b) {
                    this['deleteBuffer'](_0x2b1e86);
                }
            else
                this['deleteBuffer'](_0x4af32b);
        }
    }
    ['deleteBuffer'](_0x2dbd22) {
        const _0x4d262e = this.#gl;
        _0x2dbd22?.['vertex'] && (!_0x2dbd22?.['isRefVertex'] && _0x4d262e['deleteBuffer'](_0x2dbd22['vertex']), _0x2dbd22['vertex'] = null), _0x2dbd22?.['index'] && (!_0x2dbd22?.['isRefIndex'] && _0x4d262e['deleteBuffer'](_0x2dbd22['index']), _0x2dbd22['index'] = null), _0x2dbd22?.['texture'] && (!_0x2dbd22?.['isRefTexture'] && _0x4d262e['deleteTexture'](_0x2dbd22['texture']), _0x2dbd22['texture'] = null);
    }
    ['readyBufferCache']() {
        this.#bufferCache['purgeIdx'] >= 0xe10 ? this.#bufferCache['purgeIdx'] = 0x0 : this.#bufferCache['purgeIdx'] += 0x1;
    }
    ['purgeBufferCache']() {
        const _0x3399d9 = this.#gl;
        for (const [_0x47dce8, _0x2f56d6] of this.#bufferCache['vertex']) {
            this.#bufferCache['purgeIdx'] != _0x2f56d6['purgeIdx'] && (_0x3399d9['deleteBuffer'](_0x2f56d6['buffer']), this.#bufferCache['vertex']['delete'](_0x47dce8));
        }
        for (const [_0x5da9e6, _0x2d851f] of this.#bufferCache['index']) {
            this.#bufferCache['purgeIdx'] != _0x2d851f['purgeIdx'] && (_0x3399d9['deleteBuffer'](_0x2d851f['buffer']), this.#bufferCache['index']['delete'](_0x5da9e6));
        }
        for (const [_0x19f197, _0x4c973d] of this.#bufferCache['texture']) {
            this.#bufferCache['purgeIdx'] != _0x4c973d['purgeIdx'] && (_0x3399d9['deleteTexture'](_0x4c973d['buffer']), this.#bufferCache['texture']['delete'](_0x19f197));
        }
    }
    ['clearColor'](_0x5df5d5) {
        const _0x327b24 = this.#gl;
        _0x5df5d5 ? (_0x327b24['clearColor'](_0x5df5d5['fltValue']['r'], _0x5df5d5['fltValue']['g'], _0x5df5d5['fltValue']['b'], _0x5df5d5['fltValue']['a']), _0x327b24['clear'](_0x327b24['DEPTH_BUFFER_BIT'] | _0x327b24['COLOR_BUFFER_BIT']), _0x327b24['uniformMatrix4fv'](this.#programInfo['uniforms']['projectionMatrix'], ![], this.#projectionMatrix)) : (_0x327b24['clearColor'](0x1, 0x1, 0x1, 0x0), _0x327b24['clear'](_0x327b24['DEPTH_BUFFER_BIT'] | _0x327b24['COLOR_BUFFER_BIT']), _0x327b24['uniformMatrix4fv'](this.#programInfo['uniforms']['projectionMatrix'], ![], this.#projectionMatrix));
    }
    ['reset']() {
        this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#viewMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#viewMatrixStack = [];
    }
    ['save']() {
        this.#viewMatrixStack['push'](structuredClone(this.#viewMatrix));
    }
    ['restore']() {
        this.#viewMatrixStack['length'] > 0x0 && (this.#viewMatrix = this.#viewMatrixStack['pop']());
    }
    ['translate'](_0x23bd6b, _0x4cee8e) {
        this.#viewMatrix = logi['maps']['GlMath']['translateMatrix'](this.#viewMatrix, _0x23bd6b, _0x4cee8e, 0x0);
    }
    ['rotate'](_0x43c330) {
        this.#viewMatrix = logi['maps']['GlMath']['rotateMatrix'](this.#viewMatrix, 'z', _0x43c330 * (Math['PI'] / 0xb4));
    }
    ['scale'](_0x1854e7, _0x1caead) {
        this.#viewMatrix = logi['maps']['GlMath']['scaleMatrix'](this.#viewMatrix, _0x1854e7, _0x1caead, 0x1);
    }
    ['setOrigin'](_0x33c63a, _0x198d2c, _0x28cd8d = 0x0) {
        this.#origin['x'] = _0x33c63a, this.#origin['y'] = _0x198d2c, this.#origin['z'] = _0x28cd8d;
    }
    ['setBlendMode'](_0x3318d7, _0x46ed0d) {
        const _0x48b55b = this.#gl, _0x256855 = _0x48b55b[_0x3318d7], _0x352e5d = _0x48b55b[_0x46ed0d];
        _0x256855 && _0x352e5d ? (_0x48b55b['enable'](_0x48b55b['BLEND']), _0x48b55b['blendFunc'](_0x256855, _0x352e5d)) : (_0x48b55b['disable'](_0x48b55b['BLEND']), _0x48b55b['blendFunc'](_0x48b55b['SRC_ALPHA'], _0x48b55b['ONE_MINUS_SRC_ALPHA']));
    }
    ['drawImage'](_0x1034d4, ..._0x2e1102) {
        if (_0x1034d4['image']) {
            const _0x5a6b8e = _0x1034d4['image'];
            !_0x1034d4['meshBuffers']['image'] && (_0x1034d4['meshBuffers']['image'] = new logi['maps']['Gfxgl']['MeshBuffer']());
            if (_0x2e1102['length'] === 0x2) {
                const [_0x9be548, _0x1ae1aa] = _0x2e1102;
                this.#createImageBuffer(_0x1034d4['meshBuffers']['image'], _0x5a6b8e, _0x9be548, _0x1ae1aa, _0x5a6b8e['naturalWidth'], _0x5a6b8e['naturalHeight']);
            } else {
                if (_0x2e1102['length'] === 0x4) {
                    const [_0x1b6231, _0x521516, _0x4ad87a, _0x3b1dd3] = _0x2e1102;
                    this.#createImageBuffer(_0x1034d4['meshBuffers']['image'], _0x5a6b8e, _0x1b6231, _0x521516, _0x4ad87a, _0x3b1dd3);
                } else
                    this.#createImageBuffer(_0x1034d4['meshBuffers']['image'], _0x5a6b8e, 0x0, 0x0, _0x5a6b8e['naturalWidth'], _0x5a6b8e['naturalHeight']);
            }
            this.#drawImageBuffer(_0x1034d4['meshBuffers']['image']);
        }
    }
    ['bindSvgDtmBuffer'](_0x13d0c2, _0x3d9b6e, _0x491640) {
        if (_0x13d0c2['image']) {
            !_0x13d0c2['meshBuffers']['image'] && (_0x13d0c2['meshBuffers']['image'] = new logi['maps']['Gfxgl']['MeshBuffer']());
            const _0x3dfc67 = _0x13d0c2['meshBuffers']['image'];
            this.#bindSvgDtmBuffer(_0x3dfc67, _0x13d0c2['image'], _0x3d9b6e, _0x491640);
        }
    }
    ['bindSvgPolygonBuffers'](_0x81d25e) {
        for (const _0x560129 of _0x81d25e['elemDatas']) {
            !_0x560129['meshBuffers']['polygon'] && (_0x560129['meshBuffers']['polygon'] = new logi['maps']['Gfxgl']['MeshBuffer']());
            const _0x5b5e54 = _0x560129['meshBuffers']['polygon'];
            this.#bindSvgPolygonBuffer(_0x5b5e54, _0x560129['geometry']);
        }
        const _0x2507dc = _0x81d25e['layerStyle']?.['fillStyle'] == 'stroke-fill' ? 0x2 : 0x1;
        for (const _0x45493b of _0x81d25e['elemDatas']) {
            const _0x4203e0 = _0x45493b['style'];
            if (_0x4203e0['stroke'] > 0x0) {
                !_0x45493b['meshBuffers']['stroke'] && (_0x45493b['meshBuffers']['stroke'] = new logi['maps']['Gfxgl']['MeshBuffer']());
                const _0x2e1a50 = _0x45493b['meshBuffers']['stroke'];
                this.#bindSvgPolygonStrokeBuffer(_0x2e1a50, _0x45493b['geometry'], _0x4203e0['stroke'] * _0x2507dc);
            }
        }
    }
    ['bindSvgPolyLineBuffers'](_0x3e66a4, _0x3af3b4 = 0x1) {
        let _0x43cbfa = 0x0;
        for (const _0x57e937 of _0x3e66a4['elemDatas']) {
            const _0x1aa394 = _0x57e937['style'];
            if (_0x1aa394['stroke'] > 0x0) {
                _0x43cbfa = _0x1aa394['width'] + _0x1aa394['stroke'] * 0x2;
                _0x1aa394['fixedScale'] == !![] && (_0x43cbfa = _0x43cbfa / _0x1aa394['fixedScale'] ? _0x3af3b4 : 0x1);
                !_0x57e937['meshBuffers']['stroke'] && (_0x57e937['meshBuffers']['stroke'] = new logi['maps']['Gfxgl']['MeshBuffer']());
                const _0x34f5fe = _0x57e937['meshBuffers']['stroke'];
                _0x1aa394['dash'] > 0x0 && _0x1aa394['gap'] > 0x0 ? this.#bindSvgDashedLineBuffer(_0x34f5fe, _0x57e937['geometry'], _0x43cbfa, _0x1aa394['dash'], _0x1aa394['gap']) : this.#bindSvgPolyLineBuffer(_0x34f5fe, _0x57e937['geometry'], _0x43cbfa);
            }
        }
        for (const _0x250e78 of _0x3e66a4['elemDatas']) {
            const _0x4286be = _0x250e78['style'];
            _0x43cbfa = _0x4286be['width'];
            _0x4286be['fixedScale'] == !![] && (_0x43cbfa = _0x43cbfa / _0x4286be['fixedScale'] ? _0x3af3b4 : 0x1);
            !_0x250e78['meshBuffers']['polyline'] && (_0x250e78['meshBuffers']['polyline'] = new logi['maps']['Gfxgl']['MeshBuffer']());
            const _0x43971b = _0x250e78['meshBuffers']['polyline'];
            _0x4286be['dash'] > 0x0 && _0x4286be['gap'] > 0x0 ? this.#bindSvgDashedLineBuffer(_0x43971b, _0x250e78['geometry'], _0x43cbfa, _0x4286be['dash'], _0x4286be['gap']) : this.#bindSvgPolyLineBuffer(_0x43971b, _0x250e78['geometry'], _0x43cbfa);
        }
    }
    ['bindSvgSymbolBuffers'](_0x39a822) {
        for (const _0x3375dc of _0x39a822['elemDatas']) {
            const _0x1a84dc = _0x3375dc['style'];
            if (_0x3375dc['symbol']) {
                !_0x3375dc['meshBuffers']['image'] && (_0x3375dc['meshBuffers']['image'] = new logi['maps']['Gfxgl']['MeshBuffer']());
                const _0x37c089 = _0x3375dc['meshBuffers']['image'], _0x4bbf50 = _0x3375dc['symbol'];
                this.#bindSvgSymbolImageBuffer(_0x37c089, _0x4bbf50, _0x1a84dc, _0x4bbf50['naturalWidth'], _0x4bbf50['naturalHeight']);
            }
            if (_0x3375dc['geometry']['texts'] && _0x1a84dc['fontSize'] > 0x0) {
                if (!_0x3375dc['meshBuffers']['texts']) {
                    _0x3375dc['meshBuffers']['texts'] = new Array();
                    for (let _0x501fe5 = 0x0; _0x501fe5 < _0x3375dc['geometry']['texts']['length']; ++_0x501fe5) {
                        _0x3375dc['meshBuffers']['texts']['push'](new logi['maps']['Gfxgl']['MeshBuffer']());
                    }
                }
                for (let _0x186786 = 0x0; _0x186786 < _0x3375dc['geometry']['texts']['length']; ++_0x186786) {
                    const _0x5ccee9 = _0x3375dc['meshBuffers']['texts'][_0x186786], _0x1bc9af = _0x3375dc['geometry']['texts'][_0x186786];
                    this.#bindSvgSymbolTextBuffer(_0x5ccee9, _0x1bc9af, _0x39a822['fontFamily'], _0x1a84dc['fontSize'], _0x1a84dc['bold'], _0x1a84dc['textColorData']['hexString'], _0x1a84dc['textAlign'], _0x1a84dc['stroke'], _0x1a84dc['strokeColorData']['hexString']);
                }
            }
        }
    }
    ['bindSvgTextBuffers'](_0x4011d8) {
        for (const _0xd761cc of _0x4011d8['elemDatas']) {
            const _0x3bbd9a = _0xd761cc['style'];
            if (_0xd761cc['geometry']['texts'] && _0x3bbd9a['fontSize'] > 0x0) {
                if (!_0xd761cc['meshBuffers']['texts']) {
                    _0xd761cc['meshBuffers']['texts'] = new Array();
                    for (let _0x396890 = 0x0; _0x396890 < _0xd761cc['geometry']['texts']['length']; ++_0x396890) {
                        _0xd761cc['meshBuffers']['texts']['push'](new logi['maps']['Gfxgl']['MeshBuffer']());
                    }
                }
                for (let _0xaa4cd7 = 0x0; _0xaa4cd7 < _0xd761cc['geometry']['texts']['length']; ++_0xaa4cd7) {
                    const _0x44618d = _0xd761cc['meshBuffers']['texts'][_0xaa4cd7], _0x3179a9 = _0xd761cc['geometry']['texts'][_0xaa4cd7];
                    this.#createSvgTextBuffer(_0x44618d, _0x3179a9, _0x4011d8['fontFamily'], _0x3bbd9a['fontSize'], _0x3bbd9a['bold'], _0x3bbd9a['textColorData']['hexString'], _0x3bbd9a['textAlign'], _0x3bbd9a['stroke'], _0x3bbd9a['strokeColorData']['hexString']);
                }
            }
        }
    }
    ['bindSvgCurvedBuffers'](_0x3d5462) {
        for (const _0x2c30c0 of _0x3d5462['elemDatas']) {
            const _0x3dbfdd = _0x2c30c0['style'];
            if (_0x2c30c0['geometry']['texts'] && _0x3dbfdd['fontSize'] > 0x0) {
                if (!_0x2c30c0['meshBuffers']['texts']) {
                    _0x2c30c0['meshBuffers']['texts'] = new Array();
                    for (let _0x1c6dbe = 0x0; _0x1c6dbe < _0x2c30c0['geometry']['texts']['length']; ++_0x1c6dbe) {
                        _0x2c30c0['meshBuffers']['texts']['push'](new logi['maps']['Gfxgl']['MeshBuffer']());
                    }
                }
                for (let _0x277564 = 0x0; _0x277564 < _0x2c30c0['geometry']['texts']['length']; ++_0x277564) {
                    const _0x10770a = _0x2c30c0['meshBuffers']['texts'][_0x277564], _0x55cb8e = _0x2c30c0['geometry']['texts'][_0x277564];
                    this.#bindSvgCurvedBuffer(_0x10770a, _0x55cb8e, _0x3d5462['fontFamily'], _0x3dbfdd['fontSize'], _0x3dbfdd['bold'], _0x3dbfdd['textColorData']['hexString'], _0x3dbfdd['textAlign'], _0x3dbfdd['stroke'], _0x3dbfdd['strokeColorData']['hexString']);
                }
            }
        }
    }
    ['drawSvgDtmBuffer'](_0x26b18b) {
        const _0x31f2cd = this.#gl;
        _0x26b18b['meshBuffers']['image'] && (_0x31f2cd['disable'](_0x31f2cd['DEPTH_TEST']), _0x31f2cd['blendFunc'](_0x31f2cd['DST_COLOR'], _0x31f2cd['SRC_COLOR']), this.#drawSvgDtmBuffer(_0x26b18b['meshBuffers']['image']), _0x31f2cd['blendFunc'](_0x31f2cd['SRC_ALPHA'], _0x31f2cd['ONE_MINUS_SRC_ALPHA']), _0x31f2cd['enable'](_0x31f2cd['DEPTH_TEST']));
    }
    ['drawSvgPolygonBuffers'](_0x4ac46f) {
        if (_0x4ac46f['layerStyle']?.['fillStyle'] == 'fill-stroke') {
            for (const _0x5d8188 of _0x4ac46f['elemDatas']) {
                if (_0x5d8188['meshBuffers']['polygon']) {
                    const _0x4d3395 = _0x5d8188['style'];
                    this.#setOriginZ(_0x4d3395['depthZ']), this.#drawSvgPolygonBuffer(_0x5d8188['meshBuffers']['polygon'], _0x4d3395['fillColorData']['fltValue']);
                }
            }
            for (const _0x2bd87f of _0x4ac46f['elemDatas']) {
                if (_0x2bd87f['meshBuffers']['stroke']) {
                    const _0x4a34cb = _0x2bd87f['style'];
                    this.#setOriginZ(_0x4a34cb['depthZ']), this.#drawSvgPolygonStrokeBuffer(_0x2bd87f['meshBuffers']['stroke'], _0x4a34cb['strokeColorData']['fltValue']);
                }
            }
        } else {
            for (const _0x1fb43d of _0x4ac46f['elemDatas']) {
                if (_0x1fb43d['meshBuffers']['stroke']) {
                    const _0x50caa1 = _0x1fb43d['style'];
                    this.#setOriginZ(_0x50caa1['depthZ']), this.#drawSvgPolygonStrokeBuffer(_0x1fb43d['meshBuffers']['stroke'], _0x50caa1['strokeColorData']['fltValue']);
                }
            }
            for (const _0x1e0441 of _0x4ac46f['elemDatas']) {
                if (_0x1e0441['meshBuffers']['polygon']) {
                    const _0x17dafd = _0x1e0441['style'];
                    this.#setOriginZ(_0x17dafd['depthZ']), this.#drawSvgPolygonBuffer(_0x1e0441['meshBuffers']['polygon'], _0x17dafd['fillColorData']['fltValue']);
                }
            }
        }
    }
    ['drawSvgPolyLineBuffers'](_0x71dac2) {
        for (const _0x43c180 of _0x71dac2['elemDatas']) {
            if (_0x43c180['meshBuffers']['stroke']) {
                const _0x3ac3c5 = _0x43c180['style'];
                this.#setOriginZ(_0x3ac3c5['depthZ']), _0x3ac3c5['dash'] > 0x0 && _0x3ac3c5['gap'] > 0x0 ? this.#drawSvgDashedLineBuffer(_0x43c180['meshBuffers']['stroke'], _0x3ac3c5['strokeColorData']['fltValue']) : this.#drawSvgPolyLineBuffer(_0x43c180['meshBuffers']['stroke'], _0x3ac3c5['strokeColorData']['fltValue']);
            }
        }
        for (const _0x4d0b33 of _0x71dac2['elemDatas']) {
            if (_0x4d0b33['meshBuffers']['polyline']) {
                const _0x1489d5 = _0x4d0b33['style'];
                this.#setOriginZ(_0x1489d5['depthZ']), _0x1489d5['dash'] > 0x0 && _0x1489d5['gap'] > 0x0 ? this.#drawSvgDashedLineBuffer(_0x4d0b33['meshBuffers']['polyline'], _0x1489d5['colorData']['fltValue']) : this.#drawSvgPolyLineBuffer(_0x4d0b33['meshBuffers']['polyline'], _0x1489d5['colorData']['fltValue']);
            }
        }
    }
    ['drawSvgSymbolBuffers'](_0x552277, _0x55f064 = 0x1) {
        for (const _0x24d8ad of _0x552277['elemDatas']) {
            const _0x5167c7 = _0x24d8ad['style'], _0x2022b4 = _0x5167c7['fixedScale'] == !![] ? _0x55f064 : 0x1;
            this['save'](), this.#setOriginZ(_0x5167c7['depthZ']), this['scale'](0x1 / _0x2022b4, 0x1 / _0x2022b4), this.#applyTransform(_0x24d8ad['transforms'], _0x2022b4);
            _0x24d8ad['symbol'] && (this['save'](), this.#applyTransform(_0x24d8ad['geometry']['image']['transforms'], _0x2022b4), this['translate'](_0x24d8ad['geometry']['image']['x'], _0x24d8ad['geometry']['image']['y']), this.#drawSvgSymbolImageBuffer(_0x24d8ad['meshBuffers']['image']), this['restore']());
            if (_0x24d8ad['geometry']['texts'] && _0x5167c7['fontSize'] > 0x0)
                for (let _0x30a7cc = 0x0; _0x30a7cc < _0x24d8ad['geometry']['texts']['length']; ++_0x30a7cc) {
                    const _0xe5b94e = _0x24d8ad['meshBuffers']['texts'][_0x30a7cc], _0x438e5d = _0x24d8ad['geometry']['texts'][_0x30a7cc];
                    this['save'](), this.#applyTransform(_0x438e5d['transforms'], _0x2022b4), this['translate'](_0x438e5d['x'], _0x438e5d['y']), this.#drawSvgSymbolTextBuffer(_0xe5b94e), this['restore']();
                }
            this['restore']();
        }
    }
    ['drawSvgTextBuffers'](_0x30e206, _0x5d14a5 = 0x1) {
        for (const _0x449541 of _0x30e206['elemDatas']) {
            const _0x4845c9 = _0x449541['style'], _0x32535c = _0x4845c9['fixedScale'] == !![] ? _0x5d14a5 : 0x1;
            this['save'](), this.#setOriginZ(_0x4845c9['depthZ']), this['scale'](0x1 / _0x32535c, 0x1 / _0x32535c), this.#applyTransform(_0x449541['transforms'], _0x32535c);
            if (_0x449541['geometry']['texts'] && _0x4845c9['fontSize'] > 0x0)
                for (let _0x3f7fbc = 0x0; _0x3f7fbc < _0x449541['geometry']['texts']['length']; ++_0x3f7fbc) {
                    const _0x592398 = _0x449541['meshBuffers']['texts'][_0x3f7fbc], _0xf66bfb = _0x449541['geometry']['texts'][_0x3f7fbc];
                    for (let _0x261377 = -0x1; _0x261377 <= 0x1; ++_0x261377) {
                        this['save'](), this.#applyTransform(_0xf66bfb['transforms'], _0x32535c), this['translate'](_0xf66bfb['x'] * _0x32535c + _0x261377 * 0.25, _0xf66bfb['y'] * _0x32535c), this.#drawSvgTextBuffer(_0x592398), this['restore']();
                    }
                    for (let _0x2a2405 = -0x1; _0x2a2405 <= 0x1; ++_0x2a2405) {
                        this['save'](), this.#applyTransform(_0xf66bfb['transforms'], _0x32535c), this['translate'](_0xf66bfb['x'] * _0x32535c, _0xf66bfb['y'] * _0x32535c + _0x2a2405 * 0.25), this.#drawSvgTextBuffer(_0x592398), this['restore']();
                    }
                }
            this['restore']();
        }
    }
    ['drawSvgCurvedBuffers'](_0x4e80fe, _0x104f63 = 0x1) {
        for (const _0x3aa285 of _0x4e80fe['elemDatas']) {
            const _0x449861 = _0x3aa285['style'], _0x30d9b1 = _0x449861['fixedScale'] == !![] ? _0x104f63 : 0x1;
            this['save'](), this.#setOriginZ(_0x449861['depthZ']), this['scale'](0x1 / _0x30d9b1, 0x1 / _0x30d9b1), this.#applyTransform(_0x3aa285['transforms'], _0x30d9b1);
            if (_0x3aa285['geometry']['texts'] && _0x449861['fontSize'] > 0x0) {
                const _0x4d5e08 = this.#getRatioCurved(_0x3aa285['geometry']['texts'], _0x30d9b1);
                if (_0x4d5e08)
                    for (let _0x345800 = 0x0; _0x345800 < _0x3aa285['geometry']['texts']['length']; ++_0x345800) {
                        const _0x34206c = _0x3aa285['meshBuffers']['texts'][_0x345800], _0x3c03e5 = _0x4d5e08[_0x345800];
                        this['save'](), this['translate'](_0x3c03e5['x'] * _0x30d9b1, _0x3c03e5['y'] * _0x30d9b1), this['rotate'](_0x3c03e5['angle']), this.#drawSvgCurvedBuffer(_0x34206c), this['restore']();
                    }
            }
            this['restore']();
        }
    }
    ['screenshot']() {
        return new Promise(_0x187276 => {
            const _0x4586c0 = this['getCanvas']()['toDataURL'](), _0x151255 = new Image();
            _0x151255['src'] = _0x4586c0, _0x151255['onload'] = () => {
                _0x187276(_0x151255);
            };
        });
    }
    #initShader() {
        const _0x912a54 = this.#gl, _0xb31d13 = this.#loadShader(_0x912a54['VERTEX_SHADER'], this.#vsSource), _0x457191 = this.#loadShader(_0x912a54['FRAGMENT_SHADER'], this.#fsSource), _0x26305c = _0x912a54['createProgram']();
        _0x912a54['attachShader'](_0x26305c, _0xb31d13), _0x912a54['attachShader'](_0x26305c, _0x457191), _0x912a54['linkProgram'](_0x26305c);
        if (!_0x912a54['getProgramParameter'](_0x26305c, _0x912a54['LINK_STATUS']))
            return console['error']('Unable\x20to\x20initialize\x20the\x20shader\x20program:\x20' + _0x912a54['getProgramInfoLog'](_0x26305c)), null;
        this.#programInfo = {
            'program': _0x26305c,
            'attributes': {
                'vertex': _0x912a54['getAttribLocation'](_0x26305c, 'aVertexPosition'),
                'texCoord': _0x912a54['getAttribLocation'](_0x26305c, 'aTexCoord')
            },
            'uniforms': {
                'modelViewMatrix': _0x912a54['getUniformLocation'](_0x26305c, 'uModelViewMatrix'),
                'projectionMatrix': _0x912a54['getUniformLocation'](_0x26305c, 'uProjectionMatrix'),
                'enableTex': _0x912a54['getUniformLocation'](_0x26305c, 'uEnableTex'),
                'fontMode': _0x912a54['getUniformLocation'](_0x26305c, 'uFontMode'),
                'color': _0x912a54['getUniformLocation'](_0x26305c, 'uColor')
            }
        }, _0x912a54['useProgram'](this.#programInfo['program']);
    }
    #loadShader(_0x3427e0, _0x3bce01) {
        const _0xa299f0 = this.#gl, _0xe0f0b = _0xa299f0['createShader'](_0x3427e0);
        _0xa299f0['shaderSource'](_0xe0f0b, _0x3bce01), _0xa299f0['compileShader'](_0xe0f0b);
        if (!_0xa299f0['getShaderParameter'](_0xe0f0b, _0xa299f0['COMPILE_STATUS']))
            return console['error']('An\x20error\x20occurred\x20compiling\x20the\x20shaders:\x20' + _0xa299f0['getShaderInfoLog'](_0xe0f0b)), _0xa299f0['deleteShader'](_0xe0f0b), null;
        return _0xe0f0b;
    }
    #getVertexBufferCache(_0x1b34bd) {
        const _0x493150 = this.#gl, _0x210af3 = this.#bufferCache['vertex']['has'](_0x1b34bd);
        let _0x29c353;
        return !_0x210af3 && (_0x29c353 = new logi['maps']['Gfxgl']['CacheBuffer'](), _0x29c353['buffer'] = _0x493150['createBuffer'](), this.#bufferCache['vertex']['set'](_0x1b34bd, _0x29c353)), _0x29c353 = this.#bufferCache['vertex']['get'](_0x1b34bd), _0x29c353['purgeIdx'] = this.#bufferCache['purgeIdx'], {
            'cacheKey': _0x210af3,
            'cacheBuffer': _0x29c353
        };
    }
    #getIndexBufferCache(_0x78ff42) {
        const _0x5c7e11 = this.#gl, _0x223958 = this.#bufferCache['index']['has'](_0x78ff42);
        let _0xba263;
        return !_0x223958 && (_0xba263 = new logi['maps']['Gfxgl']['CacheBuffer'](), _0xba263['buffer'] = _0x5c7e11['createBuffer'](), this.#bufferCache['index']['set'](_0x78ff42, _0xba263)), _0xba263 = this.#bufferCache['index']['get'](_0x78ff42), _0xba263['purgeIdx'] = this.#bufferCache['purgeIdx'], {
            'cacheKey': _0x223958,
            'cacheBuffer': _0xba263
        };
    }
    #getTextureBufferCache(_0x1759da) {
        const _0x48a87b = this.#gl, _0x47651d = this.#bufferCache['texture']['has'](_0x1759da);
        let _0x485250;
        return !_0x47651d && (_0x485250 = new logi['maps']['Gfxgl']['CacheBuffer'](), _0x485250['buffer'] = _0x48a87b['createTexture'](), this.#bufferCache['texture']['set'](_0x1759da, _0x485250)), _0x485250 = this.#bufferCache['texture']['get'](_0x1759da), _0x485250['purgeIdx'] = this.#bufferCache['purgeIdx'], {
            'cacheKey': _0x47651d,
            'cacheBuffer': _0x485250
        };
    }
    #setOriginZ(_0x5847c7) {
        this.#origin['z'] = _0x5847c7;
    }
    #createImageBuffer(_0x2729ae, _0x49f8e0, _0x3d6dba, _0x6dc317, _0x22d2ac, _0x1b479d) {
        const _0x18b42a = this.#gl;
        if (!_0x2729ae['vertex']) {
            _0x2729ae['vertex'] = _0x18b42a['createBuffer']();
            const _0x3d28ac = new Float32Array([
                _0x3d6dba,
                _0x6dc317,
                0x0,
                0x0,
                _0x3d6dba,
                _0x1b479d,
                0x0,
                0x1,
                _0x22d2ac,
                _0x6dc317,
                0x1,
                0x0,
                _0x22d2ac,
                _0x1b479d,
                0x1,
                0x1
            ]);
            _0x18b42a['bindBuffer'](_0x18b42a['ARRAY_BUFFER'], _0x2729ae['vertex']), _0x18b42a['bufferData'](_0x18b42a['ARRAY_BUFFER'], _0x3d28ac, _0x18b42a['STATIC_DRAW']);
        }
        if (!_0x2729ae['index']) {
            _0x2729ae['index'] = _0x18b42a['createBuffer']();
            const _0x204f06 = new Uint16Array([
                0x0,
                0x1,
                0x2,
                0x1,
                0x3,
                0x2
            ]);
            _0x18b42a['bindBuffer'](_0x18b42a['ELEMENT_ARRAY_BUFFER'], _0x2729ae['index']), _0x18b42a['bufferData'](_0x18b42a['ELEMENT_ARRAY_BUFFER'], _0x204f06, _0x18b42a['STATIC_DRAW']), _0x2729ae['indexCount'] = _0x204f06['length'];
        }
        !_0x2729ae['texture'] && (_0x2729ae['texture'] = _0x18b42a['createTexture'](), _0x18b42a['bindTexture'](_0x18b42a['TEXTURE_2D'], _0x2729ae['texture']), _0x18b42a['texParameteri'](_0x18b42a['TEXTURE_2D'], _0x18b42a['TEXTURE_MIN_FILTER'], _0x18b42a['LINEAR']), _0x18b42a['texParameteri'](_0x18b42a['TEXTURE_2D'], _0x18b42a['TEXTURE_MAG_FILTER'], _0x18b42a['LINEAR']), _0x18b42a['texParameteri'](_0x18b42a['TEXTURE_2D'], _0x18b42a['TEXTURE_WRAP_S'], _0x18b42a['CLAMP_TO_EDGE']), _0x18b42a['texParameteri'](_0x18b42a['TEXTURE_2D'], _0x18b42a['TEXTURE_WRAP_T'], _0x18b42a['CLAMP_TO_EDGE']), _0x18b42a['texImage2D'](_0x18b42a['TEXTURE_2D'], 0x0, _0x18b42a['LUMINANCE'], _0x18b42a['LUMINANCE'], _0x18b42a['UNSIGNED_BYTE'], _0x49f8e0));
    }
    #drawImageBuffer(_0x51a12c) {
        const _0x143746 = this.#gl;
        this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#modelMatrix = logi['maps']['GlMath']['translateMatrix'](this.#modelMatrix, this.#origin['x'], this.#origin['y'], this.#origin['z']);
        const _0x28f600 = logi['maps']['GlMath']['multiplyMatrix'](this.#modelMatrix, this.#viewMatrix);
        _0x143746['uniformMatrix4fv'](this.#programInfo['uniforms']['modelViewMatrix'], ![], _0x28f600), _0x143746['uniform4f'](this.#programInfo['uniforms']['color'], 0x1, 0x1, 0x1, 0x1), _0x143746['uniform1i'](this.#programInfo['uniforms']['enableTex'], !![]), _0x143746['bindBuffer'](_0x143746['ARRAY_BUFFER'], _0x51a12c['vertex']), _0x143746['vertexAttribPointer'](this.#programInfo['attributes']['vertex'], 0x2, _0x143746['FLOAT'], ![], 0x10, 0x0), _0x143746['vertexAttribPointer'](this.#programInfo['attributes']['texCoord'], 0x2, _0x143746['FLOAT'], ![], 0x10, 0x8), _0x143746['enableVertexAttribArray'](this.#programInfo['attributes']['vertex']), _0x143746['enableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x143746['bindTexture'](_0x143746['TEXTURE_2D'], _0x51a12c['texture']), _0x143746['bindBuffer'](_0x143746['ELEMENT_ARRAY_BUFFER'], _0x51a12c['index']), _0x143746['drawElements'](_0x143746['TRIANGLES'], _0x51a12c['indexCount'], _0x143746['UNSIGNED_SHORT'], 0x0), _0x143746['disableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x143746['uniform1i'](this.#programInfo['uniforms']['enableTex'], ![]);
    }
    #bindSvgDtmBuffer(_0x289dcb, _0x29c059, _0x16fa94, _0x33a252) {
        const _0x41c7c3 = this.#gl;
        {
            const _0x243c22 = 'dtm.' + _0x16fa94 + '.' + _0x33a252, {
                    cacheKey: _0x245c68,
                    cacheBuffer: _0xdd1d2b
                } = this.#getVertexBufferCache(_0x243c22);
            if (!_0x245c68) {
                const _0x4bb24d = new Float32Array([
                    0x0,
                    0x0,
                    0x0,
                    0x0,
                    0x0,
                    _0x33a252,
                    0x0,
                    0x1,
                    _0x16fa94,
                    0x0,
                    0x1,
                    0x0,
                    _0x16fa94,
                    _0x33a252,
                    0x1,
                    0x1
                ]);
                _0x41c7c3['bindBuffer'](_0x41c7c3['ARRAY_BUFFER'], _0xdd1d2b['buffer']), _0x41c7c3['bufferData'](_0x41c7c3['ARRAY_BUFFER'], _0x4bb24d, _0x41c7c3['STATIC_DRAW']);
            }
            _0x289dcb['vertex'] = _0xdd1d2b['buffer'], _0x289dcb['isRefVertex'] = !![];
        }
        {
            const _0x2fb8ed = 'dtm.6', {
                    cacheKey: _0x587d19,
                    cacheBuffer: _0x212fef
                } = this.#getIndexBufferCache(_0x2fb8ed);
            if (!_0x587d19) {
                const _0x5c784 = new Uint16Array([
                    0x0,
                    0x1,
                    0x2,
                    0x1,
                    0x3,
                    0x2
                ]);
                _0x212fef['indexCount'] = _0x5c784['length'], _0x41c7c3['bindBuffer'](_0x41c7c3['ELEMENT_ARRAY_BUFFER'], _0x212fef['buffer']), _0x41c7c3['bufferData'](_0x41c7c3['ELEMENT_ARRAY_BUFFER'], _0x5c784, _0x41c7c3['STATIC_DRAW']);
            }
            _0x289dcb['index'] = _0x212fef['buffer'], _0x289dcb['indexCount'] = _0x212fef['indexCount'], _0x289dcb['isRefIndex'] = !![];
        }
        !_0x289dcb['texture'] && (_0x289dcb['texture'] = _0x41c7c3['createTexture'](), _0x289dcb['isRefTexture'] = ![], _0x41c7c3['bindTexture'](_0x41c7c3['TEXTURE_2D'], _0x289dcb['texture']), _0x41c7c3['texParameteri'](_0x41c7c3['TEXTURE_2D'], _0x41c7c3['TEXTURE_MIN_FILTER'], _0x41c7c3['LINEAR']), _0x41c7c3['texParameteri'](_0x41c7c3['TEXTURE_2D'], _0x41c7c3['TEXTURE_MAG_FILTER'], _0x41c7c3['LINEAR']), _0x41c7c3['texParameteri'](_0x41c7c3['TEXTURE_2D'], _0x41c7c3['TEXTURE_WRAP_S'], _0x41c7c3['CLAMP_TO_EDGE']), _0x41c7c3['texParameteri'](_0x41c7c3['TEXTURE_2D'], _0x41c7c3['TEXTURE_WRAP_T'], _0x41c7c3['CLAMP_TO_EDGE']), _0x41c7c3['texImage2D'](_0x41c7c3['TEXTURE_2D'], 0x0, _0x41c7c3['LUMINANCE'], _0x41c7c3['LUMINANCE'], _0x41c7c3['UNSIGNED_BYTE'], _0x29c059));
    }
    #bindSvgPolygonBuffer(_0x4aeaad, _0x40b7e6) {
        const _0xa6cb8c = this.#gl;
        if (!_0x4aeaad['vertex'] || !_0x4aeaad['index']) {
            const _0x238277 = [], _0x24e77e = [];
            !_0x40b7e6['coords'] && (_0x40b7e6['coords'] = this.#parsePathStringToCoords(_0x40b7e6['pathString']));
            for (const _0x573223 of _0x40b7e6['coords']) {
                const _0x4cd9b9 = _earcut(_0x573223), _0x5df30e = parseInt(_0x238277['length'] * 0.5);
                _0x238277['push'](..._0x573223);
                for (let _0x4abf10 = 0x0; _0x4abf10 < _0x4cd9b9['length']; _0x4abf10 += 0x3) {
                    _0x24e77e['push'](_0x4cd9b9[_0x4abf10 + 0x2] + _0x5df30e), _0x24e77e['push'](_0x4cd9b9[_0x4abf10 + 0x1] + _0x5df30e), _0x24e77e['push'](_0x4cd9b9[_0x4abf10 + 0x0] + _0x5df30e);
                }
            }
            !_0x4aeaad['vertex'] && (_0x4aeaad['vertex'] = _0xa6cb8c['createBuffer']()), _0xa6cb8c['bindBuffer'](_0xa6cb8c['ARRAY_BUFFER'], _0x4aeaad['vertex']), _0xa6cb8c['bufferData'](_0xa6cb8c['ARRAY_BUFFER'], new Float32Array(_0x238277), _0xa6cb8c['STATIC_DRAW']), !_0x4aeaad['index'] && (_0x4aeaad['index'] = _0xa6cb8c['createBuffer']()), _0xa6cb8c['bindBuffer'](_0xa6cb8c['ELEMENT_ARRAY_BUFFER'], _0x4aeaad['index']), _0xa6cb8c['bufferData'](_0xa6cb8c['ELEMENT_ARRAY_BUFFER'], new Uint16Array(_0x24e77e), _0xa6cb8c['STATIC_DRAW']), _0x4aeaad['indexCount'] = _0x24e77e['length'];
        }
    }
    #bindSvgPolygonStrokeBuffer(_0x3cf287, _0x2db65c, _0x37bb7c, _0x17b6c5 = 0x1) {
        const _0x479ad7 = this.#gl;
        _0x37bb7c = parseFloat(_0x37bb7c['toFixed'](_0x17b6c5));
        if (!_0x3cf287['vertex'] || !_0x3cf287['index'] || _0x3cf287['width'] != _0x37bb7c) {
            const _0x26a63e = [], _0x161e12 = [], _0x2a45d2 = _0x37bb7c * 0.5, _0x3cb902 = 0x9;
            let _0x153f85 = 0x0, _0x254199, _0x4d6b80, _0x101a15, _0x3b080f, _0x479de7, _0x24398f;
            !_0x2db65c['coords'] && (_0x2db65c['coords'] = this.#parsePathStringToCoords(_0x2db65c['pathString']));
            for (const _0x174f96 of _0x2db65c['coords']) {
                for (let _0x3aa190 = 0x0; _0x3aa190 < _0x174f96['length'] - 0x3; _0x3aa190 += 0x2) {
                    _0x254199 = _0x174f96[_0x3aa190 + 0x0], _0x4d6b80 = _0x174f96[_0x3aa190 + 0x1], _0x101a15 = _0x174f96[_0x3aa190 + 0x2], _0x3b080f = _0x174f96[_0x3aa190 + 0x3], _0x479de7 = logi['maps']['GlMath']['normalize']({
                        'x': _0x101a15 - _0x254199,
                        'y': _0x3b080f - _0x4d6b80
                    }), _0x479de7 && (_0x24398f = logi['maps']['GlMath']['rotateVector'](_0x479de7, -0x5a), _0x24398f['x'] *= _0x2a45d2, _0x24398f['y'] *= _0x2a45d2, _0x153f85 = parseInt(_0x26a63e['length'] * 0.5), _0x26a63e['push'](_0x254199 + _0x24398f['x'], _0x4d6b80 + _0x24398f['y']), _0x26a63e['push'](_0x101a15 + _0x24398f['x'], _0x3b080f + _0x24398f['y']), _0x26a63e['push'](_0x254199 - _0x24398f['x'], _0x4d6b80 - _0x24398f['y']), _0x26a63e['push'](_0x101a15 - _0x24398f['x'], _0x3b080f - _0x24398f['y']), _0x161e12['push'](_0x153f85 + 0x0, _0x153f85 + 0x2, _0x153f85 + 0x1), _0x161e12['push'](_0x153f85 + 0x1, _0x153f85 + 0x2, _0x153f85 + 0x3), _0x153f85 = parseInt(_0x26a63e['length'] * 0.5), this.#addRoundCap(_0x26a63e, _0x161e12, _0x254199, _0x4d6b80, _0x479de7, _0x37bb7c, _0x3cb902, !![], _0x153f85), _0x153f85 = parseInt(_0x26a63e['length'] * 0.5), this.#addRoundCap(_0x26a63e, _0x161e12, _0x101a15, _0x3b080f, _0x479de7, _0x37bb7c, _0x3cb902, ![], _0x153f85));
                }
            }
            !_0x3cf287['vertex'] && (_0x3cf287['vertex'] = _0x479ad7['createBuffer']()), _0x479ad7['bindBuffer'](_0x479ad7['ARRAY_BUFFER'], _0x3cf287['vertex']), _0x479ad7['bufferData'](_0x479ad7['ARRAY_BUFFER'], new Float32Array(_0x26a63e), _0x479ad7['STATIC_DRAW']), !_0x3cf287['index'] && (_0x3cf287['index'] = _0x479ad7['createBuffer']()), _0x479ad7['bindBuffer'](_0x479ad7['ELEMENT_ARRAY_BUFFER'], _0x3cf287['index']), _0x479ad7['bufferData'](_0x479ad7['ELEMENT_ARRAY_BUFFER'], new Uint16Array(_0x161e12), _0x479ad7['STATIC_DRAW']), _0x3cf287['indexCount'] = _0x161e12['length'], _0x3cf287['width'] = _0x37bb7c;
        }
    }
    #bindSvgPolyLineBuffer(_0xc04b1a, _0x5d8dff, _0x55bc1d, _0x14cb69 = 0x1) {
        const _0x60e45e = this.#gl;
        _0x55bc1d = parseFloat(_0x55bc1d['toFixed'](_0x14cb69));
        if (!_0xc04b1a['vertex'] || !_0xc04b1a['index'] || _0xc04b1a['width'] != _0x55bc1d) {
            const _0x47a3a0 = [], _0x111ee7 = [], _0x1ba088 = _0x55bc1d * 0.5, _0x4bda08 = 0x9;
            let _0x1f8297 = 0x0, _0x58f894, _0x2f47eb, _0xe6da57, _0x4f7329, _0x5b5a60, _0x31f2ae;
            !_0x5d8dff['coords'] && (_0x5d8dff['coords'] = this.#parsePathStringToCoords(_0x5d8dff['pathString']));
            for (const _0x988ba9 of _0x5d8dff['coords']) {
                for (let _0x6cb28d = 0x0; _0x6cb28d < _0x988ba9['length'] - 0x3; _0x6cb28d += 0x2) {
                    _0x58f894 = _0x988ba9[_0x6cb28d + 0x0], _0x2f47eb = _0x988ba9[_0x6cb28d + 0x1], _0xe6da57 = _0x988ba9[_0x6cb28d + 0x2], _0x4f7329 = _0x988ba9[_0x6cb28d + 0x3], _0x5b5a60 = logi['maps']['GlMath']['normalize']({
                        'x': _0xe6da57 - _0x58f894,
                        'y': _0x4f7329 - _0x2f47eb
                    }), _0x5b5a60 && (_0x31f2ae = logi['maps']['GlMath']['rotateVector'](_0x5b5a60, -0x5a), _0x31f2ae['x'] *= _0x1ba088, _0x31f2ae['y'] *= _0x1ba088, _0x1f8297 = parseInt(_0x47a3a0['length'] * 0.5), _0x47a3a0['push'](_0x58f894 + _0x31f2ae['x'], _0x2f47eb + _0x31f2ae['y']), _0x47a3a0['push'](_0xe6da57 + _0x31f2ae['x'], _0x4f7329 + _0x31f2ae['y']), _0x47a3a0['push'](_0x58f894 - _0x31f2ae['x'], _0x2f47eb - _0x31f2ae['y']), _0x47a3a0['push'](_0xe6da57 - _0x31f2ae['x'], _0x4f7329 - _0x31f2ae['y']), _0x111ee7['push'](_0x1f8297 + 0x0, _0x1f8297 + 0x2, _0x1f8297 + 0x1), _0x111ee7['push'](_0x1f8297 + 0x1, _0x1f8297 + 0x2, _0x1f8297 + 0x3), _0x1f8297 = parseInt(_0x47a3a0['length'] * 0.5), this.#addRoundCap(_0x47a3a0, _0x111ee7, _0x58f894, _0x2f47eb, _0x5b5a60, _0x55bc1d, _0x4bda08, !![], _0x1f8297), _0x1f8297 = parseInt(_0x47a3a0['length'] * 0.5), this.#addRoundCap(_0x47a3a0, _0x111ee7, _0xe6da57, _0x4f7329, _0x5b5a60, _0x55bc1d, _0x4bda08, ![], _0x1f8297));
                }
            }
            !_0xc04b1a['vertex'] && (_0xc04b1a['vertex'] = _0x60e45e['createBuffer']()), _0x60e45e['bindBuffer'](_0x60e45e['ARRAY_BUFFER'], _0xc04b1a['vertex']), _0x60e45e['bufferData'](_0x60e45e['ARRAY_BUFFER'], new Float32Array(_0x47a3a0), _0x60e45e['STATIC_DRAW']), !_0xc04b1a['index'] && (_0xc04b1a['index'] = _0x60e45e['createBuffer']()), _0x60e45e['bindBuffer'](_0x60e45e['ELEMENT_ARRAY_BUFFER'], _0xc04b1a['index']), _0x60e45e['bufferData'](_0x60e45e['ELEMENT_ARRAY_BUFFER'], new Uint16Array(_0x111ee7), _0x60e45e['STATIC_DRAW']), _0xc04b1a['indexCount'] = _0x111ee7['length'], _0xc04b1a['width'] = _0x55bc1d;
        }
    }
    #bindSvgDashedLineBuffer(_0x193c0c, _0x77ad10, _0x400c0f, _0x3509b5, _0x221beb, _0x56f2c9 = 0x1) {
        const _0x35be3c = this.#gl;
        _0x400c0f = parseFloat(_0x400c0f['toFixed'](_0x56f2c9));
        if (!_0x193c0c['vertex'] || !_0x193c0c['index'] || _0x193c0c['width'] != _0x400c0f) {
            const _0x5e20a8 = [], _0x3b242e = [], _0x3040a7 = _0x400c0f * 0.5;
            let _0x5bd403 = 0x0, _0x43e2e5, _0x4897ab, _0xef78f8, _0x3a9f5a, _0x581671, _0x399509;
            !_0x77ad10['coords'] && (_0x77ad10['coords'] = this.#parsePathStringToCoords(_0x77ad10['pathString']));
            for (const _0x405ebc of _0x77ad10['coords']) {
                const _0x57cb66 = this.#splitPolylineIntoDashes(_0x405ebc, _0x3509b5, _0x221beb);
                for (const _0x1aff13 of _0x57cb66) {
                    for (let _0x580e17 = 0x0; _0x580e17 < _0x1aff13['length'] - 0x3; _0x580e17 += 0x2) {
                        _0x43e2e5 = _0x1aff13[_0x580e17 + 0x0], _0x4897ab = _0x1aff13[_0x580e17 + 0x1], _0xef78f8 = _0x1aff13[_0x580e17 + 0x2], _0x3a9f5a = _0x1aff13[_0x580e17 + 0x3], _0x581671 = logi['maps']['GlMath']['normalize']({
                            'x': _0xef78f8 - _0x43e2e5,
                            'y': _0x3a9f5a - _0x4897ab
                        }), _0x581671 && (_0x399509 = logi['maps']['GlMath']['rotateVector'](_0x581671, -0x5a), _0x399509['x'] *= _0x3040a7, _0x399509['y'] *= _0x3040a7, _0x5bd403 = parseInt(_0x5e20a8['length'] * 0.5), _0x5e20a8['push'](_0x43e2e5 + _0x399509['x'], _0x4897ab + _0x399509['y']), _0x5e20a8['push'](_0xef78f8 + _0x399509['x'], _0x3a9f5a + _0x399509['y']), _0x5e20a8['push'](_0x43e2e5 - _0x399509['x'], _0x4897ab - _0x399509['y']), _0x5e20a8['push'](_0xef78f8 - _0x399509['x'], _0x3a9f5a - _0x399509['y']), _0x3b242e['push'](_0x5bd403 + 0x0, _0x5bd403 + 0x2, _0x5bd403 + 0x1), _0x3b242e['push'](_0x5bd403 + 0x1, _0x5bd403 + 0x2, _0x5bd403 + 0x3));
                    }
                }
            }
            !_0x193c0c['vertex'] && (_0x193c0c['vertex'] = _0x35be3c['createBuffer']()), _0x35be3c['bindBuffer'](_0x35be3c['ARRAY_BUFFER'], _0x193c0c['vertex']), _0x35be3c['bufferData'](_0x35be3c['ARRAY_BUFFER'], new Float32Array(_0x5e20a8), _0x35be3c['STATIC_DRAW']), !_0x193c0c['index'] && (_0x193c0c['index'] = _0x35be3c['createBuffer']()), _0x35be3c['bindBuffer'](_0x35be3c['ELEMENT_ARRAY_BUFFER'], _0x193c0c['index']), _0x35be3c['bufferData'](_0x35be3c['ELEMENT_ARRAY_BUFFER'], new Uint16Array(_0x3b242e), _0x35be3c['STATIC_DRAW']), _0x193c0c['indexCount'] = _0x3b242e['length'], _0x193c0c['width'] = _0x400c0f;
        }
    }
    #addRoundCap(_0x4e2d22, _0x5f4d18, _0x4c0216, _0x1f5fc8, _0x242081, _0x26c0cb, _0x3bdb6e, _0x244c32, _0x3d8729) {
        const _0x24c9bf = _0x26c0cb * 0.5, _0x1e6bc0 = _0x244c32 == !![] ? -0x5a : 0x5a, _0x47cc10 = 0xb4 / _0x3bdb6e, _0xcd53bd = {
                'x': _0x242081['x'] * _0x24c9bf,
                'y': _0x242081['y'] * _0x24c9bf
            };
        _0x4e2d22['push'](_0x4c0216, _0x1f5fc8);
        for (let _0x4e577e = 0x0; _0x4e577e <= _0x3bdb6e; ++_0x4e577e) {
            const _0x20cdc9 = _0x1e6bc0 + _0x4e577e * -_0x47cc10, _0x5cf5fd = logi['maps']['GlMath']['rotateVector'](_0xcd53bd, _0x20cdc9);
            _0x4e2d22['push'](_0x4c0216 + _0x5cf5fd['x'], _0x1f5fc8 + _0x5cf5fd['y']), _0x4e577e >= 0x1 && _0x5f4d18['push'](_0x3d8729, _0x3d8729 + _0x4e577e, _0x3d8729 + _0x4e577e + 0x1);
        }
    }
    #parsePathStringToCoords(_0xebbc1c) {
        const _0x52907d = _0xebbc1c['match'](/[a-zA-Z][^a-zA-Z]*/g);
        let _0x2fd87a = [], _0x4f7333 = [], _0x5048ae = {
                'x': 0x0,
                'y': 0x0
            }, _0x5cf9fa = {
                'x': 0x0,
                'y': 0x0
            };
        return _0x52907d['forEach'](_0x6baf8 => {
            let _0x456615 = _0x6baf8['trim']();
            const _0x452e17 = _0x456615[0x0], _0x48a3ff = _0x456615['slice'](0x1)['split'](/[ ,]+/)['map'](Number);
            switch (_0x452e17) {
            case 'm':
                _0x4f7333['length'] > 0x0 && (_0x2fd87a['push'](_0x4f7333), _0x4f7333 = []);
                for (let _0x558be0 = 0x0; _0x558be0 < _0x48a3ff['length']; _0x558be0 += 0x2) {
                    _0x5048ae['x'] += _0x48a3ff[_0x558be0 + 0x0], _0x5048ae['y'] += _0x48a3ff[_0x558be0 + 0x1], _0x4f7333['push'](_0x5048ae['x'], _0x5048ae['y']), _0x558be0 == 0x0 && (_0x5cf9fa = { ..._0x5048ae });
                }
                break;
            case 'z':
                _0x4f7333['push'](_0x5cf9fa['x'], _0x5cf9fa['y']), _0x5048ae['x'] = _0x5cf9fa['x'], _0x5048ae['y'] = _0x5cf9fa['y'];
                break;
            default:
                console['log']('Unsupported\x20command:\x20' + _0x452e17);
                break;
            }
        }), _0x4f7333['length'] > 0x0 && (_0x2fd87a['push'](_0x4f7333), _0x4f7333 = []), _0x2fd87a;
    }
    #splitPolylineIntoDashes(_0x4dc92a, _0x34d395, _0x31f692) {
        let _0x1014d0 = [], _0x131c84, _0x543a9b;
        if (_0x4dc92a['length'] < 0x4)
            return _0x1014d0;
        let _0x4dbba0 = 0x0;
        const _0x1bbd90 = new Array();
        _0x1bbd90['push'](0x0);
        const _0x1942ed = {
            'x': 0x0,
            'y': 0x0
        };
        _0x131c84 = [
            _0x4dc92a[0x0],
            _0x4dc92a[0x1]
        ];
        for (let _0x4b609c = 0x2; _0x4b609c < _0x4dc92a['length'] - 0x1; _0x4b609c += 0x2) {
            _0x543a9b = [
                _0x4dc92a[_0x4b609c + 0x0],
                _0x4dc92a[_0x4b609c + 0x1]
            ], _0x1942ed['x'] = _0x131c84[0x0] - _0x543a9b[0x0], _0x1942ed['y'] = _0x131c84[0x1] - _0x543a9b[0x1], _0x4dbba0 += Math['sqrt'](_0x1942ed['x'] * _0x1942ed['x'] + _0x1942ed['y'] * _0x1942ed['y']), _0x1bbd90['push'](_0x4dbba0), _0x131c84 = _0x543a9b;
        }
        if (_0x4dbba0 == 0x0)
            return _0x1014d0;
        let _0x5bdf8e, _0x2bc937, _0x32810c = {
                'x': 0x0,
                'y': 0x0
            }, _0x2e90f8 = {
                'x': 0x0,
                'y': 0x0
            }, _0x4710ea = ![], _0x5cdf15 = [], _0x1a6369 = 0x1, _0x295f94 = !![], _0x430354 = _0x34d395;
        _0x5cdf15['push'](_0x4dc92a[0x0], _0x4dc92a[0x1]);
        for (let _0x6642af = 0x1; _0x6642af < _0x1bbd90['length']; ++_0x6642af) {
            _0x4710ea = ![], _0x5bdf8e = _0x6642af, _0x2bc937 = _0x6642af - 0x1, _0x32810c['x'] = _0x4dc92a[_0x5bdf8e * 0x2 + 0x0], _0x32810c['y'] = _0x4dc92a[_0x5bdf8e * 0x2 + 0x1], _0x2e90f8['x'] = _0x4dc92a[_0x2bc937 * 0x2 + 0x0], _0x2e90f8['y'] = _0x4dc92a[_0x2bc937 * 0x2 + 0x1];
            if (_0x430354 > _0x1bbd90[_0x5bdf8e])
                _0x5cdf15['push'](_0x32810c['x'], _0x32810c['y']);
            else {
                while (_0x430354 <= _0x1bbd90[_0x5bdf8e]) {
                    _0x430354 == _0x1bbd90[_0x5bdf8e] && (_0x4710ea = !![]);
                    _0x1a6369 = (_0x430354 - _0x1bbd90[_0x2bc937]) / (_0x1bbd90[_0x5bdf8e] - _0x1bbd90[_0x2bc937]);
                    const _0x1191d2 = _0x2e90f8['x'] + (_0x32810c['x'] - _0x2e90f8['x']) * _0x1a6369, _0x5091cb = _0x2e90f8['y'] + (_0x32810c['y'] - _0x2e90f8['y']) * _0x1a6369;
                    _0x5cdf15['push'](_0x1191d2, _0x5091cb), _0x295f94 ? (_0x1014d0['push'](_0x5cdf15), _0x5cdf15 = [
                        _0x1191d2,
                        _0x5091cb
                    ], _0x430354 += _0x31f692) : (_0x5cdf15 = [
                        _0x1191d2,
                        _0x5091cb
                    ], _0x430354 += _0x34d395), _0x295f94 = !_0x295f94;
                }
                _0x4710ea == ![] && _0x5cdf15['push'](_0x32810c['x'], _0x32810c['y']);
            }
        }
        return _0x295f94 && _0x5cdf15['length'] >= 0x4 && _0x1014d0['push'](_0x5cdf15), _0x1014d0;
    }
    #bindSvgSymbolImageBuffer(_0x25741e, _0x268531, _0x55be4f, _0x3abc84, _0x39f350) {
        const _0xde8849 = this.#gl;
        {
            const _0xdb0628 = 'sym.' + _0x3abc84 + '.' + _0x39f350, {
                    cacheKey: _0x299a27,
                    cacheBuffer: _0x2faf25
                } = this.#getVertexBufferCache(_0xdb0628);
            if (!_0x299a27) {
                const _0x5c2947 = new Float32Array([
                    0x0,
                    0x0,
                    0x0,
                    0x0,
                    0x0,
                    _0x39f350,
                    0x0,
                    0x1,
                    _0x3abc84,
                    0x0,
                    0x1,
                    0x0,
                    _0x3abc84,
                    _0x39f350,
                    0x1,
                    0x1
                ]);
                _0xde8849['bindBuffer'](_0xde8849['ARRAY_BUFFER'], _0x2faf25['buffer']), _0xde8849['bufferData'](_0xde8849['ARRAY_BUFFER'], _0x5c2947, _0xde8849['STATIC_DRAW']);
            }
            _0x25741e['vertex'] = _0x2faf25['buffer'], _0x25741e['isRefVertex'] = !![];
        }
        {
            const _0x38abee = 'sym.6', {
                    cacheKey: _0x2c010d,
                    cacheBuffer: _0x5304b1
                } = this.#getIndexBufferCache(_0x38abee);
            if (!_0x2c010d) {
                const _0x3fc4d2 = new Uint16Array([
                    0x0,
                    0x1,
                    0x2,
                    0x1,
                    0x3,
                    0x2
                ]);
                _0x5304b1['indexCount'] = _0x3fc4d2['length'], _0xde8849['bindBuffer'](_0xde8849['ELEMENT_ARRAY_BUFFER'], _0x5304b1['buffer']), _0xde8849['bufferData'](_0xde8849['ELEMENT_ARRAY_BUFFER'], _0x3fc4d2, _0xde8849['STATIC_DRAW']);
            }
            _0x25741e['index'] = _0x5304b1['buffer'], _0x25741e['indexCount'] = _0x5304b1['indexCount'], _0x25741e['isRefIndex'] = !![];
        }
        {
            const _0x210d43 = 'sym.' + _0x55be4f['iconGroup'] + '.' + _0x55be4f['iconId'], {
                    cacheKey: _0x28f8f3,
                    cacheBuffer: _0x577a37
                } = this.#getTextureBufferCache(_0x210d43);
            !_0x28f8f3 && (_0xde8849['bindTexture'](_0xde8849['TEXTURE_2D'], _0x577a37['buffer']), _0xde8849['texParameteri'](_0xde8849['TEXTURE_2D'], _0xde8849['TEXTURE_MIN_FILTER'], _0xde8849['LINEAR']), _0xde8849['texParameteri'](_0xde8849['TEXTURE_2D'], _0xde8849['TEXTURE_MAG_FILTER'], _0xde8849['LINEAR']), _0xde8849['texParameteri'](_0xde8849['TEXTURE_2D'], _0xde8849['TEXTURE_WRAP_S'], _0xde8849['CLAMP_TO_EDGE']), _0xde8849['texParameteri'](_0xde8849['TEXTURE_2D'], _0xde8849['TEXTURE_WRAP_T'], _0xde8849['CLAMP_TO_EDGE']), _0xde8849['texImage2D'](_0xde8849['TEXTURE_2D'], 0x0, _0xde8849['RGBA'], _0xde8849['RGBA'], _0xde8849['UNSIGNED_BYTE'], _0x268531)), _0x25741e['texture'] = _0x577a37['buffer'], _0x25741e['isRefTexture'] = !![];
        }
    }
    #bindSvgSymbolTextBuffer(_0x51656b, _0x3a0830, _0x2c7490, _0x57c3aa, _0x5e2959, _0x22b292, _0x41aed0, _0x135ca1, _0x3fea45) {
        const _0x134a41 = this.#gl;
        if (!_0x51656b['vertex'] || !_0x51656b['texture']) {
            const _0x3febff = document['createElement']('canvas'), _0x3bbc4f = _0x3febff['getContext']('2d'), _0x1d38d5 = logi['maps']['Utils']['getTextSize'](_0x3bbc4f, _0x3a0830['textContent'], _0x2c7490, _0x57c3aa, _0x5e2959), _0x5b265f = this.#devicePixelRatio * 0x2;
            _0x3febff['width'] = _0x1d38d5['width'] * _0x5b265f, _0x3febff['height'] = _0x1d38d5['height'] * _0x5b265f, _0x3febff['style']['width'] = _0x1d38d5['width'], _0x3febff['style']['height'] = _0x1d38d5['height'], _0x3bbc4f['scale'](_0x5b265f, _0x5b265f), _0x3bbc4f['font'] = logi['maps']['Utils']['getFormatFont'](_0x2c7490, _0x57c3aa, _0x5e2959), _0x3bbc4f['textAlign'] = 'left', _0x3bbc4f['textBaseline'] = 'top';
            _0x135ca1 > 0x0 && (_0x3bbc4f['lineWidth'] = _0x135ca1, _0x3bbc4f['strokeStyle'] = _0x3fea45, _0x3bbc4f['strokeText'](_0x3a0830['textContent'], 0x0, 0x0));
            _0x3bbc4f['fillStyle'] = _0x22b292, _0x3bbc4f['fillText'](_0x3a0830['textContent'], 0x0, 0x0);
            const _0x1e0fb8 = logi['maps']['Utils']['getAlignPosition'](0x0, 0x0, _0x41aed0, _0x1d38d5['width'], ascent), _0x25cb35 = new Float32Array([
                    _0x1e0fb8['x'],
                    _0x1e0fb8['y'],
                    0x0,
                    0x0,
                    _0x1e0fb8['x'],
                    _0x1d38d5['height'] + _0x1e0fb8['y'],
                    0x0,
                    0x1,
                    _0x1d38d5['width'] + _0x1e0fb8['x'],
                    _0x1e0fb8['y'],
                    0x1,
                    0x0,
                    _0x1d38d5['width'] + _0x1e0fb8['x'],
                    _0x1d38d5['height'] + _0x1e0fb8['y'],
                    0x1,
                    0x1
                ]);
            !_0x51656b['vertex'] && (_0x51656b['vertex'] = _0x134a41['createBuffer']()), _0x134a41['bindBuffer'](_0x134a41['ARRAY_BUFFER'], _0x51656b['vertex']), _0x134a41['bufferData'](_0x134a41['ARRAY_BUFFER'], _0x25cb35, _0x134a41['STATIC_DRAW']), !_0x51656b['texture'] && (_0x51656b['texture'] = _0x134a41['createTexture']()), _0x134a41['bindTexture'](_0x134a41['TEXTURE_2D'], _0x51656b['texture']), _0x134a41['texParameteri'](_0x134a41['TEXTURE_2D'], _0x134a41['TEXTURE_MIN_FILTER'], _0x134a41['LINEAR']), _0x134a41['texParameteri'](_0x134a41['TEXTURE_2D'], _0x134a41['TEXTURE_MAG_FILTER'], _0x134a41['LINEAR']), _0x134a41['texParameteri'](_0x134a41['TEXTURE_2D'], _0x134a41['TEXTURE_WRAP_S'], _0x134a41['CLAMP_TO_EDGE']), _0x134a41['texParameteri'](_0x134a41['TEXTURE_2D'], _0x134a41['TEXTURE_WRAP_T'], _0x134a41['CLAMP_TO_EDGE']), _0x134a41['texImage2D'](_0x134a41['TEXTURE_2D'], 0x0, _0x134a41['RGBA'], _0x134a41['RGBA'], _0x134a41['UNSIGNED_BYTE'], _0x3febff);
        }
        {
            const _0x1480e5 = 'txt.6', {
                    cacheKey: _0x5de9b8,
                    cacheBuffer: _0x290778
                } = this.#getIndexBufferCache(_0x1480e5);
            if (!_0x5de9b8) {
                const _0x136ab5 = new Uint16Array([
                    0x0,
                    0x1,
                    0x2,
                    0x1,
                    0x3,
                    0x2
                ]);
                _0x290778['indexCount'] = _0x136ab5['length'], _0x134a41['bindBuffer'](_0x134a41['ELEMENT_ARRAY_BUFFER'], _0x290778['buffer']), _0x134a41['bufferData'](_0x134a41['ELEMENT_ARRAY_BUFFER'], _0x136ab5, _0x134a41['STATIC_DRAW']);
            }
            _0x51656b['index'] = _0x290778['buffer'], _0x51656b['indexCount'] = _0x290778['indexCount'], _0x51656b['isRefIndex'] = !![];
        }
    }
    #createSvgTextBuffer(_0x21d31e, _0x3502e9, _0x17516a, _0x1b5335, _0xbf9659, _0x3d3b22, _0x4be0f2, _0x2edabc, _0x20eaa0) {
        const _0x392383 = this.#gl;
        if (!_0x21d31e['vertex'] || !_0x21d31e['texture']) {
            const _0x4e306a = document['createElement']('canvas'), _0x1089f7 = _0x4e306a['getContext']('2d'), _0x2be853 = logi['maps']['Utils']['getTextSize'](_0x1089f7, _0x3502e9['textContent'], _0x17516a, _0x1b5335, _0xbf9659), _0x47c558 = this.#devicePixelRatio * 0x2;
            _0x4e306a['width'] = _0x2be853['width'] * _0x47c558, _0x4e306a['height'] = _0x2be853['height'] * _0x47c558, _0x4e306a['style']['width'] = _0x2be853['width'] + 'px', _0x4e306a['style']['height'] = _0x2be853['height'] + 'px', _0x1089f7['scale'](_0x47c558, _0x47c558), _0x1089f7['font'] = logi['maps']['Utils']['getFormatFont'](_0x17516a, _0x1b5335, _0xbf9659), _0x1089f7['textAlign'] = 'left', _0x1089f7['textBaseline'] = 'top';
            _0x2edabc > 0x0 && (_0x1089f7['lineWidth'] = _0x2edabc, _0x1089f7['strokeStyle'] = _0x20eaa0, _0x1089f7['strokeText'](_0x3502e9['textContent'], 0x0, 0x0));
            _0x1089f7['fillStyle'] = _0x3d3b22, _0x1089f7['fillText'](_0x3502e9['textContent'], 0x0, 0x0);
            const _0x540f26 = logi['maps']['Utils']['getAlignPosition'](0x0, 0x0, _0x4be0f2, _0x2be853['width'], ascent), _0x474028 = new Float32Array([
                    _0x540f26['x'],
                    _0x540f26['y'],
                    0x0,
                    0x0,
                    _0x540f26['x'],
                    _0x2be853['height'] + _0x540f26['y'],
                    0x0,
                    0x1,
                    _0x2be853['width'] + _0x540f26['x'],
                    _0x540f26['y'],
                    0x1,
                    0x0,
                    _0x2be853['width'] + _0x540f26['x'],
                    _0x2be853['height'] + _0x540f26['y'],
                    0x1,
                    0x1
                ]);
            !_0x21d31e['vertex'] && (_0x21d31e['vertex'] = _0x392383['createBuffer']()), _0x392383['bindBuffer'](_0x392383['ARRAY_BUFFER'], _0x21d31e['vertex']), _0x392383['bufferData'](_0x392383['ARRAY_BUFFER'], _0x474028, _0x392383['STATIC_DRAW']), !_0x21d31e['texture'] && (_0x21d31e['texture'] = _0x392383['createTexture']()), _0x392383['bindTexture'](_0x392383['TEXTURE_2D'], _0x21d31e['texture']), _0x392383['texParameteri'](_0x392383['TEXTURE_2D'], _0x392383['TEXTURE_MIN_FILTER'], _0x392383['LINEAR']), _0x392383['texParameteri'](_0x392383['TEXTURE_2D'], _0x392383['TEXTURE_MAG_FILTER'], _0x392383['LINEAR']), _0x392383['texParameteri'](_0x392383['TEXTURE_2D'], _0x392383['TEXTURE_WRAP_S'], _0x392383['CLAMP_TO_EDGE']), _0x392383['texParameteri'](_0x392383['TEXTURE_2D'], _0x392383['TEXTURE_WRAP_T'], _0x392383['CLAMP_TO_EDGE']), _0x392383['texImage2D'](_0x392383['TEXTURE_2D'], 0x0, _0x392383['RGBA'], _0x392383['RGBA'], _0x392383['UNSIGNED_BYTE'], _0x4e306a);
        }
        {
            const _0x168c00 = 'txt.6', {
                    cacheKey: _0x174df1,
                    cacheBuffer: _0xf81789
                } = this.#getIndexBufferCache(_0x168c00);
            if (!_0x174df1) {
                const _0x1a8a76 = new Uint16Array([
                    0x0,
                    0x1,
                    0x2,
                    0x1,
                    0x3,
                    0x2
                ]);
                _0xf81789['indexCount'] = _0x1a8a76['length'], _0x392383['bindBuffer'](_0x392383['ELEMENT_ARRAY_BUFFER'], _0xf81789['buffer']), _0x392383['bufferData'](_0x392383['ELEMENT_ARRAY_BUFFER'], _0x1a8a76, _0x392383['STATIC_DRAW']);
            }
            _0x21d31e['index'] = _0xf81789['buffer'], _0x21d31e['indexCount'] = _0xf81789['indexCount'], _0x21d31e['isRefIndex'] = !![];
        }
    }
    #bindSvgCurvedBuffer(_0x2fc584, _0xae0baf, _0x23ebcc, _0x5a800c, _0x22c35f, _0x4b4dd3, _0x1d99cd, _0x29400d, _0x5a829e) {
        const _0x34813d = this.#gl;
        if (!_0x2fc584['vertex'] || !_0x2fc584['texture']) {
            const _0x10fe90 = document['createElement']('canvas'), _0x414511 = _0x10fe90['getContext']('2d'), _0xd099d9 = logi['maps']['Utils']['getTextSize'](_0x414511, _0xae0baf['textContent'], _0x23ebcc, _0x5a800c, _0x22c35f), _0x47d9c8 = this.#devicePixelRatio * 0x2;
            _0x10fe90['width'] = _0xd099d9['width'] * _0x47d9c8, _0x10fe90['height'] = _0xd099d9['height'] * _0x47d9c8, _0x10fe90['style']['width'] = _0xd099d9['width'], _0x10fe90['style']['height'] = _0xd099d9['height'], _0x414511['scale'](_0x47d9c8, _0x47d9c8), _0x414511['font'] = logi['maps']['Utils']['getFormatFont'](_0x23ebcc, _0x5a800c, _0x22c35f), _0x414511['textAlign'] = 'left', _0x414511['textBaseline'] = 'top';
            _0x29400d > 0x0 && (_0x414511['lineWidth'] = _0x29400d, _0x414511['strokeStyle'] = _0x5a829e, _0x414511['strokeText'](_0xae0baf['textContent'], 0x0, 0x0));
            _0x414511['fillStyle'] = _0x4b4dd3, _0x414511['fillText'](_0xae0baf['textContent'], 0x0, 0x0);
            const _0x506cee = logi['maps']['Utils']['getAlignPosition'](0x0, 0x0, _0x1d99cd, _0xd099d9['width'], ascent), _0x5655ab = new Float32Array([
                    _0x506cee['x'],
                    _0x506cee['y'],
                    0x0,
                    0x0,
                    _0x506cee['x'],
                    _0xd099d9['height'] + _0x506cee['y'],
                    0x0,
                    0x1,
                    _0xd099d9['width'] + _0x506cee['x'],
                    _0x506cee['y'],
                    0x1,
                    0x0,
                    _0xd099d9['width'] + _0x506cee['x'],
                    _0xd099d9['height'] + _0x506cee['y'],
                    0x1,
                    0x1
                ]);
            !_0x2fc584['vertex'] && (_0x2fc584['vertex'] = _0x34813d['createBuffer']()), _0x34813d['bindBuffer'](_0x34813d['ARRAY_BUFFER'], _0x2fc584['vertex']), _0x34813d['bufferData'](_0x34813d['ARRAY_BUFFER'], _0x5655ab, _0x34813d['STATIC_DRAW']), !_0x2fc584['texture'] && (_0x2fc584['texture'] = _0x34813d['createTexture']()), _0x34813d['bindTexture'](_0x34813d['TEXTURE_2D'], _0x2fc584['texture']), _0x34813d['texParameteri'](_0x34813d['TEXTURE_2D'], _0x34813d['TEXTURE_MIN_FILTER'], _0x34813d['LINEAR']), _0x34813d['texParameteri'](_0x34813d['TEXTURE_2D'], _0x34813d['TEXTURE_MAG_FILTER'], _0x34813d['LINEAR']), _0x34813d['texParameteri'](_0x34813d['TEXTURE_2D'], _0x34813d['TEXTURE_WRAP_S'], _0x34813d['CLAMP_TO_EDGE']), _0x34813d['texParameteri'](_0x34813d['TEXTURE_2D'], _0x34813d['TEXTURE_WRAP_T'], _0x34813d['CLAMP_TO_EDGE']), _0x34813d['texImage2D'](_0x34813d['TEXTURE_2D'], 0x0, _0x34813d['RGBA'], _0x34813d['RGBA'], _0x34813d['UNSIGNED_BYTE'], _0x10fe90);
        }
        {
            const _0x444930 = 'txt.6', {
                    cacheKey: _0x1629da,
                    cacheBuffer: _0x58aa3b
                } = this.#getIndexBufferCache(_0x444930);
            if (!_0x1629da) {
                const _0x2663bb = new Uint16Array([
                    0x0,
                    0x1,
                    0x2,
                    0x1,
                    0x3,
                    0x2
                ]);
                _0x58aa3b['indexCount'] = _0x2663bb['length'], _0x34813d['bindBuffer'](_0x34813d['ELEMENT_ARRAY_BUFFER'], _0x58aa3b['buffer']), _0x34813d['bufferData'](_0x34813d['ELEMENT_ARRAY_BUFFER'], _0x2663bb, _0x34813d['STATIC_DRAW']);
            }
            _0x2fc584['index'] = _0x58aa3b['buffer'], _0x2fc584['indexCount'] = _0x58aa3b['indexCount'], _0x2fc584['isRefIndex'] = !![];
        }
    }
    #drawSvgDtmBuffer(_0xa7f08f) {
        const _0x3b9c89 = this.#gl;
        this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#modelMatrix = logi['maps']['GlMath']['translateMatrix'](this.#modelMatrix, this.#origin['x'], this.#origin['y'], this.#origin['z']);
        const _0x56cf85 = logi['maps']['GlMath']['multiplyMatrix'](this.#modelMatrix, this.#viewMatrix);
        _0x3b9c89['uniformMatrix4fv'](this.#programInfo['uniforms']['modelViewMatrix'], ![], _0x56cf85), _0x3b9c89['uniform4f'](this.#programInfo['uniforms']['color'], 0x1, 0x1, 0x1, 0x1), _0x3b9c89['uniform1i'](this.#programInfo['uniforms']['enableTex'], !![]), _0x3b9c89['bindBuffer'](_0x3b9c89['ARRAY_BUFFER'], _0xa7f08f['vertex']), _0x3b9c89['vertexAttribPointer'](this.#programInfo['attributes']['vertex'], 0x2, _0x3b9c89['FLOAT'], ![], 0x10, 0x0), _0x3b9c89['vertexAttribPointer'](this.#programInfo['attributes']['texCoord'], 0x2, _0x3b9c89['FLOAT'], ![], 0x10, 0x8), _0x3b9c89['enableVertexAttribArray'](this.#programInfo['attributes']['vertex']), _0x3b9c89['enableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x3b9c89['bindTexture'](_0x3b9c89['TEXTURE_2D'], _0xa7f08f['texture']), _0x3b9c89['bindBuffer'](_0x3b9c89['ELEMENT_ARRAY_BUFFER'], _0xa7f08f['index']), _0x3b9c89['drawElements'](_0x3b9c89['TRIANGLES'], _0xa7f08f['indexCount'], _0x3b9c89['UNSIGNED_SHORT'], 0x0), _0x3b9c89['disableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x3b9c89['uniform1i'](this.#programInfo['uniforms']['enableTex'], ![]);
    }
    #drawSvgPolygonBuffer(_0xe69920, _0x25e92e) {
        const _0x55934f = this.#gl;
        this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#modelMatrix = logi['maps']['GlMath']['translateMatrix'](this.#modelMatrix, this.#origin['x'], this.#origin['y'], this.#origin['z']);
        const _0x4f29ef = logi['maps']['GlMath']['multiplyMatrix'](this.#modelMatrix, this.#viewMatrix);
        _0x55934f['uniformMatrix4fv'](this.#programInfo['uniforms']['modelViewMatrix'], ![], _0x4f29ef), _0x55934f['uniform4f'](this.#programInfo['uniforms']['color'], _0x25e92e['r'], _0x25e92e['g'], _0x25e92e['b'], _0x25e92e['a']), _0x55934f['bindBuffer'](_0x55934f['ARRAY_BUFFER'], _0xe69920['vertex']), _0x55934f['vertexAttribPointer'](this.#programInfo['attributes']['vertex'], 0x2, _0x55934f['FLOAT'], ![], 0x0, 0x0), _0x55934f['enableVertexAttribArray'](this.#programInfo['attributes']['vertex']), _0x55934f['bindBuffer'](_0x55934f['ELEMENT_ARRAY_BUFFER'], _0xe69920['index']), _0x55934f['drawElements'](_0x55934f['TRIANGLES'], _0xe69920['indexCount'], _0x55934f['UNSIGNED_SHORT'], 0x0);
    }
    #drawSvgPolygonStrokeBuffer(_0x3b11e7, _0x285433) {
        const _0x400e43 = this.#gl;
        this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#modelMatrix = logi['maps']['GlMath']['translateMatrix'](this.#modelMatrix, this.#origin['x'], this.#origin['y'], this.#origin['z']);
        const _0x248745 = logi['maps']['GlMath']['multiplyMatrix'](this.#modelMatrix, this.#viewMatrix);
        _0x400e43['uniformMatrix4fv'](this.#programInfo['uniforms']['modelViewMatrix'], ![], _0x248745), _0x400e43['uniform4f'](this.#programInfo['uniforms']['color'], _0x285433['r'], _0x285433['g'], _0x285433['b'], _0x285433['a']), _0x400e43['bindBuffer'](_0x400e43['ARRAY_BUFFER'], _0x3b11e7['vertex']), _0x400e43['vertexAttribPointer'](this.#programInfo['attributes']['vertex'], 0x2, _0x400e43['FLOAT'], ![], 0x0, 0x0), _0x400e43['enableVertexAttribArray'](this.#programInfo['attributes']['vertex']), _0x400e43['bindBuffer'](_0x400e43['ELEMENT_ARRAY_BUFFER'], _0x3b11e7['index']), _0x400e43['drawElements'](_0x400e43['TRIANGLES'], _0x3b11e7['indexCount'], _0x400e43['UNSIGNED_SHORT'], 0x0);
    }
    #drawSvgPolyLineBuffer(_0x392d47, _0x302a34) {
        const _0x4f850d = this.#gl;
        this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#modelMatrix = logi['maps']['GlMath']['translateMatrix'](this.#modelMatrix, this.#origin['x'], this.#origin['y'], this.#origin['z']);
        const _0x274b3e = logi['maps']['GlMath']['multiplyMatrix'](this.#modelMatrix, this.#viewMatrix);
        _0x4f850d['uniformMatrix4fv'](this.#programInfo['uniforms']['modelViewMatrix'], ![], _0x274b3e), _0x4f850d['uniform4f'](this.#programInfo['uniforms']['color'], _0x302a34['r'], _0x302a34['g'], _0x302a34['b'], _0x302a34['a']), _0x4f850d['bindBuffer'](_0x4f850d['ARRAY_BUFFER'], _0x392d47['vertex']), _0x4f850d['vertexAttribPointer'](this.#programInfo['attributes']['vertex'], 0x2, _0x4f850d['FLOAT'], ![], 0x0, 0x0), _0x4f850d['enableVertexAttribArray'](this.#programInfo['attributes']['vertex']), _0x4f850d['bindBuffer'](_0x4f850d['ELEMENT_ARRAY_BUFFER'], _0x392d47['index']), _0x4f850d['drawElements'](_0x4f850d['TRIANGLES'], _0x392d47['indexCount'], _0x4f850d['UNSIGNED_SHORT'], 0x0);
    }
    #drawSvgDashedLineBuffer(_0x746c50, _0x57ed58) {
        const _0x21bd0d = this.#gl;
        this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#modelMatrix = logi['maps']['GlMath']['translateMatrix'](this.#modelMatrix, this.#origin['x'], this.#origin['y'], this.#origin['z']);
        const _0x5976ae = logi['maps']['GlMath']['multiplyMatrix'](this.#modelMatrix, this.#viewMatrix);
        _0x21bd0d['uniformMatrix4fv'](this.#programInfo['uniforms']['modelViewMatrix'], ![], _0x5976ae), _0x21bd0d['uniform4f'](this.#programInfo['uniforms']['color'], _0x57ed58['r'], _0x57ed58['g'], _0x57ed58['b'], _0x57ed58['a']), _0x21bd0d['bindBuffer'](_0x21bd0d['ARRAY_BUFFER'], _0x746c50['vertex']), _0x21bd0d['vertexAttribPointer'](this.#programInfo['attributes']['vertex'], 0x2, _0x21bd0d['FLOAT'], ![], 0x0, 0x0), _0x21bd0d['enableVertexAttribArray'](this.#programInfo['attributes']['vertex']), _0x21bd0d['bindBuffer'](_0x21bd0d['ELEMENT_ARRAY_BUFFER'], _0x746c50['index']), _0x21bd0d['drawElements'](_0x21bd0d['TRIANGLES'], _0x746c50['indexCount'], _0x21bd0d['UNSIGNED_SHORT'], 0x0);
    }
    #drawSvgSymbolImageBuffer(_0x5e0931) {
        const _0x462f6a = this.#gl;
        this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#modelMatrix = logi['maps']['GlMath']['translateMatrix'](this.#modelMatrix, this.#origin['x'], this.#origin['y'], this.#origin['z']);
        const _0x2eaf3a = logi['maps']['GlMath']['multiplyMatrix'](this.#modelMatrix, this.#viewMatrix);
        _0x462f6a['uniformMatrix4fv'](this.#programInfo['uniforms']['modelViewMatrix'], ![], _0x2eaf3a), _0x462f6a['uniform4f'](this.#programInfo['uniforms']['color'], 0x1, 0x1, 0x1, 0x1), _0x462f6a['uniform1i'](this.#programInfo['uniforms']['enableTex'], !![]), _0x462f6a['bindBuffer'](_0x462f6a['ARRAY_BUFFER'], _0x5e0931['vertex']), _0x462f6a['vertexAttribPointer'](this.#programInfo['attributes']['vertex'], 0x2, _0x462f6a['FLOAT'], ![], 0x10, 0x0), _0x462f6a['vertexAttribPointer'](this.#programInfo['attributes']['texCoord'], 0x2, _0x462f6a['FLOAT'], ![], 0x10, 0x8), _0x462f6a['enableVertexAttribArray'](this.#programInfo['attributes']['vertex']), _0x462f6a['enableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x462f6a['bindTexture'](_0x462f6a['TEXTURE_2D'], _0x5e0931['texture']), _0x462f6a['bindBuffer'](_0x462f6a['ELEMENT_ARRAY_BUFFER'], _0x5e0931['index']), _0x462f6a['drawElements'](_0x462f6a['TRIANGLES'], _0x5e0931['indexCount'], _0x462f6a['UNSIGNED_SHORT'], 0x0), _0x462f6a['disableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x462f6a['uniform1i'](this.#programInfo['uniforms']['enableTex'], ![]);
    }
    #drawSvgSymbolTextBuffer(_0x385e25) {
        const _0x1b49c3 = this.#gl;
        this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#modelMatrix = logi['maps']['GlMath']['translateMatrix'](this.#modelMatrix, this.#origin['x'], this.#origin['y'], this.#origin['z']);
        const _0xe6227d = logi['maps']['GlMath']['multiplyMatrix'](this.#modelMatrix, this.#viewMatrix);
        _0x1b49c3['uniformMatrix4fv'](this.#programInfo['uniforms']['modelViewMatrix'], ![], _0xe6227d), _0x1b49c3['uniform4f'](this.#programInfo['uniforms']['color'], 0x1, 0x1, 0x1, 0x1), _0x1b49c3['uniform1i'](this.#programInfo['uniforms']['enableTex'], !![]), _0x1b49c3['uniform1i'](this.#programInfo['uniforms']['fontMode'], !![]), _0x1b49c3['bindBuffer'](_0x1b49c3['ARRAY_BUFFER'], _0x385e25['vertex']), _0x1b49c3['vertexAttribPointer'](this.#programInfo['attributes']['vertex'], 0x2, _0x1b49c3['FLOAT'], ![], 0x10, 0x0), _0x1b49c3['vertexAttribPointer'](this.#programInfo['attributes']['texCoord'], 0x2, _0x1b49c3['FLOAT'], ![], 0x10, 0x8), _0x1b49c3['enableVertexAttribArray'](this.#programInfo['attributes']['vertex']), _0x1b49c3['enableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x1b49c3['bindTexture'](_0x1b49c3['TEXTURE_2D'], _0x385e25['texture']), _0x1b49c3['bindBuffer'](_0x1b49c3['ELEMENT_ARRAY_BUFFER'], _0x385e25['index']), _0x1b49c3['drawElements'](_0x1b49c3['TRIANGLES'], _0x385e25['indexCount'], _0x1b49c3['UNSIGNED_SHORT'], 0x0), _0x1b49c3['disableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x1b49c3['uniform1i'](this.#programInfo['uniforms']['enableTex'], ![]), _0x1b49c3['uniform1i'](this.#programInfo['uniforms']['fontMode'], ![]);
    }
    #drawSvgTextBuffer(_0x30dd9f) {
        const _0x4bbc07 = this.#gl;
        this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#modelMatrix = logi['maps']['GlMath']['translateMatrix'](this.#modelMatrix, this.#origin['x'], this.#origin['y'], this.#origin['z']);
        const _0x389048 = logi['maps']['GlMath']['multiplyMatrix'](this.#modelMatrix, this.#viewMatrix);
        _0x4bbc07['uniformMatrix4fv'](this.#programInfo['uniforms']['modelViewMatrix'], ![], _0x389048), _0x4bbc07['uniform4f'](this.#programInfo['uniforms']['color'], 0x1, 0x1, 0x1, 0x1), _0x4bbc07['uniform1i'](this.#programInfo['uniforms']['enableTex'], !![]), _0x4bbc07['uniform1i'](this.#programInfo['uniforms']['fontMode'], !![]), _0x4bbc07['bindBuffer'](_0x4bbc07['ARRAY_BUFFER'], _0x30dd9f['vertex']), _0x4bbc07['vertexAttribPointer'](this.#programInfo['attributes']['vertex'], 0x2, _0x4bbc07['FLOAT'], ![], 0x10, 0x0), _0x4bbc07['vertexAttribPointer'](this.#programInfo['attributes']['texCoord'], 0x2, _0x4bbc07['FLOAT'], ![], 0x10, 0x8), _0x4bbc07['enableVertexAttribArray'](this.#programInfo['attributes']['vertex']), _0x4bbc07['enableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x4bbc07['bindTexture'](_0x4bbc07['TEXTURE_2D'], _0x30dd9f['texture']), _0x4bbc07['bindBuffer'](_0x4bbc07['ELEMENT_ARRAY_BUFFER'], _0x30dd9f['index']), _0x4bbc07['drawElements'](_0x4bbc07['TRIANGLES'], _0x30dd9f['indexCount'], _0x4bbc07['UNSIGNED_SHORT'], 0x0), _0x4bbc07['disableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x4bbc07['uniform1i'](this.#programInfo['uniforms']['enableTex'], ![]), _0x4bbc07['uniform1i'](this.#programInfo['uniforms']['fontMode'], ![]);
    }
    #drawSvgCurvedBuffer(_0x471912) {
        const _0x3854ab = this.#gl;
        this.#modelMatrix = logi['maps']['GlMath']['loadIdentityMatrix'](), this.#modelMatrix = logi['maps']['GlMath']['translateMatrix'](this.#modelMatrix, this.#origin['x'], this.#origin['y'], this.#origin['z']);
        const _0x4ed8d6 = logi['maps']['GlMath']['multiplyMatrix'](this.#modelMatrix, this.#viewMatrix);
        _0x3854ab['uniformMatrix4fv'](this.#programInfo['uniforms']['modelViewMatrix'], ![], _0x4ed8d6), _0x3854ab['uniform4f'](this.#programInfo['uniforms']['color'], 0x1, 0x1, 0x1, 0x1), _0x3854ab['uniform1i'](this.#programInfo['uniforms']['enableTex'], !![]), _0x3854ab['uniform1i'](this.#programInfo['uniforms']['fontMode'], !![]), _0x3854ab['bindBuffer'](_0x3854ab['ARRAY_BUFFER'], _0x471912['vertex']), _0x3854ab['vertexAttribPointer'](this.#programInfo['attributes']['vertex'], 0x2, _0x3854ab['FLOAT'], ![], 0x10, 0x0), _0x3854ab['vertexAttribPointer'](this.#programInfo['attributes']['texCoord'], 0x2, _0x3854ab['FLOAT'], ![], 0x10, 0x8), _0x3854ab['enableVertexAttribArray'](this.#programInfo['attributes']['vertex']), _0x3854ab['enableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x3854ab['bindTexture'](_0x3854ab['TEXTURE_2D'], _0x471912['texture']), _0x3854ab['bindBuffer'](_0x3854ab['ELEMENT_ARRAY_BUFFER'], _0x471912['index']), _0x3854ab['drawElements'](_0x3854ab['TRIANGLES'], _0x471912['indexCount'], _0x3854ab['UNSIGNED_SHORT'], 0x0), _0x3854ab['disableVertexAttribArray'](this.#programInfo['attributes']['texCoord']), _0x3854ab['uniform1i'](this.#programInfo['uniforms']['enableTex'], ![]), _0x3854ab['uniform1i'](this.#programInfo['uniforms']['fontMode'], ![]);
    }
    #applyTransform(_0x46286d, _0x312b4c = 0x1) {
        if (!_0x46286d)
            return;
        if (_0x46286d['trfArray']['length'] == 0x0) {
            const _0x5a1f3c = _0x46286d['trfString']?.['match'](/(\w+\([^)]+\))/g) || [];
            _0x5a1f3c['forEach'](_0x3b3e32 => {
                const _0x1bd22e = _0x3b3e32['split']('('), _0x208b9b = _0x1bd22e[0x0], _0xebd133 = _0x1bd22e[0x1]['split'](')')[0x0]['split'](',');
                if (_0xebd133['length'] >= 0x1)
                    switch (_0x208b9b) {
                    case 'rotate': {
                            _0xebd133['length'] == 0x3 ? _0x46286d['trfArray']['push']({
                                'r3': {
                                    'angle': parseFloat(_0xebd133[0x0]),
                                    'pivotX': parseFloat(_0xebd133[0x1]),
                                    'pivotY': parseFloat(_0xebd133[0x2])
                                }
                            }) : _0x46286d['trfArray']['push']({ 'r1': { 'angle': parseFloat(_0xebd133[0x0]) } });
                        }
                        break;
                    case 'scale': {
                            _0x46286d['trfArray']['push']({
                                's2': {
                                    'scaleX': parseFloat(_0xebd133[0x0]),
                                    'scaleY': _0xebd133['length'] > 0x1 ? parseFloat(_0xebd133[0x1]) : parseFloat(_0xebd133[0x0])
                                }
                            });
                        }
                        break;
                    case 'translate': {
                            _0x46286d['trfArray']['push']({
                                't2': {
                                    'translateX': parseFloat(_0xebd133[0x0]),
                                    'translateY': _0xebd133['length'] > 0x1 ? parseFloat(_0xebd133[0x1]) : 0x0
                                }
                            });
                        }
                        break;
                    }
            });
        }
        if (_0x46286d['trfArray']['length'] > 0x0)
            for (const _0x236c01 of _0x46286d['trfArray']) {
                if (_0x236c01['r3']) {
                    const _0x56dfe9 = _0x236c01['r3'];
                    this['translate'](_0x56dfe9['pivotX'] * _0x312b4c, _0x56dfe9['pivotY'] * _0x312b4c), this['rotate'](_0x56dfe9['angle']), this['translate'](-(_0x56dfe9['pivotX'] * _0x312b4c), -(_0x56dfe9['pivotY'] * _0x312b4c));
                }
                _0x236c01['r1'] && this['rotate'](_0x236c01['r1']['angle']);
                if (_0x236c01['s2']) {
                    const _0x5582df = _0x236c01['s2'];
                    this['scale'](_0x5582df['scaleX'], _0x5582df['scaleY']);
                }
                if (_0x236c01['t2']) {
                    const _0x53432d = _0x236c01['t2'];
                    this['translate'](_0x53432d['translateX'] * _0x312b4c, _0x53432d['translateY'] * _0x312b4c);
                }
            }
    }
    #getRatioCurved(_0x240510, _0x43f621 = 0x1) {
        const _0x4251d7 = new Array(), _0x1e3289 = 0.75 / _0x43f621;
        let _0x8e138b = 0x0, _0x51db9b = new Array();
        _0x51db9b['push'](0x0);
        for (let _0x28adb6 = 0x1; _0x28adb6 < _0x240510['length']; ++_0x28adb6) {
            _0x8e138b += Math['sqrt'](Math['pow'](_0x240510[_0x28adb6]['x'] - _0x240510[_0x28adb6 - 0x1]['x'], 0x2) + Math['pow'](_0x240510[_0x28adb6]['y'] - _0x240510[_0x28adb6 - 0x1]['y'], 0x2)), _0x51db9b['push'](_0x8e138b * _0x1e3289);
        }
        if (_0x8e138b == 0x0)
            return null;
        const _0x207b59 = (_0x8e138b - _0x8e138b * _0x1e3289) * 0.5;
        let _0x17120e = 0x0, _0x4c93a5 = _0x207b59 + _0x51db9b[0x0];
        for (let _0x5e8281 = 0x0; _0x5e8281 < _0x240510['length'] - 0x1; _0x5e8281++) {
            const _0x268258 = _0x240510[_0x5e8281], _0x13777f = _0x240510[_0x5e8281 + 0x1], _0xe4b3a2 = Math['sqrt'](Math['pow'](_0x13777f['x'] - _0x268258['x'], 0x2) + Math['pow'](_0x13777f['y'] - _0x268258['y'], 0x2));
            while (_0x17120e + _0xe4b3a2 >= _0x4c93a5) {
                const _0x34b61a = _0x4c93a5 - _0x17120e, _0x1b2662 = _0x34b61a / _0xe4b3a2, _0x485ebd = _0x268258['x'] + (_0x13777f['x'] - _0x268258['x']) * _0x1b2662, _0x19bbaf = _0x268258['y'] + (_0x13777f['y'] - _0x268258['y']) * _0x1b2662, _0x169e58 = Math['atan2'](_0x13777f['y'] - _0x268258['y'], _0x13777f['x'] - _0x268258['x']);
                _0x4251d7['push']({
                    'x': _0x485ebd,
                    'y': _0x19bbaf,
                    'angle': _0x169e58 * (0xb4 / Math['PI'])
                });
                if (_0x4251d7['length'] >= _0x51db9b['length'])
                    break;
                else
                    _0x4c93a5 = _0x207b59 + _0x51db9b[_0x4251d7['length']];
            }
            _0x17120e += _0xe4b3a2;
        }
        while (_0x4251d7['length'] < _0x240510['length']) {
            const _0x3d7f7c = _0x240510[_0x240510['length'] - 0x2], _0x24680e = _0x240510[_0x240510['length'] - 0x1], _0xbc2a19 = _0x24680e['x'], _0x45b367 = _0x24680e['y'], _0x5789ac = Math['atan2'](_0x24680e['y'] - _0x3d7f7c['y'], _0x24680e['x'] - _0x3d7f7c['x']);
            _0x4251d7['push']({
                'x': _0xbc2a19,
                'y': _0x45b367,
                'angle': _0x5789ac * (0xb4 / Math['PI'])
            });
        }
        return _0x4251d7;
    }
};
export default logi['maps']['Gfxgl'];