import a0_0x4313d9 from '../common/logi-maps-defines.js?v=2.1.10.1';
import a0_0x419ee6 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a0_0x46a4b6 from './logi-maps-gfxcanvas.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['Defines'] = a0_0x4313d9, logi['maps']['Utils'] = a0_0x419ee6, logi['maps']['GfxCanvas'] = a0_0x46a4b6, logi['maps']['Gfx2d'] = class extends logi['maps']['GfxCanvas'] {
    #context = null;
    constructor(_0x2521ad) {
        super(_0x2521ad, '2d'), this.#context = _0x2521ad['getContext']('2d');
    }
    ['getCtx']() {
        return this.#context;
    }
    ['clearColor'](_0x583a96) {
        _0x583a96 ? (this.#context['fillStyle'] = _0x583a96['hexString'], this.#context['fillRect'](0x0, 0x0, this['width'], this['height'])) : this.#context['clearRect'](0x0, 0x0, this['width'], this['height']);
    }
    ['reset']() {
        this.#context['setTransform'](0x1, 0x0, 0x0, 0x1, 0x0, 0x0);
    }
    ['save']() {
        this.#context['save']();
    }
    ['restore']() {
        this.#context['restore']();
    }
    ['setTransform'](..._0x1870d8) {
        this.#context['setTransform'](..._0x1870d8);
    }
    ['translate'](_0x7a8b8f, _0x330640) {
        this.#context['translate'](_0x7a8b8f, _0x330640);
    }
    ['rotate'](_0x19fce8) {
        this.#context['rotate'](logi['maps']['Utils']['degToRad'](_0x19fce8));
    }
    ['scale'](_0x3d5f71, _0x537ecc) {
        this.#context['scale'](_0x3d5f71, _0x537ecc);
    }
    ['setBlendMode'](_0x415df1) {
        this.#context['globalCompositeOperation'] = _0x415df1;
    }
    ['drawImage'](_0x278104, ..._0x1cea40) {
        let _0xa3804e = _0x278104;
        _0x278104 instanceof logi['maps']['Gfx2d'] && (_0xa3804e = _0x278104['getCanvas']());
        _0x278104['image'] && (_0xa3804e = _0x278104['image']);
        if (_0x1cea40['length'] === 0x2) {
            const [_0x28b7df, _0x5ed4a3] = _0x1cea40;
            this.#context['drawImage'](_0xa3804e, _0x28b7df, _0x5ed4a3);
        } else {
            if (_0x1cea40['length'] === 0x4) {
                const [_0x1032c7, _0xe96399, _0x1b4fa4, _0xd6f2b7] = _0x1cea40;
                this.#context['drawImage'](_0xa3804e, _0x1032c7, _0xe96399, _0x1b4fa4, _0xd6f2b7);
            } else {
                if (_0x1cea40['length'] === 0x8) {
                    const [_0x61c198, _0x4a142e, _0x170df4, _0x2b302d, _0x4247ae, _0x556c34, _0x496bf7, _0x11eb82] = _0x1cea40;
                    this.#context['drawImage'](_0xa3804e, _0x61c198, _0x4a142e, _0x170df4, _0x2b302d, _0x4247ae, _0x556c34, _0x496bf7, _0x11eb82);
                }
            }
        }
    }
    ['drawObjImage'](_0x105eb2, _0x58e330, _0x33b884, _0xf83066 = 0x0, _0x1178e = 0x0, _0x178f11 = 0x0, _0x136dca = 0x0, _0x55981f = 0x0, _0x57e6bc = 0x0, _0x142e42 = 0x0) {
        _0xf83066 == 0x0 ? this.#context['drawImage'](_0x105eb2, _0x58e330 + _0x136dca + _0x57e6bc, _0x33b884 + _0x55981f + _0x142e42) : (this['save'](), this['translate'](_0x58e330 + _0x136dca + _0x1178e, _0x33b884 + _0x55981f + _0x178f11), this['rotate'](_0xf83066), this.#context['drawImage'](_0x105eb2, -_0x1178e + _0x57e6bc, -_0x178f11 + _0x142e42), this['restore']());
    }
    ['drawObjText'](_0x4bfb53, _0x522a9d, _0x3236fe, _0x4665da, _0x5c27d4, _0x5049c1, _0x3c931a, _0x38a59c, _0x584a2e = 0x0, _0x482894 = null) {
        let _0x571118 = 0x0, _0x284c08 = logi['maps']['Utils']['convertToTextBaseline'](_0x38a59c);
        if (_0x284c08 == 'middle') {
            this.#context['font'] = logi['maps']['Utils']['getFormatFont'](_0x4665da, _0x5c27d4, _0x5049c1);
            const _0x5b3a73 = this.#context['measureText'](_0x4bfb53), _0x475ea5 = _0x5b3a73['actualBoundingBoxAscent'] + _0x5b3a73['actualBoundingBoxDescent'];
            _0x571118 = _0x5b3a73['actualBoundingBoxAscent'] - _0x475ea5 / 0x2, _0x284c08 = 'alphabetic';
        }
        this.#context['font'] = logi['maps']['Utils']['getFormatFont'](_0x4665da, _0x5c27d4, _0x5049c1), this.#context['fillStyle'] = _0x3c931a, this.#context['textAlign'] = logi['maps']['Utils']['convertToTextAlign'](_0x38a59c), this.#context['textBaseline'] = _0x284c08, _0x584a2e > 0x0 && (this.#context['lineWidth'] = _0x584a2e, this.#context['strokeStyle'] = _0x482894, this.#context['strokeText'](_0x4bfb53, _0x522a9d, _0x3236fe + _0x571118)), this.#context['fillText'](_0x4bfb53, _0x522a9d, _0x3236fe + _0x571118);
    }
    ['drawObjPolygon'](_0x17aafa, _0x109a70, _0xd0d12 = 0x0, _0x560c71 = '#FFFFFF', _0xc1040a = 'round', _0x37e068 = 'round') {
        if (_0x17aafa['length'] < 0x4)
            return;
        this.#context['beginPath'](), this.#context['fillStyle'] = _0x109a70, this.#context['lineCap'] = _0xc1040a, this.#context['lineJoin'] = _0x37e068, this.#context['moveTo'](_0x17aafa[0x0]['x'], _0x17aafa[0x0]['y']);
        for (let _0x5effad of _0x17aafa) {
            this.#context['lineTo'](_0x5effad['x'], _0x5effad['y']);
        }
        this.#context['fill'](), _0xd0d12 > 0x0 && (this.#context['lineWidth'] = _0xd0d12, this.#context['strokeStyle'] = _0x560c71, this.#context['stroke']());
    }
    ['drawObjLines'](_0x178e28, _0x1c0b60, _0x5d02de, _0x36a4f7, _0x7590ab = 'round', _0x8c0503 = 'round') {
        const _0x49daee = _0x178e28['length'] <= _0x1c0b60['length'] ? _0x178e28['length'] : _0x1c0b60['length'];
        if (_0x49daee > 0x0) {
            this.#context['beginPath'](), this.#context['lineWidth'] = _0x5d02de, this.#context['strokeStyle'] = _0x36a4f7, this.#context['lineCap'] = _0x7590ab, this.#context['lineJoin'] = _0x8c0503;
            for (let _0x43f70f = 0x0; _0x43f70f < _0x49daee; ++_0x43f70f) {
                this.#context['moveTo'](_0x178e28[_0x43f70f]['x'], _0x178e28[_0x43f70f]['y']), this.#context['lineTo'](_0x1c0b60[_0x43f70f]['x'], _0x1c0b60[_0x43f70f]['y']);
            }
            this.#context['stroke']();
        }
    }
    ['drawObjLine'](_0x5dd531, _0x42e87e, _0x7520e1, _0x490fd6, _0x1ac360 = 'round', _0x3800e1 = 'round') {
        this.#context['beginPath'](), this.#context['lineWidth'] = _0x7520e1, this.#context['strokeStyle'] = _0x490fd6, this.#context['lineCap'] = _0x1ac360, this.#context['lineJoin'] = _0x3800e1, this.#context['moveTo'](_0x5dd531['x'], _0x5dd531['y']), this.#context['lineTo'](_0x42e87e['x'], _0x42e87e['y']), this.#context['stroke']();
    }
    ['drawObjPolyLine'](_0x4f9495, _0x24202d, _0x44e6f7, _0x1607a3 = 0x0, _0x287580 = 0x0, _0x2a9645 = 'round', _0x160e96 = 'round') {
        if (_0x4f9495['length'] < 0x2)
            return;
        this.#context['beginPath'](), this.#context['lineWidth'] = _0x24202d, this.#context['strokeStyle'] = _0x44e6f7, this.#context['lineCap'] = _0x2a9645, this.#context['lineJoin'] = _0x160e96, this.#context['moveTo'](_0x4f9495[0x0]['x'] + _0x1607a3, _0x4f9495[0x0]['y'] + _0x287580);
        for (let _0x24ca44 of _0x4f9495) {
            this.#context['lineTo'](_0x24ca44['x'] + _0x1607a3, _0x24ca44['y'] + _0x287580);
        }
        this.#context['stroke']();
    }
    ['drawObjDottedLine'](_0x4bfb61, _0xdd9a11, _0x290e82, _0x9c4b4f, _0x31695e = 0x0, _0x372cc8 = 0x0, _0x2f1630 = 'round', _0x748847 = 'round') {
        if (_0x4bfb61['length'] < 0x2)
            return;
        this.#context['setLineDash']([
            0x0,
            _0x290e82
        ]), this.#context['beginPath'](), this.#context['lineWidth'] = _0xdd9a11 * 0x2, this.#context['strokeStyle'] = _0x9c4b4f, this.#context['lineCap'] = _0x2f1630, this.#context['lineJoin'] = _0x748847, this.#context['moveTo'](_0x4bfb61[0x0]['x'] + _0x31695e, _0x4bfb61[0x0]['y'] + _0x372cc8);
        for (let _0x455aa1 of _0x4bfb61) {
            this.#context['lineTo'](_0x455aa1['x'] + _0x31695e, _0x455aa1['y'] + _0x372cc8);
        }
        this.#context['stroke'](), this.#context['setLineDash']([]);
    }
    ['drawObjPolyDashedLine'](_0x2df91d, _0x2fc942, _0x40e943, _0x58fb1f, _0x2615e9 = 0x0, _0x49d80e = 0x0, _0xf06d1a = 'round', _0x2d1f22 = 'round') {
        if (_0x2df91d['length'] < 0x2)
            return;
        this.#context['setLineDash'](_0x40e943), this.#context['beginPath'](), this.#context['lineWidth'] = _0x2fc942, this.#context['strokeStyle'] = _0x58fb1f, this.#context['lineCap'] = _0xf06d1a, this.#context['lineJoin'] = _0x2d1f22, this.#context['moveTo'](_0x2df91d[0x0]['x'] + _0x2615e9, _0x2df91d[0x0]['y'] + _0x49d80e);
        for (let _0x1c347c of _0x2df91d) {
            this.#context['lineTo'](_0x1c347c['x'] + _0x2615e9, _0x1c347c['y'] + _0x49d80e);
        }
        this.#context['stroke'](), this.#context['setLineDash']([]);
    }
    ['drawObjDot'](_0x1758c5, _0x588936, _0x232f5b, _0x24b9a1) {
        this.#context['beginPath'](), this.#context['arc'](_0x1758c5, _0x588936, _0x232f5b, 0x0, Math['PI'] * 0x2, ![]), this.#context['fillStyle'] = _0x24b9a1, this.#context['fill']();
    }
    ['drawObjCircle'](_0x5566cf, _0x1d2466, _0x188a37, _0x3e4ffb, _0x370faf, _0x380aaa) {
        this.#context['beginPath'](), this.#context['arc'](_0x5566cf, _0x1d2466, _0x188a37, 0x0, Math['PI'] * 0x2, ![]), this.#context['fillStyle'] = _0x3e4ffb, this.#context['fill'](), _0x370faf > 0x0 && (this.#context['lineWidth'] = _0x370faf, this.#context['strokeStyle'] = _0x380aaa, this.#context['stroke']());
    }
    ['drawObjRoundRect'](_0x3894db, _0x5e5e9b, _0x47ba1b, _0x4d1068, _0x216e7f = 0x0, _0x7eb0d2 = '#FFFFFF', _0x17620f = 0x0, _0x2c85e8 = '#000000', _0x43e69b = 0x0, _0x881a92 = 0x0, _0x427887 = 0x0, _0x26a0bd = 0x0, _0x325b3f = 0x0, _0x4c3923 = 0x0, _0x56e7bc = 0x0, _0x2d16e4 = {
        'left': 0x0,
        'right': 0x0,
        'top': 0x0,
        'bottom': 0x0
    }) {
        this['save'](), this['translate'](_0x3894db + _0x26a0bd + _0x881a92, _0x5e5e9b + _0x325b3f + _0x427887);
        _0x43e69b != 0x0 && this['rotate'](_0x43e69b);
        const _0x47dde6 = _0x2d16e4['left'] ?? 0x0, _0x170256 = _0x2d16e4['right'] ?? 0x0, _0x387330 = _0x2d16e4['top'] ?? 0x0, _0x2ce719 = _0x2d16e4['bottom'] ?? 0x0, _0x1493f8 = _0x47ba1b + _0x47dde6 + _0x170256, _0x22c79b = _0x4d1068 + _0x387330 + _0x2ce719, _0x29a162 = _0x4c3923 - _0x47dde6 - _0x881a92, _0x22bc64 = _0x56e7bc - _0x387330 - _0x427887;
        this.#context['beginPath'](), this.#context['moveTo'](_0x29a162 + _0x216e7f, _0x22bc64), this.#context['lineTo'](_0x29a162 + _0x1493f8 - _0x216e7f, _0x22bc64), this.#context['quadraticCurveTo'](_0x29a162 + _0x1493f8, _0x22bc64, _0x29a162 + _0x1493f8, _0x22bc64 + _0x216e7f), this.#context['lineTo'](_0x29a162 + _0x1493f8, _0x22bc64 + _0x22c79b - _0x216e7f), this.#context['quadraticCurveTo'](_0x29a162 + _0x1493f8, _0x22bc64 + _0x22c79b, _0x29a162 + _0x1493f8 - _0x216e7f, _0x22bc64 + _0x22c79b), this.#context['lineTo'](_0x29a162 + _0x216e7f, _0x22bc64 + _0x22c79b), this.#context['quadraticCurveTo'](_0x29a162, _0x22bc64 + _0x22c79b, _0x29a162, _0x22bc64 + _0x22c79b - _0x216e7f), this.#context['lineTo'](_0x29a162, _0x22bc64 + _0x216e7f), this.#context['quadraticCurveTo'](_0x29a162, _0x22bc64, _0x29a162 + _0x216e7f, _0x22bc64), this.#context['closePath'](), this.#context['fillStyle'] = _0x7eb0d2, this.#context['fill'](), _0x17620f > 0x0 && (this.#context['lineWidth'] = _0x17620f, this.#context['strokeStyle'] = _0x2c85e8, this.#context['stroke']()), this['restore']();
    }
    ['drawDtm'](_0xdec4d, _0x2e749d, _0x121e54, _0x214c94, _0x57a8f6) {
        this.#context['globalCompositeOperation'] = 'hard-light';
        if (_0xdec4d['image']) {
            const _0x4a3ffe = _0xdec4d['image'];
            this.#context['drawImage'](_0x4a3ffe, _0x2e749d, _0x121e54, _0x214c94, _0x57a8f6);
        }
        this.#context['globalCompositeOperation'] = 'source-over';
    }
    ['drawSvgPolygons'](_0x12abce) {
        if (_0x12abce['layerStyle']?.['fillStyle'] == 'fill-stroke') {
            for (const _0x367ef2 of _0x12abce['elemDatas']) {
                const _0x54cb7c = _0x367ef2['style'];
                this.#drawSvgPolygon(_0x367ef2['geometry'], _0x54cb7c['fillColorData']['hexString']);
            }
            for (const _0x290199 of _0x12abce['elemDatas']) {
                const _0x346202 = _0x290199['style'];
                _0x346202['stroke'] > 0x0 && this.#drawSvgPolyLine(_0x290199['geometry'], _0x346202['stroke'], _0x346202['strokeColorData']['hexString']);
            }
        } else {
            for (const _0x8726b2 of _0x12abce['elemDatas']) {
                const _0x54ace9 = _0x8726b2['style'];
                _0x54ace9['stroke'] > 0x0 && this.#drawSvgPolyLine(_0x8726b2['geometry'], _0x54ace9['stroke'] * 0x2, _0x54ace9['strokeColorData']['hexString']);
            }
            for (const _0x603ddc of _0x12abce['elemDatas']) {
                const _0x176b72 = _0x603ddc['style'];
                this.#drawSvgPolygon(_0x603ddc['geometry'], _0x176b72['fillColorData']['hexString']);
            }
        }
    }
    ['drawSvgPolylines'](_0x224b2c, _0x1a0f90 = 0x1) {
        for (const _0x10fc93 of _0x224b2c['elemDatas']) {
            const _0x4a1b5d = _0x10fc93['style'];
            if (_0x4a1b5d['stroke'] > 0x0) {
                const _0x18722 = _0x4a1b5d['fixedScale'] == ![] ? _0x4a1b5d['width'] + _0x4a1b5d['stroke'] * 0x2 : (_0x4a1b5d['width'] + _0x4a1b5d['stroke'] * 0x2) / _0x1a0f90;
                _0x4a1b5d['dash'] > 0x0 && _0x4a1b5d['gap'] > 0x0 ? this.#drawSvgDashedLine(_0x10fc93['geometry'], _0x18722, _0x4a1b5d['dash'], _0x4a1b5d['gap'], _0x4a1b5d['strokeColorData']['hexString']) : this.#drawSvgPolyLine(_0x10fc93['geometry'], _0x18722, _0x4a1b5d['strokeColorData']['hexString']);
            }
        }
        for (const _0x1a63bf of _0x224b2c['elemDatas']) {
            const _0x220c83 = _0x1a63bf['style'], _0x3c394a = _0x220c83['fixedScale'] == ![] ? _0x220c83['width'] : _0x220c83['width'] / _0x1a0f90;
            _0x220c83['dash'] > 0x0 && _0x220c83['gap'] > 0x0 ? this.#drawSvgDashedLine(_0x1a63bf['geometry'], _0x3c394a, _0x220c83['dash'], _0x220c83['gap'], _0x220c83['colorData']['hexString']) : this.#drawSvgPolyLine(_0x1a63bf['geometry'], _0x3c394a, _0x220c83['colorData']['hexString']);
        }
    }
    ['drawSvgSymbols'](_0x296a0e, _0x334cbd = 0x1) {
        for (const _0x3b2108 of _0x296a0e['elemDatas']) {
            const _0x2860b4 = _0x3b2108['style'];
            this.#drawSvgSymbol(_0x3b2108['symbol'], _0x3b2108['transforms'], _0x3b2108['geometry']['image'], _0x3b2108['geometry']['texts'], _0x296a0e['fontFamily'], _0x2860b4['fontSize'], _0x2860b4['bold'], _0x2860b4['textColorData']['hexString'], _0x2860b4['textAlign'], _0x2860b4['stroke'], _0x2860b4['strokeColorData']['hexString'], _0x334cbd);
        }
    }
    ['drawSvgTexts'](_0x289bfc, _0x38a984 = 0x1) {
        for (const _0x2d4a76 of _0x289bfc['elemDatas']) {
            const _0x2ebd9e = _0x2d4a76['style'];
            this.#drawSvgText(_0x2d4a76['transforms'], _0x2d4a76['geometry']['texts'], _0x289bfc['fontFamily'], _0x2ebd9e['fontSize'], _0x2ebd9e['bold'], _0x2ebd9e['textColorData']['hexString'], _0x2ebd9e['textAlign'], _0x2ebd9e['stroke'], _0x2ebd9e['strokeColorData']['hexString'], _0x38a984);
        }
    }
    ['drawSvgCurveds'](_0xb08eb1, _0x24a29e = 0x1) {
        for (const _0x59f9cd of _0xb08eb1['elemDatas']) {
            const _0x3cc714 = _0x59f9cd['style'];
            this.#drawSvgCurved(_0x59f9cd['transforms'], _0x59f9cd['geometry']['texts'], _0xb08eb1['fontFamily'], _0x3cc714['fontSize'], _0x3cc714['bold'], _0x3cc714['textColorData']['hexString'], _0x3cc714['textAlign'], _0x3cc714['stroke'], _0x3cc714['strokeColorData']['hexString'], _0x24a29e);
        }
    }
    ['getTextSize'](_0x14bca7, _0x20c2ea, _0x3dadb0) {
        return logi['maps']['Utils']['getTextSize'](this.#context, _0x14bca7, _0x20c2ea, _0x3dadb0);
    }
    #drawSvgPolygon(_0x406429, _0x596456) {
        !_0x406429['path2d'] && (_0x406429['path2d'] = new Path2D(_0x406429['pathString'])), this.#context['fillStyle'] = _0x596456, this.#context['fill'](_0x406429['path2d']);
    }
    #drawSvgPolyLine(_0x4c9f82, _0x5796fa, _0x4827fb, _0x2c8a75 = 'round', _0x4e9eb1 = 'round') {
        if (_0x5796fa <= 0x0)
            return;
        !_0x4c9f82['path2d'] && (_0x4c9f82['path2d'] = new Path2D(_0x4c9f82['pathString'])), this.#context['lineWidth'] = _0x5796fa, this.#context['strokeStyle'] = _0x4827fb, this.#context['lineCap'] = _0x2c8a75, this.#context['lineJoin'] = _0x4e9eb1, this.#context['stroke'](_0x4c9f82['path2d']);
    }
    #drawSvgDashedLine(_0x217d63, _0xc06cf4, _0x216658, _0x39cd0a, _0x390b10, _0x3f377c = 'round', _0x52c773 = 'round') {
        if (_0xc06cf4 <= 0x0)
            return;
        !_0x217d63['path2d'] && (_0x217d63['path2d'] = new Path2D(_0x217d63['pathString'])), this.#context['setLineDash']([
            _0x216658,
            _0x39cd0a
        ]), this.#context['lineWidth'] = _0xc06cf4, this.#context['strokeStyle'] = _0x390b10, this.#context['lineCap'] = _0x3f377c, this.#context['lineJoin'] = _0x52c773, this.#context['stroke'](_0x217d63['path2d']);
    }
    #drawSvgSymbol(_0x313d18, _0x5e1c92, _0x2f7dce, _0xbbcead, _0x1128a6, _0x50794a, _0x3ce228, _0x50eac2, _0x14538f, _0x55e27e, _0x5db7fc, _0x3493ce = 0x1) {
        this['save'](), this.#applyTransform(_0x5e1c92, _0x3493ce);
        _0x313d18 && (this['save'](), this.#applyTransform(_0x2f7dce['transforms']), this['translate'](_0x2f7dce['x'], _0x2f7dce['y']), this.#context['drawImage'](_0x313d18, 0x0, 0x0), this['restore']());
        if (_0xbbcead && _0x50794a > 0x0) {
            this.#context['font'] = logi['maps']['Utils']['getFormatFont'](_0x1128a6, _0x50794a, _0x3ce228), this.#context['textAlign'] = logi['maps']['Utils']['convertToTextAlign'](_0x14538f), this.#context['textBaseline'] = logi['maps']['Utils']['convertToTextBaseline'](_0x14538f);
            for (let _0x19525c of _0xbbcead) {
                this['save'](), _0x19525c['transform'] && this.#applyTransform(_0x19525c['transforms']), this['translate'](_0x19525c['x'], _0x19525c['y']), _0x55e27e > 0x0 && (this.#context['lineWidth'] = _0x55e27e, this.#context['strokeStyle'] = _0x5db7fc, this.#context['strokeText'](_0x19525c['textContent'], 0x0, 0x0)), this.#context['fillStyle'] = _0x50eac2, this.#context['fillText'](_0x19525c['textContent'], 0x0, 0x0), this['restore']();
            }
        }
        this['restore']();
    }
    #drawSvgText(_0x1aeb43, _0x1508b1, _0x1335f2, _0x14b2af, _0x52a502, _0x2cd755, _0x2da3d8, _0x549799, _0x21216b, _0x10c8cb = 0x1) {
        this['save'](), this.#applyTransform(_0x1aeb43, _0x10c8cb);
        if (_0x1508b1 && _0x14b2af > 0x0) {
            this.#context['font'] = logi['maps']['Utils']['getFormatFont'](_0x1335f2, _0x14b2af, _0x52a502), this.#context['textAlign'] = logi['maps']['Utils']['convertToTextAlign'](_0x2da3d8), this.#context['textBaseline'] = logi['maps']['Utils']['convertToTextBaseline'](_0x2da3d8);
            for (let _0x3cef98 of _0x1508b1) {
                this['save'](), _0x3cef98['transform'] && this.#applyTransform(_0x3cef98['transforms'], _0x10c8cb), this['translate'](_0x3cef98['x'] * _0x10c8cb, _0x3cef98['y'] * _0x10c8cb), _0x549799 > 0x0 && (this.#context['lineWidth'] = _0x549799, this.#context['strokeStyle'] = _0x21216b, this.#context['strokeText'](_0x3cef98['textContent'], 0x0, 0x0)), this.#context['fillStyle'] = _0x2cd755, this.#context['fillText'](_0x3cef98['textContent'], 0x0, 0x0), this['restore']();
            }
        }
        this['restore']();
    }
    #drawSvgCurved(_0x3dfe93, _0x3cc575, _0x2466be, _0x193b2b, _0x3d7bbf, _0x296b9a, _0x30a668, _0x17d469, _0x2f8388, _0x3f70e0 = 0x1) {
        this['save'](), this.#applyTransform(_0x3dfe93, _0x3f70e0);
        if (_0x3cc575 && _0x193b2b > 0x0) {
            this.#context['font'] = logi['maps']['Utils']['getFormatFont'](_0x2466be, _0x193b2b, _0x3d7bbf), this.#context['textAlign'] = logi['maps']['Utils']['convertToTextAlign'](_0x30a668), this.#context['textBaseline'] = logi['maps']['Utils']['convertToTextBaseline'](_0x30a668);
            const _0x38b470 = this.#getRatioCurved(_0x3cc575, _0x3f70e0);
            if (_0x38b470)
                for (let _0x109b3e = 0x0; _0x109b3e < _0x3cc575['length']; ++_0x109b3e) {
                    const _0xc0a263 = _0x3cc575[_0x109b3e];
                    this['save']();
                    const _0x4c6728 = _0x38b470[_0x109b3e]['angle'], _0x2f6550 = _0x38b470[_0x109b3e]['x'] * _0x3f70e0, _0x519491 = _0x38b470[_0x109b3e]['y'] * _0x3f70e0;
                    this['translate'](_0x2f6550, _0x519491), this['rotate'](_0x4c6728), _0x17d469 > 0x0 && (this.#context['lineWidth'] = _0x17d469, this.#context['strokeStyle'] = _0x2f8388, this.#context['strokeText'](_0xc0a263['textContent'], 0x0, 0x0)), this.#context['fillStyle'] = _0x296b9a, this.#context['fillText'](_0xc0a263['textContent'], 0x0, 0x0), this['restore']();
                }
        }
        this['restore']();
    }
    #applyTransform(_0x55ac05, _0x2360c7 = 0x1) {
        if (!_0x55ac05)
            return;
        if (_0x55ac05['trfArray']['length'] == 0x0) {
            const _0xd52d45 = _0x55ac05['trfString']?.['match'](/(\w+\([^)]+\))/g) || [];
            _0xd52d45['forEach'](_0x3679e4 => {
                const _0x5f335f = _0x3679e4['split']('('), _0x2ad666 = _0x5f335f[0x0], _0x5c9531 = _0x5f335f[0x1]['split'](')')[0x0]['split'](',');
                if (_0x5c9531['length'] >= 0x1)
                    switch (_0x2ad666) {
                    case 'rotate': {
                            _0x5c9531['length'] == 0x3 ? _0x55ac05['trfArray']['push']({
                                'r3': {
                                    'angle': parseFloat(_0x5c9531[0x0]),
                                    'pivotX': parseFloat(_0x5c9531[0x1]),
                                    'pivotY': parseFloat(_0x5c9531[0x2])
                                }
                            }) : _0x55ac05['trfArray']['push']({ 'r1': { 'angle': parseFloat(_0x5c9531[0x0]) } });
                        }
                        break;
                    case 'scale': {
                            _0x55ac05['trfArray']['push']({
                                's2': {
                                    'scaleX': parseFloat(_0x5c9531[0x0]),
                                    'scaleY': _0x5c9531['length'] > 0x1 ? parseFloat(_0x5c9531[0x1]) : parseFloat(_0x5c9531[0x0])
                                }
                            });
                        }
                        break;
                    case 'translate': {
                            _0x55ac05['trfArray']['push']({
                                't2': {
                                    'translateX': parseFloat(_0x5c9531[0x0]),
                                    'translateY': _0x5c9531['length'] > 0x1 ? parseFloat(_0x5c9531[0x1]) : 0x0
                                }
                            });
                        }
                        break;
                    }
            });
        }
        if (_0x55ac05['trfArray']['length'] > 0x0)
            for (const _0x48a3d7 of _0x55ac05['trfArray']) {
                if (_0x48a3d7['r3']) {
                    const _0x1e724c = _0x48a3d7['r3'];
                    this['translate'](_0x1e724c['pivotX'] * _0x2360c7, _0x1e724c['pivotY'] * _0x2360c7), this['rotate'](_0x1e724c['angle']), this['translate'](-(_0x1e724c['pivotX'] * _0x2360c7), -(_0x1e724c['pivotY'] * _0x2360c7));
                }
                _0x48a3d7['r1'] && this['rotate'](_0x48a3d7['r1']['angle']);
                if (_0x48a3d7['s2']) {
                    const _0x9ec33a = _0x48a3d7['s2'];
                    this['scale'](_0x9ec33a['scaleX'], _0x9ec33a['scaleY']);
                }
                if (_0x48a3d7['t2']) {
                    const _0x3a45b7 = _0x48a3d7['t2'];
                    this['translate'](_0x3a45b7['translateX'] * _0x2360c7, _0x3a45b7['translateY'] * _0x2360c7);
                }
            }
    }
    #getRatioCurved(_0x33981a, _0x1547b7 = 0x1) {
        const _0xd376d7 = new Array(), _0x95c773 = 0.75 / _0x1547b7;
        let _0x3e7a86 = 0x0, _0x49048d = new Array();
        _0x49048d['push'](0x0);
        for (let _0x2fb81d = 0x1; _0x2fb81d < _0x33981a['length']; ++_0x2fb81d) {
            _0x3e7a86 += Math['sqrt'](Math['pow'](_0x33981a[_0x2fb81d]['x'] - _0x33981a[_0x2fb81d - 0x1]['x'], 0x2) + Math['pow'](_0x33981a[_0x2fb81d]['y'] - _0x33981a[_0x2fb81d - 0x1]['y'], 0x2)), _0x49048d['push'](_0x3e7a86 * _0x95c773);
        }
        if (_0x3e7a86 == 0x0)
            return null;
        const _0x163137 = (_0x3e7a86 - _0x3e7a86 * _0x95c773) * 0.5;
        let _0x277c99 = 0x0, _0x48e60e = _0x163137 + _0x49048d[0x0];
        for (let _0xa89c73 = 0x0; _0xa89c73 < _0x33981a['length'] - 0x1; _0xa89c73++) {
            const _0x32eb76 = _0x33981a[_0xa89c73], _0x153cd3 = _0x33981a[_0xa89c73 + 0x1], _0x32ec67 = Math['sqrt'](Math['pow'](_0x153cd3['x'] - _0x32eb76['x'], 0x2) + Math['pow'](_0x153cd3['y'] - _0x32eb76['y'], 0x2));
            while (_0x277c99 + _0x32ec67 >= _0x48e60e) {
                const _0x9f38c7 = _0x48e60e - _0x277c99, _0x382444 = _0x9f38c7 / _0x32ec67, _0x72a173 = _0x32eb76['x'] + (_0x153cd3['x'] - _0x32eb76['x']) * _0x382444, _0x5057d9 = _0x32eb76['y'] + (_0x153cd3['y'] - _0x32eb76['y']) * _0x382444, _0x12de33 = Math['atan2'](_0x153cd3['y'] - _0x32eb76['y'], _0x153cd3['x'] - _0x32eb76['x']);
                _0xd376d7['push']({
                    'x': _0x72a173,
                    'y': _0x5057d9,
                    'angle': _0x12de33 * (0xb4 / Math['PI'])
                });
                if (_0xd376d7['length'] >= _0x49048d['length'])
                    break;
                else
                    _0x48e60e = _0x163137 + _0x49048d[_0xd376d7['length']];
            }
            _0x277c99 += _0x32ec67;
        }
        while (_0xd376d7['length'] < _0x33981a['length']) {
            const _0x4e562d = _0x33981a[_0x33981a['length'] - 0x2], _0x1e8247 = _0x33981a[_0x33981a['length'] - 0x1], _0x85e640 = _0x1e8247['x'], _0x5e1595 = _0x1e8247['y'], _0x52c539 = Math['atan2'](_0x1e8247['y'] - _0x4e562d['y'], _0x1e8247['x'] - _0x4e562d['x']);
            _0xd376d7['push']({
                'x': _0x85e640,
                'y': _0x5e1595,
                'angle': _0x52c539 * (0xb4 / Math['PI'])
            });
        }
        return _0xd376d7;
    }
};
export default logi['maps']['Gfx2d'];