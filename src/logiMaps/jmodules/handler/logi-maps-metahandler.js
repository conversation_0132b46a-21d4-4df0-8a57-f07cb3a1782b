import a6_0x2041b2 from '../common/logi-maps-types.js?v=2.1.10.1';
import a6_0x339d75 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a6_0x5a9d3f from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a6_0x4631c5 from './logi-maps-objecthandler.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectsInTile'] = a6_0x2041b2['ObjectsInTile'], logi['maps']['Utils'] = a6_0x339d75, logi['maps']['BoundaryData'] = a6_0x5a9d3f, logi['maps']['ObjectHandler'] = a6_0x4631c5, logi['maps']['MetaHandler'] = class extends logi['maps']['ObjectHandler'] {
    #metaObjects;
    constructor(_0x5819d0) {
        super(_0x5819d0), this.#metaObjects = new Map();
    }
    ['addMeta'](_0x1679ba, _0x5d7ab9) {
        const _0x349fe6 = _0x1679ba['getKey']();
        if (this.#metaObjects['has'](_0x349fe6))
            return console['warn']('[logi.maps]\x20' + _0x349fe6 + '\x20메타\x20객체가\x20이미\x20추가되어\x20있습니다.'), ![];
        return _0x1679ba['getLayer']() && _0x1679ba['getLayer']() != _0x5d7ab9 && _0x1679ba['getLayer']()['removeMeta'](_0x349fe6), _0x1679ba['setLayer'](_0x5d7ab9), this.#metaObjects['set'](_0x349fe6, _0x1679ba), !![];
    }
    ['removeMeta'](_0x36950f) {
        let _0x11f810 = ![];
        if (_0x36950f['class']) {
            const _0x364a43 = _0x36950f['class'];
            for (const [_0x35de98, _0x236cda] of this.#metaObjects) {
                _0x236cda['getClass']() == _0x364a43 && (_0x236cda['setLayer'](null), this.#metaObjects['delete'](_0x35de98), _0x11f810 = !![]);
            }
        } else {
            const _0xb7c683 = _0x36950f['key'] ?? _0x36950f, _0x3a87f0 = this.#metaObjects['get'](_0xb7c683);
            _0x3a87f0 && (_0x3a87f0['setLayer'](null), this.#metaObjects['delete'](_0xb7c683), _0x11f810 = !![]);
        }
        return _0x11f810;
    }
    ['getDrawObjects']() {
        const _0x1548d4 = this['getMapCoord']()['getLevel'](), _0x418c11 = [];
        for (const [, _0x3707d9] of this.#metaObjects) {
            _0x3707d9['getVisible']() == !![] && _0x3707d9['checkRenderRange'](_0x1548d4) == !![] && _0x418c11['push'](_0x3707d9);
        }
        return _0x418c11['sort']((_0x266a7f, _0x4b6ce0) => _0x266a7f['zIndex'] - _0x4b6ce0['zIndex']);
    }
};
export default logi['maps']['MetaHandler'];