import a5_0x154c4e from '../common/logi-maps-types.js?v=2.1.10.1';
import a5_0x409aa5 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a5_0x2b0f47 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a5_0x5d4b7a from './logi-maps-objecthandler.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectsInTile'] = a5_0x154c4e['ObjectsInTile'], logi['maps']['Utils'] = a5_0x409aa5, logi['maps']['BoundaryData'] = a5_0x2b0f47, logi['maps']['ObjectHandler'] = a5_0x5d4b7a, logi['maps']['LineHandler'] = class extends logi['maps']['ObjectHandler'] {
    #lineObjects;
    #drawingLineOnMove;
    constructor(_0x3a79c7) {
        super(_0x3a79c7), this.#lineObjects = new Map(), this.#drawingLineOnMove = !![];
    }
    ['sendEvent'](_0x406f57) {
        for (const [, _0x10989a] of this.#lineObjects) {
            const _0xaa06d8 = _0x10989a?.['eventHandlers'][_0x406f57['type']];
            _0xaa06d8?.['length'] > 0x0 && (_0x10989a['isHit'](_0x406f57['point']) == !![] && (_0x406f57['source'] = _0x10989a, _0xaa06d8['forEach'](_0x120eb8 => {
                _0x120eb8?.(_0x406f57);
            })));
        }
    }
    ['hitLine'](_0x531e67, _0x626b8b) {
        const _0x1bab09 = {
            'x': _0x531e67,
            'y': _0x626b8b
        };
        for (const [, _0x2fc556] of this.#lineObjects) {
            if (_0x2fc556?.['isHit'](_0x1bab09) == !![])
                return _0x2fc556;
        }
        return null;
    }
    ['findLine'](_0x10be46) {
        if (_0x10be46['class']) {
            const _0x2699b9 = _0x10be46['class'], _0x3b126a = new Array();
            for (const [, _0x234fa9] of this.#lineObjects) {
                _0x234fa9['getClass']() == _0x2699b9 && _0x3b126a['push'](_0x234fa9);
            }
            return _0x3b126a;
        } else {
            if (_0x10be46['rect']) {
                const _0x5e4b08 = new Array(), _0x370ceb = this['toBoundaryRect'](_0x10be46['rect']);
                if (_0x370ceb)
                    for (const [, _0xd62254] of this.#lineObjects) {
                        _0xd62254['isOverlap'](_0x370ceb) == !![] && _0x5e4b08['push'](_0xd62254);
                    }
                return _0x5e4b08;
            } else {
                const _0xf621d3 = _0x10be46['key'] ?? _0x10be46;
                return this.#lineObjects['get'](_0xf621d3) ?? null;
            }
        }
    }
    ['addLine'](_0x4a516d, _0x2ecb9f) {
        const _0x2dd9af = _0x4a516d['getKey']();
        if (this.#lineObjects['has'](_0x2dd9af))
            return console['warn']('[logi.maps]\x20' + _0x2dd9af + '\x20라인이\x20이미\x20추가되어\x20있습니다.'), ![];
        return _0x4a516d['getLayer']() && _0x4a516d['getLayer']() != _0x2ecb9f && _0x4a516d['getLayer']()['removeLine'](_0x2dd9af), _0x4a516d['setLayer'](_0x2ecb9f), this.#lineObjects['set'](_0x2dd9af, _0x4a516d), !![];
    }
    ['isExistLine'](_0x6a0ac6) {
        return this.#lineObjects['has'](_0x6a0ac6);
    }
    ['removeLine'](_0x2a2000) {
        let _0xaacf9 = ![];
        if (_0x2a2000['class']) {
            const _0x2810fc = _0x2a2000['class'];
            for (const [_0x2f6eaa, _0x129b32] of this.#lineObjects) {
                _0x129b32['getClass']() == _0x2810fc && (_0x129b32['setLayer'](null), this.#lineObjects['delete'](_0x2f6eaa), _0xaacf9 = !![]);
            }
        } else {
            const _0x29b422 = _0x2a2000['key'] ?? _0x2a2000, _0x352d54 = this.#lineObjects['get'](_0x29b422);
            _0x352d54 && (_0x352d54['setLayer'](null), this.#lineObjects['delete'](_0x29b422), _0xaacf9 = !![]);
        }
        return _0xaacf9;
    }
    ['removeLineAll'](_0x5ecc1c = []) {
        if (_0x5ecc1c['length'] == 0x0) {
            for (const [, _0x407d59] of this.#lineObjects) {
                _0x407d59['setLayer'](null);
            }
            this.#lineObjects['clear']();
        } else
            for (const _0x470859 of this.#lineObjects['keys']()) {
                !_0x5ecc1c['includes'](_0x470859) && this['removeLine'](_0x470859);
            }
    }
    ['setDrawingLineOnMove'](_0x1c7ec4) {
        if (this.#drawingLineOnMove != _0x1c7ec4)
            return this.#drawingLineOnMove = _0x1c7ec4, !![];
        return ![];
    }
    ['getDrawObjects'](_0x31601e = ![]) {
        const _0x23e5e4 = this['getMapCoord']()['getLevel'](), _0x5254ce = [];
        if (_0x31601e == ![] || this.#drawingLineOnMove == !![])
            for (const [, _0x44d695] of this.#lineObjects) {
                _0x44d695['getVisible']() == !![] && _0x44d695['checkRenderRange'](_0x23e5e4) == !![] && _0x5254ce['push'](_0x44d695);
            }
        return _0x5254ce['sort']((_0x318d47, _0x15b7e3) => _0x318d47['zIndex'] - _0x15b7e3['zIndex']);
    }
};
export default logi['maps']['LineHandler'];