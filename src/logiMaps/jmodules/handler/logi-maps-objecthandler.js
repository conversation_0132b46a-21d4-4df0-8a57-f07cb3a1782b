import a7_0x5e5d18 from '../common/logi-maps-types.js?v=2.1.10.1';
import a7_0x306453 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a7_0x5f3ea3 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectHandler'] = class {
    #mapCoord;
    constructor(_0x3bd9aa) {
        this.#mapCoord = _0x3bd9aa;
    }
    ['getMapCoord']() {
        return this.#mapCoord;
    }
    ['toBoundaryRect'](_0x2330bb) {
        if (_0x2330bb?.['length'] != 0x2)
            return null;
        else {
            const _0x3cfa8a = this['getMapCoord'](), _0x4d68f8 = _0x3cfa8a['world2screen'](_0x2330bb[0x0]['lng'], _0x2330bb[0x0]['lat']), _0x37b9fa = _0x3cfa8a['world2screen'](_0x2330bb[0x1]['lng'], _0x2330bb[0x1]['lat']);
            return [
                {
                    'x': _0x4d68f8['x'],
                    'y': _0x4d68f8['y']
                },
                {
                    'x': _0x4d68f8['x'],
                    'y': _0x37b9fa['y']
                },
                {
                    'x': _0x37b9fa['x'],
                    'y': _0x37b9fa['y']
                },
                {
                    'x': _0x37b9fa['x'],
                    'y': _0x4d68f8['y']
                }
            ];
        }
    }
};
export default logi['maps']['ObjectHandler'];