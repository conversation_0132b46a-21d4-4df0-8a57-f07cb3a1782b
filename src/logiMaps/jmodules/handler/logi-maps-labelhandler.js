import a4_0x600e36 from '../common/logi-maps-types.js?v=2.1.10.1';
import a4_0x1fa170 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a4_0x169621 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a4_0x493c9c from './logi-maps-objecthandler.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectsInTile'] = a4_0x600e36['ObjectsInTile'], logi['maps']['Utils'] = a4_0x1fa170, logi['maps']['BoundaryData'] = a4_0x169621, logi['maps']['ObjectHandler'] = a4_0x493c9c, logi['maps']['LabelHandler'] = class extends logi['maps']['ObjectHandler'] {
    #labelObjects;
    #tiledLabelCacheMap;
    #tiledLabelInRect;
    #drawingLabelOnMove;
    constructor(_0x484f82) {
        super(_0x484f82), this.#labelObjects = new Map(), this.#tiledLabelCacheMap = new Map(), this.#tiledLabelInRect = new Map(), this.#drawingLabelOnMove = !![];
    }
    ['sendEvent'](_0x2ba26d) {
        for (const [, _0x54c16e] of this.#tiledLabelInRect) {
            for (const [, _0xfb12] of _0x54c16e['objects']) {
                const _0x18a113 = _0xfb12?.['eventHandlers'][_0x2ba26d['type']];
                _0x18a113?.['length'] > 0x0 && (_0xfb12['isHit'](_0x2ba26d['point']) == !![] && (_0x2ba26d['source'] = _0xfb12, _0x18a113['forEach'](_0x548de7 => {
                    _0x548de7?.(_0x2ba26d);
                })));
            }
        }
    }
    ['hitLabel'](_0x5e07cf, _0x57a864) {
        const _0x2651d2 = {
            'x': _0x5e07cf,
            'y': _0x57a864
        };
        for (const [, _0x413d8b] of this.#tiledLabelInRect) {
            for (const [, _0x188705] of _0x413d8b['objects']) {
                if (_0x188705?.['isHit'](_0x2651d2) == !![])
                    return _0x188705;
            }
        }
        return null;
    }
    ['hitLabels'](_0x2efe25, _0x50fc37) {
        const _0x4e38a5 = new Array(), _0x1a8f43 = {
                'x': _0x2efe25,
                'y': _0x50fc37
            };
        for (const [, _0x1a88c3] of this.#tiledLabelInRect) {
            for (const [, _0x1f8886] of _0x1a88c3['objects']) {
                _0x1f8886?.['isHit'](_0x1a8f43) == !![] && _0x4e38a5['push'](_0x1f8886);
            }
        }
        return _0x4e38a5;
    }
    ['findLabel'](_0x1991c2) {
        if (_0x1991c2['class']) {
            const _0x210ab2 = _0x1991c2['class'], _0xe37a90 = new Array();
            for (const [, _0x3c540c] of this.#labelObjects) {
                _0x3c540c['getClass']() == _0x210ab2 && _0xe37a90['push'](_0x3c540c);
            }
            return _0xe37a90;
        } else {
            if (_0x1991c2['rect']) {
                const _0x3ddd31 = new Array(), _0x562d0c = this['toBoundaryRect'](_0x1991c2['rect']);
                if (_0x562d0c)
                    for (const [, _0x6be041] of this.#labelObjects) {
                        _0x6be041['isOverlap'](_0x562d0c) == !![] && _0x3ddd31['push'](_0x6be041);
                    }
                return _0x3ddd31;
            } else {
                const _0x5a92b2 = _0x1991c2['key'] ?? _0x1991c2;
                return this.#labelObjects['get'](_0x5a92b2) ?? null;
            }
        }
    }
    ['addLabel'](_0x4b18e9, _0x445aea) {
        const _0x369019 = _0x4b18e9['getKey']();
        if (this.#labelObjects['has'](_0x369019))
            return console['warn']('[logi.maps]\x20' + _0x369019 + '\x20라벨이\x20이미\x20추가되어\x20있습니다.'), ![];
        return _0x4b18e9['getLayer']() && _0x4b18e9['getLayer']() != _0x445aea && (_0x4b18e9['getLayer']()['removeLabel'](_0x369019), this.#delTiledLabelCacheMap(_0x4b18e9['tileInfo']['tileId'], _0x369019)), _0x4b18e9['setLayer'](_0x445aea), _0x4b18e9['resetBoundary'](), this.#labelObjects['set'](_0x369019, _0x4b18e9), this.#addTiledLabelCacheMap(_0x4b18e9), !![];
    }
    ['isExistLabel'](_0x17a37f) {
        return this.#labelObjects['has'](_0x17a37f);
    }
    ['removeLabel'](_0x2297a0) {
        let _0x31de9b = ![];
        if (_0x2297a0['class']) {
            const _0x35332f = _0x2297a0['class'];
            for (const [_0x1dd5f0, _0x1abd60] of this.#labelObjects) {
                if (_0x1abd60['getClass']() == _0x35332f) {
                    const _0x26d49a = _0x1abd60['tileInfo']['tileId'];
                    _0x1abd60['setLayer'](null), this.#labelObjects['delete'](_0x1dd5f0), this.#delTiledLabelCacheMap(_0x26d49a, _0x1dd5f0), _0x31de9b = !![];
                }
            }
        } else {
            const _0x4bd095 = _0x2297a0['key'] ?? _0x2297a0, _0x3af27e = this.#labelObjects['get'](_0x4bd095);
            if (_0x3af27e) {
                const _0x535f17 = _0x3af27e['tileInfo']['tileId'];
                _0x3af27e['setLayer'](null), this.#labelObjects['delete'](_0x4bd095), this.#delTiledLabelCacheMap(_0x535f17, _0x4bd095), _0x31de9b = !![];
            }
        }
        return _0x31de9b;
    }
    ['removeLabelAll'](_0x363777 = []) {
        if (_0x363777['length'] == 0x0) {
            for (const [, _0x2839a9] of this.#labelObjects) {
                _0x2839a9['expiredTileId'] = !![], _0x2839a9['tileInfo']['tileId'] = 0x0, _0x2839a9['setLayer'](null);
            }
            this.#labelObjects['clear'](), this.#tiledLabelCacheMap['clear'](), this.#tiledLabelInRect['clear']();
        } else
            for (const _0x5b8387 of this.#labelObjects['keys']()) {
                !_0x363777['includes'](_0x5b8387) && this['removeLabel'](_0x5b8387);
            }
    }
    ['setDrawingLabelOnMove'](_0x1accfe) {
        if (this.#drawingLabelOnMove != _0x1accfe)
            return this.#drawingLabelOnMove = _0x1accfe, !![];
        return ![];
    }
    ['getDrawObjects'](_0x46b1e6 = ![]) {
        const _0x76265 = [];
        if (_0x46b1e6 == ![] || this.#drawingLabelOnMove == !![])
            for (const [, _0xb504a9] of this.#tiledLabelInRect) {
                for (const [, _0x2f4bcc] of _0xb504a9['objects']) {
                    _0x2f4bcc['boundaryData']['bLoad'] === logi['maps']['BoundaryData']['STATUS']['LOAD'] && _0x76265['push'](_0x2f4bcc);
                }
            }
        return _0x76265['sort']((_0x134332, _0x319460) => _0x134332['zIndex'] - _0x319460['zIndex']);
    }
    ['boundaryCheckInit'](_0x11f470) {
        const _0x437e69 = this['getMapCoord']()['getMapRect']();
        let _0x120662 = new Array();
        for (const [, _0x2f984c] of this.#tiledLabelCacheMap) {
            for (const [, _0x5ac8d8] of _0x2f984c['objects']) {
                _0x5ac8d8['overlapInfo']['visibility'] = _0x11f470, _0x5ac8d8['boundaryData']['overlapCnt'] = 0x0, _0x5ac8d8['expiredTileId'] == !![] && _0x120662['push'](_0x5ac8d8);
            }
        }
        for (const _0x385f1a of _0x120662) {
            this.#delTiledLabelCacheMap(_0x385f1a['tileInfo']['tileId'], _0x385f1a['getKey']()), this.#addTiledLabelCacheMap(_0x385f1a);
        }
        for (const [_0x1dfabd, _0x10cdc6] of this.#tiledLabelInRect) {
            !logi['maps']['Utils']['rectOnMapRect'](_0x10cdc6['tileInfo']['boundary'], _0x437e69) && this.#tiledLabelInRect['delete'](_0x1dfabd);
        }
    }
    ['updateOverlapCheck1st'](_0x5ee165, _0x1416ff) {
        const _0x36056c = this['getMapCoord']()['getMapRect'](), _0x526668 = this['getMapCoord']()['getLevel']();
        for (const [, _0x74fc77] of this.#tiledLabelInRect) {
            for (const [, _0x464af4] of _0x74fc77['objects']) {
                if (_0x464af4['getVisible']() == ![] || _0x464af4['checkRenderRange'](_0x526668) == ![]) {
                    _0x464af4['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], _0x464af4['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']();
                    continue;
                }
                const _0x1cadfa = _0x464af4['getPosition']();
                if (!logi['maps']['Utils']['pointInMapRect'](_0x1cadfa, _0x36056c)) {
                    if (_0x464af4['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['LOAD'] || _0x464af4['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['LOAD_OUTSIDE'])
                        _0x464af4['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD_OUTSIDE'];
                    else
                        _0x464af4['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['OVERLAP'] || _0x464af4['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['OVERLAP_OUTSIDE'] ? (_0x464af4['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['OVERLAP_OUTSIDE'], _0x464af4['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']()) : (_0x464af4['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], _0x464af4['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']());
                    continue;
                }
                _0x464af4['updateBoundary'](), _0x5ee165 == ![] ? _0x464af4['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD'] : _0x1416ff['isBoundaryOverlapped'](_0x464af4['boundaryData']) == ![] ? (_0x464af4['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD'], _0x1416ff['addBoundary'](_0x464af4['boundaryData'], ![])) : (_0x464af4['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['OVERLAP'], _0x464af4['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']());
            }
        }
    }
    ['updateOverlapCheck2nd'](_0x4dea5d, _0x30c89b) {
        const _0x342d40 = this['getMapCoord']()['getMapRect'](), _0x5a48ea = this['getMapCoord']()['getLevel']();
        for (const [_0x221ade, _0x43337c] of this.#tiledLabelCacheMap) {
            if (!logi['maps']['Utils']['rectOnMapRect'](_0x43337c['tileInfo']['boundary'], _0x342d40))
                continue;
            let _0xcadd40 = this.#tiledLabelInRect['get'](_0x221ade);
            _0xcadd40 === undefined && (this.#tiledLabelInRect['set'](_0x221ade, new logi['maps']['ObjectsInTile']()), _0xcadd40 = this.#tiledLabelInRect['get'](_0x221ade), _0xcadd40['tileInfo']['tileId'] = _0x43337c['tileInfo']['tileId'], _0xcadd40['tileInfo']['boundary']['xMin'] = _0x43337c['tileInfo']['boundary']['xMin'], _0xcadd40['tileInfo']['boundary']['yMin'] = _0x43337c['tileInfo']['boundary']['yMin'], _0xcadd40['tileInfo']['boundary']['xMax'] = _0x43337c['tileInfo']['boundary']['xMax'], _0xcadd40['tileInfo']['boundary']['yMax'] = _0x43337c['tileInfo']['boundary']['yMax']);
            for (const [_0x2ea1c3, _0x4e6068] of _0x43337c['objects']) {
                if (_0x4e6068['getVisible']() == ![] || _0x4e6068['checkRenderRange'](_0x5a48ea) == ![]) {
                    _0x4e6068['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], _0x4e6068['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']();
                    continue;
                }
                const _0x457d6a = _0x4e6068['getPosition']();
                if (!logi['maps']['Utils']['pointInMapRect'](_0x457d6a, _0x342d40)) {
                    if (_0x4e6068['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['LOAD'] || _0x4e6068['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['LOAD_OUTSIDE'])
                        _0x4e6068['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD_OUTSIDE'];
                    else
                        _0x4e6068['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['OVERLAP'] || _0x4e6068['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['OVERLAP_OUTSIDE'] ? (_0x4e6068['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['OVERLAP_OUTSIDE'], _0x4e6068['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']()) : (_0x4e6068['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], _0x4e6068['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']());
                    continue;
                }
                let _0x16465c = _0xcadd40['objects']['get'](_0x2ea1c3);
                _0x16465c === undefined && (_0x4e6068['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], _0x4e6068['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick'](), _0x4e6068['updateBoundary'](), _0x4dea5d == ![] ? _0x4e6068['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD'] : _0x30c89b['isBoundaryOverlapped'](_0x4e6068['boundaryData']) == ![] ? (_0x4e6068['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD'], _0x30c89b['addBoundary'](_0x4e6068['boundaryData'], ![])) : (_0x4e6068['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['OVERLAP'], _0x4e6068['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']()), _0xcadd40['objects']['set'](_0x2ea1c3, _0x4e6068));
            }
        }
    }
    ['refreshTiledLabelByLevel']() {
        this.#tiledLabelCacheMap['clear'](), this.#tiledLabelInRect['clear']();
        if (this.#labelObjects)
            for (const [, _0x4495ef] of this.#labelObjects) {
                _0x4495ef['expiredTileId'] = !![], this.#addTiledLabelCacheMap(_0x4495ef);
            }
    }
    #addTiledLabelCacheMap(_0x48614a) {
        if (_0x48614a['expiredTileId'] == !![]) {
            const _0x4e7312 = this['getMapCoord'](), _0x1c6984 = _0x48614a['getPosition'](), _0x2355dd = _0x4e7312['getLevel'](), _0x5d88f2 = _0x4e7312['getTileInfo'](_0x1c6984['lng'], _0x1c6984['lat'], _0x2355dd);
            _0x48614a['expiredTileId'] = ![], _0x48614a['tileInfo']['tileId'] = _0x5d88f2['tileId'], _0x48614a['tileInfo']['boundary']['xMin'] = _0x5d88f2['boundary']['xMin'], _0x48614a['tileInfo']['boundary']['yMin'] = _0x5d88f2['boundary']['yMin'], _0x48614a['tileInfo']['boundary']['xMax'] = _0x5d88f2['boundary']['xMax'], _0x48614a['tileInfo']['boundary']['yMax'] = _0x5d88f2['boundary']['yMax'];
        }
        let _0x2ff85a = this.#tiledLabelCacheMap['get'](_0x48614a['tileInfo']['tileId']);
        _0x2ff85a === undefined && (this.#tiledLabelCacheMap['set'](_0x48614a['tileInfo']['tileId'], new logi['maps']['ObjectsInTile']()), _0x2ff85a = this.#tiledLabelCacheMap['get'](_0x48614a['tileInfo']['tileId']), _0x2ff85a['tileInfo']['tileId'] = _0x48614a['tileInfo']['tileId'], _0x2ff85a['tileInfo']['boundary']['xMin'] = _0x48614a['tileInfo']['boundary']['xMin'], _0x2ff85a['tileInfo']['boundary']['yMin'] = _0x48614a['tileInfo']['boundary']['yMin'], _0x2ff85a['tileInfo']['boundary']['xMax'] = _0x48614a['tileInfo']['boundary']['xMax'], _0x2ff85a['tileInfo']['boundary']['yMax'] = _0x48614a['tileInfo']['boundary']['yMax']), _0x2ff85a['objects']['set'](_0x48614a['getKey'](), _0x48614a);
    }
    #delTiledLabelCacheMap(_0x20e70c, _0x258121) {
        let _0x59b0ff = this.#tiledLabelCacheMap['get'](_0x20e70c);
        _0x59b0ff !== undefined && (_0x59b0ff['objects']['delete'](_0x258121), _0x59b0ff['objects']['size'] == 0x0 && this.#tiledLabelCacheMap['delete'](_0x20e70c));
        let _0x446676 = this.#tiledLabelInRect['get'](_0x20e70c);
        _0x446676 !== undefined && (_0x446676['objects']['delete'](_0x258121), _0x446676['objects']['size'] == 0x0 && this.#tiledLabelInRect['delete'](_0x20e70c));
    }
};
export default logi['maps']['LabelHandler'];