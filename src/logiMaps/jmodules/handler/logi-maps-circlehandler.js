import a0_0x1b824f from '../common/logi-maps-types.js?v=2.1.10.1';
import a0_0x52ec8c from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a0_0x2a4f23 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a0_0x4e3a27 from './logi-maps-objecthandler.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectsInTile'] = a0_0x1b824f['ObjectsInTile'], logi['maps']['Utils'] = a0_0x52ec8c, logi['maps']['BoundaryData'] = a0_0x2a4f23, logi['maps']['ObjectHandler'] = a0_0x4e3a27, logi['maps']['CircleHandler'] = class extends logi['maps']['ObjectHandler'] {
    #circleObjects;
    #drawingCircleOnMove;
    constructor(_0x5832c9) {
        super(_0x5832c9), this.#circleObjects = new Map(), this.#drawingCircleOnMove = !![];
    }
    ['sendEvent'](_0x40ef45) {
        for (const [, _0x551d2e] of this.#circleObjects) {
            const _0x520ca3 = _0x551d2e?.['eventHandlers'][_0x40ef45['type']];
            _0x520ca3?.['length'] > 0x0 && (_0x551d2e['isHit'](_0x40ef45['point']) == !![] && (_0x40ef45['source'] = _0x551d2e, _0x520ca3['forEach'](_0x25a9fd => {
                _0x25a9fd?.(_0x40ef45);
            })));
        }
    }
    ['hitCircle'](_0x3b6a54, _0x2586d0) {
        const _0x35450c = {
            'x': _0x3b6a54,
            'y': _0x2586d0
        };
        for (const [, _0x1d81e5] of this.#circleObjects) {
            if (_0x1d81e5?.['isHit'](_0x35450c) == !![])
                return _0x1d81e5;
        }
        return null;
    }
    ['findCircle'](_0x203f57) {
        if (_0x203f57['class']) {
            const _0x2141bd = _0x203f57['class'], _0x3ccacb = new Array();
            for (const [, _0x2e2cff] of this.#circleObjects) {
                _0x2e2cff['getClass']() == _0x2141bd && _0x3ccacb['push'](_0x2e2cff);
            }
            return _0x3ccacb;
        } else {
            if (_0x203f57['rect']) {
                const _0x2d9e1b = new Array(), _0x167d8e = this['toBoundaryRect'](_0x203f57['rect']);
                if (_0x167d8e)
                    for (const [, _0x186a8c] of this.#circleObjects) {
                        _0x186a8c['isOverlap'](_0x167d8e) == !![] && _0x2d9e1b['push'](_0x186a8c);
                    }
                return _0x2d9e1b;
            } else {
                const _0x3146b6 = _0x203f57['key'] ?? _0x203f57;
                return this.#circleObjects['get'](_0x3146b6) ?? null;
            }
        }
    }
    ['addCircle'](_0x2f2aaa, _0x1137cd) {
        const _0x100d95 = _0x2f2aaa['getKey']();
        if (this.#circleObjects['has'](_0x100d95))
            return console['warn']('[logi.maps]\x20' + _0x100d95 + '\x20원형이\x20이미\x20추가되어\x20있습니다.'), ![];
        return _0x2f2aaa['getLayer']() && _0x2f2aaa['getLayer']() != _0x1137cd && _0x2f2aaa['getLayer']()['removeCircle'](_0x100d95), _0x2f2aaa['setLayer'](_0x1137cd), this.#circleObjects['set'](_0x100d95, _0x2f2aaa), !![];
    }
    ['isExistCircle'](_0x1a7aa8) {
        return this.#circleObjects['has'](_0x1a7aa8);
    }
    ['removeCircle'](_0x18862a) {
        let _0xb8403d = ![];
        if (_0x18862a['class']) {
            const _0x364c12 = _0x18862a['class'];
            for (const [_0x268a95, _0x59bd94] of this.#circleObjects) {
                _0x59bd94['getClass']() == _0x364c12 && (_0x59bd94['setLayer'](null), this.#circleObjects['delete'](_0x268a95), _0xb8403d = !![]);
            }
        } else {
            const _0x13eabd = _0x18862a['key'] ?? _0x18862a, _0x4b2df4 = this.#circleObjects['get'](_0x13eabd);
            _0x4b2df4 && (_0x4b2df4['setLayer'](null), this.#circleObjects['delete'](_0x13eabd), _0xb8403d = !![]);
        }
        return _0xb8403d;
    }
    ['removeCircleAll'](_0x473419 = []) {
        if (_0x473419['length'] == 0x0) {
            for (const [, _0x40428e] of this.#circleObjects) {
                _0x40428e['setLayer'](null);
            }
            this.#circleObjects['clear']();
        } else
            for (const _0x4d0e69 of this.#circleObjects['keys']()) {
                !_0x473419['includes'](_0x4d0e69) && this['removeCircle'](_0x4d0e69);
            }
    }
    ['setDrawingCircleOnMove'](_0x187130) {
        if (this.#drawingCircleOnMove != _0x187130)
            return this.#drawingCircleOnMove = _0x187130, !![];
        return ![];
    }
    ['getDrawObjects'](_0x326858 = ![]) {
        const _0x41a8dc = this['getMapCoord']()['getLevel'](), _0x5268f1 = [];
        if (_0x326858 == ![] || this.#drawingCircleOnMove == !![])
            for (const [, _0x15ca57] of this.#circleObjects) {
                _0x15ca57['getVisible']() == !![] && _0x15ca57['checkRenderRange'](_0x41a8dc) == !![] && _0x5268f1['push'](_0x15ca57);
            }
        return _0x5268f1['sort']((_0x4a7997, _0x209a26) => _0x4a7997['zIndex'] - _0x209a26['zIndex']);
    }
};
export default logi['maps']['CircleHandler'];