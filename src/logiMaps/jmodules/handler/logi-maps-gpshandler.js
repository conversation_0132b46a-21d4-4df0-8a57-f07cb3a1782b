import a2_0x441988 from '../common/logi-maps-types.js?v=2.1.10.1';
import a2_0x58200b from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a2_0x3dc889 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a2_0x363c9d from './logi-maps-objecthandler.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectsInTile'] = a2_0x441988['ObjectsInTile'], logi['maps']['Utils'] = a2_0x58200b, logi['maps']['BoundaryData'] = a2_0x3dc889, logi['maps']['ObjectHandler'] = a2_0x363c9d, logi['maps']['GpsHandler'] = class extends logi['maps']['ObjectHandler'] {
    #gpsObjects;
    #drawingGpsOnMove;
    constructor(_0x1db6c3) {
        super(_0x1db6c3), this.#gpsObjects = new Map(), this.#drawingGpsOnMove = !![];
    }
    ['sendEvent'](_0x248b7d) {
        for (const [, _0x4e9db9] of this.#gpsObjects) {
            const _0x2196b5 = _0x4e9db9?.['eventHandlers'][_0x248b7d['type']];
            _0x2196b5?.['length'] > 0x0 && (_0x4e9db9['isHit'](_0x248b7d['point']) == !![] && (_0x248b7d['source'] = _0x4e9db9, _0x2196b5['forEach'](_0xeecb3f => {
                _0xeecb3f?.(_0x248b7d);
            })));
        }
    }
    ['hitGps'](_0x461c2d, _0x21d8f4) {
        const _0x8768dc = {
            'x': _0x461c2d,
            'y': _0x21d8f4
        };
        for (const [, _0xb8ac1d] of this.#gpsObjects) {
            if (_0xb8ac1d?.['isHit'](_0x8768dc) == !![])
                return _0xb8ac1d;
        }
        return null;
    }
    ['findGps'](_0x5e9beb) {
        if (_0x5e9beb['class']) {
            const _0xc2ef53 = _0x5e9beb['class'], _0xe0ad4d = new Array();
            for (const [, _0x470da4] of this.#gpsObjects) {
                _0x470da4['getClass']() == _0xc2ef53 && _0xe0ad4d['push'](_0x470da4);
            }
            return _0xe0ad4d;
        } else {
            if (_0x5e9beb['rect']) {
                const _0x2f585f = new Array(), _0x5a9c2e = this['toBoundaryRect'](_0x5e9beb['rect']);
                if (_0x5a9c2e)
                    for (const [, _0x3d5768] of this.#gpsObjects) {
                        _0x3d5768['isOverlap'](_0x5a9c2e) == !![] && _0x2f585f['push'](_0x3d5768);
                    }
                return _0x2f585f;
            } else {
                const _0x4db528 = _0x5e9beb['key'] ?? _0x5e9beb;
                return this.#gpsObjects['get'](_0x4db528) ?? null;
            }
        }
    }
    ['addGps'](_0x4f66e6, _0x5ad7c2) {
        const _0x290c92 = _0x4f66e6['getKey']();
        if (this.#gpsObjects['has'](_0x290c92))
            return console['warn']('[logi.maps]\x20' + _0x290c92 + '\x20GPS가\x20이미\x20추가되어\x20있습니다.'), ![];
        return _0x4f66e6['getLayer']() && _0x4f66e6['getLayer']() != _0x5ad7c2 && _0x4f66e6['getLayer']()['removeGps'](_0x290c92), _0x4f66e6['setLayer'](_0x5ad7c2), this.#gpsObjects['set'](_0x290c92, _0x4f66e6), !![];
    }
    ['isExistGps'](_0x10a2c9) {
        return this.#gpsObjects['has'](_0x10a2c9);
    }
    ['removeGps'](_0x45a142) {
        let _0x460be8 = ![];
        if (_0x45a142['class']) {
            const _0xb8778b = _0x45a142['class'];
            for (const [_0x4f6855, _0x1514d0] of this.#gpsObjects) {
                _0x1514d0['getClass']() == _0xb8778b && (_0x1514d0['setLayer'](null), this.#gpsObjects['delete'](_0x4f6855), _0x460be8 = !![]);
            }
        } else {
            const _0x1d21b7 = _0x45a142['key'] ?? _0x45a142, _0x61876e = this.#gpsObjects['get'](_0x1d21b7);
            _0x61876e && (_0x61876e['setLayer'](null), this.#gpsObjects['delete'](_0x1d21b7), _0x460be8 = !![]);
        }
        return _0x460be8;
    }
    ['removeGpsAll'](_0x17446b = []) {
        if (_0x17446b['length'] == 0x0) {
            for (const [, _0x2dfcbc] of this.#gpsObjects) {
                _0x2dfcbc['setLayer'](null);
            }
            this.#gpsObjects['clear']();
        } else
            for (const _0xcd690a of this.#gpsObjects['keys']()) {
                !_0x17446b['includes'](_0xcd690a) && this['removeGps'](_0xcd690a);
            }
    }
    ['setDrawingGpsOnMove'](_0x362463) {
        if (this.#drawingGpsOnMove != _0x362463)
            return this.#drawingGpsOnMove = _0x362463, !![];
        return ![];
    }
    ['getDrawObjects'](_0x2a354b = ![]) {
        const _0x293984 = this['getMapCoord']()['getLevel'](), _0x5c0761 = [];
        if (_0x2a354b == ![] || this.#drawingGpsOnMove == !![])
            for (const [, _0x13842e] of this.#gpsObjects) {
                _0x13842e['getVisible']() == !![] && _0x13842e['checkRenderRange'](_0x293984) == !![] && _0x5c0761['push'](_0x13842e);
            }
        return _0x5c0761['sort']((_0x14c54f, _0xc4e339) => _0x14c54f['zIndex'] - _0xc4e339['zIndex']);
    }
};
export default logi['maps']['GpsHandler'];