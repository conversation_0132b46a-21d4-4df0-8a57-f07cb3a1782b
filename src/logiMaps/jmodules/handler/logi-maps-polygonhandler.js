import a8_0x27b10f from '../common/logi-maps-types.js?v=2.1.10.1';
import a8_0x389e31 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a8_0x3d2b10 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a8_0x3c0305 from './logi-maps-objecthandler.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectsInTile'] = a8_0x27b10f['ObjectsInTile'], logi['maps']['Utils'] = a8_0x389e31, logi['maps']['BoundaryData'] = a8_0x3d2b10, logi['maps']['ObjectHandler'] = a8_0x3c0305, logi['maps']['PolygonHandler'] = class extends logi['maps']['ObjectHandler'] {
    #polygonObjects;
    #drawingPolygonOnMove;
    constructor(_0x1137ab) {
        super(_0x1137ab), this.#polygonObjects = new Map(), this.#drawingPolygonOnMove = !![];
    }
    ['sendEvent'](_0x567464) {
        for (const [, _0x2a73c1] of this.#polygonObjects) {
            const _0x30634f = _0x2a73c1?.['eventHandlers'][_0x567464['type']];
            _0x30634f?.['length'] > 0x0 && (_0x2a73c1['isHit'](_0x567464['point']) == !![] && (_0x567464['source'] = _0x2a73c1, _0x30634f['forEach'](_0x9ae344 => {
                _0x9ae344?.(_0x567464);
            })));
        }
    }
    ['hitPolygon'](_0x74be69, _0x3c9376) {
        const _0x264e8e = {
            'x': _0x74be69,
            'y': _0x3c9376
        };
        for (const [, _0x4f92b0] of this.#polygonObjects) {
            if (_0x4f92b0?.['isHit'](_0x264e8e) == !![])
                return _0x4f92b0;
        }
        return null;
    }
    ['findPolygon'](_0x22581a) {
        if (_0x22581a['class']) {
            const _0x11c3dd = _0x22581a['class'], _0x1a4f01 = new Array();
            for (const [, _0x3e8126] of this.#polygonObjects) {
                _0x3e8126['getClass']() == _0x11c3dd && _0x1a4f01['push'](_0x3e8126);
            }
            return _0x1a4f01;
        } else {
            if (_0x22581a['rect']) {
                const _0x585cd9 = new Array(), _0x3d774b = this['toBoundaryRect'](_0x22581a['rect']);
                if (_0x3d774b)
                    for (const [, _0x443a6a] of this.#polygonObjects) {
                        _0x443a6a['isOverlap'](_0x3d774b) == !![] && _0x585cd9['push'](_0x443a6a);
                    }
                return _0x585cd9;
            } else {
                const _0x321cff = _0x22581a['key'] ?? _0x22581a;
                return this.#polygonObjects['get'](_0x321cff) ?? null;
            }
        }
    }
    ['addPolygon'](_0x123aec, _0x4a4699) {
        const _0xd1753e = _0x123aec['getKey']();
        if (this.#polygonObjects['has'](_0xd1753e))
            return console['warn']('[logi.maps]\x20' + _0xd1753e + '\x20폴리곤이\x20이미\x20추가되어\x20있습니다.'), ![];
        return _0x123aec['getLayer']() && _0x123aec['getLayer']() != _0x4a4699 && _0x123aec['getLayer']()['removePolygon'](_0xd1753e), _0x123aec['setLayer'](_0x4a4699), this.#polygonObjects['set'](_0xd1753e, _0x123aec), !![];
    }
    ['isExistPolygon'](_0x4e9fab) {
        return this.#polygonObjects['has'](_0x4e9fab);
    }
    ['removePolygon'](_0x5096ed) {
        let _0x1c4a45 = ![];
        if (_0x5096ed['class']) {
            const _0x225c44 = _0x5096ed['class'];
            for (const [_0x8c8c05, _0x460a6c] of this.#polygonObjects) {
                _0x460a6c['getClass']() == _0x225c44 && (_0x460a6c['setLayer'](null), this.#polygonObjects['delete'](_0x8c8c05), _0x1c4a45 = !![]);
            }
        } else {
            const _0x3906f1 = _0x5096ed['key'] ?? _0x5096ed, _0xe3beb7 = this.#polygonObjects['get'](_0x3906f1);
            _0xe3beb7 && (_0xe3beb7['setLayer'](null), this.#polygonObjects['delete'](_0x3906f1), _0x1c4a45 = !![]);
        }
        return _0x1c4a45;
    }
    ['removePolygonAll'](_0x4307f1 = []) {
        if (_0x4307f1['length'] == 0x0) {
            for (const [, _0xf13957] of this.#polygonObjects) {
                _0xf13957['setLayer'](null);
            }
            this.#polygonObjects['clear']();
        } else
            for (const _0x2d0481 of this.#polygonObjects['keys']()) {
                !_0x4307f1['includes'](_0x2d0481) && this['removePolygon'](_0x2d0481);
            }
    }
    ['setDrawingPolygonOnMove'](_0x5e0e10) {
        if (this.#drawingPolygonOnMove != _0x5e0e10)
            return this.#drawingPolygonOnMove = _0x5e0e10, !![];
        return ![];
    }
    ['getDrawObjects'](_0x3170c6 = ![]) {
        const _0x344199 = this['getMapCoord']()['getLevel'](), _0x495d0f = [];
        if (_0x3170c6 == ![] || this.#drawingPolygonOnMove == !![])
            for (const [, _0x94318a] of this.#polygonObjects) {
                _0x94318a['getVisible']() == !![] && _0x94318a['checkRenderRange'](_0x344199) == !![] && _0x495d0f['push'](_0x94318a);
            }
        return _0x495d0f['sort']((_0x30bb8b, _0x4b1c6b) => _0x30bb8b['zIndex'] - _0x4b1c6b['zIndex']);
    }
};
export default logi['maps']['PolygonHandler'];