import a1_0x349413 from '../common/logi-maps-types.js?v=2.1.10.1';
import a1_0x4ce391 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a1_0x93879 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a1_0x3fbfbd from './logi-maps-objecthandler.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectsInTile'] = a1_0x349413['ObjectsInTile'], logi['maps']['Utils'] = a1_0x4ce391, logi['maps']['BoundaryData'] = a1_0x93879, logi['maps']['ObjectHandler'] = a1_0x3fbfbd, logi['maps']['CustomHandler'] = class extends logi['maps']['ObjectHandler'] {
    #customObjects;
    constructor(_0x1050e7) {
        super(_0x1050e7), this.#customObjects = new Map();
    }
    ['addCustom'](_0x519cf0, _0x2af82f) {
        const _0x4272ea = _0x519cf0['getKey']();
        if (this.#customObjects['has'](_0x4272ea))
            return console['warn']('[logi.maps]\x20' + _0x4272ea + '\x20커스텀\x20객체가\x20이미\x20추가되어\x20있습니다.'), ![];
        return _0x519cf0['getLayer']() && _0x519cf0['getLayer']() != _0x2af82f && _0x519cf0['getLayer']()['removeCustom'](_0x4272ea), _0x519cf0['setLayer'](_0x2af82f), this.#customObjects['set'](_0x4272ea, _0x519cf0), !![];
    }
    ['removeCustom'](_0x5aed48) {
        let _0x2ef8a5 = ![];
        if (_0x5aed48['class']) {
            const _0x8c671e = _0x5aed48['class'];
            for (const [_0x2b0ac1, _0x2b8e3c] of this.#customObjects) {
                _0x2b8e3c['getClass']() == _0x8c671e && (_0x2b8e3c['destroy'](), _0x2b8e3c['setLayer'](null), this.#customObjects['delete'](_0x2b0ac1), _0x2ef8a5 = !![]);
            }
        } else {
            const _0x9566d8 = _0x5aed48['key'] ?? _0x5aed48, _0x29ed6b = this.#customObjects['get'](_0x9566d8);
            _0x29ed6b && (_0x29ed6b['destroy'](), _0x29ed6b['setLayer'](null), this.#customObjects['delete'](_0x9566d8), _0x2ef8a5 = !![]);
        }
        return _0x2ef8a5;
    }
    ['removeCustomAll'](_0x138eb3 = []) {
        if (_0x138eb3['length'] == 0x0) {
            for (const [, _0x54394e] of this.#customObjects) {
                _0x54394e['destroy'](), _0x54394e['setLayer'](null);
            }
            this.#customObjects['clear']();
        } else
            for (const _0x27f727 of this.#customObjects['keys']()) {
                !_0x138eb3['includes'](_0x27f727) && this['removeCustom'](_0x27f727);
            }
    }
    ['getDrawObjects']() {
        const _0x7d77f8 = this['getMapCoord']()['getLevel'](), _0x858e40 = [];
        for (const [, _0x255b76] of this.#customObjects) {
            _0x255b76['getVisible']() == !![] && _0x255b76['checkRenderRange'](_0x7d77f8) == !![] && _0x858e40['push'](_0x255b76);
        }
        return _0x858e40['sort']((_0x5c1c0e, _0x58b661) => _0x5c1c0e['zIndex'] - _0x58b661['zIndex']);
    }
};
export default logi['maps']['CustomHandler'];