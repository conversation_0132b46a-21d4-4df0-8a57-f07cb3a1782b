import a9_0x49636d from '../common/logi-maps-types.js?v=2.1.10.1';
import a9_0x543dd2 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a9_0x126c39 from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a9_0xba4b12 from './logi-maps-objecthandler.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectsInTile'] = a9_0x49636d['ObjectsInTile'], logi['maps']['Utils'] = a9_0x543dd2, logi['maps']['BoundaryData'] = a9_0x126c39, logi['maps']['ObjectHandler'] = a9_0xba4b12, logi['maps']['RouteHandler'] = class extends logi['maps']['ObjectHandler'] {
    #routeObjects;
    #drawingRouteOnMove;
    constructor(_0x20ffa0) {
        super(_0x20ffa0), this.#routeObjects = new Map(), this.#drawingRouteOnMove = !![];
    }
    ['sendEvent'](_0x5ad75c) {
        for (const [, _0x492304] of this.#routeObjects) {
            const _0x44460b = _0x492304?.['eventHandlers'][_0x5ad75c['type']];
            _0x44460b?.['length'] > 0x0 && (_0x492304['isHit'](_0x5ad75c['point']) == !![] && (_0x5ad75c['source'] = _0x492304, _0x44460b['forEach'](_0x5e89af => {
                _0x5e89af?.(_0x5ad75c);
            })));
        }
    }
    ['hitRoute'](_0x124ca4, _0x4083f3) {
        const _0x16759f = {
            'x': _0x124ca4,
            'y': _0x4083f3
        };
        for (const [, _0x5274a1] of this.#routeObjects) {
            if (_0x5274a1?.['isHit'](_0x16759f) == !![])
                return _0x5274a1;
        }
        return null;
    }
    ['findRoute'](_0x434921) {
        if (_0x434921['class']) {
            const _0x3ba14b = _0x434921['class'], _0xb202d7 = new Array();
            for (const [, _0x14c7be] of this.#routeObjects) {
                _0x14c7be['getClass']() == _0x3ba14b && _0xb202d7['push'](_0x14c7be);
            }
            return _0xb202d7;
        } else {
            if (_0x434921['rect']) {
                const _0x170b1b = new Array(), _0x21893f = this['toBoundaryRect'](_0x434921['rect']);
                if (_0x21893f)
                    for (const [, _0x1d951f] of this.#routeObjects) {
                        _0x1d951f['isOverlap'](_0x21893f) == !![] && _0x170b1b['push'](_0x1d951f);
                    }
                return _0x170b1b;
            } else {
                const _0x380d51 = _0x434921['key'] ?? _0x434921;
                return this.#routeObjects['get'](_0x380d51) ?? null;
            }
        }
    }
    ['addRoute'](_0x7fe262, _0x44a986) {
        const _0xf81592 = _0x7fe262['getKey']();
        if (this.#routeObjects['has'](_0xf81592))
            return console['warn']('[logi.maps]\x20' + _0xf81592 + '\x20경로가\x20이미\x20추가되어\x20있습니다.'), ![];
        return _0x7fe262['getLayer']() && _0x7fe262['getLayer']() != _0x44a986 && _0x7fe262['getLayer']()['removeRoute'](_0xf81592), _0x7fe262['setLayer'](_0x44a986), this.#routeObjects['set'](_0xf81592, _0x7fe262), !![];
    }
    ['isExistRoute'](_0x52df22) {
        return this.#routeObjects['has'](_0x52df22);
    }
    ['removeRoute'](_0x281078) {
        let _0x1edb94 = ![];
        if (_0x281078['class']) {
            const _0x284c59 = _0x281078['class'];
            for (const [_0x209886, _0x3f5887] of this.#routeObjects) {
                _0x3f5887['getClass']() == _0x284c59 && (_0x3f5887['setLayer'](null), this.#routeObjects['delete'](_0x209886), _0x1edb94 = !![]);
            }
        } else {
            const _0x5dfefc = _0x281078['key'] ?? _0x281078, _0xa43a2f = this.#routeObjects['get'](_0x5dfefc);
            _0xa43a2f && (_0xa43a2f['setLayer'](null), this.#routeObjects['delete'](_0x5dfefc), _0x1edb94 = !![]);
        }
        return _0x1edb94;
    }
    ['removeRouteAll'](_0xba5d99 = []) {
        if (_0xba5d99['length'] == 0x0) {
            for (const [, _0xa006da] of this.#routeObjects) {
                _0xa006da['setLayer'](null);
            }
            this.#routeObjects['clear']();
        } else
            for (const _0x14c915 of this.#routeObjects['keys']()) {
                !_0xba5d99['includes'](_0x14c915) && this['removeRoute'](_0x14c915);
            }
    }
    ['setDrawingRouteOnMove'](_0x56253a) {
        if (this.#drawingRouteOnMove != _0x56253a)
            return this.#drawingRouteOnMove = _0x56253a, !![];
        return ![];
    }
    ['getDrawObjects'](_0xdf4d33 = ![]) {
        const _0x3dc837 = this['getMapCoord']()['getLevel'](), _0x195a2e = [];
        if (_0xdf4d33 == ![] || this.#drawingRouteOnMove == !![])
            for (const [, _0x3940b6] of this.#routeObjects) {
                _0x3940b6['getVisible']() == !![] && _0x3940b6['checkRenderRange'](_0x3dc837) == !![] && _0x195a2e['push'](_0x3940b6);
            }
        return _0x195a2e['sort']((_0x310ff6, _0x2e5c4c) => _0x310ff6['zIndex'] - _0x2e5c4c['zIndex']);
    }
};
export default logi['maps']['RouteHandler'];