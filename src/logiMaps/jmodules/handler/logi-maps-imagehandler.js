import a3_0x2f64ca from '../common/logi-maps-types.js?v=2.1.10.1';
import a3_0xf098e1 from '../utility/logi-maps-utils.js?v=2.1.10.1';
import a3_0x28a12c from '../utility/logi-maps-boundarydata.js?v=2.1.10.1';
import a3_0x46fa0d from './logi-maps-objecthandler.js?v=2.1.10.1';
var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['ObjectsInTile'] = a3_0x2f64ca['ObjectsInTile'], logi['maps']['Utils'] = a3_0xf098e1, logi['maps']['BoundaryData'] = a3_0x28a12c, logi['maps']['ObjectHandler'] = a3_0x46fa0d, logi['maps']['ImageHandler'] = class extends logi['maps']['ObjectHandler'] {
    #imageObjects;
    #tiledImageCacheMap;
    #tiledImageInRect;
    #drawingImageOnMove;
    constructor(_0x133609) {
        super(_0x133609), this.#imageObjects = new Map(), this.#tiledImageCacheMap = new Map(), this.#tiledImageInRect = new Map(), this.#drawingImageOnMove = !![];
    }
    ['sendEvent'](_0x36bb0b) {
        for (const [, _0xa5643b] of this.#tiledImageInRect) {
            for (const [, _0x1cc9cc] of _0xa5643b['objects']) {
                const _0x45c469 = _0x1cc9cc?.['eventHandlers'][_0x36bb0b['type']];
                _0x45c469?.['length'] > 0x0 && (_0x1cc9cc['isHit'](_0x36bb0b['point']) == !![] && (_0x36bb0b['source'] = _0x1cc9cc, _0x45c469['forEach'](_0x56f149 => {
                    _0x56f149?.(_0x36bb0b);
                })));
            }
        }
    }
    ['hitImage'](_0x3c636f, _0x141c99) {
        const _0x4d1884 = {
            'x': _0x3c636f,
            'y': _0x141c99
        };
        for (const [, _0x37bb4e] of this.#tiledImageInRect) {
            for (const [, _0x44c770] of _0x37bb4e['objects']) {
                if (_0x44c770?.['isHit'](_0x4d1884) == !![])
                    return _0x44c770;
            }
        }
        return null;
    }
    ['hitImages'](_0x215180, _0x50c27a) {
        const _0x1e9e87 = new Array(), _0x25abeb = {
                'x': _0x215180,
                'y': _0x50c27a
            };
        for (const [, _0x51c566] of this.#tiledImageInRect) {
            for (const [, _0x40e84a] of _0x51c566['objects']) {
                _0x40e84a?.['isHit'](_0x25abeb) == !![] && _0x1e9e87['push'](_0x40e84a);
            }
        }
        return _0x1e9e87;
    }
    ['findImage'](_0xe17f4b) {
        if (_0xe17f4b['class']) {
            const _0x47b989 = _0xe17f4b['class'], _0x4a3442 = new Array();
            for (const [, _0x467129] of this.#imageObjects) {
                _0x467129['getClass']() == _0x47b989 && _0x4a3442['push'](_0x467129);
            }
            return _0x4a3442;
        } else {
            if (_0xe17f4b['rect']) {
                const _0x2f8817 = new Array(), _0x2d9f8d = this['toBoundaryRect'](_0xe17f4b['rect']);
                if (_0x2d9f8d)
                    for (const [, _0x1d7067] of this.#imageObjects) {
                        _0x1d7067['isOverlap'](_0x2d9f8d) == !![] && _0x2f8817['push'](_0x1d7067);
                    }
                return _0x2f8817;
            } else {
                const _0x174d8a = _0xe17f4b['key'] ?? _0xe17f4b;
                return this.#imageObjects['get'](_0x174d8a) ?? null;
            }
        }
    }
    ['addImage'](_0x20177d, _0x3d92c2) {
        const _0x5e9374 = _0x20177d['getKey']();
        if (this.#imageObjects['has'](_0x5e9374))
            return console['warn']('[logi.maps]\x20' + _0x5e9374 + '\x20이미지가\x20이미\x20추가되어\x20있습니다.'), ![];
        return _0x20177d['getLayer']() && _0x20177d['getLayer']() != _0x3d92c2 && (_0x20177d['getLayer']()['removeImage'](_0x5e9374), this.#delTiledImageCacheMap(_0x20177d['tileInfo']['tileId'], _0x5e9374)), _0x20177d['setLayer'](_0x3d92c2), _0x20177d['resetBoundary'](), this.#imageObjects['set'](_0x5e9374, _0x20177d), this.#addTiledImageCacheMap(_0x20177d), !![];
    }
    ['isExistImage'](_0x32d56f) {
        return this.#imageObjects['has'](_0x32d56f);
    }
    ['removeImage'](_0x1ed386) {
        let _0x7adf64 = ![];
        if (_0x1ed386['class']) {
            const _0x2d407e = _0x1ed386['class'];
            for (const [_0x2c3f6c, _0x4055f8] of this.#imageObjects) {
                if (_0x4055f8['getClass']() == _0x2d407e) {
                    const _0x123672 = _0x4055f8['tileInfo']['tileId'];
                    _0x4055f8['setLayer'](null), this.#imageObjects['delete'](_0x2c3f6c), this.#delTiledImageCacheMap(_0x123672, _0x2c3f6c), _0x7adf64 = !![];
                }
            }
        } else {
            const _0x4f08c2 = _0x1ed386['key'] ?? _0x1ed386, _0x2778bc = this.#imageObjects['get'](_0x4f08c2);
            if (_0x2778bc) {
                const _0x337e7b = _0x2778bc['tileInfo']['tileId'];
                _0x2778bc['setLayer'](null), this.#imageObjects['delete'](_0x4f08c2), this.#delTiledImageCacheMap(_0x337e7b, _0x4f08c2), _0x7adf64 = !![];
            }
        }
        return _0x7adf64;
    }
    ['removeImageAll'](_0x21ff23 = []) {
        if (_0x21ff23['length'] == 0x0) {
            for (const [, _0xb60634] of this.#imageObjects) {
                _0xb60634['expiredTileId'] = !![], _0xb60634['tileInfo']['tileId'] = 0x0, _0xb60634['setLayer'](null);
            }
            this.#imageObjects['clear'](), this.#tiledImageCacheMap['clear'](), this.#tiledImageInRect['clear']();
        } else
            for (const _0x9b501c of this.#imageObjects['keys']()) {
                !_0x21ff23['includes'](_0x9b501c) && this['removeImage'](_0x9b501c);
            }
    }
    ['setDrawingImageOnMove'](_0x2eb372) {
        if (this.#drawingImageOnMove != _0x2eb372)
            return this.#drawingImageOnMove = _0x2eb372, !![];
        return ![];
    }
    ['getDrawObjects'](_0x1abee3 = ![]) {
        const _0x3e3a8c = [];
        if (_0x1abee3 == ![] || this.#drawingImageOnMove == !![])
            for (const [, _0x566cf1] of this.#tiledImageInRect) {
                for (const [, _0x423ad3] of _0x566cf1['objects']) {
                    _0x423ad3['boundaryData']['bLoad'] === logi['maps']['BoundaryData']['STATUS']['LOAD'] && _0x3e3a8c['push'](_0x423ad3);
                }
            }
        return _0x3e3a8c['sort']((_0x5c2ef2, _0x100ca6) => _0x5c2ef2['zIndex'] - _0x100ca6['zIndex']);
    }
    ['boundaryCheckInit'](_0x43cd35) {
        const _0x21ea0c = this['getMapCoord']()['getMapRect']();
        let _0xf7a4af = new Array();
        for (const [, _0x9ceb40] of this.#tiledImageCacheMap) {
            for (const [, _0x40b369] of _0x9ceb40['objects']) {
                _0x40b369['overlapInfo']['visibility'] = _0x43cd35, _0x40b369['boundaryData']['overlapCnt'] = 0x0, _0x40b369['expiredTileId'] == !![] && _0xf7a4af['push'](_0x40b369);
            }
        }
        for (const _0x8667fd of _0xf7a4af) {
            this.#delTiledImageCacheMap(_0x8667fd['tileInfo']['tileId'], _0x8667fd['getKey']()), this.#addTiledImageCacheMap(_0x8667fd);
        }
        for (const [_0x491841, _0x1172ca] of this.#tiledImageInRect) {
            !logi['maps']['Utils']['rectOnMapRect'](_0x1172ca['tileInfo']['boundary'], _0x21ea0c) && this.#tiledImageInRect['delete'](_0x491841);
        }
    }
    ['updateOverlapCheck1st'](_0x2a95ca, _0xbdd33b) {
        const _0x458c29 = this['getMapCoord']()['getMapRect'](), _0x28d3ea = this['getMapCoord']()['getLevel']();
        for (const [, _0xe5c14e] of this.#tiledImageInRect) {
            for (const [, _0x13f8c1] of _0xe5c14e['objects']) {
                if (_0x13f8c1['getVisible']() == ![] || _0x13f8c1['checkRenderRange'](_0x28d3ea) == ![]) {
                    _0x13f8c1['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], _0x13f8c1['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']();
                    continue;
                }
                const _0x2b1c92 = _0x13f8c1['getPosition']();
                if (!logi['maps']['Utils']['pointInMapRect'](_0x2b1c92, _0x458c29)) {
                    if (_0x13f8c1['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['LOAD'] || _0x13f8c1['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['LOAD_OUTSIDE'])
                        _0x13f8c1['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD_OUTSIDE'];
                    else
                        _0x13f8c1['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['OVERLAP'] || _0x13f8c1['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['OVERLAP_OUTSIDE'] ? (_0x13f8c1['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['OVERLAP_OUTSIDE'], _0x13f8c1['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']()) : (_0x13f8c1['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], _0x13f8c1['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']());
                    continue;
                }
                _0x13f8c1['updateBoundary'](), _0x2a95ca == ![] ? _0x13f8c1['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD'] : _0xbdd33b['isBoundaryOverlapped'](_0x13f8c1['boundaryData']) == ![] ? (_0x13f8c1['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD'], _0xbdd33b['addBoundary'](_0x13f8c1['boundaryData'], ![])) : (_0x13f8c1['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['OVERLAP'], _0x13f8c1['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']());
            }
        }
    }
    ['updateOverlapCheck2nd'](_0xdc4a87, _0xbe34a4) {
        const _0x563d07 = this['getMapCoord']()['getMapRect'](), _0xc2f4dd = this['getMapCoord']()['getLevel']();
        for (const [_0x123e06, _0x3fbefa] of this.#tiledImageCacheMap) {
            if (!logi['maps']['Utils']['rectOnMapRect'](_0x3fbefa['tileInfo']['boundary'], _0x563d07))
                continue;
            let _0x347661 = this.#tiledImageInRect['get'](_0x123e06);
            _0x347661 === undefined && (this.#tiledImageInRect['set'](_0x123e06, new logi['maps']['ObjectsInTile']()), _0x347661 = this.#tiledImageInRect['get'](_0x123e06), _0x347661['tileInfo']['tileId'] = _0x3fbefa['tileInfo']['tileId'], _0x347661['tileInfo']['boundary']['xMin'] = _0x3fbefa['tileInfo']['boundary']['xMin'], _0x347661['tileInfo']['boundary']['yMin'] = _0x3fbefa['tileInfo']['boundary']['yMin'], _0x347661['tileInfo']['boundary']['xMax'] = _0x3fbefa['tileInfo']['boundary']['xMax'], _0x347661['tileInfo']['boundary']['yMax'] = _0x3fbefa['tileInfo']['boundary']['yMax']);
            for (const [_0x32ec4e, _0x2c5012] of _0x3fbefa['objects']) {
                if (_0x2c5012['getVisible']() == ![] || _0x2c5012['checkRenderRange'](_0xc2f4dd) == ![]) {
                    _0x2c5012['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], _0x2c5012['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']();
                    continue;
                }
                const _0x1796e4 = _0x2c5012['getPosition']();
                if (!logi['maps']['Utils']['pointInMapRect'](_0x1796e4, _0x563d07)) {
                    if (_0x2c5012['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['LOAD'] || _0x2c5012['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['LOAD_OUTSIDE'])
                        _0x2c5012['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD_OUTSIDE'];
                    else
                        _0x2c5012['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['OVERLAP'] || _0x2c5012['boundaryData']['bLoad'] == logi['maps']['BoundaryData']['STATUS']['OVERLAP_OUTSIDE'] ? (_0x2c5012['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['OVERLAP_OUTSIDE'], _0x2c5012['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']()) : (_0x2c5012['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], _0x2c5012['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']());
                    continue;
                }
                let _0x5b447d = _0x347661['objects']['get'](_0x32ec4e);
                _0x5b447d === undefined && (_0x2c5012['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['NOT_LOAD'], _0x2c5012['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick'](), _0x2c5012['updateBoundary'](), _0xdc4a87 == ![] ? _0x2c5012['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD'] : _0xbe34a4['isBoundaryOverlapped'](_0x2c5012['boundaryData']) == ![] ? (_0x2c5012['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['LOAD'], _0xbe34a4['addBoundary'](_0x2c5012['boundaryData'], ![])) : (_0x2c5012['boundaryData']['bLoad'] = logi['maps']['BoundaryData']['STATUS']['OVERLAP'], _0x2c5012['boundaryData']['creationTick'] = logi['maps']['Utils']['getCurTick']()), _0x347661['objects']['set'](_0x32ec4e, _0x2c5012));
            }
        }
    }
    ['refreshTiledImageByLevel']() {
        this.#tiledImageCacheMap['clear'](), this.#tiledImageInRect['clear']();
        if (this.#imageObjects)
            for (const [, _0x346c54] of this.#imageObjects) {
                _0x346c54['expiredTileId'] = !![], this.#addTiledImageCacheMap(_0x346c54);
            }
    }
    #addTiledImageCacheMap(_0x160f7e) {
        if (_0x160f7e['expiredTileId'] == !![]) {
            const _0x8ace72 = this['getMapCoord'](), _0x1102f7 = _0x160f7e['getPosition'](), _0x59b277 = _0x8ace72['getLevel'](), _0x44a7df = _0x8ace72['getTileInfo'](_0x1102f7['lng'], _0x1102f7['lat'], _0x59b277);
            _0x160f7e['expiredTileId'] = ![], _0x160f7e['tileInfo']['tileId'] = _0x44a7df['tileId'], _0x160f7e['tileInfo']['boundary']['xMin'] = _0x44a7df['boundary']['xMin'], _0x160f7e['tileInfo']['boundary']['yMin'] = _0x44a7df['boundary']['yMin'], _0x160f7e['tileInfo']['boundary']['xMax'] = _0x44a7df['boundary']['xMax'], _0x160f7e['tileInfo']['boundary']['yMax'] = _0x44a7df['boundary']['yMax'];
        }
        let _0x4bbee6 = this.#tiledImageCacheMap['get'](_0x160f7e['tileInfo']['tileId']);
        _0x4bbee6 === undefined && (this.#tiledImageCacheMap['set'](_0x160f7e['tileInfo']['tileId'], new logi['maps']['ObjectsInTile']()), _0x4bbee6 = this.#tiledImageCacheMap['get'](_0x160f7e['tileInfo']['tileId']), _0x4bbee6['tileInfo']['tileId'] = _0x160f7e['tileInfo']['tileId'], _0x4bbee6['tileInfo']['boundary']['xMin'] = _0x160f7e['tileInfo']['boundary']['xMin'], _0x4bbee6['tileInfo']['boundary']['yMin'] = _0x160f7e['tileInfo']['boundary']['yMin'], _0x4bbee6['tileInfo']['boundary']['xMax'] = _0x160f7e['tileInfo']['boundary']['xMax'], _0x4bbee6['tileInfo']['boundary']['yMax'] = _0x160f7e['tileInfo']['boundary']['yMax']), _0x4bbee6['objects']['set'](_0x160f7e['getKey'](), _0x160f7e);
    }
    #delTiledImageCacheMap(_0x260c78, _0x42422a) {
        let _0x2906e1 = this.#tiledImageCacheMap['get'](_0x260c78);
        _0x2906e1 !== undefined && (_0x2906e1['objects']['delete'](_0x42422a), _0x2906e1['objects']['size'] == 0x0 && this.#tiledImageCacheMap['delete'](_0x260c78));
        let _0x30a63f = this.#tiledImageInRect['get'](_0x260c78);
        _0x30a63f !== undefined && (_0x30a63f['objects']['delete'](_0x42422a), _0x30a63f['objects']['size'] == 0x0 && this.#tiledImageInRect['delete'](_0x260c78));
    }
};
export default logi['maps']['ImageHandler'];