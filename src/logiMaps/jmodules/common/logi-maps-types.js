var logi = logi ?? {};
logi['maps'] = logi['maps'] ?? {}, logi['maps']['EVENT'] = {
    'dblclick': 'dblclick',
    'click': 'click',
    'click2': 'click2',
    'mousedown': 'mousedown',
    'mousemove': 'mousemove',
    'mouseup': 'mouseup',
    'touchstart': 'touchstart',
    'touchmove': 'touchmove',
    'touchend': 'touchend'
}, logi['maps']['OBJEVENT'] = {
    'dblclick': 'dblclick',
    'click': 'click',
    'dbltouch': 'dbltouch',
    'touch': 'touch'
}, logi['maps']['BRIDGE_MAPEVENT'] = {
    'movestarted': 'movestarted',
    'moved': 'moved',
    'movefinished': 'movefinished',
    'touch': 'touch'
}, logi['maps']['ALIGN'] = {
    'LT': 'left-top',
    'CT': 'center-top',
    'RT': 'right-top',
    'LM': 'left-middle',
    'CM': 'center-middle',
    'RM': 'right-middle',
    'LB': 'left-bottom',
    'CB': 'center-bottom',
    'RB': 'right-bottom'
}, logi['maps']['LINETYPE'] = {
    'STRAIGHT': 'STRAIGHT',
    'POLY': 'POLY',
    'DOT': 'DOT',
    'DASH': 'DASH'
}, logi['maps']['DISTRICT_VISIBLETYPE'] = {
    'POST_ON': 'POST_ON',
    'SIDO_ON': 'SIDO_ON',
    'SGG_ON': 'SGG_ON',
    'EMD_ON': 'EMD_ON',
    'POST_HOVER': 'POST_HOVER',
    'SIDO_HOVER': 'SIDO_HOVER',
    'SGG_HOVER': 'SGG_HOVER',
    'EMD_HOVER': 'EMD_HOVER',
    'ALL_OFF': 'ALL_OFF'
}, logi['maps']['DISTRICT_DATATYPE'] = {
    'POST': 'POST',
    'SIDO': 'SIDO',
    'SGG': 'SGG',
    'EMD': 'EMD'
}, logi['maps']['LatLng'] = class {
    ['lat'];
    ['lng'];
    /**
   * @preserve .
   * @constructor
   * @description
   *  월드 좌표
   * @param {Number} latitude 위도
   * @param {Number} longitude 경도
   */
    constructor(_0x761692, _0x1350bc) {
        this['lat'] = _0x761692 ?? 0x0, this['lng'] = _0x1350bc ?? 0x0;
    }
}, logi['maps']['Point'] = class {
    ['x'];
    ['y'];
    /**
   * @preserve .
   * @constructor
   * @description
   *  화면 좌표
   * @param {Number} x 화면 좌표 X
   * @param {Number} y 화면 좌표 Y
   */
    constructor(_0x5db38d, _0x441978) {
        this['x'] = _0x5db38d ?? 0x0, this['y'] = _0x441978 ?? 0x0;
    }
}, logi['maps']['LatLngBound'] = class {
    ['min'];
    ['max'];
    constructor() {
        this['min'] = {
            'lat': 0x0,
            'lng': 0x0
        }, this['max'] = {
            'lat': 0x0,
            'lng': 0x0
        };
    }
}, logi['maps']['MapRect'] = class {
    constructor(_0x2dc0b1, _0x11f0b8, _0x55444a, _0x2057fc) {
        this['west'] = _0x2dc0b1 ?? 0x0, this['south'] = _0x11f0b8 ?? 0x0, this['east'] = _0x55444a ?? 0x0, this['north'] = _0x2057fc ?? 0x0;
    }
}, logi['maps']['Rect'] = class {
    constructor(_0x79b0f9, _0x46e3a8, _0x437416, _0x3800de) {
        this['xMin'] = _0x79b0f9 ?? 0x0, this['yMin'] = _0x46e3a8 ?? 0x0, this['xMax'] = _0x437416 ?? 0x0, this['yMax'] = _0x3800de ?? 0x0;
    }
}, logi['maps']['Size'] = class {
    constructor(_0x15e7ca, _0x29dab3) {
        this['width'] = _0x15e7ca ?? 0x0, this['height'] = _0x29dab3 ?? 0x0;
    }
}, logi['maps']['TileUID'] = class {
    #tileX = 0x0;
    #tileY = 0x0;
    #level = 0x0;
    #tileId = '';
    constructor(_0x47c44c = 0x0, _0x5ac94b = 0x0, _0x2117e6 = 0x0) {
        this['setTile'](_0x47c44c, _0x5ac94b, _0x2117e6);
    }
    ['setTile'](_0x9735c9, _0x36edd3, _0x4b84e8) {
        this.#tileX = parseInt(_0x9735c9), this.#tileY = parseInt(_0x36edd3), this.#level = parseInt(_0x4b84e8), this.#tileId = this.#level['toString'](0x24)['toUpperCase']() + this.#tileX['toString'](0x24)['toUpperCase']()['padStart'](0x4, '0') + this.#tileY['toString'](0x24)['toUpperCase']()['padStart'](0x4, '0');
    }
    ['setId'](_0x387711) {
        _0x387711['length'] == 0x9 && (this.#level = parseInt(_0x387711['substring'](0x0, 0x1), 0x24), this.#tileX = parseInt(_0x387711['substring'](0x1, 0x5), 0x24), this.#tileY = parseInt(_0x387711['substring'](0x5, 0x9), 0x24), this.#tileId = _0x387711);
    }
    ['getId']() {
        return this.#tileId;
    }
    ['toTileId']() {
        return (BigInt(this.#level) << 0x38n | BigInt(this.#tileY) << 0x1cn | BigInt(this.#tileX))['toString']();
    }
    ['getTileX']() {
        return this.#tileX;
    }
    ['getTileY']() {
        return this.#tileY;
    }
    ['getLevel']() {
        return this.#level;
    }
}, logi['maps']['TileOnScreen'] = class {
    constructor() {
        this['tileUID'] = new logi['maps']['TileUID'](), this['rctOnScreen'] = new logi['maps']['Rect'](), this['distanceFromCenter'] = 0x0;
    }
    ['getId']() {
        return this['tileUID']['getId']();
    }
    ['getTileX']() {
        return this['tileUID']['getTileX']();
    }
    ['getTileY']() {
        return this['tileUID']['getTileY']();
    }
    ['getLevel']() {
        return this['tileUID']['getLevel']();
    }
}, logi['maps']['TileInfo'] = class {
    constructor() {
        this['tileId'] = '', this['boundary'] = {
            'xMin': 0x0,
            'yMin': 0x0,
            'xMax': 0x0,
            'yMax': 0x0
        };
    }
}, logi['maps']['ObjectsInTile'] = class {
    constructor() {
        this['tileInfo'] = new logi['maps']['TileInfo'](), this['objects'] = new Map();
    }
}, logi['maps']['ZoomInfo'] = class {
    #tileScale = {
        'tileLevel': 0x0,
        'tileLevelOffset': 0x1
    };
    #zoomScale = 0x0;
    constructor(_0x1cb8ef) {
        _0x1cb8ef?.['tileLevel'] && (this.#tileScale['tileLevel'] = Math['round'](_0x1cb8ef['tileLevel']), this.#tileScale['tileLevelOffset'] = _0x1cb8ef['tileLevelOffset'] ?? 0x1, this.#zoomScale = logi['maps']['ZoomInfo']['toZoomScale'](this.#tileScale['tileLevel'], this.#tileScale['tileLevelOffset'])), _0x1cb8ef?.['zoomScale'] && (this.#zoomScale = _0x1cb8ef['zoomScale'], this.#tileScale = logi['maps']['ZoomInfo']['toTileScale'](this.#zoomScale));
    }
    ['setTileLevel'](_0x45feb1) {
        this.#tileScale['tileLevel'] = Math['round'](_0x45feb1), this.#zoomScale = logi['maps']['ZoomInfo']['toZoomScale'](this.#tileScale['tileLevel'], this.#tileScale['tileLevelOffset']);
    }
    ['setTileLevelOffset'](_0x4aee7d) {
        this.#tileScale['tileLevelOffset'] = _0x4aee7d, this.#zoomScale = logi['maps']['ZoomInfo']['toZoomScale'](this.#tileScale['tileLevel'], this.#tileScale['tileLevelOffset']);
    }
    ['setZoomScale'](_0x3e5a73) {
        this.#zoomScale = _0x3e5a73;
        const {
            tileLevel: _0x529515,
            tileLevelOffset: _0x5dee83
        } = logi['maps']['ZoomInfo']['toTileScale'](this.#zoomScale);
        this.#tileScale['tileLevel'] = _0x529515, this.#tileScale['tileLevelOffset'] = _0x5dee83;
    }
    get ['tileLevel']() {
        return this.#tileScale['tileLevel'];
    }
    get ['tileLevelOffset']() {
        return this.#tileScale['tileLevelOffset'];
    }
    get ['zoomScale']() {
        return this.#zoomScale;
    }
    static ['toZoomScale'](_0x43de20, _0x5b2e28) {
        return _0x5b2e28 >= 0x1 ? _0x43de20 + (_0x5b2e28 - 0x1) : _0x43de20 + (_0x5b2e28 - 0x1) * 0x2;
    }
    static ['toTileScale'](_0x59dc8c) {
        const _0x45a7f5 = Math['round'](_0x59dc8c);
        return _0x59dc8c >= _0x45a7f5 ? {
            'tileLevel': _0x45a7f5,
            'tileLevelOffset': 0x1 + (_0x59dc8c - _0x45a7f5)
        } : {
            'tileLevel': _0x45a7f5,
            'tileLevelOffset': 0x1 + (_0x59dc8c - _0x45a7f5) * 0.5
        };
    }
};
export default logi['maps'];