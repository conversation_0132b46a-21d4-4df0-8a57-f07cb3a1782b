import React, { createContext, useContext, useState, useEffect } from 'react';

type DistanceUnitContextValue = {
  distanceUnit: 'km' | 'mi';
  setDistanceUnit: (unit: 'km' | 'mi') => void;
};

const DistanceUnitContext = createContext<DistanceUnitContextValue | undefined>(
  undefined,
);

export const DistanceUnitProvider: React.FC<
  React.PropsWithChildren<object>
> = ({ children }) => {
  const [distanceUnit, setDistanceUnitState] = useState<'km' | 'mi'>(() => {
    const stored = localStorage.getItem('distanceUnit');
    if (stored === 'km' || stored === 'mi') {
      return stored;
    }
    return 'mi'; // 기본값
  });

  const setDistanceUnit = (unit: 'km' | 'mi') => {
    setDistanceUnitState(unit);
    localStorage.setItem('distanceUnit', unit);
  };

  // (optional) distanceUnit이 변경될 때마다 localStorage에 저장하는 useEffect도 가능
  // useEffect(() => {
  //   localStorage.setItem('distanceUnit', distanceUnit);
  // }, [distanceUnit]);

  return (
    <DistanceUnitContext.Provider value={{ distanceUnit, setDistanceUnit }}>
      {children}
    </DistanceUnitContext.Provider>
  );
};

export const useDistanceUnit = (): DistanceUnitContextValue => {
  const context = useContext(DistanceUnitContext);
  if (!context) throw new Error('');
  return context;
};
