import React, { createContext, useContext, useState } from 'react';

type SelectedTypeContextValue = {
  selectedType: string;
  setSelectedType: (type: string) => void;
};

const SelectedTypeContext = createContext<SelectedTypeContextValue | undefined>(
  undefined,
);

export const SelectedTypeProvider: React.FC<
  React.PropsWithChildren<object>
> = ({ children }) => {
  const [selectedType, setSelectedType] = useState<string>('Vehicle');
  return (
    <SelectedTypeContext.Provider value={{ selectedType, setSelectedType }}>
      {children}
    </SelectedTypeContext.Provider>
  );
};

export const useSelectedType = (): SelectedTypeContextValue => {
  const context = useContext(SelectedTypeContext);
  if (!context) throw new Error('eror');
  return context;
};
