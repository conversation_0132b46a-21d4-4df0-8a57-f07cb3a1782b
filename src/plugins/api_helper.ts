import axios from 'axios';
import { AuthResponseDTO } from '@/api/generated';
import { authenticationApi } from '@/api';

axios.defaults.baseURL = '';

// content type
axios.defaults.headers.post['Content-Type'] = 'application/json';

// content type
const loginUser =
  typeof window !== 'undefined' && localStorage.getItem('loginUser');
let token = false;

if (loginUser && loginUser !== 'undefined') {
  token = JSON.parse(loginUser) ? JSON.parse(loginUser).token : null;
}

if (token) axios.defaults.headers.common['Authorization'] = 'Bearer ' + token;

// 요청 인터셉터
axios.interceptors.request.use(
  (config) => {
    const token = getLoggedInUser()?.access_token;
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error),
);

// intercepting to capture errors
axios.interceptors.response.use(
  function (response) {
    // 삭제 - openapi 에서 생성된 함수에서 타입 추론 오류 발생. typescript 는 인터셉터의 런타임 타입을 추론하지 못함.
    // return response.data ? response : response;
    return response;
  },
  async function (error: {
    status: number;
    message?: string;
    config?: { url?: string };
  }) {
    // HTTP 상태 코드에 따른 메시지 매핑
    const statusMessages: Record<number, string> = {
      400: 'Bad Request - The server could not understand the request.',
      401: 'Unauthorized - Invalid credentials or session expired.',
      403: 'Forbidden - You do not have permission to access this resource.',
      404: 'Not Found - The requested resource could not be found.',
      500: 'Internal Server Error - An error occurred on the server.',
      502: 'Bad Gateway - Received an invalid response from the server.',
      503: 'Service Unavailable - The server is currently unavailable.',
      504: 'Gateway Timeout - The server took too long to respond.',
    };
    // 상태와 메시지 추출
    const status = error.status;
    const fallbackMessage = error.message || 'An unexpected error occurred.';

    // 에러 메시지 가져오기
    const message = status ? statusMessages[status] : fallbackMessage;

    console.error(`API 오류 발생 !!!!!! : ${message}`, {
      상태코드: status,
      URL: error.config?.url,
    });

    switch (status) {
      case 400:
        // alert();
        break;
      case 401: {
        // 무한 요청에 빠지는 문제 . auth 관련 엔드포인트는 refresh token 로직 스킵
        if (error.config?.url?.includes('/auth')) {
          return Promise.reject(message);
        }
        const refreshToken = getLoggedInUser()?.refresh_token;
        eraseLoggedInUser();
        if (refreshToken) {
          authenticationApi
            .refreshToken({
              refreshTokenRequestDTO: {
                refreshToken,
              },
            })
            .then((res) => {
              setLoggedInUser(res.data);
              console.log('토큰 재발급 오케이 이전 요청 로그 -> ', error);
            })
            .catch((err) => {
              console.error('토큰 제발급 오류 재발급 요청 로그 -> ', err);
              // 리프레시 토큰 발급 실패시..
              logout();
            });
        } else {
          // 리프레시 토큰 없을 시..
          logout();
          return Promise.reject(message);
        }

        break;
      }
      case 403: {
        console.error('403 Forbidden 에러 발생:', message);
        break;
      }
      case 404:
        console.error('404 Not Found 에러 발생:', message);
        break;
      case 500:
        console.error('500 Internal Server Error 에러 발생:', message);
        break;
      case 502:
        console.error('502 Bad Gateway 에러 발생:', message);
        break;
      case 503:
        console.error('503 Service Unavailable 에러 발생:', message);
        break;
      case 504:
        console.error('504 Gateway Timeout 에러 발생:', message);
        break;
      default:
        break;
    }

    return Promise.reject(message);
  },
);

export function logout() {
  const isOk = eraseLoggedInUser();
  if (isOk) {
    console.error('로그아웃 처리: 인증 정보가 삭제되었습니다.');
    window.location.href = '/login';
  }
}

// 로그인한 유저의 정보 가지고 오기
const getLoggedInUser = (): AuthResponseDTO | null => {
  if (typeof window !== 'undefined') {
    const user = localStorage.getItem('loginUser');

    if (!user) {
      return null;
    } else {
      return JSON.parse(user);
    }
  } else {
    return null;
  }
};

// 로그인한 유저의 정보 저장하기
const setLoggedInUser = (loginToken: AuthResponseDTO): boolean => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('loginUser', JSON.stringify(loginToken));
    return true;
  } else {
    return false;
  }
};

// 로그인한 유저의 정보 삭제하기
const eraseLoggedInUser = (): boolean => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('loginUser');
    return true;
  } else {
    return false;
  }
};

export { getLoggedInUser, setLoggedInUser, eraseLoggedInUser };
