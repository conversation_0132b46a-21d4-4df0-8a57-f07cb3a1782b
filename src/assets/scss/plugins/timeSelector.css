.react-time-picker {
  display: inline-flex;
  align-items: center;
  border: 1px solid #ebebeb;
  border-radius: 4px;
  padding: 5px 5px 5px 12px;
  background: inherit;
}

.react-time-picker__wrapper {
  display: flex;
  align-items: center;
  gap: 5px;
}

.react-time-picker__inputGroup {
  display: flex;
  align-items: center;
}

.react-time-picker__inputGroup__input {
  min-width: 40px !important;
  max-width: 40px !important;
  height: 30px;
  border: 0;
  font-weight: 600; /* Semibold (보통 600) */
  text-align: center;
  font-size: 18px;
  border-radius: 4px;
  outline: none;
  appearance: textfield;
  color: #2f2f2f;
  background: inherit;
}
.react-time-picker__inputGroup__input[type='number'] {
  padding: 0 !important;
}

.react-time-picker__inputGroup__input:focus {
  border-color: #007bff;
}

.react-time-picker__inputGroup__divider {
  padding: 0 5px;
  font-size: 18px;
  font-weight: bold;
}

.react-time-picker__clear-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.react-time-picker__clear-button__icon {
  width: 15px;
  height: 15px;
  stroke: #2f2f2f;
}

.react-time-picker input[hidden] {
  display: none !important; /* 완전히 숨김 */
}

.react-time-picker__inputGroup__leadingZero {
  display: none; /* 불필요한 요소 숨김 */
}
