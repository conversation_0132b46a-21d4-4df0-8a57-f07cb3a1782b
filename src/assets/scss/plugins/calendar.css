.react-calendar {
  width: 256px;
  background: white;
  /*border: 1px solid #a0a096;*/
  font-family: 'Pretendard', serif;
  line-height: 1.125em;
}

.react-calendar--doubleView {
  width: 700px;
}

.react-calendar--doubleView .react-calendar__viewContainer {
  display: flex;
  margin: -0.5em;
}
.react-calendar__month-view {
  height: 100%;
}
.react-calendar__month-view > * {
  height: 100%;
  align-items: start !important;
  > * {
    height: 100%;
  }
}
.react-calendar__month-view__days {
  height: calc(100% - 60px);
  /*flex:1;*/
}

.react-calendar .react-calendar__viewContainer {
  height: calc(100% - 60px);
}

.react-calendar--doubleView .react-calendar__viewContainer > * {
  width: 50%;
  margin: 0.5em;
}

.react-calendar,
.react-calendar *,
.react-calendar *:before,
.react-calendar *:after {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.react-calendar button {
  margin: 0;
  border: 0;
  outline: none;
}

.react-calendar button:enabled:hover {
  cursor: pointer;
}

.react-calendar__navigation {
  display: flex;
  height: 22px;
  margin-bottom: 1em;
  font-weight: bold;
  font-size: 14px;
}

.react-calendar__navigation button {
  min-width: 44px;
  background: none;
}

.react-calendar__navigation button:disabled {
  background-color: #f0f0f0;
}

.react-calendar__navigation button:enabled:hover,
.react-calendar__navigation button:enabled:focus {
  background-color: #e6e6e6;
}

.react-calendar__month-view__weekdays {
  font-size: 12px;
  font-weight: 400;
  line-height: 140%;
  color: #6f6f6f;
  text-align: center;
}

.react-calendar__month-view__weekdays__weekday {
  padding: 0.5em;
}

.react-calendar__month-view__weekNumbers .react-calendar__tile {
  display: flex;
  align-items: center;
  justify-content: center;
  font: inherit;
  font-size: 13px;
  color: #171717;
  font-weight: bold;
}

.react-calendar__month-view__days__day--weekend {
  color: #d10000;
}

.react-calendar__month-view__days__day--neighboringMonth,
.react-calendar__decade-view__years__year--neighboringDecade,
.react-calendar__century-view__decades__decade--neighboringCentury {
  color: #757575;
}

.react-calendar__year-view .react-calendar__tile,
.react-calendar__decade-view .react-calendar__tile,
.react-calendar__century-view .react-calendar__tile {
  padding: 18px;
}

.react-calendar__tile {
  max-width: 100%;
  padding: 2px;
  background: none;
  text-align: center;
  font: inherit;
  font-size: 12px;
}

.react-calendar__tile:disabled {
  background-color: #f0f0f0;
  color: #ababab;
}

.react-calendar__month-view__days__day--neighboringMonth:disabled,
.react-calendar__decade-view__years__year--neighboringDecade:disabled,
.react-calendar__century-view__decades__decade--neighboringCentury:disabled {
  @apply text-gray-15;
}

.react-calendar__tile:enabled:hover,
.react-calendar__tile:enabled:focus {
  background-color: #e6e6e6;
}

.react-calendar__tile--now {
  background: #ffa06d !important;
  border-radius: 3px !important;
  abbr {
    color: white;
    font-weight: bold;
  }
}

.react-calendar__tile--now:enabled:hover,
.react-calendar__tile--now:enabled:focus {
  background: #ff5900 !important;
  border-radius: 3px !important;
}

.react-calendar__tile--hasActive {
  background: #76baff;
}

.react-calendar__tile--hasActive:enabled:hover,
.react-calendar__tile--hasActive:enabled:focus {
  background: #ff5900;
  border-radius: 3px !important;
}

.react-calendar__tile--active {
  background: #3182f71a;
  color: white !important;
}

.react-calendar__tile--rangeStart {
  background: #ff5900;
  border-radius: 3px;
  abbr {
    color: white;
    font-weight: bold;
  }
}
.react-calendar__tile--rangeEnd {
  background: #ff5900 !important;
  border-radius: 3px !important;
  abbr {
    color: white;
    font-weight: bold;
  }
}

.react-calendar__tile--active:enabled:hover,
.react-calendar__tile--active:enabled:focus {
  background: #ff5900;
}

.react-calendar--selectRange .react-calendar__tile--hover {
  background-color: #e6e6e6;
}

.react-calendar__navigation__prev2-button {
  display: none;
}

.react-calendar__navigation__next2-button {
  display: none;
}
