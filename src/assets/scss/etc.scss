.fc-multimonth-header-table {
  border: none;
  thead {
    border: none;
    tr {
      border: none;
      th {
        border: none;
      }
    }
  }
  tbody {
    border: none;
    tr {
      border: none;
      th {
        border: none;
      }
    }
  }
}

.fc-multimonth-daygrid-table {
  border: none;
  tbody {
    border: none;
    tr {
      border: none;
      td {
        border: none;
      }
    }
  }
}

.fc-scrollgrid-sync-inner {
  min-height: 48px !important;
}
.fc-col-header-cell {
  background: white;
}
.fc .fc-daygrid-day.fc-day-today {
  background: white;
}
.fc-multimonth-month {
  padding: 1.2em !important;
}
.fc-multimonth-title {
  display: none;
}

.fc-col-header-cell-cushion {
  width: 100% !important;
}
.fc-timegrid-axis-frame {
  justify-content: flex-start !important;
}

.fc-timegrid-col-frame {
  background: white;
}
.fc-daygrid-day-frame {
  padding: 8px;
  background: white;
}
.fc-daygrid-day-events {
  padding-left: 8px;
}
.fc-daygrid-day-top {
  flex-direction: column-reverse !important;
}

.tox-statusbar__branding {
  display: none;
}

[role='dialog'] {
}
.tox {
  letter-spacing: 0.1px !important;
}
.tox-tinymce {
  width: 100% !important;
}
.dropdown-style {
  min-width: 190px !important;
}

.note-modal-content * {
  letter-spacing: 0.01em !important;
}

.note-modal-footer {
  height: 64px !important;
  padding: 10px;
  text-align: center;
}

.rt-SegmentedControlRoot {
  @apply w-fit py-[5px] h-auto border border-gray-5 rounded-lg !important;
  button[role='radio'] {
    @apply w-auto py-[5px] px-[14px] bg-none !important;
  }
  button[role='radio'][aria-checked='true'] {
    @apply border-none shadow-none !important;
  }
}

.rt-PopoverContent {
  @apply shadow-[0px_2px_8px_0px_rgba(0,0,0,0.12)] !important;
}

.data-radix-popper-content-wrapper {
  @apply z-50;
}

.react-calendar__month-view__weekdays__weekday abbr {
  text-decoration: none !important;
}

div[style*='--radix-popper-available-width'] {
  @apply z-50 !important;
}
@supports (min-height: 100dvh) {
  .radix-themes:where([data-is-root-theme='true']) {
    @apply min-h-fit !important;
  }
}

.radix-themes:where([data-is-root-theme='true']) {
  @apply h-auto !important;
}

@media (min-width: 768px) {
  .note-modal-content {
    margin: 30px auto !important;
    width: 644px !important;
  }
}
