*,
body,
span,
i,
em {
  @apply font-pretendard text-gray-15;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
*::before,
*::after {
  @apply box-border;
}

i,
em {
  @apply not-italic;
}

input:-internal-autofill-selected {
  @apply bg-white !important;
}
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px #fff inset !important;
  box-shadow: 0 0 0 1000px #fff inset !important;
  -webkit-text-fill-color: #171717 !important;
}

// 세로 스크롤 바
div::-webkit-scrollbar-thumb,
::-webkit-scrollbar {
  @apply w-[3px] rounded-full !important;
}
div::-webkit-scrollbar-thumb,
::-webkit-scrollbar-thumb {
  @apply bg-gray-7 !important;
}

// 가로 스크롤 바
div::-webkit-scrollbar-thumb,
::-webkit-scrollbar:horizontal {
  @apply h-[3px] rounded-full !important;
}
div::-webkit-scrollbar-thumb,
::-webkit-scrollbar-thumb:horizontal {
  @apply bg-gray-7 !important;
}

.hide-scrollbar {
  overflow: auto; /* 또는 scroll */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE, Edge */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
