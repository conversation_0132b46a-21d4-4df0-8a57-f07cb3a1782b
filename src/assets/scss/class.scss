@layer utilities {
  // 탭 디자인
  .tab-design {
    @apply text-center [&_button]:px-0 [&_button]:cursor-pointer [&_span]:h-12 [&_span]:px-0 [&_span]:f-c-c [&_span]:subtitle4 [&_span]:text-gray-8 [&_span]:[&_span]:px-4  !important;
    @apply [&_button]:h-12;
    button:where([data-state='active'], [data-active]) {
      @apply [&_span]:text-secondary-6  !important;
    }
    .rt-BaseTabListTriggerInnerHidden.rt-TabsTriggerInnerHidden {
      @apply h-12 p-0  !important;
    }
    .rt-BaseTabListTrigger:where([data-state='active'], [data-active])::before {
      @apply bg-secondary-6 p-0 !important;
    }
    .rt-TabsTriggerInnerHidden .rt-BaseTabListTriggerInner {
      @apply p-0 hover:bg-none !important;
    }
  }
  // 버튼 탭 디자인
  .tab-design-bt {
    @apply w-full f-c gap-[10px] text-center shadow-none [&_button]:min-w-[70px] [&_button]:px-4 [&_button]:bg-gray-5 [&_button]:rounded-md [&_button]:cursor-pointer [&_button]:hover:bg-primary-0 [&_span]:w-full [&_span]:min-w-[70px] [&_span]:h-12 [&_span]:f-c-c [&_span]:subtitle4 [&_span]:text-primary-10 !important;
    @apply [&_button]:h-12;
    button[data-state='active'] {
      @apply bg-secondary-6 [&_span]:text-white !important;
    }
    .rt-BaseTabListTriggerInnerHidden.rt-TabsTriggerInnerHidden {
      @apply h-12 p-0  !important;
    }
    .rt-BaseTabListTrigger:where([data-state='active'], [data-active])::before {
      @apply hidden !important;
    }
  }
  // 탭 클래스
  .tab-wrap {
    @apply py-[30px] flex-1;
  }

  // 호버 메뉴 디자인 클래스
  .hover-design {
    @apply p-[5px] border border-gray-2 shadow-[0px_2px_8px_rgba(0,0,0,0.12)] !important;
    p {
      @apply py-[10px] px-[15px] rounded-md body4 cursor-pointer transition-colors duration-200 hover:bg-primary-0;
      &:last-child {
        @apply border-0;
      }
    }
  }

  // 변경 버튼 디자인 클래스
  .trans-btn {
    @apply bg-white f-c border border-gray-6 rounded-md [&_button]:px-3 [&_button]:py-[2px]  [&_circle]:text-gray-3 [&_path]:text-gray-3 !important;
    button.active svg path,
    button.active svg circle {
      @apply text-secondary-6 !important;
    }
    .trans-divider {
      @apply w-[1px] h-[28px] bg-gray-6;
    }
  }

  // 파일 디자인 클래스
  .file-design {
    @apply w-full py-9 flex justify-center border border-dashed border-gray-8 rounded cursor-pointer !important;
    > div {
      @apply f-c-c;
    }
  }

  // 타임라인
  .timeline {
    @apply absolute bottom-0 right-0 z-10;
    // 타이틀
    .title-wrap {
      @apply f-c;
      .title {
        @apply px-5 py-[6px] bg-primary-1 border-r border-b border-gray-6 subtitle6;
        > div {
          @apply f-c gap-[2px];
          img {
            @apply cursor-pointer;
          }
        }
        &:first-child {
          @apply w-[280px] flex-shrink-0;
        }
        &:last-child {
          @apply w-full f-c-b border-r-0;
        }
      }
    }
    // 카드 정보
    .card-wrap {
      @apply max-h-[450px] overflow-y-scroll hide-scrollbar;
      .card-section {
        @apply f-c overflow-y-scroll hide-scrollbar;
        .info {
          @apply w-full h-[90px] bg-gray-1 border-r border-b border-gray-6;
        }
        .card-info {
          @apply w-[280px] py-3 px-5 flex-shrink-0;
          h2 {
            @apply mb-1 f-c subtitle5;
            span {
              @apply body3;
            }
          }
          p {
            @apply caption2;
          }
        }
        // 현황 정보
        .time-con {
          @apply w-full overflow-scroll hide-scrollbar;
          .time-info {
            @apply w-full py-[10px] px-3 f-js border-r-0;
            .time-bar {
              @apply w-[100px] f-c-c flex-col caption5 relative;
              svg {
                @apply mb-[5px] flex-shrink-0 z-[1];
              }
              > div {
                @apply space-x-[6px];
                span {
                  @apply text-primary-8;
                }
              }
              &::after {
                @apply h-[14px] bg-gray-5 body6 text-gray-12 text-center absolute top-[6.5%] left-1/2 translate-y-1/2;
                content: attr(data-estimatedTime);
                width: var(--bar-width);
                &:last-child {
                  @apply hidden;
                }
              }
            }
          }
        }
      }
    }
  }

  // 모니터링 트래킹 보기 타임라인
  .tracking-line {
    @apply w-full pb-[30px] pl-[30px] space-y-1 relative;
    &:last-child {
      &::after {
        @apply hidden;
      }
    }
    span {
      &::before {
        @apply content-[''] w-[18px] h-[18px] bg-gray-5 rounded-full absolute top-0 left-0;

        background-size: cover;
        background-position: center;
        z-index: 1;
      }
      &::after {
        @apply content-[''] w-[9px] h-[9px] bg-gray-7 rounded-full absolute top-[4.5px] left-[4.5px] z-[1];
      }
    }
    &::after {
      @apply content-[''] w-[2px] h-[calc(100%+15px)] border border-gray-5 border-dashed absolute top-0 left-[8px];
    }
    > div {
      @apply space-y-1;
      h2 {
        @apply body4 text-gray-10;
      }
      p {
        @apply f-s gap-[10px] caption4 text-gray-15;
        em {
          @apply block flex-shrink-0 body6 text-gray-10;
        }
        span {
          @apply block flex-shrink-0 text-gray-10;
        }
      }
    }
    &.active {
      span {
        &::after {
          @apply bg-secondary-6;
        }
      }
      &::after {
        @apply border-secondary-6 border-solid;
      }
      span::before {
        @apply bg-[url('src/assets/images/ic/18/line_check.svg')] z-[2];
      }
      > div h2 {
        @apply text-secondary-6;
      }
    }
    &.prev-completed {
      span::after {
        @apply bg-secondary-6;
      }
    }
  }

  // 장비상세 운행 이력 타임라인
  .timeline-wk {
    // 카드 정보
    .card-wrap {
      @apply max-h-[450px] overflow-y-scroll hide-scrollbar;
      .card-section {
        @apply f-c overflow-y-scroll hide-scrollbar;
        .info {
          @apply w-full h-[90px];
        }
        // 현황 정보
        .time-con {
          @apply w-full overflow-scroll hide-scrollbar;
          .time-info {
            @apply w-full py-[10px] px-3 f-js border-r-0;
            .time-bar {
              @apply w-[100px] f-c-c flex-col caption5 relative;
              svg {
                @apply mb-[5px] flex-shrink-0 z-[1];
              }
              > div {
                @apply space-x-[6px];
              }
              h2 {
                @apply body4 text-primary-8 !important;
              }
              p {
                @apply caption4 text-gray-10 !important;
              }
              span {
                @apply caption5 text-gray-8 !important;
              }
              &::after {
                @apply w-[100px] h-[14px] bg-gray-5 body6 text-gray-12 text-center absolute top-[-6.5px] left-1/2 translate-y-1/2;
                content: attr(data-estimatedTime);
                &:last-child {
                  @apply hidden;
                }
              }
            }
          }
        }
      }
    }
  }
  // 장비 상세 운행 이력
  .h-a-m {
    @apply w-7 h-7 transition-transform duration-300;
  }
  .h-c {
    @apply pb-6 px-6 border-t border-gray-6 transition-all duration-300 ease-in-out overflow-hidden;
  }

  // 배차 라인
  .dispatch-line {
    @apply relative;
    > div {
      @apply ml-[34px];
    }
    &::before {
      @apply content-[''] w-[18px] h-[18px] bg-[url('@/assets/images/ic/18/dispatch_circle.svg')] bg-no-repeat bg-center bg-contain absolute top-[17px] left-[1px] z-[1];
      &:first-child,
      &:last-child {
        @apply w-[10px] h-[10px] bg-gray-6 bg-none rounded-full left-[4.5px] !important;
      }
    }
    &.dispatch-line--first-filled::before {
      @apply bg-secondary-6 !important;
    }
    &.dispatch-line--filled::before {
      @apply bg-[url('@/assets/images/ic/18/dispatch_circle_fill.svg')] !important;
    }
    &.dispatch-line--last-filled::before {
      @apply bg-semantic-1 !important;
    }
    &::after {
      @apply content-[''] w-[2px] h-[calc(100%+10px)] border border-gray-5 border-dashed absolute top-[25px] left-[9px];
      &:first-child {
        @apply h-[calc(100%+15px)] top-1/2 !important;
      }
      &:last-child {
        @apply hidden;
      }
    }
  }
  // 배차 메뉴 디자인 클래스
  .d-s-t {
    @apply p-2 f-c-c caption3 text-gray-10;
  }

  // 레이아웃 클래스
  .w-b-b-r {
    @apply bg-white border border-gray-6 rounded-md;
  }
  .wrap-layout {
    @apply p-[30px] bg-white border border-gray-6 rounded-md;
  }
  .wrap-layout-2 {
    @apply mt-[30px] p-[30px] bg-white border border-gray-6 rounded-md;
  }
  .b-b-r {
    @apply border border-gray-6 rounded-md;
  }
  .bg-w-br {
    @apply bg-white border border-gray-6 rounded-md;
  }
  .bg-w-br-div {
    @apply [&>div]:bg-white [&>div]:border [&>div]:border-gray-6 [&>div]:rounded-md;
  }
  .p-p-div {
    @apply [&>div]:py-5 [&>div]:px-6;
  }
  .info-layout {
    h3 {
      @apply pt-1 pb-[14px] px-5 subtitle3;
    }
    > div {
      @apply py-4 px-5 f-c gap-[60px] border-b border-gray-6;
      &:last-child {
        @apply pb-[10px] border-0;
      }
      p {
        @apply w-[170px];
      }
      p,
      span {
        @apply body2;
      }
    }
  }

  // 팝업 클래스
  .popup-wrap {
    @apply bg-white rounded-10;
    h2 {
      @apply subtitle3;
    }
    p {
      @apply flex-shrink-0 body1;
    }
    article {
      &:first-child {
        @apply py-6 px-[30px] f-c-b border-b border-gray-4;
      }
      &:last-child {
        @apply p-[30px];
      }
    }
  }

  // 스위치 클래스
  .switch-design {
    @apply f-c border border-gray-7 rounded-[5px];
    i {
      @apply py-[1px] px-3 rounded-[5px] body1 text-gray-7 text-center cursor-pointer transition-all duration-100;
      &.selected {
        @apply bg-primary-10 text-white;
      }
    }
  }

  // 입력 클래스
  .input-design {
    @apply w-[200px] px-4 py-3 border border-gray-6 rounded-md body2 !important;
  }

  // 파란색 커서 글씨
  .blue-underline {
    @apply text-semantic-2 underline underline-offset-2 cursor-pointer;
  }

  // 테이블 주변 border 클래스
  .table-border {
    > div {
      &:first-child {
        @apply border border-gray-6 rounded-md;
        > table {
          @apply border-0 !important;
        }
      }
    }
  }

  // 텍스트 경계선
  .text-divider {
    @apply f-c [&>div]:h-[14px] [&>div]:px-3 [&>div:first-child]:pl-0 [&>div:last-child]:pr-0 [&>div]:f-c [&>div]:gap-[10px] [&>div]:border-r [&>div:last-child]:border-0 [&>div]:border-gray-6;
  }
  .text-divider2 {
    @apply f-c-b [&>div]:w-full [&>div]:px-6 [&>div:first-child]:pl-0 [&>div:last-child]:pr-0 [&>div]:space-y-2 [&>div]:border-r [&>div:last-child]:border-0 [&>div]:border-gray-6;
  }
  .text-divider3 {
    @apply f-c [&>div]:px-3 [&>div:first-child]:pl-0 [&>div]:border-r [&>div:last-child]:border-0 [&>div]:border-gray-6 [&>div]:leading-[14px];
  }
}

// 경계선
.divider {
  @apply w-full h-[1px] bg-gray-6;
}
.divider-v {
  @apply w-[1px] h-[40px] bg-gray-6;
}
