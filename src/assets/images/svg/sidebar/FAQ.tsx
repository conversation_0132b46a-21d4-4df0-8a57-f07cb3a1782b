import React from 'react';

interface SvgProps {
  className?: string;
}

const FAQ: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${className}`}
    >
      <path
        d="M13.1111 2.5H11C5.75329 2.5 1.5 6.7533 1.5 12V17.2778C1.5 19.6096 3.39035 21.5 5.72222 21.5H13.1111C18.3578 21.5 22.6111 17.2467 22.6111 12C22.6111 6.7533 18.3578 2.5 13.1111 2.5Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
      <path
        d="M7.5 8.7002H13.5"
        stroke="white"
        strokeWidth="1.25"
        strokeLinecap="round"
      />
      <path
        d="M7.5 12H16.5"
        stroke="white"
        strokeWidth="1.25"
        strokeLinecap="round"
      />
      <path
        d="M7.5 15.3008H16.5"
        stroke="white"
        strokeWidth="1.25"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default FAQ;
