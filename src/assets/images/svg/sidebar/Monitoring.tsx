import React from 'react';

interface SvgProps {
  className?: string;
}

const Monitoring: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      className={`${className}`}
    >
      <path
        d="M12 17.9004V21.5504M12 17.9004H18C20.2091 17.9004 21.5 16.2664 21.5 13.7664V6.26642C21.5 4.05728 20.2091 2.40039 18 2.40039H6C3.79086 2.40039 2.5 4.05728 2.5 6.26642V13.7664C2.5 16.2664 3.79086 17.9004 6 17.9004H12ZM12 21.5504H7M12 21.5504H17M6 9.90039H9.33871L10.7581 8.05424L11.8226 10.0542L12.8871 12.0542L14.6613 9.90039H18"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default Monitoring;
