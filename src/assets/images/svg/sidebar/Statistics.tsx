import React from 'react';

interface SvgProps {
  className?: string;
}

const Statistics: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="20"
      viewBox="0 0 22 20"
      fill="none"
      className={`${className}`}
    >
      <path
        d="M1.5 1V16C1.5 17.6569 2.84314 19 4.5 19H20.7"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M10.1641 15.1748L10.1641 7.98978"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M14 15.1748L14 9.18978"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M17.8005 15.1748L17.8005 6.77481"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M6.40063 15.1748L6.40063 10.9898"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M6.30078 6.6998L9.32109 3.67949C9.65816 3.34243 10.1863 3.29014 10.5829 3.55455L12.8638 5.07512C13.2397 5.32574 13.7369 5.29316 14.077 4.99565L18.3008 1.2998"
        stroke="white"
        strokeWidth="1.3"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default Statistics;
