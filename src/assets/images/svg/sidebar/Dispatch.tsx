import React from 'react';

interface SvgProps {
  className?: string;
}

const Dispatch: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      className={`${className}`}
    >
      <path
        d="M12 7C12 8.10457 11.1046 9 10 9C8.89543 9 8 8.10457 8 7C8 5.89543 8.89543 5 10 5C11.1046 5 12 5.89543 12 7Z"
        stroke="white"
        strokeWidth="1.5"
      />
      <path
        d="M16 7C16 10.3137 12 15 10 15C8 15 4 10.3137 4 7C4 3.68629 6.68629 1 10 1C13.3137 1 16 3.68629 16 7Z"
        stroke="white"
        strokeWidth="1.5"
      />
      <path
        d="M12.9999 13H14.1264C15.3135 13 16.4393 13.5273 17.1992 14.4393L18.2662 15.7196C19.3518 17.0223 18.4254 19 16.7298 19H3.26994C1.57426 19 0.647948 17.0223 1.73349 15.7196L2.80047 14.4393C3.56044 13.5273 4.68623 13 5.87335 13H6.99985"
        stroke="white"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default Dispatch;
