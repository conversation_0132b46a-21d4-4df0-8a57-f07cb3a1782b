import React from 'react';

interface SvgProps {
  className?: string;
}

const Fleet: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${className}`}
    >
      <path
        d="M16.5 4H17C19.2091 4 21 5.79086 21 8V18C21 20.2091 19.2091 22 17 22H7C4.79086 22 3 20.2091 3 18V8C3 5.79086 4.79086 4 7 4H7.5M16.5 4C16.5 5.10457 15.6046 6 14.5 6H9.5C8.39543 6 7.5 5.10457 7.5 4M16.5 4C16.5 2.89543 15.6046 2 14.5 2H9.5C8.39543 2 7.5 2.89543 7.5 4M12 10H16M12 14H16M12 18H16"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <circle cx="8.5" cy="10" r="1" fill="white" />
      <circle cx="8.5" cy="14" r="1" fill="white" />
      <circle cx="8.5" cy="18" r="1" fill="white" />
    </svg>
  );
};

export default Fleet;
