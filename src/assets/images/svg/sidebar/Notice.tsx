import React from 'react';

interface SvgProps {
  className?: string;
}

const Notice: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      width="23"
      height="18"
      viewBox="0 0 23 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${className}`}
    >
      <path
        d="M10.8 1.40345L6.53323 4.60208C6.1871 4.86156 5.76617 5.00181 5.33358 5.00181H3C1.89543 5.00181 1 5.89684 1 7.00091V10.9991C1 12.1032 1.89543 12.9982 3 12.9982H5.33358C5.76617 12.9982 6.1871 13.1384 6.53323 13.3979L10.8 16.5966C12.1185 17.585 14 16.6446 14 14.9973V3.00272C14 1.35538 12.1185 0.415042 10.8 1.40345Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.8008 5.91406C17.761 6.7183 18.348 7.79495 18.348 8.97804C18.348 10.1611 17.761 11.2378 16.8008 12.042"
        stroke="white"
        strokeWidth="1.4"
        strokeLinecap="round"
      />
      <path
        d="M18.8555 3.7998C20.3918 5.1647 21.331 6.99193 21.331 8.9998C21.331 11.0077 20.3918 12.8349 18.8555 14.1998"
        stroke="white"
        strokeWidth="1.4"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default Notice;
