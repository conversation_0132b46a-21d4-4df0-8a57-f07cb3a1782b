import React from 'react';

interface SvgProps {
  className?: string;
}

const Calendar: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${className}`}
    >
      <path
        d="M3 9H21M8 2V5M16 2V5M7 22H17C19.2091 22 21 20.2091 21 18V7.5C21 5.29086 19.2091 3.5 17 3.5H7C4.79086 3.5 3 5.29086 3 7.5V18C3 20.2091 4.79086 22 7 22Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <circle cx="11.9993" cy="13.2337" r="0.833333" fill="white" />
      <circle cx="16.1341" cy="13.2337" r="0.833333" fill="white" />
      <circle cx="7.83333" cy="13.2337" r="0.833333" fill="white" />
      <circle cx="11.9993" cy="16.9339" r="0.833333" fill="white" />
      <circle cx="16.1341" cy="16.9339" r="0.833333" fill="white" />
      <circle cx="7.83333" cy="16.9339" r="0.833333" fill="white" />
    </svg>
  );
};

export default Calendar;
