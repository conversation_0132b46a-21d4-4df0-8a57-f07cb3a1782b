import React from 'react';

interface SvgProps {
  className?: string;
}

const Service: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${className}`}
    >
      <path
        d="M3.19995 6.60811C3.19995 4.33926 4.95102 2.5 7.11106 2.5H16.8888C19.0489 2.5 20.8 4.33926 20.8 6.60811V17.3919C20.8 19.6607 19.0489 21.5 16.8888 21.5H7.11106C4.95102 21.5 3.19995 19.6607 3.19995 17.3919V6.60811Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <circle cx="12.0008" cy="8.2" r="1.2" fill="white" />
      <path
        d="M12 16.79L12 11.7998"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default Service;
