// TimelineBadge.tsx
import React from 'react';

interface SvgProps {
  className?: string;
  arrived?: boolean; // 도착했으면 true, 아니면 false
}

const TimelineBadge: React.FC<SvgProps> = ({
  className,
  arrived = true, // 기본 도착(체크)
}) => {
  const fillColor = arrived ? '#282C62' : '#DFDFDF';
  const strokeColor = arrived ? 'white' : '#C1C1C1';

  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`bg-white rounded-full ${className ?? ''}`}
    >
      <circle
        cx="15"
        cy="15"
        r="14"
        fill={fillColor}
        stroke={strokeColor}
        strokeWidth="2"
      />
      <path
        d="M9 16.1839L12.0226 18.6213C12.7318 19.1931 13.7603 19.1083 14.3678 18.428L21 11"
        stroke={strokeColor}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default TimelineBadge;
