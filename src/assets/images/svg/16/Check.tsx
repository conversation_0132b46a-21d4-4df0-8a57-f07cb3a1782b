import React from 'react';

interface SvgProps {
  className?: string;
}

const Check: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={` ${className}`}
    >
      <path
        d="M3.33398 8.66699L5.68493 10.5478C6.23648 10.989 7.03642 10.9235 7.50892 10.3985L12.6673 4.66699"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default Check;
