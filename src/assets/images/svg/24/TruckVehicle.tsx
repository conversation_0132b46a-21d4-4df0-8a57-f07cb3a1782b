import React from 'react';

interface SvgProps {
  className?: string;
}

const Heavy: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      width="24"
      height="20"
      viewBox="0 0 24 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={` ${className}`}
    >
      <path
        d="M14.75 16.3V4.30005H17.5074C18.303 4.30005 19.0661 4.61612 19.6287 5.17873L21.8713 7.42137C22.4339 7.98398 22.75 8.74704 22.75 9.54269V16.3H14.75Z"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        d="M1.25 2.80005C1.25 1.69548 2.14543 0.800049 3.25 0.800049H14.75V16.3H3.25C2.14543 16.3 1.25 15.4046 1.25 14.3V2.80005Z"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <circle
        cx="5.25"
        cy="16.3"
        r="2"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <circle
        cx="12.25"
        cy="16.3"
        r="2"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <circle
        cx="19.25"
        cy="16.3"
        r="2"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
    </svg>
  );
};

export default Heavy;
