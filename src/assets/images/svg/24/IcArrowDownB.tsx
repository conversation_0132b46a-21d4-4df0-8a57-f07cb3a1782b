import React from 'react';

interface SvgProps {
  className?: string;
}

const IcArrowDown: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={` ${className}`}
    >
      <path
        d="M7 10L12 14L17 10"
        stroke="#3D3D3D"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default IcArrowDown;
