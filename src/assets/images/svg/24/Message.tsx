import React from 'react';

interface SvgProps {
  className?: string;
}

const Message: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      className={` ${className}`}
    >
      <path
        d="M6.50078 8.32495L9.99839 10.6567C11.2412 11.4852 12.8603 11.4852 14.1032 10.6567L17.6008 8.32495M6.50078 20.35H17.6008C19.6442 20.35 21.3008 18.6934 21.3008 16.65V7.39995C21.3008 5.3565 19.6442 3.69995 17.6008 3.69995H6.50078C4.45733 3.69995 2.80078 5.3565 2.80078 7.39995V16.65C2.80078 18.6934 4.45733 20.35 6.50078 20.35Z"
        stroke="#A5A5A5"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default Message;
