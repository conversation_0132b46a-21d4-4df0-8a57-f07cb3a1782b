import React from 'react';

interface SvgProps {
  className?: string;
}

const Heavy: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      width="24"
      height="19"
      viewBox="0 0 24 19"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={` ${className}`}
    >
      <path
        d="M15.7773 7.59961C16.9901 7.59969 18.0609 8.39428 18.4111 9.55566L19.9814 14.7646L20.2734 15.7314H3.75C2.23122 15.7314 1 14.5002 1 12.9814V9.34961C1 8.38311 1.7835 7.59961 2.75 7.59961H15.7773Z"
        fill="white"
      />
      <path
        d="M15.7773 7.59961L15.7774 6.84961H15.7773V7.59961ZM18.4111 9.55566L19.1292 9.33919L19.1292 9.33912L18.4111 9.55566ZM19.9814 14.7646L19.2634 14.9811L19.2635 14.9815L19.9814 14.7646ZM20.2734 15.7314V16.4814H21.2834L20.9914 15.5146L20.2734 15.7314ZM1 9.34961L0.25 9.34961V9.34961H1ZM15.7773 7.59961L15.7773 8.34961C16.6596 8.34967 17.4385 8.92789 17.6931 9.77221L18.4111 9.55566L19.1292 9.33912C18.6833 7.86067 17.3205 6.84972 15.7774 6.84961L15.7773 7.59961ZM18.4111 9.55566L17.6931 9.77214L19.2634 14.9811L19.9814 14.7646L20.6995 14.5482L19.1292 9.33919L18.4111 9.55566ZM19.9814 14.7646L19.2635 14.9815L19.5555 15.9483L20.2734 15.7314L20.9914 15.5146L20.6994 14.5478L19.9814 14.7646ZM20.2734 15.7314V14.9814H3.75V15.7314V16.4814H20.2734V15.7314ZM3.75 15.7314V14.9814C2.64543 14.9814 1.75 14.086 1.75 12.9814H1H0.25C0.25 14.9144 1.817 16.4814 3.75 16.4814V15.7314ZM1 12.9814H1.75V9.34961H1H0.25V12.9814H1ZM1 9.34961L1.75 9.34961C1.75 8.79733 2.19772 8.34961 2.75 8.34961V7.59961V6.84961C1.36929 6.84961 0.250001 7.9689 0.25 9.34961L1 9.34961ZM2.75 7.59961V8.34961H15.7773V7.59961V6.84961H2.75V7.59961Z"
        fill="currentColor"
      />
      <path
        d="M6.82227 7.49987V2.71387C6.82227 2.16158 7.26998 1.71387 7.82227 1.71387H10.9985C12.1189 1.71387 13.146 2.33826 13.6618 3.33297L15.8223 7.49987H6.82227Z"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        d="M16.1992 17L16.1992 14.8976C16.1992 13.2973 17.4965 12 19.0968 12C19.4655 12 19.802 12.2099 19.9642 12.541L22.6377 18L17.1992 18C16.6469 18 16.1992 17.5523 16.1992 17Z"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <circle
        cx="3.95898"
        cy="15"
        r="3"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <circle
        cx="12.959"
        cy="15"
        r="3"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
    </svg>
  );
};

export default Heavy;
