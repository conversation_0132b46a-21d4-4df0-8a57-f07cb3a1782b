import React from 'react';

interface SvgProps {
  className?: string;
}

const Agricultural: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="19"
      viewBox="0 0 24 19"
      fill="none"
      className={` ${className}`}
    >
      <path
        d="M19.5518 7.09985C21.0705 7.09985 22.3018 8.33107 22.3018 9.84985V15.2317H3.26172V7.09985H19.5518Z"
        fill="white"
      />
      <path
        d="M22.3018 9.84985H23.0518V9.84985L22.3018 9.84985ZM22.3018 15.2317V15.9817H23.0518V15.2317H22.3018ZM3.26172 15.2317H2.51172V15.9817H3.26172V15.2317ZM3.26172 7.09985V6.34985H2.51172V7.09985H3.26172ZM19.5518 7.09985V7.84985C20.6563 7.84985 21.5518 8.74528 21.5518 9.84985L22.3018 9.84985L23.0518 9.84985C23.0518 7.91686 21.4848 6.34985 19.5518 6.34985V7.09985ZM22.3018 9.84985H21.5518V15.2317H22.3018H23.0518V9.84985H22.3018ZM22.3018 15.2317V14.4817H3.26172V15.2317V15.9817H22.3018V15.2317ZM3.26172 15.2317H4.01172V7.09985H3.26172H2.51172V15.2317H3.26172ZM3.26172 7.09985V7.84985H19.5518V7.09985V6.34985H3.26172V7.09985Z"
        fill="currentColor"
      />
      <path
        d="M7.86719 6.98595V1.19995H5.26462C4.16045 1.19995 3.26518 2.09477 3.26462 3.19895L3.26273 6.98595H7.86719Z"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <path
        d="M7.86719 6.99987V1.21387H11.0434C12.1639 1.21387 13.1909 1.83826 13.7067 2.83297L15.8672 6.99987H7.86719Z"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <circle
        cx="5.69922"
        cy="13.7998"
        r="4"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
      <circle
        cx="17.7598"
        cy="15.2998"
        r="2.5"
        fill="white"
        stroke="currentColor"
        strokeWidth="1.5"
      />
    </svg>
  );
};

export default Agricultural;
