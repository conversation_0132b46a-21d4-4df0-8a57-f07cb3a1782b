import React, { useState, useRef, useEffect } from 'react';

const GRADIENT_BASE = ['#EB642B', '#F48B34', '#FDB33C'];
const GRADIENT_HOVER = ['#FDB33C', '#F48B34', '#EB642B'];
const STOP_DELAYS = [300, 150, 0];
const ANIMATION_DURATION = 1000;

function lerpColor(a: string, b: string, t: number): string {
  const ah = a.replace('#', '');
  const bh = b.replace('#', '');
  const ar = parseInt(ah.substring(0, 2), 16);
  const ag = parseInt(ah.substring(2, 4), 16);
  const ab = parseInt(ah.substring(4, 6), 16);
  const br = parseInt(bh.substring(0, 2), 16);
  const bg = parseInt(bh.substring(2, 4), 16);
  const bb = parseInt(bh.substring(4, 6), 16);
  const rr = Math.round(ar + (br - ar) * t);
  const rg = Math.round(ag + (bg - ag) * t);
  const rb = Math.round(ab + (bb - ab) * t);
  return (
    '#' +
    [rr, rg, rb]
      .map((v) => v.toString(16).padStart(2, '0'))
      .join('')
      .toUpperCase()
  );
}

const Agricultural: React.FC<{ className?: string }> = ({ className }) => {
  const [hover, setHover] = useState(false);
  const [animTime, setAnimTime] = useState(0);
  const animating = useRef(false);

  useEffect(() => {
    let startTime: number | null = null;

    function animate(ts: number) {
      if (startTime === null) startTime = ts;
      let elapsed = ts - startTime;
      if (elapsed > ANIMATION_DURATION) elapsed = ANIMATION_DURATION;

      // 방향에 따라 증가/감소
      const nextTime = hover
        ? Math.min(animTime + elapsed, ANIMATION_DURATION)
        : Math.max(animTime - elapsed, 0);

      setAnimTime(nextTime);

      // 진행중이면 루프 계속
      if (
        (hover && nextTime < ANIMATION_DURATION) ||
        (!hover && nextTime > 0)
      ) {
        requestAnimationFrame(animate);
        animating.current = true;
      } else {
        animating.current = false;
      }
    }

    // 오직 호버 상태가 바뀔 때만 애니메이션 실행
    if (!animating.current) {
      animating.current = true;
      requestAnimationFrame(animate);
    }
  }, [hover]); // hover 바뀔 때만!

  const stops = GRADIENT_BASE.map((base, idx) => {
    const delay = STOP_DELAYS[idx];
    let t = (animTime - delay) / (ANIMATION_DURATION - STOP_DELAYS[2]);
    t = Math.max(0, Math.min(1, t));
    return lerpColor(base, GRADIENT_HOVER[idx], t);
  });

  return (
    <svg
      width="128"
      height="34"
      viewBox="0 0 128 34"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={{ cursor: 'pointer' }}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      <rect
        width="128"
        height="34"
        rx="6"
        fill="url(#paint0_linear_901_7485)"
      />
      <path
        d="M40.4844 23H38.3125L42.2969 11.6875H44.7969L48.7969 23H46.625L45.6875 20.2031H41.4219L40.4844 23ZM41.9688 18.5625H45.1406L43.5938 14H43.5L41.9688 18.5625ZM52.1719 11.6875V23H50.1406V11.6875H52.1719ZM65.7031 15.5C65.4688 14.1406 64.375 13.3594 63 13.3594C61.1406 13.3594 59.8438 14.7812 59.8438 17.3438C59.8438 19.9375 61.1562 21.3281 63 21.3281C64.3438 21.3281 65.4375 20.5781 65.7031 19.25H67.75C67.4375 21.4219 65.6562 23.1562 62.9688 23.1562C59.9844 23.1562 57.7969 20.9844 57.7969 17.3438C57.7969 13.6875 60.0156 11.5312 62.9688 11.5312C65.4688 11.5312 67.4062 12.9844 67.75 15.5H65.7031ZM71.4062 18.0312V23H69.4062V11.6875H71.3594V15.9531H71.4688C71.8594 15 72.6719 14.4062 73.9844 14.4062C75.75 14.4062 76.9062 15.5469 76.9062 17.5938V23H74.9219V17.9062C74.9219 16.75 74.2969 16.0938 73.25 16.0938C72.1719 16.0938 71.4062 16.7969 71.4062 18.0312ZM78.4062 20.6094C78.4062 18.7031 79.9844 18.1406 81.5625 18.0469C82.1484 18.0078 83.3672 17.9375 83.7344 17.9219V17.25C83.7344 16.4062 83.1875 15.9219 82.2031 15.9219C81.3125 15.9219 80.7656 16.3281 80.6094 16.9375H78.6875C78.8281 15.5 80.125 14.4062 82.25 14.4062C83.8594 14.4062 85.7188 15.0625 85.7188 17.3281V23H83.8125V21.8281H83.75C83.375 22.5469 82.5781 23.1719 81.25 23.1719C79.625 23.1719 78.4062 22.2812 78.4062 20.6094ZM80.3281 20.5938C80.3281 21.3281 80.9219 21.7031 81.7656 21.7031C82.9688 21.7031 83.75 20.9062 83.75 19.9531V19.25L81.875 19.375C80.9219 19.4531 80.3281 19.8438 80.3281 20.5938ZM91.8281 14.5156V16.0469H90.1562V20.4531C90.1562 21.2656 90.5625 21.4375 91.0625 21.4375C91.2969 21.4375 91.6875 21.4219 91.9375 21.4062V23.0312C91.7031 23.0781 91.3125 23.1094 90.8125 23.1094C89.3125 23.1094 88.1562 22.375 88.1719 20.7812V16.0469H86.9531V14.5156H88.1719V12.4844H90.1562V14.5156H91.8281ZM93.6406 23V11.6875H95.6406V15.9062H95.7188C96.0312 15.2969 96.6406 14.4062 98.1406 14.4062C100.109 14.4062 101.656 15.9375 101.656 18.7656C101.656 21.5625 100.141 23.1562 98.1562 23.1562C96.6875 23.1562 96.0312 22.2812 95.7188 21.6562H95.5938V23H93.6406ZM95.5938 18.75C95.5938 20.4062 96.3125 21.5156 97.5938 21.5156C98.9219 21.5156 99.625 20.3594 99.625 18.75C99.625 17.1719 98.9375 16.0312 97.5938 16.0312C96.3125 16.0312 95.5938 17.1094 95.5938 18.75ZM106.922 23.1719C104.453 23.1719 102.859 21.4219 102.859 18.7969C102.859 16.1562 104.453 14.4062 106.922 14.4062C109.406 14.4062 110.984 16.1562 110.984 18.7969C110.984 21.4219 109.406 23.1719 106.922 23.1719ZM106.938 21.5781C108.297 21.5781 108.969 20.3438 108.969 18.7812C108.969 17.2344 108.297 15.9844 106.938 15.9844C105.547 15.9844 104.875 17.2344 104.875 18.7812C104.875 20.3438 105.547 21.5781 106.938 21.5781ZM116.766 14.5156V16.0469H115.094V20.4531C115.094 21.2656 115.5 21.4375 116 21.4375C116.234 21.4375 116.625 21.4219 116.875 21.4062V23.0312C116.641 23.0781 116.25 23.1094 115.75 23.1094C114.25 23.1094 113.094 22.375 113.109 20.7812V16.0469H111.891V14.5156H113.109V12.4844H115.094V14.5156H116.766Z"
        fill="white"
      />
      <rect x="6" y="5" width="24" height="24" rx="5" fill="white" />
      <path
        d="M15.8281 10.731C16.2144 10.7312 16.5281 11.0449 16.5283 11.4312V13.1343H22.5166C24.0077 13.1343 25.2166 14.3435 25.2168 15.8345V22.5688C25.2168 22.9554 24.9032 23.269 24.5166 23.269H13.4824C11.9914 23.2689 10.7822 22.0599 10.7822 20.5688V15.8345C10.7824 14.3435 11.9915 13.1344 13.4824 13.1343H15.1279V12.1313H13.751C13.3644 12.1313 13.0508 11.8178 13.0508 11.4312C13.051 11.0447 13.3645 10.731 13.751 10.731H15.8281ZM13.4824 14.5347C12.7647 14.5348 12.1828 15.1167 12.1826 15.8345V20.5688C12.1826 21.2867 12.7646 21.8695 13.4824 21.8696H23.8164V15.8345C23.8162 15.1167 23.2345 14.5347 22.5166 14.5347H18.8574V16.1431C18.8574 16.6164 18.4734 17.0005 18 17.0005C17.5266 17.0005 17.1426 16.6164 17.1426 16.1431V14.5347H13.4824ZM9.62109 16.3306C9.96384 16.3306 10.2412 16.6089 10.2412 16.9517V19.4536C10.2412 19.7964 9.96384 20.0747 9.62109 20.0747C9.2783 20.0747 9 19.7964 9 19.4536V16.9517C9 16.6089 9.2783 16.3306 9.62109 16.3306ZM26.3789 16.3306C26.7217 16.3306 27 16.6089 27 16.9517V19.4536C27 19.7964 26.7217 20.0747 26.3789 20.0747C26.0362 20.0747 25.7588 19.7964 25.7588 19.4536V16.9517C25.7588 16.6089 26.0362 16.3306 26.3789 16.3306ZM15.2314 16.9546C15.6136 16.9548 15.9228 17.2648 15.9229 17.647V19.0464C15.9227 19.4284 15.6135 19.7386 15.2314 19.7388C14.8492 19.7388 14.5392 19.4286 14.5391 19.0464V17.647C14.5391 17.2646 14.8491 16.9546 15.2314 16.9546ZM20.7686 16.9546C21.1509 16.9546 21.4609 17.2646 21.4609 17.647V19.0464C21.4608 19.4286 21.1508 19.7388 20.7686 19.7388C20.3865 19.7385 20.0773 19.4284 20.0771 19.0464V17.647C20.0772 17.2648 20.3864 16.9548 20.7686 16.9546Z"
        fill="#FF5900"
      />
      <defs>
        <linearGradient
          id="paint0_linear_901_7485"
          x1="46.4516"
          y1="34"
          x2="80.4345"
          y2="-1.0791"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={stops[0]} />
          <stop offset="0.5" stopColor={stops[1]} />
          <stop offset="1" stopColor={stops[2]} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default Agricultural;
