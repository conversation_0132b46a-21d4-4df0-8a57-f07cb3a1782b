import React from 'react';

type UserIconProps = React.SVGProps<SVGSVGElement>;

const UserIcon: React.FC<UserIconProps> = (props) => (
  <svg
    width="14"
    height="16"
    viewBox="0 0 14 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M7.00195 8.88867C10.4381 8.88881 13.2235 10.4808 13.2236 12.4443C13.2236 14.4079 10.4382 15.9999 7.00195 16C3.56551 16 0.779297 14.408 0.779297 12.4443C0.779425 10.4807 3.56559 8.88867 7.00195 8.88867ZM7.00293 0C8.96656 5.86259e-05 10.5586 1.59202 10.5586 3.55566C10.5585 5.51926 8.96652 7.11127 7.00293 7.11133C5.03929 7.11133 3.44732 5.51929 3.44727 3.55566C3.44727 1.59199 5.03925 0 7.00293 0Z"
      fill="currentColor"
    />
  </svg>
);

export default UserIcon;
