import React from 'react';

interface SvgProps {
  className?: string;
}

const Filter: React.FC<SvgProps> = ({ className }) => {
  return (
    <svg
      width="24"
      height="26"
      viewBox="0 0 24 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${className}`}
    >
      <path
        d="M20 9.5C20 13.9183 16.4183 17.5 12 17.5C7.58172 17.5 4 13.9183 4 9.5C4 5.08172 7.58172 1.5 12 1.5C16.4183 1.5 20 5.08172 20 9.5Z"
        stroke="#3D3D3D"
        strokeWidth="1.5"
      />
      <path
        d="M11.9998 9.5V5.5"
        stroke="#3D3D3D"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 9.5L16 9.5"
        stroke="#3D3D3D"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M0.999999 22.8506L23 22.8506"
        stroke="#3D3D3D"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.6496 22.8502C13.6496 23.7615 12.9109 24.5002 11.9996 24.5002C11.0883 24.5002 10.3496 23.7615 10.3496 22.8502C10.3496 21.9389 11.0883 21.2002 11.9996 21.2002C12.9109 21.2002 13.6496 21.9389 13.6496 22.8502Z"
        fill="white"
        stroke="#3D3D3D"
        strokeWidth="1.4"
      />
      <path
        d="M21.0188 22.8502C21.0188 23.7615 20.28 24.5002 19.3688 24.5002C18.4575 24.5002 17.7188 23.7615 17.7188 22.8502C17.7188 21.9389 18.4575 21.2002 19.3688 21.2002C20.28 21.2002 21.0188 21.9389 21.0188 22.8502Z"
        fill="white"
        stroke="#3D3D3D"
        strokeWidth="1.4"
      />
      <path
        d="M6.27852 22.8502C6.27852 23.7615 5.53979 24.5002 4.62852 24.5002C3.71725 24.5002 2.97852 23.7615 2.97852 22.8502C2.97852 21.9389 3.71725 21.2002 4.62852 21.2002C5.53979 21.2002 6.27852 21.9389 6.27852 22.8502Z"
        fill="white"
        stroke="#3D3D3D"
        strokeWidth="1.4"
      />
    </svg>
  );
};

export default Filter;
