import React from 'react';

interface SvgProps {
  onClick?: () => void;
  className?: string;
}

const Pen: React.FC<SvgProps> = ({ onClick, className }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${className}`}
      onClick={onClick}
    >
      <path
        d="M10.3413 3.77406C10.3413 3.77406 10.3413 5.73561 12.3028 7.69716C14.2644 9.65871 16.2259 9.65871 16.2259 9.65871M2.58355 18.9857L6.70281 18.3972C7.297 18.3123 7.84763 18.037 8.27205 17.6126L18.1875 7.69715C19.2708 6.61382 19.2708 4.85739 18.1875 3.77405L16.226 1.8125C15.1426 0.729167 13.3862 0.729166 12.3028 1.8125L2.3874 11.728C1.96297 12.1524 1.68766 12.703 1.60278 13.2972L1.01431 17.4164C0.883541 18.3318 1.66816 19.1165 2.58355 18.9857Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M13 19H19"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default Pen;
