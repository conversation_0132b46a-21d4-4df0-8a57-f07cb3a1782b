import React from 'react';

interface SvgProps {
  onClick?: () => void;
  className?: string;
}

const Drone: React.FC<SvgProps> = ({ onClick, className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="21"
      viewBox="0 0 22 21"
      fill="none"
      className={`${className}`}
      onClick={onClick}
    >
      <path
        d="M1.60057 20.2002C5.40984 20.2002 15.7886 20.2002 15.7886 20.2002C17.1599 20.2002 18.2715 19.0885 18.2715 17.7172C18.2715 16.3458 17.1599 15.2341 15.7886 15.2341L5.12401 15.2341C3.75274 15.2341 2.64111 14.1225 2.64111 12.7511C2.64111 11.3798 3.75274 10.2681 5.12401 10.2681L10.6454 10.2681"
        stroke="#3D3D3D"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M17.6666 5.60411C17.6666 6.37799 17.0391 7.00535 16.2651 7.00535C15.491 7.00535 14.8636 6.37799 14.8636 5.60411C14.8636 4.83023 15.491 4.20287 16.2651 4.20287C17.0391 4.20287 17.6666 4.83023 17.6666 5.60411Z"
        stroke="#3D3D3D"
        strokeWidth="1.4"
      />
      <path
        d="M20.4696 5.6041C20.4696 7.92575 17.6666 11.2091 16.2651 11.2091C14.8636 11.2091 12.0605 7.92575 12.0605 5.6041C12.0605 3.28246 13.943 1.40039 16.2651 1.40039C18.5872 1.40039 20.4696 3.28246 20.4696 5.6041Z"
        stroke="#3D3D3D"
        strokeWidth="1.4"
      />
    </svg>
  );
};

export default Drone;
