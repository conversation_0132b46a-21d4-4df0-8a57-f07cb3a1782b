import React from 'react';

interface SvgProps {
  onClick?: () => void;
  className?: string;
}

const Excel: React.FC<SvgProps> = ({ onClick, className }) => {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`${className}`}
      onClick={onClick}
    >
      <path
        d="M15.0786 3.19922V7.51033C15.0786 9.89129 17.0087 11.8214 19.3897 11.8214L23.7008 11.8214M4.30078 7.51033L4.30078 20.4437C4.30078 22.8246 6.23093 24.7548 8.61189 24.7548H19.3897C21.7706 24.7548 23.7008 22.8246 23.7008 20.4437V13.6072C23.7008 12.4638 23.2466 11.3672 22.4381 10.5587L16.3413 4.46191C15.5328 3.65342 14.4362 3.19922 13.2928 3.19922L8.61189 3.19922C6.23093 3.19922 4.30078 5.12937 4.30078 7.51033Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
      <path
        d="M8.85254 15.8516L9.86426 17.5537H9.89844L10.917 15.8516H12.0791L10.5684 18.3262L12.1133 20.8008H10.9238L9.89844 19.0986H9.86426L8.83887 20.8008H7.65625L9.20801 18.3262L7.68359 15.8516H8.85254ZM12.7012 20.8008V15.8516H13.7266V19.9531H15.8594V20.8008H12.7012ZM19.209 17.2666C19.168 16.877 18.8535 16.6445 18.3545 16.6445C17.835 16.6445 17.5479 16.8838 17.541 17.2119C17.5342 17.5674 17.9102 17.7314 18.334 17.8271L18.7783 17.9365C19.626 18.1279 20.2617 18.5586 20.2617 19.3857C20.2617 20.2949 19.5508 20.8691 18.3477 20.8691C17.1514 20.8691 16.3857 20.3154 16.3584 19.2559H17.3564C17.3906 19.7549 17.7871 20.0078 18.334 20.0078C18.874 20.0078 19.2227 19.7549 19.2227 19.3857C19.2227 19.0508 18.915 18.8936 18.375 18.7568L17.835 18.627C17.001 18.4219 16.4883 18.0049 16.4883 17.2803C16.4883 16.3779 17.2812 15.7832 18.3613 15.7832C19.4551 15.7832 20.1797 16.3916 20.1934 17.2666H19.209Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default Excel;
