import React from 'react';

interface SvgProps {
  onClick?: () => void;
  className?: string;
}

const Drone: React.FC<SvgProps> = ({ onClick, className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      className={`${className}`}
      onClick={onClick}
    >
      <path
        d="M6.99995 17.7849C5.58865 17.9428 3.94573 18.8658 2.73625 19.6785C2.02789 20.1546 1 19.6597 1 18.8062V9.13687C1 8.55169 1.25537 7.99498 1.72214 7.64205C2.50423 7.0507 2.99985 6.79994 3.99978 6.49996M6.99995 17.7849C10.3182 17.4135 11.6816 21.3464 14.9999 20.9749M6.99995 17.7849C6.99995 16.019 6.99995 12.2655 6.99995 10.4997M14.9999 20.9749C16.8671 20.7659 19.1399 19.2178 20.2777 18.3575C20.7445 18.0046 20.9998 17.4479 20.9998 16.8627C20.9998 16.8627 21.0011 8.29328 20.9998 7.3999C20.9985 6.50652 19.9986 6 19.9986 6M14.9999 20.9749V12.4995"
        stroke="#3D3D3D"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.6612 7.92089L9.14602 7.41582C8.70986 7.27043 8.6846 6.66302 9.10718 6.48191L13.7494 4.49237C14.1643 4.31457 14.5838 4.73404 14.406 5.1489L12.4164 9.79116C12.2353 10.2137 11.6279 10.1885 11.4825 9.75231L10.9775 8.23711C10.9277 8.08781 10.8105 7.97065 10.6612 7.92089Z"
        stroke="#3D3D3D"
        strokeWidth="1.4"
      />
      <path
        d="M17.8983 6.99995C17.8983 10.3136 15.2121 12.9999 11.8984 12.9999C8.58471 12.9999 5.89844 10.3136 5.89844 6.99995C5.89844 3.68627 8.58471 1 11.8984 1C15.2121 1 17.8983 3.68627 17.8983 6.99995Z"
        stroke="#3D3D3D"
        strokeWidth="1.4"
      />
    </svg>
  );
};

export default Drone;
