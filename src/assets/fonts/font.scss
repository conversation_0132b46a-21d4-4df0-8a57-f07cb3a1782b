@font-face {
  font-family: 'Pretendard';
  src: url('../fonts/Pretendard-Thin.woff') format('woff');
  font-style: normal;
  font-display: swap;
  font-weight: 100;
}

@font-face {
  font-family: 'Pretendard';
  src: url('../fonts/Pretendard-ExtraLight.woff') format('woff');
  font-style: normal;
  font-display: swap;
  font-weight: 200;
}

@font-face {
  font-family: 'Pretendard';
  src: url('../fonts/Pretendard-Light.woff') format('woff');
  font-style: normal;
  font-display: swap;
  font-weight: 300;
}

@font-face {
  font-family: 'Pretendard';
  src: url('../fonts/Pretendard-Regular.woff') format('woff');
  font-style: normal;
  font-display: swap;
  font-weight: 400;
}

@font-face {
  font-family: 'Pretendard';
  src: url('../fonts/Pretendard-Medium.woff') format('woff');
  font-style: normal;
  font-display: swap;
  font-weight: 500;
}

@font-face {
  font-family: 'Pretendard';
  src: url('../fonts/Pretendard-SemiBold.woff') format('woff');
  font-style: normal;
  font-display: swap;
  font-weight: 600;
}

@font-face {
  font-family: 'Pretendard';
  src: url('../fonts/Pretendard-Bold.woff') format('woff');
  font-style: normal;
  font-display: swap;
  font-weight: 700;
}

@font-face {
  font-family: 'Pretendard';
  src: url('../fonts/Pretendard-ExtraBold.woff') format('woff');
  font-style: normal;
  font-display: swap;
  font-weight: 800;
}

@font-face {
  font-family: 'Pretendard';
  src: url('../fonts/Pretendard-Black.woff') format('woff');
  font-style: normal;
  font-display: swap;
  font-weight: 900;
}

@font-face {
  font-family: 'BrunoAce';
  src: url('../fonts/BrunoAce-Regular.ttf') format('truetype');
  font-style: normal;
  font-display: swap;
  font-weight: 400;
}
