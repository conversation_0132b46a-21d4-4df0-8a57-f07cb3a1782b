import Error404 from '../Pages/Error/404';
import Login from '../Pages/Authentication/Login';
import ProfileManagement from '@/Pages/ProfileManagement/ProfileManagement.tsx';

import Trial from '@/Pages/Trial/Trial.tsx';
import TrialSuccess from '@/Pages/Trial/TrialSuccess.tsx';

import Dashboard from '../Pages/Dashboard/Dashboard.tsx';

import MonitoringMap from '@/Pages/MonitoringEq/MonitoringMap.tsx';
import EQList from '@/Pages/MonitoringEq/EqList.tsx';

import Dispatch from '@/Pages/Dispatch/Dispatch.tsx';
import DispatchHistory from '@/Pages/Dispatch/DispatchHistory.tsx';

import FleetManagement from '@/Pages/Fleet/FleetManagement.tsx';
import FleetInfoDetails from '@/Pages/Fleet/FleetManagement/FleetInfoDetails.tsx';
import EqDetails from '@/Pages/Fleet/FleetManagement/EquipmentDetails.tsx';
import FleetDriver from '@/Pages/Fleet/FleetDriver.tsx';
import FleetDriverDetails from '@/Pages/Fleet/FleetDriver/FleetDriverDetails.tsx';
import FleetDriverRegistration from '@/Pages/Fleet/FleetDriver/FleetDriverRegistration.tsx';
import FleetDriverModify from '@/Pages/Fleet/FleetDriver/FleetDriverModify.tsx';
import FleetDriverEqDetails from '@/Pages/Fleet/FleetDriver/FleetDriverEqDetails.tsx';
import FleetVehicle from '@/Pages/Fleet/FleetVehicle.tsx';
import VehicleRegistration from '@/Pages/Fleet/FleetVehicle/VehicleRegistration.tsx';
import FleetFault from '@/Pages/Fleet/FleetFault.tsx';
import FaultDetail from '@/Pages/Fleet/FleetFault/FaultDetail.tsx';
import MaintenanceCompletionReport from '@/Pages/Fleet/FleetFault/MaintenanceCompletionReport.tsx';
import FleetConsumables from '@/Pages/Fleet/FleetConsumables.tsx';
import FleetAccident from '@/Pages/Fleet/FleetAccident.tsx';
import AccidentResolutionReport from '@/Pages/Fleet/FleetAccident/AccidentResolutionReport.tsx';
import FleetDealer from '@/Pages/Fleet/FleetDealer.tsx';
import FleetReport from '@/Pages/Fleet/FleetReport.tsx';
import ReportDetails from '@/Pages/Fleet/FleetReport/ReportDetails.tsx';

import Access from '@/Pages/Statistics/Access.tsx';
import Battery from '@/Pages/Statistics/Battery.tsx';
import Breakdown from '@/Pages/Statistics/Breakdown.tsx';
import Fuel from '@/Pages/Statistics/Fuel.tsx';
import Heatmap from '@/Pages/Statistics/Heatmap.tsx';
import EquipmentOperationTrend from '@/Pages/Statistics/EquipmentOperationTrend.tsx';
import EquipmentUptime from '@/Pages/Statistics/EquipmentUptime.tsx';
import ProductivityEfficiency from '@/Pages/Statistics/ProductivityEfficiency.tsx';
import Shock from '@/Pages/Statistics/Shock.tsx';

import User from '@/Pages/Management/User.tsx';
import Service from '@/Pages/Management/Service.tsx';
import Dealership from '@/Pages/Management/Dealership.tsx';

import CalendarPage from '@/Pages/Calendar/CalendarPage.tsx';

import Manual from '@/Pages/Service/Maunal.tsx';
import TSG from '@/Pages/Service/TSG.tsx';
import TSGDetail from '@/Pages/Service/TSGDetail.tsx';

import Notice from '@/Pages/Notice/Notice.tsx';
import NoticeDetail from '@/Pages/Notice/NoticeDetail.tsx';
import NoticeRegistration from '@/Pages/Notice/NoticeRegistration.tsx';

import FAQ from '@/Pages/FAQ/FAQ.tsx';
import RegistrationFAQ from '@/Pages/FAQ/RegistrationFAQ.tsx';

import QA from '@/Pages/Q&A/QA.tsx';
import QARegistration from '@/Pages/Q&A/QARegistration.tsx';
import QADetails from '@/Pages/Q&A/QADetails.tsx';

const allRoutes = [
  // 에러 페이지
  { path: '*', element: <Error404 /> },

  // 내 정보
  { path: '/profile', element: <ProfileManagement /> },

  { path: '/trial', element: <Trial /> },
  { path: '/trial/success', element: <TrialSuccess /> },

  // 대시보드
  { path: '/', element: <Dashboard /> },

  // 모니터링 및 장비 상세
  { path: '/map', element: <MonitoringMap /> },
  { path: '/eq_list', element: <EQList /> },

  // 배차
  { path: '/dispatch', element: <Dispatch /> },
  // 배차
  { path: '/dispatch-history', element: <DispatchHistory /> },

  // 플릿
  {
    path: '/fleet-management',
    element: <FleetManagement />,
  },
  {
    path: '/fleet-management/fleetInfoDetails',
    element: <FleetInfoDetails />,
  },
  {
    path: '/fleet-management/eqDetails',
    element: <EqDetails />,
  },
  {
    path: '/fleet-driver',
    element: <FleetDriver />,
  },
  {
    path: '/fleet-driver/fleetDriverDetails',
    element: <FleetDriverDetails />,
  },
  {
    path: '/fleet-driver/fleetDriverRegistration',
    element: <FleetDriverRegistration />,
  },
  {
    path: '/fleet-driver/fleetDriverModify',
    element: <FleetDriverModify />,
  },
  {
    path: '/fleet-driver/fleetDriverEqDetails',
    element: <FleetDriverEqDetails />,
  },
  { path: '/fleet-vehicle', element: <FleetVehicle /> },
  { path: '/fleet-vehicle-add', element: <VehicleRegistration /> },
  {
    path: '/fleet-fault',
    element: <FleetFault />,
  },
  {
    path: '/fleet-fault-detail',
    element: <FaultDetail />,
  },
  {
    path: '/fleet-fault-maintenance',
    element: <MaintenanceCompletionReport />,
  },
  {
    path: '/fleet-consumables',
    element: <FleetConsumables />,
  },
  {
    path: '/fleet-accident',
    element: <FleetAccident />,
  },
  {
    path: '/fleet-accident-report',
    element: <AccidentResolutionReport />,
  },
  {
    path: '/fleet-dealer',
    element: <FleetDealer />,
  },
  {
    path: '/fleet-report',
    element: <FleetReport />,
  },
  {
    path: '/fleet-report/reportDetails',
    element: <ReportDetails />,
  },

  // 통계
  { path: '/access', element: <Access /> },
  { path: '/battery', element: <Battery /> },
  {
    path: '/statics/breakdown',
    element: <Breakdown />,
  },
  {
    path: '/equipment-operation-trend',
    element: <EquipmentOperationTrend />,
  },
  {
    path: '/equipment-uptime',
    element: <EquipmentUptime />,
  },
  { path: '/fuel', element: <Fuel /> },
  { path: '/heatmap', element: <Heatmap /> },
  {
    path: '/productivity-efficiency',
    element: <ProductivityEfficiency />,
  },
  { path: '/shock', element: <Shock /> },

  // 관리
  { path: '/user', element: <User /> },
  { path: '/service', element: <Service /> },
  {
    path: '/dealership',
    element: <Dealership />,
  },

  // 캘린더
  { path: '/calendar', element: <CalendarPage /> },

  // Service
  { path: '/manual', element: <Manual></Manual> },
  { path: '/tsg', element: <TSG></TSG> },
  {
    path: '/tsg-detail',
    element: <TSGDetail></TSGDetail>,
  },

  // Notice
  { path: '/notice', element: <Notice></Notice> },
  {
    path: '/notice-detail/:noticeId',
    element: <NoticeDetail></NoticeDetail>,
  },
  {
    path: '/notice-add',
    element: <NoticeRegistration />,
  },
  {
    path: '/notice-edit/:noticeId',
    element: <NoticeRegistration />,
  },

  // FAQ
  { path: '/faq', element: <FAQ /> },
  {
    path: '/faq-registration',
    element: <RegistrationFAQ />,
  },

  // Q&A
  { path: '/qna', element: <QA /> },
  {
    path: '/qna-registration',
    element: <QARegistration />,
  },
  {
    path: '/qna-edit/:qnaId',
    element: <QARegistration />,
  },
  {
    path: '/qna-details/:qnaId',
    element: <QADetails />,
  },
];

const authRoutes = [{ path: '/login', element: <Login /> }];
const trialRoutes = [{ path: '/trial', element: <Trial /> }];
const trialSuccessRoutes = [
  { path: '/trial/success', element: <TrialSuccess /> },
];

export { allRoutes, authRoutes, trialRoutes, trialSuccessRoutes };
