import {
  createBrowserRouter,
  createRoutesFromElements,
  Route,
  RouterProvider,
} from 'react-router-dom';
import {
  allRoutes,
  authRoutes,
  trialRoutes,
  trialSuccessRoutes,
} from './allRoutes';
import NonAuthLayout from '../Layout/NonAuthLayout';
import Layout from '../Layout/Index';
import RootLayout from '../Layout/RootLayout';
import { v4 } from 'uuid';
import { useIsFetching } from '@tanstack/react-query';
import Loading from '@/Common/Components/etc/Loading';

const router = createBrowserRouter(
  createRoutesFromElements(
    <Route element={<RootLayout />}>
      <Route element={<NonAuthLayout />}>
        {authRoutes.map((route) => (
          <Route key={v4()} {...route} />
        ))}
        {trialRoutes.map((route) => (
          <Route key={v4()} {...route} />
        ))}
        {trialSuccessRoutes.map((route) => (
          <Route key={v4()} {...route} />
        ))}
      </Route>
      <Route element={<Layout />}>
        {allRoutes.map((route) => (
          <Route key={v4()} {...route} />
        ))}
      </Route>
    </Route>,
  ),
);

const Routing = () => {
  const isFetching = useIsFetching({
    predicate: (query) => !query.queryKey.includes('aiChat'),
  });

  return (
    <>
      {isFetching ? <Loading /> : null}
      <RouterProvider router={router} />
    </>
  );
};

export default Routing;
